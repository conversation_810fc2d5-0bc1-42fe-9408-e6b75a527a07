-- TABLE: GASPAR_BUILDING_INFO_MASTER(ガスパル建物情報マスタ)

CREATE TABLE GASPAR_BUILDING_INFO_MASTER(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    CREATION_PROGRAM_ID                          varchar(10)                   
,    CREATION_TERMINAL_ID                         varchar(10)                   
,    CREATION_RESPONSIBLE_CD                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATE_TERMINAL_ID                           varchar(10)                   
,    UPDATE_RESPONSIBLE_CD                        varchar(6)                    
,    LOGICAL_DELETE_SIGN                          numeric(1)                    
,    BUILDING_CD                                  varchar(9)                    
,    ORDER_CD                                     varchar(7)                    
,    SALES_OFFICE_CD                              varchar(3)                    
,    PROPERTY_RESPONSIBLE_CD                      varchar(6)                    
,    MANAGEMENT_BRANCH_CD                         varchar(6)                    
,    MANAGEMENT_RESPONSIBLE_CD                    varchar(6)                    
,    NORTH_ORDER_CD                               numeric(3)                    
,    DAIKEN_BRANCH_CD                             varchar(6)                    
,    TRANSFER_DESTINATION_TYPE                    numeric(2,0)                  
) TABLESPACE :TS_TBL;

COMMENT ON TABLE GASPAR_BUILDING_INFO_MASTER IS 'ガスパル建物情報マスタ 既存システム物理名: FFD10P';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: FD101D @290';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.CREATION_TIME IS '作成時間 既存システム物理名: FD102H @290';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.CREATION_PROGRAM_ID IS '作成プログラムID 既存システム物理名: FD103M @290';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.CREATION_TERMINAL_ID IS '作成端末ID 既存システム物理名: FD104M';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.CREATION_RESPONSIBLE_CD IS '作成担当者CD 既存システム物理名: FD105C';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: FD106D';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.UPDATE_TIME IS '更新時間 既存システム物理名: FD107H';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: FD108M';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.UPDATE_TERMINAL_ID IS '更新端末ID 既存システム物理名: FD109M';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.UPDATE_RESPONSIBLE_CD IS '更新担当者CD 既存システム物理名: FD110C';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: FD111S';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.BUILDING_CD IS '建物CD 既存システム物理名: FD112C';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.ORDER_CD IS '受注CD 既存システム物理名: FD113C';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.SALES_OFFICE_CD IS '販売所CD 既存システム物理名: FD114C';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.PROPERTY_RESPONSIBLE_CD IS '物件担当者CD 既存システム物理名: FD115C';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.MANAGEMENT_BRANCH_CD IS '管理支店CD 既存システム物理名: FD116C';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.MANAGEMENT_RESPONSIBLE_CD IS '管理担当者CD 既存システム物理名: FD117C';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.NORTH_ORDER_CD IS '北順CD 既存システム物理名: FD118C';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.DAIKEN_BRANCH_CD IS '大建支店CD 既存システム物理名: FD119C';
COMMENT ON COLUMN GASPAR_BUILDING_INFO_MASTER.TRANSFER_DESTINATION_TYPE IS '移管先区分 既存システム物理名: FD120B';
