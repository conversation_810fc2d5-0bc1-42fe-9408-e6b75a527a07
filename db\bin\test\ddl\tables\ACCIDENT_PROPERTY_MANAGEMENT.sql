-- TABLE: ACCIDENT_PROPERTY_MANAGEMENT(事故物件管理)

CREATE TABLE ACCIDENT_PROPERTY_MANAGEMENT(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1,0)                  
,    BUILDING_CODE                                varchar(9)                    
,    ROOM_CODE                                    varchar(5)                    
,    TENANT_CONTRACT_NUMBER                       varchar(8)                    
,    INCIDENT_CATEGORY                            varchar(2)                    
,    TARGET_CATEGORY                              varchar(2)                    
,    OCCURRENCE_DATE                              numeric(8)                    
,    NOTICE_PERIOD_EXPIRATION_DATE                numeric(8,0)                  
,    NOTICE_PERIOD                                numeric(2,0)                  
,    SPECIAL_CONTRACT_CD                          varchar(4)                    
,    INCIDENT_DETAIL_1                            varchar(72)                   
,    INCIDENT_DETAIL_2                            varchar(72)                   
,    INCIDENT_DETAIL_3                            varchar(72)                   
,    INCIDENT_DETAIL_4                            varchar(72)                   
,    INCIDENT_DETAIL_5                            varchar(72)                   
,    INCIDENT_DETAIL_6                            varchar(72)                   
,    SPECIAL_CONTRACT_CD_2                        varchar(4)                    
,    SUB_CATEGORY                                 varchar(2)                    
,    GENERAL_APPLICATION_NO                       varchar(22)                   
,    ISSUE_CASE_NO                                varchar(22)                   
) TABLESPACE :TS_TBL;

COMMENT ON TABLE ACCIDENT_PROPERTY_MANAGEMENT IS '事故物件管理 既存システム物理名: FEJ1BP';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.CREATION_DATE IS '作成年月日 既存システム物理名: FEJ01D 自殺、他殺、病死等';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.CREATION_TIME IS '作成時刻 既存システム物理名: FEJ02H 当該部屋、周辺部屋、共用部等';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.UPDATE_DATE IS '更新年月日 既存システム物理名: FEJ03D';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.UPDATE_TIME IS '更新時刻 既存システム物理名: FEJ04H';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: FEJ05M';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.UPDATER IS '更新者 既存システム物理名: FEJ06M';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: FEJ07S';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.BUILDING_CODE IS '建物コード 既存システム物理名: FEJ08C';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.ROOM_CODE IS '部屋コード 既存システム物理名: FEJ09C';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.TENANT_CONTRACT_NUMBER IS 'テナント契約番号 既存システム物理名: FEJ10N';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.INCIDENT_CATEGORY IS '事故区分 既存システム物理名: FEJ11B';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.TARGET_CATEGORY IS '対象区分 既存システム物理名: FEJ12B';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.OCCURRENCE_DATE IS '発生日 既存システム物理名: FEJ13D';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.NOTICE_PERIOD_EXPIRATION_DATE IS '告知期間満了日 既存システム物理名: FEJ14D';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.NOTICE_PERIOD IS '告知期間 既存システム物理名: FEJ15Q';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.SPECIAL_CONTRACT_CD IS '特約CD 既存システム物理名: FEJ16C';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.INCIDENT_DETAIL_1 IS '事故内容1 既存システム物理名: FEJ17M';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.INCIDENT_DETAIL_2 IS '事故内容2 既存システム物理名: FEJ18M';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.INCIDENT_DETAIL_3 IS '事故内容3 既存システム物理名: FEJ19M';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.INCIDENT_DETAIL_4 IS '事故内容4 既存システム物理名: FEJ20M';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.INCIDENT_DETAIL_5 IS '事故内容5 既存システム物理名: FEJ21M';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.INCIDENT_DETAIL_6 IS '事故内容6 既存システム物理名: FEJ22M';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.SPECIAL_CONTRACT_CD_2 IS '特約CD2 既存システム物理名: FEJ23C';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.SUB_CATEGORY IS 'サブ区分 既存システム物理名: FEJ24B';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.GENERAL_APPLICATION_NO IS '一般申請No 既存システム物理名: FEJ25N';
COMMENT ON COLUMN ACCIDENT_PROPERTY_MANAGEMENT.ISSUE_CASE_NO IS '問題案件No 既存システム物理名: FEJ26N';
