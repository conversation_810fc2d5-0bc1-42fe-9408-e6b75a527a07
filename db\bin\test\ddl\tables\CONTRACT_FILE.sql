-- TABLE: CONTRACT_FILE(請負契約ファイル)

CREATE TABLE CONTRACT_FILE(
     CONTRACT_CD                                  numeric(7,0)                  
,    CONTRACT_ADDITIONAL_CD                       numeric(3,0)                  
,    CHANGE_HISTORY_NO                            numeric(3,0)                  
,    RECORD_DELETE_FLAG                           numeric(1,0)                  
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM                               varchar(10)                   
,    UPDATE_EMPLOYEE_NO                           numeric(5,0)                  
,    CONTRACT_STATUS_CATEGORY                     numeric(1,0)                  
,    CONTRACT_CONTENT_REPORT_FLAG                 numeric(1,0)                  
,    TRANSACTION_CATEGORY                         varchar(1)                    
,    ORDERER_CD                                   varchar(7)                    
,    CONTRACT_DATE_CHANGE_DATE                    numeric(8,0)                  
,    ORIGINAL_CONTRACT_DATE                       numeric(8,0)                  
,    AGREED_TERMINATION_DATE                      numeric(8,0)                  
,    CONTRACT_JUDGMENT_CATEGORY                   numeric(1,0)                  
,    CONTRACT_JUDGMENT_DATE                       numeric(8,0)                  
,    CONTRACT_JUDGMENT_DEPT_CATEGORY              numeric(1,0)                  
,    CONSTRUCTION_BRANCH                          numeric(3,0)                  
,    CONTRACT_BRANCH                              numeric(3,0)                  
,    AFFILIATED_SECTION                           numeric(4,0)                  
,    CONTRACT_RESPONSIBLE_PERSON                  numeric(5,0)                  
,    CONTRACT_SECTION_MANAGER                     numeric(5,0)                  
,    CONTRACT_COMPANION                           numeric(5,0)                  
,    BUSINESS_PROJECTION_NO                       numeric(3,0)                  
,    REPEAT_CATEGORY                              numeric(1,0)                  
,    CONTRACT_CATEGORY                            numeric(1,0)                  
,    MAINTENANCE_CATEGORY                         numeric(1,0)                  
,    CONTRACT_PARTNER_CATEGORY                    numeric(1,0)                  
,    ACTIVITY_AREA                                numeric(2,0)                  
,    RESERVE1                                     numeric(6,0)                  
,    OTHER_COMPANY_FLAG                           numeric(1,0)                  
,    ADDITIONAL_CONSTRUCTION_CATEGORY             numeric(1,0)                  
,    LAND_CD                                      numeric(8,0)                  
,    CONSTRUCTION_NAME                            varchar(42)                   
,    CONSTRUCTION_START_TIMING_CATEGORY           numeric(1,0)                  
,    CONSTRUCTION_PERIOD_START_TIMING             numeric(4,0)                  
,    CONSTRUCTION_PERIOD_COMPLETION_TIMING        numeric(4,0)                  
,    LAND_AREA_M                                  numeric(7,2)                  
,    LAND_AREA_TSUBO                              numeric(7,2)                  
,    CONSTRUCTION_FLOOR_AREA_M                    numeric(7,2)                  
,    CONSTRUCTION_FLOOR_AREA_TSUBO                numeric(7,2)                  
,    WAREHOUSE_FACTORY_AREA_M                     numeric(7,2)                  
,    WAREHOUSE_FACTORY_AREA_TSUBO                 numeric(7,2)                  
,    BUILDING_TYPE_ST                             varchar(3)                    
,    NUMBER_OF_BUILDINGS                          numeric(2,0)                  
,    NUMBER_OF_ROOMS                              numeric(3,0)                  
,    FIRE_PROTECTION_CATEGORY                     numeric(1,0)                  
,    LAND_USE_CATEGORY                            numeric(2,0)                  
,    LEASE_CATEGORY                               numeric(1,0)                  
,    SET_CATEGORY                                 numeric(1,0)                  
,    RENT_REVIEW_CATEGORY                         numeric(1,0)                  
,    MANAGEMENT_AVAILABILITY_CATEGORY             numeric(1,0)                  
,    GUARANTEE_CATEGORY                           numeric(1,0)                  
,    MUTUAL_AID_ASSOCIATION_AVAILABILITY          numeric(1,0)                  
,    WATER_CATEGORY                               numeric(1,0)                  
,    DEVELOPMENT_APPLICATION_CATEGORY             numeric(1,0)                  
,    AGRICULTURAL_CONVERSION_CATEGORY             numeric(1,0)                  
,    TAX_ACCOUNTANT_COMPANION_CATEGORY            numeric(1,0)                  
,    INFORMATION                                  numeric(9,0)                  
,    NUMBER_OF_CONTRACT_PLOTS                     numeric(2,1)                  
,    CONTRACT_PAYMENT_CATEGORY                    numeric(1,0)                  
,    FINANCING_SOURCE_CATEGORY1                   numeric(2,0)                  
,    FINANCING_SOURCE_CATEGORY2                   numeric(2,0)                  
,    FINANCING_SOURCE_CATEGORY3                   numeric(2,0)                  
,    TOTAL_CONTRACT_AMOUNT                        numeric(11,0)                 
,    TOTAL_CONTRACT_PRICE                         numeric(11,0)                 
,    MAIN_CONTRACT_AMOUNT                         numeric(11,0)                 
,    MAIN_CONTRACT_PRICE                          numeric(11,0)                 
,    SEPARATE_CONTRACT_AMOUNT                     numeric(11,0)                 
,    SEPARATE_CONTRACT_PRICE                      numeric(11,0)                 
,    RESERVE2                                     numeric(11,0)                 
,    STAMP_DUTY_COLLECTION_AMOUNT                 numeric(11,0)                 
,    PREP_FUND_A_APPLICATION_AMOUNT_TAX_INCL      numeric(11,0)                 
,    CONTRACT_DISCOUNT_AMOUNT                     numeric(11,0)                 
,    DISCOUNT_APPROVAL_NO1                        varchar(8)                    
,    DISCOUNT_USAGE_AMOUNT1_TAX_INCL              numeric(11,0)                 
,    DISCOUNT_APPROVAL_NO2                        varchar(8)                    
,    DISCOUNT_USAGE_AMOUNT2_TAX_INCL              numeric(11,0)                 
,    DISCOUNT_APPROVAL_NO3                        varchar(8)                    
,    DISCOUNT_USAGE_AMOUNT3_TAX_INCL              numeric(11,0)                 
,    DISCOUNT_APPROVAL_NO4                        varchar(8)                    
,    DISCOUNT_USAGE_AMOUNT4_TAX_INCL              numeric(11,0)                 
,    DISCOUNT_APPROVAL_NO5                        varchar(8)                    
,    DISCOUNT_USAGE_AMOUNT5_TAX_INCL              numeric(11,0)                 
,    DISCOUNT_UNALLOCATED_AMOUNT                  numeric(11,0)                 
,    INFORMATION_REWARD_USAGE_AMOUNT              numeric(11,0)                 
,    PREPARATION_FUND_A_USAGE_AMOUNT_TAX_INCL     numeric(11,0)                 
,    INFORMATION_APPROVAL_NO1                     varchar(8)                    
,    INFORMATION_USAGE_AMOUNT1_TAX_INCL           numeric(11,0)                 
,    INFORMATION_APPROVAL_NO2                     varchar(8)                    
,    INFORMATION_USAGE_AMOUNT2_TAX_INCL           numeric(11,0)                 
,    INFORMATION_APPROVAL_NO3                     varchar(8)                    
,    INFORMATION_USAGE_AMOUNT3_TAX_INCL           numeric(11,0)                 
,    INFORMATION_APPROVAL_NO4                     varchar(8)                    
,    INFORMATION_USAGE_AMOUNT4_TAX_INCL           numeric(11,0)                 
,    INFORMATION_UNALLOCATED_AMOUNT               numeric(11,0)                 
,    PROCESS_CD1                                  numeric(2,0)                  
,    PAYMENT_AMOUNT1                              numeric(11,0)                 
,    PROCESS_CD2                                  numeric(2,0)                  
,    PAYMENT_AMOUNT2                              numeric(11,0)                 
,    PROCESS_CD3                                  numeric(2,0)                  
,    PAYMENT_AMOUNT3                              numeric(11,0)                 
,    PROCESS_CD4                                  numeric(2,0)                  
,    PAYMENT_AMOUNT4                              numeric(11,0)                 
,    PROCESS_CD5                                  numeric(2,0)                  
,    PAYMENT_AMOUNT5                              numeric(11,0)                 
,    PROCESS_CD6                                  numeric(2,0)                  
,    PAYMENT_AMOUNT6                              numeric(11,0)                 
,    GROSS_MARGIN_RATE_CONTRACT                   numeric(5,2)                  
,    GROSS_MARGIN_RATE_CURRENT                    numeric(5,2)                  
,    MAIN_GROSS_MARGIN_RATE_CONTRACT              numeric(5,2)                  
,    MAIN_GROSS_MARGIN_RATE_CURRENT               numeric(5,2)                  
,    SEPARATE_GROSS_MARGIN_RATE_CONTRACT          numeric(5,2)                  
,    SEPARATE_GROSS_MARGIN_RATE_CURRENT           numeric(5,2)                  
,    ADDITIONAL_COMMISSION_RATE_CONTRACT          numeric(3,2)                  
,    ADDITIONAL_COMMISSION_RATE_CURRENT           numeric(3,2)                  
,    MAIN_COMMISSION_RATE_CONTRACT                numeric(3,2)                  
,    MAIN_COMMISSION_RATE_CURRENT                 numeric(3,2)                  
,    SEPARATE_COMMISSION_RATE_CONTRACT            numeric(3,2)                  
,    SEPARATE_COMMISSION_RATE_CURRENT             numeric(3,2)                  
,    CONTRACT_CHANGE_COUNT                        numeric(2,0)                  
,    BUDGET_VARIATION_EXISTENCE_CATEGORY          numeric(1,0)                  
,    SPECIAL_COND_CHANGE_EXISTENCE_CATEGORY       numeric(1,0)                  
,    START_TIMING_ORIGINAL_CONTRACT_DAYS          numeric(4,0)                  
,    COMPLETION_TIMING_ORIGINAL_CONTRACT_DAYS     numeric(4,0)                  
,    AMOUNT_CHANGE_BEFORE_CHANGE_AMOUNT           numeric(11,0)                 
,    AMOUNT_CHANGE_VARIATION_AMOUNT               numeric(11,0)                 
,    ADDITIONAL_CONSTRUCTION_AMOUNT               numeric(11,0)                 
,    CANCELLED_CONSTRUCTION_AMOUNT                numeric(11,0)                 
,    DISCOUNT_AMOUNT                              numeric(11,0)                 
,    ADDITIONAL_CONSTRUCTION_AMOUNT_CUMULATIVE    numeric(11,0)                 
,    CANCELLED_CONSTRUCTION_AMOUNT_CUMULATIVE     numeric(11,0)                 
,    DISCOUNT_AMOUNT_CUMULATIVE                   numeric(11,0)                 
,    RESERVE3                                     numeric(1,0)                  
,    RETURN_CATEGORY                              numeric(1,0)                  
,    AGREED_TERMINATION_REASON_CD                 numeric(2,0)                  
,    RETURN_AMOUNT                                numeric(11,0)                 
,    TRANSFER_AMOUNT                              numeric(11,0)                 
,    ADDITIONAL_COLLECTION_AMOUNT                 numeric(11,0)                 
,    CONSTRUCTION_ADVANCE_REMAINING_BALANCE       numeric(11,0)                 
,    MISCELLANEOUS_EXPENSES                       numeric(11,0)                 
,    TRANSFER_DESTINATION_PROJECT_CD              numeric(8,0)                  
,    TRANSFER_DESTINATION_CONTRACT_NO             numeric(3,0)                  
,    TERMINATION_REPORT_FLAG                      numeric(1,0)                  
,    CONTRACT_CONTENT_REPORT_DATE                 numeric(8,0)                  
,    AGREED_TERMINATION_REPORT_DATE               numeric(8,0)                  
,    PROCESS_CD7                                  numeric(2,0)                  
,    PAYMENT_AMOUNT7                              numeric(11,0)                 
,    PROCESS_CD8                                  numeric(2,0)                  
,    PAYMENT_AMOUNT8                              numeric(11,0)                 
,    PROCESS_CD9                                  numeric(2,0)                  
,    PAYMENT_AMOUNT9                              numeric(11,0)                 
,    PROCESS_CD10                                 numeric(2,0)                  
,    PAYMENT_AMOUNT10                             numeric(11,0)                 
,    COLLECTION_PERFORMANCE_AMOUNT                numeric(11,0)                 
,    PLOT_CHANGE_BEFORE_CHANGE_PLOTS              numeric(2,1)                  
,    PLOT_CHANGE_VARIATION_PLOTS                  numeric(2,1)                  
,    RESERVE4                                     numeric(1,0)                  
,    RESERVE5                                     numeric(3,0)                  
,    RESERVE6                                     numeric(2,0)                  
,    RESERVE7                                     numeric(4,0)                  
,    BRANCH_TRANSFER_PROCESS_DATE                 numeric(8,0)                  
,    PROCESS_DATE                                 numeric(8,0)                  
,    BRANCH_TRANSFER_BUSINESS_PLACE               numeric(3,0)                  
,    POST_COMPLETION_DISCOUNT_AMOUNT              numeric(11,0)                 
,    POST_COMPLETION_DISCOUNT_APPROVAL_NO         varchar(9)                    
,    POST_COMPLETION_DISCOUNT_ACHIEVEMENT_DATE    numeric(8,0)                  
,    POST_COMPLETION_DISCOUNT_INPUT_DATE          numeric(8,0)                  
,    SETTLEMENT_AMOUNT_SALES_COMPANY              numeric(11,0)                 
,    SETTLEMENT_AMOUNT_CONSTRUCTION               numeric(11,0)                 
,    PROJECT_CD                                   numeric(8,0)                  
,    BRANCH_MANAGER                               numeric(5,0)                  
,    COST_ACCOUNTING_MONTH                        numeric(6,0)                  
,    ANCILLARY_CONTRACT_AMOUNT                    numeric(11,0)                 
,    ANCILLARY_CONTRACT_PRICE                     numeric(11,0)                 
,    LANDSCAPING_CONTRACT_AMOUNT                  numeric(11,0)                 
,    LANDSCAPING_CONTRACT_PRICE                   numeric(11,0)                 
,    OTHER_CONTRACT_AMOUNT                        numeric(11,0)                 
,    OTHER_CONTRACT_PRICE                         numeric(11,0)                 
,    ANCILLARY_GROSS_MARGIN_RATE_CONTRACT         numeric(5,2)                  
,    ANCILLARY_GROSS_MARGIN_RATE_CURRENT          numeric(5,2)                  
,    LANDSCAPING_GROSS_MARGIN_RATE_CONTRACT       numeric(5,2)                  
,    LANDSCAPING_GROSS_MARGIN_RATE_CURRENT        numeric(5,2)                  
,    OTHER_GROSS_MARGIN_RATE_CONTRACT             numeric(5,2)                  
,    OTHER_GROSS_MARGIN_RATE_CURRENT              numeric(5,2)                  
,    ANCILLARY_COMMISSION_RATE_CONTRACT           numeric(3,2)                  
,    ANCILLARY_COMMISSION_RATE_CURRENT            numeric(3,2)                  
,    LANDSCAPING_COMMISSION_RATE_CONTRACT         numeric(3,2)                  
,    LANDSCAPING_COMMISSION_RATE_CURRENT          numeric(3,2)                  
,    OTHER_COMMISSION_RATE_CONTRACT               numeric(3,2)                  
,    OTHER_COMMISSION_RATE_CURRENT                numeric(3,2)                  
,    CONTRACT_AMOUNT_EXCLUDING_TAX                numeric(11,0)                 
,    CONTRACT_AMOUNT_CONSUMPTION_TAX1             numeric(11,0)                 
,    CONTRACT_AMOUNT_CONSUMPTION_TAX2             numeric(11,0)                 
,    MAIN_CONSTRUCTION_EXCLUDING_TAX              numeric(11,0)                 
,    MAIN_CONSTRUCTION_CONSUMPTION_TAX            numeric(11,0)                 
,    ANCILLARY_CONSTRUCTION_EXCLUDING_TAX         numeric(11,0)                 
,    ANCILLARY_CONSTRUCTION_CONSUMPTION_TAX       numeric(11,0)                 
,    LANDSCAPING_CONSTRUCTION_EXCLUDING_TAX       numeric(11,0)                 
,    LANDSCAPING_CONSTRUCTION_CONSUMPTION_TAX     numeric(11,0)                 
,    OTHER_CONSTRUCTION_EXCLUDING_TAX             numeric(11,0)                 
,    OTHER_CONSTRUCTION_CONSUMPTION_TAX           numeric(11,0)                 
,    SEPARATE_CONSTRUCTION_EXCLUDING_TAX          numeric(11,0)                 
,    SEPARATE_CONSTRUCTION_CONSUMPTION_TAX        numeric(11,0)                 
,    PREPARATION_FUND_A_APPLICATION_AMOUNT        numeric(11,0)                 
,    DISCOUNT_USAGE_AMOUNT1                       numeric(11,0)                 
,    DISCOUNT_USAGE_AMOUNT2                       numeric(11,0)                 
,    DISCOUNT_USAGE_AMOUNT3                       numeric(11,0)                 
,    DISCOUNT_USAGE_AMOUNT4                       numeric(11,0)                 
,    DISCOUNT_USAGE_AMOUNT5                       numeric(11,0)                 
,    PREPARATION_FUND_A_USAGE_AMOUNT              numeric(11,0)                 
,    INFORMATION_USAGE_AMOUNT1                    numeric(11,0)                 
,    INFORMATION_USAGE_AMOUNT2                    numeric(11,0)                 
,    INFORMATION_USAGE_AMOUNT3                    numeric(11,0)                 
,    INFORMATION_USAGE_AMOUNT4                    numeric(11,0)                 
,    PLAN_CHANGE_EXISTENCE_CATEGORY               numeric(1,0)                  
,    COMMISSION_SYSTEM_CHANGE_CATEGORY            numeric(1,0)                  
,    COMMISSION_APPLICABLE_DATE                   numeric(8,0)                  
,    CONSUMPTION_TAX_AMOUNT_CHANGE_EXISTENCE      numeric(1,0)                  
,    BULK_ORDER_FLAG                              varchar(1)                    
,    SPECIAL_RENTAL_CATEGORY                      varchar(1)                    
,    LARGE_PROPERTY_CATEGORY                      varchar(1)                    
,    COMMISSION_TARGET_CATEGORY                   varchar(1)                    
,    INFORMATION_PARTNER                          varchar(2)                    
,    TENANT_CONTRACT                              varchar(8)                    
,    REWARD_AMOUNT                                numeric(11,0)                 
,    CONVERSION_COMPLEX_CATEGORY                  varchar(1)                    
,    COMPENSATION_AMOUNT                          numeric(11,0)                 
,    ASSESSMENT_SECTION_MANAGER                   varchar(6)                    
,    RESERVE8                                     varchar(8)                    
,    RESERVE9                                     varchar(7)                    
,    RESERVE10                                    varchar(3)                    
,    FIRST_EXCLUSION_FLAG                         varchar(1)                    
,    EXCLUSION_AMOUNT                             numeric(11,0)                 
,    RESERVE11                                    varchar(7)                    
,    RESERVE12                                    varchar(3)                    
,    INTEREST_BURDEN                              varchar(1)                    
,    SUBSIDY_TYPE                                 varchar(1)                    
,    MAINTENANCE_TYPE                             varchar(1)                    
,    SURVEY_PROJECT_AMOUNT                        numeric(11,0)                 
,    BASIC_DESIGN_AMOUNT                          numeric(11,0)                 
,    DETAILED_DESIGN_AMOUNT                       numeric(11,0)                 
,    MANAGEMENT_FORM                              numeric(1,0)                  
,    PARTNER_COMPANY_CD                           varchar(9)                    
,    ASSESSMENT_BRANCH_CD                         varchar(6)                    
,    MORTGAGE_SETTING                             numeric(1,0)                  
,    MORTGAGE_AMOUNT                              numeric(11,0)                 
,    MANAGEMENT_FEE                               numeric(1,0)                  
,    EXCLUSION_CONVERSION_AMOUNT                  numeric(11,0)                 
,    TOTAL_PRICE                                  numeric(11,0)                 
,    MAIN_PRICE                                   numeric(11,0)                 
,    ANCILLARY_PRICE                              numeric(11,0)                 
,    LANDSCAPING_PRICE                            numeric(11,0)                 
,    OTHER_PRICE                                  numeric(11,0)                 
,    SEPARATE_PRICE                               numeric(11,0)                 
,    TOTAL_PRICE_GROSS_MARGIN_RATE                numeric(4,2)                  
,    MAIN_PRICE_GROSS_MARGIN_RATE                 numeric(4,2)                  
,    ANCILLARY_PRICE_GROSS_MARGIN_RATE            numeric(4,2)                  
,    LANDSCAPING_PRICE_GROSS_MARGIN_RATE          numeric(4,2)                  
,    OTHER_PRICE_GROSS_MARGIN_RATE                numeric(4,2)                  
,    SEPARATE_PRICE_GROSS_MARGIN_RATE             numeric(4,2)                  
,    TOTAL_PRICE_USAGE_AMOUNT                     numeric(11,0)                 
,    MAIN_PRICE_USAGE_AMOUNT                      numeric(11,0)                 
,    ANCILLARY_PRICE_USAGE_AMOUNT                 numeric(11,0)                 
,    LANDSCAPING_PRICE_USAGE_AMOUNT               numeric(11,0)                 
,    OTHER_PRICE_USAGE_AMOUNT                     numeric(11,0)                 
,    SEPARATE_PRICE_USAGE_AMOUNT                  numeric(11,0)                 
,    CONTRACT_COMMISSION_RATE_CONTRACT            numeric(3,2)                  
,    CONTRACT_COMMISSION_RATE_CURRENT             numeric(3,2)                  
,    INFORMATION_PREPARATION_FUND_B_USAGE         numeric(11,0)                 
,    INFORMATION_REGULATIONS                      numeric(1,0)                  
,    REWARD_RATE                                  numeric(4,2)                  
,    REWARD_AMOUNT2                               numeric(11,0)                 
,    BUSINESS_PROJECTION_CD1                      numeric(6,0)                  
,    BUSINESS_PROJECTION_CD2                      numeric(8,0)                  
,    CHANGE_APPROVAL_FLAG                         numeric(1,0)                  
,    PARTNER_COMPANY_INTRODUCTION                 varchar(1)                    
,    CONTRACT_CD_ST                               varchar(7)                    
,    CONTRACT_ADDITIONAL_CD_ST                    varchar(3)                    
,    UPDATE_EMPLOYEE_NO_ST                        varchar(6)                    
,    CONTRACT_RESPONSIBLE_PERSON_ST               varchar(6)                    
,    CONTRACT_SECTION_MANAGER_ST                  varchar(6)                    
,    CONTRACT_COMPANION_ST                        varchar(6)                    
,    LAND_USE_CATEGORY_ST                         varchar(2)                    
,    TRANSFER_DESTINATION_PROJECT_CD_ST           varchar(7)                    
,    TRANSFER_DESTINATION_CONTRACT_NO_ST          varchar(3)                    
,    BRANCH_MANAGER_ST                            varchar(6)                    
,    CONSTRUCTION_BRANCH_JA                       varchar(3)                    
,    CONTRACT_BRANCH_JA                           varchar(3)                    
,    BRANCH_TRANSFER_BUSINESS_PLACE_JA            varchar(3)                    
,    CONTRACT_DEPUTY_MANAGER                      varchar(6)                    
,    AFFILIATED_MEMBER1                           varchar(6)                    
,    AFFILIATED_MEMBER2                           varchar(6)                    
,    AFFILIATED_MEMBER3                           varchar(6)                    
,    AFFILIATED_MEMBER4                           varchar(6)                    
,    AFFILIATED_MEMBER5                           varchar(6)                    
,    AFFILIATED_MEMBER6                           varchar(6)                    
,    AFFILIATED_MEMBER7                           varchar(6)                    
,    PERFORMANCE_RECORDING_BRANCH                 varchar(3)                    
,    PERFORMANCE_RECORDING_SECTION                varchar(3)                    
,    RENT_PREPAYMENT_APPLICABLE_CATEGORY          numeric(1,0)                  
,    CONSTRAINT UQ_CONTRACT_FILE UNIQUE (CONTRACT_CD, CONTRACT_ADDITIONAL_CD, RECORD_DELETE_FLAG)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE CONTRACT_FILE IS '請負契約ファイル 既存システム物理名: AEUKYP';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_CD IS '契約コード 既存システム物理名: AEAKCD 契約追加コード ?ST';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_ADDITIONAL_CD IS '契約追加コード 既存システム物理名: AEAKEN 更新社員番号 ?ST';
COMMENT ON COLUMN CONTRACT_FILE.CHANGE_HISTORY_NO IS '変更履歴番号 既存システム物理名: AEARNO';
COMMENT ON COLUMN CONTRACT_FILE.RECORD_DELETE_FLAG IS 'レコード削除フラグ 既存システム物理名: AEADLT';
COMMENT ON COLUMN CONTRACT_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: AEACDT';
COMMENT ON COLUMN CONTRACT_FILE.CREATION_TIME IS '作成時刻 既存システム物理名: AEACTM';
COMMENT ON COLUMN CONTRACT_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: AEAUDT';
COMMENT ON COLUMN CONTRACT_FILE.UPDATE_TIME IS '更新時刻 既存システム物理名: AEAUTM';
COMMENT ON COLUMN CONTRACT_FILE.UPDATE_PROGRAM IS '更新プログラム 既存システム物理名: AEAUPG';
COMMENT ON COLUMN CONTRACT_FILE.UPDATE_EMPLOYEE_NO IS '更新社員番号 既存システム物理名: AEAUSY';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_STATUS_CATEGORY IS '契約状態区分 既存システム物理名: AEA001';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_CONTENT_REPORT_FLAG IS '契約内容報告フラグ 既存システム物理名: AEA002';
COMMENT ON COLUMN CONTRACT_FILE.TRANSACTION_CATEGORY IS '取引区分 既存システム物理名: AEA0C1';
COMMENT ON COLUMN CONTRACT_FILE.ORDERER_CD IS '注文者コード 既存システム物理名: AEA003';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_DATE_CHANGE_DATE IS '契約日／契約変更日 既存システム物理名: AEA004';
COMMENT ON COLUMN CONTRACT_FILE.ORIGINAL_CONTRACT_DATE IS '原契約年月日 既存システム物理名: AEA005';
COMMENT ON COLUMN CONTRACT_FILE.AGREED_TERMINATION_DATE IS '合意解約年月日 既存システム物理名: AEA006';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_JUDGMENT_CATEGORY IS '契約判定区分 既存システム物理名: AEA007';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_JUDGMENT_DATE IS '契約判定年月日 既存システム物理名: AEA008';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_JUDGMENT_DEPT_CATEGORY IS '契約判定部署区分 既存システム物理名: AEA009';
COMMENT ON COLUMN CONTRACT_FILE.CONSTRUCTION_BRANCH IS '施工支店 既存システム物理名: AEA010';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_BRANCH IS '契約支店 既存システム物理名: AEA011';
COMMENT ON COLUMN CONTRACT_FILE.AFFILIATED_SECTION IS '所属課 既存システム物理名: AEA012';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_RESPONSIBLE_PERSON IS '契約担当者 既存システム物理名: AEA013';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_SECTION_MANAGER IS '契約課長 既存システム物理名: AEA014';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_COMPANION IS '契約同行者 既存システム物理名: AEA015';
COMMENT ON COLUMN CONTRACT_FILE.BUSINESS_PROJECTION_NO IS '事業試算書枝番 既存システム物理名: AEA016';
COMMENT ON COLUMN CONTRACT_FILE.REPEAT_CATEGORY IS 'リピート区分 既存システム物理名: AEA017';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_CATEGORY IS '契約区分 既存システム物理名: AEA019';
COMMENT ON COLUMN CONTRACT_FILE.MAINTENANCE_CATEGORY IS '営繕区分 既存システム物理名: AEA020';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_PARTNER_CATEGORY IS '契約相手先区分 既存システム物理名: AEA021';
COMMENT ON COLUMN CONTRACT_FILE.ACTIVITY_AREA IS '活動エリア 既存システム物理名: AEA022';
COMMENT ON COLUMN CONTRACT_FILE.RESERVE1 IS '予備1 既存システム物理名: AEA023';
COMMENT ON COLUMN CONTRACT_FILE.OTHER_COMPANY_FLAG IS '他社物フラグ 既存システム物理名: AEA024';
COMMENT ON COLUMN CONTRACT_FILE.ADDITIONAL_CONSTRUCTION_CATEGORY IS '追加工事区分 既存システム物理名: AEA025';
COMMENT ON COLUMN CONTRACT_FILE.LAND_CD IS '土地コード 既存システム物理名: AEA026';
COMMENT ON COLUMN CONTRACT_FILE.CONSTRUCTION_NAME IS '工事名称 既存システム物理名: AEA027';
COMMENT ON COLUMN CONTRACT_FILE.CONSTRUCTION_START_TIMING_CATEGORY IS '工事着手時期区分 既存システム物理名: AEA028';
COMMENT ON COLUMN CONTRACT_FILE.CONSTRUCTION_PERIOD_START_TIMING IS '工期・工事着手時期 既存システム物理名: AEA029';
COMMENT ON COLUMN CONTRACT_FILE.CONSTRUCTION_PERIOD_COMPLETION_TIMING IS '工期・完成時期 既存システム物理名: AEA030';
COMMENT ON COLUMN CONTRACT_FILE.LAND_AREA_M IS '敷地面積(M) 既存システム物理名: AEA031';
COMMENT ON COLUMN CONTRACT_FILE.LAND_AREA_TSUBO IS '敷地面積(坪) 既存システム物理名: AEA032';
COMMENT ON COLUMN CONTRACT_FILE.CONSTRUCTION_FLOOR_AREA_M IS '建築施工床面積M 既存システム物理名: AEA033';
COMMENT ON COLUMN CONTRACT_FILE.CONSTRUCTION_FLOOR_AREA_TSUBO IS '建築施工床面積坪 既存システム物理名: AEA034';
COMMENT ON COLUMN CONTRACT_FILE.WAREHOUSE_FACTORY_AREA_M IS '倉庫工場部分面積M 既存システム物理名: AEA035';
COMMENT ON COLUMN CONTRACT_FILE.WAREHOUSE_FACTORY_AREA_TSUBO IS '倉庫工場部分面積坪 既存システム物理名: AEA036';
COMMENT ON COLUMN CONTRACT_FILE.BUILDING_TYPE_ST IS '建物種別-ST 既存システム物理名: AEA037';
COMMENT ON COLUMN CONTRACT_FILE.NUMBER_OF_BUILDINGS IS '棟数 既存システム物理名: AEA038';
COMMENT ON COLUMN CONTRACT_FILE.NUMBER_OF_ROOMS IS '部屋数 既存システム物理名: AEA039';
COMMENT ON COLUMN CONTRACT_FILE.FIRE_PROTECTION_CATEGORY IS '防火区分 既存システム物理名: AEA040';
COMMENT ON COLUMN CONTRACT_FILE.LAND_USE_CATEGORY IS '用途地域区分 既存システム物理名: AEA041';
COMMENT ON COLUMN CONTRACT_FILE.LEASE_CATEGORY IS 'リース区分 既存システム物理名: AEA042';
COMMENT ON COLUMN CONTRACT_FILE.SET_CATEGORY IS 'セット区分 既存システム物理名: AEA043';
COMMENT ON COLUMN CONTRACT_FILE.RENT_REVIEW_CATEGORY IS '家賃審査区分 既存システム物理名: AEA044';
COMMENT ON COLUMN CONTRACT_FILE.MANAGEMENT_AVAILABILITY_CATEGORY IS '管理可否区分 既存システム物理名: AEA045';
COMMENT ON COLUMN CONTRACT_FILE.GUARANTEE_CATEGORY IS '保証区分 既存システム物理名: AEA046';
COMMENT ON COLUMN CONTRACT_FILE.MUTUAL_AID_ASSOCIATION_AVAILABILITY IS '共済会加入可否 既存システム物理名: AEA047';
COMMENT ON COLUMN CONTRACT_FILE.WATER_CATEGORY IS '水道区分 既存システム物理名: AEA048';
COMMENT ON COLUMN CONTRACT_FILE.DEVELOPMENT_APPLICATION_CATEGORY IS '開発申請区分 既存システム物理名: AEA049';
COMMENT ON COLUMN CONTRACT_FILE.AGRICULTURAL_CONVERSION_CATEGORY IS '農転区分 既存システム物理名: AEA050';
COMMENT ON COLUMN CONTRACT_FILE.TAX_ACCOUNTANT_COMPANION_CATEGORY IS '税理士同行区分 既存システム物理名: AEA051';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION IS '耳より情報 既存システム物理名: AEA052';
COMMENT ON COLUMN CONTRACT_FILE.NUMBER_OF_CONTRACT_PLOTS IS '契約筆数 既存システム物理名: AEA053';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_PAYMENT_CATEGORY IS '契約時金区分 既存システム物理名: AEA054';
COMMENT ON COLUMN CONTRACT_FILE.FINANCING_SOURCE_CATEGORY1 IS '融資先区分1 既存システム物理名: AEA055';
COMMENT ON COLUMN CONTRACT_FILE.FINANCING_SOURCE_CATEGORY2 IS '融資先区分2 既存システム物理名: AEA056';
COMMENT ON COLUMN CONTRACT_FILE.FINANCING_SOURCE_CATEGORY3 IS '融資先区分3 既存システム物理名: AEA057';
COMMENT ON COLUMN CONTRACT_FILE.TOTAL_CONTRACT_AMOUNT IS '請負総額 請負額 既存システム物理名: AEA058';
COMMENT ON COLUMN CONTRACT_FILE.TOTAL_CONTRACT_PRICE IS '請負総額 定価 既存システム物理名: AEA059';
COMMENT ON COLUMN CONTRACT_FILE.MAIN_CONTRACT_AMOUNT IS '本体請負額 請負額 既存システム物理名: AEA060';
COMMENT ON COLUMN CONTRACT_FILE.MAIN_CONTRACT_PRICE IS '本体請負額 定価 既存システム物理名: AEA061';
COMMENT ON COLUMN CONTRACT_FILE.SEPARATE_CONTRACT_AMOUNT IS '別途請負額 請負額 既存システム物理名: AEA062';
COMMENT ON COLUMN CONTRACT_FILE.SEPARATE_CONTRACT_PRICE IS '別途請負額 定価 既存システム物理名: AEA063';
COMMENT ON COLUMN CONTRACT_FILE.RESERVE2 IS '予備2 既存システム物理名: AEA064';
COMMENT ON COLUMN CONTRACT_FILE.STAMP_DUTY_COLLECTION_AMOUNT IS '印紙代回収予定額 既存システム物理名: AEA065';
COMMENT ON COLUMN CONTRACT_FILE.PREP_FUND_A_APPLICATION_AMOUNT_TAX_INCL IS '準備金Ａ申請額税込 既存システム物理名: AEA066';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_DISCOUNT_AMOUNT IS '契約時値引額 既存システム物理名: AEA067';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_APPROVAL_NO1 IS '値引承認番号1 既存システム物理名: AEA068';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_USAGE_AMOUNT1_TAX_INCL IS '値引使用金1・税込 既存システム物理名: AEA069';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_APPROVAL_NO2 IS '値引承認番号2 既存システム物理名: AEA070';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_USAGE_AMOUNT2_TAX_INCL IS '値引使用金2・税込 既存システム物理名: AEA071';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_APPROVAL_NO3 IS '値引承認番号3 既存システム物理名: AEA072';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_USAGE_AMOUNT3_TAX_INCL IS '値引使用金3・税込 既存システム物理名: AEA073';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_APPROVAL_NO4 IS '値引承認番号4 既存システム物理名: AEA074';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_USAGE_AMOUNT4_TAX_INCL IS '値引使用金4・税込 既存システム物理名: AEA075';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_APPROVAL_NO5 IS '値引承認番号5 既存システム物理名: AEA076';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_USAGE_AMOUNT5_TAX_INCL IS '値引使用金5・税込 既存システム物理名: AEA077';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_UNALLOCATED_AMOUNT IS '値引充当無金額 既存システム物理名: AEA078';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_REWARD_USAGE_AMOUNT IS '耳より報奨金使用額 既存システム物理名: AEA079';
COMMENT ON COLUMN CONTRACT_FILE.PREPARATION_FUND_A_USAGE_AMOUNT_TAX_INCL IS '準備金Ａ使用額税込 既存システム物理名: AEA080';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_APPROVAL_NO1 IS '耳より承認番号1 既存システム物理名: AEA081';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_USAGE_AMOUNT1_TAX_INCL IS '耳より使用額1税込 既存システム物理名: AEA082';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_APPROVAL_NO2 IS '耳より承認番号2 既存システム物理名: AEA083';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_USAGE_AMOUNT2_TAX_INCL IS '耳より使用額2税込 既存システム物理名: AEA084';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_APPROVAL_NO3 IS '耳より承認番号3 既存システム物理名: AEA085';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_USAGE_AMOUNT3_TAX_INCL IS '耳より使用額3税込 既存システム物理名: AEA086';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_APPROVAL_NO4 IS '耳より承認番号4 既存システム物理名: AEA087';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_USAGE_AMOUNT4_TAX_INCL IS '耳より使用額4税込 既存システム物理名: AEA088';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_UNALLOCATED_AMOUNT IS '耳より充当無金額 既存システム物理名: AEA089';
COMMENT ON COLUMN CONTRACT_FILE.PROCESS_CD1 IS '工程コード1 既存システム物理名: AEA090';
COMMENT ON COLUMN CONTRACT_FILE.PAYMENT_AMOUNT1 IS '支払金額1 既存システム物理名: AEA091';
COMMENT ON COLUMN CONTRACT_FILE.PROCESS_CD2 IS '工程コード2 既存システム物理名: AEA092';
COMMENT ON COLUMN CONTRACT_FILE.PAYMENT_AMOUNT2 IS '支払金額2 既存システム物理名: AEA093';
COMMENT ON COLUMN CONTRACT_FILE.PROCESS_CD3 IS '工程コード3 既存システム物理名: AEA094';
COMMENT ON COLUMN CONTRACT_FILE.PAYMENT_AMOUNT3 IS '支払金額3 既存システム物理名: AEA095';
COMMENT ON COLUMN CONTRACT_FILE.PROCESS_CD4 IS '工程コード4 既存システム物理名: AEA096';
COMMENT ON COLUMN CONTRACT_FILE.PAYMENT_AMOUNT4 IS '支払金額4 既存システム物理名: AEA097';
COMMENT ON COLUMN CONTRACT_FILE.PROCESS_CD5 IS '工程コード5 既存システム物理名: AEA098';
COMMENT ON COLUMN CONTRACT_FILE.PAYMENT_AMOUNT5 IS '支払金額5 既存システム物理名: AEA099';
COMMENT ON COLUMN CONTRACT_FILE.PROCESS_CD6 IS '工程コード6 既存システム物理名: AEA100';
COMMENT ON COLUMN CONTRACT_FILE.PAYMENT_AMOUNT6 IS '支払金額6 既存システム物理名: AEA101';
COMMENT ON COLUMN CONTRACT_FILE.GROSS_MARGIN_RATE_CONTRACT IS '粗利率 契約時 既存システム物理名: AEA102';
COMMENT ON COLUMN CONTRACT_FILE.GROSS_MARGIN_RATE_CURRENT IS '粗利率 現在 既存システム物理名: AEA103';
COMMENT ON COLUMN CONTRACT_FILE.MAIN_GROSS_MARGIN_RATE_CONTRACT IS '本体粗利率 契約時 既存システム物理名: AEA104';
COMMENT ON COLUMN CONTRACT_FILE.MAIN_GROSS_MARGIN_RATE_CURRENT IS '本体粗利率 現在 既存システム物理名: AEA105';
COMMENT ON COLUMN CONTRACT_FILE.SEPARATE_GROSS_MARGIN_RATE_CONTRACT IS '別途粗利率 契約時 既存システム物理名: AEA106';
COMMENT ON COLUMN CONTRACT_FILE.SEPARATE_GROSS_MARGIN_RATE_CURRENT IS '別途粗利率 現在 既存システム物理名: AEA107';
COMMENT ON COLUMN CONTRACT_FILE.ADDITIONAL_COMMISSION_RATE_CONTRACT IS '追加歩合給率契約時 既存システム物理名: AEA108';
COMMENT ON COLUMN CONTRACT_FILE.ADDITIONAL_COMMISSION_RATE_CURRENT IS '追加歩合給率現在 既存システム物理名: AEA109';
COMMENT ON COLUMN CONTRACT_FILE.MAIN_COMMISSION_RATE_CONTRACT IS '本体歩合給率契約時 既存システム物理名: AEA110';
COMMENT ON COLUMN CONTRACT_FILE.MAIN_COMMISSION_RATE_CURRENT IS '本体歩合給率現在 既存システム物理名: AEA111';
COMMENT ON COLUMN CONTRACT_FILE.SEPARATE_COMMISSION_RATE_CONTRACT IS '別途歩合給率契約時 既存システム物理名: AEA112';
COMMENT ON COLUMN CONTRACT_FILE.SEPARATE_COMMISSION_RATE_CURRENT IS '別途歩合給率現在 既存システム物理名: AEA113';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_CHANGE_COUNT IS '契約変更回数 既存システム物理名: AEA114';
COMMENT ON COLUMN CONTRACT_FILE.BUDGET_VARIATION_EXISTENCE_CATEGORY IS '予算変動有無区分 既存システム物理名: AEA115';
COMMENT ON COLUMN CONTRACT_FILE.SPECIAL_COND_CHANGE_EXISTENCE_CATEGORY IS '特約事項変更有無区 既存システム物理名: AEA116';
COMMENT ON COLUMN CONTRACT_FILE.START_TIMING_ORIGINAL_CONTRACT_DAYS IS '着手時期原契約日数 既存システム物理名: AEA117';
COMMENT ON COLUMN CONTRACT_FILE.COMPLETION_TIMING_ORIGINAL_CONTRACT_DAYS IS '完成時期原契約日数 既存システム物理名: AEA118';
COMMENT ON COLUMN CONTRACT_FILE.AMOUNT_CHANGE_BEFORE_CHANGE_AMOUNT IS '金額変更変更前請額 既存システム物理名: AEA119';
COMMENT ON COLUMN CONTRACT_FILE.AMOUNT_CHANGE_VARIATION_AMOUNT IS '金額変更変更増減額 既存システム物理名: AEA120';
COMMENT ON COLUMN CONTRACT_FILE.ADDITIONAL_CONSTRUCTION_AMOUNT IS '追加工事金額 既存システム物理名: AEA121';
COMMENT ON COLUMN CONTRACT_FILE.CANCELLED_CONSTRUCTION_AMOUNT IS '取り止め工事金額 既存システム物理名: AEA122';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_AMOUNT IS '値引き金額 既存システム物理名: AEA123';
COMMENT ON COLUMN CONTRACT_FILE.ADDITIONAL_CONSTRUCTION_AMOUNT_CUMULATIVE IS '追加工事金額・累計 既存システム物理名: AEA124';
COMMENT ON COLUMN CONTRACT_FILE.CANCELLED_CONSTRUCTION_AMOUNT_CUMULATIVE IS '取止工事金額・累計 既存システム物理名: AEA125';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_AMOUNT_CUMULATIVE IS '値引き金額・累計 既存システム物理名: AEA126';
COMMENT ON COLUMN CONTRACT_FILE.RESERVE3 IS '予備3 既存システム物理名: AEA128';
COMMENT ON COLUMN CONTRACT_FILE.RETURN_CATEGORY IS '返却区分 既存システム物理名: AEA129';
COMMENT ON COLUMN CONTRACT_FILE.AGREED_TERMINATION_REASON_CD IS '合意解約事由コード 既存システム物理名: AEA130';
COMMENT ON COLUMN CONTRACT_FILE.RETURN_AMOUNT IS '返却金額 既存システム物理名: AEA131';
COMMENT ON COLUMN CONTRACT_FILE.TRANSFER_AMOUNT IS '振替金額 既存システム物理名: AEA132';
COMMENT ON COLUMN CONTRACT_FILE.ADDITIONAL_COLLECTION_AMOUNT IS '追加徴収金額 既存システム物理名: AEA133';
COMMENT ON COLUMN CONTRACT_FILE.CONSTRUCTION_ADVANCE_REMAINING_BALANCE IS '工事立替金残高 既存システム物理名: AEA134';
COMMENT ON COLUMN CONTRACT_FILE.MISCELLANEOUS_EXPENSES IS '諸経費 既存システム物理名: AEA135';
COMMENT ON COLUMN CONTRACT_FILE.TRANSFER_DESTINATION_PROJECT_CD IS '振替先・企画コード 既存システム物理名: AEA136';
COMMENT ON COLUMN CONTRACT_FILE.TRANSFER_DESTINATION_CONTRACT_NO IS '振替先・契約枝番 既存システム物理名: AEA137';
COMMENT ON COLUMN CONTRACT_FILE.TERMINATION_REPORT_FLAG IS '解約報告フラグ 既存システム物理名: AEA139';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_CONTENT_REPORT_DATE IS '契約内容報告日 既存システム物理名: AEA146';
COMMENT ON COLUMN CONTRACT_FILE.AGREED_TERMINATION_REPORT_DATE IS '合意解約報告日 既存システム物理名: AEA147';
COMMENT ON COLUMN CONTRACT_FILE.PROCESS_CD7 IS '工程コード7 既存システム物理名: AEA148';
COMMENT ON COLUMN CONTRACT_FILE.PAYMENT_AMOUNT7 IS '支払金額7 既存システム物理名: AEA149';
COMMENT ON COLUMN CONTRACT_FILE.PROCESS_CD8 IS '工程コード8 既存システム物理名: AEA150';
COMMENT ON COLUMN CONTRACT_FILE.PAYMENT_AMOUNT8 IS '支払金額8 既存システム物理名: AEA151';
COMMENT ON COLUMN CONTRACT_FILE.PROCESS_CD9 IS '工程コード9 既存システム物理名: AEA152';
COMMENT ON COLUMN CONTRACT_FILE.PAYMENT_AMOUNT9 IS '支払金額9 既存システム物理名: AEA153';
COMMENT ON COLUMN CONTRACT_FILE.PROCESS_CD10 IS '工程コード10 既存システム物理名: AEA154';
COMMENT ON COLUMN CONTRACT_FILE.PAYMENT_AMOUNT10 IS '支払金額10 既存システム物理名: AEA155';
COMMENT ON COLUMN CONTRACT_FILE.COLLECTION_PERFORMANCE_AMOUNT IS '回収実績金額 既存システム物理名: AEA160';
COMMENT ON COLUMN CONTRACT_FILE.PLOT_CHANGE_BEFORE_CHANGE_PLOTS IS '筆数変更変更前筆数 既存システム物理名: AEA161';
COMMENT ON COLUMN CONTRACT_FILE.PLOT_CHANGE_VARIATION_PLOTS IS '筆数変更増減筆数 既存システム物理名: AEA162';
COMMENT ON COLUMN CONTRACT_FILE.RESERVE4 IS '予備4 既存システム物理名: AEA163';
COMMENT ON COLUMN CONTRACT_FILE.RESERVE5 IS '予備5 既存システム物理名: AEA164';
COMMENT ON COLUMN CONTRACT_FILE.RESERVE6 IS '予備6 既存システム物理名: AEA165';
COMMENT ON COLUMN CONTRACT_FILE.RESERVE7 IS '予備7 既存システム物理名: AEA166';
COMMENT ON COLUMN CONTRACT_FILE.BRANCH_TRANSFER_PROCESS_DATE IS '引継支店処理日 既存システム物理名: AEA167';
COMMENT ON COLUMN CONTRACT_FILE.PROCESS_DATE IS '処理日 既存システム物理名: AEA168';
COMMENT ON COLUMN CONTRACT_FILE.BRANCH_TRANSFER_BUSINESS_PLACE IS '引継事業所 既存システム物理名: AEA196';
COMMENT ON COLUMN CONTRACT_FILE.POST_COMPLETION_DISCOUNT_AMOUNT IS '完工後値引額 既存システム物理名: AEA170';
COMMENT ON COLUMN CONTRACT_FILE.POST_COMPLETION_DISCOUNT_APPROVAL_NO IS '完工後値引承認番号 既存システム物理名: AEA171';
COMMENT ON COLUMN CONTRACT_FILE.POST_COMPLETION_DISCOUNT_ACHIEVEMENT_DATE IS '完工後値引実績日 既存システム物理名: AEA172';
COMMENT ON COLUMN CONTRACT_FILE.POST_COMPLETION_DISCOUNT_INPUT_DATE IS '完工後値引入力日 既存システム物理名: AEA173';
COMMENT ON COLUMN CONTRACT_FILE.SETTLEMENT_AMOUNT_SALES_COMPANY IS '精算金額(販社) 既存システム物理名: AEA174';
COMMENT ON COLUMN CONTRACT_FILE.SETTLEMENT_AMOUNT_CONSTRUCTION IS '精算金額(建託) 既存システム物理名: AEA175';
COMMENT ON COLUMN CONTRACT_FILE.PROJECT_CD IS '企画コード 既存システム物理名: AEAKKK';
COMMENT ON COLUMN CONTRACT_FILE.BRANCH_MANAGER IS '支店長 既存システム物理名: AEA176';
COMMENT ON COLUMN CONTRACT_FILE.COST_ACCOUNTING_MONTH IS '原価計上年月 既存システム物理名: AEA191';
COMMENT ON COLUMN CONTRACT_FILE.ANCILLARY_CONTRACT_AMOUNT IS '附帯請負額 請負額 既存システム物理名: AEA177';
COMMENT ON COLUMN CONTRACT_FILE.ANCILLARY_CONTRACT_PRICE IS '附帯請負額 定価 既存システム物理名: AEA178';
COMMENT ON COLUMN CONTRACT_FILE.LANDSCAPING_CONTRACT_AMOUNT IS '外構請負額 請負額 既存システム物理名: AEA179';
COMMENT ON COLUMN CONTRACT_FILE.LANDSCAPING_CONTRACT_PRICE IS '外構請負額 定価 既存システム物理名: AEA180';
COMMENT ON COLUMN CONTRACT_FILE.OTHER_CONTRACT_AMOUNT IS 'その他請負額 請負 既存システム物理名: AEA181';
COMMENT ON COLUMN CONTRACT_FILE.OTHER_CONTRACT_PRICE IS 'その他請負額 定価 既存システム物理名: AEA182';
COMMENT ON COLUMN CONTRACT_FILE.ANCILLARY_GROSS_MARGIN_RATE_CONTRACT IS '附帯粗利率 契約時 既存システム物理名: AEA183';
COMMENT ON COLUMN CONTRACT_FILE.ANCILLARY_GROSS_MARGIN_RATE_CURRENT IS '附帯粗利率 現在 既存システム物理名: AEA184';
COMMENT ON COLUMN CONTRACT_FILE.LANDSCAPING_GROSS_MARGIN_RATE_CONTRACT IS '外構粗利率 契約時 既存システム物理名: AEA185';
COMMENT ON COLUMN CONTRACT_FILE.LANDSCAPING_GROSS_MARGIN_RATE_CURRENT IS '外構粗利率 現在 既存システム物理名: AEA186';
COMMENT ON COLUMN CONTRACT_FILE.OTHER_GROSS_MARGIN_RATE_CONTRACT IS '他 粗利率 契約時 既存システム物理名: AEA192';
COMMENT ON COLUMN CONTRACT_FILE.OTHER_GROSS_MARGIN_RATE_CURRENT IS '他 粗利率 現在 既存システム物理名: AEA193';
COMMENT ON COLUMN CONTRACT_FILE.ANCILLARY_COMMISSION_RATE_CONTRACT IS '附帯歩合給率契約時 既存システム物理名: AEA187';
COMMENT ON COLUMN CONTRACT_FILE.ANCILLARY_COMMISSION_RATE_CURRENT IS '附帯歩合給率現在 既存システム物理名: AEA188';
COMMENT ON COLUMN CONTRACT_FILE.LANDSCAPING_COMMISSION_RATE_CONTRACT IS '外構歩合給率契約時 既存システム物理名: AEA189';
COMMENT ON COLUMN CONTRACT_FILE.LANDSCAPING_COMMISSION_RATE_CURRENT IS '外構歩合給率現在 既存システム物理名: AEA190';
COMMENT ON COLUMN CONTRACT_FILE.OTHER_COMMISSION_RATE_CONTRACT IS '他 歩合給率契約時 既存システム物理名: AEA194';
COMMENT ON COLUMN CONTRACT_FILE.OTHER_COMMISSION_RATE_CURRENT IS '他 歩合給率現在 既存システム物理名: AEA195';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_AMOUNT_EXCLUDING_TAX IS '請負代金・税抜 既存システム物理名: AEA197';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_AMOUNT_CONSUMPTION_TAX1 IS '請負代金・消費税1 既存システム物理名: AEA198';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_AMOUNT_CONSUMPTION_TAX2 IS '請負代金・消費税2 既存システム物理名: AEA199';
COMMENT ON COLUMN CONTRACT_FILE.MAIN_CONSTRUCTION_EXCLUDING_TAX IS '本体工事・税抜 既存システム物理名: AEA200';
COMMENT ON COLUMN CONTRACT_FILE.MAIN_CONSTRUCTION_CONSUMPTION_TAX IS '本体工事・消費税 既存システム物理名: AEA201';
COMMENT ON COLUMN CONTRACT_FILE.ANCILLARY_CONSTRUCTION_EXCLUDING_TAX IS '附帯工事・税抜 既存システム物理名: AEA202';
COMMENT ON COLUMN CONTRACT_FILE.ANCILLARY_CONSTRUCTION_CONSUMPTION_TAX IS '附帯工事・消費税 既存システム物理名: AEA203';
COMMENT ON COLUMN CONTRACT_FILE.LANDSCAPING_CONSTRUCTION_EXCLUDING_TAX IS '外構工事・税抜 既存システム物理名: AEA204';
COMMENT ON COLUMN CONTRACT_FILE.LANDSCAPING_CONSTRUCTION_CONSUMPTION_TAX IS '外構工事・消費税 既存システム物理名: AEA205';
COMMENT ON COLUMN CONTRACT_FILE.OTHER_CONSTRUCTION_EXCLUDING_TAX IS 'その他工事・税抜 既存システム物理名: AEA206';
COMMENT ON COLUMN CONTRACT_FILE.OTHER_CONSTRUCTION_CONSUMPTION_TAX IS 'その他工事・消費税 既存システム物理名: AEA207';
COMMENT ON COLUMN CONTRACT_FILE.SEPARATE_CONSTRUCTION_EXCLUDING_TAX IS '別途工事・税抜 既存システム物理名: AEA208';
COMMENT ON COLUMN CONTRACT_FILE.SEPARATE_CONSTRUCTION_CONSUMPTION_TAX IS '別途工事・消費税 既存システム物理名: AEA209';
COMMENT ON COLUMN CONTRACT_FILE.PREPARATION_FUND_A_APPLICATION_AMOUNT IS '準備金Ａ申請額 既存システム物理名: AEA210';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_USAGE_AMOUNT1 IS '値引使用額1 既存システム物理名: AEA211';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_USAGE_AMOUNT2 IS '値引使用額2 既存システム物理名: AEA212';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_USAGE_AMOUNT3 IS '値引使用額3 既存システム物理名: AEA213';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_USAGE_AMOUNT4 IS '値引使用額4 既存システム物理名: AEA214';
COMMENT ON COLUMN CONTRACT_FILE.DISCOUNT_USAGE_AMOUNT5 IS '値引使用額5 既存システム物理名: AEA215';
COMMENT ON COLUMN CONTRACT_FILE.PREPARATION_FUND_A_USAGE_AMOUNT IS '準備金Ａ使用金額 既存システム物理名: AEA216';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_USAGE_AMOUNT1 IS '耳より使用金額1 既存システム物理名: AEA217';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_USAGE_AMOUNT2 IS '耳より使用金額2 既存システム物理名: AEA218';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_USAGE_AMOUNT3 IS '耳より使用金額3 既存システム物理名: AEA219';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_USAGE_AMOUNT4 IS '耳より使用金額4 既存システム物理名: AEA220';
COMMENT ON COLUMN CONTRACT_FILE.PLAN_CHANGE_EXISTENCE_CATEGORY IS 'プラン変更有無区分 既存システム物理名: AEA221';
COMMENT ON COLUMN CONTRACT_FILE.COMMISSION_SYSTEM_CHANGE_CATEGORY IS '歩合給制度変更区分 既存システム物理名: AEA222';
COMMENT ON COLUMN CONTRACT_FILE.COMMISSION_APPLICABLE_DATE IS '歩合給適用年月日 既存システム物理名: AEA223';
COMMENT ON COLUMN CONTRACT_FILE.CONSUMPTION_TAX_AMOUNT_CHANGE_EXISTENCE IS '消費税額変更有無 既存システム物理名: AEA224';
COMMENT ON COLUMN CONTRACT_FILE.BULK_ORDER_FLAG IS '一括発注フラグ 既存システム物理名: AEA225';
COMMENT ON COLUMN CONTRACT_FILE.SPECIAL_RENTAL_CATEGORY IS '特優賃区分 既存システム物理名: AEA226';
COMMENT ON COLUMN CONTRACT_FILE.LARGE_PROPERTY_CATEGORY IS '大型物件区分 既存システム物理名: AEA227';
COMMENT ON COLUMN CONTRACT_FILE.COMMISSION_TARGET_CATEGORY IS '歩合給対象区分 既存システム物理名: AEA228';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_PARTNER IS '耳より相手先 既存システム物理名: AEA229';
COMMENT ON COLUMN CONTRACT_FILE.TENANT_CONTRACT IS 'テナント契約 既存システム物理名: AEA230';
COMMENT ON COLUMN CONTRACT_FILE.REWARD_AMOUNT IS '報奨額 既存システム物理名: AEA231';
COMMENT ON COLUMN CONTRACT_FILE.CONVERSION_COMPLEX_CATEGORY IS '転換／複合区分 既存システム物理名: AEA232';
COMMENT ON COLUMN CONTRACT_FILE.COMPENSATION_AMOUNT IS '補償額 既存システム物理名: AEA233';
COMMENT ON COLUMN CONTRACT_FILE.ASSESSMENT_SECTION_MANAGER IS '査定テ課長 既存システム物理名: AEA234';
COMMENT ON COLUMN CONTRACT_FILE.RESERVE8 IS '予備8 既存システム物理名: AEA235';
COMMENT ON COLUMN CONTRACT_FILE.RESERVE9 IS '予備9 既存システム物理名: AEA236';
COMMENT ON COLUMN CONTRACT_FILE.RESERVE10 IS '予備10 既存システム物理名: AEA237';
COMMENT ON COLUMN CONTRACT_FILE.FIRST_EXCLUSION_FLAG IS '初回除外フラグ 既存システム物理名: AEA238';
COMMENT ON COLUMN CONTRACT_FILE.EXCLUSION_AMOUNT IS '除外金額 既存システム物理名: AEA239';
COMMENT ON COLUMN CONTRACT_FILE.RESERVE11 IS '予備11 既存システム物理名: AEA240';
COMMENT ON COLUMN CONTRACT_FILE.RESERVE12 IS '予備12 既存システム物理名: AEA241';
COMMENT ON COLUMN CONTRACT_FILE.INTEREST_BURDEN IS '金利負担 既存システム物理名: AEA242';
COMMENT ON COLUMN CONTRACT_FILE.SUBSIDY_TYPE IS '補助種別 既存システム物理名: AEA243';
COMMENT ON COLUMN CONTRACT_FILE.MAINTENANCE_TYPE IS '営繕種別 既存システム物理名: AEA244';
COMMENT ON COLUMN CONTRACT_FILE.SURVEY_PROJECT_AMOUNT IS '調査企画額 既存システム物理名: AEA248';
COMMENT ON COLUMN CONTRACT_FILE.BASIC_DESIGN_AMOUNT IS '基本設計額 既存システム物理名: AEA249';
COMMENT ON COLUMN CONTRACT_FILE.DETAILED_DESIGN_AMOUNT IS '実施設計額 既存システム物理名: AEA250';
COMMENT ON COLUMN CONTRACT_FILE.MANAGEMENT_FORM IS '管理形態 既存システム物理名: AEA251';
COMMENT ON COLUMN CONTRACT_FILE.PARTNER_COMPANY_CD IS '提携業者コード 既存システム物理名: AEA253';
COMMENT ON COLUMN CONTRACT_FILE.ASSESSMENT_BRANCH_CD IS '査定支店コード 既存システム物理名: AEA254';
COMMENT ON COLUMN CONTRACT_FILE.MORTGAGE_SETTING IS '抵当権設定 既存システム物理名: AEA255';
COMMENT ON COLUMN CONTRACT_FILE.MORTGAGE_AMOUNT IS '抵当額 既存システム物理名: AEA256';
COMMENT ON COLUMN CONTRACT_FILE.MANAGEMENT_FEE IS '営支手数料 既存システム物理名: AEA257';
COMMENT ON COLUMN CONTRACT_FILE.EXCLUSION_CONVERSION_AMOUNT IS '除外換算額 既存システム物理名: AEA258';
COMMENT ON COLUMN CONTRACT_FILE.TOTAL_PRICE IS '定価総額 既存システム物理名: AEA259';
COMMENT ON COLUMN CONTRACT_FILE.MAIN_PRICE IS '本体定価額 既存システム物理名: AEA260';
COMMENT ON COLUMN CONTRACT_FILE.ANCILLARY_PRICE IS '附帯定価額 既存システム物理名: AEA261';
COMMENT ON COLUMN CONTRACT_FILE.LANDSCAPING_PRICE IS '外構定価額 既存システム物理名: AEA262';
COMMENT ON COLUMN CONTRACT_FILE.OTHER_PRICE IS '他 定価額 既存システム物理名: AEA263';
COMMENT ON COLUMN CONTRACT_FILE.SEPARATE_PRICE IS '別途定価額 既存システム物理名: AEA264';
COMMENT ON COLUMN CONTRACT_FILE.TOTAL_PRICE_GROSS_MARGIN_RATE IS '定価総額 粗利率 既存システム物理名: AEA265';
COMMENT ON COLUMN CONTRACT_FILE.MAIN_PRICE_GROSS_MARGIN_RATE IS '本体定価 粗利率 既存システム物理名: AEA266';
COMMENT ON COLUMN CONTRACT_FILE.ANCILLARY_PRICE_GROSS_MARGIN_RATE IS '附帯定価 粗利率 既存システム物理名: AEA267';
COMMENT ON COLUMN CONTRACT_FILE.LANDSCAPING_PRICE_GROSS_MARGIN_RATE IS '外構定価 粗利率 既存システム物理名: AEA268';
COMMENT ON COLUMN CONTRACT_FILE.OTHER_PRICE_GROSS_MARGIN_RATE IS '他 定価 粗利率 既存システム物理名: AEA269';
COMMENT ON COLUMN CONTRACT_FILE.SEPARATE_PRICE_GROSS_MARGIN_RATE IS '別途定価 粗利率 既存システム物理名: AEA270';
COMMENT ON COLUMN CONTRACT_FILE.TOTAL_PRICE_USAGE_AMOUNT IS '定価総額 使用額 既存システム物理名: AEA271';
COMMENT ON COLUMN CONTRACT_FILE.MAIN_PRICE_USAGE_AMOUNT IS '本体定価 使用額 既存システム物理名: AEA272';
COMMENT ON COLUMN CONTRACT_FILE.ANCILLARY_PRICE_USAGE_AMOUNT IS '附帯定価 使用額 既存システム物理名: AEA273';
COMMENT ON COLUMN CONTRACT_FILE.LANDSCAPING_PRICE_USAGE_AMOUNT IS '外構定価 使用額 既存システム物理名: AEA274';
COMMENT ON COLUMN CONTRACT_FILE.OTHER_PRICE_USAGE_AMOUNT IS '他 定価 使用額 既存システム物理名: AEA275';
COMMENT ON COLUMN CONTRACT_FILE.SEPARATE_PRICE_USAGE_AMOUNT IS '別途定価 使用額 既存システム物理名: AEA276';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_COMMISSION_RATE_CONTRACT IS '請負歩合給率契約時 既存システム物理名: AEA281';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_COMMISSION_RATE_CURRENT IS '請負歩合給率現在 既存システム物理名: AEA282';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_PREPARATION_FUND_B_USAGE IS '耳より準備金Ｂ使用 既存システム物理名: AEA283';
COMMENT ON COLUMN CONTRACT_FILE.INFORMATION_REGULATIONS IS '耳より規定 既存システム物理名: AEA284';
COMMENT ON COLUMN CONTRACT_FILE.REWARD_RATE IS '報奨金率 既存システム物理名: AEA285';
COMMENT ON COLUMN CONTRACT_FILE.REWARD_AMOUNT2 IS '報奨金額2 既存システム物理名: AEA286';
COMMENT ON COLUMN CONTRACT_FILE.BUSINESS_PROJECTION_CD1 IS '事業試算書コード1 既存システム物理名: AEA287';
COMMENT ON COLUMN CONTRACT_FILE.BUSINESS_PROJECTION_CD2 IS '事業試算書コード2 既存システム物理名: AEA288';
COMMENT ON COLUMN CONTRACT_FILE.CHANGE_APPROVAL_FLAG IS '変更承認フラグ 既存システム物理名: AEA289';
COMMENT ON COLUMN CONTRACT_FILE.PARTNER_COMPANY_INTRODUCTION IS '提携業者斡旋 既存システム物理名: AEA290';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_CD_ST IS '契約コード-ST 既存システム物理名: AEA0ST';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_ADDITIONAL_CD_ST IS '契約追加コード-ST 既存システム物理名: AEA1ST';
COMMENT ON COLUMN CONTRACT_FILE.UPDATE_EMPLOYEE_NO_ST IS '更新社員番号-ST 既存システム物理名: AEA2ST';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_RESPONSIBLE_PERSON_ST IS '契約担当者-ST 既存システム物理名: AEA3ST';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_SECTION_MANAGER_ST IS '契約課長-ST 既存システム物理名: AEA4ST';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_COMPANION_ST IS '契約同行者-ST 既存システム物理名: AEA5ST';
COMMENT ON COLUMN CONTRACT_FILE.LAND_USE_CATEGORY_ST IS '用途地域区分-ST 既存システム物理名: AEA6ST';
COMMENT ON COLUMN CONTRACT_FILE.TRANSFER_DESTINATION_PROJECT_CD_ST IS '振替先・企画コード-ST 既存システム物理名: AEA7ST';
COMMENT ON COLUMN CONTRACT_FILE.TRANSFER_DESTINATION_CONTRACT_NO_ST IS '振替先・契約枝番-ST 既存システム物理名: AEA8ST';
COMMENT ON COLUMN CONTRACT_FILE.BRANCH_MANAGER_ST IS '支店長-ST 既存システム物理名: AEA9ST';
COMMENT ON COLUMN CONTRACT_FILE.CONSTRUCTION_BRANCH_JA IS '施工支店-JA 既存システム物理名: AEABM0';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_BRANCH_JA IS '契約支店-JA 既存システム物理名: AEABM1';
COMMENT ON COLUMN CONTRACT_FILE.BRANCH_TRANSFER_BUSINESS_PLACE_JA IS '引継事業所-JA 既存システム物理名: AEABM2';
COMMENT ON COLUMN CONTRACT_FILE.CONTRACT_DEPUTY_MANAGER IS '契約次長 既存システム物理名: AEA291';
COMMENT ON COLUMN CONTRACT_FILE.AFFILIATED_MEMBER1 IS '所属課員1 既存システム物理名: AEA292';
COMMENT ON COLUMN CONTRACT_FILE.AFFILIATED_MEMBER2 IS '所属課員2 既存システム物理名: AEA293';
COMMENT ON COLUMN CONTRACT_FILE.AFFILIATED_MEMBER3 IS '所属課員3 既存システム物理名: AEA294';
COMMENT ON COLUMN CONTRACT_FILE.AFFILIATED_MEMBER4 IS '所属課員4 既存システム物理名: AEA295';
COMMENT ON COLUMN CONTRACT_FILE.AFFILIATED_MEMBER5 IS '所属課員5 既存システム物理名: AEA296';
COMMENT ON COLUMN CONTRACT_FILE.AFFILIATED_MEMBER6 IS '所属課員6 既存システム物理名: AEA297';
COMMENT ON COLUMN CONTRACT_FILE.AFFILIATED_MEMBER7 IS '所属課員7 既存システム物理名: AEA298';
COMMENT ON COLUMN CONTRACT_FILE.PERFORMANCE_RECORDING_BRANCH IS '実績計上支店 既存システム物理名: AEA299';
COMMENT ON COLUMN CONTRACT_FILE.PERFORMANCE_RECORDING_SECTION IS '実績計上所属課 既存システム物理名: AEA300';
COMMENT ON COLUMN CONTRACT_FILE.RENT_PREPAYMENT_APPLICABLE_CATEGORY IS '賃料前払適用区分 既存システム物理名: AEA301';
