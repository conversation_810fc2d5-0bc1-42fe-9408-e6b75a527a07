/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.ParkingLayoutPinTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingLayoutPinPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 駐車場配置図区画ピン 既存システム物理名: -
 */
@Suppress("UNCHECKED_CAST")
open class ParkingLayoutPinRecord private constructor() : UpdatableRecordImpl<ParkingLayoutPinRecord>(ParkingLayoutPinTable.PARKING_LAYOUT_PIN) {

    open var buildingCode: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var parkingLotCode: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var xCoordinate: BigDecimal?
        set(value): Unit = set(2, value)
        get(): BigDecimal? = get(2) as BigDecimal?

    open var yCoordinate: BigDecimal?
        set(value): Unit = set(3, value)
        get(): BigDecimal? = get(3) as BigDecimal?

    open var creationDate: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var creationTime: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var creator: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var updateDate: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var updateTime: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var updater: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var deleteFlag: String
        set(value): Unit = set(10, value)
        get(): String = get(10) as String

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised ParkingLayoutPinRecord
     */
    constructor(buildingCode: String, parkingLotCode: String, xCoordinate: BigDecimal? = null, yCoordinate: BigDecimal? = null, creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, deleteFlag: String): this() {
        this.buildingCode = buildingCode
        this.parkingLotCode = parkingLotCode
        this.xCoordinate = xCoordinate
        this.yCoordinate = yCoordinate
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.deleteFlag = deleteFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ParkingLayoutPinRecord
     */
    constructor(value: ParkingLayoutPinPojo?): this() {
        if (value != null) {
            this.buildingCode = value.buildingCode
            this.parkingLotCode = value.parkingLotCode
            this.xCoordinate = value.xCoordinate
            this.yCoordinate = value.yCoordinate
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            resetChangedOnNotNull()
        }
    }
}
