package jp.ne.simplex.application.service

import io.kotest.core.spec.style.FunSpec
import jp.ne.simplex.application.model.*
import jp.ne.simplex.mock.*
import jp.ne.simplex.shared.TestCase
import jp.ne.simplex.stub.stubApiKeyAuthInfo
import jp.ne.simplex.stub.stubJwtAuthInfo
import jp.ne.simplex.stub.stubParkingReservationInfo
import jp.ne.simplex.stub.stubRegisterParkingReservation
import org.junit.jupiter.api.Assertions.assertEquals

class ParkingReservationMailServiceTest : FunSpec({

    context("予約登録時のメール送信処理") {
        var mailSendCount = 0

        val service = ParkingReservationMailService(
            receptAddress = "<EMAIL>",
            inputName = "テストユーザー",
            isSendMail = true,
            emailAddressRepository = MockEmailAddressRepository(
                getAddressListByBranchCodeFunc = { _ ->
                    listOf(
                        EmailAddress.of("<EMAIL>"),
                        EmailAddress.of("<EMAIL>"),
                        EmailAddress.of("<EMAIL>"),
                    )
                }
            ),
            officeBranchMappingRepository = MockOfficeBranchMappingRepository(),
            buildingRepository = MockBuildingMasterRepository(),
            branchRepository = MockBranchRepository(),
            mailRepository = MockMailRepository(
                sendMailUPFunc = { _ -> mailSendCount++ },
            ),
            parkingRepository = MockParkingRepository(),
        )

        beforeTest {
            mailSendCount = 0
        }

        listOf(
            TestCase(ParkingReservation.Status.RESERVATION, true), // 受付はメール送信する
            TestCase(ParkingReservation.Status.FINISHED, true), // 完了はメール送信する
            TestCase(ParkingReservation.Status.CANCEL, true), // キャンセルはメール送信する
            TestCase(ParkingReservation.Status.TENTATIVE, false) // 仮申込はメール送信しない
        ).forEach { testCase ->
            test("予約ステータス: ${testCase.input.name} の場合、メール送信処理が正しく動作すること") {
                // execute
                service.notifyRegister(
                    stubJwtAuthInfo(), stubRegisterParkingReservation(
                        parkingReservationStatus = testCase.input
                    )
                )

                // verify
                assertEquals(testCase.expected, mailSendCount == 1)
            }
        }
    }

    context("予約更新時のメール送信処理") {
        var mailSendCount = 0

        val service = ParkingReservationMailService(
            receptAddress = "<EMAIL>",
            inputName = "テストユーザー",
            isSendMail = true,
            emailAddressRepository = MockEmailAddressRepository(
                getAddressListByBranchCodeFunc = { _ ->
                    listOf(
                        EmailAddress.of("<EMAIL>"),
                        EmailAddress.of("<EMAIL>"),
                        EmailAddress.of("<EMAIL>"),
                    )
                }
            ),
            officeBranchMappingRepository = MockOfficeBranchMappingRepository(),
            buildingRepository = MockBuildingMasterRepository(),
            branchRepository = MockBranchRepository(),
            mailRepository = MockMailRepository(
                sendMailUPFunc = { _ -> mailSendCount++ },
            ),
            parkingRepository = MockParkingRepository(),
        )

        beforeTest {
            mailSendCount = 0
        }

        test("予約ステータスが更新されていない場合、メールが送信されないこと") {
            // execute
            service.notifyUpdate(
                stubJwtAuthInfo(),
                stubParkingReservationInfo(
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION
                ),
                ParkingReservation.Status.RESERVATION,
                null,
            )

            // verify
            assertEquals(0, mailSendCount)
        }


        test("予約ステータス：仮申込みの場合、メールが送信されないこと") {
            // execute
            service.notifyUpdate(
                stubJwtAuthInfo(),
                stubParkingReservationInfo(
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION
                ),
                ParkingReservation.Status.TENTATIVE,
                null,
            )

            // verify
            assertEquals(0, mailSendCount)
        }

        listOf(
            TestCase(
                ParkingReservation.Status.RESERVATION,
                MailProperty.MailTemplateType.PARKING_YOYAKU_UKETUKE_INFO
            ), // 受付はメール送信する
            TestCase(
                ParkingReservation.Status.FINISHED,
                MailProperty.MailTemplateType.PARKING_YOYAKU_KEIYAKU_INFO
            ), // 完了はメール送信する
            TestCase(
                ParkingReservation.Status.CANCEL,
                MailProperty.MailTemplateType.PARKING_YOYAKU_CANCEL_INFO
            ), // キャンセルはメール送信する
        ).forEach { testCase ->
            test("予約ステータス: ${testCase.input.name} の場合、${testCase.expected.name}のメールテンプレートでメールが送信されること") {
                // setup
                val service = ParkingReservationMailService(
                    receptAddress = "<EMAIL>",
                    inputName = "テストユーザー",
                    isSendMail = true,
                    emailAddressRepository = MockEmailAddressRepository(
                        getAddressListByBranchCodeFunc = { _ ->
                            listOf(
                                EmailAddress.of("<EMAIL>"),
                                EmailAddress.of("<EMAIL>"),
                                EmailAddress.of("<EMAIL>"),
                            )
                        }
                    ),
                    officeBranchMappingRepository = MockOfficeBranchMappingRepository(),
                    buildingRepository = MockBuildingMasterRepository(),
                    branchRepository = MockBranchRepository(),
                    mailRepository = MockMailRepository(
                        sendMailUPFunc = { property ->
                            assertEquals(testCase.expected, property.mailTemplateType)
                            mailSendCount++
                        },
                    ),
                    parkingRepository = MockParkingRepository(),
                )

                // execute
                service.notifyUpdate(
                    stubJwtAuthInfo(),
                    stubParkingReservationInfo(
                        parkingReservationStatus = when (testCase.input) {
                            ParkingReservation.Status.RESERVATION -> ParkingReservation.Status.FINISHED
                            ParkingReservation.Status.FINISHED -> ParkingReservation.Status.CANCEL
                            ParkingReservation.Status.CANCEL -> ParkingReservation.Status.RESERVATION
                            ParkingReservation.Status.TENTATIVE -> ParkingReservation.Status.TENTATIVE
                        }
                    ),
                    testCase.input,
                    null,
                )

                // verify
                assertEquals(1, mailSendCount)
            }
        }

    }
    context("ログインしているかの区別の送信処理") {

        var mailSendCount = 0
        var lastMailToList: List<EmailAddress>? = null
        val addressLists = listOf(
            EmailAddress.of("<EMAIL>"),
            EmailAddress.of("<EMAIL>"),
            EmailAddress.of("<EMAIL>"),
        )

        val tenantAddresses = listOf(
            EmailAddress.of("<EMAIL>"),
            EmailAddress.of("<EMAIL>"),
            EmailAddress.of("<EMAIL>"),
        )

        val daitateAddresses = listOf(
            EmailAddress.of("<EMAIL>"),
            EmailAddress.of("<EMAIL>"),
            EmailAddress.of("<EMAIL>"),
        )
        val mockBuildingMasterRepository = MockBuildingMasterRepository(
            findActiveByFunc = { buildingCode ->
                Building(
                    code = buildingCode,
                    name = Building.Name.of("テストビル"),
                    businessOfficeCode = Office.Code.of("officeCode1"),
                    leasingStoreCode = Branch.Code.of("leasingBranch"),
                    reviewBranchCode = Branch.Code.of("reviewBranch"),
                )
            },
            findByFunc = { buildingCode -> null },
            isExistFunc = { _ -> true }
        )

        val service = ParkingReservationMailService(
            receptAddress = "<EMAIL>",
            inputName = "テストユーザー",
            isSendMail = true,
            emailAddressRepository = MockEmailAddressRepository(
                getAddressListByBranchCodeFunc = { _ ->
                    addressLists
                },
                getTenantAddressListFunc = { _ ->
                    tenantAddresses
                },
                getDaitateAddressListFunc = { _ ->
                    daitateAddresses
                },

                ),
            officeBranchMappingRepository = MockOfficeBranchMappingRepository(
                getByOfficeCodeFunc = { _ -> Branch.Code.of("validBranchCode") }
            ),
            buildingRepository = mockBuildingMasterRepository,
            branchRepository = MockBranchRepository(),
            mailRepository = MockMailRepository(
                sendMailUPFunc = { property ->
                    mailSendCount++
                    lastMailToList = property.toList
                    println("送信先リスト: ${property.toList.map { it }}")
                },
            ),
            parkingRepository = MockParkingRepository(),
        )

        beforeTest {
            mailSendCount = 0
        }
        test("AuthInfoがJwtでない場合、送信先に営業の人のアドレスが含まれていないこと") {
            service.notifyRegister(
                stubApiKeyAuthInfo(),
                stubRegisterParkingReservation(parkingReservationStatus = ParkingReservation.Status.RESERVATION)
            )
            assertEquals(1, mailSendCount)
            val sentAddresses = lastMailToList ?: emptyList()

            assertEquals(3, sentAddresses.size) { "送信先のアドレス数が正しいこと" }
            addressLists.forEach {
                assert(sentAddresses.contains(it)) { "送信先にaddressListのアドレスが含まれていること" }
            }

            tenantAddresses.forEach {
                assert(!sentAddresses.contains(it)) { "送信先にtenantAddressが含まれていないこと" }
            }
            daitateAddresses.forEach {
                assert(!sentAddresses.contains(it)) { "送信先にdaitateAddressが含まれていないこと" }
            }

        }
        test("AuthInfoがJwtの場合、送信先に営業の人のアドレスが含まれていること") {
            service.notifyRegister(
                stubJwtAuthInfo(),
                stubRegisterParkingReservation(parkingReservationStatus = ParkingReservation.Status.RESERVATION)
            )
            assertEquals(1, mailSendCount)

            assertEquals(1, mailSendCount)
            val sentAddresses = lastMailToList ?: emptyList()

            assertEquals(9, sentAddresses.size) { "送信先のアドレス数が正しいこと" }
            addressLists.forEach {
                assert(sentAddresses.contains(it)) { "送信先にaddressListのアドレスが含まれていること" }
            }

            tenantAddresses.forEach {
                assert(sentAddresses.contains(it)) { "送信先にtenantAddressが含まれていること" }
            }
            daitateAddresses.forEach {
                assert(sentAddresses.contains(it)) { "送信先にdaitateAddressが含まれていること" }
            }
        }

    }
    context("送信先が50件を超える場合は分割送信すること") {

        var mailSendCount = 0
        val totalAddresses = mutableListOf<EmailAddress>()
        val batchSize = 50
        val bigAddressList = (1..123).map { EmailAddress.of("user$<EMAIL>") }

        val mockBuildingMasterRepository = MockBuildingMasterRepository(
            findActiveByFunc = { buildingCode ->
                Building(
                    code = buildingCode,
                    name = Building.Name.of("テストビル"),
                    businessOfficeCode = Office.Code.of("officeCode1"),
                    leasingStoreCode = Branch.Code.of("leasingBranch"),
                    reviewBranchCode = Branch.Code.of("reviewBranch"),
                )
            },
            findByFunc = { buildingCode -> null },
            isExistFunc = { _ -> true }
        )

        val service = ParkingReservationMailService(
            receptAddress = "<EMAIL>",
            inputName = "テストユーザー",
            isSendMail = true,
            emailAddressRepository = MockEmailAddressRepository(
                getAddressListByBranchCodeFunc = { _ ->
                    bigAddressList
                },
                getTenantAddressListFunc = { _ -> emptyList() },
                getDaitateAddressListFunc = { _ -> emptyList() }
            ),
            officeBranchMappingRepository = MockOfficeBranchMappingRepository(
                getByOfficeCodeFunc = { _ -> Branch.Code.of("validBranchCode") }
            ),
            buildingRepository = mockBuildingMasterRepository,
            branchRepository = MockBranchRepository(),
            mailRepository = MockMailRepository(
                sendMailUPFunc = { property ->
                    mailSendCount++
                    totalAddresses.addAll(property.toList)
                    println("送信バッチ ${mailSendCount} 件数: ${property.toList.size}")
                }
            ),
            parkingRepository = MockParkingRepository(),
        )

        beforeTest {
            mailSendCount = 0
            totalAddresses.clear()
        }

        test("送信先が50件を超えたら分割して送信されること") {
            service.notifyRegister(
                stubJwtAuthInfo(),
                stubRegisterParkingReservation(parkingReservationStatus = ParkingReservation.Status.RESERVATION)
            )

            val expectedSendCount = (bigAddressList.size + batchSize - 1) / batchSize
            assertEquals(expectedSendCount, mailSendCount, "送信回数が正しいこと")

            assertEquals(bigAddressList.size, totalAddresses.distinct().size, "送信先の総数が一致すること")

            totalAddresses.forEach {
                assert(bigAddressList.contains(it)) { "送信先に元のアドレス以外が含まれていないこと" }
            }
        }
    }

})
