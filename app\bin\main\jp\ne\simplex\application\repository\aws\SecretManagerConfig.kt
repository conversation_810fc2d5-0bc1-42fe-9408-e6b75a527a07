package jp.ne.simplex.application.repository.aws

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import software.amazon.awssdk.http.apache.ApacheHttpClient
import software.amazon.awssdk.http.apache.ProxyConfiguration
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient
import java.net.URI

@Component
class SecretManagerConfig(
    @Value("\${aws.endpoint}")
    private val endpoint: String? = null
) {

    @Bean
    @Profile("!dev")
    fun prodClient(): SecretsManagerClient {
        return SecretsManagerClient.builder() //
            .region(Region.AP_NORTHEAST_1) //
            .build()
    }

    @Bean
    @Profile("dev")
    fun devClient(): SecretsManagerClient {
        if (endpoint == null) {
            throw RuntimeException("AWS endpoint is not configured for dev profile.")
        }

        val proxyConfiguration = ProxyConfiguration.builder()
            .nonProxyHosts(setOf("localhost"))
            .build()

        val httpClient = ApacheHttpClient.builder()
            .proxyConfiguration(proxyConfiguration)
            .build()

        return SecretsManagerClient.builder() //
            .region(Region.AP_NORTHEAST_1) //
            .httpClient(httpClient) //
            .endpointOverride(URI.create(endpoint)) //
            .build()
    }
}
