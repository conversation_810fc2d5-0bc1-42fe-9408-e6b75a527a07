/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.AccidentPropertyManagementVRTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.AccidentPropertyManagementVRPojo

import org.jooq.impl.TableRecordImpl


/**
 * This class is generated by jOOQ.
 */
@Suppress("UNCHECKED_CAST")
open class AccidentPropertyManagementVRRecord private constructor() : TableRecordImpl<AccidentPropertyManagementVRRecord>(AccidentPropertyManagementVRTable.ACCIDENT_PROPERTY_MANAGEMENT_V_R) {

    open var noticeFlag: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var targetCategory: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    open var buildingCode: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var roomCode: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    /**
     * Create a detached, initialised AccidentPropertyManagementVRRecord
     */
    constructor(noticeFlag: Int? = null, targetCategory: String? = null, buildingCode: String? = null, roomCode: String? = null): this() {
        this.noticeFlag = noticeFlag
        this.targetCategory = targetCategory
        this.buildingCode = buildingCode
        this.roomCode = roomCode
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised AccidentPropertyManagementVRRecord
     */
    constructor(value: AccidentPropertyManagementVRPojo?): this() {
        if (value != null) {
            this.noticeFlag = value.noticeFlag
            this.targetCategory = value.targetCategory
            this.buildingCode = value.buildingCode
            this.roomCode = value.roomCode
            resetChangedOnNotNull()
        }
    }
}
