-- TABLE: PRODUCT_NAME_MASTER(商品名称マスタ)

CREATE TABLE PRODUCT_NAME_MASTER(
     UNIQUE_NUMBER                                numeric(19,0)     NOT NULL    
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM                               varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    DELETE_FLAG                                  varchar(1)                    
,    PRODUCT_NAME_CODE                            numeric(3,0)      NOT NULL    
,    EFFECTIVE_START_DATE                         numeric(8,0)      NOT NULL    
,    EFFECTIVE_END_DATE                           numeric(8,0)                  
,    PRODUCT_NAME                                 varchar(42)                   
,    PRODUCT_NAME_ABBREVIATION                    varchar(22)                   
,    CONSTRAINT PK_PRODUCT_NAME_MASTER PRIMARY KEY (UNIQUE_NUMBER, PRODUCT_NAME_CODE, EFFECTIVE_START_DATE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PRODUCT_NAME_MASTER IS '商品名称マスタ 既存システム物理名: BGJMAP';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.UNIQUE_NUMBER IS 'UNIQUE_NUMBER 既存システム物理名: UNIQUE_NUMBER';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: BGJ01D 削除フラグ ":有効 "C":工事中止 "D":論理 "';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: BGJ02H';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: BGJ03D';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: BGJ04H';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.UPDATE_PROGRAM IS '更新プログラム 既存システム物理名: BGJ05P';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.UPDATER IS '更新者 既存システム物理名: BGJ06P';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.DELETE_FLAG IS '削除フラグ 既存システム物理名: BGJ08S';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.PRODUCT_NAME_CODE IS '商品名称コード 既存システム物理名: BGJS21';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.EFFECTIVE_START_DATE IS '適用開始日 既存システム物理名: BGJT01';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.EFFECTIVE_END_DATE IS '適用終了日 既存システム物理名: BGJT02';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.PRODUCT_NAME IS '商品名称 既存システム物理名: BGJS22';
COMMENT ON COLUMN PRODUCT_NAME_MASTER.PRODUCT_NAME_ABBREVIATION IS '商品名称(略称) 既存システム物理名: BGJS23';
