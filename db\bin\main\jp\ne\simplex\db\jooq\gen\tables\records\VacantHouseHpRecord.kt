/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.VacantHouseHpTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.VacantHouseHpPojo

import org.jooq.impl.TableRecordImpl


/**
 * 空き家HP用 既存システム物理名: EMEH2P
 */
@Suppress("UNCHECKED_CAST")
open class VacantHouseHpRecord private constructor() : TableRecordImpl<VacantHouseHpRecord>(VacantHouseHpTable.VACANT_HOUSE_HP) {

    open var propertyCdDivision: String?
        set(value): Unit = set(0, value)
        get(): String? = get(0) as String?

    open var propertyCdSeparator_1: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    open var propertyBuildingCd: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var propertyCdSeparator_2: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var propertyRoomCd: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var deleteFlag: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var customerCompanyCd: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var customerBranchCd: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var customerDepartmentCd: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var customerCompletionFlag: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var municipalityCd: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var lineCd: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var stationCd: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var rent: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var layoutRooms: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var exclusiveArea: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var propertyType: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var feature_1: Byte?
        set(value): Unit = set(17, value)
        get(): Byte? = get(17) as Byte?

    open var feature_2: Byte?
        set(value): Unit = set(18, value)
        get(): Byte? = get(18) as Byte?

    open var feature_3: Byte?
        set(value): Unit = set(19, value)
        get(): Byte? = get(19) as Byte?

    open var feature_4: Byte?
        set(value): Unit = set(20, value)
        get(): Byte? = get(20) as Byte?

    open var feature_5: Byte?
        set(value): Unit = set(21, value)
        get(): Byte? = get(21) as Byte?

    open var feature_6: Byte?
        set(value): Unit = set(22, value)
        get(): Byte? = get(22) as Byte?

    open var feature_7: Byte?
        set(value): Unit = set(23, value)
        get(): Byte? = get(23) as Byte?

    open var feature_8: Byte?
        set(value): Unit = set(24, value)
        get(): Byte? = get(24) as Byte?

    open var feature_9: Byte?
        set(value): Unit = set(25, value)
        get(): Byte? = get(25) as Byte?

    open var feature_10: Byte?
        set(value): Unit = set(26, value)
        get(): Byte? = get(26) as Byte?

    open var feature_11: Byte?
        set(value): Unit = set(27, value)
        get(): Byte? = get(27) as Byte?

    open var feature_12: Byte?
        set(value): Unit = set(28, value)
        get(): Byte? = get(28) as Byte?

    open var feature_13: Byte?
        set(value): Unit = set(29, value)
        get(): Byte? = get(29) as Byte?

    open var feature_14: Byte?
        set(value): Unit = set(30, value)
        get(): Byte? = get(30) as Byte?

    open var feature_15: Byte?
        set(value): Unit = set(31, value)
        get(): Byte? = get(31) as Byte?

    open var feature_16: Byte?
        set(value): Unit = set(32, value)
        get(): Byte? = get(32) as Byte?

    open var feature_17: Byte?
        set(value): Unit = set(33, value)
        get(): Byte? = get(33) as Byte?

    open var feature_18: Byte?
        set(value): Unit = set(34, value)
        get(): Byte? = get(34) as Byte?

    open var feature_19: Byte?
        set(value): Unit = set(35, value)
        get(): Byte? = get(35) as Byte?

    open var feature_20: Byte?
        set(value): Unit = set(36, value)
        get(): Byte? = get(36) as Byte?

    open var feature_21: Byte?
        set(value): Unit = set(37, value)
        get(): Byte? = get(37) as Byte?

    open var feature_22: Byte?
        set(value): Unit = set(38, value)
        get(): Byte? = get(38) as Byte?

    open var feature_23: Byte?
        set(value): Unit = set(39, value)
        get(): Byte? = get(39) as Byte?

    open var feature_24: Byte?
        set(value): Unit = set(40, value)
        get(): Byte? = get(40) as Byte?

    open var feature_25: Byte?
        set(value): Unit = set(41, value)
        get(): Byte? = get(41) as Byte?

    open var feature_26: Byte?
        set(value): Unit = set(42, value)
        get(): Byte? = get(42) as Byte?

    open var feature_27: Byte?
        set(value): Unit = set(43, value)
        get(): Byte? = get(43) as Byte?

    open var feature_28: Byte?
        set(value): Unit = set(44, value)
        get(): Byte? = get(44) as Byte?

    open var feature_29: Byte?
        set(value): Unit = set(45, value)
        get(): Byte? = get(45) as Byte?

    open var feature_30: Byte?
        set(value): Unit = set(46, value)
        get(): Byte? = get(46) as Byte?

    open var feature_31: Byte?
        set(value): Unit = set(47, value)
        get(): Byte? = get(47) as Byte?

    open var feature_32: Byte?
        set(value): Unit = set(48, value)
        get(): Byte? = get(48) as Byte?

    open var feature_33: Byte?
        set(value): Unit = set(49, value)
        get(): Byte? = get(49) as Byte?

    open var feature_34: Byte?
        set(value): Unit = set(50, value)
        get(): Byte? = get(50) as Byte?

    open var feature_35: Byte?
        set(value): Unit = set(51, value)
        get(): Byte? = get(51) as Byte?

    open var feature_36: Byte?
        set(value): Unit = set(52, value)
        get(): Byte? = get(52) as Byte?

    open var feature_37: Byte?
        set(value): Unit = set(53, value)
        get(): Byte? = get(53) as Byte?

    open var feature_38: Byte?
        set(value): Unit = set(54, value)
        get(): Byte? = get(54) as Byte?

    open var feature_39: Byte?
        set(value): Unit = set(55, value)
        get(): Byte? = get(55) as Byte?

    open var feature_40: Byte?
        set(value): Unit = set(56, value)
        get(): Byte? = get(56) as Byte?

    open var feature_41: Byte?
        set(value): Unit = set(57, value)
        get(): Byte? = get(57) as Byte?

    open var feature_42: Byte?
        set(value): Unit = set(58, value)
        get(): Byte? = get(58) as Byte?

    open var feature_43: Byte?
        set(value): Unit = set(59, value)
        get(): Byte? = get(59) as Byte?

    open var feature_44: Byte?
        set(value): Unit = set(60, value)
        get(): Byte? = get(60) as Byte?

    open var feature_45: Byte?
        set(value): Unit = set(61, value)
        get(): Byte? = get(61) as Byte?

    open var feature_46: Byte?
        set(value): Unit = set(62, value)
        get(): Byte? = get(62) as Byte?

    open var feature_47: Byte?
        set(value): Unit = set(63, value)
        get(): Byte? = get(63) as Byte?

    open var feature_48: Byte?
        set(value): Unit = set(64, value)
        get(): Byte? = get(64) as Byte?

    open var feature_49: Byte?
        set(value): Unit = set(65, value)
        get(): Byte? = get(65) as Byte?

    open var feature_50: Byte?
        set(value): Unit = set(66, value)
        get(): Byte? = get(66) as Byte?

    open var feature_51: Byte?
        set(value): Unit = set(67, value)
        get(): Byte? = get(67) as Byte?

    open var feature_52: Byte?
        set(value): Unit = set(68, value)
        get(): Byte? = get(68) as Byte?

    open var feature_53: Byte?
        set(value): Unit = set(69, value)
        get(): Byte? = get(69) as Byte?

    open var feature_54: Byte?
        set(value): Unit = set(70, value)
        get(): Byte? = get(70) as Byte?

    open var feature_55: Byte?
        set(value): Unit = set(71, value)
        get(): Byte? = get(71) as Byte?

    open var feature_56: Byte?
        set(value): Unit = set(72, value)
        get(): Byte? = get(72) as Byte?

    open var feature_57: Byte?
        set(value): Unit = set(73, value)
        get(): Byte? = get(73) as Byte?

    open var feature_58: Byte?
        set(value): Unit = set(74, value)
        get(): Byte? = get(74) as Byte?

    open var feature_59: Byte?
        set(value): Unit = set(75, value)
        get(): Byte? = get(75) as Byte?

    open var feature_60: Byte?
        set(value): Unit = set(76, value)
        get(): Byte? = get(76) as Byte?

    open var feature_99: Byte?
        set(value): Unit = set(77, value)
        get(): Byte? = get(77) as Byte?

    open var featureNewBuilding: Byte?
        set(value): Unit = set(78, value)
        get(): Byte? = get(78) as Byte?

    open var featureCornerRoom: Byte?
        set(value): Unit = set(79, value)
        get(): Byte? = get(79) as Byte?

    open var featureAboveSecondFloor: Byte?
        set(value): Unit = set(80, value)
        get(): Byte? = get(80) as Byte?

    open var lineName: String?
        set(value): Unit = set(81, value)
        get(): String? = get(81) as String?

    open var stationName: String?
        set(value): Unit = set(82, value)
        get(): String? = get(82) as String?

    open var busStopName: String?
        set(value): Unit = set(83, value)
        get(): String? = get(83) as String?

    open var busTime: Short?
        set(value): Unit = set(84, value)
        get(): Short? = get(84) as Short?

    open var walkTime: Short?
        set(value): Unit = set(85, value)
        get(): Short? = get(85) as Short?

    open var distance: Short?
        set(value): Unit = set(86, value)
        get(): Short? = get(86) as Short?

    open var keyMoney: String?
        set(value): Unit = set(87, value)
        get(): String? = get(87) as String?

    open var deposit: String?
        set(value): Unit = set(88, value)
        get(): String? = get(88) as String?

    open var neighborhoodAssociationFee: String?
        set(value): Unit = set(89, value)
        get(): String? = get(89) as String?

    open var maintenanceFee: String?
        set(value): Unit = set(90, value)
        get(): String? = get(90) as String?

    open var roomTypeName: String?
        set(value): Unit = set(91, value)
        get(): String? = get(91) as String?

    open var layout: String?
        set(value): Unit = set(92, value)
        get(): String? = get(92) as String?

    open var layoutDetails: String?
        set(value): Unit = set(93, value)
        get(): String? = get(93) as String?

    open var parkingDivision: String?
        set(value): Unit = set(94, value)
        get(): String? = get(94) as String?

    open var parkingFee: String?
        set(value): Unit = set(95, value)
        get(): String? = get(95) as String?

    open var buildYear: String?
        set(value): Unit = set(96, value)
        get(): String? = get(96) as String?

    open var handlingStoreCompany: String?
        set(value): Unit = set(97, value)
        get(): String? = get(97) as String?

    open var locationPublishAreaName: String?
        set(value): Unit = set(98, value)
        get(): String? = get(98) as String?

    open var floorCount: String?
        set(value): Unit = set(99, value)
        get(): String? = get(99) as String?

    open var direction: String?
        set(value): Unit = set(100, value)
        get(): String? = get(100) as String?

    open var roomPosition: String?
        set(value): Unit = set(101, value)
        get(): String? = get(101) as String?

    open var availableDate: String?
        set(value): Unit = set(102, value)
        get(): String? = get(102) as String?

    open var transportation: String?
        set(value): Unit = set(103, value)
        get(): String? = get(103) as String?

    open var equipment: String?
        set(value): Unit = set(104, value)
        get(): String? = get(104) as String?

    open var remarks: String?
        set(value): Unit = set(105, value)
        get(): String? = get(105) as String?

    open var contactBranchName: String?
        set(value): Unit = set(106, value)
        get(): String? = get(106) as String?

    open var branchPhoneNumber: String?
        set(value): Unit = set(107, value)
        get(): String? = get(107) as String?

    open var branchFaxNumber: String?
        set(value): Unit = set(108, value)
        get(): String? = get(108) as String?

    open var transactionType: String?
        set(value): Unit = set(109, value)
        get(): String? = get(109) as String?

    open var buildingName: String?
        set(value): Unit = set(110, value)
        get(): String? = get(110) as String?

    open var structureName: String?
        set(value): Unit = set(111, value)
        get(): String? = get(111) as String?

    open var agentAvailableDivision: String?
        set(value): Unit = set(112, value)
        get(): String? = get(112) as String?

    open var subleaseDivision: String?
        set(value): Unit = set(113, value)
        get(): String? = get(113) as String?

    open var creationDate: Int?
        set(value): Unit = set(114, value)
        get(): Int? = get(114) as Int?

    open var creationTime: Int?
        set(value): Unit = set(115, value)
        get(): Int? = get(115) as Int?

    open var creator: String?
        set(value): Unit = set(116, value)
        get(): String? = get(116) as String?

    open var updateDate: Int?
        set(value): Unit = set(117, value)
        get(): Int? = get(117) as Int?

    open var updateTime: Int?
        set(value): Unit = set(118, value)
        get(): Int? = get(118) as Int?

    open var updater: String?
        set(value): Unit = set(119, value)
        get(): String? = get(119) as String?

    open var branchAddress: String?
        set(value): Unit = set(120, value)
        get(): String? = get(120) as String?

    open var recommendationComment: String?
        set(value): Unit = set(121, value)
        get(): String? = get(121) as String?

    open var completionYearMonth: Int?
        set(value): Unit = set(122, value)
        get(): Int? = get(122) as Int?

    open var propertyPostalCode: String?
        set(value): Unit = set(123, value)
        get(): String? = get(123) as String?

    open var recordSeparator: String?
        set(value): Unit = set(124, value)
        get(): String? = get(124) as String?

    open var rentTax: Int?
        set(value): Unit = set(125, value)
        get(): Int? = get(125) as Int?

    open var keyMoneyTax: Int?
        set(value): Unit = set(126, value)
        get(): Int? = get(126) as Int?

    open var keyMoneyTotal: Int?
        set(value): Unit = set(127, value)
        get(): Int? = get(127) as Int?

    open var maintenanceFeeTax: Int?
        set(value): Unit = set(128, value)
        get(): Int? = get(128) as Int?

    open var parkingFeeTax: Int?
        set(value): Unit = set(129, value)
        get(): Int? = get(129) as Int?

    open var changeDivision: String?
        set(value): Unit = set(130, value)
        get(): String? = get(130) as String?

    open var updateFlag: String?
        set(value): Unit = set(131, value)
        get(): String? = get(131) as String?

    open var feature_61: Byte?
        set(value): Unit = set(132, value)
        get(): Byte? = get(132) as Byte?

    open var feature_62: Byte?
        set(value): Unit = set(133, value)
        get(): Byte? = get(133) as Byte?

    open var feature_63: Byte?
        set(value): Unit = set(134, value)
        get(): Byte? = get(134) as Byte?

    open var feature_64: Byte?
        set(value): Unit = set(135, value)
        get(): Byte? = get(135) as Byte?

    open var feature_65: Byte?
        set(value): Unit = set(136, value)
        get(): Byte? = get(136) as Byte?

    open var feature_66: Byte?
        set(value): Unit = set(137, value)
        get(): Byte? = get(137) as Byte?

    open var feature_67: Byte?
        set(value): Unit = set(138, value)
        get(): Byte? = get(138) as Byte?

    open var feature_68: Byte?
        set(value): Unit = set(139, value)
        get(): Byte? = get(139) as Byte?

    open var feature_69: Byte?
        set(value): Unit = set(140, value)
        get(): Byte? = get(140) as Byte?

    open var feature_70: Byte?
        set(value): Unit = set(141, value)
        get(): Byte? = get(141) as Byte?

    open var feature_71: Byte?
        set(value): Unit = set(142, value)
        get(): Byte? = get(142) as Byte?

    open var feature_72: Byte?
        set(value): Unit = set(143, value)
        get(): Byte? = get(143) as Byte?

    open var feature_73: Byte?
        set(value): Unit = set(144, value)
        get(): Byte? = get(144) as Byte?

    open var feature_74: Byte?
        set(value): Unit = set(145, value)
        get(): Byte? = get(145) as Byte?

    open var feature_75: Byte?
        set(value): Unit = set(146, value)
        get(): Byte? = get(146) as Byte?

    open var feature_76: Byte?
        set(value): Unit = set(147, value)
        get(): Byte? = get(147) as Byte?

    open var feature_77: Byte?
        set(value): Unit = set(148, value)
        get(): Byte? = get(148) as Byte?

    open var feature_78: Byte?
        set(value): Unit = set(149, value)
        get(): Byte? = get(149) as Byte?

    open var feature_79: Byte?
        set(value): Unit = set(150, value)
        get(): Byte? = get(150) as Byte?

    open var feature_80: Byte?
        set(value): Unit = set(151, value)
        get(): Byte? = get(151) as Byte?

    open var feature_81: Byte?
        set(value): Unit = set(152, value)
        get(): Byte? = get(152) as Byte?

    open var feature_82: Byte?
        set(value): Unit = set(153, value)
        get(): Byte? = get(153) as Byte?

    open var feature_83: Byte?
        set(value): Unit = set(154, value)
        get(): Byte? = get(154) as Byte?

    open var feature_84: Byte?
        set(value): Unit = set(155, value)
        get(): Byte? = get(155) as Byte?

    open var feature_85: Byte?
        set(value): Unit = set(156, value)
        get(): Byte? = get(156) as Byte?

    open var feature_86: Byte?
        set(value): Unit = set(157, value)
        get(): Byte? = get(157) as Byte?

    open var feature_87: Byte?
        set(value): Unit = set(158, value)
        get(): Byte? = get(158) as Byte?

    open var feature_88: Byte?
        set(value): Unit = set(159, value)
        get(): Byte? = get(159) as Byte?

    open var feature_89: Byte?
        set(value): Unit = set(160, value)
        get(): Byte? = get(160) as Byte?

    open var feature_90: Byte?
        set(value): Unit = set(161, value)
        get(): Byte? = get(161) as Byte?

    open var feature_91: Byte?
        set(value): Unit = set(162, value)
        get(): Byte? = get(162) as Byte?

    open var feature_92: Byte?
        set(value): Unit = set(163, value)
        get(): Byte? = get(163) as Byte?

    open var feature_93: Byte?
        set(value): Unit = set(164, value)
        get(): Byte? = get(164) as Byte?

    open var feature_94: Byte?
        set(value): Unit = set(165, value)
        get(): Byte? = get(165) as Byte?

    open var feature_95: Byte?
        set(value): Unit = set(166, value)
        get(): Byte? = get(166) as Byte?

    open var feature_96: Byte?
        set(value): Unit = set(167, value)
        get(): Byte? = get(167) as Byte?

    open var feature_97: Byte?
        set(value): Unit = set(168, value)
        get(): Byte? = get(168) as Byte?

    open var feature_98: Byte?
        set(value): Unit = set(169, value)
        get(): Byte? = get(169) as Byte?

    open var equipmentFlag_1: Byte?
        set(value): Unit = set(170, value)
        get(): Byte? = get(170) as Byte?

    open var equipmentFlag_2: Byte?
        set(value): Unit = set(171, value)
        get(): Byte? = get(171) as Byte?

    open var equipmentFlag_3: Byte?
        set(value): Unit = set(172, value)
        get(): Byte? = get(172) as Byte?

    open var equipmentFlag_4: Byte?
        set(value): Unit = set(173, value)
        get(): Byte? = get(173) as Byte?

    open var equipmentFlag_5: Byte?
        set(value): Unit = set(174, value)
        get(): Byte? = get(174) as Byte?

    open var equipmentFlag_6: Byte?
        set(value): Unit = set(175, value)
        get(): Byte? = get(175) as Byte?

    open var equipmentFlag_7: Byte?
        set(value): Unit = set(176, value)
        get(): Byte? = get(176) as Byte?

    open var equipmentFlag_8: Byte?
        set(value): Unit = set(177, value)
        get(): Byte? = get(177) as Byte?

    open var equipmentFlag_9: Byte?
        set(value): Unit = set(178, value)
        get(): Byte? = get(178) as Byte?

    open var equipmentFlag_10: Byte?
        set(value): Unit = set(179, value)
        get(): Byte? = get(179) as Byte?

    open var rentalDivision: Byte?
        set(value): Unit = set(180, value)
        get(): Byte? = get(180) as Byte?

    open var distance2: BigDecimal?
        set(value): Unit = set(181, value)
        get(): BigDecimal? = get(181) as BigDecimal?

    open var prefectureCode: String?
        set(value): Unit = set(182, value)
        get(): String? = get(182) as String?

    open var cityWardCode: String?
        set(value): Unit = set(183, value)
        get(): String? = get(183) as String?

    open var townVillageAliasCode: String?
        set(value): Unit = set(184, value)
        get(): String? = get(184) as String?

    open var productNameCode: Short?
        set(value): Unit = set(185, value)
        get(): Short? = get(185) as Short?

    open var approvalDivision: Byte?
        set(value): Unit = set(186, value)
        get(): Byte? = get(186) as Byte?

    open var propertyNameJudgmentSign: Byte?
        set(value): Unit = set(187, value)
        get(): Byte? = get(187) as Byte?

    open var depositZeroFlag: Byte?
        set(value): Unit = set(188, value)
        get(): Byte? = get(188) as Byte?

    open var fletsSupportCd: Byte?
        set(value): Unit = set(189, value)
        get(): Byte? = get(189) as Byte?

    open var skyPerfectSupportCd: Byte?
        set(value): Unit = set(190, value)
        get(): Byte? = get(190) as Byte?

    open var campaignTargetFlag: Byte?
        set(value): Unit = set(191, value)
        get(): Byte? = get(191) as Byte?

    open var propertyAddressDetails: String?
        set(value): Unit = set(192, value)
        get(): String? = get(192) as String?

    open var parking_2CarsAvailable: Byte?
        set(value): Unit = set(193, value)
        get(): Byte? = get(193) as Byte?

    open var newInteriorMaterialUsed: Byte?
        set(value): Unit = set(194, value)
        get(): Byte? = get(194) as Byte?

    open var serviceRoomSign: Byte?
        set(value): Unit = set(195, value)
        get(): Byte? = get(195) as Byte?

    open var highVoltagePowerBulkReception: Byte?
        set(value): Unit = set(196, value)
        get(): Byte? = get(196) as Byte?

    open var highCostRentSign: Byte?
        set(value): Unit = set(197, value)
        get(): Byte? = get(197) as Byte?

    open var layoutDisplayOrder: String?
        set(value): Unit = set(198, value)
        get(): String? = get(198) as String?

    open var prefectureKanjiName: String?
        set(value): Unit = set(199, value)
        get(): String? = get(199) as String?

    open var prefectureKanaName: String?
        set(value): Unit = set(200, value)
        get(): String? = get(200) as String?

    open var cityWardKanjiName: String?
        set(value): Unit = set(201, value)
        get(): String? = get(201) as String?

    open var cityWardKanaName: String?
        set(value): Unit = set(202, value)
        get(): String? = get(202) as String?

    open var townVillageKanjiName: String?
        set(value): Unit = set(203, value)
        get(): String? = get(203) as String?

    open var townVillageKanaName: String?
        set(value): Unit = set(204, value)
        get(): String? = get(204) as String?

    open var keyExchangeSpecialContractTarget: Byte?
        set(value): Unit = set(205, value)
        get(): Byte? = get(205) as Byte?

    open var solarPowerDiscountTarget: Byte?
        set(value): Unit = set(206, value)
        get(): Byte? = get(206) as Byte?

    open var cleaningCostFlatRate: Byte?
        set(value): Unit = set(207, value)
        get(): Byte? = get(207) as Byte?

    open var petFlag: Byte?
        set(value): Unit = set(208, value)
        get(): Byte? = get(208) as Byte?

    open var flagReserve: Byte?
        set(value): Unit = set(209, value)
        get(): Byte? = get(209) as Byte?

    open var cleaningCostTotal: Int?
        set(value): Unit = set(210, value)
        get(): Int? = get(210) as Int?

    open var flagReserve_6: Byte?
        set(value): Unit = set(211, value)
        get(): Byte? = get(211) as Byte?

    open var flagReserve_7: Byte?
        set(value): Unit = set(212, value)
        get(): Byte? = get(212) as Byte?

    open var flagReserve_8: Byte?
        set(value): Unit = set(213, value)
        get(): Byte? = get(213) as Byte?

    open var flagReserve_9: Byte?
        set(value): Unit = set(214, value)
        get(): Byte? = get(214) as Byte?

    open var flagReserve_10: Byte?
        set(value): Unit = set(215, value)
        get(): Byte? = get(215) as Byte?

    open var dateReserve_6: Int?
        set(value): Unit = set(216, value)
        get(): Int? = get(216) as Int?

    open var dateReserve_7: Int?
        set(value): Unit = set(217, value)
        get(): Int? = get(217) as Int?

    open var dateReserve_8: Int?
        set(value): Unit = set(218, value)
        get(): Int? = get(218) as Int?

    open var dateReserve_9: Int?
        set(value): Unit = set(219, value)
        get(): Int? = get(219) as Int?

    open var dateReserve_10: Int?
        set(value): Unit = set(220, value)
        get(): Int? = get(220) as Int?

    open var maintenanceFeeMain: Int?
        set(value): Unit = set(221, value)
        get(): Int? = get(221) as Int?

    open var generalCableTvMain: Int?
        set(value): Unit = set(222, value)
        get(): Int? = get(222) as Int?

    open var generalCableTvTax: Int?
        set(value): Unit = set(223, value)
        get(): Int? = get(223) as Int?

    open var generalInternetMain: Int?
        set(value): Unit = set(224, value)
        get(): Int? = get(224) as Int?

    open var generalInternetTax: Int?
        set(value): Unit = set(225, value)
        get(): Int? = get(225) as Int?

    open var generalWaterQualityMaintenanceMain: Int?
        set(value): Unit = set(226, value)
        get(): Int? = get(226) as Int?

    open var generalWaterQualityMaintenanceTax: Int?
        set(value): Unit = set(227, value)
        get(): Int? = get(227) as Int?

    open var generalTenantWaterMain: Int?
        set(value): Unit = set(228, value)
        get(): Int? = get(228) as Int?

    open var generalTenantWaterTax: Int?
        set(value): Unit = set(229, value)
        get(): Int? = get(229) as Int?

    open var generalDrainageUseMain: Int?
        set(value): Unit = set(230, value)
        get(): Int? = get(230) as Int?

    open var generalDrainageUseTax: Int?
        set(value): Unit = set(231, value)
        get(): Int? = get(231) as Int?

    open var generalGarbageCollectionMain: Int?
        set(value): Unit = set(232, value)
        get(): Int? = get(232) as Int?

    open var generalGarbageCollectionTax: Int?
        set(value): Unit = set(233, value)
        get(): Int? = get(233) as Int?

    open var generalCommonAntennaMain: Int?
        set(value): Unit = set(234, value)
        get(): Int? = get(234) as Int?

    open var generalCommonAntennaTax: Int?
        set(value): Unit = set(235, value)
        get(): Int? = get(235) as Int?

    open var generalLandlordCleaningMain: Int?
        set(value): Unit = set(236, value)
        get(): Int? = get(236) as Int?

    open var generalLandlordCleaningTax: Int?
        set(value): Unit = set(237, value)
        get(): Int? = get(237) as Int?

    open var generalBuildingMaintenanceMain: Int?
        set(value): Unit = set(238, value)
        get(): Int? = get(238) as Int?

    open var generalBuildingMaintenanceTax: Int?
        set(value): Unit = set(239, value)
        get(): Int? = get(239) as Int?

    open var generalBuildingManagementMain: Int?
        set(value): Unit = set(240, value)
        get(): Int? = get(240) as Int?

    open var generalBuildingManagementTax: Int?
        set(value): Unit = set(241, value)
        get(): Int? = get(241) as Int?

    open var generalNeighborhoodAssociationMain: Int?
        set(value): Unit = set(242, value)
        get(): Int? = get(242) as Int?

    open var generalNeighborhoodAssociationTax: Int?
        set(value): Unit = set(243, value)
        get(): Int? = get(243) as Int?

    open var generalNeighborhoodAssocOtherMain: Int?
        set(value): Unit = set(244, value)
        get(): Int? = get(244) as Int?

    open var generalNeighborhoodAssocOtherTax: Int?
        set(value): Unit = set(245, value)
        get(): Int? = get(245) as Int?

    open var generalRepaymentAgencyMain: Int?
        set(value): Unit = set(246, value)
        get(): Int? = get(246) as Int?

    open var generalRepaymentAgencyTax: Int?
        set(value): Unit = set(247, value)
        get(): Int? = get(247) as Int?

    open var generalHlCommissionMain: Int?
        set(value): Unit = set(248, value)
        get(): Int? = get(248) as Int?

    open var generalHlCommissionTax: Int?
        set(value): Unit = set(249, value)
        get(): Int? = get(249) as Int?

    open var generalFurnitureIncludedMain: Int?
        set(value): Unit = set(250, value)
        get(): Int? = get(250) as Int?

    open var generalFurnitureIncludedTax: Int?
        set(value): Unit = set(251, value)
        get(): Int? = get(251) as Int?

    open var generalTenantDepositMain: Int?
        set(value): Unit = set(252, value)
        get(): Int? = get(252) as Int?

    open var generalTenantDepositTax: Int?
        set(value): Unit = set(253, value)
        get(): Int? = get(253) as Int?

    open var generalRentalMain: Int?
        set(value): Unit = set(254, value)
        get(): Int? = get(254) as Int?

    open var generalRentalTax: Int?
        set(value): Unit = set(255, value)
        get(): Int? = get(255) as Int?

    open var reserveAmount_1Main: Int?
        set(value): Unit = set(256, value)
        get(): Int? = get(256) as Int?

    open var reserveAmount_1Tax: Int?
        set(value): Unit = set(257, value)
        get(): Int? = get(257) as Int?

    open var reserveAmount_2Main: Int?
        set(value): Unit = set(258, value)
        get(): Int? = get(258) as Int?

    open var reserveAmount_2Tax: Int?
        set(value): Unit = set(259, value)
        get(): Int? = get(259) as Int?

    open var reserveAmount_3Main: Int?
        set(value): Unit = set(260, value)
        get(): Int? = get(260) as Int?

    open var reserveAmount_3Tax: Int?
        set(value): Unit = set(261, value)
        get(): Int? = get(261) as Int?

    open var flagReserve_11: Byte?
        set(value): Unit = set(262, value)
        get(): Byte? = get(262) as Byte?

    open var flagReserve_12: Byte?
        set(value): Unit = set(263, value)
        get(): Byte? = get(263) as Byte?

    open var flagReserve_13: Byte?
        set(value): Unit = set(264, value)
        get(): Byte? = get(264) as Byte?

    open var flagReserve_14: Byte?
        set(value): Unit = set(265, value)
        get(): Byte? = get(265) as Byte?

    open var flagReserve_15: Byte?
        set(value): Unit = set(266, value)
        get(): Byte? = get(266) as Byte?

    open var divisionReserve_1: String?
        set(value): Unit = set(267, value)
        get(): String? = get(267) as String?

    open var divisionReserve_2: String?
        set(value): Unit = set(268, value)
        get(): String? = get(268) as String?

    open var divisionReserve_3: String?
        set(value): Unit = set(269, value)
        get(): String? = get(269) as String?

    open var divisionReserve_4: String?
        set(value): Unit = set(270, value)
        get(): String? = get(270) as String?

    open var divisionReserve_5: String?
        set(value): Unit = set(271, value)
        get(): String? = get(271) as String?

    open var amountReserve_4: Int?
        set(value): Unit = set(272, value)
        get(): Int? = get(272) as Int?

    open var amountReserve_5: Int?
        set(value): Unit = set(273, value)
        get(): Int? = get(273) as Int?

    open var amountReserve_6: Int?
        set(value): Unit = set(274, value)
        get(): Int? = get(274) as Int?

    open var amountReserve_7: Int?
        set(value): Unit = set(275, value)
        get(): Int? = get(275) as Int?

    open var amountReserve_8: Int?
        set(value): Unit = set(276, value)
        get(): Int? = get(276) as Int?

    open var dateReserve_14: Int?
        set(value): Unit = set(277, value)
        get(): Int? = get(277) as Int?

    open var dateReserve_15: Int?
        set(value): Unit = set(278, value)
        get(): Int? = get(278) as Int?

    open var dateReserve_16: Int?
        set(value): Unit = set(279, value)
        get(): Int? = get(279) as Int?

    open var dateReserve_17: Int?
        set(value): Unit = set(280, value)
        get(): Int? = get(280) as Int?

    open var dateReserve_18: Int?
        set(value): Unit = set(281, value)
        get(): Int? = get(281) as Int?

    open var division_1DigitReserve_1: String?
        set(value): Unit = set(282, value)
        get(): String? = get(282) as String?

    open var division_1DigitReserve_2: String?
        set(value): Unit = set(283, value)
        get(): String? = get(283) as String?

    open var division_1DigitReserve_3: String?
        set(value): Unit = set(284, value)
        get(): String? = get(284) as String?

    open var division_1DigitReserve_4: String?
        set(value): Unit = set(285, value)
        get(): String? = get(285) as String?

    open var division_1DigitReserve_5: String?
        set(value): Unit = set(286, value)
        get(): String? = get(286) as String?

    open var leasingStoreCd: String?
        set(value): Unit = set(287, value)
        get(): String? = get(287) as String?

    open var managementBranchCd: String?
        set(value): Unit = set(288, value)
        get(): String? = get(288) as String?

    open var officeCd: String?
        set(value): Unit = set(289, value)
        get(): String? = get(289) as String?

    open var reviewBranchCd: String?
        set(value): Unit = set(290, value)
        get(): String? = get(290) as String?

    open var feature_100: Byte?
        set(value): Unit = set(291, value)
        get(): Byte? = get(291) as Byte?

    open var feature_101: Byte?
        set(value): Unit = set(292, value)
        get(): Byte? = get(292) as Byte?

    open var feature_102: Byte?
        set(value): Unit = set(293, value)
        get(): Byte? = get(293) as Byte?

    open var feature_103: Byte?
        set(value): Unit = set(294, value)
        get(): Byte? = get(294) as Byte?

    open var feature_104: Byte?
        set(value): Unit = set(295, value)
        get(): Byte? = get(295) as Byte?

    open var feature_105: Byte?
        set(value): Unit = set(296, value)
        get(): Byte? = get(296) as Byte?

    open var feature_106: Byte?
        set(value): Unit = set(297, value)
        get(): Byte? = get(297) as Byte?

    open var feature_107: Byte?
        set(value): Unit = set(298, value)
        get(): Byte? = get(298) as Byte?

    open var feature_108: Byte?
        set(value): Unit = set(299, value)
        get(): Byte? = get(299) as Byte?

    open var feature_109: Byte?
        set(value): Unit = set(300, value)
        get(): Byte? = get(300) as Byte?

    open var feature_110: Byte?
        set(value): Unit = set(301, value)
        get(): Byte? = get(301) as Byte?

    open var feature_111: Byte?
        set(value): Unit = set(302, value)
        get(): Byte? = get(302) as Byte?

    open var feature_112: Byte?
        set(value): Unit = set(303, value)
        get(): Byte? = get(303) as Byte?

    open var feature_113: Byte?
        set(value): Unit = set(304, value)
        get(): Byte? = get(304) as Byte?

    open var feature_114: Byte?
        set(value): Unit = set(305, value)
        get(): Byte? = get(305) as Byte?

    open var feature_115: Byte?
        set(value): Unit = set(306, value)
        get(): Byte? = get(306) as Byte?

    open var feature_116: Byte?
        set(value): Unit = set(307, value)
        get(): Byte? = get(307) as Byte?

    open var feature_117: Byte?
        set(value): Unit = set(308, value)
        get(): Byte? = get(308) as Byte?

    open var feature_118: Byte?
        set(value): Unit = set(309, value)
        get(): Byte? = get(309) as Byte?

    open var feature_119: Byte?
        set(value): Unit = set(310, value)
        get(): Byte? = get(310) as Byte?

    open var feature_120: Byte?
        set(value): Unit = set(311, value)
        get(): Byte? = get(311) as Byte?

    open var feature_121: Byte?
        set(value): Unit = set(312, value)
        get(): Byte? = get(312) as Byte?

    open var feature_122: Byte?
        set(value): Unit = set(313, value)
        get(): Byte? = get(313) as Byte?

    open var feature_123: Byte?
        set(value): Unit = set(314, value)
        get(): Byte? = get(314) as Byte?

    open var feature_124: Byte?
        set(value): Unit = set(315, value)
        get(): Byte? = get(315) as Byte?

    open var feature_125: Byte?
        set(value): Unit = set(316, value)
        get(): Byte? = get(316) as Byte?

    open var feature_126: Byte?
        set(value): Unit = set(317, value)
        get(): Byte? = get(317) as Byte?

    open var feature_127: Byte?
        set(value): Unit = set(318, value)
        get(): Byte? = get(318) as Byte?

    open var feature_128: Byte?
        set(value): Unit = set(319, value)
        get(): Byte? = get(319) as Byte?

    open var feature_129: Byte?
        set(value): Unit = set(320, value)
        get(): Byte? = get(320) as Byte?

    open var feature_130: Byte?
        set(value): Unit = set(321, value)
        get(): Byte? = get(321) as Byte?

    open var feature_131: Byte?
        set(value): Unit = set(322, value)
        get(): Byte? = get(322) as Byte?

    open var feature_132: Byte?
        set(value): Unit = set(323, value)
        get(): Byte? = get(323) as Byte?

    open var feature_133: Byte?
        set(value): Unit = set(324, value)
        get(): Byte? = get(324) as Byte?

    open var feature_134: Byte?
        set(value): Unit = set(325, value)
        get(): Byte? = get(325) as Byte?

    open var feature_135: Byte?
        set(value): Unit = set(326, value)
        get(): Byte? = get(326) as Byte?

    open var feature_136: Byte?
        set(value): Unit = set(327, value)
        get(): Byte? = get(327) as Byte?

    open var feature_137: Byte?
        set(value): Unit = set(328, value)
        get(): Byte? = get(328) as Byte?

    open var feature_138: Byte?
        set(value): Unit = set(329, value)
        get(): Byte? = get(329) as Byte?

    open var feature_139: Byte?
        set(value): Unit = set(330, value)
        get(): Byte? = get(330) as Byte?

    open var feature_140: Byte?
        set(value): Unit = set(331, value)
        get(): Byte? = get(331) as Byte?

    open var feature_141: Byte?
        set(value): Unit = set(332, value)
        get(): Byte? = get(332) as Byte?

    open var feature_142: Byte?
        set(value): Unit = set(333, value)
        get(): Byte? = get(333) as Byte?

    open var feature_143: Byte?
        set(value): Unit = set(334, value)
        get(): Byte? = get(334) as Byte?

    open var feature_144: Byte?
        set(value): Unit = set(335, value)
        get(): Byte? = get(335) as Byte?

    open var feature_145: Byte?
        set(value): Unit = set(336, value)
        get(): Byte? = get(336) as Byte?

    open var feature_146: Byte?
        set(value): Unit = set(337, value)
        get(): Byte? = get(337) as Byte?

    open var feature_147: Byte?
        set(value): Unit = set(338, value)
        get(): Byte? = get(338) as Byte?

    open var feature_148: Byte?
        set(value): Unit = set(339, value)
        get(): Byte? = get(339) as Byte?

    open var feature_149: Byte?
        set(value): Unit = set(340, value)
        get(): Byte? = get(340) as Byte?

    open var feature_150: Byte?
        set(value): Unit = set(341, value)
        get(): Byte? = get(341) as Byte?

    open var feature_151: Byte?
        set(value): Unit = set(342, value)
        get(): Byte? = get(342) as Byte?

    open var feature_152: Byte?
        set(value): Unit = set(343, value)
        get(): Byte? = get(343) as Byte?

    open var feature_153: Byte?
        set(value): Unit = set(344, value)
        get(): Byte? = get(344) as Byte?

    open var feature_154: Byte?
        set(value): Unit = set(345, value)
        get(): Byte? = get(345) as Byte?

    open var feature_155: Byte?
        set(value): Unit = set(346, value)
        get(): Byte? = get(346) as Byte?

    open var feature_156: Byte?
        set(value): Unit = set(347, value)
        get(): Byte? = get(347) as Byte?

    open var feature_157: Byte?
        set(value): Unit = set(348, value)
        get(): Byte? = get(348) as Byte?

    open var feature_158: Byte?
        set(value): Unit = set(349, value)
        get(): Byte? = get(349) as Byte?

    open var feature_159: Byte?
        set(value): Unit = set(350, value)
        get(): Byte? = get(350) as Byte?

    open var feature_160: Byte?
        set(value): Unit = set(351, value)
        get(): Byte? = get(351) as Byte?

    open var feature_161: Byte?
        set(value): Unit = set(352, value)
        get(): Byte? = get(352) as Byte?

    open var feature_162: Byte?
        set(value): Unit = set(353, value)
        get(): Byte? = get(353) as Byte?

    open var feature_163: Byte?
        set(value): Unit = set(354, value)
        get(): Byte? = get(354) as Byte?

    open var feature_164: Byte?
        set(value): Unit = set(355, value)
        get(): Byte? = get(355) as Byte?

    open var feature_165: Byte?
        set(value): Unit = set(356, value)
        get(): Byte? = get(356) as Byte?

    open var feature_166: Byte?
        set(value): Unit = set(357, value)
        get(): Byte? = get(357) as Byte?

    open var feature_167: Byte?
        set(value): Unit = set(358, value)
        get(): Byte? = get(358) as Byte?

    open var feature_168: Byte?
        set(value): Unit = set(359, value)
        get(): Byte? = get(359) as Byte?

    open var feature_169: Byte?
        set(value): Unit = set(360, value)
        get(): Byte? = get(360) as Byte?

    open var feature_170: Byte?
        set(value): Unit = set(361, value)
        get(): Byte? = get(361) as Byte?

    open var feature_171: Byte?
        set(value): Unit = set(362, value)
        get(): Byte? = get(362) as Byte?

    open var feature_172: Byte?
        set(value): Unit = set(363, value)
        get(): Byte? = get(363) as Byte?

    open var feature_173: Byte?
        set(value): Unit = set(364, value)
        get(): Byte? = get(364) as Byte?

    open var feature_174: Byte?
        set(value): Unit = set(365, value)
        get(): Byte? = get(365) as Byte?

    open var feature_175: Byte?
        set(value): Unit = set(366, value)
        get(): Byte? = get(366) as Byte?

    open var feature_176: Byte?
        set(value): Unit = set(367, value)
        get(): Byte? = get(367) as Byte?

    open var feature_177: Byte?
        set(value): Unit = set(368, value)
        get(): Byte? = get(368) as Byte?

    open var feature_178: Byte?
        set(value): Unit = set(369, value)
        get(): Byte? = get(369) as Byte?

    open var feature_179: Byte?
        set(value): Unit = set(370, value)
        get(): Byte? = get(370) as Byte?

    open var feature_180: Byte?
        set(value): Unit = set(371, value)
        get(): Byte? = get(371) as Byte?

    open var feature_181: Byte?
        set(value): Unit = set(372, value)
        get(): Byte? = get(372) as Byte?

    open var feature_182: Byte?
        set(value): Unit = set(373, value)
        get(): Byte? = get(373) as Byte?

    open var feature_183: Byte?
        set(value): Unit = set(374, value)
        get(): Byte? = get(374) as Byte?

    open var feature_184: Byte?
        set(value): Unit = set(375, value)
        get(): Byte? = get(375) as Byte?

    open var feature_185: Byte?
        set(value): Unit = set(376, value)
        get(): Byte? = get(376) as Byte?

    open var feature_186: Byte?
        set(value): Unit = set(377, value)
        get(): Byte? = get(377) as Byte?

    open var feature_187: Byte?
        set(value): Unit = set(378, value)
        get(): Byte? = get(378) as Byte?

    open var feature_188: Byte?
        set(value): Unit = set(379, value)
        get(): Byte? = get(379) as Byte?

    open var feature_189: Byte?
        set(value): Unit = set(380, value)
        get(): Byte? = get(380) as Byte?

    open var feature_190: Byte?
        set(value): Unit = set(381, value)
        get(): Byte? = get(381) as Byte?

    open var feature_191: Byte?
        set(value): Unit = set(382, value)
        get(): Byte? = get(382) as Byte?

    open var feature_192: Byte?
        set(value): Unit = set(383, value)
        get(): Byte? = get(383) as Byte?

    open var feature_193: Byte?
        set(value): Unit = set(384, value)
        get(): Byte? = get(384) as Byte?

    open var feature_194: Byte?
        set(value): Unit = set(385, value)
        get(): Byte? = get(385) as Byte?

    open var feature_195: Byte?
        set(value): Unit = set(386, value)
        get(): Byte? = get(386) as Byte?

    open var feature_196: Byte?
        set(value): Unit = set(387, value)
        get(): Byte? = get(387) as Byte?

    open var feature_197: Byte?
        set(value): Unit = set(388, value)
        get(): Byte? = get(388) as Byte?

    open var feature_198: Byte?
        set(value): Unit = set(389, value)
        get(): Byte? = get(389) as Byte?

    open var feature_199: Byte?
        set(value): Unit = set(390, value)
        get(): Byte? = get(390) as Byte?

    open var marketingBranchOfficeCd: String?
        set(value): Unit = set(391, value)
        get(): String? = get(391) as String?

    /**
     * Create a detached, initialised VacantHouseHpRecord
     */
    constructor(value: VacantHouseHpPojo?): this() {
        if (value != null) {
            this.propertyCdDivision = value.propertyCdDivision
            this.propertyCdSeparator_1 = value.propertyCdSeparator_1
            this.propertyBuildingCd = value.propertyBuildingCd
            this.propertyCdSeparator_2 = value.propertyCdSeparator_2
            this.propertyRoomCd = value.propertyRoomCd
            this.deleteFlag = value.deleteFlag
            this.customerCompanyCd = value.customerCompanyCd
            this.customerBranchCd = value.customerBranchCd
            this.customerDepartmentCd = value.customerDepartmentCd
            this.customerCompletionFlag = value.customerCompletionFlag
            this.municipalityCd = value.municipalityCd
            this.lineCd = value.lineCd
            this.stationCd = value.stationCd
            this.rent = value.rent
            this.layoutRooms = value.layoutRooms
            this.exclusiveArea = value.exclusiveArea
            this.propertyType = value.propertyType
            this.feature_1 = value.feature_1
            this.feature_2 = value.feature_2
            this.feature_3 = value.feature_3
            this.feature_4 = value.feature_4
            this.feature_5 = value.feature_5
            this.feature_6 = value.feature_6
            this.feature_7 = value.feature_7
            this.feature_8 = value.feature_8
            this.feature_9 = value.feature_9
            this.feature_10 = value.feature_10
            this.feature_11 = value.feature_11
            this.feature_12 = value.feature_12
            this.feature_13 = value.feature_13
            this.feature_14 = value.feature_14
            this.feature_15 = value.feature_15
            this.feature_16 = value.feature_16
            this.feature_17 = value.feature_17
            this.feature_18 = value.feature_18
            this.feature_19 = value.feature_19
            this.feature_20 = value.feature_20
            this.feature_21 = value.feature_21
            this.feature_22 = value.feature_22
            this.feature_23 = value.feature_23
            this.feature_24 = value.feature_24
            this.feature_25 = value.feature_25
            this.feature_26 = value.feature_26
            this.feature_27 = value.feature_27
            this.feature_28 = value.feature_28
            this.feature_29 = value.feature_29
            this.feature_30 = value.feature_30
            this.feature_31 = value.feature_31
            this.feature_32 = value.feature_32
            this.feature_33 = value.feature_33
            this.feature_34 = value.feature_34
            this.feature_35 = value.feature_35
            this.feature_36 = value.feature_36
            this.feature_37 = value.feature_37
            this.feature_38 = value.feature_38
            this.feature_39 = value.feature_39
            this.feature_40 = value.feature_40
            this.feature_41 = value.feature_41
            this.feature_42 = value.feature_42
            this.feature_43 = value.feature_43
            this.feature_44 = value.feature_44
            this.feature_45 = value.feature_45
            this.feature_46 = value.feature_46
            this.feature_47 = value.feature_47
            this.feature_48 = value.feature_48
            this.feature_49 = value.feature_49
            this.feature_50 = value.feature_50
            this.feature_51 = value.feature_51
            this.feature_52 = value.feature_52
            this.feature_53 = value.feature_53
            this.feature_54 = value.feature_54
            this.feature_55 = value.feature_55
            this.feature_56 = value.feature_56
            this.feature_57 = value.feature_57
            this.feature_58 = value.feature_58
            this.feature_59 = value.feature_59
            this.feature_60 = value.feature_60
            this.feature_99 = value.feature_99
            this.featureNewBuilding = value.featureNewBuilding
            this.featureCornerRoom = value.featureCornerRoom
            this.featureAboveSecondFloor = value.featureAboveSecondFloor
            this.lineName = value.lineName
            this.stationName = value.stationName
            this.busStopName = value.busStopName
            this.busTime = value.busTime
            this.walkTime = value.walkTime
            this.distance = value.distance
            this.keyMoney = value.keyMoney
            this.deposit = value.deposit
            this.neighborhoodAssociationFee = value.neighborhoodAssociationFee
            this.maintenanceFee = value.maintenanceFee
            this.roomTypeName = value.roomTypeName
            this.layout = value.layout
            this.layoutDetails = value.layoutDetails
            this.parkingDivision = value.parkingDivision
            this.parkingFee = value.parkingFee
            this.buildYear = value.buildYear
            this.handlingStoreCompany = value.handlingStoreCompany
            this.locationPublishAreaName = value.locationPublishAreaName
            this.floorCount = value.floorCount
            this.direction = value.direction
            this.roomPosition = value.roomPosition
            this.availableDate = value.availableDate
            this.transportation = value.transportation
            this.equipment = value.equipment
            this.remarks = value.remarks
            this.contactBranchName = value.contactBranchName
            this.branchPhoneNumber = value.branchPhoneNumber
            this.branchFaxNumber = value.branchFaxNumber
            this.transactionType = value.transactionType
            this.buildingName = value.buildingName
            this.structureName = value.structureName
            this.agentAvailableDivision = value.agentAvailableDivision
            this.subleaseDivision = value.subleaseDivision
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.branchAddress = value.branchAddress
            this.recommendationComment = value.recommendationComment
            this.completionYearMonth = value.completionYearMonth
            this.propertyPostalCode = value.propertyPostalCode
            this.recordSeparator = value.recordSeparator
            this.rentTax = value.rentTax
            this.keyMoneyTax = value.keyMoneyTax
            this.keyMoneyTotal = value.keyMoneyTotal
            this.maintenanceFeeTax = value.maintenanceFeeTax
            this.parkingFeeTax = value.parkingFeeTax
            this.changeDivision = value.changeDivision
            this.updateFlag = value.updateFlag
            this.feature_61 = value.feature_61
            this.feature_62 = value.feature_62
            this.feature_63 = value.feature_63
            this.feature_64 = value.feature_64
            this.feature_65 = value.feature_65
            this.feature_66 = value.feature_66
            this.feature_67 = value.feature_67
            this.feature_68 = value.feature_68
            this.feature_69 = value.feature_69
            this.feature_70 = value.feature_70
            this.feature_71 = value.feature_71
            this.feature_72 = value.feature_72
            this.feature_73 = value.feature_73
            this.feature_74 = value.feature_74
            this.feature_75 = value.feature_75
            this.feature_76 = value.feature_76
            this.feature_77 = value.feature_77
            this.feature_78 = value.feature_78
            this.feature_79 = value.feature_79
            this.feature_80 = value.feature_80
            this.feature_81 = value.feature_81
            this.feature_82 = value.feature_82
            this.feature_83 = value.feature_83
            this.feature_84 = value.feature_84
            this.feature_85 = value.feature_85
            this.feature_86 = value.feature_86
            this.feature_87 = value.feature_87
            this.feature_88 = value.feature_88
            this.feature_89 = value.feature_89
            this.feature_90 = value.feature_90
            this.feature_91 = value.feature_91
            this.feature_92 = value.feature_92
            this.feature_93 = value.feature_93
            this.feature_94 = value.feature_94
            this.feature_95 = value.feature_95
            this.feature_96 = value.feature_96
            this.feature_97 = value.feature_97
            this.feature_98 = value.feature_98
            this.equipmentFlag_1 = value.equipmentFlag_1
            this.equipmentFlag_2 = value.equipmentFlag_2
            this.equipmentFlag_3 = value.equipmentFlag_3
            this.equipmentFlag_4 = value.equipmentFlag_4
            this.equipmentFlag_5 = value.equipmentFlag_5
            this.equipmentFlag_6 = value.equipmentFlag_6
            this.equipmentFlag_7 = value.equipmentFlag_7
            this.equipmentFlag_8 = value.equipmentFlag_8
            this.equipmentFlag_9 = value.equipmentFlag_9
            this.equipmentFlag_10 = value.equipmentFlag_10
            this.rentalDivision = value.rentalDivision
            this.distance2 = value.distance2
            this.prefectureCode = value.prefectureCode
            this.cityWardCode = value.cityWardCode
            this.townVillageAliasCode = value.townVillageAliasCode
            this.productNameCode = value.productNameCode
            this.approvalDivision = value.approvalDivision
            this.propertyNameJudgmentSign = value.propertyNameJudgmentSign
            this.depositZeroFlag = value.depositZeroFlag
            this.fletsSupportCd = value.fletsSupportCd
            this.skyPerfectSupportCd = value.skyPerfectSupportCd
            this.campaignTargetFlag = value.campaignTargetFlag
            this.propertyAddressDetails = value.propertyAddressDetails
            this.parking_2CarsAvailable = value.parking_2CarsAvailable
            this.newInteriorMaterialUsed = value.newInteriorMaterialUsed
            this.serviceRoomSign = value.serviceRoomSign
            this.highVoltagePowerBulkReception = value.highVoltagePowerBulkReception
            this.highCostRentSign = value.highCostRentSign
            this.layoutDisplayOrder = value.layoutDisplayOrder
            this.prefectureKanjiName = value.prefectureKanjiName
            this.prefectureKanaName = value.prefectureKanaName
            this.cityWardKanjiName = value.cityWardKanjiName
            this.cityWardKanaName = value.cityWardKanaName
            this.townVillageKanjiName = value.townVillageKanjiName
            this.townVillageKanaName = value.townVillageKanaName
            this.keyExchangeSpecialContractTarget = value.keyExchangeSpecialContractTarget
            this.solarPowerDiscountTarget = value.solarPowerDiscountTarget
            this.cleaningCostFlatRate = value.cleaningCostFlatRate
            this.petFlag = value.petFlag
            this.flagReserve = value.flagReserve
            this.cleaningCostTotal = value.cleaningCostTotal
            this.flagReserve_6 = value.flagReserve_6
            this.flagReserve_7 = value.flagReserve_7
            this.flagReserve_8 = value.flagReserve_8
            this.flagReserve_9 = value.flagReserve_9
            this.flagReserve_10 = value.flagReserve_10
            this.dateReserve_6 = value.dateReserve_6
            this.dateReserve_7 = value.dateReserve_7
            this.dateReserve_8 = value.dateReserve_8
            this.dateReserve_9 = value.dateReserve_9
            this.dateReserve_10 = value.dateReserve_10
            this.maintenanceFeeMain = value.maintenanceFeeMain
            this.generalCableTvMain = value.generalCableTvMain
            this.generalCableTvTax = value.generalCableTvTax
            this.generalInternetMain = value.generalInternetMain
            this.generalInternetTax = value.generalInternetTax
            this.generalWaterQualityMaintenanceMain = value.generalWaterQualityMaintenanceMain
            this.generalWaterQualityMaintenanceTax = value.generalWaterQualityMaintenanceTax
            this.generalTenantWaterMain = value.generalTenantWaterMain
            this.generalTenantWaterTax = value.generalTenantWaterTax
            this.generalDrainageUseMain = value.generalDrainageUseMain
            this.generalDrainageUseTax = value.generalDrainageUseTax
            this.generalGarbageCollectionMain = value.generalGarbageCollectionMain
            this.generalGarbageCollectionTax = value.generalGarbageCollectionTax
            this.generalCommonAntennaMain = value.generalCommonAntennaMain
            this.generalCommonAntennaTax = value.generalCommonAntennaTax
            this.generalLandlordCleaningMain = value.generalLandlordCleaningMain
            this.generalLandlordCleaningTax = value.generalLandlordCleaningTax
            this.generalBuildingMaintenanceMain = value.generalBuildingMaintenanceMain
            this.generalBuildingMaintenanceTax = value.generalBuildingMaintenanceTax
            this.generalBuildingManagementMain = value.generalBuildingManagementMain
            this.generalBuildingManagementTax = value.generalBuildingManagementTax
            this.generalNeighborhoodAssociationMain = value.generalNeighborhoodAssociationMain
            this.generalNeighborhoodAssociationTax = value.generalNeighborhoodAssociationTax
            this.generalNeighborhoodAssocOtherMain = value.generalNeighborhoodAssocOtherMain
            this.generalNeighborhoodAssocOtherTax = value.generalNeighborhoodAssocOtherTax
            this.generalRepaymentAgencyMain = value.generalRepaymentAgencyMain
            this.generalRepaymentAgencyTax = value.generalRepaymentAgencyTax
            this.generalHlCommissionMain = value.generalHlCommissionMain
            this.generalHlCommissionTax = value.generalHlCommissionTax
            this.generalFurnitureIncludedMain = value.generalFurnitureIncludedMain
            this.generalFurnitureIncludedTax = value.generalFurnitureIncludedTax
            this.generalTenantDepositMain = value.generalTenantDepositMain
            this.generalTenantDepositTax = value.generalTenantDepositTax
            this.generalRentalMain = value.generalRentalMain
            this.generalRentalTax = value.generalRentalTax
            this.reserveAmount_1Main = value.reserveAmount_1Main
            this.reserveAmount_1Tax = value.reserveAmount_1Tax
            this.reserveAmount_2Main = value.reserveAmount_2Main
            this.reserveAmount_2Tax = value.reserveAmount_2Tax
            this.reserveAmount_3Main = value.reserveAmount_3Main
            this.reserveAmount_3Tax = value.reserveAmount_3Tax
            this.flagReserve_11 = value.flagReserve_11
            this.flagReserve_12 = value.flagReserve_12
            this.flagReserve_13 = value.flagReserve_13
            this.flagReserve_14 = value.flagReserve_14
            this.flagReserve_15 = value.flagReserve_15
            this.divisionReserve_1 = value.divisionReserve_1
            this.divisionReserve_2 = value.divisionReserve_2
            this.divisionReserve_3 = value.divisionReserve_3
            this.divisionReserve_4 = value.divisionReserve_4
            this.divisionReserve_5 = value.divisionReserve_5
            this.amountReserve_4 = value.amountReserve_4
            this.amountReserve_5 = value.amountReserve_5
            this.amountReserve_6 = value.amountReserve_6
            this.amountReserve_7 = value.amountReserve_7
            this.amountReserve_8 = value.amountReserve_8
            this.dateReserve_14 = value.dateReserve_14
            this.dateReserve_15 = value.dateReserve_15
            this.dateReserve_16 = value.dateReserve_16
            this.dateReserve_17 = value.dateReserve_17
            this.dateReserve_18 = value.dateReserve_18
            this.division_1DigitReserve_1 = value.division_1DigitReserve_1
            this.division_1DigitReserve_2 = value.division_1DigitReserve_2
            this.division_1DigitReserve_3 = value.division_1DigitReserve_3
            this.division_1DigitReserve_4 = value.division_1DigitReserve_4
            this.division_1DigitReserve_5 = value.division_1DigitReserve_5
            this.leasingStoreCd = value.leasingStoreCd
            this.managementBranchCd = value.managementBranchCd
            this.officeCd = value.officeCd
            this.reviewBranchCd = value.reviewBranchCd
            this.feature_100 = value.feature_100
            this.feature_101 = value.feature_101
            this.feature_102 = value.feature_102
            this.feature_103 = value.feature_103
            this.feature_104 = value.feature_104
            this.feature_105 = value.feature_105
            this.feature_106 = value.feature_106
            this.feature_107 = value.feature_107
            this.feature_108 = value.feature_108
            this.feature_109 = value.feature_109
            this.feature_110 = value.feature_110
            this.feature_111 = value.feature_111
            this.feature_112 = value.feature_112
            this.feature_113 = value.feature_113
            this.feature_114 = value.feature_114
            this.feature_115 = value.feature_115
            this.feature_116 = value.feature_116
            this.feature_117 = value.feature_117
            this.feature_118 = value.feature_118
            this.feature_119 = value.feature_119
            this.feature_120 = value.feature_120
            this.feature_121 = value.feature_121
            this.feature_122 = value.feature_122
            this.feature_123 = value.feature_123
            this.feature_124 = value.feature_124
            this.feature_125 = value.feature_125
            this.feature_126 = value.feature_126
            this.feature_127 = value.feature_127
            this.feature_128 = value.feature_128
            this.feature_129 = value.feature_129
            this.feature_130 = value.feature_130
            this.feature_131 = value.feature_131
            this.feature_132 = value.feature_132
            this.feature_133 = value.feature_133
            this.feature_134 = value.feature_134
            this.feature_135 = value.feature_135
            this.feature_136 = value.feature_136
            this.feature_137 = value.feature_137
            this.feature_138 = value.feature_138
            this.feature_139 = value.feature_139
            this.feature_140 = value.feature_140
            this.feature_141 = value.feature_141
            this.feature_142 = value.feature_142
            this.feature_143 = value.feature_143
            this.feature_144 = value.feature_144
            this.feature_145 = value.feature_145
            this.feature_146 = value.feature_146
            this.feature_147 = value.feature_147
            this.feature_148 = value.feature_148
            this.feature_149 = value.feature_149
            this.feature_150 = value.feature_150
            this.feature_151 = value.feature_151
            this.feature_152 = value.feature_152
            this.feature_153 = value.feature_153
            this.feature_154 = value.feature_154
            this.feature_155 = value.feature_155
            this.feature_156 = value.feature_156
            this.feature_157 = value.feature_157
            this.feature_158 = value.feature_158
            this.feature_159 = value.feature_159
            this.feature_160 = value.feature_160
            this.feature_161 = value.feature_161
            this.feature_162 = value.feature_162
            this.feature_163 = value.feature_163
            this.feature_164 = value.feature_164
            this.feature_165 = value.feature_165
            this.feature_166 = value.feature_166
            this.feature_167 = value.feature_167
            this.feature_168 = value.feature_168
            this.feature_169 = value.feature_169
            this.feature_170 = value.feature_170
            this.feature_171 = value.feature_171
            this.feature_172 = value.feature_172
            this.feature_173 = value.feature_173
            this.feature_174 = value.feature_174
            this.feature_175 = value.feature_175
            this.feature_176 = value.feature_176
            this.feature_177 = value.feature_177
            this.feature_178 = value.feature_178
            this.feature_179 = value.feature_179
            this.feature_180 = value.feature_180
            this.feature_181 = value.feature_181
            this.feature_182 = value.feature_182
            this.feature_183 = value.feature_183
            this.feature_184 = value.feature_184
            this.feature_185 = value.feature_185
            this.feature_186 = value.feature_186
            this.feature_187 = value.feature_187
            this.feature_188 = value.feature_188
            this.feature_189 = value.feature_189
            this.feature_190 = value.feature_190
            this.feature_191 = value.feature_191
            this.feature_192 = value.feature_192
            this.feature_193 = value.feature_193
            this.feature_194 = value.feature_194
            this.feature_195 = value.feature_195
            this.feature_196 = value.feature_196
            this.feature_197 = value.feature_197
            this.feature_198 = value.feature_198
            this.feature_199 = value.feature_199
            this.marketingBranchOfficeCd = value.marketingBranchOfficeCd
            resetChangedOnNotNull()
        }
    }
}
