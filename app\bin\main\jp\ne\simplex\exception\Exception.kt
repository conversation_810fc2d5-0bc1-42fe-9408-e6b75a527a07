package jp.ne.simplex.exception

open class BaseException(val type: ErrorType, val detail: ErrorMessage.Detail, ex: Throwable?) :
    RuntimeException(detail.message, ex)

/** クライアントからのリクエスト不正時などにスローする用のException */
class ClientValidationException(detail: ErrorMessage.Detail, ex: Throwable? = null) :
    BaseException(ErrorType.BAD_REQUEST, detail, ex)

/** サービス層以降でバリデーションエラー時などにスローする用のException */
class ServerValidationException(detail: ErrorMessage.Detail, ex: Throwable? = null) :
    BaseException(ErrorType.BAD_REQUEST, detail, ex)

/** モデルの作成に失敗したときにスローする用のException */
class ModelCreationFailedException(detail: ErrorMessage.Detail, ex: Throwable? = null) :
    BaseException(ErrorType.MODEL_CREATION_FAILED, detail, ex)

/** DBアクセス時のバージョン不正による競合発生時や、レコードロック取得失敗時などにスローする用のException */
class DBValidationException(detail: ErrorMessage.Detail, ex: Throwable? = null) :
    BaseException(ErrorType.DB_UPDATE_FAILED, detail, ex)

/** データ不整合発生時にスローするException（基本的に発生し得ない想定） */
class DataInconsistencyException(detail: ErrorMessage.Detail, ex: Throwable? = null) :
    BaseException(ErrorType.DATA_INCONSISTENCY, detail, ex)

/** 外部システムのAPIコール時の認証エラー発生時にスローする用のException */
class ExternalApiUnauthorizedException(type: ErrorType, detail: ErrorMessage.Detail) :
    BaseException(type, detail, null)

/** 外部システムのAPIコール時の接続失敗などシステムレベルの問題発生時にスローする用のException */
class ExternalApiConnectionException(type: ErrorType, ex: Throwable? = null) :
    BaseException(type, ErrorMessage.UNEXPECTED_ERROR.format(), ex)

/** 外部システム側でAPIの処理に失敗した場合（APIレスポンスでNGが返却された場合など） */
class ExternalApiServerException(type: ErrorType, detail: ErrorMessage.Detail) :
    BaseException(type, detail, null)


