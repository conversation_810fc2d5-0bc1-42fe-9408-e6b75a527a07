/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.LatestRentEvaluationTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.LatestRentEvaluationPojo

import org.jooq.impl.TableRecordImpl


/**
 * 最新家賃査定 既存システム物理名: EAC30P
 */
@Suppress("UNCHECKED_CAST")
open class LatestRentEvaluationRecord private constructor() : TableRecordImpl<LatestRentEvaluationRecord>(LatestRentEvaluationTable.LATEST_RENT_EVALUATION) {

    open var creationMonth: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateMonth: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var assessmentReviewNumber: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var latestRentAssessmentHistory: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var buildingCode: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var propertyCode: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var roomParkingDivision: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var taxDivision: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var keyMoneyMonths: BigDecimal?
        set(value): Unit = set(12, value)
        get(): BigDecimal? = get(12) as BigDecimal?

    open var depositMonths: BigDecimal?
        set(value): Unit = set(13, value)
        get(): BigDecimal? = get(13) as BigDecimal?

    open var depreciationMonths: BigDecimal?
        set(value): Unit = set(14, value)
        get(): BigDecimal? = get(14) as BigDecimal?

    open var keyMoneyAmount: Int?
        set(value): Unit = set(15, value)
        get(): Int? = get(15) as Int?

    open var keyMoneyInoutDivision: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var additionalKeyMoneyAmount: Int?
        set(value): Unit = set(17, value)
        get(): Int? = get(17) as Int?

    open var depositAmount: Int?
        set(value): Unit = set(18, value)
        get(): Int? = get(18) as Int?

    open var depreciation: Int?
        set(value): Unit = set(19, value)
        get(): Int? = get(19) as Int?

    open var rent: Int?
        set(value): Unit = set(20, value)
        get(): Int? = get(20) as Int?

    open var rentInoutDivision: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var parkingFeeCombinedSign: Byte?
        set(value): Unit = set(22, value)
        get(): Byte? = get(22) as Byte?

    open var parkingFee: Int?
        set(value): Unit = set(23, value)
        get(): Int? = get(23) as Int?

    open var parkingFeeInoutDivision: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var commonFee: Int?
        set(value): Unit = set(25, value)
        get(): Int? = get(25) as Int?

    open var commonFeeInoutDivision: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var neighborhoodAssociationFee: Int?
        set(value): Unit = set(27, value)
        get(): Int? = get(27) as Int?

    open var newRentAdded: Int?
        set(value): Unit = set(28, value)
        get(): Int? = get(28) as Int?

    open var standardRentForCoop: Int?
        set(value): Unit = set(29, value)
        get(): Int? = get(29) as Int?

    open var initialSetRent: Int?
        set(value): Unit = set(30, value)
        get(): Int? = get(30) as Int?

    open var managementFee: Int?
        set(value): Unit = set(31, value)
        get(): Int? = get(31) as Int?

    open var managementFeeInoutDivision: String?
        set(value): Unit = set(32, value)
        get(): String? = get(32) as String?

    open var managementFeeDiscountAmount: Int?
        set(value): Unit = set(33, value)
        get(): Int? = get(33) as Int?

    open var coopFee: Int?
        set(value): Unit = set(34, value)
        get(): Int? = get(34) as Int?

    open var coopBenefitAmount: Int?
        set(value): Unit = set(35, value)
        get(): Int? = get(35) as Int?

    open var mngContractInitialPayment: Int?
        set(value): Unit = set(36, value)
        get(): Int? = get(36) as Int?

    open var mngContractInitialPaymentInout: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var tenantRegistrationFee: Int?
        set(value): Unit = set(38, value)
        get(): Int? = get(38) as Int?

    open var tenantRegistrationFeeInout: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var maintenanceFee: Int?
        set(value): Unit = set(40, value)
        get(): Int? = get(40) as Int?

    open var maintenanceFeeInoutDivision: String?
        set(value): Unit = set(41, value)
        get(): String? = get(41) as String?

    open var neighborhoodFeePaidByDaito: Int?
        set(value): Unit = set(42, value)
        get(): Int? = get(42) as Int?

    open var guaranteeDivision: String?
        set(value): Unit = set(43, value)
        get(): String? = get(43) as String?

    open var coopMembershipFee: Int?
        set(value): Unit = set(44, value)
        get(): Int? = get(44) as Int?

    open var waterManagementFee: Int?
        set(value): Unit = set(45, value)
        get(): Int? = get(45) as Int?

    open var latestRentAssessmentApprover: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var latestRentAssessmentDate: Int?
        set(value): Unit = set(47, value)
        get(): Int? = get(47) as Int?

    open var brokerApplicationClearSign: Byte?
        set(value): Unit = set(48, value)
        get(): Byte? = get(48) as Byte?

    open var brokerApplicationOutputDate: Int?
        set(value): Unit = set(49, value)
        get(): Int? = get(49) as Int?

    open var outputTaxRateDivision: String?
        set(value): Unit = set(50, value)
        get(): String? = get(50) as String?

    open var buildingUnitOutputCount: Byte?
        set(value): Unit = set(51, value)
        get(): Byte? = get(51) as Byte?

    open var roomUnitOutputCount: Byte?
        set(value): Unit = set(52, value)
        get(): Byte? = get(52) as Byte?

    open var brokerApplicationCollectionDivision: String?
        set(value): Unit = set(53, value)
        get(): String? = get(53) as String?

    open var brokerApplicationOutputUnitDivision: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var multipleOutputNumber: Short?
        set(value): Unit = set(55, value)
        get(): Short? = get(55) as Short?

    open var brokerApplicationFormNumber: String?
        set(value): Unit = set(56, value)
        get(): String? = get(56) as String?

    open var brokerApplicationApprovalDate: Int?
        set(value): Unit = set(57, value)
        get(): Int? = get(57) as Int?

    open var brokerApplicationCollectionDate: Int?
        set(value): Unit = set(58, value)
        get(): Int? = get(58) as Int?

    open var initialCollectionEntryDate: Int?
        set(value): Unit = set(59, value)
        get(): Int? = get(59) as Int?

    open var brokerApplicationResponsibleBranch: String?
        set(value): Unit = set(60, value)
        get(): String? = get(60) as String?

    open var brokerApplicationResponsiblePerson: String?
        set(value): Unit = set(61, value)
        get(): String? = get(61) as String?

    open var roomCode: String?
        set(value): Unit = set(62, value)
        get(): String? = get(62) as String?

    open var dataMigrationOriginKey1: String?
        set(value): Unit = set(63, value)
        get(): String? = get(63) as String?

    open var dataMigrationOriginKey2: String?
        set(value): Unit = set(64, value)
        get(): String? = get(64) as String?

    open var transmissionDivision: String?
        set(value): Unit = set(65, value)
        get(): String? = get(65) as String?

    open var contractRent: Int?
        set(value): Unit = set(66, value)
        get(): Int? = get(66) as Int?

    open var nonStandardDivision: Byte?
        set(value): Unit = set(67, value)
        get(): Byte? = get(67) as Byte?

    open var collectionResponsiblePersonCode: String?
        set(value): Unit = set(68, value)
        get(): String? = get(68) as String?

    open var recruitmentStartDate: Int?
        set(value): Unit = set(69, value)
        get(): Int? = get(69) as Int?

    open var postVacancyCollectionDate: Int?
        set(value): Unit = set(70, value)
        get(): Int? = get(70) as Int?

    open var challengeEndDate: Int?
        set(value): Unit = set(71, value)
        get(): Int? = get(71) as Int?

    open var challengRent: Int?
        set(value): Unit = set(72, value)
        get(): Int? = get(72) as Int?

    open var tenancyOneYearOverSign: Byte?
        set(value): Unit = set(73, value)
        get(): Byte? = get(73) as Byte?

    open var recruitmentRent: Int?
        set(value): Unit = set(74, value)
        get(): Int? = get(74) as Int?

    /**
     * Create a detached, initialised LatestRentEvaluationRecord
     */
    constructor(creationMonth: Int? = null, creationTime: Int? = null, updateMonth: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, assessmentReviewNumber: String, latestRentAssessmentHistory: String, buildingCode: String? = null, propertyCode: String? = null, roomParkingDivision: String? = null, taxDivision: String? = null, keyMoneyMonths: BigDecimal? = null, depositMonths: BigDecimal? = null, depreciationMonths: BigDecimal? = null, keyMoneyAmount: Int? = null, keyMoneyInoutDivision: String? = null, additionalKeyMoneyAmount: Int? = null, depositAmount: Int? = null, depreciation: Int? = null, rent: Int? = null, rentInoutDivision: String? = null, parkingFeeCombinedSign: Byte? = null, parkingFee: Int? = null, parkingFeeInoutDivision: String? = null, commonFee: Int? = null, commonFeeInoutDivision: String? = null, neighborhoodAssociationFee: Int? = null, newRentAdded: Int? = null, standardRentForCoop: Int? = null, initialSetRent: Int? = null, managementFee: Int? = null, managementFeeInoutDivision: String? = null, managementFeeDiscountAmount: Int? = null, coopFee: Int? = null, coopBenefitAmount: Int? = null, mngContractInitialPayment: Int? = null, mngContractInitialPaymentInout: String? = null, tenantRegistrationFee: Int? = null, tenantRegistrationFeeInout: String? = null, maintenanceFee: Int? = null, maintenanceFeeInoutDivision: String? = null, neighborhoodFeePaidByDaito: Int? = null, guaranteeDivision: String? = null, coopMembershipFee: Int? = null, waterManagementFee: Int? = null, latestRentAssessmentApprover: String? = null, latestRentAssessmentDate: Int? = null, brokerApplicationClearSign: Byte? = null, brokerApplicationOutputDate: Int? = null, outputTaxRateDivision: String? = null, buildingUnitOutputCount: Byte? = null, roomUnitOutputCount: Byte? = null, brokerApplicationCollectionDivision: String? = null, brokerApplicationOutputUnitDivision: String? = null, multipleOutputNumber: Short? = null, brokerApplicationFormNumber: String? = null, brokerApplicationApprovalDate: Int? = null, brokerApplicationCollectionDate: Int? = null, initialCollectionEntryDate: Int? = null, brokerApplicationResponsibleBranch: String? = null, brokerApplicationResponsiblePerson: String? = null, roomCode: String? = null, dataMigrationOriginKey1: String? = null, dataMigrationOriginKey2: String? = null, transmissionDivision: String? = null, contractRent: Int? = null, nonStandardDivision: Byte? = null, collectionResponsiblePersonCode: String? = null, recruitmentStartDate: Int? = null, postVacancyCollectionDate: Int? = null, challengeEndDate: Int? = null, challengRent: Int? = null, tenancyOneYearOverSign: Byte? = null, recruitmentRent: Int? = null): this() {
        this.creationMonth = creationMonth
        this.creationTime = creationTime
        this.updateMonth = updateMonth
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.assessmentReviewNumber = assessmentReviewNumber
        this.latestRentAssessmentHistory = latestRentAssessmentHistory
        this.buildingCode = buildingCode
        this.propertyCode = propertyCode
        this.roomParkingDivision = roomParkingDivision
        this.taxDivision = taxDivision
        this.keyMoneyMonths = keyMoneyMonths
        this.depositMonths = depositMonths
        this.depreciationMonths = depreciationMonths
        this.keyMoneyAmount = keyMoneyAmount
        this.keyMoneyInoutDivision = keyMoneyInoutDivision
        this.additionalKeyMoneyAmount = additionalKeyMoneyAmount
        this.depositAmount = depositAmount
        this.depreciation = depreciation
        this.rent = rent
        this.rentInoutDivision = rentInoutDivision
        this.parkingFeeCombinedSign = parkingFeeCombinedSign
        this.parkingFee = parkingFee
        this.parkingFeeInoutDivision = parkingFeeInoutDivision
        this.commonFee = commonFee
        this.commonFeeInoutDivision = commonFeeInoutDivision
        this.neighborhoodAssociationFee = neighborhoodAssociationFee
        this.newRentAdded = newRentAdded
        this.standardRentForCoop = standardRentForCoop
        this.initialSetRent = initialSetRent
        this.managementFee = managementFee
        this.managementFeeInoutDivision = managementFeeInoutDivision
        this.managementFeeDiscountAmount = managementFeeDiscountAmount
        this.coopFee = coopFee
        this.coopBenefitAmount = coopBenefitAmount
        this.mngContractInitialPayment = mngContractInitialPayment
        this.mngContractInitialPaymentInout = mngContractInitialPaymentInout
        this.tenantRegistrationFee = tenantRegistrationFee
        this.tenantRegistrationFeeInout = tenantRegistrationFeeInout
        this.maintenanceFee = maintenanceFee
        this.maintenanceFeeInoutDivision = maintenanceFeeInoutDivision
        this.neighborhoodFeePaidByDaito = neighborhoodFeePaidByDaito
        this.guaranteeDivision = guaranteeDivision
        this.coopMembershipFee = coopMembershipFee
        this.waterManagementFee = waterManagementFee
        this.latestRentAssessmentApprover = latestRentAssessmentApprover
        this.latestRentAssessmentDate = latestRentAssessmentDate
        this.brokerApplicationClearSign = brokerApplicationClearSign
        this.brokerApplicationOutputDate = brokerApplicationOutputDate
        this.outputTaxRateDivision = outputTaxRateDivision
        this.buildingUnitOutputCount = buildingUnitOutputCount
        this.roomUnitOutputCount = roomUnitOutputCount
        this.brokerApplicationCollectionDivision = brokerApplicationCollectionDivision
        this.brokerApplicationOutputUnitDivision = brokerApplicationOutputUnitDivision
        this.multipleOutputNumber = multipleOutputNumber
        this.brokerApplicationFormNumber = brokerApplicationFormNumber
        this.brokerApplicationApprovalDate = brokerApplicationApprovalDate
        this.brokerApplicationCollectionDate = brokerApplicationCollectionDate
        this.initialCollectionEntryDate = initialCollectionEntryDate
        this.brokerApplicationResponsibleBranch = brokerApplicationResponsibleBranch
        this.brokerApplicationResponsiblePerson = brokerApplicationResponsiblePerson
        this.roomCode = roomCode
        this.dataMigrationOriginKey1 = dataMigrationOriginKey1
        this.dataMigrationOriginKey2 = dataMigrationOriginKey2
        this.transmissionDivision = transmissionDivision
        this.contractRent = contractRent
        this.nonStandardDivision = nonStandardDivision
        this.collectionResponsiblePersonCode = collectionResponsiblePersonCode
        this.recruitmentStartDate = recruitmentStartDate
        this.postVacancyCollectionDate = postVacancyCollectionDate
        this.challengeEndDate = challengeEndDate
        this.challengRent = challengRent
        this.tenancyOneYearOverSign = tenancyOneYearOverSign
        this.recruitmentRent = recruitmentRent
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised LatestRentEvaluationRecord
     */
    constructor(value: LatestRentEvaluationPojo?): this() {
        if (value != null) {
            this.creationMonth = value.creationMonth
            this.creationTime = value.creationTime
            this.updateMonth = value.updateMonth
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.assessmentReviewNumber = value.assessmentReviewNumber
            this.latestRentAssessmentHistory = value.latestRentAssessmentHistory
            this.buildingCode = value.buildingCode
            this.propertyCode = value.propertyCode
            this.roomParkingDivision = value.roomParkingDivision
            this.taxDivision = value.taxDivision
            this.keyMoneyMonths = value.keyMoneyMonths
            this.depositMonths = value.depositMonths
            this.depreciationMonths = value.depreciationMonths
            this.keyMoneyAmount = value.keyMoneyAmount
            this.keyMoneyInoutDivision = value.keyMoneyInoutDivision
            this.additionalKeyMoneyAmount = value.additionalKeyMoneyAmount
            this.depositAmount = value.depositAmount
            this.depreciation = value.depreciation
            this.rent = value.rent
            this.rentInoutDivision = value.rentInoutDivision
            this.parkingFeeCombinedSign = value.parkingFeeCombinedSign
            this.parkingFee = value.parkingFee
            this.parkingFeeInoutDivision = value.parkingFeeInoutDivision
            this.commonFee = value.commonFee
            this.commonFeeInoutDivision = value.commonFeeInoutDivision
            this.neighborhoodAssociationFee = value.neighborhoodAssociationFee
            this.newRentAdded = value.newRentAdded
            this.standardRentForCoop = value.standardRentForCoop
            this.initialSetRent = value.initialSetRent
            this.managementFee = value.managementFee
            this.managementFeeInoutDivision = value.managementFeeInoutDivision
            this.managementFeeDiscountAmount = value.managementFeeDiscountAmount
            this.coopFee = value.coopFee
            this.coopBenefitAmount = value.coopBenefitAmount
            this.mngContractInitialPayment = value.mngContractInitialPayment
            this.mngContractInitialPaymentInout = value.mngContractInitialPaymentInout
            this.tenantRegistrationFee = value.tenantRegistrationFee
            this.tenantRegistrationFeeInout = value.tenantRegistrationFeeInout
            this.maintenanceFee = value.maintenanceFee
            this.maintenanceFeeInoutDivision = value.maintenanceFeeInoutDivision
            this.neighborhoodFeePaidByDaito = value.neighborhoodFeePaidByDaito
            this.guaranteeDivision = value.guaranteeDivision
            this.coopMembershipFee = value.coopMembershipFee
            this.waterManagementFee = value.waterManagementFee
            this.latestRentAssessmentApprover = value.latestRentAssessmentApprover
            this.latestRentAssessmentDate = value.latestRentAssessmentDate
            this.brokerApplicationClearSign = value.brokerApplicationClearSign
            this.brokerApplicationOutputDate = value.brokerApplicationOutputDate
            this.outputTaxRateDivision = value.outputTaxRateDivision
            this.buildingUnitOutputCount = value.buildingUnitOutputCount
            this.roomUnitOutputCount = value.roomUnitOutputCount
            this.brokerApplicationCollectionDivision = value.brokerApplicationCollectionDivision
            this.brokerApplicationOutputUnitDivision = value.brokerApplicationOutputUnitDivision
            this.multipleOutputNumber = value.multipleOutputNumber
            this.brokerApplicationFormNumber = value.brokerApplicationFormNumber
            this.brokerApplicationApprovalDate = value.brokerApplicationApprovalDate
            this.brokerApplicationCollectionDate = value.brokerApplicationCollectionDate
            this.initialCollectionEntryDate = value.initialCollectionEntryDate
            this.brokerApplicationResponsibleBranch = value.brokerApplicationResponsibleBranch
            this.brokerApplicationResponsiblePerson = value.brokerApplicationResponsiblePerson
            this.roomCode = value.roomCode
            this.dataMigrationOriginKey1 = value.dataMigrationOriginKey1
            this.dataMigrationOriginKey2 = value.dataMigrationOriginKey2
            this.transmissionDivision = value.transmissionDivision
            this.contractRent = value.contractRent
            this.nonStandardDivision = value.nonStandardDivision
            this.collectionResponsiblePersonCode = value.collectionResponsiblePersonCode
            this.recruitmentStartDate = value.recruitmentStartDate
            this.postVacancyCollectionDate = value.postVacancyCollectionDate
            this.challengeEndDate = value.challengeEndDate
            this.challengRent = value.challengRent
            this.tenancyOneYearOverSign = value.tenancyOneYearOverSign
            this.recruitmentRent = value.recruitmentRent
            resetChangedOnNotNull()
        }
    }
}
