package jp.ne.simplex.application.repository.external.dkportal.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.model.ExclusiveProperty
import jp.ne.simplex.application.model.ExclusivePropertyAction
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalOperationName
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalRequest

@JsonIgnoreProperties(value = ["ecode"])
abstract class DKPortalUpdateExclusiveRequest(
    @field:JsonProperty("dk_link_id")
    val dkLinkId: String,

    @field:JsonProperty("exclusive_from")
    val exclusiveFrom: String,

    @field:JsonProperty("exclusive_to")
    val exclusiveTo: String,

    @field:JsonProperty("e_code")
    val eCode: String?,

    @field:JsonProperty("company_type")
    val companyType: Int,
) : DKPortalRequest {
    abstract override fun getDKPortalOperationName(): DKPortalOperationName

    companion object {

        fun of(
            property: Property,
            exclusiveId: ExclusiveProperty.Id,
            record: ExclusivePropertyAction.Record
        ): DKPortalUpdateExclusiveRequest {
            return when (property.getType()) {
                Property.Type.RESIDENTIAL -> {
                    DKPortalUpdateExclusiveHousingRequest(exclusiveId, record)
                }

                Property.Type.COMMERCIAL -> {
                    DKPortalUpdateExclusiveBusinessRequest(exclusiveId, record)
                }
            }
        }

    }
}
