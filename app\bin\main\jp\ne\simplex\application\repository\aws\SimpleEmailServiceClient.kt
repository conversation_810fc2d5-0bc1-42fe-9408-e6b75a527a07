package jp.ne.simplex.application.repository.aws

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository
import software.amazon.awssdk.services.ses.SesClient
import software.amazon.awssdk.services.ses.model.Destination
import software.amazon.awssdk.services.ses.model.Message
import software.amazon.awssdk.services.ses.model.SendEmailRequest

@Repository
class SimpleEmailServiceClient(
    private val client: SesClient
) {

    companion object {
        private val log = LoggerFactory.getLogger(SimpleEmailServiceClient::class.java)
    }

    fun send(destination: Destination, msg: Message, sender: String) {
        try {
            val request = SendEmailRequest.builder()
                .destination(destination)
                .message(msg)
                .source(sender)
                .build()

            client.sendEmail(request)
        } catch (e: Exception) {
            log.warn("メールの送信に失敗しました: ${e.message}")
            throw e
        }
    }
}

