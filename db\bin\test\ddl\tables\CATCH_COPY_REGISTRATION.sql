-- TABLE: CATCH_COPY_REGISTRATION(キャッチコピー登録)

CREATE TABLE CATCH_COPY_REGISTRATION(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    BUILDING_CD                                  varchar(9)        NOT NULL    
,    CATCH_COPY_REGISTRANT                        varchar(42)                   
,    CATCH_COPY                                   varchar(402)                  
,    CONSTRAINT PK_CATCH_COPY_REGISTRATION PRIMARY KEY (BUILDING_CD)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE CATCH_COPY_REGISTRATION IS 'キャッチコピー登録 既存システム物理名: EMEKCP';
COMMENT ON COLUMN CATCH_COPY_REGISTRATION.CREATION_DATE IS '作成年月日 既存システム物理名: EMEK1D';
COMMENT ON COLUMN CATCH_COPY_REGISTRATION.CREATION_TIME IS '作成時刻 既存システム物理名: EMEK2T';
COMMENT ON COLUMN CATCH_COPY_REGISTRATION.CREATOR IS '作成者 既存システム物理名: EMEK3C';
COMMENT ON COLUMN CATCH_COPY_REGISTRATION.UPDATE_DATE IS '更新年月日 既存システム物理名: EMEK4D';
COMMENT ON COLUMN CATCH_COPY_REGISTRATION.UPDATE_TIME IS '更新時刻 既存システム物理名: EMEK5T';
COMMENT ON COLUMN CATCH_COPY_REGISTRATION.UPDATER IS '更新者 既存システム物理名: EMEK6C';
COMMENT ON COLUMN CATCH_COPY_REGISTRATION.BUILDING_CD IS '建物CD 既存システム物理名: EMEK7N';
COMMENT ON COLUMN CATCH_COPY_REGISTRATION.CATCH_COPY_REGISTRANT IS 'キャッチ登録者 既存システム物理名: EMEK8M';
COMMENT ON COLUMN CATCH_COPY_REGISTRATION.CATCH_COPY IS 'キャッチコピー 既存システム物理名: EMEK9M';
