truncate table LEASING_STORE_TABLE;
insert into LEASING_STORE_TABLE (CREATION_DATE, CREATION_TIME, CREATOR, UPDATE_DATE, UPDATE_TIME, UPDATER, <PERSON>ANCH_CD, BRANCH_NAME, LEASING_STORE_CD, LEASING_STORE_NAME, START_DATE, END_DATE, LEASING_STORE_COMPANY_NAME, LEASING_STORE_PHONE_NUMBER, STORE_NORTH_ORDER) values
 (20181225, 0, '0', 20181225, 0, '0', '738', '品川', '201', '渋谷店', 20181225, 99991231, null, '00-0000-0000', 245)
,(20181130, 0, '0', 20181130, 0, '0', '735', '板橋', '202', '新宿店', 20181130, 99991231, null, '00-0000-0000', 244)
,(20180928, 0, '0', 20240408, 0, '0', '101', '日立', '203', '高萩駅前店', 20180928, 20240407, null, '00-0000-0000', 243)
,(20180928, 0, '0', 20180928, 0, '0', '069', '富山', '204', '魚津店', 20180928, 99991231, null, '00-0000-0000', 242)
,(20180928, 0, '0', 20180928, 0, '0', '045', '札幌', '205', '麻生店', 20180928, 99991231, null, '00-0000-0000', 241)
,(20180730, 0, '0', 20180730, 0, '0', '070', '高松', '206', '高松国道11号店', 20180730, 99991231, null, '00-0000-0000', 240)
,(20180629, 0, '0', 20180629, 0, '0', '039', '所沢', '207', '所沢店', 20180629, 99991231, null, '00-0000-0000', 239)
,(20180131, 0, '000000', 20180131, 0, '000000', '715', '上尾', '208', '桶川店', 20180131, 99991231, '大東リーシング社', '0224-55-0080', 238)
,(20171220, 0, '0', 20171220, 0, '0', '615', '津', '209', '津店', 20171220, 99991231, null, '00-0000-0000', 236)
,(20180124, 0, '000000', 20180124, 0, '000000', '460', '仙台南', '210', '柴田店', 20180124, 99991231, '大東リーシング社', '0224-55-0080', 237)
,(20171026, 0, '000000', 20171026, 0, '000000', '101', '日立', '211', '日立南店', 20171023, 99991231, '大東リーシング社', '0294-38-9820', 234)
,(20171026, 0, '000000', 20171026, 0, '000000', '159', '春日井', '212', '春日井店', 20171107, 99991231, '大東リーシング社', '0568-85-0612', 235)
,(20170421, 0, '0', 20170421, 0, '0', '030', '水戸', '213', '水戸インター店', 20170401, 99991231, null, '00-0000-0000', 51)
,(20170421, 0, '0', 20170421, 0, '0', '737', '旭川', '214', '旭川店', 20170401, 99991231, null, '00-0000-0000', 1)
,(20170421, 0, '0', 20170421, 0, '0', '094', '福井', '215', '越前店', 20170401, 99991231, null, '00-0000-0000', 16)
,(20170421, 0, '0', 20170421, 0, '0', '045', '札幌', '216', '白石駅前店', 20170401, 99991231, null, '00-0000-0000', 2)
,(20170421, 0, '0', 20170421, 0, '0', '439', '千歳', '217', '千歳店', 20170401, 99991231, null, '00-0000-0000', 3)
,(20170421, 0, '0', 20170421, 0, '0', '181', '徳山', '218', '岩国店', 20170401, 99991231, null, '00-0000-0000', 199)
,(20170421, 0, '0', 20170421, 0, '0', '439', '千歳', '219', '苫小牧店', 20170401, 99991231, null, '00-0000-0000', 4)
,(20170421, 0, '0', 20170421, 0, '0', '439', '苫小牧千歳', '220', '函館店', 20170401, 99991231, null, '00-0000-0000', 17)
,(20170421, 0, '0', 20170421, 0, '0', '074', '青森', '221', '青森店', 20170401, 99991231, null, '00-0000-0000', 18)
,(20170421, 0, '0', 20170421, 0, '0', '443', '八戸', '222', '八戸店', 20170401, 99991231, null, '00-0000-0000', 19)
,(20170421, 0, '0', 20170421, 0, '0', '075', '秋田', '223', '秋田店', 20170401, 99991231, null, '00-0000-0000', 20)
,(20170421, 0, '0', 20170421, 0, '0', '075', '秋田', '224', '横手店', 20170401, 99991231, null, '00-0000-0000', 21)
,(20170421, 0, '0', 20170421, 0, '0', '168', '盛岡', '225', '盛岡店', 20170401, 99991231, null, '00-0000-0000', 22)
,(20170421, 0, '0', 20170421, 0, '0', '168', '盛岡', '226', '北上駅前店', 20170401, 99991231, null, '00-0000-0000', 23)
,(20170421, 0, '0', 20170421, 0, '0', '041', '仙台北', '227', '仙台北店', 20170401, 99991231, null, '00-0000-0000', 24)
,(20170421, 0, '0', 20170421, 0, '0', '041', '仙台北', '228', '古川店', 20170401, 99991231, null, '00-0000-0000', 25)
,(20170421, 0, '0', 20170421, 0, '0', '040', '仙台', '229', '仙台駅東口店', 20170401, 99991231, null, '00-0000-0000', 26)
,(20170421, 0, '0', 20170421, 0, '0', '040', '仙台', '230', '中野栄店', 20170401, 99991231, null, '00-0000-0000', 27)
,(20170421, 0, '0', 20170421, 0, '0', '460', '仙台南', '231', '仙台南店', 20170401, 99991231, null, '00-0000-0000', 28)
,(20170421, 0, '0', 20170421, 0, '0', '460', '仙台南', '232', '岩沼店', 20170401, 99991231, null, '00-0000-0000', 29)
,(20170421, 0, '0', 20170421, 0, '0', '460', '仙台南', '233', '相馬店', 20170401, 99991231, null, '00-0000-0000', 30)
,(20170421, 0, '0', 20170421, 0, '0', '143', '庄内', '234', '庄内店', 20170401, 99991231, null, '00-0000-0000', 31)
,(20170421, 0, '0', 20170421, 0, '0', '113', '山形', '235', '山形嶋店', 20170401, 99991231, null, '00-0000-0000', 32)
,(20170421, 0, '0', 20170421, 0, '0', '113', '山形', '236', '米沢店', 20170401, 99991231, null, '00-0000-0000', 33)
,(20170421, 0, '0', 20170421, 0, '0', '119', '福島', '237', '福島店', 20170401, 99991231, null, '00-0000-0000', 34)
,(20170421, 0, '0', 20170421, 0, '0', '038', '郡山', '238', '会津駅前店', 20170401, 99991231, null, '00-0000-0000', 35)
,(20170421, 0, '0', 20170421, 0, '0', '038', '郡山', '239', '郡山店', 20170401, 99991231, null, '00-0000-0000', 36)
,(20170421, 0, '0', 20170421, 0, '0', '038', '郡山', '240', '新白河店', 20170401, 99991231, null, '00-0000-0000', 37)
,(20170421, 0, '0', 20170421, 0, '0', '114', 'いわき', '241', 'いわき店', 20170401, 99991231, null, '00-0000-0000', 38)
,(20170421, 0, '0', 20170421, 0, '0', '033', '宇都宮', '242', '宇都宮北店', 20170401, 99991231, null, '00-0000-0000', 39)
,(20170421, 0, '0', 20170421, 0, '0', '033', '宇都宮', '243', '北栃木店', 20170401, 99991231, null, '00-0000-0000', 40)
,(20170421, 0, '0', 20170421, 0, '0', '033', '宇都宮', '244', '宇都宮店', 20170401, 99991231, null, '00-0000-0000', 41)
,(20170421, 0, '0', 20170421, 0, '0', '033', '宇都宮', '245', '真岡店', 20170401, 99991231, null, '00-0000-0000', 42)
,(20170421, 0, '0', 20170421, 0, '0', '601', '南栃木', '246', '足利店', 20170401, 99991231, null, '00-0000-0000', 43)
,(20170421, 0, '0', 20170421, 0, '0', '013', '太田', '247', '佐野店', 20170401, 99991231, null, '00-0000-0000', 44)
,(20170421, 0, '0', 20170421, 0, '0', '042', '小山', '248', '小山店', 20170401, 99991231, null, '00-0000-0000', 45)
,(20170421, 0, '0', 20170421, 0, '0', '042', '小山', '249', '下館店', 20170401, 99991231, null, '00-0000-0000', 46)
,(20170421, 0, '0', 20170421, 0, '0', '013', '太田', '250', '太田店', 20170401, 99991231, null, '00-0000-0000', 56)
,(20170421, 0, '0', 20170421, 0, '0', '013', '太田', '251', '桐生店', 20170401, 99991231, null, '00-0000-0000', 57)
,(20170421, 0, '0', 20170421, 0, '0', '122', '前橋', '252', '前橋店', 20170401, 99991231, null, '00-0000-0000', 58)
,(20170421, 0, '0', 20170421, 0, '0', '122', '前橋', '253', '伊勢崎店', 20170401, 99991231, null, '00-0000-0000', 59)
,(20170421, 0, '0', 20170421, 0, '0', '035', '高崎', '254', '高崎店', 20170401, 99991231, null, '00-0000-0000', 60)
,(20170421, 0, '0', 20170421, 0, '0', '027', '熊谷', '255', '熊谷店', 20170401, 99991231, null, '00-0000-0000', 52)
,(20170421, 0, '0', 20170421, 0, '0', '027', '熊谷', '256', '深谷店', 20170401, 99991231, null, '00-0000-0000', 53)
,(20170421, 0, '0', 20170421, 0, '0', '102', '埼玉北', '257', '久喜店', 20170401, 99991231, null, '00-0000-0000', 54)
,(20170421, 0, '0', 20170421, 0, '0', '102', '埼玉北', '258', '古河店', 20170401, 99991231, null, '00-0000-0000', 55)
,(20170421, 0, '0', 20170421, 0, '0', '037', '春日部', '259', '春日部店', 20170401, 99991231, null, '00-0000-0000', 87)
,(20170421, 0, '0', 20170421, 0, '0', '044', '川越', '260', '川越店', 20170401, 99991231, null, '00-0000-0000', 61)
,(20170421, 0, '0', 20170421, 0, '0', '044', '川越', '261', '坂戸店', 20170401, 99991231, null, '00-0000-0000', 62)
,(20170421, 0, '0', 20170421, 0, '0', '716', '狭山', '262', '狭山店', 20170401, 99991231, null, '00-0000-0000', 63)
,(20170421, 0, '0', 20170421, 0, '0', '028', 'さいたま', '263', 'さいたま店', 20170401, 99991231, null, '00-0000-0000', 64)
,(20170421, 0, '0', 20170421, 0, '0', '603', '川口', '264', '川口駅前店', 20170401, 99991231, null, '00-0000-0000', 89)
,(20170421, 0, '0', 20170421, 0, '0', '101', '日立', '265', '日立駅前店', 20170401, 99991231, null, '00-0000-0000', 47)
,(20170421, 0, '0', 20170421, 0, '0', '030', '水戸', '266', '水戸店', 20170401, 99991231, null, '00-0000-0000', 48)
,(20170421, 0, '0', 20170421, 0, '0', '030', '水戸', '267', '石岡駅前店', 20170401, 99991231, null, '00-0000-0000', 49)
,(20170421, 0, '0', 20170421, 0, '0', '030', '水戸', '268', 'ひたちなか店', 20170401, 99991231, null, '00-0000-0000', 50)
,(20170421, 0, '0', 20170421, 0, '0', '043', 'つくば', '269', 'つくば店', 20170401, 99991231, null, '00-0000-0000', 71)
,(20170421, 0, '0', 20170421, 0, '0', '043', 'つくば', '270', '牛久店', 20170401, 99991231, null, '00-0000-0000', 72)
,(20170421, 0, '0', 20170421, 0, '0', '043', 'つくば', '271', '土浦店', 20170401, 99991231, null, '00-0000-0000', 73)
,(20170421, 0, '0', 20170421, 0, '0', '130', '守谷', '272', '守谷店', 20170401, 99991231, null, '00-0000-0000', 74)
,(20170421, 0, '0', 20170421, 0, '0', '036', '柏', '273', '柏店', 20170401, 99991231, null, '00-0000-0000', 75)
,(20170421, 0, '0', 20170421, 0, '0', '019', '成田', '274', '成田店', 20170401, 99991231, null, '00-0000-0000', 76)
,(20170421, 0, '0', 20170421, 0, '0', '019', '成田', '275', '八街店', 20170401, 20210501, null, '00-0000-0000', 77)
,(20170421, 0, '0', 20170421, 0, '0', '019', '成田', '276', '神栖店', 20170401, 99991231, null, '00-0000-0000', 78)
,(20170421, 0, '0', 20170421, 0, '0', '034', '千葉', '277', '千葉店', 20170401, 99991231, null, '00-0000-0000', 79)
,(20170421, 0, '0', 20170421, 0, '0', '034', '千葉', '278', '四街道駅前店', 20170401, 99991231, null, '00-0000-0000', 80)
,(20170421, 0, '0', 20170421, 0, '0', '022', '千葉南', '279', '市原店', 20170401, 99991231, null, '00-0000-0000', 81)
,(20170421, 0, '0', 20170421, 0, '0', '022', '千葉南', '280', '茂原店', 20170401, 99991231, null, '00-0000-0000', 82)
,(20170421, 0, '0', 20170421, 0, '0', '022', '千葉南', '281', '木更津店', 20170401, 99991231, null, '00-0000-0000', 83)
,(20170421, 0, '0', 20170421, 0, '0', '766', '八千代', '282', '八千代中央店', 20170401, 99991231, null, '00-0000-0000', 84)
,(20170421, 0, '0', 20170421, 0, '0', '698', '松戸', '283', '松戸駅前店', 20170401, 99991231, null, '00-0000-0000', 85)
,(20170421, 0, '0', 20170421, 0, '0', '029', '船橋', '284', '船橋店', 20170401, 99991231, null, '00-0000-0000', 86)
,(20170421, 0, '0', 20170421, 0, '0', '128', '越谷', '285', '越谷店', 20170401, 99991231, null, '00-0000-0000', 88)
,(20170421, 0, '0', 20170421, 0, '0', '020', '新潟', '286', '新潟店', 20170401, 99991231, null, '00-0000-0000', 65)
,(20170421, 0, '0', 20170421, 0, '0', '020', '新潟', '287', '新発田店', 20170401, 99991231, null, '00-0000-0000', 66)
,(20170421, 0, '0', 20170421, 0, '0', '020', '新潟', '288', '燕三条店', 20170401, 99991231, null, '00-0000-0000', 67)
,(20170421, 0, '0', 20170421, 0, '0', '714', '長岡', '289', '長岡店', 20170401, 99991231, null, '00-0000-0000', 68)
,(20170421, 0, '0', 20170421, 0, '0', '714', '長岡', '290', '柏崎店', 20170401, 99991231, null, '00-0000-0000', 69)
,(20170421, 0, '0', 20170421, 0, '0', '132', '上越', '291', '上越店', 20170401, 99991231, null, '00-0000-0000', 70)
,(20170421, 0, '0', 20170421, 0, '0', '078', '長野', '292', '長野店', 20170401, 99991231, null, '00-0000-0000', 5)
,(20170421, 0, '0', 20170421, 0, '0', '410', '上田', '293', '上田店', 20170401, 99991231, null, '00-0000-0000', 6)
,(20170421, 0, '0', 20170421, 0, '0', '410', '上田', '294', '佐久店', 20170401, 99991231, null, '00-0000-0000', 7)
,(20170421, 0, '0', 20170421, 0, '0', '079', '松本', '295', '松本伊勢町通り店', 20170401, 99991231, null, '00-0000-0000', 8)
,(20170421, 0, '0', 20170421, 0, '0', '069', '富山', '296', '富山中央店', 20170401, 99991231, null, '00-0000-0000', 9)
,(20170421, 0, '0', 20170421, 0, '0', '069', '富山', '297', '高岡店', 20170401, 99991231, null, '00-0000-0000', 10)
,(20170421, 0, '0', 20170421, 0, '0', '021', '金沢', '298', '金沢店', 20170401, 99991231, null, '00-0000-0000', 11)
,(20170421, 0, '0', 20170421, 0, '0', '021', '金沢', '299', '七尾店', 20170401, 99991231, null, '00-0000-0000', 12)
,(20170421, 0, '0', 20170421, 0, '0', '021', '金沢', '301', '野々市店', 20170401, 99991231, null, '00-0000-0000', 13)
;
