/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.RegionMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.RegionMasterPojo

import org.jooq.Record3
import org.jooq.impl.UpdatableRecordImpl


/**
 * 地域マスタ 既存システム物理名: JXH1MP
 */
@Suppress("UNCHECKED_CAST")
open class RegionMasterRecord private constructor() : UpdatableRecordImpl<RegionMasterRecord>(RegionMasterTable.REGION_MASTER) {

    open var groupCode: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var departmentCode: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var useStartDate: Int
        set(value): Unit = set(2, value)
        get(): Int = get(2) as Int

    open var useFinishDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var regionCode_1: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var regionName_1: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var regionOrder_1: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var regionCode_2: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var regionName_2: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var regionOrder_2: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var regionCode_3: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var regionName_3: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var regionOrder_3: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var regionCode_4: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var regionName_4: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var regionOrder_4: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var regionCode_5: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var regionName_5: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var regionOrder_5: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var salesDepartmentSuperior: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    @Suppress("INAPPLICABLE_JVM_NAME")
    @set:JvmName("setIsDeleted")
    open var isDeleted: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var createUser: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var createProgramId: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var createDate: Int?
        set(value): Unit = set(23, value)
        get(): Int? = get(23) as Int?

    open var createTime: Int?
        set(value): Unit = set(24, value)
        get(): Int? = get(24) as Int?

    open var updateUser: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var updateProgramId: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var updateDate: Int?
        set(value): Unit = set(27, value)
        get(): Int? = get(27) as Int?

    open var updateTime: Int?
        set(value): Unit = set(28, value)
        get(): Int? = get(28) as Int?

    open var deviceId: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record3<String?, String?, Int?> = super.key() as Record3<String?, String?, Int?>

    /**
     * Create a detached, initialised RegionMasterRecord
     */
    constructor(groupCode: String, departmentCode: String, useStartDate: Int, useFinishDate: Int? = null, regionCode_1: String? = null, regionName_1: String? = null, regionOrder_1: String? = null, regionCode_2: String? = null, regionName_2: String? = null, regionOrder_2: String? = null, regionCode_3: String? = null, regionName_3: String? = null, regionOrder_3: String? = null, regionCode_4: String? = null, regionName_4: String? = null, regionOrder_4: String? = null, regionCode_5: String? = null, regionName_5: String? = null, regionOrder_5: String? = null, salesDepartmentSuperior: String? = null, isDeleted: String? = null, createUser: String? = null, createProgramId: String? = null, createDate: Int? = null, createTime: Int? = null, updateUser: String? = null, updateProgramId: String? = null, updateDate: Int? = null, updateTime: Int? = null, deviceId: String? = null): this() {
        this.groupCode = groupCode
        this.departmentCode = departmentCode
        this.useStartDate = useStartDate
        this.useFinishDate = useFinishDate
        this.regionCode_1 = regionCode_1
        this.regionName_1 = regionName_1
        this.regionOrder_1 = regionOrder_1
        this.regionCode_2 = regionCode_2
        this.regionName_2 = regionName_2
        this.regionOrder_2 = regionOrder_2
        this.regionCode_3 = regionCode_3
        this.regionName_3 = regionName_3
        this.regionOrder_3 = regionOrder_3
        this.regionCode_4 = regionCode_4
        this.regionName_4 = regionName_4
        this.regionOrder_4 = regionOrder_4
        this.regionCode_5 = regionCode_5
        this.regionName_5 = regionName_5
        this.regionOrder_5 = regionOrder_5
        this.salesDepartmentSuperior = salesDepartmentSuperior
        this.isDeleted = isDeleted
        this.createUser = createUser
        this.createProgramId = createProgramId
        this.createDate = createDate
        this.createTime = createTime
        this.updateUser = updateUser
        this.updateProgramId = updateProgramId
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.deviceId = deviceId
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised RegionMasterRecord
     */
    constructor(value: RegionMasterPojo?): this() {
        if (value != null) {
            this.groupCode = value.groupCode
            this.departmentCode = value.departmentCode
            this.useStartDate = value.useStartDate
            this.useFinishDate = value.useFinishDate
            this.regionCode_1 = value.regionCode_1
            this.regionName_1 = value.regionName_1
            this.regionOrder_1 = value.regionOrder_1
            this.regionCode_2 = value.regionCode_2
            this.regionName_2 = value.regionName_2
            this.regionOrder_2 = value.regionOrder_2
            this.regionCode_3 = value.regionCode_3
            this.regionName_3 = value.regionName_3
            this.regionOrder_3 = value.regionOrder_3
            this.regionCode_4 = value.regionCode_4
            this.regionName_4 = value.regionName_4
            this.regionOrder_4 = value.regionOrder_4
            this.regionCode_5 = value.regionCode_5
            this.regionName_5 = value.regionName_5
            this.regionOrder_5 = value.regionOrder_5
            this.salesDepartmentSuperior = value.salesDepartmentSuperior
            this.isDeleted = value.isDeleted
            this.createUser = value.createUser
            this.createProgramId = value.createProgramId
            this.createDate = value.createDate
            this.createTime = value.createTime
            this.updateUser = value.updateUser
            this.updateProgramId = value.updateProgramId
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.deviceId = value.deviceId
            resetChangedOnNotNull()
        }
    }
}
