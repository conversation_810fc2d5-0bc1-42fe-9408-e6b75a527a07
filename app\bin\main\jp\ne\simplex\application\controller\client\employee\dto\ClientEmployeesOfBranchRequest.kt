package jp.ne.simplex.application.controller.client.employee.dto

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.controller.client.shared.ClientBranchDto
import jp.ne.simplex.application.model.Branch

data class ClientEmployeesOfBranchRequest(
    @JsonProperty("branch")
    @field:Schema(description = "支店情報")
    val branch: ClientBranchDto,
) {

    @JsonIgnore
    fun getBranch(): Branch {
        return branch.getBranch()
    }
}
