package jp.ne.simplex.application.controller.client.exclusive.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.ExclusiveProperty


class ClientExclusiveTargetDto private constructor(
    @JsonProperty("companyType")
    @field:Schema(description = "先行先種別", example = "1")
    val companyType: String,

    @JsonProperty("ecode")
    @field:Schema(description = "Eコード", example = "E12345678")
    val eCode: String?,
) {

    companion object {

        fun of(source: ExclusiveProperty.ExclusiveTarget): ClientExclusiveTargetDto {
            return ClientExclusiveTargetDto(
                companyType = source.companyType.value.toString(),
                eCode = source.eCode?.value
            )
        }
    }
}
