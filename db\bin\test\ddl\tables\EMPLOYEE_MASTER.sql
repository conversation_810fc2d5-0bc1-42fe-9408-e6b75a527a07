-- TABLE: EMPLOYEE_MASTER(社員マスタ)

CREATE TABLE EMPLOYEE_MASTER(
     <PERSON>MPLOYEE_NUMBER                              varchar(6)        NOT NULL    
,    NAME_KANJI                                   varchar(26)                   
,    NAME_KANA                                    varchar(24)                   
,    <PERSON><PERSON><PERSON>                                       varchar(1)                    
,    DATE_OF_BIRTH                                numeric(10)                   
,    JOIN_DATE                                    numeric(10)                   
,    RESIGNATION_DATE                             numeric(10)                   
,    COMPANY_CODE                                 varchar(3)                    
,    AFFILIATION_START_DATE                       numeric(10)                   
,    AFFILIATION_CODE                             varchar(6)                    
,    POSITION_CODE                                varchar(2)                    
,    JOB_TYPE_CODE                                varchar(3)                    
,    RECRUITMENT_CATEGORY                         varchar(2)                    
,    EMPLOYMENT_CATEGORY                          varchar(2)                    
,    ACCOUNTING_AFFILIATION_CODE                  varchar(6)                    
,    DEDICATED_TECHNICIAN_CATEGORY_1              varchar(1)                    
,    ASSIGNED_AFFILIATION_CODE_1                  varchar(6)                    
,    DEDICATED_TECHNICIAN_CATEGORY_2              varchar(1)                    
,    ASSIGNED_AFFILIATION_CODE_2                  varchar(6)                    
,    DEDICATED_TECHNICIAN_CATEGORY_3              varchar(1)                    
,    ASSIGNED_AFFILIATION_CODE_3                  varchar(6)                    
,    DEDICATED_TECHNICIAN_CATEGORY_4              varchar(1)                    
,    ASSIGNED_AFFILIATION_CODE_4                  varchar(6)                    
,    DEDICATED_TECHNICIAN_CATEGORY_5              varchar(1)                    
,    ASSIGNED_AFFILIATION_CODE_5                  varchar(6)                    
,    NEW_OLD_SALARY_USE_CATEGORY                  varchar(1)                    
,    NEW_OLD_SALARY_USE_DATE                      numeric(10)                   
,    POSITION_PROMOTION_DEMOTION_DATE             numeric(10)                   
,    ORGANIZATION_MANAGEMENT_TRANSFER_DATE        numeric(10)                   
,    ORGANIZATION_MANAGEMENT_POSITION_CODE        varchar(2)                    
,    ORIGINAL_AFFILIATION_START_DATE              numeric(10)                   
,    ORIGINAL_AFFILIATION_COMPANY_CODE            varchar(3)                    
,    EMPLOYMENT_CATEGORY_START_DATE               numeric(10)                   
,    DELETE_CATEGORY                              varchar(1)                    
,    CREATOR                                      varchar(6)                    
,    CREATION_PROGRAM                             varchar(10)                   
,    CREATION_DATE                                numeric(10)                   
,    CREATION_TIME                                numeric(8)                    
,    UPDATER                                      varchar(6)                    
,    UPDATE_PROGRAM                               varchar(10)                   
,    UPDATE_DATE                                  numeric(10)                   
,    UPDATE_TIME                                  numeric(8)                    
,    TERMINAL_ID                                  varchar(10)                   
,    QUALIFICATION_PROMOTION_DEMOTION_DATE        numeric(10)                   
,    QUALIFICATION_CODE                           varchar(2)                    
,    STORE_TRANSFER_DATE                          numeric(10)                   
,    SATELLITE_STORE_CODE                         varchar(5)                    
,    RESERVE_1                                    varchar(2)                    
,    RESERVE_2                                    varchar(2)                    
,    RESERVE_3                                    varchar(2)                    
,    RESERVE_4                                    varchar(3)                    
,    RESERVE_5                                    varchar(3)                    
,    RESERVE_6                                    varchar(3)                    
,    RESERVE_7                                    varchar(6)                    
,    RESERVE_8                                    varchar(6)                    
,    RESERVE_9                                    varchar(6)                    
,    RESERVE_10                                   numeric(10)                   
,    RESERVE_11                                   numeric(10)                   
,    RESERVE_12                                   numeric(10)                   
,    CONSTRAINT PK_EMPLOYEE_MASTER PRIMARY KEY (EMPLOYEE_NUMBER)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE EMPLOYEE_MASTER IS '社員マスタ 既存システム物理名: XXEMPP';
COMMENT ON COLUMN EMPLOYEE_MASTER.EMPLOYEE_NUMBER IS '社員番号 既存システム物理名: XXE10K 0:在籍、0以外:退社';
COMMENT ON COLUMN EMPLOYEE_MASTER.NAME_KANJI IS '氏名(漢字) 既存システム物理名: XXE12M 001:大東建託株式会社,018:大東建託パートナーズ株式会社,160:大東建託リーシング株式会社';
COMMENT ON COLUMN EMPLOYEE_MASTER.NAME_KANA IS '氏名(カナ) 既存システム物理名: XXE11M';
COMMENT ON COLUMN EMPLOYEE_MASTER.GENDER IS '性別 既存システム物理名: XXE13B';
COMMENT ON COLUMN EMPLOYEE_MASTER.DATE_OF_BIRTH IS '生年月日 既存システム物理名: XXE14D';
COMMENT ON COLUMN EMPLOYEE_MASTER.JOIN_DATE IS '入社年月日 既存システム物理名: XXE15D';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESIGNATION_DATE IS '退職年月日 既存システム物理名: XXE16D';
COMMENT ON COLUMN EMPLOYEE_MASTER.COMPANY_CODE IS '会社CD 既存システム物理名: XXE30C';
COMMENT ON COLUMN EMPLOYEE_MASTER.AFFILIATION_START_DATE IS '所属開始日 既存システム物理名: XXE31D';
COMMENT ON COLUMN EMPLOYEE_MASTER.AFFILIATION_CODE IS '所属CD 既存システム物理名: XXE32C';
COMMENT ON COLUMN EMPLOYEE_MASTER.POSITION_CODE IS '役職CD 既存システム物理名: XXE19C';
COMMENT ON COLUMN EMPLOYEE_MASTER.JOB_TYPE_CODE IS '職種CD 既存システム物理名: XXE33C';
COMMENT ON COLUMN EMPLOYEE_MASTER.RECRUITMENT_CATEGORY IS '採用形態区分 既存システム物理名: XXE34B';
COMMENT ON COLUMN EMPLOYEE_MASTER.EMPLOYMENT_CATEGORY IS '雇用形態区分 既存システム物理名: XXE35B';
COMMENT ON COLUMN EMPLOYEE_MASTER.ACCOUNTING_AFFILIATION_CODE IS '計上所属コード 既存システム物理名: XXE36C';
COMMENT ON COLUMN EMPLOYEE_MASTER.DEDICATED_TECHNICIAN_CATEGORY_1 IS '専任技術者区分1 既存システム物理名: XXE37B';
COMMENT ON COLUMN EMPLOYEE_MASTER.ASSIGNED_AFFILIATION_CODE_1 IS '選任所属CD1 既存システム物理名: XXE38C';
COMMENT ON COLUMN EMPLOYEE_MASTER.DEDICATED_TECHNICIAN_CATEGORY_2 IS '専任技術者区分2 既存システム物理名: XXE39B';
COMMENT ON COLUMN EMPLOYEE_MASTER.ASSIGNED_AFFILIATION_CODE_2 IS '選任所属CD2 既存システム物理名: XXE40C';
COMMENT ON COLUMN EMPLOYEE_MASTER.DEDICATED_TECHNICIAN_CATEGORY_3 IS '専任技術者区分3 既存システム物理名: XXE41B';
COMMENT ON COLUMN EMPLOYEE_MASTER.ASSIGNED_AFFILIATION_CODE_3 IS '選任所属CD3 既存システム物理名: XXE42C';
COMMENT ON COLUMN EMPLOYEE_MASTER.DEDICATED_TECHNICIAN_CATEGORY_4 IS '専任技術者区分4 既存システム物理名: XXE43B';
COMMENT ON COLUMN EMPLOYEE_MASTER.ASSIGNED_AFFILIATION_CODE_4 IS '選任所属CD4 既存システム物理名: XXE44C';
COMMENT ON COLUMN EMPLOYEE_MASTER.DEDICATED_TECHNICIAN_CATEGORY_5 IS '専任技術者区分5 既存システム物理名: XXE45B';
COMMENT ON COLUMN EMPLOYEE_MASTER.ASSIGNED_AFFILIATION_CODE_5 IS '選任所属CD5 既存システム物理名: XXE46C';
COMMENT ON COLUMN EMPLOYEE_MASTER.NEW_OLD_SALARY_USE_CATEGORY IS '新旧給与使用区分 既存システム物理名: XXE47B';
COMMENT ON COLUMN EMPLOYEE_MASTER.NEW_OLD_SALARY_USE_DATE IS '新旧給与使用年月日 既存システム物理名: XXE48D';
COMMENT ON COLUMN EMPLOYEE_MASTER.POSITION_PROMOTION_DEMOTION_DATE IS '役職昇降格年月日 既存システム物理名: XXE49D';
COMMENT ON COLUMN EMPLOYEE_MASTER.ORGANIZATION_MANAGEMENT_TRANSFER_DATE IS '組織管理異動年月日 既存システム物理名: XXE50D';
COMMENT ON COLUMN EMPLOYEE_MASTER.ORGANIZATION_MANAGEMENT_POSITION_CODE IS '組織管理職位コード 既存システム物理名: XXE51C';
COMMENT ON COLUMN EMPLOYEE_MASTER.ORIGINAL_AFFILIATION_START_DATE IS '原籍開始年月日 既存システム物理名: XXE52D';
COMMENT ON COLUMN EMPLOYEE_MASTER.ORIGINAL_AFFILIATION_COMPANY_CODE IS '原籍会社コード 既存システム物理名: XXE53C';
COMMENT ON COLUMN EMPLOYEE_MASTER.EMPLOYMENT_CATEGORY_START_DATE IS '雇用形態開始年月日 既存システム物理名: XXE54D';
COMMENT ON COLUMN EMPLOYEE_MASTER.DELETE_CATEGORY IS '削除区分 既存システム物理名: XXE08S';
COMMENT ON COLUMN EMPLOYEE_MASTER.CREATOR IS '作成者 既存システム物理名: XXE09C';
COMMENT ON COLUMN EMPLOYEE_MASTER.CREATION_PROGRAM IS '作成プログラム 既存システム物理名: XXE0AC';
COMMENT ON COLUMN EMPLOYEE_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: XXE01D';
COMMENT ON COLUMN EMPLOYEE_MASTER.CREATION_TIME IS '作成時間 既存システム物理名: XXE02H';
COMMENT ON COLUMN EMPLOYEE_MASTER.UPDATER IS '更新者 既存システム物理名: XXE06P';
COMMENT ON COLUMN EMPLOYEE_MASTER.UPDATE_PROGRAM IS '更新プログラム 既存システム物理名: XXE05P';
COMMENT ON COLUMN EMPLOYEE_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: XXE03D';
COMMENT ON COLUMN EMPLOYEE_MASTER.UPDATE_TIME IS '更新時間 既存システム物理名: XXE04H';
COMMENT ON COLUMN EMPLOYEE_MASTER.TERMINAL_ID IS '端末ID 既存システム物理名: XXEZ9C';
COMMENT ON COLUMN EMPLOYEE_MASTER.QUALIFICATION_PROMOTION_DEMOTION_DATE IS '資格昇降年月日 既存システム物理名: XXE55D';
COMMENT ON COLUMN EMPLOYEE_MASTER.QUALIFICATION_CODE IS '資格CD 既存システム物理名: XXE56C';
COMMENT ON COLUMN EMPLOYEE_MASTER.STORE_TRANSFER_DATE IS '店舗異動年月日 既存システム物理名: XXE57D';
COMMENT ON COLUMN EMPLOYEE_MASTER.SATELLITE_STORE_CODE IS 'サテライト店舗CD 既存システム物理名: XXE58B';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESERVE_1 IS '予備 既存システム物理名: XXE59B';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESERVE_2 IS '予備 既存システム物理名: XXE60B';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESERVE_3 IS '予備 既存システム物理名: XXE61B';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESERVE_4 IS '予備 既存システム物理名: XXE62B';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESERVE_5 IS '予備 既存システム物理名: XXE63B';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESERVE_6 IS '予備 既存システム物理名: XXE64B';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESERVE_7 IS '予備 既存システム物理名: XXE65C';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESERVE_8 IS '予備 既存システム物理名: XXE66C';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESERVE_9 IS '予備 既存システム物理名: XXE67C';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESERVE_10 IS '予備 既存システム物理名: XXE68D';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESERVE_11 IS '予備 既存システム物理名: XXE69D';
COMMENT ON COLUMN EMPLOYEE_MASTER.RESERVE_12 IS '予備 既存システム物理名: XXE70D';
