package jp.ne.simplex.application.model

import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.mock.MockLocalDateTime
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.TestCase
import java.time.LocalDate
import java.time.LocalDateTime

class DateRangeTest : FunSpec({

    context("DateRangeインスタンス生成時のバリデーションチェック") {
        test("from < to の場合、正常にインスタンスが生成されること") {
            shouldNotThrow<ModelCreationFailedException> {
                DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 2))
            }
        }

        test("from = to（from, toの同日を許可する場合）、正常にインスタンスが生成されること") {
            shouldNotThrow<ModelCreationFailedException> {
                DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 1), true)
            }
        }

        test("from = to（from, toの同日を許可しない場合）、Exceptionがスローされること") {
            shouldThrow<ModelCreationFailedException> {
                DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 1), false)
            }
        }

        test("from > to の場合、Exceptionがスローされること") {
            shouldThrow<ModelCreationFailedException> {
                DateRange.of(LocalDate.of(2023, 1, 2), LocalDate.of(2023, 1, 1))
            }
        }
    }

    context("期間が開始済みかチェックする関数の検証") {
        val now = LocalDateTime.of(2024, 4, 28, 0, 0)

        beforeEach { MockLocalDateTime.setNow(now) }
        afterEach { MockLocalDateTime.close() }

        context("同一日付を許容しない場合、期間の開始日とチェック対象の日付が同じ場合、開始済みでないと判断されること") {
            listOf(
                TestCase(LocalDate.of(2023, 4, 29), true),
                TestCase(LocalDate.of(2024, 4, 28), false),
                TestCase(LocalDate.of(2024, 4, 29), false),
                TestCase(LocalDate.of(2024, 4, 30), false),
            ).forEach {
                test("現在日付が${now.yyyyMMdd()}で、${it.input.yyyyMMdd()}から始まる期間の場合、開始済み = ${it.expected}と判断されること") {
                    DateRange.of(it.input, now.plusDays(7).toLocalDate()).isStarted()
                        .shouldBe(it.expected)
                }
            }
        }

        context("同一日付を許容する場合、期間の開始日とチェック対象の日付が同じ場合、開始済みと判断されること") {
            listOf(
                TestCase(LocalDate.of(2023, 4, 29), true),
                TestCase(LocalDate.of(2024, 4, 28), true),
                TestCase(LocalDate.of(2024, 4, 29), false),
                TestCase(LocalDate.of(2024, 4, 30), false),
            ).forEach {
                test("現在日付が${now.yyyyMMdd()}で、${it.input.yyyyMMdd()}から始まる期間の場合、開始済み = ${it.expected}と判断されること") {
                    DateRange.of(it.input, now.plusDays(7).toLocalDate())
                        .isStarted(allowSameDate = true)
                        .shouldBe(it.expected)
                }
            }
        }
    }

    context("期間が終了済みがチェックする関数の検証") {
        val now = LocalDateTime.of(2024, 4, 28, 0, 0)

        beforeEach { MockLocalDateTime.setNow(now) }
        afterEach { MockLocalDateTime.close() }

        context("同一日付を許容しない場合、期間の終了日とチェック対象の日付が同じ場合、終了済みでないと判断されること") {
            listOf(
                TestCase(LocalDate.of(2023, 4, 29), true),
                TestCase(LocalDate.of(2024, 4, 28), false),
                TestCase(LocalDate.of(2024, 4, 29), false),
                TestCase(LocalDate.of(2024, 4, 30), false),
            ).forEach {
                test("現在日付が${now.yyyyMMdd()}で、${it.input.yyyyMMdd()}で終了する期間の場合、終了済み = ${it.expected}と判断されること") {
                    DateRange.of(it.input.minusDays(7), it.input) //
                        .isEnded() //
                        .shouldBe(it.expected)
                }
            }
        }

        context("同一日付を許容する場合、期間の終了日とチェック対象の日付が同じ場合、終了済みと判断されること") {
            listOf(
                TestCase(LocalDate.of(2023, 4, 29), true),
                TestCase(LocalDate.of(2024, 4, 28), true),
                TestCase(LocalDate.of(2024, 4, 29), false),
                TestCase(LocalDate.of(2024, 4, 30), false),
            ).forEach {
                test("現在日付が${now.yyyyMMdd()}で、${it.input.yyyyMMdd()}で終了期間の場合、終了済み = ${it.expected}と判断されること") {
                    DateRange.of(it.input.minusDays(7), it.input) //
                        .isEnded(allowSameDate = true) //
                        .shouldBe(it.expected)
                }
            }
        }
    }

    context("期間の重複チェック（同一日付を許容しない場合）") {
        listOf(
            // 期間が完全に同一な場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 1), true),
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 1), true),
                ),
                true,
            ),
            // fromは一致、toが異なる場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 1), true),
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 2)),
                ),
                true,
            ),
            // fromは異なる、toが一致の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 3)),
                    DateRange.of(LocalDate.of(2023, 1, 2), LocalDate.of(2023, 1, 3)),
                ),
                true,
            ),
            // from1 < from2 < to1 < to2 の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 3)),
                    DateRange.of(LocalDate.of(2023, 1, 2), LocalDate.of(2023, 1, 5)),
                ),
                true,
            ),
            // from2 < from1 < to2  < to1の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 3)),
                    DateRange.of(LocalDate.of(2022, 12, 31), LocalDate.of(2023, 1, 2)),
                ),
                true,
            ),
            // to1 = from2 の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 3)),
                    DateRange.of(LocalDate.of(2023, 1, 3), LocalDate.of(2023, 1, 3), true),
                ),
                true,
            ),
            // to1 < from2 の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 3)),
                    DateRange.of(LocalDate.of(2023, 1, 4), LocalDate.of(2023, 1, 4), true),
                ),
                false,
            ),

            // from1 = to2 の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 3), LocalDate.of(2023, 1, 4)),
                    DateRange.of(LocalDate.of(2023, 1, 2), LocalDate.of(2023, 1, 3)),
                ),
                true,
            ),

            // from1 > to2 の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 3), LocalDate.of(2023, 1, 4)),
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 2)),
                ),
                false,
            ),
        ).forEach {
            test(
                "${it.input.first.from.yyyyMMdd()}~${it.input.first.to.yyyyMMdd()}と" +
                        "${it.input.second.from.yyyyMMdd()}~${it.input.second.to.yyyyMMdd()}は、" +
                        "重複があるかどうか=${it.expected}と判断されること"
            ) {
                it.input.first.isConflict(it.input.second).shouldBe(it.expected)
            }
        }
    }

    context("期間の重複チェック（同一日付を許容する場合）") {
        listOf(
            // 期間が完全に同一な場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 1), true),
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 1), true),
                ),
                false,
            ),
            // fromは一致、toが異なる場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 1), true),
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 2)),
                ),
                false,
            ),
            // fromは異なる、toが一致の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 3)),
                    DateRange.of(LocalDate.of(2023, 1, 2), LocalDate.of(2023, 1, 3)),
                ),
                true,
            ),
            // from1 < from2 < to1 < to2 の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 3)),
                    DateRange.of(LocalDate.of(2023, 1, 2), LocalDate.of(2023, 1, 5)),
                ),
                true,
            ),
            // from2 < from1 < to2  < to1の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 3)),
                    DateRange.of(LocalDate.of(2022, 12, 31), LocalDate.of(2023, 1, 2)),
                ),
                true,
            ),
            // to1 = from2 の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 3)),
                    DateRange.of(LocalDate.of(2023, 1, 3), LocalDate.of(2023, 1, 3), true),
                ),
                false,
            ),
            // to1 < from2 の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 3)),
                    DateRange.of(LocalDate.of(2023, 1, 4), LocalDate.of(2023, 1, 4), true),
                ),
                false,
            ),

            // from1 = to2 の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 3), LocalDate.of(2023, 1, 4)),
                    DateRange.of(LocalDate.of(2023, 1, 2), LocalDate.of(2023, 1, 3)),
                ),
                false,
            ),

            // from1 > to2 の場合
            TestCase(
                Pair(
                    DateRange.of(LocalDate.of(2023, 1, 3), LocalDate.of(2023, 1, 4)),
                    DateRange.of(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 2)),
                ),
                false,
            ),
        ).forEach {
            test(
                "${it.input.first.from.yyyyMMdd()}~${it.input.first.to.yyyyMMdd()}と" +
                        "${it.input.second.from.yyyyMMdd()}~${it.input.second.to.yyyyMMdd()}は、" +
                        "重複があるかどうか=${it.expected}と判断されること"
            ) {
                it.input.first.isConflict(it.input.second, true).shouldBe(it.expected)
            }
        }
    }
})
