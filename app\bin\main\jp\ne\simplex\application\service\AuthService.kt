package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.model.LoginAccessKeyInfo
import jp.ne.simplex.application.model.LoginInfo
import jp.ne.simplex.application.repository.db.AuthRepository
import jp.ne.simplex.authentication.AccessKeyVerifier
import jp.ne.simplex.authentication.JwtAuthProcessor
import jp.ne.simplex.authentication.JwtAuthToken
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ServerValidationException
import org.springframework.stereotype.Service

@Service
class AuthService(
    private val authRepository: AuthRepository,
    private val jwtAuthProcessor: JwtAuthProcessor,
    private val accessKeyVerifier: AccessKeyVerifier,
) {

    fun login(loginInfo: LoginInfo): JwtAuthToken {
        val employee = authRepository.login(loginInfo)
            ?: throw ServerValidationException(ErrorMessage.AUTHENTICATION_FAILURE.format())

        return jwtAuthProcessor.gen(employee)
    }

    fun ssoLogin(employeeCode: Employee.Code): JwtAuthToken {
        val employee = authRepository.getEmployee(employeeCode)
            ?: throw ServerValidationException(ErrorMessage.AUTHENTICATION_FAILURE.format())

        return jwtAuthProcessor.gen(employee)
    }

    /**
     * 駐車場詳細遷移のためのアクセスキーを使ったSSO認証
     */
    fun loginWithAccessKey(loginInfo: LoginAccessKeyInfo): JwtAuthToken {
        accessKeyVerifier.verify(loginInfo.accessKey)
        return ssoLogin(loginInfo.employeeCode)
    }
}
