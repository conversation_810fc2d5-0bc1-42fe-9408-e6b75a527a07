package jp.ne.simplex.application.controller.client.parking.dto

import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.RegisterParkingImage
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException
import org.springframework.web.bind.annotation.RequestPart
import org.springframework.web.multipart.MultipartFile

class ClientParkingImageRegisterRequest(
    @RequestPart("buildingCd")
    @field:Schema(description = "建物コード", example = "000130301")
    val buildingCd: String,

    @RequestPart("image")
    @field:Schema(description = "駐車場配置図画像")
    val image: MultipartFile,
) {
    fun toServiceInterface(): RegisterParkingImage {
        try {
            val buildingCode = Building.Code.of(buildingCd)
            return RegisterParkingImage.of(
                buildingCode = buildingCode,
                image = image,
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }
}
