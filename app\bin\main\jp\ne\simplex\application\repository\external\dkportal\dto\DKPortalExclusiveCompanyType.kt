package jp.ne.simplex.application.repository.external.dkportal.dto

import jp.ne.simplex.application.model.ExclusiveProperty

enum class DKPortalExclusiveCompanyType(val value: Int) {
    RealEstate(0), // 不動産会社

    Leasing(100), // 大東リーシング

    IiheyaNet(20), // いい部屋ネットFC

    HouseCom(300), // ハウスコム
    ;

    companion object {
        fun of(companyType: ExclusiveProperty.CompanyType): DKPortalExclusiveCompanyType {
            return when (companyType) {
                ExclusiveProperty.CompanyType.RealEstate -> RealEstate
                ExclusiveProperty.CompanyType.Leasing -> Leasing
                ExclusiveProperty.CompanyType.HouseCom -> HouseCom
            }
        }
    }

}
