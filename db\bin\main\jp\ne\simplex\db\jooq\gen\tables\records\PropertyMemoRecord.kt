/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.PropertyMemoTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.PropertyMemoPojo

import org.jooq.Record3
import org.jooq.impl.UpdatableRecordImpl


/**
 * 物件メモ 既存システム物理名: -
 */
@Suppress("UNCHECKED_CAST")
open class PropertyMemoRecord private constructor() : UpdatableRecordImpl<PropertyMemoRecord>(PropertyMemoTable.PROPERTY_MEMO) {

    open var buildingCode: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var propertyCode: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var seqNumber: Short
        set(value): Unit = set(2, value)
        get(): Short = get(2) as Short

    open var content: String
        set(value): Unit = set(3, value)
        get(): String = get(3) as String

    open var bookmarkFlag: String
        set(value): Unit = set(4, value)
        get(): String = get(4) as String

    open var creationDate: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var creationTime: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var creator: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var updateDate: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var updateTime: Int?
        set(value): Unit = set(9, value)
        get(): Int? = get(9) as Int?

    open var updater: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var deleteFlag: String
        set(value): Unit = set(11, value)
        get(): String = get(11) as String

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record3<String?, String?, Short?> = super.key() as Record3<String?, String?, Short?>

    /**
     * Create a detached, initialised PropertyMemoRecord
     */
    constructor(buildingCode: String, propertyCode: String, seqNumber: Short, content: String, bookmarkFlag: String, creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, deleteFlag: String): this() {
        this.buildingCode = buildingCode
        this.propertyCode = propertyCode
        this.seqNumber = seqNumber
        this.content = content
        this.bookmarkFlag = bookmarkFlag
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.deleteFlag = deleteFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised PropertyMemoRecord
     */
    constructor(value: PropertyMemoPojo?): this() {
        if (value != null) {
            this.buildingCode = value.buildingCode
            this.propertyCode = value.propertyCode
            this.seqNumber = value.seqNumber
            this.content = value.content
            this.bookmarkFlag = value.bookmarkFlag
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            resetChangedOnNotNull()
        }
    }
}
