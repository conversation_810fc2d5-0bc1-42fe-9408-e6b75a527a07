package jp.ne.simplex.application.controller.client.auth.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.model.LoginAccessKeyInfo
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.log.MaskTarget
import jp.ne.simplex.log.MaskValue

@MaskTarget
data class ClientLoginParkingRequest(
    @JsonProperty("employeeCode")
    @field:Schema(example = "000011")
    val employeeCode: String,

    @MaskValue
    @JsonProperty("accessKey")
    @field:Schema(example = "92b6d96bca9")
    val accessKey: String,

    @JsonProperty("buildingCode")
    @field:Schema(example = "000000101")
    val buildingCode: String,

    ) {

    fun toServiceInterface(): LoginAccessKeyInfo {
        try {
            return LoginAccessKeyInfo(
                employeeCode = Employee.Code(employeeCode),
                accessKey = LoginAccessKeyInfo.AccessKey.of(accessKey),
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }

    fun toParkingServiceInterface(): Building.Code? {
        return try {
            Building.Code.of(buildingCode)
        } catch (e: ModelCreationFailedException) {
            null
        }
    }
}
