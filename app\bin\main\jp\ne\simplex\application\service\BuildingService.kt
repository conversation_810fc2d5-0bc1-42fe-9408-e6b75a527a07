package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ImageFile
import jp.ne.simplex.application.model.RegisterGarbageImage
import jp.ne.simplex.application.model.RegisterParkingImage
import jp.ne.simplex.application.repository.db.BuildingMasterRepositoryInterface
import jp.ne.simplex.application.repository.external.eboard.EboardRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ServerValidationException
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class BuildingService(
    private val buildingMasterRepository: BuildingMasterRepositoryInterface,
    private val eboardRepository: EboardRepositoryInterface,
) {
    companion object {
        private val log = LoggerFactory.getLogger(BuildingService::class.java)
    }

    fun get(buildingCode: Building.Code): Building {
        return buildingMasterRepository.findBy(buildingCode)
            ?: throw ServerValidationException(ErrorMessage.INVALID_BUILDING_CODE.format())
    }

    fun getActive(buildingCode: Building.Code): Building {
        return buildingMasterRepository.findActiveBy(buildingCode)
            ?: throw ServerValidationException(ErrorMessage.INVALID_BUILDING_CODE.format())
    }

    /** 駐車場配置図画像を登録する */
    fun registerParkingImage(
        requestUser: AuthInfo.RequestUser,
        param: RegisterParkingImage
    ) {
        val building = param.buildingCode.let { get(it) }
        runCatching {
            eboardRepository.registerParkingImage(
                requestUser,
                building.code,
                ImageFile.of(building.code, param.imageFile)
            )
        }.onFailure {
            log.error("registerParkingImage failed. buildingCode=${building.code}", it)
            throw it
        }
    }

    /** 駐車場配置図画像を削除する */
    fun deleteParkingImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code
    ) {
        val building = buildingCode.let { get(it) }
        runCatching {
            eboardRepository.deleteParkingImage(requestUser, building.code)
        }.onFailure {
            log.error("deleteParkingImage failed. buildingCode=${building.code}", it)
            throw it
        }
    }

    /** ゴミ置場画像を登録する */
    fun registerGarbageImage(
        requestUser: AuthInfo.RequestUser,
        param: RegisterGarbageImage
    ) {
        val building = param.buildingCode.let { get(it) }
        runCatching {
            eboardRepository.registerGarbageImage(
                requestUser,
                building.code,
                ImageFile.of(building.code, param.imageFile)
            )
        }.onFailure {
            log.error("registerGarbageImage failed. buildingCode=${building.code}", it)
            throw it
        }
    }

    /** ゴミ置場画像を削除する */
    fun deleteGarbageImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code
    ) {
        val building = buildingCode.let { get(it) }
        runCatching {
            eboardRepository.deleteGarbageImage(requestUser, building.code)
        }.onFailure {
            log.error("deleteGarbageImage failed. buildingCode=${building.code}", it)
            throw it
        }
    }

}
