#!/bin/bash

echo "$(date) /**************** DBオブジェクト（Schema/Table/Index/View/Function）作成 ****************/"
echo

sh create-objects.sh "${DB_NAME}" "${APP_USER}" "${APP_USER}" "${APP_USER_PASSWORD}" "${DATA_TABLESPACE}" "${IDX_TABLESPACE}"

echo "$(date) /**************** ${OPE_USER} にSELECT権限付与, スキーマ検索パスを設定 ****************/"
echo

# TODO: OPE_USERは他の権限も必要
sh grant-select.sh "${OPE_USER}" "${DB_NAME}" "${APP_USER}"
sh set-search-path.sh "${OPE_USER}" "${DB_NAME}"

echo "$(date) /**************** ${READONLY_USER} にSELECT権限付与, スキーマ検索パスを設定 ****************/"
echo

sh grant-select.sh "${READONLY_USER}" "${DB_NAME}" "${APP_USER}"
sh set-search-path.sh "${READONLY_USER}" "${DB_NAME}"

echo "$(date) /**************** 初期データ登録 ****************/"
echo

if [ "${INIT_DB_TYPE}" != "DDL_ONLY" ]; then
  sh init-db-data.sh "${DB_NAME}" "${APP_USER}" "${APP_USER_PASSWORD}"
fi
