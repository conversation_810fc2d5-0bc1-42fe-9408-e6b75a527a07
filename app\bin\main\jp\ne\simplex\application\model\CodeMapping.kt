package jp.ne.simplex.application.model

import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException

class CodeMapping(
    val eCode: ECode
) {

    data class ECode private constructor(val value: String) {
        companion object {

            private const val LENGTH = 9

            fun of(value: String): ECode {
                return if (value.length == LENGTH) ECode(value)
                else throw ModelCreationFailedException(
                    ErrorMessage.STRING_LENGTH.format("Eコード", LENGTH)
                )
            }
        }
    }
}
