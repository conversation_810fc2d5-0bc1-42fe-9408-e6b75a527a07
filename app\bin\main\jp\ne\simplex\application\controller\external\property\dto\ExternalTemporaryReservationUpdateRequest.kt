package jp.ne.simplex.application.controller.external.property.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.ForceUpdateTemporaryReservation.ForceCancelTemporaryReservation
import jp.ne.simplex.application.model.ForceUpdateTemporaryReservation.ForceRegisterTemporaryReservation
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.shared.StringExtension.Companion.toBoolean
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd

data class ExternalTemporaryReservationUpdateRequest(
    @JsonProperty("buildingCd")
    @field:Schema(description = "建物コード", example = "000130305")
    val buildingCd: String,

    @JsonProperty("roomCd")
    @field:Schema(description = "部屋コード", example = "01010")
    val roomCd: String,

    @JsonProperty("temporaryReservationCancelFlag")
    @field:Schema(description = "仮押さえキャンセルフラグ（0:登録, 1:キャンセル）", example = "0")
    val temporaryReservationCancelFlag: String,

    @JsonProperty("applicationScheduledDate")
    @field:Schema(description = "申込予定日（仮押さえ登録の場合必須 yyyyMMdd）", example = "20241218")
    val applicationScheduledDate: String? = null,

    @JsonProperty("applicationScheduledPersonCd")
    @field:Schema(description = "申込予定担当者コード（仮押さえ登録の場合必須）", example = "000011")
    val applicationScheduledPersonCd: String? = null,

    @JsonProperty("comment")
    @field:Schema(description = "コメント（最大文字数:257文字）", example = "コメントが入ります")
    val comment: String? = null,

    @JsonProperty("customerRepBranchCd")
    @field:Schema(description = "客付け担当支店コード（仮押さえ登録の場合必須）", example = "643")
    val customerRepBranchCd: String? = null,

    @JsonProperty("contractFormECode")
    @field:Schema(description = "Eコード", example = "E5650123")
    val contractFormECode: String? = null,

    @JsonProperty("temporaryReservationSequence")
    @field:Schema(description = "仮押さえシーケンス", example = "202410315425304634")
    val temporaryReservationSequence: String? = null,

    @JsonProperty("registrationDate")
    @field:Schema(description = "登録日（仮押さえ登録の場合必須）", example = "20241031")
    val registrationDate: String? = null,

    @JsonProperty("registrationTime")
    @field:Schema(description = "登録時刻（仮押さえ登録の場合必須）", example = "101952")
    val registrationTime: String? = null,

    @JsonProperty("otherCompanyFlag")
    @field:Schema(description = "他社フラグ（仮押さえ登録の場合必須、0:false, 1:true）", example = "0")
    val otherCompanyFlag: String? = null,

    @JsonProperty("otherCompanyName")
    @field:Schema(description = "他社会社名", example = "〇〇株式会社")
    val otherCompanyName: String? = null,

    @JsonProperty("otherCompanyStoreName")
    @field:Schema(description = "他社店舗名", example = "〇〇支店")
    val otherCompanyStoreName: String? = null,

    @JsonProperty("otherCompanyRepName")
    @field:Schema(description = "他社担当者名", example = "山田太郎")
    val otherCompanyRepName: String? = null,
) {

    companion object {
        // コメントに設定可能な最大文字数
        private const val MAX_LENGTH = 257
    }

    // Service層の Interface に変換する
    fun toServiceInterface(): TemporaryReservation {
        if ((comment?.length ?: 0) > MAX_LENGTH) {
            throw ClientValidationException(
                ErrorMessage.STRING_MAX_LENGTH.format("コメント", MAX_LENGTH)
            )
        }
        try {
            return when (temporaryReservationCancelFlag.toBoolean()) {
                true -> this.getForceCancelRequest()
                false -> this.getForceRegisterRequest()
                else -> throw ClientValidationException(ErrorMessage.TEMPORARY_RESERVATION_INVALID_CANCEL_FLAG.format())
            }
        } catch (e: ClientValidationException) {
            throw e
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }

    private fun getForceRegisterRequest(): ForceRegisterTemporaryReservation {
        return when (otherCompanyFlag.toBoolean()) {
            true -> {
                if (contractFormECode == null || otherCompanyName == null || otherCompanyStoreName == null || otherCompanyRepName == null) {
                    throw ClientValidationException(
                        ErrorMessage.MISSING_REQUIRED_FIELDS.format("contractFormECode, otherCompanyName, otherCompanyStoreName, otherCompanyRepName")
                    )
                }
                ForceRegisterTemporaryReservation(
                    id = Property.Id(Building.Code.of(buildingCd), Room.Code.of(roomCd)),
                    comment = TemporaryReservation.Comment.of(comment),
                    scheduledMoveInDate = applicationScheduledDate?.yyyyMMdd()!!,
                    assignedBranchCode = null,
                    assignedEmployeeCode = null,
                    otherCompanyInfo = TemporaryReservation.OtherCompanyInfo(
                        companyCode = contractFormECode,
                        companyName = otherCompanyName,
                        storeName = otherCompanyStoreName,
                        staffName = otherCompanyRepName,
                    ),
                )
            }

            false -> {
                if (customerRepBranchCd == null || applicationScheduledPersonCd == null) {
                    throw ClientValidationException(
                        ErrorMessage.MISSING_REQUIRED_FIELDS.format("customerRepBranchCd, applicationScheduledPersonCd")
                    )
                }
                ForceRegisterTemporaryReservation(
                    id = Property.Id(Building.Code.of(buildingCd), Room.Code.of(roomCd)),
                    comment = TemporaryReservation.Comment.of(comment),
                    scheduledMoveInDate = applicationScheduledDate?.yyyyMMdd()!!,
                    assignedBranchCode = Branch.Code.of(customerRepBranchCd),
                    assignedEmployeeCode = Employee.Code.of(applicationScheduledPersonCd),
                    otherCompanyInfo = null,
                )
            }

            else -> throw ClientValidationException(ErrorMessage.TEMPORARY_RESERVATION_INVALID_OTHER_COMPANY_FLAG.format())
        }
    }

    private fun getForceCancelRequest(): ForceCancelTemporaryReservation {
        return ForceCancelTemporaryReservation(
            id = Property.Id(Building.Code.of(buildingCd), Room.Code.of(roomCd)),
            comment = TemporaryReservation.Comment.of(comment),
        )
    }
}
