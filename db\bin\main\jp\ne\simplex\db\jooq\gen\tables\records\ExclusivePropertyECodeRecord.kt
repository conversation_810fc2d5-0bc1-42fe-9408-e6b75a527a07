/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ExclusivePropertyECodeTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ExclusivePropertyECodePojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 先行公開Eコードテーブル 既存システム物理名: -
 */
@Suppress("UNCHECKED_CAST")
open class ExclusivePropertyECodeRecord private constructor() : UpdatableRecordImpl<ExclusivePropertyECodeRecord>(ExclusivePropertyECodeTable.EXCLUSIVE_PROPERTY_E_CODE) {

    open var id: Long
        set(value): Unit = set(0, value)
        get(): Long = get(0) as Long

    open var eCode: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var creationDate: Int
        set(value): Unit = set(2, value)
        get(): Int = get(2) as Int

    open var creationTime: Int
        set(value): Unit = set(3, value)
        get(): Int = get(3) as Int

    open var creator: String
        set(value): Unit = set(4, value)
        get(): String = get(4) as String

    open var updateDate: Int
        set(value): Unit = set(5, value)
        get(): Int = get(5) as Int

    open var updateTime: Int
        set(value): Unit = set(6, value)
        get(): Int = get(6) as Int

    open var updater: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var deleteFlag: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<Long?> = super.key() as Record1<Long?>

    /**
     * Create a detached, initialised ExclusivePropertyECodeRecord
     */
    constructor(id: Long, eCode: String, creationDate: Int, creationTime: Int, creator: String, updateDate: Int, updateTime: Int, updater: String, deleteFlag: String): this() {
        this.id = id
        this.eCode = eCode
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.deleteFlag = deleteFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ExclusivePropertyECodeRecord
     */
    constructor(value: ExclusivePropertyECodePojo?): this() {
        if (value != null) {
            this.id = value.id
            this.eCode = value.eCode
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            resetChangedOnNotNull()
        }
    }
}
