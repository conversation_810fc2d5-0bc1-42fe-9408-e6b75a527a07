/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.CityGasRateAddressFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.CityGasRateAddressFilePojo

import org.jooq.impl.TableRecordImpl


/**
 * 都市ガス料金対象住所ファイル 既存システム物理名: FFF10P
 */
@Suppress("UNCHECKED_CAST")
open class CityGasRateAddressFileRecord private constructor() : TableRecordImpl<CityGasRateAddressFileRecord>(CityGasRateAddressFileTable.CITY_GAS_RATE_ADDRESS_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creationProgramId: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var creationTerminal: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var creationResponsible: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateDate: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var updateTime: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var updateTerminal: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var updateResponsible: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var deleteSign: Byte?
        set(value): Unit = set(10, value)
        get(): Byte? = get(10) as Byte?

    open var addressCd: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var targetStartDate: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    open var targetEndDate: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var gasCompanyCategory: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    /**
     * Create a detached, initialised CityGasRateAddressFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creationProgramId: String? = null, creationTerminal: String? = null, creationResponsible: String? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updateTerminal: String? = null, updateResponsible: String? = null, deleteSign: Byte? = null, addressCd: String? = null, targetStartDate: Int? = null, targetEndDate: Int? = null, gasCompanyCategory: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creationProgramId = creationProgramId
        this.creationTerminal = creationTerminal
        this.creationResponsible = creationResponsible
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updateTerminal = updateTerminal
        this.updateResponsible = updateResponsible
        this.deleteSign = deleteSign
        this.addressCd = addressCd
        this.targetStartDate = targetStartDate
        this.targetEndDate = targetEndDate
        this.gasCompanyCategory = gasCompanyCategory
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised CityGasRateAddressFileRecord
     */
    constructor(value: CityGasRateAddressFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creationProgramId = value.creationProgramId
            this.creationTerminal = value.creationTerminal
            this.creationResponsible = value.creationResponsible
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updateTerminal = value.updateTerminal
            this.updateResponsible = value.updateResponsible
            this.deleteSign = value.deleteSign
            this.addressCd = value.addressCd
            this.targetStartDate = value.targetStartDate
            this.targetEndDate = value.targetEndDate
            this.gasCompanyCategory = value.gasCompanyCategory
            resetChangedOnNotNull()
        }
    }
}
