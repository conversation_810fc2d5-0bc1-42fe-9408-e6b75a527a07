/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.TemporaryContractTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.TemporaryContractPojo

import org.jooq.impl.TableRecordImpl


/**
 * 仮契約書 既存システム物理名: HCC35P
 */
@Suppress("UNCHECKED_CAST")
open class TemporaryContractRecord private constructor() : TableRecordImpl<TemporaryContractRecord>(TemporaryContractTable.TEMPORARY_CONTRACT) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creationProgramId: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var creationTerminalId: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var creationResponsibleCd: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateDate: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var updateTime: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var updateTerminalId: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var updateResponsibleCd: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var logicalDeleteFlag: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var buildingCd: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var effectiveStartDate: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    open var effectiveEndDate: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var contractType: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var dataManagementNo: Short?
        set(value): Unit = set(15, value)
        get(): Short? = get(15) as Short?

    open var conclusionCategory: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var managementContractStartDate: Int?
        set(value): Unit = set(17, value)
        get(): Int? = get(17) as Int?

    open var managementContractEndDate: Int?
        set(value): Unit = set(18, value)
        get(): Int? = get(18) as Int?

    open var contractOutputManagementNo: Short?
        set(value): Unit = set(19, value)
        get(): Short? = get(19) as Short?

    open var confirmProofOutputDate: Int?
        set(value): Unit = set(20, value)
        get(): Int? = get(20) as Int?

    open var contractCollectionInputDate: Int?
        set(value): Unit = set(21, value)
        get(): Int? = get(21) as Int?

    open var contractApprovalCategory: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var contractApprover: Int?
        set(value): Unit = set(23, value)
        get(): Int? = get(23) as Int?

    open var contractApprovalDate: Int?
        set(value): Unit = set(24, value)
        get(): Int? = get(24) as Int?

    open var contractOutputDate: Int?
        set(value): Unit = set(25, value)
        get(): Int? = get(25) as Int?

    open var contractConclusionCategory: Byte?
        set(value): Unit = set(26, value)
        get(): Byte? = get(26) as Byte?

    open var conclusionInputDate: Int?
        set(value): Unit = set(27, value)
        get(): Int? = get(27) as Int?

    open var managementContractExpectedDate: Int?
        set(value): Unit = set(28, value)
        get(): Int? = get(28) as Int?

    open var managementContractDate: Int?
        set(value): Unit = set(29, value)
        get(): Int? = get(29) as Int?

    open var headOfficeApplicationCategory: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var headOfficeReceptionCategory: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var headOfficeReceptionDate: Int?
        set(value): Unit = set(32, value)
        get(): Int? = get(32) as Int?

    open var nonStandardApprovalDate: Int?
        set(value): Unit = set(33, value)
        get(): Int? = get(33) as Int?

    open var nonStandardApprover: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var nonStandardApplication: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var agreementTerminationDate: Int?
        set(value): Unit = set(36, value)
        get(): Int? = get(36) as Int?

    open var agreementTerminationDate2: Int?
        set(value): Unit = set(37, value)
        get(): Int? = get(37) as Int?

    open var agreementTerminationReason: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var nameChangeProgressNo: Int?
        set(value): Unit = set(39, value)
        get(): Int? = get(39) as Int?

    open var nameChangeReasonCategory: String?
        set(value): Unit = set(40, value)
        get(): String? = get(40) as String?

    open var notificationNo: Int?
        set(value): Unit = set(41, value)
        get(): Int? = get(41) as Int?

    open var agreementRegistrationNo: String?
        set(value): Unit = set(42, value)
        get(): String? = get(42) as String?

    open var oldAgreementNo: String?
        set(value): Unit = set(43, value)
        get(): String? = get(43) as String?

    open var managementDelegatorCd: String?
        set(value): Unit = set(44, value)
        get(): String? = get(44) as String?

    open var managementDelegatorName: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var delegatorPostalCode: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var delegatorAddressCd1: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var delegatorAddressCd2: String?
        set(value): Unit = set(48, value)
        get(): String? = get(48) as String?

    open var delegatorAddressCd3: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    open var delegatorAddressDetail: String?
        set(value): Unit = set(50, value)
        get(): String? = get(50) as String?

    open var delegatorBuildingName: String?
        set(value): Unit = set(51, value)
        get(): String? = get(51) as String?

    open var delegatorPhoneNo: String?
        set(value): Unit = set(52, value)
        get(): String? = get(52) as String?

    open var ownerTaxCategory: String?
        set(value): Unit = set(53, value)
        get(): String? = get(53) as String?

    open var constructionCategory: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var buildingType: String?
        set(value): Unit = set(55, value)
        get(): String? = get(55) as String?

    open var roomPurpose1Business: String?
        set(value): Unit = set(56, value)
        get(): String? = get(56) as String?

    open var roomPurpose2Residential: String?
        set(value): Unit = set(57, value)
        get(): String? = get(57) as String?

    open var roomPurpose3Parking: String?
        set(value): Unit = set(58, value)
        get(): String? = get(58) as String?

    open var roomPurpose4Tr: String?
        set(value): Unit = set(59, value)
        get(): String? = get(59) as String?

    open var roomPurpose5Other: String?
        set(value): Unit = set(60, value)
        get(): String? = get(60) as String?

    open var loanCategory: String?
        set(value): Unit = set(61, value)
        get(): String? = get(61) as String?

    open var totalUnitsBusiness: Short?
        set(value): Unit = set(62, value)
        get(): Short? = get(62) as Short?

    open var totalUnitsResidential: Short?
        set(value): Unit = set(63, value)
        get(): Short? = get(63) as Short?

    open var totalParkingUnits: Short?
        set(value): Unit = set(64, value)
        get(): Short? = get(64) as Short?

    open var managedUnitsBusiness: Short?
        set(value): Unit = set(65, value)
        get(): Short? = get(65) as Short?

    open var managedUnitsResidential: Short?
        set(value): Unit = set(66, value)
        get(): Short? = get(66) as Short?

    open var managedParkingUnits: Short?
        set(value): Unit = set(67, value)
        get(): Short? = get(67) as Short?

    open var contractForm: String?
        set(value): Unit = set(68, value)
        get(): String? = get(68) as String?

    open var managementCategory: String?
        set(value): Unit = set(69, value)
        get(): String? = get(69) as String?

    open var managementPartnership: String?
        set(value): Unit = set(70, value)
        get(): String? = get(70) as String?

    open var partnershipType: String?
        set(value): Unit = set(71, value)
        get(): String? = get(71) as String?

    open var managementPartner: String?
        set(value): Unit = set(72, value)
        get(): String? = get(72) as String?

    open var maintenancePartner: String?
        set(value): Unit = set(73, value)
        get(): String? = get(73) as String?

    open var detailsIssue: String?
        set(value): Unit = set(74, value)
        get(): String? = get(74) as String?

    open var managementFormCategory: String?
        set(value): Unit = set(75, value)
        get(): String? = get(75) as String?

    open var specialRent: String?
        set(value): Unit = set(76, value)
        get(): String? = get(76) as String?

    open var comCommunityPartnership: String?
        set(value): Unit = set(77, value)
        get(): String? = get(77) as String?

    open var proRataDays: Byte?
        set(value): Unit = set(78, value)
        get(): Byte? = get(78) as Byte?

    open var excludedProRataDays: Byte?
        set(value): Unit = set(79, value)
        get(): Byte? = get(79) as Byte?

    open var nonStandardFlag: String?
        set(value): Unit = set(80, value)
        get(): String? = get(80) as String?

    open var newOwnerNameAtNameChange: String?
        set(value): Unit = set(81, value)
        get(): String? = get(81) as String?

    open var ownerAddressAtNameChange: String?
        set(value): Unit = set(82, value)
        get(): String? = get(82) as String?

    open var ownerTransferAccountOwnerCd: String?
        set(value): Unit = set(83, value)
        get(): String? = get(83) as String?

    open var detailsConsolidationUnit: String?
        set(value): Unit = set(84, value)
        get(): String? = get(84) as String?

    open var transferAccountDivision: String?
        set(value): Unit = set(85, value)
        get(): String? = get(85) as String?

    open var recipientCd: String?
        set(value): Unit = set(86, value)
        get(): String? = get(86) as String?

    open var previousRecipientCd: String?
        set(value): Unit = set(87, value)
        get(): String? = get(87) as String?

    open var renewalFeeAcquisition: String?
        set(value): Unit = set(88, value)
        get(): String? = get(88) as String?

    open var renewalFeeAcquisitionMonths: Byte?
        set(value): Unit = set(89, value)
        get(): Byte? = get(89) as Byte?

    open var renewalFeeCommissionRate: Short?
        set(value): Unit = set(90, value)
        get(): Short? = get(90) as Short?

    open var guarantorNotRequiredApprovalFlag: String?
        set(value): Unit = set(91, value)
        get(): String? = get(91) as String?

    open var fixedTermRentalContractConclusion: String?
        set(value): Unit = set(92, value)
        get(): String? = get(92) as String?

    open var waterFeeManagementFeeCollection: String?
        set(value): Unit = set(93, value)
        get(): String? = get(93) as String?

    open var waterMeterCategory: String?
        set(value): Unit = set(94, value)
        get(): String? = get(94) as String?

    open var gasCategory: String?
        set(value): Unit = set(95, value)
        get(): String? = get(95) as String?

    open var sharedOwnershipCd1: String?
        set(value): Unit = set(96, value)
        get(): String? = get(96) as String?

    open var delegatorSharedInterest1: BigDecimal?
        set(value): Unit = set(97, value)
        get(): BigDecimal? = get(97) as BigDecimal?

    open var sharedOwnershipCd2: String?
        set(value): Unit = set(98, value)
        get(): String? = get(98) as String?

    open var delegatorSharedInterest2: BigDecimal?
        set(value): Unit = set(99, value)
        get(): BigDecimal? = get(99) as BigDecimal?

    open var sharedOwnershipCd3: String?
        set(value): Unit = set(100, value)
        get(): String? = get(100) as String?

    open var delegatorSharedInterest3: BigDecimal?
        set(value): Unit = set(101, value)
        get(): BigDecimal? = get(101) as BigDecimal?

    open var waterReadingCategory: String?
        set(value): Unit = set(102, value)
        get(): String? = get(102) as String?

    open var waterFeeCollection: String?
        set(value): Unit = set(103, value)
        get(): String? = get(103) as String?

    open var depositHandlingCategory: String?
        set(value): Unit = set(104, value)
        get(): String? = get(104) as String?

    open var communityFeeManagementCollection: String?
        set(value): Unit = set(105, value)
        get(): String? = get(105) as String?

    open var communityFeeManagementPayment: String?
        set(value): Unit = set(106, value)
        get(): String? = get(106) as String?

    open var gasReadingCategory: String?
        set(value): Unit = set(107, value)
        get(): String? = get(107) as String?

    open var petsAllowedCategory: String?
        set(value): Unit = set(108, value)
        get(): String? = get(108) as String?

    open var rentTransferAccountCategory: String?
        set(value): Unit = set(109, value)
        get(): String? = get(109) as String?

    open var monthlyRentTransferAccount: String?
        set(value): Unit = set(110, value)
        get(): String? = get(110) as String?

    open var commonFeeTransferAccount: String?
        set(value): Unit = set(111, value)
        get(): String? = get(111) as String?

    open var commonFeeTransferAccountCd: String?
        set(value): Unit = set(112, value)
        get(): String? = get(112) as String?

    open var exclusiveBrokeragePeriod: String?
        set(value): Unit = set(113, value)
        get(): String? = get(113) as String?

    open var mutualAidAssociationEnrollment: String?
        set(value): Unit = set(114, value)
        get(): String? = get(114) as String?

    open var depositSettlementMethod: String?
        set(value): Unit = set(115, value)
        get(): String? = get(115) as String?

    open var depositManagementDelegatorRate: Short?
        set(value): Unit = set(116, value)
        get(): Short? = get(116) as Short?

    open var departureManagementServiceTermination: String?
        set(value): Unit = set(117, value)
        get(): String? = get(117) as String?

    open var buildingInspectionServiceSitePatrol: String?
        set(value): Unit = set(118, value)
        get(): String? = get(118) as String?

    open var vacantRoomManagementService: String?
        set(value): Unit = set(119, value)
        get(): String? = get(119) as String?

    open var waterFeeReading: String?
        set(value): Unit = set(120, value)
        get(): String? = get(120) as String?

    open var commonEquipmentMaintenance: String?
        set(value): Unit = set(121, value)
        get(): String? = get(121) as String?

    open var otherMaintenance: String?
        set(value): Unit = set(122, value)
        get(): String? = get(122) as String?

    open var maintenanceFeeCollectionTarget: String?
        set(value): Unit = set(123, value)
        get(): String? = get(123) as String?

    open var maintenanceFeeUnit: String?
        set(value): Unit = set(124, value)
        get(): String? = get(124) as String?

    open var maintenanceFeeTotal: Int?
        set(value): Unit = set(125, value)
        get(): Int? = get(125) as Int?

    open var maintenanceFeeDaitoShare: Int?
        set(value): Unit = set(126, value)
        get(): Int? = get(126) as Int?

    open var tenantSettlementService: String?
        set(value): Unit = set(127, value)
        get(): String? = get(127) as String?

    open var tenantSettlementMngFeeDeduction: String?
        set(value): Unit = set(128, value)
        get(): String? = get(128) as String?

    open var departureSettlementService: String?
        set(value): Unit = set(129, value)
        get(): String? = get(129) as String?

    open var salesDepartAchieveAccountingCategory: String?
        set(value): Unit = set(130, value)
        get(): String? = get(130) as String?

    open var moveInOutManagement: String?
        set(value): Unit = set(131, value)
        get(): String? = get(131) as String?

    open var mngDepartmentLeaseContractInput: String?
        set(value): Unit = set(132, value)
        get(): String? = get(132) as String?

    open var contractCategory: String?
        set(value): Unit = set(133, value)
        get(): String? = get(133) as String?

    open var siteArea: BigDecimal?
        set(value): Unit = set(134, value)
        get(): BigDecimal? = get(134) as BigDecimal?

    open var otherPurposes: String?
        set(value): Unit = set(135, value)
        get(): String? = get(135) as String?

    open var otherPurposesContent: String?
        set(value): Unit = set(136, value)
        get(): String? = get(136) as String?

    open var specialContractNo: String?
        set(value): Unit = set(137, value)
        get(): String? = get(137) as String?

    open var additionalTask1: String?
        set(value): Unit = set(138, value)
        get(): String? = get(138) as String?

    open var additionalTask2: String?
        set(value): Unit = set(139, value)
        get(): String? = get(139) as String?

    open var managementBranchPhoneNo: String?
        set(value): Unit = set(140, value)
        get(): String? = get(140) as String?

    open var managementContractTrInitial: Int?
        set(value): Unit = set(141, value)
        get(): Int? = get(141) as Int?

    open var managementContractTrNext: Int?
        set(value): Unit = set(142, value)
        get(): Int? = get(142) as Int?

    open var specialClausesIncluded: String?
        set(value): Unit = set(143, value)
        get(): String? = get(143) as String?

    open var maintenanceItemsIncluded: String?
        set(value): Unit = set(144, value)
        get(): String? = get(144) as String?

    open var managementDelegationDataIncluded: String?
        set(value): Unit = set(145, value)
        get(): String? = get(145) as String?

    open var mngDelegationContentDataIncluded: String?
        set(value): Unit = set(146, value)
        get(): String? = get(146) as String?

    open var agreementNoChangeManagementNo: Short?
        set(value): Unit = set(147, value)
        get(): Short? = get(147) as Short?

    open var transferDate: Int?
        set(value): Unit = set(148, value)
        get(): Int? = get(148) as Int?

    open var recordNewOldCategory: String?
        set(value): Unit = set(149, value)
        get(): String? = get(149) as String?

    open var initialSetupFlag: String?
        set(value): Unit = set(150, value)
        get(): String? = get(150) as String?

    open var contractEndDate: Int?
        set(value): Unit = set(151, value)
        get(): Int? = get(151) as Int?

    open var operationStartDate: Int?
        set(value): Unit = set(152, value)
        get(): Int? = get(152) as Int?

    open var maintenanceServiceCategory: String?
        set(value): Unit = set(153, value)
        get(): String? = get(153) as String?

    open var simultaneousContractOutput: String?
        set(value): Unit = set(154, value)
        get(): String? = get(154) as String?

    open var outputControlCategory: String?
        set(value): Unit = set(155, value)
        get(): String? = get(155) as String?

    open var managementStartExpectedDate: Int?
        set(value): Unit = set(156, value)
        get(): Int? = get(156) as Int?

    open var autoCreationCategory: String?
        set(value): Unit = set(157, value)
        get(): String? = get(157) as String?

    open var maintenanceDelegationCreationCategory: String?
        set(value): Unit = set(158, value)
        get(): String? = get(158) as String?

    open var previousConclusionDate: Int?
        set(value): Unit = set(159, value)
        get(): Int? = get(159) as Int?

    open var mainteDelegationOnlyUpdateCategory: String?
        set(value): Unit = set(160, value)
        get(): String? = get(160) as String?

    open var mainteDelegationContractOutputTarget: String?
        set(value): Unit = set(161, value)
        get(): String? = get(161) as String?

    open var mainteConclusionExpectedDate: Int?
        set(value): Unit = set(162, value)
        get(): Int? = get(162) as Int?

    open var mainteConclusionDate: Int?
        set(value): Unit = set(163, value)
        get(): Int? = get(163) as Int?

    open var conclusionExpectedDateRequired: String?
        set(value): Unit = set(164, value)
        get(): String? = get(164) as String?

    open var houseComStoreCd: String?
        set(value): Unit = set(165, value)
        get(): String? = get(165) as String?

    open var mngStartDateChangeContractChange: String?
        set(value): Unit = set(166, value)
        get(): String? = get(166) as String?

    open var businessNewGuaranteeCategory: String?
        set(value): Unit = set(167, value)
        get(): String? = get(167) as String?

    open var repairSpecialClause: String?
        set(value): Unit = set(168, value)
        get(): String? = get(168) as String?

    open var repairSpecialClausePeriod: Byte?
        set(value): Unit = set(169, value)
        get(): Byte? = get(169) as Byte?

    open var inputManagementNo: Short?
        set(value): Unit = set(170, value)
        get(): Short? = get(170) as Short?

    open var nonLeaseUseResidential: String?
        set(value): Unit = set(171, value)
        get(): String? = get(171) as String?

    open var nonLeaseUseBusiness: String?
        set(value): Unit = set(172, value)
        get(): String? = get(172) as String?

    open var nonLeaseUseParking: String?
        set(value): Unit = set(173, value)
        get(): String? = get(173) as String?

    open var buildingStructure: String?
        set(value): Unit = set(174, value)
        get(): String? = get(174) as String?

    open var buildingFloors: Byte?
        set(value): Unit = set(175, value)
        get(): Byte? = get(175) as Byte?

    open var buildingAddressCd1: String?
        set(value): Unit = set(176, value)
        get(): String? = get(176) as String?

    open var buildingAddressCd2: String?
        set(value): Unit = set(177, value)
        get(): String? = get(177) as String?

    open var buildingAddressCd3: String?
        set(value): Unit = set(178, value)
        get(): String? = get(178) as String?

    open var buildingAddressDetail: String?
        set(value): Unit = set(179, value)
        get(): String? = get(179) as String?

    open var meterCount: Short?
        set(value): Unit = set(180, value)
        get(): Short? = get(180) as Short?

    open var waterUsageIncluded: String?
        set(value): Unit = set(181, value)
        get(): String? = get(181) as String?

    open var waterUsageMonthly: Int?
        set(value): Unit = set(182, value)
        get(): Int? = get(182) as Int?

    open var maintenanceItemBIncluded: String?
        set(value): Unit = set(183, value)
        get(): String? = get(183) as String?

    open var maintenanceItemBMonthly: Int?
        set(value): Unit = set(184, value)
        get(): Int? = get(184) as Int?

    open var maintenanceItemAIncluded: String?
        set(value): Unit = set(185, value)
        get(): String? = get(185) as String?

    open var maintenanceItemAMonthly: Int?
        set(value): Unit = set(186, value)
        get(): Int? = get(186) as Int?

    open var rentalAdjustmentIncluded: String?
        set(value): Unit = set(187, value)
        get(): String? = get(187) as String?

    open var rentalAdjustmentMonthly: Int?
        set(value): Unit = set(188, value)
        get(): Int? = get(188) as Int?

    open var maintenanceFeeAdjustmentIncluded: String?
        set(value): Unit = set(189, value)
        get(): String? = get(189) as String?

    open var maintenanceFeeAdjustmentMonthly: Int?
        set(value): Unit = set(190, value)
        get(): Int? = get(190) as Int?

    open var repairFeeAdjustmentIncluded: String?
        set(value): Unit = set(191, value)
        get(): String? = get(191) as String?

    open var repairFeeAdjustmentMonthly: Int?
        set(value): Unit = set(192, value)
        get(): Int? = get(192) as Int?

    open var communityFeeAdjustmentIncluded: String?
        set(value): Unit = set(193, value)
        get(): String? = get(193) as String?

    open var communityFeeAdjustmentMonthly: Int?
        set(value): Unit = set(194, value)
        get(): Int? = get(194) as Int?

    open var catvAdjustmentIncluded: String?
        set(value): Unit = set(195, value)
        get(): String? = get(195) as String?

    open var catvAdjustmentMonthly: Int?
        set(value): Unit = set(196, value)
        get(): Int? = get(196) as Int?

    open var otherAdjustmentDescription: String?
        set(value): Unit = set(197, value)
        get(): String? = get(197) as String?

    open var otherAdjustmentIncluded: String?
        set(value): Unit = set(198, value)
        get(): String? = get(198) as String?

    open var otherAdjustmentMonthly: Int?
        set(value): Unit = set(199, value)
        get(): Int? = get(199) as Int?

    open var businessNonLeaseAdjustmentIncluded: String?
        set(value): Unit = set(200, value)
        get(): String? = get(200) as String?

    open var businessNonLeaseAdjustmentMonthly: Int?
        set(value): Unit = set(201, value)
        get(): Int? = get(201) as Int?

    open var aboveDescription: String?
        set(value): Unit = set(202, value)
        get(): String? = get(202) as String?

    open var aboveIncluded: String?
        set(value): Unit = set(203, value)
        get(): String? = get(203) as String?

    open var aboveMonthly: Int?
        set(value): Unit = set(204, value)
        get(): Int? = get(204) as Int?

    open var aboveAdjustmentCategory: String?
        set(value): Unit = set(205, value)
        get(): String? = get(205) as String?

    open var subleaseRentalAssessmentTotal: Int?
        set(value): Unit = set(206, value)
        get(): Int? = get(206) as Int?

    open var rentalAdjustmentAmount: Int?
        set(value): Unit = set(207, value)
        get(): Int? = get(207) as Int?

    open var leaseRate: BigDecimal?
        set(value): Unit = set(208, value)
        get(): BigDecimal? = get(208) as BigDecimal?

    open var leaseRental: Int?
        set(value): Unit = set(209, value)
        get(): Int? = get(209) as Int?

    open var adjustmentAmount: Int?
        set(value): Unit = set(210, value)
        get(): Int? = get(210) as Int?

    open var adjustmentCategory: String?
        set(value): Unit = set(211, value)
        get(): String? = get(211) as String?

    open var leasePaymentRental: Int?
        set(value): Unit = set(212, value)
        get(): Int? = get(212) as Int?

    open var consumptionTax: Int?
        set(value): Unit = set(213, value)
        get(): Int? = get(213) as Int?

    open var contractBranchCd: String?
        set(value): Unit = set(214, value)
        get(): String? = get(214) as String?

    open var parkingAddressCd1: String?
        set(value): Unit = set(215, value)
        get(): String? = get(215) as String?

    open var parkingAddressCd2: String?
        set(value): Unit = set(216, value)
        get(): String? = get(216) as String?

    open var parkingAddressCd3: String?
        set(value): Unit = set(217, value)
        get(): String? = get(217) as String?

    open var parkingAddressDetail: String?
        set(value): Unit = set(218, value)
        get(): String? = get(218) as String?

    open var leaseAssessmentParkingSpaces: Short?
        set(value): Unit = set(219, value)
        get(): Short? = get(219) as Short?

    open var leaseNonAssessmentParkingSpaces: Short?
        set(value): Unit = set(220, value)
        get(): Short? = get(220) as Short?

    open var leasedResidentialUnits: Short?
        set(value): Unit = set(221, value)
        get(): Short? = get(221) as Short?

    open var leasedBusinessUnits: Short?
        set(value): Unit = set(222, value)
        get(): Short? = get(222) as Short?

    open var otherItem1: String?
        set(value): Unit = set(223, value)
        get(): String? = get(223) as String?

    open var otherAdjustment1: Int?
        set(value): Unit = set(224, value)
        get(): Int? = get(224) as Int?

    open var otherItem2: String?
        set(value): Unit = set(225, value)
        get(): String? = get(225) as String?

    open var otherAdjustment2: Int?
        set(value): Unit = set(226, value)
        get(): Int? = get(226) as Int?

    open var otherItem3: String?
        set(value): Unit = set(227, value)
        get(): String? = get(227) as String?

    open var otherAdjustment3: Int?
        set(value): Unit = set(228, value)
        get(): Int? = get(228) as Int?

    open var otherItem4: String?
        set(value): Unit = set(229, value)
        get(): String? = get(229) as String?

    open var otherAdjustment4: Int?
        set(value): Unit = set(230, value)
        get(): Int? = get(230) as Int?

    open var otherItem5: String?
        set(value): Unit = set(231, value)
        get(): String? = get(231) as String?

    open var otherAdjustment5: Int?
        set(value): Unit = set(232, value)
        get(): Int? = get(232) as Int?

    open var bulkSwitchSign: String?
        set(value): Unit = set(233, value)
        get(): String? = get(233) as String?

    open var switchPaymentMethod: Byte?
        set(value): Unit = set(234, value)
        get(): Byte? = get(234) as Byte?

    open var daitoBulkRoomParkingDbUpdateDate: Int?
        set(value): Unit = set(235, value)
        get(): Int? = get(235) as Int?

    open var switchType: String?
        set(value): Unit = set(236, value)
        get(): String? = get(236) as String?

    open var expirationPaymentMethod: Byte?
        set(value): Unit = set(237, value)
        get(): Byte? = get(237) as Byte?

    open var nonJoinedRoomsKyosai: Short?
        set(value): Unit = set(238, value)
        get(): Short? = get(238) as Short?

    open var joinedRoomsKyosai: Short?
        set(value): Unit = set(239, value)
        get(): Short? = get(239) as Short?

    open var managementOnlyContractOutput: Byte?
        set(value): Unit = set(240, value)
        get(): Byte? = get(240) as Byte?

    open var maintenanceShortfall: Int?
        set(value): Unit = set(241, value)
        get(): Int? = get(241) as Int?

    open var managementFeeRate: BigDecimal?
        set(value): Unit = set(242, value)
        get(): BigDecimal? = get(242) as BigDecimal?

    open var reserve: Int?
        set(value): Unit = set(243, value)
        get(): Int? = get(243) as Int?

    open var expirationType: String?
        set(value): Unit = set(244, value)
        get(): String? = get(244) as String?

    open var nonLeaseUseTr: String?
        set(value): Unit = set(245, value)
        get(): String? = get(245) as String?

    open var rentRevisionProcessingCategory: String?
        set(value): Unit = set(246, value)
        get(): String? = get(246) as String?

    open var changeContractTargetCategory: String?
        set(value): Unit = set(247, value)
        get(): String? = get(247) as String?

    open var confirmationOutputTargetCategory: String?
        set(value): Unit = set(248, value)
        get(): String? = get(248) as String?

    open var receptionCategory: String?
        set(value): Unit = set(249, value)
        get(): String? = get(249) as String?

    open var meterCount2: Short?
        set(value): Unit = set(250, value)
        get(): Short? = get(250) as Short?

    open var waterUsageIncluded2: String?
        set(value): Unit = set(251, value)
        get(): String? = get(251) as String?

    open var waterUsageMonthly2: Int?
        set(value): Unit = set(252, value)
        get(): Int? = get(252) as Int?

    open var maintenanceItemBIncluded2: String?
        set(value): Unit = set(253, value)
        get(): String? = get(253) as String?

    open var maintenanceItemBMonthly2: Int?
        set(value): Unit = set(254, value)
        get(): Int? = get(254) as Int?

    open var maintenanceItemAIncluded2: String?
        set(value): Unit = set(255, value)
        get(): String? = get(255) as String?

    open var maintenanceItemAMonthly2: Int?
        set(value): Unit = set(256, value)
        get(): Int? = get(256) as Int?

    open var rentAdjustmentIncluded: String?
        set(value): Unit = set(257, value)
        get(): String? = get(257) as String?

    open var rentAdjustmentMonthly: Int?
        set(value): Unit = set(258, value)
        get(): Int? = get(258) as Int?

    open var maintenanceFeeAdjustmentIncluded2: String?
        set(value): Unit = set(259, value)
        get(): String? = get(259) as String?

    open var maintenanceFeeAdjustmentMonthly2: Int?
        set(value): Unit = set(260, value)
        get(): Int? = get(260) as Int?

    open var repairFeeAdjustmentIncluded2: String?
        set(value): Unit = set(261, value)
        get(): String? = get(261) as String?

    open var repairFeeAdjustmentMonthly2: Int?
        set(value): Unit = set(262, value)
        get(): Int? = get(262) as Int?

    open var communityFeeAdjustmentIncluded2: String?
        set(value): Unit = set(263, value)
        get(): String? = get(263) as String?

    open var communityFeeAdjustmentMonthly2: Int?
        set(value): Unit = set(264, value)
        get(): Int? = get(264) as Int?

    open var catvAdjustmentIncluded2: String?
        set(value): Unit = set(265, value)
        get(): String? = get(265) as String?

    open var catvAdjustmentMonthly2: Int?
        set(value): Unit = set(266, value)
        get(): Int? = get(266) as Int?

    open var otherAdjustmentDescription2: String?
        set(value): Unit = set(267, value)
        get(): String? = get(267) as String?

    open var otherAdjustmentIncluded2: String?
        set(value): Unit = set(268, value)
        get(): String? = get(268) as String?

    open var otherAdjustmentMonthly2: Int?
        set(value): Unit = set(269, value)
        get(): Int? = get(269) as Int?

    open var businessNonLeaseAdjustmentIncluded2: String?
        set(value): Unit = set(270, value)
        get(): String? = get(270) as String?

    open var businessNonLeaseAdjustmentMonthly2: Int?
        set(value): Unit = set(271, value)
        get(): Int? = get(271) as Int?

    open var aboveDescription2: String?
        set(value): Unit = set(272, value)
        get(): String? = get(272) as String?

    open var aboveIncluded2: String?
        set(value): Unit = set(273, value)
        get(): String? = get(273) as String?

    open var aboveMonthly2: Int?
        set(value): Unit = set(274, value)
        get(): Int? = get(274) as Int?

    open var aboveAdjustmentCategory2: String?
        set(value): Unit = set(275, value)
        get(): String? = get(275) as String?

    open var subleaseRentAssessmentTotal: Int?
        set(value): Unit = set(276, value)
        get(): Int? = get(276) as Int?

    open var rentAdjustmentAmount: Int?
        set(value): Unit = set(277, value)
        get(): Int? = get(277) as Int?

    open var leaseRate2: BigDecimal?
        set(value): Unit = set(278, value)
        get(): BigDecimal? = get(278) as BigDecimal?

    open var leaseRental2: Int?
        set(value): Unit = set(279, value)
        get(): Int? = get(279) as Int?

    open var adjustmentAmount2: Int?
        set(value): Unit = set(280, value)
        get(): Int? = get(280) as Int?

    open var adjustmentCategory2: String?
        set(value): Unit = set(281, value)
        get(): String? = get(281) as String?

    open var leasePaymentRental2: Int?
        set(value): Unit = set(282, value)
        get(): Int? = get(282) as Int?

    open var consumptionTax2: Int?
        set(value): Unit = set(283, value)
        get(): Int? = get(283) as Int?

    open var maintenanceShortfall2: Int?
        set(value): Unit = set(284, value)
        get(): Int? = get(284) as Int?

    open var managementFeeRate2: BigDecimal?
        set(value): Unit = set(285, value)
        get(): BigDecimal? = get(285) as BigDecimal?

    open var preRevisionMinimumParkingFee: Int?
        set(value): Unit = set(286, value)
        get(): Int? = get(286) as Int?

    open var preRevisionParkingCount: Short?
        set(value): Unit = set(287, value)
        get(): Short? = get(287) as Short?

    open var postRevisionMinimumParkingFee: Int?
        set(value): Unit = set(288, value)
        get(): Int? = get(288) as Int?

    open var postRevisionParkingCount: Short?
        set(value): Unit = set(289, value)
        get(): Short? = get(289) as Short?

    open var subleaseRateUnder_10: BigDecimal?
        set(value): Unit = set(290, value)
        get(): BigDecimal? = get(290) as BigDecimal?

    open var subleaseRateUnder_20: BigDecimal?
        set(value): Unit = set(291, value)
        get(): BigDecimal? = get(291) as BigDecimal?

    open var subleaseRate_20AndAbove: BigDecimal?
        set(value): Unit = set(292, value)
        get(): BigDecimal? = get(292) as BigDecimal?

    open var vacancyRateUnder_10: BigDecimal?
        set(value): Unit = set(293, value)
        get(): BigDecimal? = get(293) as BigDecimal?

    open var vacancyRateUnder_20: BigDecimal?
        set(value): Unit = set(294, value)
        get(): BigDecimal? = get(294) as BigDecimal?

    open var vacancyRate_20AndAbove: BigDecimal?
        set(value): Unit = set(295, value)
        get(): BigDecimal? = get(295) as BigDecimal?

    open var maintenanceRequiredAmount: Int?
        set(value): Unit = set(296, value)
        get(): Int? = get(296) as Int?

    open var leaseRoomMaintenanceFee: Int?
        set(value): Unit = set(297, value)
        get(): Int? = get(297) as Int?

    open var managedRoomMaintenanceFee: Int?
        set(value): Unit = set(298, value)
        get(): Int? = get(298) as Int?

    open var ownerDelayRoomMaintenanceFee: Int?
        set(value): Unit = set(299, value)
        get(): Int? = get(299) as Int?

    open var maintenanceShortfall3: Int?
        set(value): Unit = set(300, value)
        get(): Int? = get(300) as Int?

    open var generalApplicationNo1: String?
        set(value): Unit = set(301, value)
        get(): String? = get(301) as String?

    open var generalApplicationNo2: Byte?
        set(value): Unit = set(302, value)
        get(): Byte? = get(302) as Byte?

    open var generalApplicationNo3: Short?
        set(value): Unit = set(303, value)
        get(): Short? = get(303) as Short?

    open var rentPrepaymentConfirmationCategory: String?
        set(value): Unit = set(304, value)
        get(): String? = get(304) as String?

    open var checkSheetConfirmation: String?
        set(value): Unit = set(305, value)
        get(): String? = get(305) as String?

    open var contentConfirmationDate: Int?
        set(value): Unit = set(306, value)
        get(): Int? = get(306) as Int?

    open var maintenanceContentConfirmationDate: Int?
        set(value): Unit = set(307, value)
        get(): Int? = get(307) as Int?

    open var consumptionTaxCalculationBaseDate: Int?
        set(value): Unit = set(308, value)
        get(): Int? = get(308) as Int?

    open var consumptionTaxRate: BigDecimal?
        set(value): Unit = set(309, value)
        get(): BigDecimal? = get(309) as BigDecimal?

    open var consumptionTaxRevisionFlag: Byte?
        set(value): Unit = set(310, value)
        get(): Byte? = get(310) as Byte?

    open var parkingTotalSpaces: Short?
        set(value): Unit = set(311, value)
        get(): Short? = get(311) as Short?

    open var leaseSpaces: Short?
        set(value): Unit = set(312, value)
        get(): Short? = get(312) as Short?

    open var nonLeaseSpaces: Short?
        set(value): Unit = set(313, value)
        get(): Short? = get(313) as Short?

    open var subleaseParkingFeeTotal: Int?
        set(value): Unit = set(314, value)
        get(): Int? = get(314) as Int?

    open var parkingFeeAdjustmentAmount: Int?
        set(value): Unit = set(315, value)
        get(): Int? = get(315) as Int?

    open var standardParkingFee: Int?
        set(value): Unit = set(316, value)
        get(): Int? = get(316) as Int?

    open var leaseAssessmentParkingFee: Int?
        set(value): Unit = set(317, value)
        get(): Int? = get(317) as Int?

    open var leaseRate3: BigDecimal?
        set(value): Unit = set(318, value)
        get(): BigDecimal? = get(318) as BigDecimal?

    open var leaseParkingFee: Int?
        set(value): Unit = set(319, value)
        get(): Int? = get(319) as Int?

    open var includedConsumptionTax: Int?
        set(value): Unit = set(320, value)
        get(): Int? = get(320) as Int?

    open var rentIncreaseCategory: String?
        set(value): Unit = set(321, value)
        get(): String? = get(321) as String?

    open var noteCode: String?
        set(value): Unit = set(322, value)
        get(): String? = get(322) as String?

    /**
     * Create a detached, initialised TemporaryContractRecord
     */
    constructor(value: TemporaryContractPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creationProgramId = value.creationProgramId
            this.creationTerminalId = value.creationTerminalId
            this.creationResponsibleCd = value.creationResponsibleCd
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updateTerminalId = value.updateTerminalId
            this.updateResponsibleCd = value.updateResponsibleCd
            this.logicalDeleteFlag = value.logicalDeleteFlag
            this.buildingCd = value.buildingCd
            this.effectiveStartDate = value.effectiveStartDate
            this.effectiveEndDate = value.effectiveEndDate
            this.contractType = value.contractType
            this.dataManagementNo = value.dataManagementNo
            this.conclusionCategory = value.conclusionCategory
            this.managementContractStartDate = value.managementContractStartDate
            this.managementContractEndDate = value.managementContractEndDate
            this.contractOutputManagementNo = value.contractOutputManagementNo
            this.confirmProofOutputDate = value.confirmProofOutputDate
            this.contractCollectionInputDate = value.contractCollectionInputDate
            this.contractApprovalCategory = value.contractApprovalCategory
            this.contractApprover = value.contractApprover
            this.contractApprovalDate = value.contractApprovalDate
            this.contractOutputDate = value.contractOutputDate
            this.contractConclusionCategory = value.contractConclusionCategory
            this.conclusionInputDate = value.conclusionInputDate
            this.managementContractExpectedDate = value.managementContractExpectedDate
            this.managementContractDate = value.managementContractDate
            this.headOfficeApplicationCategory = value.headOfficeApplicationCategory
            this.headOfficeReceptionCategory = value.headOfficeReceptionCategory
            this.headOfficeReceptionDate = value.headOfficeReceptionDate
            this.nonStandardApprovalDate = value.nonStandardApprovalDate
            this.nonStandardApprover = value.nonStandardApprover
            this.nonStandardApplication = value.nonStandardApplication
            this.agreementTerminationDate = value.agreementTerminationDate
            this.agreementTerminationDate2 = value.agreementTerminationDate2
            this.agreementTerminationReason = value.agreementTerminationReason
            this.nameChangeProgressNo = value.nameChangeProgressNo
            this.nameChangeReasonCategory = value.nameChangeReasonCategory
            this.notificationNo = value.notificationNo
            this.agreementRegistrationNo = value.agreementRegistrationNo
            this.oldAgreementNo = value.oldAgreementNo
            this.managementDelegatorCd = value.managementDelegatorCd
            this.managementDelegatorName = value.managementDelegatorName
            this.delegatorPostalCode = value.delegatorPostalCode
            this.delegatorAddressCd1 = value.delegatorAddressCd1
            this.delegatorAddressCd2 = value.delegatorAddressCd2
            this.delegatorAddressCd3 = value.delegatorAddressCd3
            this.delegatorAddressDetail = value.delegatorAddressDetail
            this.delegatorBuildingName = value.delegatorBuildingName
            this.delegatorPhoneNo = value.delegatorPhoneNo
            this.ownerTaxCategory = value.ownerTaxCategory
            this.constructionCategory = value.constructionCategory
            this.buildingType = value.buildingType
            this.roomPurpose1Business = value.roomPurpose1Business
            this.roomPurpose2Residential = value.roomPurpose2Residential
            this.roomPurpose3Parking = value.roomPurpose3Parking
            this.roomPurpose4Tr = value.roomPurpose4Tr
            this.roomPurpose5Other = value.roomPurpose5Other
            this.loanCategory = value.loanCategory
            this.totalUnitsBusiness = value.totalUnitsBusiness
            this.totalUnitsResidential = value.totalUnitsResidential
            this.totalParkingUnits = value.totalParkingUnits
            this.managedUnitsBusiness = value.managedUnitsBusiness
            this.managedUnitsResidential = value.managedUnitsResidential
            this.managedParkingUnits = value.managedParkingUnits
            this.contractForm = value.contractForm
            this.managementCategory = value.managementCategory
            this.managementPartnership = value.managementPartnership
            this.partnershipType = value.partnershipType
            this.managementPartner = value.managementPartner
            this.maintenancePartner = value.maintenancePartner
            this.detailsIssue = value.detailsIssue
            this.managementFormCategory = value.managementFormCategory
            this.specialRent = value.specialRent
            this.comCommunityPartnership = value.comCommunityPartnership
            this.proRataDays = value.proRataDays
            this.excludedProRataDays = value.excludedProRataDays
            this.nonStandardFlag = value.nonStandardFlag
            this.newOwnerNameAtNameChange = value.newOwnerNameAtNameChange
            this.ownerAddressAtNameChange = value.ownerAddressAtNameChange
            this.ownerTransferAccountOwnerCd = value.ownerTransferAccountOwnerCd
            this.detailsConsolidationUnit = value.detailsConsolidationUnit
            this.transferAccountDivision = value.transferAccountDivision
            this.recipientCd = value.recipientCd
            this.previousRecipientCd = value.previousRecipientCd
            this.renewalFeeAcquisition = value.renewalFeeAcquisition
            this.renewalFeeAcquisitionMonths = value.renewalFeeAcquisitionMonths
            this.renewalFeeCommissionRate = value.renewalFeeCommissionRate
            this.guarantorNotRequiredApprovalFlag = value.guarantorNotRequiredApprovalFlag
            this.fixedTermRentalContractConclusion = value.fixedTermRentalContractConclusion
            this.waterFeeManagementFeeCollection = value.waterFeeManagementFeeCollection
            this.waterMeterCategory = value.waterMeterCategory
            this.gasCategory = value.gasCategory
            this.sharedOwnershipCd1 = value.sharedOwnershipCd1
            this.delegatorSharedInterest1 = value.delegatorSharedInterest1
            this.sharedOwnershipCd2 = value.sharedOwnershipCd2
            this.delegatorSharedInterest2 = value.delegatorSharedInterest2
            this.sharedOwnershipCd3 = value.sharedOwnershipCd3
            this.delegatorSharedInterest3 = value.delegatorSharedInterest3
            this.waterReadingCategory = value.waterReadingCategory
            this.waterFeeCollection = value.waterFeeCollection
            this.depositHandlingCategory = value.depositHandlingCategory
            this.communityFeeManagementCollection = value.communityFeeManagementCollection
            this.communityFeeManagementPayment = value.communityFeeManagementPayment
            this.gasReadingCategory = value.gasReadingCategory
            this.petsAllowedCategory = value.petsAllowedCategory
            this.rentTransferAccountCategory = value.rentTransferAccountCategory
            this.monthlyRentTransferAccount = value.monthlyRentTransferAccount
            this.commonFeeTransferAccount = value.commonFeeTransferAccount
            this.commonFeeTransferAccountCd = value.commonFeeTransferAccountCd
            this.exclusiveBrokeragePeriod = value.exclusiveBrokeragePeriod
            this.mutualAidAssociationEnrollment = value.mutualAidAssociationEnrollment
            this.depositSettlementMethod = value.depositSettlementMethod
            this.depositManagementDelegatorRate = value.depositManagementDelegatorRate
            this.departureManagementServiceTermination = value.departureManagementServiceTermination
            this.buildingInspectionServiceSitePatrol = value.buildingInspectionServiceSitePatrol
            this.vacantRoomManagementService = value.vacantRoomManagementService
            this.waterFeeReading = value.waterFeeReading
            this.commonEquipmentMaintenance = value.commonEquipmentMaintenance
            this.otherMaintenance = value.otherMaintenance
            this.maintenanceFeeCollectionTarget = value.maintenanceFeeCollectionTarget
            this.maintenanceFeeUnit = value.maintenanceFeeUnit
            this.maintenanceFeeTotal = value.maintenanceFeeTotal
            this.maintenanceFeeDaitoShare = value.maintenanceFeeDaitoShare
            this.tenantSettlementService = value.tenantSettlementService
            this.tenantSettlementMngFeeDeduction = value.tenantSettlementMngFeeDeduction
            this.departureSettlementService = value.departureSettlementService
            this.salesDepartAchieveAccountingCategory = value.salesDepartAchieveAccountingCategory
            this.moveInOutManagement = value.moveInOutManagement
            this.mngDepartmentLeaseContractInput = value.mngDepartmentLeaseContractInput
            this.contractCategory = value.contractCategory
            this.siteArea = value.siteArea
            this.otherPurposes = value.otherPurposes
            this.otherPurposesContent = value.otherPurposesContent
            this.specialContractNo = value.specialContractNo
            this.additionalTask1 = value.additionalTask1
            this.additionalTask2 = value.additionalTask2
            this.managementBranchPhoneNo = value.managementBranchPhoneNo
            this.managementContractTrInitial = value.managementContractTrInitial
            this.managementContractTrNext = value.managementContractTrNext
            this.specialClausesIncluded = value.specialClausesIncluded
            this.maintenanceItemsIncluded = value.maintenanceItemsIncluded
            this.managementDelegationDataIncluded = value.managementDelegationDataIncluded
            this.mngDelegationContentDataIncluded = value.mngDelegationContentDataIncluded
            this.agreementNoChangeManagementNo = value.agreementNoChangeManagementNo
            this.transferDate = value.transferDate
            this.recordNewOldCategory = value.recordNewOldCategory
            this.initialSetupFlag = value.initialSetupFlag
            this.contractEndDate = value.contractEndDate
            this.operationStartDate = value.operationStartDate
            this.maintenanceServiceCategory = value.maintenanceServiceCategory
            this.simultaneousContractOutput = value.simultaneousContractOutput
            this.outputControlCategory = value.outputControlCategory
            this.managementStartExpectedDate = value.managementStartExpectedDate
            this.autoCreationCategory = value.autoCreationCategory
            this.maintenanceDelegationCreationCategory = value.maintenanceDelegationCreationCategory
            this.previousConclusionDate = value.previousConclusionDate
            this.mainteDelegationOnlyUpdateCategory = value.mainteDelegationOnlyUpdateCategory
            this.mainteDelegationContractOutputTarget = value.mainteDelegationContractOutputTarget
            this.mainteConclusionExpectedDate = value.mainteConclusionExpectedDate
            this.mainteConclusionDate = value.mainteConclusionDate
            this.conclusionExpectedDateRequired = value.conclusionExpectedDateRequired
            this.houseComStoreCd = value.houseComStoreCd
            this.mngStartDateChangeContractChange = value.mngStartDateChangeContractChange
            this.businessNewGuaranteeCategory = value.businessNewGuaranteeCategory
            this.repairSpecialClause = value.repairSpecialClause
            this.repairSpecialClausePeriod = value.repairSpecialClausePeriod
            this.inputManagementNo = value.inputManagementNo
            this.nonLeaseUseResidential = value.nonLeaseUseResidential
            this.nonLeaseUseBusiness = value.nonLeaseUseBusiness
            this.nonLeaseUseParking = value.nonLeaseUseParking
            this.buildingStructure = value.buildingStructure
            this.buildingFloors = value.buildingFloors
            this.buildingAddressCd1 = value.buildingAddressCd1
            this.buildingAddressCd2 = value.buildingAddressCd2
            this.buildingAddressCd3 = value.buildingAddressCd3
            this.buildingAddressDetail = value.buildingAddressDetail
            this.meterCount = value.meterCount
            this.waterUsageIncluded = value.waterUsageIncluded
            this.waterUsageMonthly = value.waterUsageMonthly
            this.maintenanceItemBIncluded = value.maintenanceItemBIncluded
            this.maintenanceItemBMonthly = value.maintenanceItemBMonthly
            this.maintenanceItemAIncluded = value.maintenanceItemAIncluded
            this.maintenanceItemAMonthly = value.maintenanceItemAMonthly
            this.rentalAdjustmentIncluded = value.rentalAdjustmentIncluded
            this.rentalAdjustmentMonthly = value.rentalAdjustmentMonthly
            this.maintenanceFeeAdjustmentIncluded = value.maintenanceFeeAdjustmentIncluded
            this.maintenanceFeeAdjustmentMonthly = value.maintenanceFeeAdjustmentMonthly
            this.repairFeeAdjustmentIncluded = value.repairFeeAdjustmentIncluded
            this.repairFeeAdjustmentMonthly = value.repairFeeAdjustmentMonthly
            this.communityFeeAdjustmentIncluded = value.communityFeeAdjustmentIncluded
            this.communityFeeAdjustmentMonthly = value.communityFeeAdjustmentMonthly
            this.catvAdjustmentIncluded = value.catvAdjustmentIncluded
            this.catvAdjustmentMonthly = value.catvAdjustmentMonthly
            this.otherAdjustmentDescription = value.otherAdjustmentDescription
            this.otherAdjustmentIncluded = value.otherAdjustmentIncluded
            this.otherAdjustmentMonthly = value.otherAdjustmentMonthly
            this.businessNonLeaseAdjustmentIncluded = value.businessNonLeaseAdjustmentIncluded
            this.businessNonLeaseAdjustmentMonthly = value.businessNonLeaseAdjustmentMonthly
            this.aboveDescription = value.aboveDescription
            this.aboveIncluded = value.aboveIncluded
            this.aboveMonthly = value.aboveMonthly
            this.aboveAdjustmentCategory = value.aboveAdjustmentCategory
            this.subleaseRentalAssessmentTotal = value.subleaseRentalAssessmentTotal
            this.rentalAdjustmentAmount = value.rentalAdjustmentAmount
            this.leaseRate = value.leaseRate
            this.leaseRental = value.leaseRental
            this.adjustmentAmount = value.adjustmentAmount
            this.adjustmentCategory = value.adjustmentCategory
            this.leasePaymentRental = value.leasePaymentRental
            this.consumptionTax = value.consumptionTax
            this.contractBranchCd = value.contractBranchCd
            this.parkingAddressCd1 = value.parkingAddressCd1
            this.parkingAddressCd2 = value.parkingAddressCd2
            this.parkingAddressCd3 = value.parkingAddressCd3
            this.parkingAddressDetail = value.parkingAddressDetail
            this.leaseAssessmentParkingSpaces = value.leaseAssessmentParkingSpaces
            this.leaseNonAssessmentParkingSpaces = value.leaseNonAssessmentParkingSpaces
            this.leasedResidentialUnits = value.leasedResidentialUnits
            this.leasedBusinessUnits = value.leasedBusinessUnits
            this.otherItem1 = value.otherItem1
            this.otherAdjustment1 = value.otherAdjustment1
            this.otherItem2 = value.otherItem2
            this.otherAdjustment2 = value.otherAdjustment2
            this.otherItem3 = value.otherItem3
            this.otherAdjustment3 = value.otherAdjustment3
            this.otherItem4 = value.otherItem4
            this.otherAdjustment4 = value.otherAdjustment4
            this.otherItem5 = value.otherItem5
            this.otherAdjustment5 = value.otherAdjustment5
            this.bulkSwitchSign = value.bulkSwitchSign
            this.switchPaymentMethod = value.switchPaymentMethod
            this.daitoBulkRoomParkingDbUpdateDate = value.daitoBulkRoomParkingDbUpdateDate
            this.switchType = value.switchType
            this.expirationPaymentMethod = value.expirationPaymentMethod
            this.nonJoinedRoomsKyosai = value.nonJoinedRoomsKyosai
            this.joinedRoomsKyosai = value.joinedRoomsKyosai
            this.managementOnlyContractOutput = value.managementOnlyContractOutput
            this.maintenanceShortfall = value.maintenanceShortfall
            this.managementFeeRate = value.managementFeeRate
            this.reserve = value.reserve
            this.expirationType = value.expirationType
            this.nonLeaseUseTr = value.nonLeaseUseTr
            this.rentRevisionProcessingCategory = value.rentRevisionProcessingCategory
            this.changeContractTargetCategory = value.changeContractTargetCategory
            this.confirmationOutputTargetCategory = value.confirmationOutputTargetCategory
            this.receptionCategory = value.receptionCategory
            this.meterCount2 = value.meterCount2
            this.waterUsageIncluded2 = value.waterUsageIncluded2
            this.waterUsageMonthly2 = value.waterUsageMonthly2
            this.maintenanceItemBIncluded2 = value.maintenanceItemBIncluded2
            this.maintenanceItemBMonthly2 = value.maintenanceItemBMonthly2
            this.maintenanceItemAIncluded2 = value.maintenanceItemAIncluded2
            this.maintenanceItemAMonthly2 = value.maintenanceItemAMonthly2
            this.rentAdjustmentIncluded = value.rentAdjustmentIncluded
            this.rentAdjustmentMonthly = value.rentAdjustmentMonthly
            this.maintenanceFeeAdjustmentIncluded2 = value.maintenanceFeeAdjustmentIncluded2
            this.maintenanceFeeAdjustmentMonthly2 = value.maintenanceFeeAdjustmentMonthly2
            this.repairFeeAdjustmentIncluded2 = value.repairFeeAdjustmentIncluded2
            this.repairFeeAdjustmentMonthly2 = value.repairFeeAdjustmentMonthly2
            this.communityFeeAdjustmentIncluded2 = value.communityFeeAdjustmentIncluded2
            this.communityFeeAdjustmentMonthly2 = value.communityFeeAdjustmentMonthly2
            this.catvAdjustmentIncluded2 = value.catvAdjustmentIncluded2
            this.catvAdjustmentMonthly2 = value.catvAdjustmentMonthly2
            this.otherAdjustmentDescription2 = value.otherAdjustmentDescription2
            this.otherAdjustmentIncluded2 = value.otherAdjustmentIncluded2
            this.otherAdjustmentMonthly2 = value.otherAdjustmentMonthly2
            this.businessNonLeaseAdjustmentIncluded2 = value.businessNonLeaseAdjustmentIncluded2
            this.businessNonLeaseAdjustmentMonthly2 = value.businessNonLeaseAdjustmentMonthly2
            this.aboveDescription2 = value.aboveDescription2
            this.aboveIncluded2 = value.aboveIncluded2
            this.aboveMonthly2 = value.aboveMonthly2
            this.aboveAdjustmentCategory2 = value.aboveAdjustmentCategory2
            this.subleaseRentAssessmentTotal = value.subleaseRentAssessmentTotal
            this.rentAdjustmentAmount = value.rentAdjustmentAmount
            this.leaseRate2 = value.leaseRate2
            this.leaseRental2 = value.leaseRental2
            this.adjustmentAmount2 = value.adjustmentAmount2
            this.adjustmentCategory2 = value.adjustmentCategory2
            this.leasePaymentRental2 = value.leasePaymentRental2
            this.consumptionTax2 = value.consumptionTax2
            this.maintenanceShortfall2 = value.maintenanceShortfall2
            this.managementFeeRate2 = value.managementFeeRate2
            this.preRevisionMinimumParkingFee = value.preRevisionMinimumParkingFee
            this.preRevisionParkingCount = value.preRevisionParkingCount
            this.postRevisionMinimumParkingFee = value.postRevisionMinimumParkingFee
            this.postRevisionParkingCount = value.postRevisionParkingCount
            this.subleaseRateUnder_10 = value.subleaseRateUnder_10
            this.subleaseRateUnder_20 = value.subleaseRateUnder_20
            this.subleaseRate_20AndAbove = value.subleaseRate_20AndAbove
            this.vacancyRateUnder_10 = value.vacancyRateUnder_10
            this.vacancyRateUnder_20 = value.vacancyRateUnder_20
            this.vacancyRate_20AndAbove = value.vacancyRate_20AndAbove
            this.maintenanceRequiredAmount = value.maintenanceRequiredAmount
            this.leaseRoomMaintenanceFee = value.leaseRoomMaintenanceFee
            this.managedRoomMaintenanceFee = value.managedRoomMaintenanceFee
            this.ownerDelayRoomMaintenanceFee = value.ownerDelayRoomMaintenanceFee
            this.maintenanceShortfall3 = value.maintenanceShortfall3
            this.generalApplicationNo1 = value.generalApplicationNo1
            this.generalApplicationNo2 = value.generalApplicationNo2
            this.generalApplicationNo3 = value.generalApplicationNo3
            this.rentPrepaymentConfirmationCategory = value.rentPrepaymentConfirmationCategory
            this.checkSheetConfirmation = value.checkSheetConfirmation
            this.contentConfirmationDate = value.contentConfirmationDate
            this.maintenanceContentConfirmationDate = value.maintenanceContentConfirmationDate
            this.consumptionTaxCalculationBaseDate = value.consumptionTaxCalculationBaseDate
            this.consumptionTaxRate = value.consumptionTaxRate
            this.consumptionTaxRevisionFlag = value.consumptionTaxRevisionFlag
            this.parkingTotalSpaces = value.parkingTotalSpaces
            this.leaseSpaces = value.leaseSpaces
            this.nonLeaseSpaces = value.nonLeaseSpaces
            this.subleaseParkingFeeTotal = value.subleaseParkingFeeTotal
            this.parkingFeeAdjustmentAmount = value.parkingFeeAdjustmentAmount
            this.standardParkingFee = value.standardParkingFee
            this.leaseAssessmentParkingFee = value.leaseAssessmentParkingFee
            this.leaseRate3 = value.leaseRate3
            this.leaseParkingFee = value.leaseParkingFee
            this.includedConsumptionTax = value.includedConsumptionTax
            this.rentIncreaseCategory = value.rentIncreaseCategory
            this.noteCode = value.noteCode
            resetChangedOnNotNull()
        }
    }
}
