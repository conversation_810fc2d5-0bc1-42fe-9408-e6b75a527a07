/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable
import java.math.BigDecimal


/**
 * テナント契約 既存システム物理名: ECB20P
 */
@Suppress("UNCHECKED_CAST")
data class TenantContractPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updater: String? = null,
    var logicalDeleteSign: Byte? = null,
    var tenantContractNumber: String,
    var tenantContractChangeSeq: String,
    var contractContentDivision: String? = null,
    var buildingCode: String? = null,
    var roomCode: String? = null,
    var parkingCode: String? = null,
    var aggregateContractNumber: String? = null,
    var aggregateContractChangeSeq: String? = null,
    var parkingSpaces: Short? = null,
    var bulkLeaseSign: Byte? = null,
    var tenantType: String? = null,
    var tenantProspectNumber: String? = null,
    var tenantCode: String? = null,
    var searchKana: String? = null,
    var infoAcquisitionDivision: String? = null,
    var hotInfoReceiptNumber: String? = null,
    var landlordCode_10: String? = null,
    var taxDivision: String? = null,
    var searchKana2: String? = null,
    var name: String? = null,
    var prefectureCode: String? = null,
    var cityCode: String? = null,
    var townCode: String? = null,
    var addressDetail: String? = null,
    var buildingName: String? = null,
    var phoneNumber: String? = null,
    var importantDisclosureOutputCount: Byte? = null,
    var importantDisclosureOutputDate: Int? = null,
    var importantDisclosureCollectionDate: Int? = null,
    var importantDisclosureDate: Int? = null,
    var importantDisclosureAgentCode: String? = null,
    var importantDisclosureDivision: String? = null,
    var paymentDate: Int? = null,
    var moveInApplicationDate: Int? = null,
    var moveInScheduledDate: Int? = null,
    var usagePurpose: String? = null,
    var moveInApplicationFee: Int? = null,
    var paymentMethodDivision: String? = null,
    var mobileReceiptNumber: String? = null,
    var reservationApplicationFeeAppliedAmount: Int? = null,
    var tenantName: String? = null,
    var contactDivision: String? = null,
    var contact: String? = null,
    var phoneNumber2: String? = null,
    var remarks: String? = null,
    var name2: String? = null,
    var prefectureCode2: String? = null,
    var cityCode2: String? = null,
    var townCode2: String? = null,
    var addressDetail2: String? = null,
    var buildingName2: String? = null,
    var phoneNumber3: String? = null,
    var relationship: String? = null,
    var name3: String? = null,
    var prefectureCode3: String? = null,
    var cityCode3: String? = null,
    var townCode3: String? = null,
    var addressDetail3: String? = null,
    var buildingName3: String? = null,
    var phoneNumber4: String? = null,
    var relationship2: String? = null,
    var cohabitantName1: String? = null,
    var cohabitantAge1: Short? = null,
    var cohabitantRelationship1: String? = null,
    var cohabitantName2: String? = null,
    var cohabitantAge2: Short? = null,
    var cohabitantRelationship2: String? = null,
    var cohabitantName3: String? = null,
    var agentShozokuBranch: Short? = null,
    var cohabitantRelationship3: String? = null,
    var cohabitantName4: String? = null,
    var agentShozokuStore: Short? = null,
    var cohabitantRelationship4: String? = null,
    var cohabitantName5: String? = null,
    var agentShozokuOffice: Short? = null,
    var cohabitantRelationship5: String? = null,
    var landlordApprovalDate: Int? = null,
    var depositChangeDate: Int? = null,
    var agencyMemberNumber: String? = null,
    var contractPeriodDivision: String? = null,
    var reservationContractSign: Byte? = null,
    var tenantReservationNumber: String? = null,
    var leaseContractOutputContentDivision: String? = null,
    var standardRent: Int? = null,
    var leaseContractOutputCount: Byte? = null,
    var leaseContractLatestOutputDate: Int? = null,
    var leaseContractCollectionDate: Int? = null,
    var leaseContractDate: Int? = null,
    var contractStartDate: Int? = null,
    var contractExpiryDate: Int? = null,
    var nextRentRevisionScheduledDate: Int? = null,
    var rentRevisionPeriod: Short? = null,
    var frontFreeRentDays: BigDecimal? = null,
    var depositMonths: BigDecimal? = null,
    var depreciationMonths: BigDecimal? = null,
    var keyMoneyAmount: Int? = null,
    var keyMoneyDivision: String? = null,
    var additionalKeyMoneyAmount: Int? = null,
    var depositAmount: Int? = null,
    var depreciation: Int? = null,
    var rent: Int? = null,
    var rentDivision: String? = null,
    var parkingFeeAggregationSign: Byte? = null,
    var parkingFee: Int? = null,
    var parkingFeeDivision: String? = null,
    var commonServiceFee: Int? = null,
    var commonServiceFeeDivision: String? = null,
    var neighborhoodAssociationFee: Int? = null,
    var additionalDepositAmount: Int? = null,
    var paymentScheduleCreationSign: Byte? = null,
    var paymentMethodDivision2: String? = null,
    var differenceProratedDays: Short? = null,
    var differenceProratedRentAmount: Int? = null,
    var differenceProratedParkingFee: Int? = null,
    var differenceProratedManagementFee: Int? = null,
    var differenceProratedCommonServiceFee: Int? = null,
    var differenceProratedAssociationFee: Int? = null,
    var differenceProratedWaterManagementFee: Int? = null,
    var differenceMonths: Byte? = null,
    var monthlyDifferenceRentAmount: Int? = null,
    var monthlyDifferenceParkingFee: Int? = null,
    var monthlyDifferenceCommonServiceFee: Int? = null,
    var monthlyDifferenceManagementFee: Int? = null,
    var monthlyDifferenceAssociationFee: Int? = null,
    var monthlyDifferenceWaterManagementFee: Int? = null,
    var paymentScheduleCreationSign2: Byte? = null,
    var differenceRentCollectionMethod: String? = null,
    var rentCollectionMethodDivision: String? = null,
    var agencyDivision: String? = null,
    var bankCode: String? = null,
    var bankBranchCode: String? = null,
    var accountType: String? = null,
    var accountNumber: String? = null,
    var accountHolderNameKana: String? = null,
    var accountHolderNameKanji: String? = null,
    var initialChangeEffectiveDate: Int? = null,
    var bankCode2: String? = null,
    var bankBranchCode2: String? = null,
    var accountType2: String? = null,
    var accountNumber2: String? = null,
    var accountHolderNameKana2: String? = null,
    var accountHolderNameKanji2: String? = null,
    var directDebitTargetDivision: String? = null,
    var directDebitAmount: Int? = null,
    var leaseContractTransactingAgent: String? = null,
    var contractEffectiveStartDate: Int? = null,
    var contractEffectiveEndDate: Int? = null,
    var frontFreeRentSign: Byte? = null,
    var frontFreeRentAmount: Int? = null,
    var frontFreeRentMonths: Short? = null,
    var landlordAdvancePaymentAllocationAmount: Int? = null,
    var landlordAdvancePayment: Int? = null,
    var mngContractInitialPaymentAmount: Int? = null,
    var mngContractInitialPaymentAmountTax: Int? = null,
    var associationEntryFeeProcess: Int? = null,
    var registrationFeeAmount: Int? = null,
    var registrationFeeAmountTax: Int? = null,
    var mngContractInitialPaymentTransferred: Byte? = null,
    var brokerageFeeExemptionDivision: String? = null,
    var rentManagementStartDate: Int? = null,
    var notarizedDocPaymentSign: Byte? = null,
    var moveInCalculationOutputDate: Int? = null,
    var remainingRent: Int? = null,
    var remainingRentTax: Int? = null,
    var remainingParkingFee: Int? = null,
    var remainingParkingFeeTax: Int? = null,
    var remainingCommonServiceFee: Int? = null,
    var remainingCommonServiceFeeTax: Int? = null,
    var remainingNeighborhoodFee: Int? = null,
    var remainingMonths: Short? = null,
    var proratedRent: Int? = null,
    var proratedRentTax: Int? = null,
    var proratedParking: Int? = null,
    var proratedParkingFeeTax: Int? = null,
    var proratedCommonServiceFee: Int? = null,
    var proratedCommonServiceFeeTax: Int? = null,
    var proratedNeighborhoodFee: Int? = null,
    var keyMoneyAmount2: Int? = null,
    var keyMoneyTax: Int? = null,
    var depositAmount2: Int? = null,
    var monthlyManagementFee: Int? = null,
    var notarizedDocCreationCost: Int? = null,
    var stampFee: Int? = null,
    var interiorCooperationFee: Int? = null,
    var interiorCooperationFeeTax: Int? = null,
    var incomeCommissionFeeTenant: Int? = null,
    var incomeCommissionFeeTax: Int? = null,
    var depositCommissionFee: Int? = null,
    var depositCommissionFeeTax: Int? = null,
    var brokerageFee: Int? = null,
    var brokerageFeeTax: Int? = null,
    var outsourcedAdvertisingFee: Int? = null,
    var outsourcedAdvertisingFeeTax: Int? = null,
    var paymentSign: Byte? = null,
    var brokerCode: String? = null,
    var brokerageFeeBreakdownDivision: String? = null,
    var remainingAmount: Long? = null,
    var remainingScheduledDate: Int? = null,
    var remainingDate: Int? = null,
    var remainingApprovalDate: Int? = null,
    var offsetAmountManagementFee: Int? = null,
    var offsetAmountManagementFeeTax: Int? = null,
    var offsetAmountAssociationFee: Int? = null,
    var offsetAmountMaintenanceFee: Int? = null,
    var offsetAmountMaintenanceFeeTax: Int? = null,
    var neighborhoodFeeDaitoPayment: Int? = null,
    var offsetAmountWaterManagementFee: Int? = null,
    var offsetAmountTax: Int? = null,
    var offsetAmountProratedManagementFee: Int? = null,
    var proratedManagementFeeTax: Int? = null,
    var offsetAmountProratedAssociation: Int? = null,
    var offsetAmountProratedMaintenance: Int? = null,
    var maintenanceProratedTax: Int? = null,
    var proratedNeighborhoodFeeDaito: Int? = null,
    var offsetAmountProratedWaterManagement: Int? = null,
    var offsetAmountTax2: Int? = null,
    var managementContractInitialPayment: Int? = null,
    var managementContractInitialPaymentTax: Int? = null,
    var tenantRegistrationFee: Int? = null,
    var tenantRegistrationFeeTax: Int? = null,
    var associationEntryFee: Int? = null,
    var notarizedDocCreationCost2: Int? = null,
    var stampFee2: Int? = null,
    var moveInSettlementAmount: Int? = null,
    var moveInSettlementDate: Int? = null,
    var tenantSettlementPaymentScheduleDiv: String? = null,
    var tenantAddressOverrideDivision: String? = null,
    var postMoveContactPhone: String? = null,
    var fireInsuranceFee: Int? = null,
    var keyHandoverDate: Int? = null,
    var renovationApplicationSign: Byte? = null,
    var managementFeeTax: Int? = null,
    var cooperationFeeOffsetMonths: Short? = null,
    var reservationContractFeeAllocationAmount: Int? = null,
    var cancellationSign: Byte? = null,
    var moveInStartProcessedSign: Byte? = null,
    var vacateNoticeDate: Int? = null,
    var vacateScheduledDate: Int? = null,
    var breachPeriodExpiryDate: Int? = null,
    var moveOutSettlementDateLandlord: Int? = null,
    var moveOutSettlementDateTenant: Int? = null,
    var associationBenefitStartDate: Int? = null,
    var restorationCompletionDate: Int? = null,
    var moveOutDate: Int? = null,
    var breachYearMonth: Int? = null,
    var breachPeriodDays: Short? = null,
    var breachPeriodStartDate: Int? = null,
    var breachPeriodEndDate: Int? = null,
    var prefectureCode4: String? = null,
    var cityCode4: String? = null,
    var townCode4: String? = null,
    var addressDetail4: String? = null,
    var remarks2: String? = null,
    var bankCode3: String? = null,
    var bankBranchCode3: String? = null,
    var accountType3: String? = null,
    var accountNumber3: String? = null,
    var accountHolderNameKana3: String? = null,
    var accountHolderNameKanji3: String? = null,
    var onSiteConfirmationDate: Byte? = null,
    var restorationWorkExistenceSign: Byte? = null,
    var constructionDivision: String? = null,
    var actualPaymentDivision: String? = null,
    var constructionOrderNumber: String? = null,
    var maintenanceWorkSign: Byte? = null,
    var constructionOrderNumber2: String? = null,
    var contractDocumentDivision: String? = null,
    var rentArrearsMonths: Short? = null,
    var rentArrearsAmount: Int? = null,
    var depositRetainedAmount: Int? = null,
    var advanceRentPaymentRequestFinal: Int? = null,
    var rentBillingFinalCreationYear: Int? = null,
    var companyCode: String? = null,
    var branchCode: String? = null,
    var directSupervisorCode: String? = null,
    var employeeCode: String? = null,
    var currentResponsibleShozokuCode: String? = null,
    var currentResponsibleBranchCode: String? = null,
    var salesPerformanceShozokuCode: String? = null,
    var salesPerformanceBranchCode: String? = null,
    var companyCode2: String? = null,
    var baseCode: String? = null,
    var directSupervisorCode2: String? = null,
    var employeeCode2: String? = null,
    var customerResponsibleBranchCode: String? = null,
    var journalEntrySeq: Short? = null,
    var previousStateDivision: String? = null,
    var currentStateDivision: String? = null,
    var modificationStateDivision: String? = null,
    var interfaceSign: Byte? = null,
    var responseReceipt: String? = null,
    var satelliteCode: String? = null,
    var responseReceiptDate: Int? = null,
    var salesOfficeStaff: String? = null,
    var parkingAggregationDivision: String? = null,
    var ledgerNo: Short? = null,
    var guarantorNotRequiredDivision: Byte? = null,
    var communicationPartnerDivision: Byte? = null,
    var nonStandardDivision: Byte? = null,
    var contractRenewalImplementer: Byte? = null,
    var corporateHousingAgencySign: Byte? = null,
    var ffPaymentSign: Byte? = null,
    var rentalDivision: Byte? = null,
    var unused6: Byte? = null,
    var unused7: Byte? = null,
    var unused8: Byte? = null,
    var unused9: Byte? = null,
    var contractRent: Int? = null,
    var specialRentalDivision: String? = null,
    var responseReceiver: String? = null,
    var parkingContractFee: Int? = null,
    var parkingContractFeeTax: Int? = null,
    var parkingContractFeeExemptionDivision: String? = null,
    var incomeParkingFeeTenant: Int? = null,
    var incomeParkingContractFeeTax: Int? = null,
    var depositParkingContractFee: Int? = null,
    var depositParkingContractFeeTax: Int? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: TenantContractPojo = other as TenantContractPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.logicalDeleteSign == null) {
            if (o.logicalDeleteSign != null)
                return false
        }
        else if (this.logicalDeleteSign != o.logicalDeleteSign)
            return false
        if (this.tenantContractNumber != o.tenantContractNumber)
            return false
        if (this.tenantContractChangeSeq != o.tenantContractChangeSeq)
            return false
        if (this.contractContentDivision == null) {
            if (o.contractContentDivision != null)
                return false
        }
        else if (this.contractContentDivision != o.contractContentDivision)
            return false
        if (this.buildingCode == null) {
            if (o.buildingCode != null)
                return false
        }
        else if (this.buildingCode != o.buildingCode)
            return false
        if (this.roomCode == null) {
            if (o.roomCode != null)
                return false
        }
        else if (this.roomCode != o.roomCode)
            return false
        if (this.parkingCode == null) {
            if (o.parkingCode != null)
                return false
        }
        else if (this.parkingCode != o.parkingCode)
            return false
        if (this.aggregateContractNumber == null) {
            if (o.aggregateContractNumber != null)
                return false
        }
        else if (this.aggregateContractNumber != o.aggregateContractNumber)
            return false
        if (this.aggregateContractChangeSeq == null) {
            if (o.aggregateContractChangeSeq != null)
                return false
        }
        else if (this.aggregateContractChangeSeq != o.aggregateContractChangeSeq)
            return false
        if (this.parkingSpaces == null) {
            if (o.parkingSpaces != null)
                return false
        }
        else if (this.parkingSpaces != o.parkingSpaces)
            return false
        if (this.bulkLeaseSign == null) {
            if (o.bulkLeaseSign != null)
                return false
        }
        else if (this.bulkLeaseSign != o.bulkLeaseSign)
            return false
        if (this.tenantType == null) {
            if (o.tenantType != null)
                return false
        }
        else if (this.tenantType != o.tenantType)
            return false
        if (this.tenantProspectNumber == null) {
            if (o.tenantProspectNumber != null)
                return false
        }
        else if (this.tenantProspectNumber != o.tenantProspectNumber)
            return false
        if (this.tenantCode == null) {
            if (o.tenantCode != null)
                return false
        }
        else if (this.tenantCode != o.tenantCode)
            return false
        if (this.searchKana == null) {
            if (o.searchKana != null)
                return false
        }
        else if (this.searchKana != o.searchKana)
            return false
        if (this.infoAcquisitionDivision == null) {
            if (o.infoAcquisitionDivision != null)
                return false
        }
        else if (this.infoAcquisitionDivision != o.infoAcquisitionDivision)
            return false
        if (this.hotInfoReceiptNumber == null) {
            if (o.hotInfoReceiptNumber != null)
                return false
        }
        else if (this.hotInfoReceiptNumber != o.hotInfoReceiptNumber)
            return false
        if (this.landlordCode_10 == null) {
            if (o.landlordCode_10 != null)
                return false
        }
        else if (this.landlordCode_10 != o.landlordCode_10)
            return false
        if (this.taxDivision == null) {
            if (o.taxDivision != null)
                return false
        }
        else if (this.taxDivision != o.taxDivision)
            return false
        if (this.searchKana2 == null) {
            if (o.searchKana2 != null)
                return false
        }
        else if (this.searchKana2 != o.searchKana2)
            return false
        if (this.name == null) {
            if (o.name != null)
                return false
        }
        else if (this.name != o.name)
            return false
        if (this.prefectureCode == null) {
            if (o.prefectureCode != null)
                return false
        }
        else if (this.prefectureCode != o.prefectureCode)
            return false
        if (this.cityCode == null) {
            if (o.cityCode != null)
                return false
        }
        else if (this.cityCode != o.cityCode)
            return false
        if (this.townCode == null) {
            if (o.townCode != null)
                return false
        }
        else if (this.townCode != o.townCode)
            return false
        if (this.addressDetail == null) {
            if (o.addressDetail != null)
                return false
        }
        else if (this.addressDetail != o.addressDetail)
            return false
        if (this.buildingName == null) {
            if (o.buildingName != null)
                return false
        }
        else if (this.buildingName != o.buildingName)
            return false
        if (this.phoneNumber == null) {
            if (o.phoneNumber != null)
                return false
        }
        else if (this.phoneNumber != o.phoneNumber)
            return false
        if (this.importantDisclosureOutputCount == null) {
            if (o.importantDisclosureOutputCount != null)
                return false
        }
        else if (this.importantDisclosureOutputCount != o.importantDisclosureOutputCount)
            return false
        if (this.importantDisclosureOutputDate == null) {
            if (o.importantDisclosureOutputDate != null)
                return false
        }
        else if (this.importantDisclosureOutputDate != o.importantDisclosureOutputDate)
            return false
        if (this.importantDisclosureCollectionDate == null) {
            if (o.importantDisclosureCollectionDate != null)
                return false
        }
        else if (this.importantDisclosureCollectionDate != o.importantDisclosureCollectionDate)
            return false
        if (this.importantDisclosureDate == null) {
            if (o.importantDisclosureDate != null)
                return false
        }
        else if (this.importantDisclosureDate != o.importantDisclosureDate)
            return false
        if (this.importantDisclosureAgentCode == null) {
            if (o.importantDisclosureAgentCode != null)
                return false
        }
        else if (this.importantDisclosureAgentCode != o.importantDisclosureAgentCode)
            return false
        if (this.importantDisclosureDivision == null) {
            if (o.importantDisclosureDivision != null)
                return false
        }
        else if (this.importantDisclosureDivision != o.importantDisclosureDivision)
            return false
        if (this.paymentDate == null) {
            if (o.paymentDate != null)
                return false
        }
        else if (this.paymentDate != o.paymentDate)
            return false
        if (this.moveInApplicationDate == null) {
            if (o.moveInApplicationDate != null)
                return false
        }
        else if (this.moveInApplicationDate != o.moveInApplicationDate)
            return false
        if (this.moveInScheduledDate == null) {
            if (o.moveInScheduledDate != null)
                return false
        }
        else if (this.moveInScheduledDate != o.moveInScheduledDate)
            return false
        if (this.usagePurpose == null) {
            if (o.usagePurpose != null)
                return false
        }
        else if (this.usagePurpose != o.usagePurpose)
            return false
        if (this.moveInApplicationFee == null) {
            if (o.moveInApplicationFee != null)
                return false
        }
        else if (this.moveInApplicationFee != o.moveInApplicationFee)
            return false
        if (this.paymentMethodDivision == null) {
            if (o.paymentMethodDivision != null)
                return false
        }
        else if (this.paymentMethodDivision != o.paymentMethodDivision)
            return false
        if (this.mobileReceiptNumber == null) {
            if (o.mobileReceiptNumber != null)
                return false
        }
        else if (this.mobileReceiptNumber != o.mobileReceiptNumber)
            return false
        if (this.reservationApplicationFeeAppliedAmount == null) {
            if (o.reservationApplicationFeeAppliedAmount != null)
                return false
        }
        else if (this.reservationApplicationFeeAppliedAmount != o.reservationApplicationFeeAppliedAmount)
            return false
        if (this.tenantName == null) {
            if (o.tenantName != null)
                return false
        }
        else if (this.tenantName != o.tenantName)
            return false
        if (this.contactDivision == null) {
            if (o.contactDivision != null)
                return false
        }
        else if (this.contactDivision != o.contactDivision)
            return false
        if (this.contact == null) {
            if (o.contact != null)
                return false
        }
        else if (this.contact != o.contact)
            return false
        if (this.phoneNumber2 == null) {
            if (o.phoneNumber2 != null)
                return false
        }
        else if (this.phoneNumber2 != o.phoneNumber2)
            return false
        if (this.remarks == null) {
            if (o.remarks != null)
                return false
        }
        else if (this.remarks != o.remarks)
            return false
        if (this.name2 == null) {
            if (o.name2 != null)
                return false
        }
        else if (this.name2 != o.name2)
            return false
        if (this.prefectureCode2 == null) {
            if (o.prefectureCode2 != null)
                return false
        }
        else if (this.prefectureCode2 != o.prefectureCode2)
            return false
        if (this.cityCode2 == null) {
            if (o.cityCode2 != null)
                return false
        }
        else if (this.cityCode2 != o.cityCode2)
            return false
        if (this.townCode2 == null) {
            if (o.townCode2 != null)
                return false
        }
        else if (this.townCode2 != o.townCode2)
            return false
        if (this.addressDetail2 == null) {
            if (o.addressDetail2 != null)
                return false
        }
        else if (this.addressDetail2 != o.addressDetail2)
            return false
        if (this.buildingName2 == null) {
            if (o.buildingName2 != null)
                return false
        }
        else if (this.buildingName2 != o.buildingName2)
            return false
        if (this.phoneNumber3 == null) {
            if (o.phoneNumber3 != null)
                return false
        }
        else if (this.phoneNumber3 != o.phoneNumber3)
            return false
        if (this.relationship == null) {
            if (o.relationship != null)
                return false
        }
        else if (this.relationship != o.relationship)
            return false
        if (this.name3 == null) {
            if (o.name3 != null)
                return false
        }
        else if (this.name3 != o.name3)
            return false
        if (this.prefectureCode3 == null) {
            if (o.prefectureCode3 != null)
                return false
        }
        else if (this.prefectureCode3 != o.prefectureCode3)
            return false
        if (this.cityCode3 == null) {
            if (o.cityCode3 != null)
                return false
        }
        else if (this.cityCode3 != o.cityCode3)
            return false
        if (this.townCode3 == null) {
            if (o.townCode3 != null)
                return false
        }
        else if (this.townCode3 != o.townCode3)
            return false
        if (this.addressDetail3 == null) {
            if (o.addressDetail3 != null)
                return false
        }
        else if (this.addressDetail3 != o.addressDetail3)
            return false
        if (this.buildingName3 == null) {
            if (o.buildingName3 != null)
                return false
        }
        else if (this.buildingName3 != o.buildingName3)
            return false
        if (this.phoneNumber4 == null) {
            if (o.phoneNumber4 != null)
                return false
        }
        else if (this.phoneNumber4 != o.phoneNumber4)
            return false
        if (this.relationship2 == null) {
            if (o.relationship2 != null)
                return false
        }
        else if (this.relationship2 != o.relationship2)
            return false
        if (this.cohabitantName1 == null) {
            if (o.cohabitantName1 != null)
                return false
        }
        else if (this.cohabitantName1 != o.cohabitantName1)
            return false
        if (this.cohabitantAge1 == null) {
            if (o.cohabitantAge1 != null)
                return false
        }
        else if (this.cohabitantAge1 != o.cohabitantAge1)
            return false
        if (this.cohabitantRelationship1 == null) {
            if (o.cohabitantRelationship1 != null)
                return false
        }
        else if (this.cohabitantRelationship1 != o.cohabitantRelationship1)
            return false
        if (this.cohabitantName2 == null) {
            if (o.cohabitantName2 != null)
                return false
        }
        else if (this.cohabitantName2 != o.cohabitantName2)
            return false
        if (this.cohabitantAge2 == null) {
            if (o.cohabitantAge2 != null)
                return false
        }
        else if (this.cohabitantAge2 != o.cohabitantAge2)
            return false
        if (this.cohabitantRelationship2 == null) {
            if (o.cohabitantRelationship2 != null)
                return false
        }
        else if (this.cohabitantRelationship2 != o.cohabitantRelationship2)
            return false
        if (this.cohabitantName3 == null) {
            if (o.cohabitantName3 != null)
                return false
        }
        else if (this.cohabitantName3 != o.cohabitantName3)
            return false
        if (this.agentShozokuBranch == null) {
            if (o.agentShozokuBranch != null)
                return false
        }
        else if (this.agentShozokuBranch != o.agentShozokuBranch)
            return false
        if (this.cohabitantRelationship3 == null) {
            if (o.cohabitantRelationship3 != null)
                return false
        }
        else if (this.cohabitantRelationship3 != o.cohabitantRelationship3)
            return false
        if (this.cohabitantName4 == null) {
            if (o.cohabitantName4 != null)
                return false
        }
        else if (this.cohabitantName4 != o.cohabitantName4)
            return false
        if (this.agentShozokuStore == null) {
            if (o.agentShozokuStore != null)
                return false
        }
        else if (this.agentShozokuStore != o.agentShozokuStore)
            return false
        if (this.cohabitantRelationship4 == null) {
            if (o.cohabitantRelationship4 != null)
                return false
        }
        else if (this.cohabitantRelationship4 != o.cohabitantRelationship4)
            return false
        if (this.cohabitantName5 == null) {
            if (o.cohabitantName5 != null)
                return false
        }
        else if (this.cohabitantName5 != o.cohabitantName5)
            return false
        if (this.agentShozokuOffice == null) {
            if (o.agentShozokuOffice != null)
                return false
        }
        else if (this.agentShozokuOffice != o.agentShozokuOffice)
            return false
        if (this.cohabitantRelationship5 == null) {
            if (o.cohabitantRelationship5 != null)
                return false
        }
        else if (this.cohabitantRelationship5 != o.cohabitantRelationship5)
            return false
        if (this.landlordApprovalDate == null) {
            if (o.landlordApprovalDate != null)
                return false
        }
        else if (this.landlordApprovalDate != o.landlordApprovalDate)
            return false
        if (this.depositChangeDate == null) {
            if (o.depositChangeDate != null)
                return false
        }
        else if (this.depositChangeDate != o.depositChangeDate)
            return false
        if (this.agencyMemberNumber == null) {
            if (o.agencyMemberNumber != null)
                return false
        }
        else if (this.agencyMemberNumber != o.agencyMemberNumber)
            return false
        if (this.contractPeriodDivision == null) {
            if (o.contractPeriodDivision != null)
                return false
        }
        else if (this.contractPeriodDivision != o.contractPeriodDivision)
            return false
        if (this.reservationContractSign == null) {
            if (o.reservationContractSign != null)
                return false
        }
        else if (this.reservationContractSign != o.reservationContractSign)
            return false
        if (this.tenantReservationNumber == null) {
            if (o.tenantReservationNumber != null)
                return false
        }
        else if (this.tenantReservationNumber != o.tenantReservationNumber)
            return false
        if (this.leaseContractOutputContentDivision == null) {
            if (o.leaseContractOutputContentDivision != null)
                return false
        }
        else if (this.leaseContractOutputContentDivision != o.leaseContractOutputContentDivision)
            return false
        if (this.standardRent == null) {
            if (o.standardRent != null)
                return false
        }
        else if (this.standardRent != o.standardRent)
            return false
        if (this.leaseContractOutputCount == null) {
            if (o.leaseContractOutputCount != null)
                return false
        }
        else if (this.leaseContractOutputCount != o.leaseContractOutputCount)
            return false
        if (this.leaseContractLatestOutputDate == null) {
            if (o.leaseContractLatestOutputDate != null)
                return false
        }
        else if (this.leaseContractLatestOutputDate != o.leaseContractLatestOutputDate)
            return false
        if (this.leaseContractCollectionDate == null) {
            if (o.leaseContractCollectionDate != null)
                return false
        }
        else if (this.leaseContractCollectionDate != o.leaseContractCollectionDate)
            return false
        if (this.leaseContractDate == null) {
            if (o.leaseContractDate != null)
                return false
        }
        else if (this.leaseContractDate != o.leaseContractDate)
            return false
        if (this.contractStartDate == null) {
            if (o.contractStartDate != null)
                return false
        }
        else if (this.contractStartDate != o.contractStartDate)
            return false
        if (this.contractExpiryDate == null) {
            if (o.contractExpiryDate != null)
                return false
        }
        else if (this.contractExpiryDate != o.contractExpiryDate)
            return false
        if (this.nextRentRevisionScheduledDate == null) {
            if (o.nextRentRevisionScheduledDate != null)
                return false
        }
        else if (this.nextRentRevisionScheduledDate != o.nextRentRevisionScheduledDate)
            return false
        if (this.rentRevisionPeriod == null) {
            if (o.rentRevisionPeriod != null)
                return false
        }
        else if (this.rentRevisionPeriod != o.rentRevisionPeriod)
            return false
        if (this.frontFreeRentDays == null) {
            if (o.frontFreeRentDays != null)
                return false
        }
        else if (this.frontFreeRentDays != o.frontFreeRentDays)
            return false
        if (this.depositMonths == null) {
            if (o.depositMonths != null)
                return false
        }
        else if (this.depositMonths != o.depositMonths)
            return false
        if (this.depreciationMonths == null) {
            if (o.depreciationMonths != null)
                return false
        }
        else if (this.depreciationMonths != o.depreciationMonths)
            return false
        if (this.keyMoneyAmount == null) {
            if (o.keyMoneyAmount != null)
                return false
        }
        else if (this.keyMoneyAmount != o.keyMoneyAmount)
            return false
        if (this.keyMoneyDivision == null) {
            if (o.keyMoneyDivision != null)
                return false
        }
        else if (this.keyMoneyDivision != o.keyMoneyDivision)
            return false
        if (this.additionalKeyMoneyAmount == null) {
            if (o.additionalKeyMoneyAmount != null)
                return false
        }
        else if (this.additionalKeyMoneyAmount != o.additionalKeyMoneyAmount)
            return false
        if (this.depositAmount == null) {
            if (o.depositAmount != null)
                return false
        }
        else if (this.depositAmount != o.depositAmount)
            return false
        if (this.depreciation == null) {
            if (o.depreciation != null)
                return false
        }
        else if (this.depreciation != o.depreciation)
            return false
        if (this.rent == null) {
            if (o.rent != null)
                return false
        }
        else if (this.rent != o.rent)
            return false
        if (this.rentDivision == null) {
            if (o.rentDivision != null)
                return false
        }
        else if (this.rentDivision != o.rentDivision)
            return false
        if (this.parkingFeeAggregationSign == null) {
            if (o.parkingFeeAggregationSign != null)
                return false
        }
        else if (this.parkingFeeAggregationSign != o.parkingFeeAggregationSign)
            return false
        if (this.parkingFee == null) {
            if (o.parkingFee != null)
                return false
        }
        else if (this.parkingFee != o.parkingFee)
            return false
        if (this.parkingFeeDivision == null) {
            if (o.parkingFeeDivision != null)
                return false
        }
        else if (this.parkingFeeDivision != o.parkingFeeDivision)
            return false
        if (this.commonServiceFee == null) {
            if (o.commonServiceFee != null)
                return false
        }
        else if (this.commonServiceFee != o.commonServiceFee)
            return false
        if (this.commonServiceFeeDivision == null) {
            if (o.commonServiceFeeDivision != null)
                return false
        }
        else if (this.commonServiceFeeDivision != o.commonServiceFeeDivision)
            return false
        if (this.neighborhoodAssociationFee == null) {
            if (o.neighborhoodAssociationFee != null)
                return false
        }
        else if (this.neighborhoodAssociationFee != o.neighborhoodAssociationFee)
            return false
        if (this.additionalDepositAmount == null) {
            if (o.additionalDepositAmount != null)
                return false
        }
        else if (this.additionalDepositAmount != o.additionalDepositAmount)
            return false
        if (this.paymentScheduleCreationSign == null) {
            if (o.paymentScheduleCreationSign != null)
                return false
        }
        else if (this.paymentScheduleCreationSign != o.paymentScheduleCreationSign)
            return false
        if (this.paymentMethodDivision2 == null) {
            if (o.paymentMethodDivision2 != null)
                return false
        }
        else if (this.paymentMethodDivision2 != o.paymentMethodDivision2)
            return false
        if (this.differenceProratedDays == null) {
            if (o.differenceProratedDays != null)
                return false
        }
        else if (this.differenceProratedDays != o.differenceProratedDays)
            return false
        if (this.differenceProratedRentAmount == null) {
            if (o.differenceProratedRentAmount != null)
                return false
        }
        else if (this.differenceProratedRentAmount != o.differenceProratedRentAmount)
            return false
        if (this.differenceProratedParkingFee == null) {
            if (o.differenceProratedParkingFee != null)
                return false
        }
        else if (this.differenceProratedParkingFee != o.differenceProratedParkingFee)
            return false
        if (this.differenceProratedManagementFee == null) {
            if (o.differenceProratedManagementFee != null)
                return false
        }
        else if (this.differenceProratedManagementFee != o.differenceProratedManagementFee)
            return false
        if (this.differenceProratedCommonServiceFee == null) {
            if (o.differenceProratedCommonServiceFee != null)
                return false
        }
        else if (this.differenceProratedCommonServiceFee != o.differenceProratedCommonServiceFee)
            return false
        if (this.differenceProratedAssociationFee == null) {
            if (o.differenceProratedAssociationFee != null)
                return false
        }
        else if (this.differenceProratedAssociationFee != o.differenceProratedAssociationFee)
            return false
        if (this.differenceProratedWaterManagementFee == null) {
            if (o.differenceProratedWaterManagementFee != null)
                return false
        }
        else if (this.differenceProratedWaterManagementFee != o.differenceProratedWaterManagementFee)
            return false
        if (this.differenceMonths == null) {
            if (o.differenceMonths != null)
                return false
        }
        else if (this.differenceMonths != o.differenceMonths)
            return false
        if (this.monthlyDifferenceRentAmount == null) {
            if (o.monthlyDifferenceRentAmount != null)
                return false
        }
        else if (this.monthlyDifferenceRentAmount != o.monthlyDifferenceRentAmount)
            return false
        if (this.monthlyDifferenceParkingFee == null) {
            if (o.monthlyDifferenceParkingFee != null)
                return false
        }
        else if (this.monthlyDifferenceParkingFee != o.monthlyDifferenceParkingFee)
            return false
        if (this.monthlyDifferenceCommonServiceFee == null) {
            if (o.monthlyDifferenceCommonServiceFee != null)
                return false
        }
        else if (this.monthlyDifferenceCommonServiceFee != o.monthlyDifferenceCommonServiceFee)
            return false
        if (this.monthlyDifferenceManagementFee == null) {
            if (o.monthlyDifferenceManagementFee != null)
                return false
        }
        else if (this.monthlyDifferenceManagementFee != o.monthlyDifferenceManagementFee)
            return false
        if (this.monthlyDifferenceAssociationFee == null) {
            if (o.monthlyDifferenceAssociationFee != null)
                return false
        }
        else if (this.monthlyDifferenceAssociationFee != o.monthlyDifferenceAssociationFee)
            return false
        if (this.monthlyDifferenceWaterManagementFee == null) {
            if (o.monthlyDifferenceWaterManagementFee != null)
                return false
        }
        else if (this.monthlyDifferenceWaterManagementFee != o.monthlyDifferenceWaterManagementFee)
            return false
        if (this.paymentScheduleCreationSign2 == null) {
            if (o.paymentScheduleCreationSign2 != null)
                return false
        }
        else if (this.paymentScheduleCreationSign2 != o.paymentScheduleCreationSign2)
            return false
        if (this.differenceRentCollectionMethod == null) {
            if (o.differenceRentCollectionMethod != null)
                return false
        }
        else if (this.differenceRentCollectionMethod != o.differenceRentCollectionMethod)
            return false
        if (this.rentCollectionMethodDivision == null) {
            if (o.rentCollectionMethodDivision != null)
                return false
        }
        else if (this.rentCollectionMethodDivision != o.rentCollectionMethodDivision)
            return false
        if (this.agencyDivision == null) {
            if (o.agencyDivision != null)
                return false
        }
        else if (this.agencyDivision != o.agencyDivision)
            return false
        if (this.bankCode == null) {
            if (o.bankCode != null)
                return false
        }
        else if (this.bankCode != o.bankCode)
            return false
        if (this.bankBranchCode == null) {
            if (o.bankBranchCode != null)
                return false
        }
        else if (this.bankBranchCode != o.bankBranchCode)
            return false
        if (this.accountType == null) {
            if (o.accountType != null)
                return false
        }
        else if (this.accountType != o.accountType)
            return false
        if (this.accountNumber == null) {
            if (o.accountNumber != null)
                return false
        }
        else if (this.accountNumber != o.accountNumber)
            return false
        if (this.accountHolderNameKana == null) {
            if (o.accountHolderNameKana != null)
                return false
        }
        else if (this.accountHolderNameKana != o.accountHolderNameKana)
            return false
        if (this.accountHolderNameKanji == null) {
            if (o.accountHolderNameKanji != null)
                return false
        }
        else if (this.accountHolderNameKanji != o.accountHolderNameKanji)
            return false
        if (this.initialChangeEffectiveDate == null) {
            if (o.initialChangeEffectiveDate != null)
                return false
        }
        else if (this.initialChangeEffectiveDate != o.initialChangeEffectiveDate)
            return false
        if (this.bankCode2 == null) {
            if (o.bankCode2 != null)
                return false
        }
        else if (this.bankCode2 != o.bankCode2)
            return false
        if (this.bankBranchCode2 == null) {
            if (o.bankBranchCode2 != null)
                return false
        }
        else if (this.bankBranchCode2 != o.bankBranchCode2)
            return false
        if (this.accountType2 == null) {
            if (o.accountType2 != null)
                return false
        }
        else if (this.accountType2 != o.accountType2)
            return false
        if (this.accountNumber2 == null) {
            if (o.accountNumber2 != null)
                return false
        }
        else if (this.accountNumber2 != o.accountNumber2)
            return false
        if (this.accountHolderNameKana2 == null) {
            if (o.accountHolderNameKana2 != null)
                return false
        }
        else if (this.accountHolderNameKana2 != o.accountHolderNameKana2)
            return false
        if (this.accountHolderNameKanji2 == null) {
            if (o.accountHolderNameKanji2 != null)
                return false
        }
        else if (this.accountHolderNameKanji2 != o.accountHolderNameKanji2)
            return false
        if (this.directDebitTargetDivision == null) {
            if (o.directDebitTargetDivision != null)
                return false
        }
        else if (this.directDebitTargetDivision != o.directDebitTargetDivision)
            return false
        if (this.directDebitAmount == null) {
            if (o.directDebitAmount != null)
                return false
        }
        else if (this.directDebitAmount != o.directDebitAmount)
            return false
        if (this.leaseContractTransactingAgent == null) {
            if (o.leaseContractTransactingAgent != null)
                return false
        }
        else if (this.leaseContractTransactingAgent != o.leaseContractTransactingAgent)
            return false
        if (this.contractEffectiveStartDate == null) {
            if (o.contractEffectiveStartDate != null)
                return false
        }
        else if (this.contractEffectiveStartDate != o.contractEffectiveStartDate)
            return false
        if (this.contractEffectiveEndDate == null) {
            if (o.contractEffectiveEndDate != null)
                return false
        }
        else if (this.contractEffectiveEndDate != o.contractEffectiveEndDate)
            return false
        if (this.frontFreeRentSign == null) {
            if (o.frontFreeRentSign != null)
                return false
        }
        else if (this.frontFreeRentSign != o.frontFreeRentSign)
            return false
        if (this.frontFreeRentAmount == null) {
            if (o.frontFreeRentAmount != null)
                return false
        }
        else if (this.frontFreeRentAmount != o.frontFreeRentAmount)
            return false
        if (this.frontFreeRentMonths == null) {
            if (o.frontFreeRentMonths != null)
                return false
        }
        else if (this.frontFreeRentMonths != o.frontFreeRentMonths)
            return false
        if (this.landlordAdvancePaymentAllocationAmount == null) {
            if (o.landlordAdvancePaymentAllocationAmount != null)
                return false
        }
        else if (this.landlordAdvancePaymentAllocationAmount != o.landlordAdvancePaymentAllocationAmount)
            return false
        if (this.landlordAdvancePayment == null) {
            if (o.landlordAdvancePayment != null)
                return false
        }
        else if (this.landlordAdvancePayment != o.landlordAdvancePayment)
            return false
        if (this.mngContractInitialPaymentAmount == null) {
            if (o.mngContractInitialPaymentAmount != null)
                return false
        }
        else if (this.mngContractInitialPaymentAmount != o.mngContractInitialPaymentAmount)
            return false
        if (this.mngContractInitialPaymentAmountTax == null) {
            if (o.mngContractInitialPaymentAmountTax != null)
                return false
        }
        else if (this.mngContractInitialPaymentAmountTax != o.mngContractInitialPaymentAmountTax)
            return false
        if (this.associationEntryFeeProcess == null) {
            if (o.associationEntryFeeProcess != null)
                return false
        }
        else if (this.associationEntryFeeProcess != o.associationEntryFeeProcess)
            return false
        if (this.registrationFeeAmount == null) {
            if (o.registrationFeeAmount != null)
                return false
        }
        else if (this.registrationFeeAmount != o.registrationFeeAmount)
            return false
        if (this.registrationFeeAmountTax == null) {
            if (o.registrationFeeAmountTax != null)
                return false
        }
        else if (this.registrationFeeAmountTax != o.registrationFeeAmountTax)
            return false
        if (this.mngContractInitialPaymentTransferred == null) {
            if (o.mngContractInitialPaymentTransferred != null)
                return false
        }
        else if (this.mngContractInitialPaymentTransferred != o.mngContractInitialPaymentTransferred)
            return false
        if (this.brokerageFeeExemptionDivision == null) {
            if (o.brokerageFeeExemptionDivision != null)
                return false
        }
        else if (this.brokerageFeeExemptionDivision != o.brokerageFeeExemptionDivision)
            return false
        if (this.rentManagementStartDate == null) {
            if (o.rentManagementStartDate != null)
                return false
        }
        else if (this.rentManagementStartDate != o.rentManagementStartDate)
            return false
        if (this.notarizedDocPaymentSign == null) {
            if (o.notarizedDocPaymentSign != null)
                return false
        }
        else if (this.notarizedDocPaymentSign != o.notarizedDocPaymentSign)
            return false
        if (this.moveInCalculationOutputDate == null) {
            if (o.moveInCalculationOutputDate != null)
                return false
        }
        else if (this.moveInCalculationOutputDate != o.moveInCalculationOutputDate)
            return false
        if (this.remainingRent == null) {
            if (o.remainingRent != null)
                return false
        }
        else if (this.remainingRent != o.remainingRent)
            return false
        if (this.remainingRentTax == null) {
            if (o.remainingRentTax != null)
                return false
        }
        else if (this.remainingRentTax != o.remainingRentTax)
            return false
        if (this.remainingParkingFee == null) {
            if (o.remainingParkingFee != null)
                return false
        }
        else if (this.remainingParkingFee != o.remainingParkingFee)
            return false
        if (this.remainingParkingFeeTax == null) {
            if (o.remainingParkingFeeTax != null)
                return false
        }
        else if (this.remainingParkingFeeTax != o.remainingParkingFeeTax)
            return false
        if (this.remainingCommonServiceFee == null) {
            if (o.remainingCommonServiceFee != null)
                return false
        }
        else if (this.remainingCommonServiceFee != o.remainingCommonServiceFee)
            return false
        if (this.remainingCommonServiceFeeTax == null) {
            if (o.remainingCommonServiceFeeTax != null)
                return false
        }
        else if (this.remainingCommonServiceFeeTax != o.remainingCommonServiceFeeTax)
            return false
        if (this.remainingNeighborhoodFee == null) {
            if (o.remainingNeighborhoodFee != null)
                return false
        }
        else if (this.remainingNeighborhoodFee != o.remainingNeighborhoodFee)
            return false
        if (this.remainingMonths == null) {
            if (o.remainingMonths != null)
                return false
        }
        else if (this.remainingMonths != o.remainingMonths)
            return false
        if (this.proratedRent == null) {
            if (o.proratedRent != null)
                return false
        }
        else if (this.proratedRent != o.proratedRent)
            return false
        if (this.proratedRentTax == null) {
            if (o.proratedRentTax != null)
                return false
        }
        else if (this.proratedRentTax != o.proratedRentTax)
            return false
        if (this.proratedParking == null) {
            if (o.proratedParking != null)
                return false
        }
        else if (this.proratedParking != o.proratedParking)
            return false
        if (this.proratedParkingFeeTax == null) {
            if (o.proratedParkingFeeTax != null)
                return false
        }
        else if (this.proratedParkingFeeTax != o.proratedParkingFeeTax)
            return false
        if (this.proratedCommonServiceFee == null) {
            if (o.proratedCommonServiceFee != null)
                return false
        }
        else if (this.proratedCommonServiceFee != o.proratedCommonServiceFee)
            return false
        if (this.proratedCommonServiceFeeTax == null) {
            if (o.proratedCommonServiceFeeTax != null)
                return false
        }
        else if (this.proratedCommonServiceFeeTax != o.proratedCommonServiceFeeTax)
            return false
        if (this.proratedNeighborhoodFee == null) {
            if (o.proratedNeighborhoodFee != null)
                return false
        }
        else if (this.proratedNeighborhoodFee != o.proratedNeighborhoodFee)
            return false
        if (this.keyMoneyAmount2 == null) {
            if (o.keyMoneyAmount2 != null)
                return false
        }
        else if (this.keyMoneyAmount2 != o.keyMoneyAmount2)
            return false
        if (this.keyMoneyTax == null) {
            if (o.keyMoneyTax != null)
                return false
        }
        else if (this.keyMoneyTax != o.keyMoneyTax)
            return false
        if (this.depositAmount2 == null) {
            if (o.depositAmount2 != null)
                return false
        }
        else if (this.depositAmount2 != o.depositAmount2)
            return false
        if (this.monthlyManagementFee == null) {
            if (o.monthlyManagementFee != null)
                return false
        }
        else if (this.monthlyManagementFee != o.monthlyManagementFee)
            return false
        if (this.notarizedDocCreationCost == null) {
            if (o.notarizedDocCreationCost != null)
                return false
        }
        else if (this.notarizedDocCreationCost != o.notarizedDocCreationCost)
            return false
        if (this.stampFee == null) {
            if (o.stampFee != null)
                return false
        }
        else if (this.stampFee != o.stampFee)
            return false
        if (this.interiorCooperationFee == null) {
            if (o.interiorCooperationFee != null)
                return false
        }
        else if (this.interiorCooperationFee != o.interiorCooperationFee)
            return false
        if (this.interiorCooperationFeeTax == null) {
            if (o.interiorCooperationFeeTax != null)
                return false
        }
        else if (this.interiorCooperationFeeTax != o.interiorCooperationFeeTax)
            return false
        if (this.incomeCommissionFeeTenant == null) {
            if (o.incomeCommissionFeeTenant != null)
                return false
        }
        else if (this.incomeCommissionFeeTenant != o.incomeCommissionFeeTenant)
            return false
        if (this.incomeCommissionFeeTax == null) {
            if (o.incomeCommissionFeeTax != null)
                return false
        }
        else if (this.incomeCommissionFeeTax != o.incomeCommissionFeeTax)
            return false
        if (this.depositCommissionFee == null) {
            if (o.depositCommissionFee != null)
                return false
        }
        else if (this.depositCommissionFee != o.depositCommissionFee)
            return false
        if (this.depositCommissionFeeTax == null) {
            if (o.depositCommissionFeeTax != null)
                return false
        }
        else if (this.depositCommissionFeeTax != o.depositCommissionFeeTax)
            return false
        if (this.brokerageFee == null) {
            if (o.brokerageFee != null)
                return false
        }
        else if (this.brokerageFee != o.brokerageFee)
            return false
        if (this.brokerageFeeTax == null) {
            if (o.brokerageFeeTax != null)
                return false
        }
        else if (this.brokerageFeeTax != o.brokerageFeeTax)
            return false
        if (this.outsourcedAdvertisingFee == null) {
            if (o.outsourcedAdvertisingFee != null)
                return false
        }
        else if (this.outsourcedAdvertisingFee != o.outsourcedAdvertisingFee)
            return false
        if (this.outsourcedAdvertisingFeeTax == null) {
            if (o.outsourcedAdvertisingFeeTax != null)
                return false
        }
        else if (this.outsourcedAdvertisingFeeTax != o.outsourcedAdvertisingFeeTax)
            return false
        if (this.paymentSign == null) {
            if (o.paymentSign != null)
                return false
        }
        else if (this.paymentSign != o.paymentSign)
            return false
        if (this.brokerCode == null) {
            if (o.brokerCode != null)
                return false
        }
        else if (this.brokerCode != o.brokerCode)
            return false
        if (this.brokerageFeeBreakdownDivision == null) {
            if (o.brokerageFeeBreakdownDivision != null)
                return false
        }
        else if (this.brokerageFeeBreakdownDivision != o.brokerageFeeBreakdownDivision)
            return false
        if (this.remainingAmount == null) {
            if (o.remainingAmount != null)
                return false
        }
        else if (this.remainingAmount != o.remainingAmount)
            return false
        if (this.remainingScheduledDate == null) {
            if (o.remainingScheduledDate != null)
                return false
        }
        else if (this.remainingScheduledDate != o.remainingScheduledDate)
            return false
        if (this.remainingDate == null) {
            if (o.remainingDate != null)
                return false
        }
        else if (this.remainingDate != o.remainingDate)
            return false
        if (this.remainingApprovalDate == null) {
            if (o.remainingApprovalDate != null)
                return false
        }
        else if (this.remainingApprovalDate != o.remainingApprovalDate)
            return false
        if (this.offsetAmountManagementFee == null) {
            if (o.offsetAmountManagementFee != null)
                return false
        }
        else if (this.offsetAmountManagementFee != o.offsetAmountManagementFee)
            return false
        if (this.offsetAmountManagementFeeTax == null) {
            if (o.offsetAmountManagementFeeTax != null)
                return false
        }
        else if (this.offsetAmountManagementFeeTax != o.offsetAmountManagementFeeTax)
            return false
        if (this.offsetAmountAssociationFee == null) {
            if (o.offsetAmountAssociationFee != null)
                return false
        }
        else if (this.offsetAmountAssociationFee != o.offsetAmountAssociationFee)
            return false
        if (this.offsetAmountMaintenanceFee == null) {
            if (o.offsetAmountMaintenanceFee != null)
                return false
        }
        else if (this.offsetAmountMaintenanceFee != o.offsetAmountMaintenanceFee)
            return false
        if (this.offsetAmountMaintenanceFeeTax == null) {
            if (o.offsetAmountMaintenanceFeeTax != null)
                return false
        }
        else if (this.offsetAmountMaintenanceFeeTax != o.offsetAmountMaintenanceFeeTax)
            return false
        if (this.neighborhoodFeeDaitoPayment == null) {
            if (o.neighborhoodFeeDaitoPayment != null)
                return false
        }
        else if (this.neighborhoodFeeDaitoPayment != o.neighborhoodFeeDaitoPayment)
            return false
        if (this.offsetAmountWaterManagementFee == null) {
            if (o.offsetAmountWaterManagementFee != null)
                return false
        }
        else if (this.offsetAmountWaterManagementFee != o.offsetAmountWaterManagementFee)
            return false
        if (this.offsetAmountTax == null) {
            if (o.offsetAmountTax != null)
                return false
        }
        else if (this.offsetAmountTax != o.offsetAmountTax)
            return false
        if (this.offsetAmountProratedManagementFee == null) {
            if (o.offsetAmountProratedManagementFee != null)
                return false
        }
        else if (this.offsetAmountProratedManagementFee != o.offsetAmountProratedManagementFee)
            return false
        if (this.proratedManagementFeeTax == null) {
            if (o.proratedManagementFeeTax != null)
                return false
        }
        else if (this.proratedManagementFeeTax != o.proratedManagementFeeTax)
            return false
        if (this.offsetAmountProratedAssociation == null) {
            if (o.offsetAmountProratedAssociation != null)
                return false
        }
        else if (this.offsetAmountProratedAssociation != o.offsetAmountProratedAssociation)
            return false
        if (this.offsetAmountProratedMaintenance == null) {
            if (o.offsetAmountProratedMaintenance != null)
                return false
        }
        else if (this.offsetAmountProratedMaintenance != o.offsetAmountProratedMaintenance)
            return false
        if (this.maintenanceProratedTax == null) {
            if (o.maintenanceProratedTax != null)
                return false
        }
        else if (this.maintenanceProratedTax != o.maintenanceProratedTax)
            return false
        if (this.proratedNeighborhoodFeeDaito == null) {
            if (o.proratedNeighborhoodFeeDaito != null)
                return false
        }
        else if (this.proratedNeighborhoodFeeDaito != o.proratedNeighborhoodFeeDaito)
            return false
        if (this.offsetAmountProratedWaterManagement == null) {
            if (o.offsetAmountProratedWaterManagement != null)
                return false
        }
        else if (this.offsetAmountProratedWaterManagement != o.offsetAmountProratedWaterManagement)
            return false
        if (this.offsetAmountTax2 == null) {
            if (o.offsetAmountTax2 != null)
                return false
        }
        else if (this.offsetAmountTax2 != o.offsetAmountTax2)
            return false
        if (this.managementContractInitialPayment == null) {
            if (o.managementContractInitialPayment != null)
                return false
        }
        else if (this.managementContractInitialPayment != o.managementContractInitialPayment)
            return false
        if (this.managementContractInitialPaymentTax == null) {
            if (o.managementContractInitialPaymentTax != null)
                return false
        }
        else if (this.managementContractInitialPaymentTax != o.managementContractInitialPaymentTax)
            return false
        if (this.tenantRegistrationFee == null) {
            if (o.tenantRegistrationFee != null)
                return false
        }
        else if (this.tenantRegistrationFee != o.tenantRegistrationFee)
            return false
        if (this.tenantRegistrationFeeTax == null) {
            if (o.tenantRegistrationFeeTax != null)
                return false
        }
        else if (this.tenantRegistrationFeeTax != o.tenantRegistrationFeeTax)
            return false
        if (this.associationEntryFee == null) {
            if (o.associationEntryFee != null)
                return false
        }
        else if (this.associationEntryFee != o.associationEntryFee)
            return false
        if (this.notarizedDocCreationCost2 == null) {
            if (o.notarizedDocCreationCost2 != null)
                return false
        }
        else if (this.notarizedDocCreationCost2 != o.notarizedDocCreationCost2)
            return false
        if (this.stampFee2 == null) {
            if (o.stampFee2 != null)
                return false
        }
        else if (this.stampFee2 != o.stampFee2)
            return false
        if (this.moveInSettlementAmount == null) {
            if (o.moveInSettlementAmount != null)
                return false
        }
        else if (this.moveInSettlementAmount != o.moveInSettlementAmount)
            return false
        if (this.moveInSettlementDate == null) {
            if (o.moveInSettlementDate != null)
                return false
        }
        else if (this.moveInSettlementDate != o.moveInSettlementDate)
            return false
        if (this.tenantSettlementPaymentScheduleDiv == null) {
            if (o.tenantSettlementPaymentScheduleDiv != null)
                return false
        }
        else if (this.tenantSettlementPaymentScheduleDiv != o.tenantSettlementPaymentScheduleDiv)
            return false
        if (this.tenantAddressOverrideDivision == null) {
            if (o.tenantAddressOverrideDivision != null)
                return false
        }
        else if (this.tenantAddressOverrideDivision != o.tenantAddressOverrideDivision)
            return false
        if (this.postMoveContactPhone == null) {
            if (o.postMoveContactPhone != null)
                return false
        }
        else if (this.postMoveContactPhone != o.postMoveContactPhone)
            return false
        if (this.fireInsuranceFee == null) {
            if (o.fireInsuranceFee != null)
                return false
        }
        else if (this.fireInsuranceFee != o.fireInsuranceFee)
            return false
        if (this.keyHandoverDate == null) {
            if (o.keyHandoverDate != null)
                return false
        }
        else if (this.keyHandoverDate != o.keyHandoverDate)
            return false
        if (this.renovationApplicationSign == null) {
            if (o.renovationApplicationSign != null)
                return false
        }
        else if (this.renovationApplicationSign != o.renovationApplicationSign)
            return false
        if (this.managementFeeTax == null) {
            if (o.managementFeeTax != null)
                return false
        }
        else if (this.managementFeeTax != o.managementFeeTax)
            return false
        if (this.cooperationFeeOffsetMonths == null) {
            if (o.cooperationFeeOffsetMonths != null)
                return false
        }
        else if (this.cooperationFeeOffsetMonths != o.cooperationFeeOffsetMonths)
            return false
        if (this.reservationContractFeeAllocationAmount == null) {
            if (o.reservationContractFeeAllocationAmount != null)
                return false
        }
        else if (this.reservationContractFeeAllocationAmount != o.reservationContractFeeAllocationAmount)
            return false
        if (this.cancellationSign == null) {
            if (o.cancellationSign != null)
                return false
        }
        else if (this.cancellationSign != o.cancellationSign)
            return false
        if (this.moveInStartProcessedSign == null) {
            if (o.moveInStartProcessedSign != null)
                return false
        }
        else if (this.moveInStartProcessedSign != o.moveInStartProcessedSign)
            return false
        if (this.vacateNoticeDate == null) {
            if (o.vacateNoticeDate != null)
                return false
        }
        else if (this.vacateNoticeDate != o.vacateNoticeDate)
            return false
        if (this.vacateScheduledDate == null) {
            if (o.vacateScheduledDate != null)
                return false
        }
        else if (this.vacateScheduledDate != o.vacateScheduledDate)
            return false
        if (this.breachPeriodExpiryDate == null) {
            if (o.breachPeriodExpiryDate != null)
                return false
        }
        else if (this.breachPeriodExpiryDate != o.breachPeriodExpiryDate)
            return false
        if (this.moveOutSettlementDateLandlord == null) {
            if (o.moveOutSettlementDateLandlord != null)
                return false
        }
        else if (this.moveOutSettlementDateLandlord != o.moveOutSettlementDateLandlord)
            return false
        if (this.moveOutSettlementDateTenant == null) {
            if (o.moveOutSettlementDateTenant != null)
                return false
        }
        else if (this.moveOutSettlementDateTenant != o.moveOutSettlementDateTenant)
            return false
        if (this.associationBenefitStartDate == null) {
            if (o.associationBenefitStartDate != null)
                return false
        }
        else if (this.associationBenefitStartDate != o.associationBenefitStartDate)
            return false
        if (this.restorationCompletionDate == null) {
            if (o.restorationCompletionDate != null)
                return false
        }
        else if (this.restorationCompletionDate != o.restorationCompletionDate)
            return false
        if (this.moveOutDate == null) {
            if (o.moveOutDate != null)
                return false
        }
        else if (this.moveOutDate != o.moveOutDate)
            return false
        if (this.breachYearMonth == null) {
            if (o.breachYearMonth != null)
                return false
        }
        else if (this.breachYearMonth != o.breachYearMonth)
            return false
        if (this.breachPeriodDays == null) {
            if (o.breachPeriodDays != null)
                return false
        }
        else if (this.breachPeriodDays != o.breachPeriodDays)
            return false
        if (this.breachPeriodStartDate == null) {
            if (o.breachPeriodStartDate != null)
                return false
        }
        else if (this.breachPeriodStartDate != o.breachPeriodStartDate)
            return false
        if (this.breachPeriodEndDate == null) {
            if (o.breachPeriodEndDate != null)
                return false
        }
        else if (this.breachPeriodEndDate != o.breachPeriodEndDate)
            return false
        if (this.prefectureCode4 == null) {
            if (o.prefectureCode4 != null)
                return false
        }
        else if (this.prefectureCode4 != o.prefectureCode4)
            return false
        if (this.cityCode4 == null) {
            if (o.cityCode4 != null)
                return false
        }
        else if (this.cityCode4 != o.cityCode4)
            return false
        if (this.townCode4 == null) {
            if (o.townCode4 != null)
                return false
        }
        else if (this.townCode4 != o.townCode4)
            return false
        if (this.addressDetail4 == null) {
            if (o.addressDetail4 != null)
                return false
        }
        else if (this.addressDetail4 != o.addressDetail4)
            return false
        if (this.remarks2 == null) {
            if (o.remarks2 != null)
                return false
        }
        else if (this.remarks2 != o.remarks2)
            return false
        if (this.bankCode3 == null) {
            if (o.bankCode3 != null)
                return false
        }
        else if (this.bankCode3 != o.bankCode3)
            return false
        if (this.bankBranchCode3 == null) {
            if (o.bankBranchCode3 != null)
                return false
        }
        else if (this.bankBranchCode3 != o.bankBranchCode3)
            return false
        if (this.accountType3 == null) {
            if (o.accountType3 != null)
                return false
        }
        else if (this.accountType3 != o.accountType3)
            return false
        if (this.accountNumber3 == null) {
            if (o.accountNumber3 != null)
                return false
        }
        else if (this.accountNumber3 != o.accountNumber3)
            return false
        if (this.accountHolderNameKana3 == null) {
            if (o.accountHolderNameKana3 != null)
                return false
        }
        else if (this.accountHolderNameKana3 != o.accountHolderNameKana3)
            return false
        if (this.accountHolderNameKanji3 == null) {
            if (o.accountHolderNameKanji3 != null)
                return false
        }
        else if (this.accountHolderNameKanji3 != o.accountHolderNameKanji3)
            return false
        if (this.onSiteConfirmationDate == null) {
            if (o.onSiteConfirmationDate != null)
                return false
        }
        else if (this.onSiteConfirmationDate != o.onSiteConfirmationDate)
            return false
        if (this.restorationWorkExistenceSign == null) {
            if (o.restorationWorkExistenceSign != null)
                return false
        }
        else if (this.restorationWorkExistenceSign != o.restorationWorkExistenceSign)
            return false
        if (this.constructionDivision == null) {
            if (o.constructionDivision != null)
                return false
        }
        else if (this.constructionDivision != o.constructionDivision)
            return false
        if (this.actualPaymentDivision == null) {
            if (o.actualPaymentDivision != null)
                return false
        }
        else if (this.actualPaymentDivision != o.actualPaymentDivision)
            return false
        if (this.constructionOrderNumber == null) {
            if (o.constructionOrderNumber != null)
                return false
        }
        else if (this.constructionOrderNumber != o.constructionOrderNumber)
            return false
        if (this.maintenanceWorkSign == null) {
            if (o.maintenanceWorkSign != null)
                return false
        }
        else if (this.maintenanceWorkSign != o.maintenanceWorkSign)
            return false
        if (this.constructionOrderNumber2 == null) {
            if (o.constructionOrderNumber2 != null)
                return false
        }
        else if (this.constructionOrderNumber2 != o.constructionOrderNumber2)
            return false
        if (this.contractDocumentDivision == null) {
            if (o.contractDocumentDivision != null)
                return false
        }
        else if (this.contractDocumentDivision != o.contractDocumentDivision)
            return false
        if (this.rentArrearsMonths == null) {
            if (o.rentArrearsMonths != null)
                return false
        }
        else if (this.rentArrearsMonths != o.rentArrearsMonths)
            return false
        if (this.rentArrearsAmount == null) {
            if (o.rentArrearsAmount != null)
                return false
        }
        else if (this.rentArrearsAmount != o.rentArrearsAmount)
            return false
        if (this.depositRetainedAmount == null) {
            if (o.depositRetainedAmount != null)
                return false
        }
        else if (this.depositRetainedAmount != o.depositRetainedAmount)
            return false
        if (this.advanceRentPaymentRequestFinal == null) {
            if (o.advanceRentPaymentRequestFinal != null)
                return false
        }
        else if (this.advanceRentPaymentRequestFinal != o.advanceRentPaymentRequestFinal)
            return false
        if (this.rentBillingFinalCreationYear == null) {
            if (o.rentBillingFinalCreationYear != null)
                return false
        }
        else if (this.rentBillingFinalCreationYear != o.rentBillingFinalCreationYear)
            return false
        if (this.companyCode == null) {
            if (o.companyCode != null)
                return false
        }
        else if (this.companyCode != o.companyCode)
            return false
        if (this.branchCode == null) {
            if (o.branchCode != null)
                return false
        }
        else if (this.branchCode != o.branchCode)
            return false
        if (this.directSupervisorCode == null) {
            if (o.directSupervisorCode != null)
                return false
        }
        else if (this.directSupervisorCode != o.directSupervisorCode)
            return false
        if (this.employeeCode == null) {
            if (o.employeeCode != null)
                return false
        }
        else if (this.employeeCode != o.employeeCode)
            return false
        if (this.currentResponsibleShozokuCode == null) {
            if (o.currentResponsibleShozokuCode != null)
                return false
        }
        else if (this.currentResponsibleShozokuCode != o.currentResponsibleShozokuCode)
            return false
        if (this.currentResponsibleBranchCode == null) {
            if (o.currentResponsibleBranchCode != null)
                return false
        }
        else if (this.currentResponsibleBranchCode != o.currentResponsibleBranchCode)
            return false
        if (this.salesPerformanceShozokuCode == null) {
            if (o.salesPerformanceShozokuCode != null)
                return false
        }
        else if (this.salesPerformanceShozokuCode != o.salesPerformanceShozokuCode)
            return false
        if (this.salesPerformanceBranchCode == null) {
            if (o.salesPerformanceBranchCode != null)
                return false
        }
        else if (this.salesPerformanceBranchCode != o.salesPerformanceBranchCode)
            return false
        if (this.companyCode2 == null) {
            if (o.companyCode2 != null)
                return false
        }
        else if (this.companyCode2 != o.companyCode2)
            return false
        if (this.baseCode == null) {
            if (o.baseCode != null)
                return false
        }
        else if (this.baseCode != o.baseCode)
            return false
        if (this.directSupervisorCode2 == null) {
            if (o.directSupervisorCode2 != null)
                return false
        }
        else if (this.directSupervisorCode2 != o.directSupervisorCode2)
            return false
        if (this.employeeCode2 == null) {
            if (o.employeeCode2 != null)
                return false
        }
        else if (this.employeeCode2 != o.employeeCode2)
            return false
        if (this.customerResponsibleBranchCode == null) {
            if (o.customerResponsibleBranchCode != null)
                return false
        }
        else if (this.customerResponsibleBranchCode != o.customerResponsibleBranchCode)
            return false
        if (this.journalEntrySeq == null) {
            if (o.journalEntrySeq != null)
                return false
        }
        else if (this.journalEntrySeq != o.journalEntrySeq)
            return false
        if (this.previousStateDivision == null) {
            if (o.previousStateDivision != null)
                return false
        }
        else if (this.previousStateDivision != o.previousStateDivision)
            return false
        if (this.currentStateDivision == null) {
            if (o.currentStateDivision != null)
                return false
        }
        else if (this.currentStateDivision != o.currentStateDivision)
            return false
        if (this.modificationStateDivision == null) {
            if (o.modificationStateDivision != null)
                return false
        }
        else if (this.modificationStateDivision != o.modificationStateDivision)
            return false
        if (this.interfaceSign == null) {
            if (o.interfaceSign != null)
                return false
        }
        else if (this.interfaceSign != o.interfaceSign)
            return false
        if (this.responseReceipt == null) {
            if (o.responseReceipt != null)
                return false
        }
        else if (this.responseReceipt != o.responseReceipt)
            return false
        if (this.satelliteCode == null) {
            if (o.satelliteCode != null)
                return false
        }
        else if (this.satelliteCode != o.satelliteCode)
            return false
        if (this.responseReceiptDate == null) {
            if (o.responseReceiptDate != null)
                return false
        }
        else if (this.responseReceiptDate != o.responseReceiptDate)
            return false
        if (this.salesOfficeStaff == null) {
            if (o.salesOfficeStaff != null)
                return false
        }
        else if (this.salesOfficeStaff != o.salesOfficeStaff)
            return false
        if (this.parkingAggregationDivision == null) {
            if (o.parkingAggregationDivision != null)
                return false
        }
        else if (this.parkingAggregationDivision != o.parkingAggregationDivision)
            return false
        if (this.ledgerNo == null) {
            if (o.ledgerNo != null)
                return false
        }
        else if (this.ledgerNo != o.ledgerNo)
            return false
        if (this.guarantorNotRequiredDivision == null) {
            if (o.guarantorNotRequiredDivision != null)
                return false
        }
        else if (this.guarantorNotRequiredDivision != o.guarantorNotRequiredDivision)
            return false
        if (this.communicationPartnerDivision == null) {
            if (o.communicationPartnerDivision != null)
                return false
        }
        else if (this.communicationPartnerDivision != o.communicationPartnerDivision)
            return false
        if (this.nonStandardDivision == null) {
            if (o.nonStandardDivision != null)
                return false
        }
        else if (this.nonStandardDivision != o.nonStandardDivision)
            return false
        if (this.contractRenewalImplementer == null) {
            if (o.contractRenewalImplementer != null)
                return false
        }
        else if (this.contractRenewalImplementer != o.contractRenewalImplementer)
            return false
        if (this.corporateHousingAgencySign == null) {
            if (o.corporateHousingAgencySign != null)
                return false
        }
        else if (this.corporateHousingAgencySign != o.corporateHousingAgencySign)
            return false
        if (this.ffPaymentSign == null) {
            if (o.ffPaymentSign != null)
                return false
        }
        else if (this.ffPaymentSign != o.ffPaymentSign)
            return false
        if (this.rentalDivision == null) {
            if (o.rentalDivision != null)
                return false
        }
        else if (this.rentalDivision != o.rentalDivision)
            return false
        if (this.unused6 == null) {
            if (o.unused6 != null)
                return false
        }
        else if (this.unused6 != o.unused6)
            return false
        if (this.unused7 == null) {
            if (o.unused7 != null)
                return false
        }
        else if (this.unused7 != o.unused7)
            return false
        if (this.unused8 == null) {
            if (o.unused8 != null)
                return false
        }
        else if (this.unused8 != o.unused8)
            return false
        if (this.unused9 == null) {
            if (o.unused9 != null)
                return false
        }
        else if (this.unused9 != o.unused9)
            return false
        if (this.contractRent == null) {
            if (o.contractRent != null)
                return false
        }
        else if (this.contractRent != o.contractRent)
            return false
        if (this.specialRentalDivision == null) {
            if (o.specialRentalDivision != null)
                return false
        }
        else if (this.specialRentalDivision != o.specialRentalDivision)
            return false
        if (this.responseReceiver == null) {
            if (o.responseReceiver != null)
                return false
        }
        else if (this.responseReceiver != o.responseReceiver)
            return false
        if (this.parkingContractFee == null) {
            if (o.parkingContractFee != null)
                return false
        }
        else if (this.parkingContractFee != o.parkingContractFee)
            return false
        if (this.parkingContractFeeTax == null) {
            if (o.parkingContractFeeTax != null)
                return false
        }
        else if (this.parkingContractFeeTax != o.parkingContractFeeTax)
            return false
        if (this.parkingContractFeeExemptionDivision == null) {
            if (o.parkingContractFeeExemptionDivision != null)
                return false
        }
        else if (this.parkingContractFeeExemptionDivision != o.parkingContractFeeExemptionDivision)
            return false
        if (this.incomeParkingFeeTenant == null) {
            if (o.incomeParkingFeeTenant != null)
                return false
        }
        else if (this.incomeParkingFeeTenant != o.incomeParkingFeeTenant)
            return false
        if (this.incomeParkingContractFeeTax == null) {
            if (o.incomeParkingContractFeeTax != null)
                return false
        }
        else if (this.incomeParkingContractFeeTax != o.incomeParkingContractFeeTax)
            return false
        if (this.depositParkingContractFee == null) {
            if (o.depositParkingContractFee != null)
                return false
        }
        else if (this.depositParkingContractFee != o.depositParkingContractFee)
            return false
        if (this.depositParkingContractFeeTax == null) {
            if (o.depositParkingContractFeeTax != null)
                return false
        }
        else if (this.depositParkingContractFeeTax != o.depositParkingContractFeeTax)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.logicalDeleteSign == null) 0 else this.logicalDeleteSign.hashCode())
        result = prime * result + this.tenantContractNumber.hashCode()
        result = prime * result + this.tenantContractChangeSeq.hashCode()
        result = prime * result + (if (this.contractContentDivision == null) 0 else this.contractContentDivision.hashCode())
        result = prime * result + (if (this.buildingCode == null) 0 else this.buildingCode.hashCode())
        result = prime * result + (if (this.roomCode == null) 0 else this.roomCode.hashCode())
        result = prime * result + (if (this.parkingCode == null) 0 else this.parkingCode.hashCode())
        result = prime * result + (if (this.aggregateContractNumber == null) 0 else this.aggregateContractNumber.hashCode())
        result = prime * result + (if (this.aggregateContractChangeSeq == null) 0 else this.aggregateContractChangeSeq.hashCode())
        result = prime * result + (if (this.parkingSpaces == null) 0 else this.parkingSpaces.hashCode())
        result = prime * result + (if (this.bulkLeaseSign == null) 0 else this.bulkLeaseSign.hashCode())
        result = prime * result + (if (this.tenantType == null) 0 else this.tenantType.hashCode())
        result = prime * result + (if (this.tenantProspectNumber == null) 0 else this.tenantProspectNumber.hashCode())
        result = prime * result + (if (this.tenantCode == null) 0 else this.tenantCode.hashCode())
        result = prime * result + (if (this.searchKana == null) 0 else this.searchKana.hashCode())
        result = prime * result + (if (this.infoAcquisitionDivision == null) 0 else this.infoAcquisitionDivision.hashCode())
        result = prime * result + (if (this.hotInfoReceiptNumber == null) 0 else this.hotInfoReceiptNumber.hashCode())
        result = prime * result + (if (this.landlordCode_10 == null) 0 else this.landlordCode_10.hashCode())
        result = prime * result + (if (this.taxDivision == null) 0 else this.taxDivision.hashCode())
        result = prime * result + (if (this.searchKana2 == null) 0 else this.searchKana2.hashCode())
        result = prime * result + (if (this.name == null) 0 else this.name.hashCode())
        result = prime * result + (if (this.prefectureCode == null) 0 else this.prefectureCode.hashCode())
        result = prime * result + (if (this.cityCode == null) 0 else this.cityCode.hashCode())
        result = prime * result + (if (this.townCode == null) 0 else this.townCode.hashCode())
        result = prime * result + (if (this.addressDetail == null) 0 else this.addressDetail.hashCode())
        result = prime * result + (if (this.buildingName == null) 0 else this.buildingName.hashCode())
        result = prime * result + (if (this.phoneNumber == null) 0 else this.phoneNumber.hashCode())
        result = prime * result + (if (this.importantDisclosureOutputCount == null) 0 else this.importantDisclosureOutputCount.hashCode())
        result = prime * result + (if (this.importantDisclosureOutputDate == null) 0 else this.importantDisclosureOutputDate.hashCode())
        result = prime * result + (if (this.importantDisclosureCollectionDate == null) 0 else this.importantDisclosureCollectionDate.hashCode())
        result = prime * result + (if (this.importantDisclosureDate == null) 0 else this.importantDisclosureDate.hashCode())
        result = prime * result + (if (this.importantDisclosureAgentCode == null) 0 else this.importantDisclosureAgentCode.hashCode())
        result = prime * result + (if (this.importantDisclosureDivision == null) 0 else this.importantDisclosureDivision.hashCode())
        result = prime * result + (if (this.paymentDate == null) 0 else this.paymentDate.hashCode())
        result = prime * result + (if (this.moveInApplicationDate == null) 0 else this.moveInApplicationDate.hashCode())
        result = prime * result + (if (this.moveInScheduledDate == null) 0 else this.moveInScheduledDate.hashCode())
        result = prime * result + (if (this.usagePurpose == null) 0 else this.usagePurpose.hashCode())
        result = prime * result + (if (this.moveInApplicationFee == null) 0 else this.moveInApplicationFee.hashCode())
        result = prime * result + (if (this.paymentMethodDivision == null) 0 else this.paymentMethodDivision.hashCode())
        result = prime * result + (if (this.mobileReceiptNumber == null) 0 else this.mobileReceiptNumber.hashCode())
        result = prime * result + (if (this.reservationApplicationFeeAppliedAmount == null) 0 else this.reservationApplicationFeeAppliedAmount.hashCode())
        result = prime * result + (if (this.tenantName == null) 0 else this.tenantName.hashCode())
        result = prime * result + (if (this.contactDivision == null) 0 else this.contactDivision.hashCode())
        result = prime * result + (if (this.contact == null) 0 else this.contact.hashCode())
        result = prime * result + (if (this.phoneNumber2 == null) 0 else this.phoneNumber2.hashCode())
        result = prime * result + (if (this.remarks == null) 0 else this.remarks.hashCode())
        result = prime * result + (if (this.name2 == null) 0 else this.name2.hashCode())
        result = prime * result + (if (this.prefectureCode2 == null) 0 else this.prefectureCode2.hashCode())
        result = prime * result + (if (this.cityCode2 == null) 0 else this.cityCode2.hashCode())
        result = prime * result + (if (this.townCode2 == null) 0 else this.townCode2.hashCode())
        result = prime * result + (if (this.addressDetail2 == null) 0 else this.addressDetail2.hashCode())
        result = prime * result + (if (this.buildingName2 == null) 0 else this.buildingName2.hashCode())
        result = prime * result + (if (this.phoneNumber3 == null) 0 else this.phoneNumber3.hashCode())
        result = prime * result + (if (this.relationship == null) 0 else this.relationship.hashCode())
        result = prime * result + (if (this.name3 == null) 0 else this.name3.hashCode())
        result = prime * result + (if (this.prefectureCode3 == null) 0 else this.prefectureCode3.hashCode())
        result = prime * result + (if (this.cityCode3 == null) 0 else this.cityCode3.hashCode())
        result = prime * result + (if (this.townCode3 == null) 0 else this.townCode3.hashCode())
        result = prime * result + (if (this.addressDetail3 == null) 0 else this.addressDetail3.hashCode())
        result = prime * result + (if (this.buildingName3 == null) 0 else this.buildingName3.hashCode())
        result = prime * result + (if (this.phoneNumber4 == null) 0 else this.phoneNumber4.hashCode())
        result = prime * result + (if (this.relationship2 == null) 0 else this.relationship2.hashCode())
        result = prime * result + (if (this.cohabitantName1 == null) 0 else this.cohabitantName1.hashCode())
        result = prime * result + (if (this.cohabitantAge1 == null) 0 else this.cohabitantAge1.hashCode())
        result = prime * result + (if (this.cohabitantRelationship1 == null) 0 else this.cohabitantRelationship1.hashCode())
        result = prime * result + (if (this.cohabitantName2 == null) 0 else this.cohabitantName2.hashCode())
        result = prime * result + (if (this.cohabitantAge2 == null) 0 else this.cohabitantAge2.hashCode())
        result = prime * result + (if (this.cohabitantRelationship2 == null) 0 else this.cohabitantRelationship2.hashCode())
        result = prime * result + (if (this.cohabitantName3 == null) 0 else this.cohabitantName3.hashCode())
        result = prime * result + (if (this.agentShozokuBranch == null) 0 else this.agentShozokuBranch.hashCode())
        result = prime * result + (if (this.cohabitantRelationship3 == null) 0 else this.cohabitantRelationship3.hashCode())
        result = prime * result + (if (this.cohabitantName4 == null) 0 else this.cohabitantName4.hashCode())
        result = prime * result + (if (this.agentShozokuStore == null) 0 else this.agentShozokuStore.hashCode())
        result = prime * result + (if (this.cohabitantRelationship4 == null) 0 else this.cohabitantRelationship4.hashCode())
        result = prime * result + (if (this.cohabitantName5 == null) 0 else this.cohabitantName5.hashCode())
        result = prime * result + (if (this.agentShozokuOffice == null) 0 else this.agentShozokuOffice.hashCode())
        result = prime * result + (if (this.cohabitantRelationship5 == null) 0 else this.cohabitantRelationship5.hashCode())
        result = prime * result + (if (this.landlordApprovalDate == null) 0 else this.landlordApprovalDate.hashCode())
        result = prime * result + (if (this.depositChangeDate == null) 0 else this.depositChangeDate.hashCode())
        result = prime * result + (if (this.agencyMemberNumber == null) 0 else this.agencyMemberNumber.hashCode())
        result = prime * result + (if (this.contractPeriodDivision == null) 0 else this.contractPeriodDivision.hashCode())
        result = prime * result + (if (this.reservationContractSign == null) 0 else this.reservationContractSign.hashCode())
        result = prime * result + (if (this.tenantReservationNumber == null) 0 else this.tenantReservationNumber.hashCode())
        result = prime * result + (if (this.leaseContractOutputContentDivision == null) 0 else this.leaseContractOutputContentDivision.hashCode())
        result = prime * result + (if (this.standardRent == null) 0 else this.standardRent.hashCode())
        result = prime * result + (if (this.leaseContractOutputCount == null) 0 else this.leaseContractOutputCount.hashCode())
        result = prime * result + (if (this.leaseContractLatestOutputDate == null) 0 else this.leaseContractLatestOutputDate.hashCode())
        result = prime * result + (if (this.leaseContractCollectionDate == null) 0 else this.leaseContractCollectionDate.hashCode())
        result = prime * result + (if (this.leaseContractDate == null) 0 else this.leaseContractDate.hashCode())
        result = prime * result + (if (this.contractStartDate == null) 0 else this.contractStartDate.hashCode())
        result = prime * result + (if (this.contractExpiryDate == null) 0 else this.contractExpiryDate.hashCode())
        result = prime * result + (if (this.nextRentRevisionScheduledDate == null) 0 else this.nextRentRevisionScheduledDate.hashCode())
        result = prime * result + (if (this.rentRevisionPeriod == null) 0 else this.rentRevisionPeriod.hashCode())
        result = prime * result + (if (this.frontFreeRentDays == null) 0 else this.frontFreeRentDays.hashCode())
        result = prime * result + (if (this.depositMonths == null) 0 else this.depositMonths.hashCode())
        result = prime * result + (if (this.depreciationMonths == null) 0 else this.depreciationMonths.hashCode())
        result = prime * result + (if (this.keyMoneyAmount == null) 0 else this.keyMoneyAmount.hashCode())
        result = prime * result + (if (this.keyMoneyDivision == null) 0 else this.keyMoneyDivision.hashCode())
        result = prime * result + (if (this.additionalKeyMoneyAmount == null) 0 else this.additionalKeyMoneyAmount.hashCode())
        result = prime * result + (if (this.depositAmount == null) 0 else this.depositAmount.hashCode())
        result = prime * result + (if (this.depreciation == null) 0 else this.depreciation.hashCode())
        result = prime * result + (if (this.rent == null) 0 else this.rent.hashCode())
        result = prime * result + (if (this.rentDivision == null) 0 else this.rentDivision.hashCode())
        result = prime * result + (if (this.parkingFeeAggregationSign == null) 0 else this.parkingFeeAggregationSign.hashCode())
        result = prime * result + (if (this.parkingFee == null) 0 else this.parkingFee.hashCode())
        result = prime * result + (if (this.parkingFeeDivision == null) 0 else this.parkingFeeDivision.hashCode())
        result = prime * result + (if (this.commonServiceFee == null) 0 else this.commonServiceFee.hashCode())
        result = prime * result + (if (this.commonServiceFeeDivision == null) 0 else this.commonServiceFeeDivision.hashCode())
        result = prime * result + (if (this.neighborhoodAssociationFee == null) 0 else this.neighborhoodAssociationFee.hashCode())
        result = prime * result + (if (this.additionalDepositAmount == null) 0 else this.additionalDepositAmount.hashCode())
        result = prime * result + (if (this.paymentScheduleCreationSign == null) 0 else this.paymentScheduleCreationSign.hashCode())
        result = prime * result + (if (this.paymentMethodDivision2 == null) 0 else this.paymentMethodDivision2.hashCode())
        result = prime * result + (if (this.differenceProratedDays == null) 0 else this.differenceProratedDays.hashCode())
        result = prime * result + (if (this.differenceProratedRentAmount == null) 0 else this.differenceProratedRentAmount.hashCode())
        result = prime * result + (if (this.differenceProratedParkingFee == null) 0 else this.differenceProratedParkingFee.hashCode())
        result = prime * result + (if (this.differenceProratedManagementFee == null) 0 else this.differenceProratedManagementFee.hashCode())
        result = prime * result + (if (this.differenceProratedCommonServiceFee == null) 0 else this.differenceProratedCommonServiceFee.hashCode())
        result = prime * result + (if (this.differenceProratedAssociationFee == null) 0 else this.differenceProratedAssociationFee.hashCode())
        result = prime * result + (if (this.differenceProratedWaterManagementFee == null) 0 else this.differenceProratedWaterManagementFee.hashCode())
        result = prime * result + (if (this.differenceMonths == null) 0 else this.differenceMonths.hashCode())
        result = prime * result + (if (this.monthlyDifferenceRentAmount == null) 0 else this.monthlyDifferenceRentAmount.hashCode())
        result = prime * result + (if (this.monthlyDifferenceParkingFee == null) 0 else this.monthlyDifferenceParkingFee.hashCode())
        result = prime * result + (if (this.monthlyDifferenceCommonServiceFee == null) 0 else this.monthlyDifferenceCommonServiceFee.hashCode())
        result = prime * result + (if (this.monthlyDifferenceManagementFee == null) 0 else this.monthlyDifferenceManagementFee.hashCode())
        result = prime * result + (if (this.monthlyDifferenceAssociationFee == null) 0 else this.monthlyDifferenceAssociationFee.hashCode())
        result = prime * result + (if (this.monthlyDifferenceWaterManagementFee == null) 0 else this.monthlyDifferenceWaterManagementFee.hashCode())
        result = prime * result + (if (this.paymentScheduleCreationSign2 == null) 0 else this.paymentScheduleCreationSign2.hashCode())
        result = prime * result + (if (this.differenceRentCollectionMethod == null) 0 else this.differenceRentCollectionMethod.hashCode())
        result = prime * result + (if (this.rentCollectionMethodDivision == null) 0 else this.rentCollectionMethodDivision.hashCode())
        result = prime * result + (if (this.agencyDivision == null) 0 else this.agencyDivision.hashCode())
        result = prime * result + (if (this.bankCode == null) 0 else this.bankCode.hashCode())
        result = prime * result + (if (this.bankBranchCode == null) 0 else this.bankBranchCode.hashCode())
        result = prime * result + (if (this.accountType == null) 0 else this.accountType.hashCode())
        result = prime * result + (if (this.accountNumber == null) 0 else this.accountNumber.hashCode())
        result = prime * result + (if (this.accountHolderNameKana == null) 0 else this.accountHolderNameKana.hashCode())
        result = prime * result + (if (this.accountHolderNameKanji == null) 0 else this.accountHolderNameKanji.hashCode())
        result = prime * result + (if (this.initialChangeEffectiveDate == null) 0 else this.initialChangeEffectiveDate.hashCode())
        result = prime * result + (if (this.bankCode2 == null) 0 else this.bankCode2.hashCode())
        result = prime * result + (if (this.bankBranchCode2 == null) 0 else this.bankBranchCode2.hashCode())
        result = prime * result + (if (this.accountType2 == null) 0 else this.accountType2.hashCode())
        result = prime * result + (if (this.accountNumber2 == null) 0 else this.accountNumber2.hashCode())
        result = prime * result + (if (this.accountHolderNameKana2 == null) 0 else this.accountHolderNameKana2.hashCode())
        result = prime * result + (if (this.accountHolderNameKanji2 == null) 0 else this.accountHolderNameKanji2.hashCode())
        result = prime * result + (if (this.directDebitTargetDivision == null) 0 else this.directDebitTargetDivision.hashCode())
        result = prime * result + (if (this.directDebitAmount == null) 0 else this.directDebitAmount.hashCode())
        result = prime * result + (if (this.leaseContractTransactingAgent == null) 0 else this.leaseContractTransactingAgent.hashCode())
        result = prime * result + (if (this.contractEffectiveStartDate == null) 0 else this.contractEffectiveStartDate.hashCode())
        result = prime * result + (if (this.contractEffectiveEndDate == null) 0 else this.contractEffectiveEndDate.hashCode())
        result = prime * result + (if (this.frontFreeRentSign == null) 0 else this.frontFreeRentSign.hashCode())
        result = prime * result + (if (this.frontFreeRentAmount == null) 0 else this.frontFreeRentAmount.hashCode())
        result = prime * result + (if (this.frontFreeRentMonths == null) 0 else this.frontFreeRentMonths.hashCode())
        result = prime * result + (if (this.landlordAdvancePaymentAllocationAmount == null) 0 else this.landlordAdvancePaymentAllocationAmount.hashCode())
        result = prime * result + (if (this.landlordAdvancePayment == null) 0 else this.landlordAdvancePayment.hashCode())
        result = prime * result + (if (this.mngContractInitialPaymentAmount == null) 0 else this.mngContractInitialPaymentAmount.hashCode())
        result = prime * result + (if (this.mngContractInitialPaymentAmountTax == null) 0 else this.mngContractInitialPaymentAmountTax.hashCode())
        result = prime * result + (if (this.associationEntryFeeProcess == null) 0 else this.associationEntryFeeProcess.hashCode())
        result = prime * result + (if (this.registrationFeeAmount == null) 0 else this.registrationFeeAmount.hashCode())
        result = prime * result + (if (this.registrationFeeAmountTax == null) 0 else this.registrationFeeAmountTax.hashCode())
        result = prime * result + (if (this.mngContractInitialPaymentTransferred == null) 0 else this.mngContractInitialPaymentTransferred.hashCode())
        result = prime * result + (if (this.brokerageFeeExemptionDivision == null) 0 else this.brokerageFeeExemptionDivision.hashCode())
        result = prime * result + (if (this.rentManagementStartDate == null) 0 else this.rentManagementStartDate.hashCode())
        result = prime * result + (if (this.notarizedDocPaymentSign == null) 0 else this.notarizedDocPaymentSign.hashCode())
        result = prime * result + (if (this.moveInCalculationOutputDate == null) 0 else this.moveInCalculationOutputDate.hashCode())
        result = prime * result + (if (this.remainingRent == null) 0 else this.remainingRent.hashCode())
        result = prime * result + (if (this.remainingRentTax == null) 0 else this.remainingRentTax.hashCode())
        result = prime * result + (if (this.remainingParkingFee == null) 0 else this.remainingParkingFee.hashCode())
        result = prime * result + (if (this.remainingParkingFeeTax == null) 0 else this.remainingParkingFeeTax.hashCode())
        result = prime * result + (if (this.remainingCommonServiceFee == null) 0 else this.remainingCommonServiceFee.hashCode())
        result = prime * result + (if (this.remainingCommonServiceFeeTax == null) 0 else this.remainingCommonServiceFeeTax.hashCode())
        result = prime * result + (if (this.remainingNeighborhoodFee == null) 0 else this.remainingNeighborhoodFee.hashCode())
        result = prime * result + (if (this.remainingMonths == null) 0 else this.remainingMonths.hashCode())
        result = prime * result + (if (this.proratedRent == null) 0 else this.proratedRent.hashCode())
        result = prime * result + (if (this.proratedRentTax == null) 0 else this.proratedRentTax.hashCode())
        result = prime * result + (if (this.proratedParking == null) 0 else this.proratedParking.hashCode())
        result = prime * result + (if (this.proratedParkingFeeTax == null) 0 else this.proratedParkingFeeTax.hashCode())
        result = prime * result + (if (this.proratedCommonServiceFee == null) 0 else this.proratedCommonServiceFee.hashCode())
        result = prime * result + (if (this.proratedCommonServiceFeeTax == null) 0 else this.proratedCommonServiceFeeTax.hashCode())
        result = prime * result + (if (this.proratedNeighborhoodFee == null) 0 else this.proratedNeighborhoodFee.hashCode())
        result = prime * result + (if (this.keyMoneyAmount2 == null) 0 else this.keyMoneyAmount2.hashCode())
        result = prime * result + (if (this.keyMoneyTax == null) 0 else this.keyMoneyTax.hashCode())
        result = prime * result + (if (this.depositAmount2 == null) 0 else this.depositAmount2.hashCode())
        result = prime * result + (if (this.monthlyManagementFee == null) 0 else this.monthlyManagementFee.hashCode())
        result = prime * result + (if (this.notarizedDocCreationCost == null) 0 else this.notarizedDocCreationCost.hashCode())
        result = prime * result + (if (this.stampFee == null) 0 else this.stampFee.hashCode())
        result = prime * result + (if (this.interiorCooperationFee == null) 0 else this.interiorCooperationFee.hashCode())
        result = prime * result + (if (this.interiorCooperationFeeTax == null) 0 else this.interiorCooperationFeeTax.hashCode())
        result = prime * result + (if (this.incomeCommissionFeeTenant == null) 0 else this.incomeCommissionFeeTenant.hashCode())
        result = prime * result + (if (this.incomeCommissionFeeTax == null) 0 else this.incomeCommissionFeeTax.hashCode())
        result = prime * result + (if (this.depositCommissionFee == null) 0 else this.depositCommissionFee.hashCode())
        result = prime * result + (if (this.depositCommissionFeeTax == null) 0 else this.depositCommissionFeeTax.hashCode())
        result = prime * result + (if (this.brokerageFee == null) 0 else this.brokerageFee.hashCode())
        result = prime * result + (if (this.brokerageFeeTax == null) 0 else this.brokerageFeeTax.hashCode())
        result = prime * result + (if (this.outsourcedAdvertisingFee == null) 0 else this.outsourcedAdvertisingFee.hashCode())
        result = prime * result + (if (this.outsourcedAdvertisingFeeTax == null) 0 else this.outsourcedAdvertisingFeeTax.hashCode())
        result = prime * result + (if (this.paymentSign == null) 0 else this.paymentSign.hashCode())
        result = prime * result + (if (this.brokerCode == null) 0 else this.brokerCode.hashCode())
        result = prime * result + (if (this.brokerageFeeBreakdownDivision == null) 0 else this.brokerageFeeBreakdownDivision.hashCode())
        result = prime * result + (if (this.remainingAmount == null) 0 else this.remainingAmount.hashCode())
        result = prime * result + (if (this.remainingScheduledDate == null) 0 else this.remainingScheduledDate.hashCode())
        result = prime * result + (if (this.remainingDate == null) 0 else this.remainingDate.hashCode())
        result = prime * result + (if (this.remainingApprovalDate == null) 0 else this.remainingApprovalDate.hashCode())
        result = prime * result + (if (this.offsetAmountManagementFee == null) 0 else this.offsetAmountManagementFee.hashCode())
        result = prime * result + (if (this.offsetAmountManagementFeeTax == null) 0 else this.offsetAmountManagementFeeTax.hashCode())
        result = prime * result + (if (this.offsetAmountAssociationFee == null) 0 else this.offsetAmountAssociationFee.hashCode())
        result = prime * result + (if (this.offsetAmountMaintenanceFee == null) 0 else this.offsetAmountMaintenanceFee.hashCode())
        result = prime * result + (if (this.offsetAmountMaintenanceFeeTax == null) 0 else this.offsetAmountMaintenanceFeeTax.hashCode())
        result = prime * result + (if (this.neighborhoodFeeDaitoPayment == null) 0 else this.neighborhoodFeeDaitoPayment.hashCode())
        result = prime * result + (if (this.offsetAmountWaterManagementFee == null) 0 else this.offsetAmountWaterManagementFee.hashCode())
        result = prime * result + (if (this.offsetAmountTax == null) 0 else this.offsetAmountTax.hashCode())
        result = prime * result + (if (this.offsetAmountProratedManagementFee == null) 0 else this.offsetAmountProratedManagementFee.hashCode())
        result = prime * result + (if (this.proratedManagementFeeTax == null) 0 else this.proratedManagementFeeTax.hashCode())
        result = prime * result + (if (this.offsetAmountProratedAssociation == null) 0 else this.offsetAmountProratedAssociation.hashCode())
        result = prime * result + (if (this.offsetAmountProratedMaintenance == null) 0 else this.offsetAmountProratedMaintenance.hashCode())
        result = prime * result + (if (this.maintenanceProratedTax == null) 0 else this.maintenanceProratedTax.hashCode())
        result = prime * result + (if (this.proratedNeighborhoodFeeDaito == null) 0 else this.proratedNeighborhoodFeeDaito.hashCode())
        result = prime * result + (if (this.offsetAmountProratedWaterManagement == null) 0 else this.offsetAmountProratedWaterManagement.hashCode())
        result = prime * result + (if (this.offsetAmountTax2 == null) 0 else this.offsetAmountTax2.hashCode())
        result = prime * result + (if (this.managementContractInitialPayment == null) 0 else this.managementContractInitialPayment.hashCode())
        result = prime * result + (if (this.managementContractInitialPaymentTax == null) 0 else this.managementContractInitialPaymentTax.hashCode())
        result = prime * result + (if (this.tenantRegistrationFee == null) 0 else this.tenantRegistrationFee.hashCode())
        result = prime * result + (if (this.tenantRegistrationFeeTax == null) 0 else this.tenantRegistrationFeeTax.hashCode())
        result = prime * result + (if (this.associationEntryFee == null) 0 else this.associationEntryFee.hashCode())
        result = prime * result + (if (this.notarizedDocCreationCost2 == null) 0 else this.notarizedDocCreationCost2.hashCode())
        result = prime * result + (if (this.stampFee2 == null) 0 else this.stampFee2.hashCode())
        result = prime * result + (if (this.moveInSettlementAmount == null) 0 else this.moveInSettlementAmount.hashCode())
        result = prime * result + (if (this.moveInSettlementDate == null) 0 else this.moveInSettlementDate.hashCode())
        result = prime * result + (if (this.tenantSettlementPaymentScheduleDiv == null) 0 else this.tenantSettlementPaymentScheduleDiv.hashCode())
        result = prime * result + (if (this.tenantAddressOverrideDivision == null) 0 else this.tenantAddressOverrideDivision.hashCode())
        result = prime * result + (if (this.postMoveContactPhone == null) 0 else this.postMoveContactPhone.hashCode())
        result = prime * result + (if (this.fireInsuranceFee == null) 0 else this.fireInsuranceFee.hashCode())
        result = prime * result + (if (this.keyHandoverDate == null) 0 else this.keyHandoverDate.hashCode())
        result = prime * result + (if (this.renovationApplicationSign == null) 0 else this.renovationApplicationSign.hashCode())
        result = prime * result + (if (this.managementFeeTax == null) 0 else this.managementFeeTax.hashCode())
        result = prime * result + (if (this.cooperationFeeOffsetMonths == null) 0 else this.cooperationFeeOffsetMonths.hashCode())
        result = prime * result + (if (this.reservationContractFeeAllocationAmount == null) 0 else this.reservationContractFeeAllocationAmount.hashCode())
        result = prime * result + (if (this.cancellationSign == null) 0 else this.cancellationSign.hashCode())
        result = prime * result + (if (this.moveInStartProcessedSign == null) 0 else this.moveInStartProcessedSign.hashCode())
        result = prime * result + (if (this.vacateNoticeDate == null) 0 else this.vacateNoticeDate.hashCode())
        result = prime * result + (if (this.vacateScheduledDate == null) 0 else this.vacateScheduledDate.hashCode())
        result = prime * result + (if (this.breachPeriodExpiryDate == null) 0 else this.breachPeriodExpiryDate.hashCode())
        result = prime * result + (if (this.moveOutSettlementDateLandlord == null) 0 else this.moveOutSettlementDateLandlord.hashCode())
        result = prime * result + (if (this.moveOutSettlementDateTenant == null) 0 else this.moveOutSettlementDateTenant.hashCode())
        result = prime * result + (if (this.associationBenefitStartDate == null) 0 else this.associationBenefitStartDate.hashCode())
        result = prime * result + (if (this.restorationCompletionDate == null) 0 else this.restorationCompletionDate.hashCode())
        result = prime * result + (if (this.moveOutDate == null) 0 else this.moveOutDate.hashCode())
        result = prime * result + (if (this.breachYearMonth == null) 0 else this.breachYearMonth.hashCode())
        result = prime * result + (if (this.breachPeriodDays == null) 0 else this.breachPeriodDays.hashCode())
        result = prime * result + (if (this.breachPeriodStartDate == null) 0 else this.breachPeriodStartDate.hashCode())
        result = prime * result + (if (this.breachPeriodEndDate == null) 0 else this.breachPeriodEndDate.hashCode())
        result = prime * result + (if (this.prefectureCode4 == null) 0 else this.prefectureCode4.hashCode())
        result = prime * result + (if (this.cityCode4 == null) 0 else this.cityCode4.hashCode())
        result = prime * result + (if (this.townCode4 == null) 0 else this.townCode4.hashCode())
        result = prime * result + (if (this.addressDetail4 == null) 0 else this.addressDetail4.hashCode())
        result = prime * result + (if (this.remarks2 == null) 0 else this.remarks2.hashCode())
        result = prime * result + (if (this.bankCode3 == null) 0 else this.bankCode3.hashCode())
        result = prime * result + (if (this.bankBranchCode3 == null) 0 else this.bankBranchCode3.hashCode())
        result = prime * result + (if (this.accountType3 == null) 0 else this.accountType3.hashCode())
        result = prime * result + (if (this.accountNumber3 == null) 0 else this.accountNumber3.hashCode())
        result = prime * result + (if (this.accountHolderNameKana3 == null) 0 else this.accountHolderNameKana3.hashCode())
        result = prime * result + (if (this.accountHolderNameKanji3 == null) 0 else this.accountHolderNameKanji3.hashCode())
        result = prime * result + (if (this.onSiteConfirmationDate == null) 0 else this.onSiteConfirmationDate.hashCode())
        result = prime * result + (if (this.restorationWorkExistenceSign == null) 0 else this.restorationWorkExistenceSign.hashCode())
        result = prime * result + (if (this.constructionDivision == null) 0 else this.constructionDivision.hashCode())
        result = prime * result + (if (this.actualPaymentDivision == null) 0 else this.actualPaymentDivision.hashCode())
        result = prime * result + (if (this.constructionOrderNumber == null) 0 else this.constructionOrderNumber.hashCode())
        result = prime * result + (if (this.maintenanceWorkSign == null) 0 else this.maintenanceWorkSign.hashCode())
        result = prime * result + (if (this.constructionOrderNumber2 == null) 0 else this.constructionOrderNumber2.hashCode())
        result = prime * result + (if (this.contractDocumentDivision == null) 0 else this.contractDocumentDivision.hashCode())
        result = prime * result + (if (this.rentArrearsMonths == null) 0 else this.rentArrearsMonths.hashCode())
        result = prime * result + (if (this.rentArrearsAmount == null) 0 else this.rentArrearsAmount.hashCode())
        result = prime * result + (if (this.depositRetainedAmount == null) 0 else this.depositRetainedAmount.hashCode())
        result = prime * result + (if (this.advanceRentPaymentRequestFinal == null) 0 else this.advanceRentPaymentRequestFinal.hashCode())
        result = prime * result + (if (this.rentBillingFinalCreationYear == null) 0 else this.rentBillingFinalCreationYear.hashCode())
        result = prime * result + (if (this.companyCode == null) 0 else this.companyCode.hashCode())
        result = prime * result + (if (this.branchCode == null) 0 else this.branchCode.hashCode())
        result = prime * result + (if (this.directSupervisorCode == null) 0 else this.directSupervisorCode.hashCode())
        result = prime * result + (if (this.employeeCode == null) 0 else this.employeeCode.hashCode())
        result = prime * result + (if (this.currentResponsibleShozokuCode == null) 0 else this.currentResponsibleShozokuCode.hashCode())
        result = prime * result + (if (this.currentResponsibleBranchCode == null) 0 else this.currentResponsibleBranchCode.hashCode())
        result = prime * result + (if (this.salesPerformanceShozokuCode == null) 0 else this.salesPerformanceShozokuCode.hashCode())
        result = prime * result + (if (this.salesPerformanceBranchCode == null) 0 else this.salesPerformanceBranchCode.hashCode())
        result = prime * result + (if (this.companyCode2 == null) 0 else this.companyCode2.hashCode())
        result = prime * result + (if (this.baseCode == null) 0 else this.baseCode.hashCode())
        result = prime * result + (if (this.directSupervisorCode2 == null) 0 else this.directSupervisorCode2.hashCode())
        result = prime * result + (if (this.employeeCode2 == null) 0 else this.employeeCode2.hashCode())
        result = prime * result + (if (this.customerResponsibleBranchCode == null) 0 else this.customerResponsibleBranchCode.hashCode())
        result = prime * result + (if (this.journalEntrySeq == null) 0 else this.journalEntrySeq.hashCode())
        result = prime * result + (if (this.previousStateDivision == null) 0 else this.previousStateDivision.hashCode())
        result = prime * result + (if (this.currentStateDivision == null) 0 else this.currentStateDivision.hashCode())
        result = prime * result + (if (this.modificationStateDivision == null) 0 else this.modificationStateDivision.hashCode())
        result = prime * result + (if (this.interfaceSign == null) 0 else this.interfaceSign.hashCode())
        result = prime * result + (if (this.responseReceipt == null) 0 else this.responseReceipt.hashCode())
        result = prime * result + (if (this.satelliteCode == null) 0 else this.satelliteCode.hashCode())
        result = prime * result + (if (this.responseReceiptDate == null) 0 else this.responseReceiptDate.hashCode())
        result = prime * result + (if (this.salesOfficeStaff == null) 0 else this.salesOfficeStaff.hashCode())
        result = prime * result + (if (this.parkingAggregationDivision == null) 0 else this.parkingAggregationDivision.hashCode())
        result = prime * result + (if (this.ledgerNo == null) 0 else this.ledgerNo.hashCode())
        result = prime * result + (if (this.guarantorNotRequiredDivision == null) 0 else this.guarantorNotRequiredDivision.hashCode())
        result = prime * result + (if (this.communicationPartnerDivision == null) 0 else this.communicationPartnerDivision.hashCode())
        result = prime * result + (if (this.nonStandardDivision == null) 0 else this.nonStandardDivision.hashCode())
        result = prime * result + (if (this.contractRenewalImplementer == null) 0 else this.contractRenewalImplementer.hashCode())
        result = prime * result + (if (this.corporateHousingAgencySign == null) 0 else this.corporateHousingAgencySign.hashCode())
        result = prime * result + (if (this.ffPaymentSign == null) 0 else this.ffPaymentSign.hashCode())
        result = prime * result + (if (this.rentalDivision == null) 0 else this.rentalDivision.hashCode())
        result = prime * result + (if (this.unused6 == null) 0 else this.unused6.hashCode())
        result = prime * result + (if (this.unused7 == null) 0 else this.unused7.hashCode())
        result = prime * result + (if (this.unused8 == null) 0 else this.unused8.hashCode())
        result = prime * result + (if (this.unused9 == null) 0 else this.unused9.hashCode())
        result = prime * result + (if (this.contractRent == null) 0 else this.contractRent.hashCode())
        result = prime * result + (if (this.specialRentalDivision == null) 0 else this.specialRentalDivision.hashCode())
        result = prime * result + (if (this.responseReceiver == null) 0 else this.responseReceiver.hashCode())
        result = prime * result + (if (this.parkingContractFee == null) 0 else this.parkingContractFee.hashCode())
        result = prime * result + (if (this.parkingContractFeeTax == null) 0 else this.parkingContractFeeTax.hashCode())
        result = prime * result + (if (this.parkingContractFeeExemptionDivision == null) 0 else this.parkingContractFeeExemptionDivision.hashCode())
        result = prime * result + (if (this.incomeParkingFeeTenant == null) 0 else this.incomeParkingFeeTenant.hashCode())
        result = prime * result + (if (this.incomeParkingContractFeeTax == null) 0 else this.incomeParkingContractFeeTax.hashCode())
        result = prime * result + (if (this.depositParkingContractFee == null) 0 else this.depositParkingContractFee.hashCode())
        result = prime * result + (if (this.depositParkingContractFeeTax == null) 0 else this.depositParkingContractFeeTax.hashCode())
        return result
    }
}
