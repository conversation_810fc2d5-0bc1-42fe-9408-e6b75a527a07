/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.BuildingStoreMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingStoreMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 建物店舗マスタ 既存システム物理名: ECM10P
 */
@Suppress("UNCHECKED_CAST")
open class BuildingStoreMasterRecord private constructor() : TableRecordImpl<BuildingStoreMasterRecord>(BuildingStoreMasterTable.BUILDING_STORE_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var buildingCode: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var leasingStoreCode: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    /**
     * Create a detached, initialised BuildingStoreMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, buildingCode: String? = null, leasingStoreCode: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.buildingCode = buildingCode
        this.leasingStoreCode = leasingStoreCode
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BuildingStoreMasterRecord
     */
    constructor(value: BuildingStoreMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.buildingCode = value.buildingCode
            this.leasingStoreCode = value.leasingStoreCode
            resetChangedOnNotNull()
        }
    }
}
