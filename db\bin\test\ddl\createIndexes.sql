\i indexes/REGION_MASTER.sql
\i indexes/TEMPORARY_RESERVATION_FILE.sql
\i indexes/PROPERTY_MAINTENANCE_INFO.sql
\i indexes/ROOM_INFO_MASTER.sql
\i indexes/ROOM_MASTER.sql
\i indexes/BUILDING_MASTER.sql
\i indexes/CUSTOMER.sql
\i indexes/PARKING.sql
\i indexes/PRODUCT_NAME_MASTER.sql
\i indexes/LATEST_ROOM_EQUIPMENT_FILE.sql
\i indexes/PANORAMA_ASSOCIATED_FILE.sql
\i indexes/EMPLOYEE_MASTER.sql
\i indexes/ACCIDENT_PROPERTY_MANAGEMENT.sql
\i indexes/CONSUMPTION_TAX_RATE_MASTER.sql
\i indexes/TEMPORARY_CONTRACT.sql
\i indexes/CONTRACT.sql
\i indexes/TENANT_CONTRACT.sql
\i indexes/TENANT_CONTRACT_BULK_COLLECTION_FILE.sql
\i indexes/THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.sql
\i indexes/DAITO_BULK_LEASE_CONTRACT_DETAILS.sql
\i indexes/MNG_ONLY_UPD_CONT_FEE_APPROVAL_BUILDING.sql
\i indexes/RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.sql
\i indexes/BELS_APPLICATION_RESULT_PROGRESS_FILE.sql
\i indexes/NEW_OFFICE_MASTER.sql
\i indexes/PARKING_RESERVATION.sql
\i indexes/EXCLUSIVE_PROPERTY.sql
\i indexes/VACANT_HOUSE_HP.sql
