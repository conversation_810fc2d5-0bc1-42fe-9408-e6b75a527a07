-- TABLE: PARKING_RESERVATION_FILE(駐車場予約ファイル)

CREATE TABLE PARKING_RESERVATION_FILE(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(10)                   
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1,0)                  
,    ORDER_CODE                                   varchar(7)        NOT NULL    
,    RECEPTION_DATE                               numeric(8,0)      NOT NULL    
,    SEQUENCE_NUMBER                              numeric(3,0)      NOT NULL    
,    STATUS_DIVISION                              varchar(1)                    
,    RESERVER_NAME                                varchar(26)                   
,    ROOM_CODE                                    varchar(5)                    
,    CONTACT_TEL                                  varchar(15)                   
,    RECEPTION_STAFF                              varchar(26)                   
,    REMARKS                                      varchar(202)                  
,    BUILDING_NUMBER                              varchar(2)        NOT NULL    
,    AS_DELETE_DATE                               numeric(8,0)                  
,    CONSTRAINT PK_PARKING_RESERVATION_FILE PRIMARY KEY (ORDER_CODE, RECEPTION_DATE, SEQUENCE_NUMBER, BUILDING_NUMBER)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PARKING_RESERVATION_FILE IS '駐車場予約ファイル 既存システム物理名: ERC30P';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: ERC01D 1：受付、2：契約済、3：ｷｬﾝｾﾙ、4：WP予約';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.CREATION_TIME IS '作成時刻 既存システム物理名: ERC02H';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.CREATOR IS '作成者 既存システム物理名: ERC03C';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: ERC04D';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.UPDATE_TIME IS '更新時刻 既存システム物理名: ERC05H';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.UPDATER IS '更新者 既存システム物理名: ERC06C';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: ERC07N';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: ERC08S';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.ORDER_CODE IS '受注コード 既存システム物理名: ERC09C';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.RECEPTION_DATE IS '受付日 既存システム物理名: ERC10D';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.SEQUENCE_NUMBER IS '連番 既存システム物理名: ERC11N';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.STATUS_DIVISION IS '状態区分 既存システム物理名: ERC12B';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.RESERVER_NAME IS '予約者氏名 既存システム物理名: ERC13M';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.ROOM_CODE IS '部屋コード 既存システム物理名: ERC14C';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.CONTACT_TEL IS '連絡先TEL 既存システム物理名: ERC15N';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.RECEPTION_STAFF IS '受付担当者 既存システム物理名: ERC16M';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.REMARKS IS '備考 既存システム物理名: ERC17X';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.BUILDING_NUMBER IS '棟番号 既存システム物理名: ERC18C';
COMMENT ON COLUMN PARKING_RESERVATION_FILE.AS_DELETE_DATE IS 'AS削除日 既存システム物理名: ERC19D';
