truncate table PROPERTY_DETAIL_FILE_NEW_RESIDENTIAL;
insert into PROPERTY_DETAIL_FILE_NEW_RESIDENTIAL (CREATION_DATE, CREATION_TIME, UPDATE_DATE, UPDATE_TIME, UPDATE_PROGRAM_ID, UPDATER, LO<PERSON><PERSON><PERSON>_DELETE_SIGN, OCCURRENCE_MONTH_CATEGORY, OCCURRENCE_MONTH, SALES_DEPARTMENT_CD, SALES_DEPARTMENT_NAME, SALES_DEPARTMENT_OUTPUT_ORDER_CD, BRANCH_CD, <PERSON><PERSON>CH_NAME, <PERSON><PERSON><PERSON>_OUTPUT_ORDER_CD, PROCESS_DATE, STATUS_NAME, C<PERSON>TOMER_FLAG, CUSTOMER_REP_CD, CUSTOMER_REP_NAME, OCCUPANCY_FLAG, BUILDING_CODE, BUILDING_NAME, ADDRESS_CD, LOCATION, EXPECTED_COMPLETION_DATE, COMPLETION_DATE, R<PERSON><PERSON>_CODE, R<PERSON><PERSON>_NUMBER, LANDLORD_CD, LANDLORD_NAME, B<PERSON><PERSON><PERSON>_LEASING_SIGN, CONTRACT_TYPE_NAME, ROOM_TYPE_CD, ROOM_TYPE_NAME, LAYOUT_CATEGORY, LAYOUT_NAME, EXCLUSIVE_AREA, TENANT_CONTRACT_NUMBER, CURRENT_STATUS_CATEGORY, MODIFIED_STATUS_CATEGORY, VACATION_NOTICE_DATE, REVIEW_APPROVAL_DATE, REVIEW_APPROVAL_DAYS_ELAPSED, ARRANGEMENT_OUTPUT_DATE, ARRANGEMENT_APPROVAL_DATE, ARRANGEMENT_COLLECTION_DATE, ARRANGEMENT_COLLECTION_DAYS_ELAPSED, MOVE_OUT_MEETING_DATE, EXPECTED_MOVE_OUT_DATE, MOVE_OUT_DATE, MOVE_OUT_RENT, REVIEW_RENT, CURRENT_RENT, DIFFERENCE_REVIEW_MOVE_OUT, DIFFERENCE_CURRENT_MOVE_OUT, DIFFERENCE_CURRENT_REVIEW, DISCREPANCY_STATUS, LONGEST_VACANCY_PERIOD_HISTORY, RESIDENTIAL_ROOM_COUNT, RESIDENTIAL_ROOM_COUNT_VACANT, RESTORATION_COMPLETION_EXPECTED_DATE, RESTORATION_COMPLETION_DATE, VACANCY_ACCOUNTING_EXPECTED_DATE, VACANCY_ACCOUNTING_DATE, VACANCY_MONTHS, VACANCY_PERIOD, OCCUPANCY_APPLICATION_DATE, CONTRACT_DATE, REMAINING_COLLECTION_EXPECTED_DATE, REMAINING_COLLECTION_DATE, EXPECTED_OCCUPANCY_DATE, OCCUPANCY_DATE, AD, FF, SUPPORT_MECHANISM_CATEGORY, PREFERRED_RENTAL_CATEGORY, FINANCING_CATEGORY, HL_APPROVAL_CATEGORY, HL, RENT_DISCOUNT_FLAG, RENT_DISCOUNT, REFORM_TYPE, REFORM, THREE_MONTH_FF, RECRUITMENT_STATUS_CATEGORY, RESERVE_CATEGORY_2, RESERVE_CATEGORY_3, RESERVE_CATEGORY_4, RESERVE_CATEGORY_5, RESERVE_CATEGORY_6, RESERVE_CATEGORY_7, RESERVE_CATEGORY_8, RESERVE_CATEGORY_9, RESERVE_CATEGORY_10, CONSTRUCTION_REP_CD, CONSTRUCTION_REP_NAME, IMPLEMENTATION_REP_CD, IMPLEMENTATION_REP_NAME, AD_ACTUAL_AMOUNT, FF_ACTUAL_AMOUNT, MUNICIPALITY_CD, MUNICIPALITY_NAME, BUSINESS_ROOM_COUNT_VACANT, LONGEST_VACANCY_PERIOD_BUSINESS, BUSINESS_ROOM_COUNT, STATUS_CODE, EXCLUSION_FLAG, COLLECTION_REP_CD, COLLECTION_REP_NAME, ABC_CATEGORY, MOVE_OUT_MEETING_TIME, MANAGEMENT_REP_CD, MANAGEMENT_REP_NAME, SUPPLY_PLAN_AREA_CODE, SUPPLY_PLAN_AREA_NAME, ASSESSMENT_AREA, STORE_SALES_DEPARTMENT_CD, STORE_SALES_DEPARTMENT_NAME, STORE_SALES_DEPARTMENT_OUTPUT_ORDER_CD, STORE_CD, STORE_NAME, STORE_OUTPUT_ORDER_CD) values
 (20240327, 100641, 20240327, 100913, 'EMU230R', 'BATCH', 0, '4', 203406, '07', '東関東', '000300', '029', '船橋', '01810', 20240401, '空', 0, null, null, 0, '026831901', '山野町マンション', '2404006007', '千葉県船橋市山野町６０－○，○１○', 20340630, 0, '01010', '0101', 'A355854800', '凱盛株○会○', 5, '管（大', '500', '店舗', null, null, 28.36, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 106000, 0, 0, 0, 0, 0, null, 0, 14, 0, 0, 0, 20340701, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '0', '0', null, '80', null, 0, null, null, 0, 0, null, null, null, null, null, null, null, null, null, null, '029617', '田中　○', null, null, 0, 0, '12204', '船橋市', 0, 0, 1, '10', 0, null, null, null, 0, null, null, null, null, null, '07', '東関東', '000300', '284', '船橋', '02020')
,(20240327, 100645, 20240327, 100913, 'EMU230R', 'BATCH', 0, '1', 202312, '12', '東京', '000550', '834', '世田谷', '02560', 20240401, '空', 0, null, null, 0, '025860101', 'Ｇｒａ○ｅ　大○', '2513097003', '東京都世田谷区大原２丁目１９－○未○', 20231227, 0, '01010', '0101', 'A217145900', '島田三○・○田○子', 5, '管（大', '500', '店舗', null, null, 49.64, '14102149', '00', '00', 0, 0, 0, 20231002, 20231002, 20231002, 182, 0, 0, 0, 200000, 0, 200000, 0, 0, 0, null, 0, 10, 10, 0, 0, 20231228, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '0', '0', null, '80', null, 0, null, null, 0, 0, null, null, null, null, null, null, null, null, null, null, '067179', '大澤　○一○', null, null, 0, 0, '13112', '東京都世田谷区', 1, 0, 1, '10', 0, '049105', '遠藤　○医○', null, 0, null, null, null, null, null, '12', '東京', '000550', '201', '渋谷', '02180')
,(20240327, 100645, 20240327, 100913, 'EMU230R', 'BATCH', 0, '4', 999999, '12', '東京', '000550', '834', '世田谷', '02560', 20240401, '空', 0, null, null, 0, '025311001', '経堂１丁目店舗付マンション', '2513158002', '東京都世田谷区経堂１丁目１７８○２○－○', 20260228, 0, '01010', '0101', 'A306927000', '川端　○・○式○社○照', 5, '管（大', '500', '店舗', null, null, 164.83, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1290000, 0, 0, 0, 0, 0, null, 0, 9, 0, 0, 0, 20260301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '0', '0', null, '80', null, 0, null, null, 0, 0, null, null, null, null, null, null, null, null, null, null, '031457', '椎名　○雄', null, null, 0, 0, '13112', '東京都世田谷区', 0, 0, 2, '10', 0, null, null, null, 0, null, null, null, null, null, '12', '東京', '000550', '201', '渋谷', '02180')
,(20240327, 100649, 20240327, 100913, 'EMU230R', 'BATCH', 0, '1', 202403, '15', '南首都圏', '000590', '024', '藤沢', '03160', 20240401, '空', 0, null, null, 0, '025470901', 'ブリー○・○ビ○シ○ン', '2625050009', '神奈川県藤沢市土棚藤沢都○計○事○北○第○（○地○）○地○', 20240325, 0, '01010', '0101', 'A00L727300', '薩田　○', 4, '３０借', '400', '事務所', null, null, 225.08, null, null, null, 0, 0, 0, 20230914, 20230914, 20230914, 200, 0, 0, 0, 636000, 0, 636000, 0, 0, 0, null, 0, 15, 15, 0, 0, 20240326, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '0', '0', null, '10', 'ﾘｰﾌﾞ', 0, null, null, 0, 0, null, null, null, null, null, null, null, null, null, null, '066783', '黒川　○弘', null, null, 0, 0, '14205', '藤沢市', 1, 0, 1, '10', 0, '025871', '大城　○章', null, 0, null, null, null, null, null, '15', '南首都圏', '000590', '321', '藤沢', '02400')
,(20240327, 100649, 20240327, 100913, 'EMU230R', 'BATCH', 0, '4', 999999, '15', '南首都圏', '000590', '024', '藤沢', '03160', 20240401, '空', 0, null, null, 0, '026057901', '南藤沢店舗付マンション', '2625090001', '神奈川県藤沢市南藤沢１１－○、○９○－○０', 20390331, 0, '01010', '0101', 'A164514900', '小塚憲○', 5, '管（大', '500', '店舗', null, null, 107.17, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 584000, 0, 0, 0, 0, 0, null, 0, 25, 0, 0, 0, 20390401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '0', '0', null, '80', null, 0, null, null, 0, 0, null, null, null, null, null, null, null, null, null, null, '066783', '黒川　○弘', null, null, 0, 0, '14205', '藤沢市', 0, 0, 1, '10', 0, null, null, null, 0, null, null, null, null, null, '15', '南首都圏', '000590', '321', '藤沢', '02400')
,(20240327, 100708, 20240327, 100913, 'EMU230R', 'BATCH', 0, '4', 999999, '29', '北九州・沖縄', '000850', '080', '福岡', '05470', 20240401, '空', 0, null, null, 0, '027409901', '今光１丁目倉庫付事務所', '8144003010', '福岡県那珂川市今光１丁目５４の○部○５○－○の○部', 20340915, 0, '01010', '0101', 'A061248600', '簑原サ○子', 5, '管（大', '410', '倉付事', null, null, 125.86, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 224000, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 20340916, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '0', '0', null, '80', null, 0, null, null, 0, 0, null, null, null, null, null, null, null, null, null, null, '028311', '加賀山　太', null, null, 0, 0, '40231', null, 0, 0, 2, '10', 0, null, null, null, 0, null, null, null, null, null, '29', '北九州・沖縄', '000850', '423', '博多', '04360')
,(20240327, 100708, 20240327, 100913, 'EMU230R', 'BATCH', 0, '4', 999999, '29', '北九州・沖縄', '000850', '080', '福岡', '05470', 20240401, '空', 0, null, null, 0, '027409901', '今光１丁目倉庫付事務所', '8144003010', '福岡県那珂川市今光１丁目５４の○部○５○－○の○部', 20340915, 0, '01020', '0102', 'A061248600', '簑原サ○子', 5, '管（大', '410', '倉付事', null, null, 125.86, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 232000, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 20340916, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '0', '0', null, '80', null, 0, null, null, 0, 0, null, null, null, null, null, null, null, null, null, null, '028311', '加賀山　太', null, null, 0, 0, '40231', null, 0, 0, 2, '10', 0, null, null, null, 0, null, null, null, null, null, '29', '北九州・沖縄', '000850', '423', '博多', '04360')
,(20240327, 100712, 20240327, 100913, 'EMU230R', 'BATCH', 0, '4', 999999, '29', '北九州・沖縄', '000850', '097', '沖縄', '05710', 20240401, '空', 0, null, null, 0, '025072401', 'おもろまち４丁目店舗付マンション', '9301081040', '沖縄県那覇市おもろまち４丁目１１－○２', 20350430, 0, '01010', '0101', 'A295917400', '久場克○', 5, '管（大', '500', '店舗', null, null, 216, null, null, null, 0, 20190823, 1683, 0, 0, 0, 0, 0, 0, 0, 1799000, 1799000, 0, 0, 0, 0, null, 0, 30, 0, 0, 0, 20350501, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '0', '0', null, '80', null, 0, null, null, 0, 0, null, null, null, null, null, null, null, null, null, null, '030842', '岡　秀○', null, null, 0, 0, '47201', '那覇市', 0, 0, 4, '10', 0, null, null, null, 0, null, null, null, null, null, '29', '北九州・沖縄', '000850', '444', '新都心', '04560')
;
