package jp.ne.simplex.application.controller.client.shared

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Property

/** クライアントと Property をやり取りするための共通DTO */
data class ClientPropertyIdDto(
    @JsonProperty("buildingCode")
    @field:Schema(description = "建物コード")
    val buildingCode: String,

    @JsonProperty("roomCode")
    @field:Schema(description = "部屋コード")
    val roomCode: String
) {
    companion object {
        fun of(propertyId: Property.Id): ClientPropertyIdDto {
            return ClientPropertyIdDto(
                buildingCode = propertyId.buildingCode.value,
                roomCode = propertyId.roomCode.value
            )
        }
    }
}
