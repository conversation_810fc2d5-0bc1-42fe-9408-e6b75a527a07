-- TABLE: BUILDING_LOCATION_INFO(建物位置情報)

CREATE TABLE BUILDING_LOCATION_INFO(
     BUILDING_CD                                  varchar(9)        NOT NULL    
,    LATITUDE                                     numeric(9,0)                  
,    LONGITUDE                                    numeric(9,0)                  
,    MATCHING_LEVEL                               numeric(1,0)                  
,    CREATOR                                      varchar(6)                    
,    CREATE_PROGRAM                               varchar(10)                   
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    UPDATE_PROGRAM                               varchar(10)                   
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    CONSTRAINT PK_BUILDING_LOCATION_INFO PRIMARY KEY (BUILDING_CD)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BUILDING_LOCATION_INFO IS '建物位置情報 既存システム物理名: EMECMP';
COMMENT ON COLUMN BUILDING_LOCATION_INFO.BUILDING_CD IS '建物CD 既存システム物理名: EMETCD 日本測地系';
COMMENT ON COLUMN BUILDING_LOCATION_INFO.LATITUDE IS '緯度 既存システム物理名: EMEIDO 日本測地系';
COMMENT ON COLUMN BUILDING_LOCATION_INFO.LONGITUDE IS '経度 既存システム物理名: EMEKDO';
COMMENT ON COLUMN BUILDING_LOCATION_INFO.MATCHING_LEVEL IS 'マッチングレベル 既存システム物理名: EMEMLV';
COMMENT ON COLUMN BUILDING_LOCATION_INFO.CREATOR IS '作成者 既存システム物理名: EMEC1C';
COMMENT ON COLUMN BUILDING_LOCATION_INFO.CREATE_PROGRAM IS '作成プログラム 既存システム物理名: EMEC2C';
COMMENT ON COLUMN BUILDING_LOCATION_INFO.CREATION_DATE IS '作成年月日 既存システム物理名: EMEC3D';
COMMENT ON COLUMN BUILDING_LOCATION_INFO.CREATION_TIME IS '作成時間 既存システム物理名: EMEC4H';
COMMENT ON COLUMN BUILDING_LOCATION_INFO.UPDATER IS '更新者 既存システム物理名: EMEC5C';
COMMENT ON COLUMN BUILDING_LOCATION_INFO.UPDATE_PROGRAM IS '更新プログラム 既存システム物理名: EMEC6C';
COMMENT ON COLUMN BUILDING_LOCATION_INFO.UPDATE_DATE IS '更新年月日 既存システム物理名: EMEC7D';
COMMENT ON COLUMN BUILDING_LOCATION_INFO.UPDATE_TIME IS '更新時刻 既存システム物理名: EMEC8H';
