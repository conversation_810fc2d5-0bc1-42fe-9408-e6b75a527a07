truncate table BUILDING_STORE_MASTER;
insert into BUILDING_STORE_MASTER (CREATION_DATE, CREATION_TIME, UPDATE_DATE, UPDATE_TIME, UPDATE_PROGRAM_ID, UPDATER, BUILDING_CODE, LEASING_STORE_CODE) values
 (20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000000101', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000000201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000000401', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000001101', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000001301', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000001401', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000001501', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000001502', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000001801', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000003101', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000003501', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000003901', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000004201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000006401', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000006501', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000007601', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000007701', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000009101', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000009701', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000009801', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000010001', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000010201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000010401', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000010601', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000010701', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000010801', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000011001', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000011201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000011701', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000011702', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000011901', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000012201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000012301', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000013401', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000013402', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000013501', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000014501', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000014502', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000015001', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000016101', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000016901', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000017201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000017401', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000017501', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000017901', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000017902', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000018201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000019601', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000019901', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000020801', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000020901', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000021101', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000021201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000023101', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000023301', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000023501', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000023601', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000024301', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000024501', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000024701', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000024801', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000024901', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000025001', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000025201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000025401', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000025501', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000025502', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000025701', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000025801', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000025901', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000026201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000026501', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000026601', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000026801', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000027101', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000027201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000027301', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000027501', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000027601', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000027801', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000027901', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000028001', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000028101', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000028102', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000028301', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000028401', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000029001', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000029201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000030001', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000030201', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000030301', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000030401', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000030501', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000030701', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000030901', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000031101', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000031401', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000031701', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000031901', '340094')
,(20170423, 193310, 20170926, 80948, 'ETA051R', 'ｾｯﾄｱｯﾌﾟ', '000032101', '340094')
;
