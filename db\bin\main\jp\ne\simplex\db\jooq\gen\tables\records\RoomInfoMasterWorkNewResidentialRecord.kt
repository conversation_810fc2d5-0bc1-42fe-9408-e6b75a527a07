/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.RoomInfoMasterWorkNewResidentialTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.RoomInfoMasterWorkNewResidentialPojo

import org.jooq.impl.TableRecordImpl


/**
 * 物件ボード用空き新築物件ワーク 既存システム物理名: EMU31P
 */
@Suppress("UNCHECKED_CAST")
open class RoomInfoMasterWorkNewResidentialRecord private constructor() : TableRecordImpl<RoomInfoMasterWorkNewResidentialRecord>(RoomInfoMasterWorkNewResidentialTable.ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL) {

    open var branchCode: String?
        set(value): Unit = set(0, value)
        get(): String? = get(0) as String?

    open var status: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    open var date: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var negotiation: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var buildingCode: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var roomCode: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var landlordName: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var buildingName: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var layoutName: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var layout: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var address_1: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var address_2: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var rent: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    open var parkingFee: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var commonFee: Short?
        set(value): Unit = set(14, value)
        get(): Short? = get(14) as Short?

    open var keyMoney: Byte?
        set(value): Unit = set(15, value)
        get(): Byte? = get(15) as Byte?

    open var securityDeposit: Byte?
        set(value): Unit = set(16, value)
        get(): Byte? = get(16) as Byte?

    open var neighborhoodAssociationFee: Int?
        set(value): Unit = set(17, value)
        get(): Int? = get(17) as Int?

    open var remarks_1: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var remarks_2: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var rockyCategory: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var categoryA: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var categoryB: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var prefectureCode: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var cityCode: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var townCode: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var propertyAddressKana: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var townNameKana: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var roomNumber: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var moveInYear: Short?
        set(value): Unit = set(29, value)
        get(): Short? = get(29) as Short?

    open var moveInMonth: Byte?
        set(value): Unit = set(30, value)
        get(): Byte? = get(30) as Byte?

    open var season: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var time: Short?
        set(value): Unit = set(32, value)
        get(): Short? = get(32) as Short?

    open var extractionBranchCode: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var address_3: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var moveInAvailableDate: Int?
        set(value): Unit = set(35, value)
        get(): Int? = get(35) as Int?

    /**
     * Create a detached, initialised RoomInfoMasterWorkNewResidentialRecord
     */
    constructor(branchCode: String? = null, status: String? = null, date: Int? = null, negotiation: String? = null, buildingCode: String? = null, roomCode: String? = null, landlordName: String? = null, buildingName: String? = null, layoutName: String? = null, layout: String? = null, address_1: String? = null, address_2: String? = null, rent: Int? = null, parkingFee: Int? = null, commonFee: Short? = null, keyMoney: Byte? = null, securityDeposit: Byte? = null, neighborhoodAssociationFee: Int? = null, remarks_1: String? = null, remarks_2: String? = null, rockyCategory: String? = null, categoryA: String? = null, categoryB: String? = null, prefectureCode: String? = null, cityCode: String? = null, townCode: String? = null, propertyAddressKana: String? = null, townNameKana: String? = null, roomNumber: String? = null, moveInYear: Short? = null, moveInMonth: Byte? = null, season: String? = null, time: Short? = null, extractionBranchCode: String? = null, address_3: String? = null, moveInAvailableDate: Int? = null): this() {
        this.branchCode = branchCode
        this.status = status
        this.date = date
        this.negotiation = negotiation
        this.buildingCode = buildingCode
        this.roomCode = roomCode
        this.landlordName = landlordName
        this.buildingName = buildingName
        this.layoutName = layoutName
        this.layout = layout
        this.address_1 = address_1
        this.address_2 = address_2
        this.rent = rent
        this.parkingFee = parkingFee
        this.commonFee = commonFee
        this.keyMoney = keyMoney
        this.securityDeposit = securityDeposit
        this.neighborhoodAssociationFee = neighborhoodAssociationFee
        this.remarks_1 = remarks_1
        this.remarks_2 = remarks_2
        this.rockyCategory = rockyCategory
        this.categoryA = categoryA
        this.categoryB = categoryB
        this.prefectureCode = prefectureCode
        this.cityCode = cityCode
        this.townCode = townCode
        this.propertyAddressKana = propertyAddressKana
        this.townNameKana = townNameKana
        this.roomNumber = roomNumber
        this.moveInYear = moveInYear
        this.moveInMonth = moveInMonth
        this.season = season
        this.time = time
        this.extractionBranchCode = extractionBranchCode
        this.address_3 = address_3
        this.moveInAvailableDate = moveInAvailableDate
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised RoomInfoMasterWorkNewResidentialRecord
     */
    constructor(value: RoomInfoMasterWorkNewResidentialPojo?): this() {
        if (value != null) {
            this.branchCode = value.branchCode
            this.status = value.status
            this.date = value.date
            this.negotiation = value.negotiation
            this.buildingCode = value.buildingCode
            this.roomCode = value.roomCode
            this.landlordName = value.landlordName
            this.buildingName = value.buildingName
            this.layoutName = value.layoutName
            this.layout = value.layout
            this.address_1 = value.address_1
            this.address_2 = value.address_2
            this.rent = value.rent
            this.parkingFee = value.parkingFee
            this.commonFee = value.commonFee
            this.keyMoney = value.keyMoney
            this.securityDeposit = value.securityDeposit
            this.neighborhoodAssociationFee = value.neighborhoodAssociationFee
            this.remarks_1 = value.remarks_1
            this.remarks_2 = value.remarks_2
            this.rockyCategory = value.rockyCategory
            this.categoryA = value.categoryA
            this.categoryB = value.categoryB
            this.prefectureCode = value.prefectureCode
            this.cityCode = value.cityCode
            this.townCode = value.townCode
            this.propertyAddressKana = value.propertyAddressKana
            this.townNameKana = value.townNameKana
            this.roomNumber = value.roomNumber
            this.moveInYear = value.moveInYear
            this.moveInMonth = value.moveInMonth
            this.season = value.season
            this.time = value.time
            this.extractionBranchCode = value.extractionBranchCode
            this.address_3 = value.address_3
            this.moveInAvailableDate = value.moveInAvailableDate
            resetChangedOnNotNull()
        }
    }
}
