/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.AutoLockNoTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.AutoLockNoPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * オートロックNo 既存システム物理名: EMKEYP
 */
@Suppress("UNCHECKED_CAST")
open class AutoLockNoRecord private constructor() : UpdatableRecordImpl<AutoLockNoRecord>(AutoLockNoTable.AUTO_LOCK_NO) {

    open var creationDate: Long?
        set(value): Unit = set(0, value)
        get(): Long? = get(0) as Long?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Long?
        set(value): Unit = set(3, value)
        get(): Long? = get(3) as Long?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var updateProgramId: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(7, value)
        get(): Byte? = get(7) as Byte?

    open var buildingCode: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    open var autolockReleaseNo: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var rentFixedFlag: Byte?
        set(value): Unit = set(10, value)
        get(): Byte? = get(10) as Byte?

    open var homeSecurity: Byte?
        set(value): Unit = set(11, value)
        get(): Byte? = get(11) as Byte?

    open var reserve_03: Byte?
        set(value): Unit = set(12, value)
        get(): Byte? = get(12) as Byte?

    open var reserve_04: Byte?
        set(value): Unit = set(13, value)
        get(): Byte? = get(13) as Byte?

    open var reserve_05: Byte?
        set(value): Unit = set(14, value)
        get(): Byte? = get(14) as Byte?

    open var reserve_06: Byte?
        set(value): Unit = set(15, value)
        get(): Byte? = get(15) as Byte?

    open var reserve_07: Byte?
        set(value): Unit = set(16, value)
        get(): Byte? = get(16) as Byte?

    open var reserve_08: Byte?
        set(value): Unit = set(17, value)
        get(): Byte? = get(17) as Byte?

    open var reserve_09: Byte?
        set(value): Unit = set(18, value)
        get(): Byte? = get(18) as Byte?

    open var reserve_10: Byte?
        set(value): Unit = set(19, value)
        get(): Byte? = get(19) as Byte?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised AutoLockNoRecord
     */
    constructor(creationDate: Long? = null, creationTime: Int? = null, creator: String? = null, updateDate: Long? = null, updateTime: Int? = null, updater: String? = null, updateProgramId: String? = null, logicalDeleteSign: Byte? = null, buildingCode: String, autolockReleaseNo: String? = null, rentFixedFlag: Byte? = null, homeSecurity: Byte? = null, reserve_03: Byte? = null, reserve_04: Byte? = null, reserve_05: Byte? = null, reserve_06: Byte? = null, reserve_07: Byte? = null, reserve_08: Byte? = null, reserve_09: Byte? = null, reserve_10: Byte? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.updateProgramId = updateProgramId
        this.logicalDeleteSign = logicalDeleteSign
        this.buildingCode = buildingCode
        this.autolockReleaseNo = autolockReleaseNo
        this.rentFixedFlag = rentFixedFlag
        this.homeSecurity = homeSecurity
        this.reserve_03 = reserve_03
        this.reserve_04 = reserve_04
        this.reserve_05 = reserve_05
        this.reserve_06 = reserve_06
        this.reserve_07 = reserve_07
        this.reserve_08 = reserve_08
        this.reserve_09 = reserve_09
        this.reserve_10 = reserve_10
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised AutoLockNoRecord
     */
    constructor(value: AutoLockNoPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.updateProgramId = value.updateProgramId
            this.logicalDeleteSign = value.logicalDeleteSign
            this.buildingCode = value.buildingCode
            this.autolockReleaseNo = value.autolockReleaseNo
            this.rentFixedFlag = value.rentFixedFlag
            this.homeSecurity = value.homeSecurity
            this.reserve_03 = value.reserve_03
            this.reserve_04 = value.reserve_04
            this.reserve_05 = value.reserve_05
            this.reserve_06 = value.reserve_06
            this.reserve_07 = value.reserve_07
            this.reserve_08 = value.reserve_08
            this.reserve_09 = value.reserve_09
            this.reserve_10 = value.reserve_10
            resetChangedOnNotNull()
        }
    }
}
