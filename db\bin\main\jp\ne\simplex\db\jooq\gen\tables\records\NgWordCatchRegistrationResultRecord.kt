/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.NgWordCatchRegistrationResultTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.NgWordCatchRegistrationResultPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * NGワードキャッチ登録結果 既存システム物理名: EMETNP
 */
@Suppress("UNCHECKED_CAST")
open class NgWordCatchRegistrationResultRecord private constructor() : UpdatableRecordImpl<NgWordCatchRegistrationResultRecord>(NgWordCatchRegistrationResultTable.NG_WORD_CATCH_REGISTRATION_RESULT) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var branchCd: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var buildingCd: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var cancelFlag: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var ngWord: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised NgWordCatchRegistrationResultRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, branchCd: String, buildingCd: String, cancelFlag: String? = null, ngWord: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.branchCd = branchCd
        this.buildingCd = buildingCd
        this.cancelFlag = cancelFlag
        this.ngWord = ngWord
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised NgWordCatchRegistrationResultRecord
     */
    constructor(value: NgWordCatchRegistrationResultPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.branchCd = value.branchCd
            this.buildingCd = value.buildingCd
            this.cancelFlag = value.cancelFlag
            this.ngWord = value.ngWord
            resetChangedOnNotNull()
        }
    }
}
