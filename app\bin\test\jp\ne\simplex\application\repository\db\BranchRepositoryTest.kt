package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Company
import jp.ne.simplex.db.jooq.gen.tables.references.AFFILIATION_MASTER
import jp.ne.simplex.db.jooq.gen.tables.references.BRANCH_FILE
import jp.ne.simplex.db.jooq.gen.tables.references.KT_ALL_BRANCH
import jp.ne.simplex.db.jooq.gen.tables.references.REGION_MASTER
import jp.ne.simplex.mock.MockLocalDateTime
import jp.ne.simplex.stub.stubAffiliationMasterPojo
import jp.ne.simplex.stub.stubBranchFilePojo
import jp.ne.simplex.stub.stubKtAllBranchPojo
import jp.ne.simplex.stub.stubRegionMasterPojo
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.math.BigInteger
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.assertEquals

class BranchRepositoryTest : AbstractTestContainerTest() {

    private lateinit var repository: BranchRepository

    private val currentDateTime = LocalDateTime.of(2025, 3, 13, 12, 30, 45)

    override fun beforeEach() {
        MockLocalDateTime.setNow(currentDateTime)

        repository = BranchRepository(dslContext)
    }

    @AfterEach
    fun tearDown() {
        MockLocalDateTime.close()
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(
            AFFILIATION_MASTER,
            REGION_MASTER,
            BRANCH_FILE,
            KT_ALL_BRANCH,
        )
    }

    @Nested
    @DisplayName("支店コードから、審査支店、リーシング/センター支店を取得できること")
    inner class Scenario1 {

        val searchBranchCode = Branch.Code.of("123")

        @Nested
        @DisplayName("審査支店マスタに、指定された支店コードに紐づくレコードが存在する場合")
        inner class Scenario1x1 {

            @Test
            @DisplayName("全ての条件に合致する場合、店舗/審査支店を取得できること（細かい条件までは確認しない）")
            fun case1() {
                // setup data
                val affiliationMasterPojo = stubAffiliationMasterPojo(
                    shozokuCode = "${searchBranchCode.getPrefix()}800",
                    shozokuAbbrev1 = "銀座一丁目",
                    company = Company.DaitouKentaku,
                    hierarchyDivision = "30",
                    usageStartDate = LocalDate.of(2024, 1, 1),
                    usageEndDate = LocalDate.of(2099, 12, 31),
                )

                val regionMasterPojo = stubRegionMasterPojo(
                    departmentCode = affiliationMasterPojo.shozokuCode!!,
                    regionCode2 = "40",
                    useStartDate = LocalDate.of(2024, 1, 1),
                    useFinishDate = LocalDate.of(2099, 12, 31),
                )

                val branchFilePojo = stubBranchFilePojo(
                    branchCode = "${searchBranchCode.getPrefix()}000"
                )

                // setup db
                dslContext.saveAffiliationMasterPojo(affiliationMasterPojo)
                dslContext.saveRegionMasterPojo(regionMasterPojo)
                dslContext.saveBranchFilePojo(branchFilePojo)

                // execute
                val actual = repository.getKtBranch(searchBranchCode)!!

                // verify
                assertEquals("${searchBranchCode.getPrefix()}000", actual.code.getValue())
                assertEquals("${affiliationMasterPojo.shozokuAbbrev1!!}支店", actual.name.value)
                assertEquals(Company.DaitouKentakuPartners, actual.company)
            }
        }

        @Nested
        @DisplayName("審査支店マスタに、指定された支店コードに紐づくレコードが存在しない場合")
        inner class Scenario1x2 {

            @Nested
            @DisplayName("リーシング/センター支店マスタに、指定された支店コードに紐づくレコードが存在する場合")
            inner class Scenario1x2x1 {

                @Test
                @DisplayName("全ての条件に合致する場合、リーシング/センター支店を取得できること（細かい条件までは確認しない）")
                fun case1() {
                    // setup data
                    val validBranchPojo = stubKtAllBranchPojo(
                        branchCode = "${searchBranchCode.getPrefix()}000",
                        branchName = "渋谷支店",
                        companyCode = BigInteger.ZERO, // 0: リーシング、1:大東建託、3:パートナーズ
                        eboardCompany = Company.DaitouKentakuLeasing,
                    )

                    val invalidBranchPojo = stubKtAllBranchPojo(
                        branchCode = "${searchBranchCode.getPrefix()}800",
                        branchName = "原宿支店",
                        companyCode = BigInteger.ZERO, // 0: リーシング、1:大東建託、3:パートナーズ
                        eboardCompany = Company.DaitouKentakuLeasing,
                    )

                    // setup db
                    dslContext.saveKtAllBranchPojo(validBranchPojo, invalidBranchPojo)

                    // execute
                    val actual = repository.getKtBranch(searchBranchCode)!!

                    // verify
                    assertEquals("${searchBranchCode.getPrefix()}000", actual.code.getValue())
                    assertEquals(validBranchPojo.branchName!!, actual.name.value)
                    assertEquals(Company.DaitouKentakuLeasing, actual.company)
                }
            }

            @Nested
            @DisplayName("リーシング/センター支店マスタに、指定された支店コードに紐づくレコードが存在しない場合")
            inner class Scenario1x2x2 {

                @Test
                @DisplayName("null返却されること")
                fun case1() {
                    // execute & verify
                    assertNull(repository.getKtBranch(searchBranchCode))
                }
            }
        }
    }

    @Nested
    @DisplayName("審査支店、リーシング/センター支店一覧を取得できること")
    inner class Scenario2 {

        @Nested
        @DisplayName("審査支店一覧を取得できること")
        inner class Scenario2x1 {
            // setup data
            val branchCodePrefix = "942"

            val affiliationMasterPojo = stubAffiliationMasterPojo(
                shozokuCode = "${branchCodePrefix}800",
                shozokuAbbrev1 = "銀座一丁目",
                company = Company.DaitouKentaku,
                hierarchyDivision = "30",
                usageStartDate = LocalDate.of(2024, 1, 1),
                usageEndDate = LocalDate.of(2099, 12, 31),
            )

            val regionMasterPojo = stubRegionMasterPojo(
                departmentCode = affiliationMasterPojo.shozokuCode!!,
                regionCode2 = "40",
                useStartDate = LocalDate.of(2024, 1, 1),
                useFinishDate = LocalDate.of(2099, 12, 31),
            )

            val branchFilePojo = stubBranchFilePojo(
                branchCode = "${branchCodePrefix}000"
            )

            @Test
            @DisplayName("全ての条件に合致する場合、店舗/審査支店を取得できること")
            fun case1() {
                // setup
                dslContext.saveAffiliationMasterPojo(affiliationMasterPojo)
                dslContext.saveRegionMasterPojo(regionMasterPojo)
                dslContext.saveBranchFilePojo(branchFilePojo)

                // execute
                val actual = repository.getKtBranchList()

                // verify
                assertEquals(1, actual.size)
            }

            @Nested
            @DisplayName("AffiliationMasterの絞り込み処理が適切に行われていること")
            inner class Scenario2x1x1 {
                @Test
                @DisplayName("AffiliationMasterの「SHOZOKU_CODE」は、suffixが800で絞り込みされること")
                fun case1() {
                    // setup
                    dslContext.saveAffiliationMasterPojo(
                        affiliationMasterPojo.copy(shozokuCode = "${branchCodePrefix}300")
                    )
                    dslContext.saveRegionMasterPojo(
                        regionMasterPojo.copy(departmentCode = "${branchCodePrefix}300")
                    )
                    dslContext.saveBranchFilePojo(branchFilePojo)

                    // execute
                    val actual = repository.getKtBranchList()

                    // verify
                    assertEquals(0, actual.size)
                }

                @Test
                @DisplayName("AffiliationMasterの「COMPANY_CODE」は、大東建託で絞り込みされること")
                fun case2() {
                    // setup
                    dslContext.saveAffiliationMasterPojo(
                        affiliationMasterPojo.copy(companyCode = Company.DaitouKentakuLeasing.code)
                    )
                    dslContext.saveRegionMasterPojo(regionMasterPojo)
                    dslContext.saveBranchFilePojo(branchFilePojo)

                    // execute
                    val actual = repository.getKtBranchList()

                    // verify
                    assertEquals(0, actual.size)
                }

                @Test
                @DisplayName("AffiliationMasterの「HIERARCHY_DIVISION」は、30で絞り込みされること")
                fun case3() {
                    // setup
                    dslContext.saveAffiliationMasterPojo(
                        affiliationMasterPojo.copy(hierarchyDivision = "40")
                    )
                    dslContext.saveRegionMasterPojo(regionMasterPojo)
                    dslContext.saveBranchFilePojo(branchFilePojo)

                    // execute
                    val actual = repository.getKtBranchList()

                    // verify
                    assertEquals(0, actual.size)
                }

                @Test
                @DisplayName("AffiliationMasterの「USAGE_START_DATE」は、現在時刻以前で絞り込みされること")
                fun case4() {
                    // setup
                    // 現在時刻は LocalDateTime.of(2025, 3, 13, 12, 30, 45) で固定中
                    dslContext.saveAffiliationMasterPojo(
                        affiliationMasterPojo.copy(usageStartDate = 20250401)
                    )
                    dslContext.saveRegionMasterPojo(regionMasterPojo)
                    dslContext.saveBranchFilePojo(branchFilePojo)

                    // execute
                    val actual = repository.getKtBranchList()

                    // verify
                    assertEquals(0, actual.size)
                }

                @Test
                @DisplayName("AffiliationMasterの「USAGE_END_DATE」は、現在時刻以降で絞り込みされること")
                fun case5() {
                    // setup
                    // 現在時刻は LocalDateTime.of(2025, 3, 13, 12, 30, 45) で固定中
                    dslContext.saveAffiliationMasterPojo(
                        affiliationMasterPojo.copy(usageEndDate = 20250301)
                    )
                    dslContext.saveRegionMasterPojo(regionMasterPojo)
                    dslContext.saveBranchFilePojo(branchFilePojo)

                    // execute
                    val actual = repository.getKtBranchList()

                    // verify
                    assertEquals(0, actual.size)
                }
            }

            @Nested
            @DisplayName("RegionMasterの絞り込み処理が適切に行われていること")
            inner class Scenario2x1x2 {

                @Test
                @DisplayName("RegionMasterの「REGION_CODE_2」は、50未満で絞り込みされること")
                fun case1() {
                    // setup
                    dslContext.saveAffiliationMasterPojo(affiliationMasterPojo)
                    dslContext.saveRegionMasterPojo(
                        regionMasterPojo.copy(regionCode_2 = "50")
                    )
                    dslContext.saveBranchFilePojo(branchFilePojo)

                    // execute
                    val actual = repository.getKtBranchList()

                    // verify
                    assertEquals(0, actual.size)
                }

                @Test
                @DisplayName("RegionMasterの「USE_START_DATE」は、現在時刻以前で絞り込みされること")
                fun case2() {
                    // setup
                    dslContext.saveAffiliationMasterPojo(affiliationMasterPojo)
                    // 現在時刻は LocalDateTime.of(2025, 3, 13, 12, 30, 45) で固定中
                    dslContext.saveRegionMasterPojo(
                        regionMasterPojo.copy(useStartDate = 20250401)
                    )
                    dslContext.saveBranchFilePojo(branchFilePojo)

                    // execute
                    val actual = repository.getKtBranchList()

                    // verify
                    assertEquals(0, actual.size)
                }

                @Test
                @DisplayName("RegionMasterの「USE_FINISH_DATE」は、現在時刻以降で絞り込みされること")
                fun case3() {
                    // setup
                    dslContext.saveAffiliationMasterPojo(affiliationMasterPojo)
                    // 現在時刻は LocalDateTime.of(2025, 3, 13, 12, 30, 45) で固定中
                    dslContext.saveRegionMasterPojo(
                        regionMasterPojo.copy(useFinishDate = 20250301)
                    )
                    dslContext.saveBranchFilePojo(branchFilePojo)

                    // execute
                    val actual = repository.getKtBranchList()

                    // verify
                    assertEquals(0, actual.size)
                }
            }
        }

        @Nested
        @DisplayName("リーシング/センター支店一覧を取得できること")
        inner class Scenario2x2 {

            @Test
            @DisplayName("所属会社がリーシングか大東建託の支店のみが取得できること")
            fun case1() {
                // setup data
                val leasingBranch = stubKtAllBranchPojo(
                    branchCode = "789000",
                    branchName = "渋谷支店",
                    companyCode = BigInteger.ZERO, // 0: リーシング、1:大東建託、3:パートナーズ
                    eboardCompany = Company.DaitouKentakuLeasing,
                )

                val daitouBranch = stubKtAllBranchPojo(
                    branchCode = "521000",
                    branchName = "原宿支店",
                    companyCode = BigInteger.ONE, // 0: リーシング、1:大東建託、3:パートナーズ
                    eboardCompany = Company.DaitouKentaku,
                )

                val partnersBranch = stubKtAllBranchPojo(
                    branchCode = "351000",
                    branchName = "札幌営業所",
                    companyCode = BigInteger.valueOf(3), // 0: リーシング、1:大東建託、3:パートナーズ
                    eboardCompany = Company.DaitouKentakuPartners,
                )

                // setup db
                dslContext.saveKtAllBranchPojo(leasingBranch, daitouBranch, partnersBranch)

                // execute
                val actual = repository.getKtBranchList()

                // verify
                assertEquals(2, actual.size)

                assertTrue(actual.map { it.code.getValue() }.contains(leasingBranch.branchCode))
                assertTrue(actual.map { it.name.value }.contains(leasingBranch.branchName!!))

                assertTrue(actual.map { it.code.getValue() }.contains(daitouBranch.branchCode))
                assertTrue(actual.map { it.name.value }.contains(daitouBranch.branchName!!))
            }
        }
    }
}
