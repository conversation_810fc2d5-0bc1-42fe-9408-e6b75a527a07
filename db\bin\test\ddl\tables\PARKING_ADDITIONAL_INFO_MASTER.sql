-- TABLE: PARKING_ADDITIONAL_INFO_MASTER(駐車場付加情報DB)

CREATE TABLE PARKING_ADDITIONAL_INFO_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_FLAG                          numeric(1,0)                  
,    CONTRACT_NUMBER                              varchar(8)                    
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    PARKING_CODE                                 varchar(3)        NOT NULL    
,    EXTERNAL_LENDING_CATEGORY                    varchar(1)                    
,    ROOM_BUILDING_CODE                           varchar(9)                    
,    ROOM_ROOM_CODE                               varchar(5)                    
,    ROOM_CONTRACT_NUMBER                         varchar(8)                    
,    CONSTRAINT PK_PARKING_ADDITIONAL_INFO_MAS PRIMARY KEY (BUILDING_CODE, PARKING_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PARKING_ADDITIONAL_INFO_MASTER IS '駐車場付加情報DB 既存システム物理名: ECC90P';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: EC901D';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: EC902H';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: EC903D';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: EC904H';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムＩＤ 既存システム物理名: EC905N';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.UPDATER IS '更新者 既存システム物理名: EC906C';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.LOGICAL_DELETE_FLAG IS '論理削除サイン 既存システム物理名: EC907S';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.CONTRACT_NUMBER IS '契約番号 既存システム物理名: EC9PKN';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.BUILDING_CODE IS '建物コード 既存システム物理名: EC9ABC';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.PARKING_CODE IS '駐車場コード 既存システム物理名: EC9BSC';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.EXTERNAL_LENDING_CATEGORY IS '外部貸し区分 既存システム物理名: EC911B';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.ROOM_BUILDING_CODE IS '部屋／建物コード 既存システム物理名: EC912C';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.ROOM_ROOM_CODE IS '部屋／部屋コード 既存システム物理名: EC913C';
COMMENT ON COLUMN PARKING_ADDITIONAL_INFO_MASTER.ROOM_CONTRACT_NUMBER IS '部屋／契約番号 既存システム物理名: EC9AKN';
