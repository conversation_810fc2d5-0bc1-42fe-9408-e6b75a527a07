/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * This class is generated by jOOQ.
 */
@Suppress("UNCHECKED_CAST")
data class ProductMasterVPojo(
    var productNameCode: Short? = null,
    var productCodeBranch: Byte? = null,
    var gradeName: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: ProductMasterVPojo = other as ProductMasterVPojo
        if (this.productNameCode == null) {
            if (o.productNameCode != null)
                return false
        }
        else if (this.productNameCode != o.productNameCode)
            return false
        if (this.productCodeBranch == null) {
            if (o.productCodeBranch != null)
                return false
        }
        else if (this.productCodeBranch != o.productCodeBranch)
            return false
        if (this.gradeName == null) {
            if (o.gradeName != null)
                return false
        }
        else if (this.gradeName != o.gradeName)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.productNameCode == null) 0 else this.productNameCode.hashCode())
        result = prime * result + (if (this.productCodeBranch == null) 0 else this.productCodeBranch.hashCode())
        result = prime * result + (if (this.gradeName == null) 0 else this.gradeName.hashCode())
        return result
    }
}
