/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ElectricityBusinessMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ElectricityBusinessMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 電力事業マスタ 既存システム物理名: BGDENP
 */
@Suppress("UNCHECKED_CAST")
open class ElectricityBusinessMasterRecord private constructor() : TableRecordImpl<ElectricityBusinessMasterRecord>(ElectricityBusinessMasterTable.ELECTRICITY_BUSINESS_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updaterId: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteFlag: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var electricityBusinessType: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var electricityBusinessTypeName: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var displayOrder: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var reportCategory: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var smileInfoDisplayFlag: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var explanationSpecialContractDisplayFlag: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var zehFlag: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var reserve1Flag: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var reserve1Value: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var reserve2Flag: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var reserve2Value: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var reserve3Flag: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var reserve3Value: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    /**
     * Create a detached, initialised ElectricityBusinessMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updaterId: String? = null, logicalDeleteFlag: String? = null, electricityBusinessType: String? = null, electricityBusinessTypeName: String? = null, displayOrder: String? = null, reportCategory: String? = null, smileInfoDisplayFlag: String? = null, explanationSpecialContractDisplayFlag: String? = null, zehFlag: String? = null, reserve1Flag: String? = null, reserve1Value: String? = null, reserve2Flag: String? = null, reserve2Value: String? = null, reserve3Flag: String? = null, reserve3Value: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updaterId = updaterId
        this.logicalDeleteFlag = logicalDeleteFlag
        this.electricityBusinessType = electricityBusinessType
        this.electricityBusinessTypeName = electricityBusinessTypeName
        this.displayOrder = displayOrder
        this.reportCategory = reportCategory
        this.smileInfoDisplayFlag = smileInfoDisplayFlag
        this.explanationSpecialContractDisplayFlag = explanationSpecialContractDisplayFlag
        this.zehFlag = zehFlag
        this.reserve1Flag = reserve1Flag
        this.reserve1Value = reserve1Value
        this.reserve2Flag = reserve2Flag
        this.reserve2Value = reserve2Value
        this.reserve3Flag = reserve3Flag
        this.reserve3Value = reserve3Value
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ElectricityBusinessMasterRecord
     */
    constructor(value: ElectricityBusinessMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updaterId = value.updaterId
            this.logicalDeleteFlag = value.logicalDeleteFlag
            this.electricityBusinessType = value.electricityBusinessType
            this.electricityBusinessTypeName = value.electricityBusinessTypeName
            this.displayOrder = value.displayOrder
            this.reportCategory = value.reportCategory
            this.smileInfoDisplayFlag = value.smileInfoDisplayFlag
            this.explanationSpecialContractDisplayFlag = value.explanationSpecialContractDisplayFlag
            this.zehFlag = value.zehFlag
            this.reserve1Flag = value.reserve1Flag
            this.reserve1Value = value.reserve1Value
            this.reserve2Flag = value.reserve2Flag
            this.reserve2Value = value.reserve2Value
            this.reserve3Flag = value.reserve3Flag
            this.reserve3Value = value.reserve3Value
            resetChangedOnNotNull()
        }
    }
}
