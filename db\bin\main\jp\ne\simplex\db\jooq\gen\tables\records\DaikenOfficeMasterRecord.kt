/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.DaikenOfficeMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.DaikenOfficeMasterPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 大建営業所マスタ 既存システム物理名: HKA10P
 */
@Suppress("UNCHECKED_CAST")
open class DaikenOfficeMasterRecord private constructor() : UpdatableRecordImpl<DaikenOfficeMasterRecord>(DaikenOfficeMasterTable.DAIKEN_OFFICE_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateResponsibleCode: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var daikenSalesOfficeCode: Short
        set(value): Unit = set(6, value)
        get(): Short = get(6) as Short

    open var postalCode: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var address: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var buildingName: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var phoneNumber: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var faxNumber: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var deletionDate: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    open var representKentakuMngBranchCode: Short
        set(value): Unit = set(13, value)
        get(): Short = get(13) as Short

    open var usageStartDate: Int?
        set(value): Unit = set(14, value)
        get(): Int? = get(14) as Int?

    open var satelliteCategory: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<Short?, Short?> = super.key() as Record2<Short?, Short?>

    /**
     * Create a detached, initialised DaikenOfficeMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updateResponsibleCode: String? = null, daikenSalesOfficeCode: Short, postalCode: Int? = null, address: String? = null, buildingName: String? = null, phoneNumber: String? = null, faxNumber: String? = null, deletionDate: Int? = null, representKentakuMngBranchCode: Short, usageStartDate: Int? = null, satelliteCategory: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updateResponsibleCode = updateResponsibleCode
        this.daikenSalesOfficeCode = daikenSalesOfficeCode
        this.postalCode = postalCode
        this.address = address
        this.buildingName = buildingName
        this.phoneNumber = phoneNumber
        this.faxNumber = faxNumber
        this.deletionDate = deletionDate
        this.representKentakuMngBranchCode = representKentakuMngBranchCode
        this.usageStartDate = usageStartDate
        this.satelliteCategory = satelliteCategory
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised DaikenOfficeMasterRecord
     */
    constructor(value: DaikenOfficeMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updateResponsibleCode = value.updateResponsibleCode
            this.daikenSalesOfficeCode = value.daikenSalesOfficeCode
            this.postalCode = value.postalCode
            this.address = value.address
            this.buildingName = value.buildingName
            this.phoneNumber = value.phoneNumber
            this.faxNumber = value.faxNumber
            this.deletionDate = value.deletionDate
            this.representKentakuMngBranchCode = value.representKentakuMngBranchCode
            this.usageStartDate = value.usageStartDate
            this.satelliteCategory = value.satelliteCategory
            resetChangedOnNotNull()
        }
    }
}
