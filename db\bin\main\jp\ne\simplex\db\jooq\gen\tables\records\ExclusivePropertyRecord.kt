/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ExclusivePropertyTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ExclusivePropertyPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 先行公開 既存システム物理名: -
 */
@Suppress("UNCHECKED_CAST")
open class ExclusivePropertyRecord private constructor() : UpdatableRecordImpl<ExclusivePropertyRecord>(ExclusivePropertyTable.EXCLUSIVE_PROPERTY) {

    open var id: Long
        set(value): Unit = set(0, value)
        get(): Long = get(0) as Long

    open var buildingCode: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var roomCode: String
        set(value): Unit = set(2, value)
        get(): String = get(2) as String

    open var salesOfficeCode: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var exclusiveFrom: Int
        set(value): Unit = set(4, value)
        get(): Int = get(4) as Int

    open var exclusiveTo: Int
        set(value): Unit = set(5, value)
        get(): Int = get(5) as Int

    open var companyType: Byte
        set(value): Unit = set(6, value)
        get(): Byte = get(6) as Byte

    open var earlyClosureFlag: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var creationDate: Int
        set(value): Unit = set(8, value)
        get(): Int = get(8) as Int

    open var creationTime: Int
        set(value): Unit = set(9, value)
        get(): Int = get(9) as Int

    open var creator: String
        set(value): Unit = set(10, value)
        get(): String = get(10) as String

    open var updateDate: Int
        set(value): Unit = set(11, value)
        get(): Int = get(11) as Int

    open var updateTime: Int
        set(value): Unit = set(12, value)
        get(): Int = get(12) as Int

    open var updater: String
        set(value): Unit = set(13, value)
        get(): String = get(13) as String

    open var deleteFlag: String
        set(value): Unit = set(14, value)
        get(): String = get(14) as String

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<Long?> = super.key() as Record1<Long?>

    /**
     * Create a detached, initialised ExclusivePropertyRecord
     */
    constructor(id: Long, buildingCode: String, roomCode: String, salesOfficeCode: String? = null, exclusiveFrom: Int, exclusiveTo: Int, companyType: Byte, earlyClosureFlag: String, creationDate: Int, creationTime: Int, creator: String, updateDate: Int, updateTime: Int, updater: String, deleteFlag: String): this() {
        this.id = id
        this.buildingCode = buildingCode
        this.roomCode = roomCode
        this.salesOfficeCode = salesOfficeCode
        this.exclusiveFrom = exclusiveFrom
        this.exclusiveTo = exclusiveTo
        this.companyType = companyType
        this.earlyClosureFlag = earlyClosureFlag
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.deleteFlag = deleteFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ExclusivePropertyRecord
     */
    constructor(value: ExclusivePropertyPojo?): this() {
        if (value != null) {
            this.id = value.id
            this.buildingCode = value.buildingCode
            this.roomCode = value.roomCode
            this.salesOfficeCode = value.salesOfficeCode
            this.exclusiveFrom = value.exclusiveFrom
            this.exclusiveTo = value.exclusiveTo
            this.companyType = value.companyType
            this.earlyClosureFlag = value.earlyClosureFlag
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            resetChangedOnNotNull()
        }
    }
}
