package jp.ne.simplex.application.controller.client.property

import jp.ne.simplex.application.controller.client.property.dto.*
import jp.ne.simplex.application.service.BuildingService
import jp.ne.simplex.application.service.PropertyMaintenanceService
import jp.ne.simplex.application.service.PropertyService
import jp.ne.simplex.application.service.TemporaryReservationService
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.openapi.ApiDefinition
import org.springframework.http.MediaType
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/property")
class ClientPropertyController(
    private val buildingService: BuildingService,
    private val propertyService: PropertyService,
    private val temporaryReservationService: TemporaryReservationService,
    private val propertyMaintenanceService: PropertyMaintenanceService,
) {

    @GetMapping()
    @ApiDefinition(summary = "物件情報取得API")
    fun getProperty(
        @ModelAttribute request: ClientPropertyGetRequest,
    ): ClientPropertyGetResponse {
        val result = propertyService.get(request.toServiceInterface())

        return ClientPropertyGetResponse.of(result)
    }

    @GetMapping("/temporary-reservation")
    @ApiDefinition(summary = "仮押さえ情報取得API")
    fun getTemporaryReservation(
        @ModelAttribute request: ClientTemporaryReservationGetRequest,
    ): ClientTemporaryReservationGetResponse {
        val result = temporaryReservationService.get(request.getPropertyId())

        return ClientTemporaryReservationGetResponse.of(result)

    }

    @PostMapping("/temporary-reservation/update/own-company")
    @ApiDefinition(summary = "自社仮押さえ情報更新API")
    fun registerOwnCompanyTemporaryReservation(
        @RequestBody request: ClientTemporaryReservationRegisterOwnRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        return temporaryReservationService.update(
            request.toServiceInterface(),
            authInfo.getRequestUser()
        )
    }

    @PostMapping("/temporary-reservation/update/other-company")
    @ApiDefinition(summary = "他社仮押さえ情報更新API")
    fun registerOtherCompanyTemporaryReservation(
        @RequestBody request: ClientTemporaryReservationRegisterOtherRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        return temporaryReservationService.update(
            request.toServiceInterface(),
            authInfo.getRequestUser()
        )
    }

    @PostMapping("/temporary-reservation/update/comment")
    @ApiDefinition(summary = "仮押さえコメント更新API")
    fun updateTemporaryReservationComment(
        @RequestBody request: ClientTemporaryReservationUpdateCommentRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        return temporaryReservationService.update(
            request.toServiceInterface(),
            authInfo.getRequestUser()
        )
    }

    @PostMapping("/temporary-reservation/cancel")
    @ApiDefinition(summary = "仮押さえ解除API")
    fun cancelTemporaryReservation(
        @RequestBody request: ClientTemporaryReservationCancelRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        return temporaryReservationService.update(
            request.toServiceInterface(),
            authInfo.getRequestUser()
        )
    }

    @PostMapping("/maintenance")
    @ApiDefinition(summary = "物件メンテナンスAPI")
    fun updatePropertyMaintenance(
        @AuthenticationPrincipal authInfo: AuthInfo,
        @RequestBody request: ClientPropertyMaintenanceRequest,
    ): ClientPropertyMaintenanceResponse {
        val result =
            propertyMaintenanceService.update(
                authInfo.getRequestUser(),
                request.toServiceInterface()
            )

        return ClientPropertyMaintenanceResponse.of(result)
    }

    @PostMapping("/register-garbage-image", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @ApiDefinition(summary = "ゴミ置場画像登録API")
    fun registerGarbageImage(
        @ModelAttribute request: ClientGarbageImageRegisterRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        val param = request.toServiceInterface()
        buildingService.registerGarbageImage(authInfo.getRequestUser(), param)
    }

    @DeleteMapping("/delete-garbage-image")
    @ApiDefinition(summary = "ゴミ置場画像削除API")
    fun deleteGarbageImage(
        @RequestBody request: ClientGarbageImageDeleteRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        val param = request.toServiceInterface()
        buildingService.deleteGarbageImage(authInfo.getRequestUser(), param)
    }
}
