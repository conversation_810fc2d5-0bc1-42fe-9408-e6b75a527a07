/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.PasswordMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.PasswordMasterPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * パスワードマスタ 既存システム物理名: XSPASP
 */
@Suppress("UNCHECKED_CAST")
open class PasswordMasterRecord private constructor() : UpdatableRecordImpl<PasswordMasterRecord>(PasswordMasterTable.PASSWORD_MASTER) {

    open var createDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var createTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateUser: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var applyDate: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    @Suppress("INAPPLICABLE_JVM_NAME")
    @set:JvmName("setIsDeleted")
    open var isDeleted: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var employeeId: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    open var currentPassword: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var prevPassword: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var prevPrevPassword: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var securityClass: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var undefined: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised PasswordMasterRecord
     */
    constructor(createDate: Int? = null, createTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updateUser: String? = null, applyDate: Int? = null, isDeleted: String? = null, employeeId: String, currentPassword: String? = null, prevPassword: String? = null, prevPrevPassword: String? = null, securityClass: String? = null, undefined: String? = null): this() {
        this.createDate = createDate
        this.createTime = createTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updateUser = updateUser
        this.applyDate = applyDate
        this.isDeleted = isDeleted
        this.employeeId = employeeId
        this.currentPassword = currentPassword
        this.prevPassword = prevPassword
        this.prevPrevPassword = prevPrevPassword
        this.securityClass = securityClass
        this.undefined = undefined
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised PasswordMasterRecord
     */
    constructor(value: PasswordMasterPojo?): this() {
        if (value != null) {
            this.createDate = value.createDate
            this.createTime = value.createTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updateUser = value.updateUser
            this.applyDate = value.applyDate
            this.isDeleted = value.isDeleted
            this.employeeId = value.employeeId
            this.currentPassword = value.currentPassword
            this.prevPassword = value.prevPassword
            this.prevPrevPassword = value.prevPrevPassword
            this.securityClass = value.securityClass
            this.undefined = value.undefined
            resetChangedOnNotNull()
        }
    }
}
