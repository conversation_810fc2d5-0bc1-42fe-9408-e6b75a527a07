/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * ライフライン部署マスタ 既存システム物理名: YCBLBP
 */
@Suppress("UNCHECKED_CAST")
data class UtilityDepartmentMasterPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updaterId: String? = null,
    var deleteFlag: Byte? = null,
    var utilityCategory: String? = null,
    var utilityCompanyCd: String? = null,
    var utilityDepartmentCd: String? = null,
    var utilityDepartmentFullName: String? = null,
    var utilityDepartmentNameKana: String? = null,
    var phoneAreaCode: String? = null,
    var phoneLocalCode: String? = null,
    var phoneSubscriberNumber: String? = null,
    var detailCategory: String? = null,
    var comment: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: UtilityDepartmentMasterPojo = other as UtilityDepartmentMasterPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updaterId == null) {
            if (o.updaterId != null)
                return false
        }
        else if (this.updaterId != o.updaterId)
            return false
        if (this.deleteFlag == null) {
            if (o.deleteFlag != null)
                return false
        }
        else if (this.deleteFlag != o.deleteFlag)
            return false
        if (this.utilityCategory == null) {
            if (o.utilityCategory != null)
                return false
        }
        else if (this.utilityCategory != o.utilityCategory)
            return false
        if (this.utilityCompanyCd == null) {
            if (o.utilityCompanyCd != null)
                return false
        }
        else if (this.utilityCompanyCd != o.utilityCompanyCd)
            return false
        if (this.utilityDepartmentCd == null) {
            if (o.utilityDepartmentCd != null)
                return false
        }
        else if (this.utilityDepartmentCd != o.utilityDepartmentCd)
            return false
        if (this.utilityDepartmentFullName == null) {
            if (o.utilityDepartmentFullName != null)
                return false
        }
        else if (this.utilityDepartmentFullName != o.utilityDepartmentFullName)
            return false
        if (this.utilityDepartmentNameKana == null) {
            if (o.utilityDepartmentNameKana != null)
                return false
        }
        else if (this.utilityDepartmentNameKana != o.utilityDepartmentNameKana)
            return false
        if (this.phoneAreaCode == null) {
            if (o.phoneAreaCode != null)
                return false
        }
        else if (this.phoneAreaCode != o.phoneAreaCode)
            return false
        if (this.phoneLocalCode == null) {
            if (o.phoneLocalCode != null)
                return false
        }
        else if (this.phoneLocalCode != o.phoneLocalCode)
            return false
        if (this.phoneSubscriberNumber == null) {
            if (o.phoneSubscriberNumber != null)
                return false
        }
        else if (this.phoneSubscriberNumber != o.phoneSubscriberNumber)
            return false
        if (this.detailCategory == null) {
            if (o.detailCategory != null)
                return false
        }
        else if (this.detailCategory != o.detailCategory)
            return false
        if (this.comment == null) {
            if (o.comment != null)
                return false
        }
        else if (this.comment != o.comment)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updaterId == null) 0 else this.updaterId.hashCode())
        result = prime * result + (if (this.deleteFlag == null) 0 else this.deleteFlag.hashCode())
        result = prime * result + (if (this.utilityCategory == null) 0 else this.utilityCategory.hashCode())
        result = prime * result + (if (this.utilityCompanyCd == null) 0 else this.utilityCompanyCd.hashCode())
        result = prime * result + (if (this.utilityDepartmentCd == null) 0 else this.utilityDepartmentCd.hashCode())
        result = prime * result + (if (this.utilityDepartmentFullName == null) 0 else this.utilityDepartmentFullName.hashCode())
        result = prime * result + (if (this.utilityDepartmentNameKana == null) 0 else this.utilityDepartmentNameKana.hashCode())
        result = prime * result + (if (this.phoneAreaCode == null) 0 else this.phoneAreaCode.hashCode())
        result = prime * result + (if (this.phoneLocalCode == null) 0 else this.phoneLocalCode.hashCode())
        result = prime * result + (if (this.phoneSubscriberNumber == null) 0 else this.phoneSubscriberNumber.hashCode())
        result = prime * result + (if (this.detailCategory == null) 0 else this.detailCategory.hashCode())
        result = prime * result + (if (this.comment == null) 0 else this.comment.hashCode())
        return result
    }
}
