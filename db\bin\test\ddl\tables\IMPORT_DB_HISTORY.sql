-- TABLE: IMPORT_DB_HISTORY(DB取込実行履歴)

CREATE TABLE IMPORT_DB_HISTORY(
     FILE_KEY                                     varchar(255)      NOT NULL    
,    IMPORT_TIMESTAMP                             timestamp         NOT NULL    
,    DELETE_FLAG                                  boolean           NOT NULL    
,    CONSTRAINT PK_IMPORT_DB_HISTORY PRIMARY KEY (FILE_KEY, DELETE_FLAG)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE IMPORT_DB_HISTORY IS 'DB取込実行履歴 既存システム物理名: -';
COMMENT ON COLUMN IMPORT_DB_HISTORY.FILE_KEY IS 'ファイルキー 既存システム物理名: -';
COMMENT ON COLUMN IMPORT_DB_HISTORY.IMPORT_TIMESTAMP IS '取込日時 既存システム物理名: -';
COMMENT ON COLUMN IMPORT_DB_HISTORY.DELETE_FLAG IS '削除フラグ 既存システム物理名: -';
