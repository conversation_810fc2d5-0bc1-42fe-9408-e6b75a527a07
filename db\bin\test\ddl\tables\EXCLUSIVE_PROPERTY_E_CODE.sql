-- TABLE: EXCLUSIVE_PROPERTY_E_CODE(先行公開Eコードテーブル)

CREATE TABLE EXCLUSIVE_PROPERTY_E_CODE(
     ID                                           numeric(18,0)     NOT NULL    
,    E_CODE                                       varchar(9)        NOT NULL    
,    CREATION_DATE                                numeric(8,0)      NOT NULL    
,    CREATION_TIME                                numeric(6,0)      NOT NULL    
,    CREATOR                                      varchar(6)        NOT NULL    
,    UPDATE_DATE                                  numeric(8,0)      NOT NULL    
,    UPDATE_TIME                                  numeric(6,0)      NOT NULL    
,    UPDATER                                      varchar(6)        NOT NULL    
,    DELETE_FLAG                                  varchar(1)        NOT NULL    
,    CONSTRAINT PK_EXCLUSIVE_PROPERTY_E_CODE PRIMARY KEY (ID)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE EXCLUSIVE_PROPERTY_E_CODE IS '先行公開Eコードテーブル 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY_E_CODE.ID IS '先行公開ID 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY_E_CODE.E_CODE IS 'Eコード 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY_E_CODE.CREATION_DATE IS '作成年月日 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY_E_CODE.CREATION_TIME IS '作成時刻 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY_E_CODE.CREATOR IS '作成者 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY_E_CODE.UPDATE_DATE IS '更新年月日 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY_E_CODE.UPDATE_TIME IS '更新時刻 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY_E_CODE.UPDATER IS '更新者 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY_E_CODE.DELETE_FLAG IS '削除フラグ 既存システム物理名: -';
