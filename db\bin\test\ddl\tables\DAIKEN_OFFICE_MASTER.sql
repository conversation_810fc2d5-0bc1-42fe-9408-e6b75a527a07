-- TABLE: DAIKEN_OFFICE_MASTER(大建営業所マスタ)

CREATE TABLE DAIKEN_OFFICE_MASTER(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATE_RESPONSIBLE_CODE                      varchar(10)                   
,    DAIKEN_SALES_OFFICE_CODE                     numeric(3)        NOT NULL    
,    POSTAL_CODE                                  numeric(7)                    
,    ADDRESS                                      varchar(62)                   
,    BUILDING_NAME                                varchar(32)                   
,    PHONE_NUMBER                                 varchar(13)                   
,    FAX_NUMBER                                   varchar(13)                   
,    DELETION_DATE                                numeric(8)                    
,    REPRESENT_KENTAKU_MNG_BRANCH_CODE            numeric(3)        NOT NULL    
,    USAGE_START_DATE                             numeric(8)                    
,    SATELLITE_CATEGORY                           varchar(1)                    
,    CONS<PERSON>AINT PK_DAIKEN_OFFICE_MASTER PRIMARY KEY (DAIKEN_SALES_OFFICE_CODE, REPRESENT_KENTAKU_MNG_BRANCH_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE DAIKEN_OFFICE_MASTER IS '大建営業所マスタ 既存システム物理名: HKA10P';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: SAKUSEI_DT @290';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: SAKUSEI_TM @290';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: KOSHIN_DT';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: KOSHIN_TM';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: KOSHIN_PGM_ID';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.UPDATE_RESPONSIBLE_CODE IS '更新担当者コード 既存システム物理名: KOSHINSHA';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.DAIKEN_SALES_OFFICE_CODE IS '大建営業所コード 既存システム物理名: EIGYOSHO_CD';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.POSTAL_CODE IS '郵便番号 既存システム物理名: YUBIN_BANGO';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.ADDRESS IS '住所 既存システム物理名: JUSYO';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.BUILDING_NAME IS 'ビル名 既存システム物理名: BUILDING_NAME';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.PHONE_NUMBER IS '電話番号 既存システム物理名: TEL_NO';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.FAX_NUMBER IS 'FAX番号 既存システム物理名: FAX_NO';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.DELETION_DATE IS '削除日 既存システム物理名: SAKUJO_DT';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.REPRESENT_KENTAKU_MNG_BRANCH_CODE IS '代表 建託管理支店コード 既存システム物理名: DAIHYO_SHITEN';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.USAGE_START_DATE IS '使用開始日 既存システム物理名: SIYO_KAISHI_DT';
COMMENT ON COLUMN DAIKEN_OFFICE_MASTER.SATELLITE_CATEGORY IS 'サテライト区分 既存システム物理名: SATELLITE_FLG';
