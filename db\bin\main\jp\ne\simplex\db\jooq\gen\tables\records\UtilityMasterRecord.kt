/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.UtilityMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.UtilityMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * ライフラインマスタ 既存システム物理名: HATK0P
 */
@Suppress("UNCHECKED_CAST")
open class UtilityMasterRecord private constructor() : TableRecordImpl<UtilityMasterRecord>(UtilityMasterTable.UTILITY_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updatePgm: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateResponsibleCd: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var utilityType: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var utilityCompanyCd: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var utilityBranchCd: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var utilityCompanyKana: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var utilityCompanyKanji: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var utilityBranchKana: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var utilityBranchKanji: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var phoneNumber: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var deleteDate: Int?
        set(value): Unit = set(14, value)
        get(): Int? = get(14) as Int?

    /**
     * Create a detached, initialised UtilityMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updatePgm: String? = null, updateResponsibleCd: String? = null, utilityType: String? = null, utilityCompanyCd: String? = null, utilityBranchCd: String? = null, utilityCompanyKana: String? = null, utilityCompanyKanji: String? = null, utilityBranchKana: String? = null, utilityBranchKanji: String? = null, phoneNumber: String? = null, deleteDate: Int? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updatePgm = updatePgm
        this.updateResponsibleCd = updateResponsibleCd
        this.utilityType = utilityType
        this.utilityCompanyCd = utilityCompanyCd
        this.utilityBranchCd = utilityBranchCd
        this.utilityCompanyKana = utilityCompanyKana
        this.utilityCompanyKanji = utilityCompanyKanji
        this.utilityBranchKana = utilityBranchKana
        this.utilityBranchKanji = utilityBranchKanji
        this.phoneNumber = phoneNumber
        this.deleteDate = deleteDate
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised UtilityMasterRecord
     */
    constructor(value: UtilityMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updatePgm = value.updatePgm
            this.updateResponsibleCd = value.updateResponsibleCd
            this.utilityType = value.utilityType
            this.utilityCompanyCd = value.utilityCompanyCd
            this.utilityBranchCd = value.utilityBranchCd
            this.utilityCompanyKana = value.utilityCompanyKana
            this.utilityCompanyKanji = value.utilityCompanyKanji
            this.utilityBranchKana = value.utilityBranchKana
            this.utilityBranchKanji = value.utilityBranchKanji
            this.phoneNumber = value.phoneNumber
            this.deleteDate = value.deleteDate
            resetChangedOnNotNull()
        }
    }
}
