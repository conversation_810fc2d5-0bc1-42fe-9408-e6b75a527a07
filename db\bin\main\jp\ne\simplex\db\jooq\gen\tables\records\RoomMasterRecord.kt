/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.RoomMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.RoomMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 部屋マスタ 既存システム物理名: ECNE0P
 */
@Suppress("UNCHECKED_CAST")
open class RoomMasterRecord private constructor() : TableRecordImpl<RoomMasterRecord>(RoomMasterTable.ROOM_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var buildingCode: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var roomCode: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var roomNumber: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var roomParentChildCode: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var ownerCode_10: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var bulkLeaseSign: Byte?
        set(value): Unit = set(12, value)
        get(): Byte? = get(12) as Byte?

    open var setPropertySign: Byte?
        set(value): Unit = set(13, value)
        get(): Byte? = get(13) as Byte?

    open var declarationRequestCollectionSign: Byte?
        set(value): Unit = set(14, value)
        get(): Byte? = get(14) as Byte?

    open var layoutCategory: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var layoutDetails: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var floorNumber: Short?
        set(value): Unit = set(17, value)
        get(): Short? = get(17) as Short?

    open var occupancyMediationSign: Byte?
        set(value): Unit = set(18, value)
        get(): Byte? = get(18) as Byte?

    open var occupantCategory: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var mutualAidAssociationSign: Byte?
        set(value): Unit = set(20, value)
        get(): Byte? = get(20) as Byte?

    open var mutualAidAssociationReferralCount: Byte?
        set(value): Unit = set(21, value)
        get(): Byte? = get(21) as Byte?

    open var mutualAidAssocCashReceiptAmount: Int?
        set(value): Unit = set(22, value)
        get(): Int? = get(22) as Int?

    open var managementSign: Byte?
        set(value): Unit = set(23, value)
        get(): Byte? = get(23) as Byte?

    open var managementUnitCategory: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var referralCount: Byte?
        set(value): Unit = set(25, value)
        get(): Byte? = get(25) as Byte?

    open var managementContractCashReceiptAmount: Int?
        set(value): Unit = set(26, value)
        get(): Int? = get(26) as Int?

    open var initialAssessmentReviewNumber: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var officeFloorAreaSquareMeters: BigDecimal?
        set(value): Unit = set(28, value)
        get(): BigDecimal? = get(28) as BigDecimal?

    open var residentialAreaSquareMeters: BigDecimal?
        set(value): Unit = set(29, value)
        get(): BigDecimal? = get(29) as BigDecimal?

    open var parkingAvailabilitySign: Byte?
        set(value): Unit = set(30, value)
        get(): Byte? = get(30) as Byte?

    open var initialOccupancyDate: Int?
        set(value): Unit = set(31, value)
        get(): Int? = get(31) as Int?

    open var occupancyStatusSign: Byte?
        set(value): Unit = set(32, value)
        get(): Byte? = get(32) as Byte?

    open var expectedAvailableDate: Int?
        set(value): Unit = set(33, value)
        get(): Int? = get(33) as Int?

    open var storeExclusiveAreaSquareMeters: BigDecimal?
        set(value): Unit = set(34, value)
        get(): BigDecimal? = get(34) as BigDecimal?

    open var balconyArea: BigDecimal?
        set(value): Unit = set(35, value)
        get(): BigDecimal? = get(35) as BigDecimal?

    open var roomPositionCategory: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var orientationCategory: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var roomType: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var studioRoomSign: Byte?
        set(value): Unit = set(39, value)
        get(): Byte? = get(39) as Byte?

    open var serviceRoomSign: Byte?
        set(value): Unit = set(40, value)
        get(): Byte? = get(40) as Byte?

    open var privateGardenAvailabilitySign: Byte?
        set(value): Unit = set(41, value)
        get(): Byte? = get(41) as Byte?

    open var underfloorStorageAvailabilitySign: Byte?
        set(value): Unit = set(42, value)
        get(): Byte? = get(42) as Byte?

    open var roomEquipmentCode: String?
        set(value): Unit = set(43, value)
        get(): String? = get(43) as String?

    open var roomFeatureCode: String?
        set(value): Unit = set(44, value)
        get(): String? = get(44) as String?

    open var newOrUsedCategory: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var currentlyAvailableSign: Byte?
        set(value): Unit = set(46, value)
        get(): Byte? = get(46) as Byte?

    open var remainingCollectionDate: Int?
        set(value): Unit = set(47, value)
        get(): Int? = get(47) as Int?

    open var frontFreeRentSign: Byte?
        set(value): Unit = set(48, value)
        get(): Byte? = get(48) as Byte?

    open var frontFreeRentMonths: Short?
        set(value): Unit = set(49, value)
        get(): Short? = get(49) as Short?

    open var frontFreeRentAmount: Int?
        set(value): Unit = set(50, value)
        get(): Int? = get(50) as Int?

    open var additionalAdvertisingMonths: BigDecimal?
        set(value): Unit = set(51, value)
        get(): BigDecimal? = get(51) as BigDecimal?

    open var rentalAssessmentStatusCategory: String?
        set(value): Unit = set(52, value)
        get(): String? = get(52) as String?

    open var priorityInformationCode: String?
        set(value): Unit = set(53, value)
        get(): String? = get(53) as String?

    open var assessmentReviewNumber: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var firstFloorAreaSquareMeters: BigDecimal?
        set(value): Unit = set(55, value)
        get(): BigDecimal? = get(55) as BigDecimal?

    open var secondFloorAreaSquareMeters: BigDecimal?
        set(value): Unit = set(56, value)
        get(): BigDecimal? = get(56) as BigDecimal?

    open var thirdFloorAreaSquareMeters: BigDecimal?
        set(value): Unit = set(57, value)
        get(): BigDecimal? = get(57) as BigDecimal?

    open var interfaceSign: Byte?
        set(value): Unit = set(58, value)
        get(): Byte? = get(58) as Byte?

    open var referralExtractionRequiredSign: Byte?
        set(value): Unit = set(59, value)
        get(): Byte? = get(59) as Byte?

    open var dataMigrationKey_1: String?
        set(value): Unit = set(60, value)
        get(): String? = get(60) as String?

    open var dataMigrationKey_2: String?
        set(value): Unit = set(61, value)
        get(): String? = get(61) as String?

    open var roomChangedSign: Byte?
        set(value): Unit = set(62, value)
        get(): Byte? = get(62) as Byte?

    open var rocky: String?
        set(value): Unit = set(63, value)
        get(): String? = get(63) as String?

    open var categoryA: String?
        set(value): Unit = set(64, value)
        get(): String? = get(64) as String?

    open var categoryB: String?
        set(value): Unit = set(65, value)
        get(): String? = get(65) as String?

    open var equipmentCode: String?
        set(value): Unit = set(66, value)
        get(): String? = get(66) as String?

    open var newGuaranteeRate: BigDecimal?
        set(value): Unit = set(67, value)
        get(): BigDecimal? = get(67) as BigDecimal?

    open var newManagementGuaranteeRate: BigDecimal?
        set(value): Unit = set(68, value)
        get(): BigDecimal? = get(68) as BigDecimal?

    open var contractMutualAidFeeRate: BigDecimal?
        set(value): Unit = set(69, value)
        get(): BigDecimal? = get(69) as BigDecimal?

    open var revivalTargetRoom: Byte?
        set(value): Unit = set(70, value)
        get(): Byte? = get(70) as Byte?

    open var specialPreferredRentCategory: String?
        set(value): Unit = set(71, value)
        get(): String? = get(71) as String?

    open var nonStandardCategory: Byte?
        set(value): Unit = set(72, value)
        get(): Byte? = get(72) as Byte?

    /**
     * Create a detached, initialised RoomMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteSign: Byte? = null, buildingCode: String? = null, roomCode: String? = null, roomNumber: String? = null, roomParentChildCode: String? = null, ownerCode_10: String? = null, bulkLeaseSign: Byte? = null, setPropertySign: Byte? = null, declarationRequestCollectionSign: Byte? = null, layoutCategory: String? = null, layoutDetails: String? = null, floorNumber: Short? = null, occupancyMediationSign: Byte? = null, occupantCategory: String? = null, mutualAidAssociationSign: Byte? = null, mutualAidAssociationReferralCount: Byte? = null, mutualAidAssocCashReceiptAmount: Int? = null, managementSign: Byte? = null, managementUnitCategory: String? = null, referralCount: Byte? = null, managementContractCashReceiptAmount: Int? = null, initialAssessmentReviewNumber: String? = null, officeFloorAreaSquareMeters: BigDecimal? = null, residentialAreaSquareMeters: BigDecimal? = null, parkingAvailabilitySign: Byte? = null, initialOccupancyDate: Int? = null, occupancyStatusSign: Byte? = null, expectedAvailableDate: Int? = null, storeExclusiveAreaSquareMeters: BigDecimal? = null, balconyArea: BigDecimal? = null, roomPositionCategory: String? = null, orientationCategory: String? = null, roomType: String? = null, studioRoomSign: Byte? = null, serviceRoomSign: Byte? = null, privateGardenAvailabilitySign: Byte? = null, underfloorStorageAvailabilitySign: Byte? = null, roomEquipmentCode: String? = null, roomFeatureCode: String? = null, newOrUsedCategory: String? = null, currentlyAvailableSign: Byte? = null, remainingCollectionDate: Int? = null, frontFreeRentSign: Byte? = null, frontFreeRentMonths: Short? = null, frontFreeRentAmount: Int? = null, additionalAdvertisingMonths: BigDecimal? = null, rentalAssessmentStatusCategory: String? = null, priorityInformationCode: String? = null, assessmentReviewNumber: String? = null, firstFloorAreaSquareMeters: BigDecimal? = null, secondFloorAreaSquareMeters: BigDecimal? = null, thirdFloorAreaSquareMeters: BigDecimal? = null, interfaceSign: Byte? = null, referralExtractionRequiredSign: Byte? = null, dataMigrationKey_1: String? = null, dataMigrationKey_2: String? = null, roomChangedSign: Byte? = null, rocky: String? = null, categoryA: String? = null, categoryB: String? = null, equipmentCode: String? = null, newGuaranteeRate: BigDecimal? = null, newManagementGuaranteeRate: BigDecimal? = null, contractMutualAidFeeRate: BigDecimal? = null, revivalTargetRoom: Byte? = null, specialPreferredRentCategory: String? = null, nonStandardCategory: Byte? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteSign = logicalDeleteSign
        this.buildingCode = buildingCode
        this.roomCode = roomCode
        this.roomNumber = roomNumber
        this.roomParentChildCode = roomParentChildCode
        this.ownerCode_10 = ownerCode_10
        this.bulkLeaseSign = bulkLeaseSign
        this.setPropertySign = setPropertySign
        this.declarationRequestCollectionSign = declarationRequestCollectionSign
        this.layoutCategory = layoutCategory
        this.layoutDetails = layoutDetails
        this.floorNumber = floorNumber
        this.occupancyMediationSign = occupancyMediationSign
        this.occupantCategory = occupantCategory
        this.mutualAidAssociationSign = mutualAidAssociationSign
        this.mutualAidAssociationReferralCount = mutualAidAssociationReferralCount
        this.mutualAidAssocCashReceiptAmount = mutualAidAssocCashReceiptAmount
        this.managementSign = managementSign
        this.managementUnitCategory = managementUnitCategory
        this.referralCount = referralCount
        this.managementContractCashReceiptAmount = managementContractCashReceiptAmount
        this.initialAssessmentReviewNumber = initialAssessmentReviewNumber
        this.officeFloorAreaSquareMeters = officeFloorAreaSquareMeters
        this.residentialAreaSquareMeters = residentialAreaSquareMeters
        this.parkingAvailabilitySign = parkingAvailabilitySign
        this.initialOccupancyDate = initialOccupancyDate
        this.occupancyStatusSign = occupancyStatusSign
        this.expectedAvailableDate = expectedAvailableDate
        this.storeExclusiveAreaSquareMeters = storeExclusiveAreaSquareMeters
        this.balconyArea = balconyArea
        this.roomPositionCategory = roomPositionCategory
        this.orientationCategory = orientationCategory
        this.roomType = roomType
        this.studioRoomSign = studioRoomSign
        this.serviceRoomSign = serviceRoomSign
        this.privateGardenAvailabilitySign = privateGardenAvailabilitySign
        this.underfloorStorageAvailabilitySign = underfloorStorageAvailabilitySign
        this.roomEquipmentCode = roomEquipmentCode
        this.roomFeatureCode = roomFeatureCode
        this.newOrUsedCategory = newOrUsedCategory
        this.currentlyAvailableSign = currentlyAvailableSign
        this.remainingCollectionDate = remainingCollectionDate
        this.frontFreeRentSign = frontFreeRentSign
        this.frontFreeRentMonths = frontFreeRentMonths
        this.frontFreeRentAmount = frontFreeRentAmount
        this.additionalAdvertisingMonths = additionalAdvertisingMonths
        this.rentalAssessmentStatusCategory = rentalAssessmentStatusCategory
        this.priorityInformationCode = priorityInformationCode
        this.assessmentReviewNumber = assessmentReviewNumber
        this.firstFloorAreaSquareMeters = firstFloorAreaSquareMeters
        this.secondFloorAreaSquareMeters = secondFloorAreaSquareMeters
        this.thirdFloorAreaSquareMeters = thirdFloorAreaSquareMeters
        this.interfaceSign = interfaceSign
        this.referralExtractionRequiredSign = referralExtractionRequiredSign
        this.dataMigrationKey_1 = dataMigrationKey_1
        this.dataMigrationKey_2 = dataMigrationKey_2
        this.roomChangedSign = roomChangedSign
        this.rocky = rocky
        this.categoryA = categoryA
        this.categoryB = categoryB
        this.equipmentCode = equipmentCode
        this.newGuaranteeRate = newGuaranteeRate
        this.newManagementGuaranteeRate = newManagementGuaranteeRate
        this.contractMutualAidFeeRate = contractMutualAidFeeRate
        this.revivalTargetRoom = revivalTargetRoom
        this.specialPreferredRentCategory = specialPreferredRentCategory
        this.nonStandardCategory = nonStandardCategory
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised RoomMasterRecord
     */
    constructor(value: RoomMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteSign = value.logicalDeleteSign
            this.buildingCode = value.buildingCode
            this.roomCode = value.roomCode
            this.roomNumber = value.roomNumber
            this.roomParentChildCode = value.roomParentChildCode
            this.ownerCode_10 = value.ownerCode_10
            this.bulkLeaseSign = value.bulkLeaseSign
            this.setPropertySign = value.setPropertySign
            this.declarationRequestCollectionSign = value.declarationRequestCollectionSign
            this.layoutCategory = value.layoutCategory
            this.layoutDetails = value.layoutDetails
            this.floorNumber = value.floorNumber
            this.occupancyMediationSign = value.occupancyMediationSign
            this.occupantCategory = value.occupantCategory
            this.mutualAidAssociationSign = value.mutualAidAssociationSign
            this.mutualAidAssociationReferralCount = value.mutualAidAssociationReferralCount
            this.mutualAidAssocCashReceiptAmount = value.mutualAidAssocCashReceiptAmount
            this.managementSign = value.managementSign
            this.managementUnitCategory = value.managementUnitCategory
            this.referralCount = value.referralCount
            this.managementContractCashReceiptAmount = value.managementContractCashReceiptAmount
            this.initialAssessmentReviewNumber = value.initialAssessmentReviewNumber
            this.officeFloorAreaSquareMeters = value.officeFloorAreaSquareMeters
            this.residentialAreaSquareMeters = value.residentialAreaSquareMeters
            this.parkingAvailabilitySign = value.parkingAvailabilitySign
            this.initialOccupancyDate = value.initialOccupancyDate
            this.occupancyStatusSign = value.occupancyStatusSign
            this.expectedAvailableDate = value.expectedAvailableDate
            this.storeExclusiveAreaSquareMeters = value.storeExclusiveAreaSquareMeters
            this.balconyArea = value.balconyArea
            this.roomPositionCategory = value.roomPositionCategory
            this.orientationCategory = value.orientationCategory
            this.roomType = value.roomType
            this.studioRoomSign = value.studioRoomSign
            this.serviceRoomSign = value.serviceRoomSign
            this.privateGardenAvailabilitySign = value.privateGardenAvailabilitySign
            this.underfloorStorageAvailabilitySign = value.underfloorStorageAvailabilitySign
            this.roomEquipmentCode = value.roomEquipmentCode
            this.roomFeatureCode = value.roomFeatureCode
            this.newOrUsedCategory = value.newOrUsedCategory
            this.currentlyAvailableSign = value.currentlyAvailableSign
            this.remainingCollectionDate = value.remainingCollectionDate
            this.frontFreeRentSign = value.frontFreeRentSign
            this.frontFreeRentMonths = value.frontFreeRentMonths
            this.frontFreeRentAmount = value.frontFreeRentAmount
            this.additionalAdvertisingMonths = value.additionalAdvertisingMonths
            this.rentalAssessmentStatusCategory = value.rentalAssessmentStatusCategory
            this.priorityInformationCode = value.priorityInformationCode
            this.assessmentReviewNumber = value.assessmentReviewNumber
            this.firstFloorAreaSquareMeters = value.firstFloorAreaSquareMeters
            this.secondFloorAreaSquareMeters = value.secondFloorAreaSquareMeters
            this.thirdFloorAreaSquareMeters = value.thirdFloorAreaSquareMeters
            this.interfaceSign = value.interfaceSign
            this.referralExtractionRequiredSign = value.referralExtractionRequiredSign
            this.dataMigrationKey_1 = value.dataMigrationKey_1
            this.dataMigrationKey_2 = value.dataMigrationKey_2
            this.roomChangedSign = value.roomChangedSign
            this.rocky = value.rocky
            this.categoryA = value.categoryA
            this.categoryB = value.categoryB
            this.equipmentCode = value.equipmentCode
            this.newGuaranteeRate = value.newGuaranteeRate
            this.newManagementGuaranteeRate = value.newManagementGuaranteeRate
            this.contractMutualAidFeeRate = value.contractMutualAidFeeRate
            this.revivalTargetRoom = value.revivalTargetRoom
            this.specialPreferredRentCategory = value.specialPreferredRentCategory
            this.nonStandardCategory = value.nonStandardCategory
            resetChangedOnNotNull()
        }
    }
}
