package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.repository.db.pojos.RoomInfoMasterPojo
import jp.ne.simplex.db.jooq.gen.tables.references.ROOM_INFO_MASTER
import jp.ne.simplex.db.jooq.gen.tables.references.TEMPORARY_RESERVATION_FILE
import jp.ne.simplex.db.jooq.gen.tables.references.VACANT_HOUSE_HP
import org.jooq.DSLContext
import org.jooq.impl.DSL.row
import org.springframework.stereotype.Repository

@Repository
class PropertyRepository(private val context: DSLContext) : PropertyRepositoryInterface {
    override fun findBy(propertyId: Property.Id): Property? {
        return list(listOf(propertyId)).firstOrNull()
    }

    override fun list(propertyIds: List<Property.Id>): List<Property> {
        val query = context.select(
            ROOM_INFO_MASTER.RECORD_TYPE,
            ROOM_INFO_MASTER.PROPERTY_CD_TYPE,
            ROOM_INFO_MASTER.PROPERTY_BUILDING_CD,
            ROOM_INFO_MASTER.PROPERTY_ROOM_CD,
            ROOM_INFO_MASTER.PROPERTY_TYPE,
            ROOM_INFO_MASTER.RECORD_STATUS_TYPE,
            VACANT_HOUSE_HP.CUSTOMER_COMPLETION_FLAG,
            ROOM_INFO_MASTER.ROOM_NUMBER,
            ROOM_INFO_MASTER.MOVE_IN_APPLICATION_DATE,
            ROOM_INFO_MASTER.DIRECTION,
            VACANT_HOUSE_HP.CHANGE_DIVISION,
            TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_DATE,
            ROOM_INFO_MASTER.MARKETING_BRANCH_OFFICE_CD
        ).from(ROOM_INFO_MASTER)
            .leftJoin(VACANT_HOUSE_HP)
            .on(ROOM_INFO_MASTER.PROPERTY_BUILDING_CD.eq(VACANT_HOUSE_HP.PROPERTY_BUILDING_CD))
            .and(ROOM_INFO_MASTER.PROPERTY_ROOM_CD.eq(VACANT_HOUSE_HP.PROPERTY_ROOM_CD))
            .leftJoin(TEMPORARY_RESERVATION_FILE)
            .on(ROOM_INFO_MASTER.PROPERTY_BUILDING_CD.eq(TEMPORARY_RESERVATION_FILE.BUILDING_CD))
            .and(ROOM_INFO_MASTER.PROPERTY_ROOM_CD.eq(TEMPORARY_RESERVATION_FILE.ROOM_CD))
            .where(
                row(
                    ROOM_INFO_MASTER.PROPERTY_BUILDING_CD,
                    ROOM_INFO_MASTER.PROPERTY_ROOM_CD
                ).`in`(propertyIds.map { row(it.buildingCode.value, it.roomCode.value) })
            )



        return query.fetchInto(RoomInfoMasterPojo::class.java)
            .mapNotNull { it.getProperty() }
    }
}

interface PropertyRepositoryInterface {
    /** 引数で指定されたIDに合致する物件情報を取得する **/
    fun findBy(propertyId: Property.Id): Property?

    /** 引数で指定されたIDに合致する物件情報を取得する **/
    fun list(propertyIds: List<Property.Id>): List<Property>

}
