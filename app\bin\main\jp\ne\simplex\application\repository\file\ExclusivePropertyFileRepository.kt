package jp.ne.simplex.application.repository.file

import jp.ne.simplex.application.model.ExclusivePropertyInfo
import jp.ne.simplex.shared.BooleanExtension.Companion.toInt
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.PathExtension.Companion.createDirectoryIfNotExist
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import java.io.BufferedWriter
import java.io.File
import java.nio.charset.Charset
import java.nio.file.Files
import java.nio.file.Path
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Repository
class ExclusivePropertyFileRepository : ExclusivePropertyFileRepositoryInterface {
    companion object {
        private val log = LoggerFactory.getLogger(ExclusivePropertyFileRepository::class.java)
        private const val EXCLUSIVE_PROPERTY_FOR_E_CLOUD_FILE_NAME_BASE =
            "exclusive-property-for-ecloud-"
        private val EXCLUSIVE_PROPERTY_FILE_DATETIME_PATTERN =
            DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
        private const val EXCLUSIVE_PROPERTY_FOR_FILE_EXTENSION = ".csv"
        private val EXCLUSIVE_PROPERTY_FILE_CHARSET = Charset.forName("cp932")
        private const val CSV_DELIMITER = ","

        private fun ExclusivePropertyInfo.toCsvRow(): List<String> {
            val id = this.id.value.toString()
            val propertyCode =
                "1-${this.propertyId.buildingCode.value}-${this.propertyId.roomCode.value}"
            val propertyName = "${this.buildingName} ${this.roomNumber?.getValue()}"
            val exclusiveFrom = this.exclusiveRange.from.yyyyMMdd()
            val exclusiveTo = this.exclusiveRange.to.yyyyMMdd()
            val leasingFlag = true.toInt().toString()
            val realEstateFlag = false.toInt().toString()
            // E-Cloud向けの直営店のみのためEコードは常に空
            val eCode = ""

            return listOf(
                id,
                propertyCode,
                propertyName,
                exclusiveFrom,
                exclusiveTo,
                leasingFlag,
                realEstateFlag,
                eCode,
            )
        }
    }

    override fun saveSentFile(): Path {
        val fileName = "sent"

        log.info("output path=${File("./work/$fileName").absolutePath}")
        val outputPath = Path.of("./work", fileName)
        outputPath.createDirectoryIfNotExist()

        Files.newBufferedWriter(outputPath, EXCLUSIVE_PROPERTY_FILE_CHARSET).use { writer ->
        }

        return outputPath.toAbsolutePath()
    }

    override fun saveExclusivePropertyForECloud(
        exclusivePropertyList: List<ExclusivePropertyInfo>
    ): Path {
        val fileName = EXCLUSIVE_PROPERTY_FOR_E_CLOUD_FILE_NAME_BASE + LocalDateTime.now().format(
            EXCLUSIVE_PROPERTY_FILE_DATETIME_PATTERN
        ) + EXCLUSIVE_PROPERTY_FOR_FILE_EXTENSION

        log.info("output path=${File("./work/${fileName}").absolutePath}")
        val outputPath = Path.of("./work", fileName)
        outputPath.createDirectoryIfNotExist()

        // ファイルに書き込み
        Files.newBufferedWriter(
            outputPath,
            EXCLUSIVE_PROPERTY_FILE_CHARSET
        ).use { writer ->
            writeExclusivePropertyForECloud(writer, exclusivePropertyList)
        }
        return outputPath.toAbsolutePath()
    }

    fun writeExclusivePropertyForECloud(
        writer: BufferedWriter,
        exclusivePropertyList: List<ExclusivePropertyInfo>
    ) {
        exclusivePropertyList.forEach {
            // 各値をダブルクォートで囲む
            val quotedRow = it.toCsvRow().joinToString(CSV_DELIMITER) { value ->
                "\"${value}\""
            }
            writer.write(quotedRow)
            writer.newLine()
        }
    }

    override fun saveFromS3ForECloud(latestFile: ResponseInputStream<GetObjectResponse>): Path {
        val fileName = EXCLUSIVE_PROPERTY_FOR_E_CLOUD_FILE_NAME_BASE + LocalDateTime.now().format(
            EXCLUSIVE_PROPERTY_FILE_DATETIME_PATTERN
        ) + EXCLUSIVE_PROPERTY_FOR_FILE_EXTENSION

        return saveFromS3(latestFile, fileName)
    }

    private fun saveFromS3(
        fileContent: ResponseInputStream<GetObjectResponse>,
        fileName: String
    ): Path {
        // 出力先パスの設定
        val outputPath = Path.of("./work", fileName)
        outputPath.createDirectoryIfNotExist()

        try {
            // ストリームからファイルに直接書き込む（文字コードの変換を避ける）
            Files.copy(
                fileContent,
                outputPath,
                java.nio.file.StandardCopyOption.REPLACE_EXISTING
            )

            log.info("Successfully saved file from S3: path=$outputPath")
            return outputPath.toAbsolutePath()
        } catch (e: Exception) {
            log.error("Failed to save file from S3", e)
            throw e
        } finally {
            fileContent.close()
        }
    }
}

interface ExclusivePropertyFileRepositoryInterface {
    /** E-Cloud向け先行公開CSVファイルを保存 */
    fun saveExclusivePropertyForECloud(
        exclusivePropertyList: List<ExclusivePropertyInfo>
    ): Path

    /** E-Cloud向け送信済みフラグファイル（空ファイル "sent"）を作成 */
    fun saveSentFile(): Path

    fun saveFromS3ForECloud(latestFile: ResponseInputStream<GetObjectResponse>): Path
}
