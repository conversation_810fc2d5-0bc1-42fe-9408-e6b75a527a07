package jp.ne.simplex.mock

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.repository.db.EmployeeRepositoryInterface

class MockEmployeeRepository(
    val findByFunc: (employeeCode: Employee.Code?) -> Employee? = { _ -> null },
    val findByBranchFunc: (branch: Branch) -> List<Employee> = { _ -> emptyList() },
    val getAffiliationBranchCodeFunc: (employeeCode: Employee.Code?) -> Branch.Code? = { _ -> null }
) : EmployeeRepositoryInterface {
    override fun findBy(employeeCode: Employee.Code?): Employee? {
        return findByFunc(employeeCode)
    }

    override fun findBy(branch: Branch): List<Employee> {
        return findByBranchFunc(branch)
    }

    override fun getAffiliationBranchCode(employeeCode: Employee.Code?): Branch.Code? {
        return getAffiliationBranchCodeFunc(employeeCode)
    }

}
