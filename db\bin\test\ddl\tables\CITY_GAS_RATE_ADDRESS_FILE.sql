-- TABLE: CITY_GAS_RATE_ADDRESS_FILE(都市ガス料金対象住所ファイル)

CREATE TABLE CITY_GAS_RATE_ADDRESS_FILE(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    CREATION_PROGRAM_ID                          varchar(10)                   
,    CREATION_TERMINAL                            varchar(10)                   
,    CREATION_RESPONSIBLE                         varchar(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATE_TERMINAL                              varchar(10)                   
,    UPDATE_RESPONSIBLE                           varchar(6)                    
,    DELETE_SIGN                                  numeric(1)                    
,    ADDRESS_CD                                   varchar(10)                   
,    TARGET_START_DATE                            numeric(8)                    
,    TARGET_END_DATE                              numeric(8)                    
,    GAS_COMPANY_CATEGORY                         varchar(2)                    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE CITY_GAS_RATE_ADDRESS_FILE IS '都市ガス料金対象住所ファイル 既存システム物理名: FFF10P';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: FF101D @290';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.CREATION_TIME IS '作成時間 既存システム物理名: FF102H @290';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.CREATION_PROGRAM_ID IS '作成プログラムID 既存システム物理名: FF103M @290';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.CREATION_TERMINAL IS '作成端末 既存システム物理名: FF104M';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.CREATION_RESPONSIBLE IS '作成担当者 既存システム物理名: FF105M';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: FF106D';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.UPDATE_TIME IS '更新時間 既存システム物理名: FF107H';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: FF108M';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.UPDATE_TERMINAL IS '更新端末 既存システム物理名: FF109M';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.UPDATE_RESPONSIBLE IS '更新担当者 既存システム物理名: FF110M';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.DELETE_SIGN IS '削除サイン 既存システム物理名: FF111S';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.ADDRESS_CD IS '住所コード 既存システム物理名: FF112C';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.TARGET_START_DATE IS '対象開始日 既存システム物理名: FF113D';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.TARGET_END_DATE IS '対象終了日 既存システム物理名: FF114D';
COMMENT ON COLUMN CITY_GAS_RATE_ADDRESS_FILE.GAS_COMPANY_CATEGORY IS 'ガス会社区分 既存システム物理名: FF115B';
