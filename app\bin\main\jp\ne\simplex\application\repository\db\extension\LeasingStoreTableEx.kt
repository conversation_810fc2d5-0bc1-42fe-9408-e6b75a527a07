package jp.ne.simplex.application.repository.db.extension

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.db.jooq.gen.tables.pojos.LeasingStoreTablePojo
import org.slf4j.LoggerFactory

class LeasingStoreTableEx {

    companion object {
        private val log = LoggerFactory.getLogger(LeasingStoreTableEx::class.java)

        fun LeasingStoreTablePojo.getBranchCode(): Branch.Code? {
            return try {
                this.branchCd?.let { Branch.Code.of(it) }
            } catch (_: Exception) {
                // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
                log.warn("Failed to deserialize LeasingStoreTable record. $this")
                null
            }
        }

        fun LeasingStoreTablePojo.getLeasingBranchCode(): Branch.Code? {
            return try {
                this.leasingStoreCd?.let { Branch.Code.of(it) }
            } catch (_: Exception) {
                // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
                log.warn("Failed to deserialize LeasingStoreTable record. $this")
                null
            }
        }
    }

}
