/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable
import java.math.BigDecimal


/**
 * 物件メンテナンス情報 既存システム物理名: EMEBMP
 */
@Suppress("UNCHECKED_CAST")
data class PropertyMaintenanceInfoPojo(
    var buildingCd: String,
    var roomCd: String,
    var rentalPrice: Int? = null,
    var securityDeposit: Int? = null,
    var keyMoney: Int? = null,
    var listingCategory: Byte? = null,
    var listingCategoryGoodRoomNet: Byte? = null,
    var lowRepairCostSpecification: Byte? = null,
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updater: String? = null,
    var adAmount: Int? = null,
    var comment: String? = null,
    var homesPanoramaSendFlag: Byte? = null,
    var adUnit: Byte? = null,
    var ffAmount: BigDecimal? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: PropertyMaintenanceInfoPojo = other as PropertyMaintenanceInfoPojo
        if (this.buildingCd != o.buildingCd)
            return false
        if (this.roomCd != o.roomCd)
            return false
        if (this.rentalPrice == null) {
            if (o.rentalPrice != null)
                return false
        }
        else if (this.rentalPrice != o.rentalPrice)
            return false
        if (this.securityDeposit == null) {
            if (o.securityDeposit != null)
                return false
        }
        else if (this.securityDeposit != o.securityDeposit)
            return false
        if (this.keyMoney == null) {
            if (o.keyMoney != null)
                return false
        }
        else if (this.keyMoney != o.keyMoney)
            return false
        if (this.listingCategory == null) {
            if (o.listingCategory != null)
                return false
        }
        else if (this.listingCategory != o.listingCategory)
            return false
        if (this.listingCategoryGoodRoomNet == null) {
            if (o.listingCategoryGoodRoomNet != null)
                return false
        }
        else if (this.listingCategoryGoodRoomNet != o.listingCategoryGoodRoomNet)
            return false
        if (this.lowRepairCostSpecification == null) {
            if (o.lowRepairCostSpecification != null)
                return false
        }
        else if (this.lowRepairCostSpecification != o.lowRepairCostSpecification)
            return false
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.adAmount == null) {
            if (o.adAmount != null)
                return false
        }
        else if (this.adAmount != o.adAmount)
            return false
        if (this.comment == null) {
            if (o.comment != null)
                return false
        }
        else if (this.comment != o.comment)
            return false
        if (this.homesPanoramaSendFlag == null) {
            if (o.homesPanoramaSendFlag != null)
                return false
        }
        else if (this.homesPanoramaSendFlag != o.homesPanoramaSendFlag)
            return false
        if (this.adUnit == null) {
            if (o.adUnit != null)
                return false
        }
        else if (this.adUnit != o.adUnit)
            return false
        if (this.ffAmount == null) {
            if (o.ffAmount != null)
                return false
        }
        else if (this.ffAmount != o.ffAmount)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + this.buildingCd.hashCode()
        result = prime * result + this.roomCd.hashCode()
        result = prime * result + (if (this.rentalPrice == null) 0 else this.rentalPrice.hashCode())
        result = prime * result + (if (this.securityDeposit == null) 0 else this.securityDeposit.hashCode())
        result = prime * result + (if (this.keyMoney == null) 0 else this.keyMoney.hashCode())
        result = prime * result + (if (this.listingCategory == null) 0 else this.listingCategory.hashCode())
        result = prime * result + (if (this.listingCategoryGoodRoomNet == null) 0 else this.listingCategoryGoodRoomNet.hashCode())
        result = prime * result + (if (this.lowRepairCostSpecification == null) 0 else this.lowRepairCostSpecification.hashCode())
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.adAmount == null) 0 else this.adAmount.hashCode())
        result = prime * result + (if (this.comment == null) 0 else this.comment.hashCode())
        result = prime * result + (if (this.homesPanoramaSendFlag == null) 0 else this.homesPanoramaSendFlag.hashCode())
        result = prime * result + (if (this.adUnit == null) 0 else this.adUnit.hashCode())
        result = prime * result + (if (this.ffAmount == null) 0 else this.ffAmount.hashCode())
        return result
    }
}
