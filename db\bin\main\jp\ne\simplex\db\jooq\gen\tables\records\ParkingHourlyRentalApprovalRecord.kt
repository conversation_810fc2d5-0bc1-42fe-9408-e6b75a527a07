/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ParkingHourlyRentalApprovalTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingHourlyRentalApprovalPojo

import org.jooq.impl.TableRecordImpl


/**
 * 駐車場時間貸承諾 既存システム物理名: HVR10P
 */
@Suppress("UNCHECKED_CAST")
open class ParkingHourlyRentalApprovalRecord private constructor() : TableRecordImpl<ParkingHourlyRentalApprovalRecord>(ParkingHourlyRentalApprovalTable.PARKING_HOURLY_RENTAL_APPROVAL) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var buildingCode: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var parkingTimeRentalConsentType: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var buildingName: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var registrationContractTypeName: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var registrationManagementResponsible: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var registrationOwnerCd: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var registrationOwnerName: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var consentDate: Int?
        set(value): Unit = set(14, value)
        get(): Int? = get(14) as Int?

    /**
     * Create a detached, initialised ParkingHourlyRentalApprovalRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteSign: Byte? = null, buildingCode: String? = null, parkingTimeRentalConsentType: String? = null, buildingName: String? = null, registrationContractTypeName: String? = null, registrationManagementResponsible: String? = null, registrationOwnerCd: String? = null, registrationOwnerName: String? = null, consentDate: Int? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteSign = logicalDeleteSign
        this.buildingCode = buildingCode
        this.parkingTimeRentalConsentType = parkingTimeRentalConsentType
        this.buildingName = buildingName
        this.registrationContractTypeName = registrationContractTypeName
        this.registrationManagementResponsible = registrationManagementResponsible
        this.registrationOwnerCd = registrationOwnerCd
        this.registrationOwnerName = registrationOwnerName
        this.consentDate = consentDate
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ParkingHourlyRentalApprovalRecord
     */
    constructor(value: ParkingHourlyRentalApprovalPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteSign = value.logicalDeleteSign
            this.buildingCode = value.buildingCode
            this.parkingTimeRentalConsentType = value.parkingTimeRentalConsentType
            this.buildingName = value.buildingName
            this.registrationContractTypeName = value.registrationContractTypeName
            this.registrationManagementResponsible = value.registrationManagementResponsible
            this.registrationOwnerCd = value.registrationOwnerCd
            this.registrationOwnerName = value.registrationOwnerName
            this.consentDate = value.consentDate
            resetChangedOnNotNull()
        }
    }
}
