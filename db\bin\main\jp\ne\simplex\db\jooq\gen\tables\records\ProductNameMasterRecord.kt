/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigInteger

import jp.ne.simplex.db.jooq.gen.tables.ProductNameMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ProductNameMasterPojo

import org.jooq.Record3
import org.jooq.impl.UpdatableRecordImpl


/**
 * 商品名称マスタ 既存システム物理名: BGJMAP
 */
@Suppress("UNCHECKED_CAST")
open class ProductNameMasterRecord private constructor() : UpdatableRecordImpl<ProductNameMasterRecord>(ProductNameMasterTable.PRODUCT_NAME_MASTER) {

    open var uniqueNumber: BigInteger
        set(value): Unit = set(0, value)
        get(): BigInteger = get(0) as BigInteger

    open var creationDate: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creationTime: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updateProgram: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var updater: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var deleteFlag: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var productNameCode: Short
        set(value): Unit = set(8, value)
        get(): Short = get(8) as Short

    open var effectiveStartDate: Int
        set(value): Unit = set(9, value)
        get(): Int = get(9) as Int

    open var effectiveEndDate: Int?
        set(value): Unit = set(10, value)
        get(): Int? = get(10) as Int?

    open var productName: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var productNameAbbreviation: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record3<BigInteger?, Short?, Int?> = super.key() as Record3<BigInteger?, Short?, Int?>

    /**
     * Create a detached, initialised ProductNameMasterRecord
     */
    constructor(uniqueNumber: BigInteger, creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgram: String? = null, updater: String? = null, deleteFlag: String? = null, productNameCode: Short, effectiveStartDate: Int, effectiveEndDate: Int? = null, productName: String? = null, productNameAbbreviation: String? = null): this() {
        this.uniqueNumber = uniqueNumber
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgram = updateProgram
        this.updater = updater
        this.deleteFlag = deleteFlag
        this.productNameCode = productNameCode
        this.effectiveStartDate = effectiveStartDate
        this.effectiveEndDate = effectiveEndDate
        this.productName = productName
        this.productNameAbbreviation = productNameAbbreviation
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ProductNameMasterRecord
     */
    constructor(value: ProductNameMasterPojo?): this() {
        if (value != null) {
            this.uniqueNumber = value.uniqueNumber
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgram = value.updateProgram
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            this.productNameCode = value.productNameCode
            this.effectiveStartDate = value.effectiveStartDate
            this.effectiveEndDate = value.effectiveEndDate
            this.productName = value.productName
            this.productNameAbbreviation = value.productNameAbbreviation
            resetChangedOnNotNull()
        }
    }
}
