/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.EnergySavingLabelInfoFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.EnergySavingLabelInfoFilePojo

import org.jooq.impl.TableRecordImpl


/**
 * 省エネラベル情報ファイル 既存システム物理名: BESELP
 */
@Suppress("UNCHECKED_CAST")
open class EnergySavingLabelInfoFileRecord private constructor() : TableRecordImpl<EnergySavingLabelInfoFileRecord>(EnergySavingLabelInfoFileTable.ENERGY_SAVING_LABEL_INFO_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgram: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var deleteFlag: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var applicationCd: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var additionalCd: Short?
        set(value): Unit = set(8, value)
        get(): Short? = get(8) as Short?

    open var buildingNo: Byte?
        set(value): Unit = set(9, value)
        get(): Byte? = get(9) as Byte?

    open var buildingCd: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var roomCd: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var approvalFlag: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var authorizationDate: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var thirdPartyEvaluationFlag: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var exclusionFlag: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var imageFileName: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var energyConsumptionPerformance: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var energyConsumptionPerformanceSolar: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var insulationPerformance: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var estimatedUtilityCost: Int?
        set(value): Unit = set(20, value)
        get(): Int? = get(20) as Int?

    open var zehStandardFlag: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var netZeroEnergyFlag: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    /**
     * Create a detached, initialised EnergySavingLabelInfoFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgram: String? = null, updater: String? = null, deleteFlag: String? = null, applicationCd: Int? = null, additionalCd: Short? = null, buildingNo: Byte? = null, buildingCd: String? = null, roomCd: String? = null, approvalFlag: String? = null, authorizationDate: Int? = null, thirdPartyEvaluationFlag: String? = null, exclusionFlag: String? = null, imageFileName: String? = null, energyConsumptionPerformance: String? = null, energyConsumptionPerformanceSolar: String? = null, insulationPerformance: String? = null, estimatedUtilityCost: Int? = null, zehStandardFlag: String? = null, netZeroEnergyFlag: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgram = updateProgram
        this.updater = updater
        this.deleteFlag = deleteFlag
        this.applicationCd = applicationCd
        this.additionalCd = additionalCd
        this.buildingNo = buildingNo
        this.buildingCd = buildingCd
        this.roomCd = roomCd
        this.approvalFlag = approvalFlag
        this.authorizationDate = authorizationDate
        this.thirdPartyEvaluationFlag = thirdPartyEvaluationFlag
        this.exclusionFlag = exclusionFlag
        this.imageFileName = imageFileName
        this.energyConsumptionPerformance = energyConsumptionPerformance
        this.energyConsumptionPerformanceSolar = energyConsumptionPerformanceSolar
        this.insulationPerformance = insulationPerformance
        this.estimatedUtilityCost = estimatedUtilityCost
        this.zehStandardFlag = zehStandardFlag
        this.netZeroEnergyFlag = netZeroEnergyFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised EnergySavingLabelInfoFileRecord
     */
    constructor(value: EnergySavingLabelInfoFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgram = value.updateProgram
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            this.applicationCd = value.applicationCd
            this.additionalCd = value.additionalCd
            this.buildingNo = value.buildingNo
            this.buildingCd = value.buildingCd
            this.roomCd = value.roomCd
            this.approvalFlag = value.approvalFlag
            this.authorizationDate = value.authorizationDate
            this.thirdPartyEvaluationFlag = value.thirdPartyEvaluationFlag
            this.exclusionFlag = value.exclusionFlag
            this.imageFileName = value.imageFileName
            this.energyConsumptionPerformance = value.energyConsumptionPerformance
            this.energyConsumptionPerformanceSolar = value.energyConsumptionPerformanceSolar
            this.insulationPerformance = value.insulationPerformance
            this.estimatedUtilityCost = value.estimatedUtilityCost
            this.zehStandardFlag = value.zehStandardFlag
            this.netZeroEnergyFlag = value.netZeroEnergyFlag
            resetChangedOnNotNull()
        }
    }
}
