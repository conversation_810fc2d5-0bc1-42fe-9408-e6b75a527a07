-- TABLE: PARKING_RESERVATION(駐車場予約)

CREATE TABLE PARKING_RESERVATION(
     PARKING_RESERVATION_ID                       varchar(36)       NOT NULL    
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    PARKING_LOT_CODE                             varchar(3)                    
,    RESERVE_TYPE                                 varchar(2)        NOT NULL    
,    RESERVE_STATUS                               varchar(2)        NOT NULL    
,    RECEPTION_DATE                               numeric(8,0)      NOT NULL    
,    RECEPTION_STAFF                              varchar(42)                   
,    RESERVER_NAME                                varchar(42)                   
,    RESERVER_TEL                                 varchar(15)                   
,    REMAR<PERSON>                                      varchar(255)                  
,    RESERVER_SYSTEM                              varchar(2)                    
,    RESERVE_START_DATETIME                       timestamp(0)                  
,    RESERVE_END_DATETIME                         timestamp(0)                  
,    LINKED_BUILDING_CODE                         varchar(9)                    
,    LINKED_ROOM_CODE                             varchar(5)                    
,    EBOARD_PARKING_RESERVATION_ID                varchar(3)        NOT NULL    
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(10)                   
,    DELETE_FLAG                                  varchar(1)        NOT NULL    
,    CONSTRAINT PK_PARKING_RESERVATION PRIMARY KEY (PARKING_RESERVATION_ID)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PARKING_RESERVATION IS '駐車場予約 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.PARKING_RESERVATION_ID IS 'ユニークID 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.BUILDING_CODE IS '建物コード 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.PARKING_LOT_CODE IS '駐車場コード 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.RESERVE_TYPE IS '予約種別 既存システム物理名: - 0: 申込(外部システム), 1: 申込(DKリンク), 2: 作業, 3: 場所変更, 4: 1日利用';
COMMENT ON COLUMN PARKING_RESERVATION.RESERVE_STATUS IS '予約状態 既存システム物理名: - 0: 仮申込, 1: 受付, 2: 契約済み, 3: キャンセル';
COMMENT ON COLUMN PARKING_RESERVATION.RECEPTION_DATE IS '受付日 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.RECEPTION_STAFF IS '受付担当者氏名 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.RESERVER_NAME IS '利用者氏名 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.RESERVER_TEL IS '利用者電話番号 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.REMARKS IS '予約メモ 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.RESERVER_SYSTEM IS '予約システム 既存システム物理名: - 0: DKリンク, 1: キマルームサイン, 2: ウェルカムパーク';
COMMENT ON COLUMN PARKING_RESERVATION.RESERVE_START_DATETIME IS '予約開始日時 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.RESERVE_END_DATETIME IS '予約終了日時 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.LINKED_BUILDING_CODE IS '同時に仮押さえした物件の建物コード 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.LINKED_ROOM_CODE IS '同時に仮押さえした物件の部屋コード 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.EBOARD_PARKING_RESERVATION_ID IS 'いい物件ボード用ユニークID 既存システム物理名: - いい物件ボードで同一建物の駐車場区画の予約を識別するためのID';
COMMENT ON COLUMN PARKING_RESERVATION.CREATION_DATE IS '作成年月日 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.CREATION_TIME IS '作成時刻 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.CREATOR IS '作成者 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.UPDATE_DATE IS '更新年月日 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.UPDATE_TIME IS '更新時刻 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.UPDATER IS '更新者 既存システム物理名: -';
COMMENT ON COLUMN PARKING_RESERVATION.DELETE_FLAG IS '削除フラグ 既存システム物理名: -';
