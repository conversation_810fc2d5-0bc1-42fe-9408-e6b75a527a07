-- TABLE: PRODUCT_MASTER(商品マスタ)

CREATE TABLE PRODUCT_MASTER(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM                               varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    DELETE_FLAG                                  varchar(1)                    
,    PRODUCT_NAME_CODE                            numeric(3)        NOT NULL    
,    PRODUCT_CODE_BRANCH                          numeric(2)        NOT NULL    
,    EFFECTIVE_START_DATE                         numeric(8)        NOT NULL    
,    EFFECTIVE_END_DATE                           numeric(8)                    
,    GRADE_NAME                                   varchar(22)                   
,    GRADE_ABBREVIATION                           varchar(12)                   
,    BUILDING_TYPE_CODE_ST                        varchar(3)                    
,    MIN_UNIT_ALIGNMENT                           numeric(3)                    
,    MAX_UNIT_ALIGNMENT                           numeric(3)                    
,    CONSTRAINT UQ_PRODUCT_MASTER UNIQUE (PRODUCT_NAME_CODE, PRODUCT_CODE_BRANCH, EFFECTIVE_START_DATE, DELETE_FLAG)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PRODUCT_MASTER IS '商品マスタ 既存システム物理名: BGKMBP';
COMMENT ON COLUMN PRODUCT_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: BGK01D @290';
COMMENT ON COLUMN PRODUCT_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: BGK02H @290';
COMMENT ON COLUMN PRODUCT_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: BGK03D @290 削除フラグ " ":有効 "C":工事中止 "D":論理';
COMMENT ON COLUMN PRODUCT_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: BGK04H';
COMMENT ON COLUMN PRODUCT_MASTER.UPDATE_PROGRAM IS '更新プログラム 既存システム物理名: BGK05P';
COMMENT ON COLUMN PRODUCT_MASTER.UPDATER IS '更新者 既存システム物理名: BGK06P';
COMMENT ON COLUMN PRODUCT_MASTER.DELETE_FLAG IS '削除フラグ 既存システム物理名: BGK08S';
COMMENT ON COLUMN PRODUCT_MASTER.PRODUCT_NAME_CODE IS '商品名称コード 既存システム物理名: BGKS21';
COMMENT ON COLUMN PRODUCT_MASTER.PRODUCT_CODE_BRANCH IS '商品コード枝番 既存システム物理名: BGKS24';
COMMENT ON COLUMN PRODUCT_MASTER.EFFECTIVE_START_DATE IS '適用開始日 既存システム物理名: BGKT01';
COMMENT ON COLUMN PRODUCT_MASTER.EFFECTIVE_END_DATE IS '適用終了日 既存システム物理名: BGKT02';
COMMENT ON COLUMN PRODUCT_MASTER.GRADE_NAME IS 'グレード名称 既存システム物理名: BGKG04';
COMMENT ON COLUMN PRODUCT_MASTER.GRADE_ABBREVIATION IS 'グレード略称 既存システム物理名: BGKG05';
COMMENT ON COLUMN PRODUCT_MASTER.BUILDING_TYPE_CODE_ST IS '建物種別コード－ST 既存システム物理名: BGKT03';
COMMENT ON COLUMN PRODUCT_MASTER.MIN_UNIT_ALIGNMENT IS '最低戸並び 既存システム物理名: BGKS25';
COMMENT ON COLUMN PRODUCT_MASTER.MAX_UNIT_ALIGNMENT IS '最高戸並び 既存システム物理名: BGKS26';
