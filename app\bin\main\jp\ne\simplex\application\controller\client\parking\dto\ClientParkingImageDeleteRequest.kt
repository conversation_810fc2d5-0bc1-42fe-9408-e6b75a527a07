package jp.ne.simplex.application.controller.client.parking.dto

import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException
import org.springframework.web.bind.annotation.RequestPart

class ClientParkingImageDeleteRequest(
    @RequestPart("buildingCd")
    @field:Schema(description = "建物コード", example = "000130301")
    val buildingCd: String,
) {
    fun toServiceInterface(): Building.Code {
        try {
            return Building.Code.of(buildingCd)
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }
}
