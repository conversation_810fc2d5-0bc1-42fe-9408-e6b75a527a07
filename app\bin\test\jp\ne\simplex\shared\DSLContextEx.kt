package jp.ne.simplex.shared

import jp.ne.simplex.application.model.*
import jp.ne.simplex.db.jooq.gen.tables.pojos.*
import jp.ne.simplex.db.jooq.gen.tables.references.*
import org.jooq.DSLContext

class DSLContextEx {

    companion object {

        // ############# TemporaryReservationFile 関連 #############
        fun DSLContext.countTemporaryReservationsBy(id: Property.Id): Int {
            return this.selectFrom(TEMPORARY_RESERVATION_FILE)
                .where(TEMPORARY_RESERVATION_FILE.BUILDING_CD.eq(id.buildingCode.value))
                .and(TEMPORARY_RESERVATION_FILE.ROOM_CD.eq(id.roomCode.value))
                .count()
        }

        fun DSLContext.selectTemporaryReservationBy(id: Property.Id): List<TemporaryReservationFilePojo> {
            return this.selectFrom(TEMPORARY_RESERVATION_FILE)
                .where(TEMPORARY_RESERVATION_FILE.BUILDING_CD.eq(id.buildingCode.value))
                .and(TEMPORARY_RESERVATION_FILE.ROOM_CD.eq(id.roomCode.value))
                .fetchInto(TemporaryReservationFilePojo::class.java)
        }

        // @formatter:off
        fun DSLContext.selectTemporaryReservationBy(buildingCode: String, roomCode: String): List<TemporaryReservationFilePojo> {
            // @formatter:on
            return this.selectTemporaryReservationBy(
                id = Property.Id(Building.Code.of(buildingCode), Room.Code.of(roomCode)),
            )
        }

        // ############# PropertyMaintenanceInfo 関連 #############
        fun DSLContext.selectPropertyMaintenanceBy(id: Property.Id): List<PropertyMaintenanceInfoPojo> {
            return this.selectFrom(PROPERTY_MAINTENANCE_INFO)
                .where(PROPERTY_MAINTENANCE_INFO.BUILDING_CD.eq(id.buildingCode.value))
                .and(PROPERTY_MAINTENANCE_INFO.ROOM_CD.eq(id.roomCode.value))
                .fetchInto(PropertyMaintenanceInfoPojo::class.java)
        }

        // ############# ParkingReservation 関連 #############
        fun DSLContext.selectBy(parkingLotId: ParkingLot.Id): List<ParkingReservationPojo> {
            return this.select().from(PARKING_RESERVATION)
                .where(PARKING_RESERVATION.BUILDING_CODE.eq(parkingLotId.buildingCode.value))
                .and(PARKING_RESERVATION.PARKING_LOT_CODE.eq(parkingLotId.parkingLotCode.value))
                .and(PARKING_RESERVATION.DELETE_FLAG.eq("0"))
                .fetchInto(ParkingReservationPojo::class.java)
        }

        fun DSLContext.selectBy(parkingReservationId: ParkingReservation.Id): ParkingReservationPojo? {
            return this.select().from(PARKING_RESERVATION)
                .where(PARKING_RESERVATION.PARKING_RESERVATION_ID.eq(parkingReservationId.value))
                .and(PARKING_RESERVATION.DELETE_FLAG.eq("0"))
                .fetchOneInto(ParkingReservationPojo::class.java)
        }

        // ############# ParkingContractPossibility 関連 #############
        fun DSLContext.selectBy(orderCode: Building.OrderCode): ParkingContractPossibilityPojo? {
            return this.select().from(PARKING_CONTRACT_POSSIBILITY)
                .where(PARKING_CONTRACT_POSSIBILITY.ORDER_CODE.eq(orderCode.value))
                .and(PARKING_CONTRACT_POSSIBILITY.DELETE_FLAG.eq("0"))
                .fetchOneInto(ParkingContractPossibilityPojo::class.java)
        }

        // ############# ParkingEnable 関連 #############
        fun DSLContext.selectParkingEnableBy(parkingLotId: ParkingLot.Id): List<ParkingEnablePojo> {
            return this.select().from(PARKING_ENABLE)
                .where(PARKING_ENABLE.BUILDING_CODE.eq(parkingLotId.buildingCode.value))
                .and(PARKING_ENABLE.PARKING_LOT_CODE.eq(parkingLotId.parkingLotCode.value))
                .and(PARKING_ENABLE.DELETE_FLAG.eq("0"))
                .fetchInto(ParkingEnablePojo::class.java)
        }

        // ############# ExclusiveProperty 関連 #############
        fun DSLContext.selectExclusivePropertyBy(id: ExclusiveProperty.Id): List<ExclusivePropertyPojo> {
            return this.select().from(EXCLUSIVE_PROPERTY)
                .where(EXCLUSIVE_PROPERTY.ID.eq(id.value))
                .fetchInto(ExclusivePropertyPojo::class.java)
        }

        fun DSLContext.selectExclusivePropertyECodeBy(id: ExclusiveProperty.Id): List<ExclusivePropertyECodePojo> {
            return this.select().from(EXCLUSIVE_PROPERTY_E_CODE)
                .where(EXCLUSIVE_PROPERTY_E_CODE.ID.eq(id.value))
                .fetchInto(ExclusivePropertyECodePojo::class.java)
        }

        fun DSLContext.selectExclusivePropertyBy(propertyId: Property.Id): List<ExclusivePropertyPojo> {
            return this.select().from(EXCLUSIVE_PROPERTY)
                .where(EXCLUSIVE_PROPERTY.BUILDING_CODE.eq(propertyId.buildingCode.value))
                .and(EXCLUSIVE_PROPERTY.ROOM_CODE.eq(propertyId.roomCode.value))
                .fetchInto(ExclusivePropertyPojo::class.java)
        }
    }
}
