package jp.ne.simplex.application.controller.client.property.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.*
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException

data class ClientTemporaryReservationCancelRequest(
    @JsonProperty("buildingCd")
    @field:Schema(description = "建物コード", example = "000130305")
    val buildingCd: String,

    @JsonProperty("roomCd")
    @field:Schema(description = "部屋コード", example = "01010")
    val roomCd: String,

    @JsonProperty("comment")
    @field:Schema(description = "コメント", example = "コメントが入ります")
    val comment: String?,

    @JsonProperty("registrationDate")
    @field:Schema(description = "登録日", example = "20241031")
    val registrationDate: String?,

    @JsonProperty("registrationTime")
    @field:Schema(description = "登録時刻", example = "101952")
    val registrationTime: String?,
) {

    // Service層の Interface に変換する
    fun toServiceInterface(): TemporaryReservation {
        try {
            return CancelTemporaryReservation(
                id = Property.Id(Building.Code.of(buildingCd), Room.Code.of(roomCd)),
                comment = TemporaryReservation.Comment.of(comment),
                version = registrationDate?.let { date ->
                    registrationTime?.let { time ->
                        TemporaryReservation.Version.of(date, time)
                    }
                }
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }

}
