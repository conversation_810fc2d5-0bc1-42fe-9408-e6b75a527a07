-- TABLE: DAITO_BULK_LEASE_CONTRACT_DETAILS(大東一括借上・契約内容)

CREATE TABLE DAITO_BULK_LEASE_CONTRACT_DETAILS(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATION_PROGRAM_ID                          varchar(10)                   
,    CREATION_TERMINAL_ID                         varchar(8)                    
,    CREATION_RESPONSIBLE_CD                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATE_TERMINAL_ID                           varchar(8)                    
,    UPDATE_RESPONSIBLE_CD                        varchar(6)                    
,    DELETE_SIGN                                  varchar(1)                    
,    BUILDING_CD                                  varchar(9)                    
,    CONTRACT_CONCLUSION_DATE                     numeric(8,0)                  
,    CONTRACT_START_DATE                          numeric(8,0)                  
,    CONTRACT_EXPIRATION_DATE                     numeric(8,0)                  
,    PAYMENT_START_DATE_RESIDENTIAL_PARKING       numeric(8,0)                  
,    PAYMENT_START_DATE_BUSINESS                  numeric(8,0)                  
,    MANAGEMENT_TERMINATION_END_DATE              numeric(8,0)                  
,    HEAD_OFFICE_APPROVAL_DATE                    numeric(8,0)                  
,    LANDLORD_CD                                  varchar(10)                   
,    HEAD_OFFICE_APPROVAL_CATEGORY                varchar(1)                    
,    CONTRACT_TYPE_CATEGORY                       varchar(1)                    
,    RENTAL_REVISION_DATE_RESIDENTIAL_PARKING     numeric(8,0)                  
,    RENTAL_REVISION_DATE_BUSINESS                numeric(8,0)                  
,    RENTAL_REVISION_STATUS_CATEGORY              varchar(1)                    
,    THIRTY_FIVE_YEAR_LUMP_SUM_CATEGORY           numeric(1,0)                  
,    CONSTRAINT UQ_DAITO_BULK_LEASE_CONTRACT_DETAILS UNIQUE (BUILDING_CD, DELETE_SIGN)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE DAITO_BULK_LEASE_CONTRACT_DETAILS IS '大東一括借上・契約内容 既存システム物理名: HUA10P';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.CREATION_DATE IS '作成年月日 既存システム物理名: HUA01D';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.CREATION_TIME IS '作成時間 既存システム物理名: HUA02H';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.CREATION_PROGRAM_ID IS '作成プログラムID 既存システム物理名: HUA03M';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.CREATION_TERMINAL_ID IS '作成端末ID 既存システム物理名: HUA04M';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.CREATION_RESPONSIBLE_CD IS '作成担当者CD 既存システム物理名: HUA05M';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.UPDATE_DATE IS '更新年月日 既存システム物理名: HUA06D';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.UPDATE_TIME IS '更新時間 既存システム物理名: HUA07H';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: HUA08M';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.UPDATE_TERMINAL_ID IS '更新端末ID 既存システム物理名: HUA09M';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.UPDATE_RESPONSIBLE_CD IS '更新担当者CD 既存システム物理名: HUA10M';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.DELETE_SIGN IS '削除サイン 既存システム物理名: HUA11S';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.BUILDING_CD IS '建物CD 既存システム物理名: HUA12C';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.CONTRACT_CONCLUSION_DATE IS '契約締結日 既存システム物理名: HUA13D';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.CONTRACT_START_DATE IS '契約開始日 既存システム物理名: HUA14D';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.CONTRACT_EXPIRATION_DATE IS '契約満了日 既存システム物理名: HUA15D';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.PAYMENT_START_DATE_RESIDENTIAL_PARKING IS '支払開始日(居・駐) 既存システム物理名: HUA16D';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.PAYMENT_START_DATE_BUSINESS IS '支払開始日(事) 既存システム物理名: HUA17D';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.MANAGEMENT_TERMINATION_END_DATE IS '管理解約終了日 既存システム物理名: HUA18D';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.HEAD_OFFICE_APPROVAL_DATE IS '本社承認日 既存システム物理名: HUA19D';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.LANDLORD_CD IS '家主CD 既存システム物理名: HUA20C';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.HEAD_OFFICE_APPROVAL_CATEGORY IS '本社承認区分 既存システム物理名: HUA21B';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.CONTRACT_TYPE_CATEGORY IS '契約形態区分 既存システム物理名: HUA22B';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.RENTAL_REVISION_DATE_RESIDENTIAL_PARKING IS '賃料改定日(居・駐) 既存システム物理名: HUA23D';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.RENTAL_REVISION_DATE_BUSINESS IS '賃料改定日(事) 既存システム物理名: HUA24D';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.RENTAL_REVISION_STATUS_CATEGORY IS '賃料改定状況区分 既存システム物理名: HUA25B';
COMMENT ON COLUMN DAITO_BULK_LEASE_CONTRACT_DETAILS.THIRTY_FIVE_YEAR_LUMP_SUM_CATEGORY IS '35年一括区分 既存システム物理名: HUA26B';
