/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.BuildingBasicInfoFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingBasicInfoFilePojo

import org.jooq.impl.TableRecordImpl


/**
 * 建物基本情報ファイル 既存システム物理名: HAD40P
 */
@Suppress("UNCHECKED_CAST")
open class BuildingBasicInfoFileRecord private constructor() : TableRecordImpl<BuildingBasicInfoFileRecord>(BuildingBasicInfoFileTable.BUILDING_BASIC_INFO_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updatePgm: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateResponsibleCd: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var buildingCd: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var equipmentInfoTentativeConfirmDate: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var equipmentInfoConfirmDate: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var equipmentDetailCompletionDate: Int?
        set(value): Unit = set(9, value)
        get(): Int? = get(9) as Int?

    open var buildingStructure: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var roofFinish: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var exteriorWall: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var toiletType: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var drainageType: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var landArea: BigDecimal?
        set(value): Unit = set(15, value)
        get(): BigDecimal? = get(15) as BigDecimal?

    open var plantingArea: BigDecimal?
        set(value): Unit = set(16, value)
        get(): BigDecimal? = get(16) as BigDecimal?

    open var unpavedParkingArea: BigDecimal?
        set(value): Unit = set(17, value)
        get(): BigDecimal? = get(17) as BigDecimal?

    open var sickHouseMeasures: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var paintingWithWarranty: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var paintingWarrantyDate: Int?
        set(value): Unit = set(20, value)
        get(): Int? = get(20) as Int?

    open var waterSupplyType: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var waterMeterType: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var waterMeterShared: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var waterMeterDirect: Byte?
        set(value): Unit = set(24, value)
        get(): Byte? = get(24) as Byte?

    open var waterPipeDiameterMain: Short?
        set(value): Unit = set(25, value)
        get(): Short? = get(25) as Short?

    open var waterPipeDiameterUnit: Short?
        set(value): Unit = set(26, value)
        get(): Short? = get(26) as Short?

    open var waterPipeDiameterBusiness: Short?
        set(value): Unit = set(27, value)
        get(): Short? = get(27) as Short?

    open var waterBureauCompanyCd: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var waterBureauBranchCd: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var electricMeterType: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var electricMeterShared: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var electricMeterDirect: Byte?
        set(value): Unit = set(32, value)
        get(): Byte? = get(32) as Byte?

    open var electricCompanyCd: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var electricBranchCd: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var gasType: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var gasMeterType: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var gasCompanyCd: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var gasBranchCd: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var deleteDate: Int?
        set(value): Unit = set(39, value)
        get(): Int? = get(39) as Int?

    /**
     * Create a detached, initialised BuildingBasicInfoFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updatePgm: String? = null, updateResponsibleCd: String? = null, buildingCd: String? = null, equipmentInfoTentativeConfirmDate: Int? = null, equipmentInfoConfirmDate: Int? = null, equipmentDetailCompletionDate: Int? = null, buildingStructure: String? = null, roofFinish: String? = null, exteriorWall: String? = null, toiletType: String? = null, drainageType: String? = null, landArea: BigDecimal? = null, plantingArea: BigDecimal? = null, unpavedParkingArea: BigDecimal? = null, sickHouseMeasures: String? = null, paintingWithWarranty: String? = null, paintingWarrantyDate: Int? = null, waterSupplyType: String? = null, waterMeterType: String? = null, waterMeterShared: String? = null, waterMeterDirect: Byte? = null, waterPipeDiameterMain: Short? = null, waterPipeDiameterUnit: Short? = null, waterPipeDiameterBusiness: Short? = null, waterBureauCompanyCd: String? = null, waterBureauBranchCd: String? = null, electricMeterType: String? = null, electricMeterShared: String? = null, electricMeterDirect: Byte? = null, electricCompanyCd: String? = null, electricBranchCd: String? = null, gasType: String? = null, gasMeterType: String? = null, gasCompanyCd: String? = null, gasBranchCd: String? = null, deleteDate: Int? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updatePgm = updatePgm
        this.updateResponsibleCd = updateResponsibleCd
        this.buildingCd = buildingCd
        this.equipmentInfoTentativeConfirmDate = equipmentInfoTentativeConfirmDate
        this.equipmentInfoConfirmDate = equipmentInfoConfirmDate
        this.equipmentDetailCompletionDate = equipmentDetailCompletionDate
        this.buildingStructure = buildingStructure
        this.roofFinish = roofFinish
        this.exteriorWall = exteriorWall
        this.toiletType = toiletType
        this.drainageType = drainageType
        this.landArea = landArea
        this.plantingArea = plantingArea
        this.unpavedParkingArea = unpavedParkingArea
        this.sickHouseMeasures = sickHouseMeasures
        this.paintingWithWarranty = paintingWithWarranty
        this.paintingWarrantyDate = paintingWarrantyDate
        this.waterSupplyType = waterSupplyType
        this.waterMeterType = waterMeterType
        this.waterMeterShared = waterMeterShared
        this.waterMeterDirect = waterMeterDirect
        this.waterPipeDiameterMain = waterPipeDiameterMain
        this.waterPipeDiameterUnit = waterPipeDiameterUnit
        this.waterPipeDiameterBusiness = waterPipeDiameterBusiness
        this.waterBureauCompanyCd = waterBureauCompanyCd
        this.waterBureauBranchCd = waterBureauBranchCd
        this.electricMeterType = electricMeterType
        this.electricMeterShared = electricMeterShared
        this.electricMeterDirect = electricMeterDirect
        this.electricCompanyCd = electricCompanyCd
        this.electricBranchCd = electricBranchCd
        this.gasType = gasType
        this.gasMeterType = gasMeterType
        this.gasCompanyCd = gasCompanyCd
        this.gasBranchCd = gasBranchCd
        this.deleteDate = deleteDate
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BuildingBasicInfoFileRecord
     */
    constructor(value: BuildingBasicInfoFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updatePgm = value.updatePgm
            this.updateResponsibleCd = value.updateResponsibleCd
            this.buildingCd = value.buildingCd
            this.equipmentInfoTentativeConfirmDate = value.equipmentInfoTentativeConfirmDate
            this.equipmentInfoConfirmDate = value.equipmentInfoConfirmDate
            this.equipmentDetailCompletionDate = value.equipmentDetailCompletionDate
            this.buildingStructure = value.buildingStructure
            this.roofFinish = value.roofFinish
            this.exteriorWall = value.exteriorWall
            this.toiletType = value.toiletType
            this.drainageType = value.drainageType
            this.landArea = value.landArea
            this.plantingArea = value.plantingArea
            this.unpavedParkingArea = value.unpavedParkingArea
            this.sickHouseMeasures = value.sickHouseMeasures
            this.paintingWithWarranty = value.paintingWithWarranty
            this.paintingWarrantyDate = value.paintingWarrantyDate
            this.waterSupplyType = value.waterSupplyType
            this.waterMeterType = value.waterMeterType
            this.waterMeterShared = value.waterMeterShared
            this.waterMeterDirect = value.waterMeterDirect
            this.waterPipeDiameterMain = value.waterPipeDiameterMain
            this.waterPipeDiameterUnit = value.waterPipeDiameterUnit
            this.waterPipeDiameterBusiness = value.waterPipeDiameterBusiness
            this.waterBureauCompanyCd = value.waterBureauCompanyCd
            this.waterBureauBranchCd = value.waterBureauBranchCd
            this.electricMeterType = value.electricMeterType
            this.electricMeterShared = value.electricMeterShared
            this.electricMeterDirect = value.electricMeterDirect
            this.electricCompanyCd = value.electricCompanyCd
            this.electricBranchCd = value.electricBranchCd
            this.gasType = value.gasType
            this.gasMeterType = value.gasMeterType
            this.gasCompanyCd = value.gasCompanyCd
            this.gasBranchCd = value.gasBranchCd
            this.deleteDate = value.deleteDate
            resetChangedOnNotNull()
        }
    }
}
