package jp.ne.simplex.application.repository.mail

import jakarta.mail.internet.InternetAddress
import jp.ne.simplex.application.model.MailProperty
import jp.ne.simplex.application.repository.smtp.SmtpClient
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Repository

@Profile("dev")
@Repository
class SmtpMailRepository(
    @Value("\${mail.app.url}")
    private val appUrl: String,
    private val smtpClient: SmtpClient,
) : MailRepository {

    override fun sendMailUP(mailProperty: MailProperty) {
        if (mailProperty.fromAddress.value.isEmpty() || mailProperty.toList.isEmpty()) {
            throw Exception("メールの送信に失敗しました。")
        }

        val (subject, body) = createSubjectAndBodyFromFile(mailProperty)

        return smtpClient.send(
            subject = subject.ifEmpty { mailProperty.subject },
            text = buildString {
                append(body)
                if (mailProperty.url.isNotEmpty()) {
                    append("\n\n以下のアドレスをクリックしてください。\n$appUrl${mailProperty.url}")
                }
            },
            from = mailProperty.getFrom(),
            to = mailProperty.toList.map { InternetAddress(it.value) },
            cc = mailProperty.ccList.map { InternetAddress(it.value) },
        )
    }
}

