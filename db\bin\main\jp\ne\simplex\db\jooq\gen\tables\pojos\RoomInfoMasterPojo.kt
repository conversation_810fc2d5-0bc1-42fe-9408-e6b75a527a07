/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable
import java.math.BigDecimal


/**
 * 部屋情報マスタ 既存システム物理名: EMUR1P
 */
@Suppress("UNCHECKED_CAST")
data class RoomInfoMasterPojo(
    var recordType: String,
    var propertyCdType: String,
    var propertyCdPart1: String? = null,
    var propertyBuildingCd: String,
    var propertyCdPart2: String? = null,
    var propertyRoomCd: String,
    var deleteFlag: String? = null,
    var customerCompanyCd: String? = null,
    var customerBranchCd: String? = null,
    var customerDepartmentCd: String? = null,
    var customerCompletionFlag: String? = null,
    var prefectureCd: String? = null,
    var cityCd: String? = null,
    var lineCd: String? = null,
    var stationCd: String? = null,
    var rent: Int? = null,
    var layoutRoomCount: String? = null,
    var exclusiveArea: String? = null,
    var propertyType: String? = null,
    var preference_1: Byte? = null,
    var preference_2: Byte? = null,
    var preference_3: Byte? = null,
    var preference_4: Byte? = null,
    var preference_5: Byte? = null,
    var preference_6: Byte? = null,
    var preference_7: Byte? = null,
    var preference_8: Byte? = null,
    var preference_9: Byte? = null,
    var preference_10: Byte? = null,
    var preference_11: Byte? = null,
    var preference_12: Byte? = null,
    var preference_13: Byte? = null,
    var preference_14: Byte? = null,
    var preference_15: Byte? = null,
    var preference_16: Byte? = null,
    var preference_17: Byte? = null,
    var preference_18: Byte? = null,
    var preference_19: Byte? = null,
    var preference_20: Byte? = null,
    var preference_21: Byte? = null,
    var preference_22: Byte? = null,
    var preference_23: Byte? = null,
    var preference_24: Byte? = null,
    var preference_25: Byte? = null,
    var preference_26: Byte? = null,
    var preference_27: Byte? = null,
    var preference_28: Byte? = null,
    var preference_29: Byte? = null,
    var preference_30: Byte? = null,
    var preference_31: Byte? = null,
    var preference_32: Byte? = null,
    var preference_33: Byte? = null,
    var preference_34: Byte? = null,
    var preference_35: Byte? = null,
    var preference_36: Byte? = null,
    var preference_37: Byte? = null,
    var preference_38: Byte? = null,
    var preference_39: Byte? = null,
    var preference_40: Byte? = null,
    var preference_41: Byte? = null,
    var preference_42: Byte? = null,
    var preference_43: Byte? = null,
    var preference_44: Byte? = null,
    var preference_45: Byte? = null,
    var preference_46: Byte? = null,
    var preference_47: Byte? = null,
    var preference_48: Byte? = null,
    var preference_49: Byte? = null,
    var preference_50: Byte? = null,
    var preference_51: Byte? = null,
    var preference_52: Byte? = null,
    var preference_53: Byte? = null,
    var preference_54: Byte? = null,
    var preference_55: Byte? = null,
    var preference_56: Byte? = null,
    var preference_57: Byte? = null,
    var preference_58: Byte? = null,
    var preference_59: Byte? = null,
    var preference_60: Byte? = null,
    var preference_99: Byte? = null,
    var preferenceNewBuild: Byte? = null,
    var preferenceCornerRoom: Byte? = null,
    var preferenceAbove_2ndFloor: Byte? = null,
    var lineName: String? = null,
    var stationName: String? = null,
    var busStopName: String? = null,
    var busTime: Short? = null,
    var walkingTime: Short? = null,
    var distance: Short? = null,
    var keyMoney: String? = null,
    var deposit: String? = null,
    var neighborhoodAssociationFee: String? = null,
    var commonServiceFee: String? = null,
    var roomTypeName: String? = null,
    var layoutType: String? = null,
    var layout: String? = null,
    var layoutDetails: String? = null,
    var parkingType: String? = null,
    var parkingFee: String? = null,
    var constructionYearMonth: String? = null,
    var handlingStoreCompany: String? = null,
    var locationListingArea: String? = null,
    var floorNumber: String? = null,
    var direction: String? = null,
    var roomPosition: String? = null,
    var availableMoveInYearMonth: String? = null,
    var transportation: String? = null,
    var equipment: String? = null,
    var notes: String? = null,
    var inquiryBranchName: String? = null,
    var branchPhoneNumber: String? = null,
    var branchFaxNumber: String? = null,
    var transactionType: String? = null,
    var buildingName: String? = null,
    var structureName: String? = null,
    var agentAssignableType: String? = null,
    var subleaseType: String? = null,
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var creator: String? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updater: String? = null,
    var branchAddress: String? = null,
    var recommendationComment: String? = null,
    var completionYearMonth: Int? = null,
    var propertyPostalCode: String? = null,
    var vacateNoticeDate: Int? = null,
    var expectedMoveOutDate: Int? = null,
    var moveOutDate: Int? = null,
    var expectedCompletionDate: Int? = null,
    var availableMoveInDate: Int? = null,
    var moveInApplicationDate: Int? = null,
    var depositDate: Int? = null,
    var balanceCollectionDate: Int? = null,
    var moveInDate: Int? = null,
    var completionDate: Int? = null,
    var tenantRecruitmentCollectionDate: Int? = null,
    var tenant: String? = null,
    var owner: String? = null,
    var customerAgentBranchCd: String? = null,
    var customerAgentDepartmentCd: String? = null,
    var customerAgentEmployeeCd: String? = null,
    var rentTax: Int? = null,
    var keyMoneyTax: Int? = null,
    var keyMoneyTotal: Int? = null,
    var commonServiceFeeTax: Int? = null,
    var parkingFeeTax: Int? = null,
    var roomNumber: String? = null,
    var newExistingFlag: String? = null,
    var roomStatusType: String? = null,
    var recordStatusType: String? = null,
    var ffUsagePeriod: String? = null,
    var adPayableAmount: String? = null,
    var locationCity: String? = null,
    var tenantContractNumber: String? = null,
    var ownerContact: String? = null,
    var vacantPeriod: Short? = null,
    var floorArea_1f: BigDecimal? = null,
    var floorArea_2f: BigDecimal? = null,
    var floorArea_3f: BigDecimal? = null,
    var recruitmentCreationDate: Int? = null,
    var recruitmentApprovalDate: Int? = null,
    var moveOutInspectionDate: Int? = null,
    var restorationDate: Int? = null,
    var restorationCompletionDate: Int? = null,
    var vacantBookingDate: Int? = null,
    var vacantBookingCompletionDate: Int? = null,
    var additionalKeyMoney: Int? = null,
    var mutualAidJoinSign: Byte? = null,
    var rentalType: Byte? = null,
    var specialRentalType: String? = null,
    var distance2: BigDecimal? = null,
    var approvalType: Byte? = null,
    var recordSeparator: String? = null,
    var changeType: String? = null,
    var noDepositFlag: Byte? = null,
    var campaignTargetFlag: Byte? = null,
    var preference_61: Byte? = null,
    var preference_62: Byte? = null,
    var preference_63: Byte? = null,
    var preference_64: Byte? = null,
    var preference_65: Byte? = null,
    var preference_66: Byte? = null,
    var preference_67: Byte? = null,
    var preference_68: Byte? = null,
    var preference_69: Byte? = null,
    var preference_70: Byte? = null,
    var preference_71: Byte? = null,
    var preference_72: Byte? = null,
    var preference_73: Byte? = null,
    var preference_74: Byte? = null,
    var preference_75: Byte? = null,
    var preference_76: Byte? = null,
    var preference_77: Byte? = null,
    var preference_78: Byte? = null,
    var preference_79: Byte? = null,
    var preference_80: Byte? = null,
    var preference_81: Byte? = null,
    var preference_82: Byte? = null,
    var preference_83: Byte? = null,
    var preference_84: Byte? = null,
    var preference_85: Byte? = null,
    var preference_86: Byte? = null,
    var preference_87: Byte? = null,
    var preference_88: Byte? = null,
    var preference_89: Byte? = null,
    var preference_90: Byte? = null,
    var preference_91: Byte? = null,
    var preference_92: Byte? = null,
    var preference_93: Byte? = null,
    var preference_94: Byte? = null,
    var preference_95: Byte? = null,
    var preference_96: Byte? = null,
    var preference_97: Byte? = null,
    var preference_98: Byte? = null,
    var propertyAddress: String? = null,
    var propertyAddressDetail: String? = null,
    var serviceRoomSign: Byte? = null,
    var highVoltageBulkReceipt: Byte? = null,
    var highRentalSign: Byte? = null,
    var solarDiscountTarget: Byte? = null,
    var cleaningCostFixed: Byte? = null,
    var previousRent: Int? = null,
    var existingReviewUpdateDate: Int? = null,
    var moveOutInspectionTime: Int? = null,
    var recruitmentStartDate: Int? = null,
    var cleaningCostTotal: Int? = null,
    var discountInitialValueSign: Byte? = null,
    var flagReserve_7: Byte? = null,
    var petFlag: Byte? = null,
    var flagReserve_9: Byte? = null,
    var flagReserve_10: Byte? = null,
    var challengeStartDate: Int? = null,
    var challengeEndDate: Int? = null,
    var applicationEndDate: Int? = null,
    var moveInEndDate: Int? = null,
    var additionalReleaseDate: Int? = null,
    var recruitmentRent: Int? = null,
    var challengeAdditionalAmount: Int? = null,
    var reviewRent: Int? = null,
    var dateReserve_11: Int? = null,
    var dateReserve_12: Int? = null,
    var dateReserve_13: Int? = null,
    var amountReserve_1: Int? = null,
    var amountReserve_2: Int? = null,
    var amountReserve_3: Int? = null,
    var commonServiceFeeBase: Int? = null,
    var generalCableTvBase: Int? = null,
    var generalCableTvTax: Int? = null,
    var generalInternetBase: Int? = null,
    var generalInternetTax: Int? = null,
    var generalWaterQualityBase: Int? = null,
    var generalWaterQualityTax: Int? = null,
    var generalTenantWaterBase: Int? = null,
    var generalTenantWaterTax: Int? = null,
    var generalDrainUseBase: Int? = null,
    var generalDrainUseTax: Int? = null,
    var generalGarbageCollectionBase: Int? = null,
    var generalGarbageCollectionTax: Int? = null,
    var generalSharedAntennaBase: Int? = null,
    var generalSharedAntennaTax: Int? = null,
    var generalOwnerCleaningBase: Int? = null,
    var generalOwnerCleaningTax: Int? = null,
    var generalBuildingMaintenanceBase: Int? = null,
    var generalBuildingMaintenanceTax: Int? = null,
    var generalBuildingManagementBase: Int? = null,
    var generalBuildingManagementTax: Int? = null,
    var generalNeighborhoodAssocBase: Int? = null,
    var generalNeighborhoodAssocTax: Int? = null,
    var generalNeighborhoodOtherBase: Int? = null,
    var generalNeighborhoodOtherTax: Int? = null,
    var generalRepaymentAgentBase: Int? = null,
    var generalRepaymentAgentTax: Int? = null,
    var generalHlCommissionBase: Int? = null,
    var generalHlCommissionTax: Int? = null,
    var generalFurnishedBase: Int? = null,
    var generalFurnishedTax: Int? = null,
    var generalTenantDepositBase: Int? = null,
    var generalTenantDepositTax: Int? = null,
    var generalRentalBase: Int? = null,
    var generalRentalTax: Int? = null,
    var reserveAmount_1Base: Int? = null,
    var reserveAmount_1Tax: Int? = null,
    var reserveAmount_2Base: Int? = null,
    var reserveAmount_2Tax: Int? = null,
    var reserveAmount_3Base: Int? = null,
    var reserveAmount_3Tax: Int? = null,
    var flagReserve_11: Byte? = null,
    var flagReserve_12: Byte? = null,
    var bundleWater: Byte? = null,
    var bundleElectricity: Byte? = null,
    var bundleGas: Byte? = null,
    var category_2digitReserve_1: String? = null,
    var category_2digitReserve_2: String? = null,
    var category_2digitReserve_3: String? = null,
    var category_2digitReserve_4: String? = null,
    var category_2digitReserve_5: String? = null,
    var amountReserve_4: Int? = null,
    var amountReserve_5: Int? = null,
    var amountReserve_6: Int? = null,
    var amountReserve_7: Int? = null,
    var amountReserve_8: Int? = null,
    var dateReserve_14: Int? = null,
    var dateReserve_15: Int? = null,
    var dateReserve_16: Int? = null,
    var dateReserve_17: Int? = null,
    var dateReserve_18: Int? = null,
    var category_1digitReserve_1: String? = null,
    var category_1digitReserve_2: String? = null,
    var category_1digitReserve_3: String? = null,
    var category_1digitReserve_4: String? = null,
    var category_1digitReserve_5: String? = null,
    var leasingStoreCd: String? = null,
    var managementBranchCd: String? = null,
    var salesOfficeCd: String? = null,
    var screeningBranchCd: String? = null,
    var preference_100: Byte? = null,
    var preference_101: Byte? = null,
    var preference_102: Byte? = null,
    var preference_103: Byte? = null,
    var preference_104: Byte? = null,
    var preference_105: Byte? = null,
    var preference_106: Byte? = null,
    var preference_107: Byte? = null,
    var preference_108: Byte? = null,
    var preference_109: Byte? = null,
    var preference_110: Byte? = null,
    var preference_111: Byte? = null,
    var preference_112: Byte? = null,
    var preference_113: Byte? = null,
    var preference_114: Byte? = null,
    var preference_115: Byte? = null,
    var preference_116: Byte? = null,
    var preference_117: Byte? = null,
    var preference_118: Byte? = null,
    var preference_119: Byte? = null,
    var preference_120: Byte? = null,
    var preference_121: Byte? = null,
    var preference_122: Byte? = null,
    var preference_123: Byte? = null,
    var preference_124: Byte? = null,
    var preference_125: Byte? = null,
    var preference_126: Byte? = null,
    var preference_127: Byte? = null,
    var preference_128: Byte? = null,
    var preference_129: Byte? = null,
    var preference_130: Byte? = null,
    var preference_131: Byte? = null,
    var preference_132: Byte? = null,
    var preference_133: Byte? = null,
    var preference_134: Byte? = null,
    var preference_135: Byte? = null,
    var preference_136: Byte? = null,
    var preference_137: Byte? = null,
    var preference_138: Byte? = null,
    var preference_139: Byte? = null,
    var preference_140: Byte? = null,
    var preference_141: Byte? = null,
    var preference_142: Byte? = null,
    var preference_143: Byte? = null,
    var preference_144: Byte? = null,
    var preference_145: Byte? = null,
    var preference_146: Byte? = null,
    var preference_147: Byte? = null,
    var preference_148: Byte? = null,
    var preference_149: Byte? = null,
    var preference_150: Byte? = null,
    var preference_151: Byte? = null,
    var preference_152: Byte? = null,
    var preference_153: Byte? = null,
    var preference_154: Byte? = null,
    var preference_155: Byte? = null,
    var preference_156: Byte? = null,
    var preference_157: Byte? = null,
    var preference_158: Byte? = null,
    var preference_159: Byte? = null,
    var preference_160: Byte? = null,
    var preference_161: Byte? = null,
    var preference_162: Byte? = null,
    var preference_163: Byte? = null,
    var preference_164: Byte? = null,
    var preference_165: Byte? = null,
    var preference_166: Byte? = null,
    var preference_167: Byte? = null,
    var preference_168: Byte? = null,
    var preference_169: Byte? = null,
    var preference_170: Byte? = null,
    var preference_171: Byte? = null,
    var preference_172: Byte? = null,
    var preference_173: Byte? = null,
    var preference_174: Byte? = null,
    var preference_175: Byte? = null,
    var preference_176: Byte? = null,
    var preference_177: Byte? = null,
    var preference_178: Byte? = null,
    var preference_179: Byte? = null,
    var preference_180: Byte? = null,
    var preference_181: Byte? = null,
    var preference_182: Byte? = null,
    var preference_183: Byte? = null,
    var preference_184: Byte? = null,
    var preference_185: Byte? = null,
    var preference_186: Byte? = null,
    var preference_187: Byte? = null,
    var preference_188: Byte? = null,
    var preference_189: Byte? = null,
    var preference_190: Byte? = null,
    var preference_191: Byte? = null,
    var preference_192: Byte? = null,
    var preference_193: Byte? = null,
    var preference_194: Byte? = null,
    var preference_195: Byte? = null,
    var preference_196: Byte? = null,
    var preference_197: Byte? = null,
    var preference_198: Byte? = null,
    var preference_199: Byte? = null,
    var marketingBranchOfficeCd: String? = null,
    var propertySituation: String? = null,
    var prefectureCityCd: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: RoomInfoMasterPojo = other as RoomInfoMasterPojo
        if (this.recordType != o.recordType)
            return false
        if (this.propertyCdType != o.propertyCdType)
            return false
        if (this.propertyCdPart1 == null) {
            if (o.propertyCdPart1 != null)
                return false
        }
        else if (this.propertyCdPart1 != o.propertyCdPart1)
            return false
        if (this.propertyBuildingCd != o.propertyBuildingCd)
            return false
        if (this.propertyCdPart2 == null) {
            if (o.propertyCdPart2 != null)
                return false
        }
        else if (this.propertyCdPart2 != o.propertyCdPart2)
            return false
        if (this.propertyRoomCd != o.propertyRoomCd)
            return false
        if (this.deleteFlag == null) {
            if (o.deleteFlag != null)
                return false
        }
        else if (this.deleteFlag != o.deleteFlag)
            return false
        if (this.customerCompanyCd == null) {
            if (o.customerCompanyCd != null)
                return false
        }
        else if (this.customerCompanyCd != o.customerCompanyCd)
            return false
        if (this.customerBranchCd == null) {
            if (o.customerBranchCd != null)
                return false
        }
        else if (this.customerBranchCd != o.customerBranchCd)
            return false
        if (this.customerDepartmentCd == null) {
            if (o.customerDepartmentCd != null)
                return false
        }
        else if (this.customerDepartmentCd != o.customerDepartmentCd)
            return false
        if (this.customerCompletionFlag == null) {
            if (o.customerCompletionFlag != null)
                return false
        }
        else if (this.customerCompletionFlag != o.customerCompletionFlag)
            return false
        if (this.prefectureCd == null) {
            if (o.prefectureCd != null)
                return false
        }
        else if (this.prefectureCd != o.prefectureCd)
            return false
        if (this.cityCd == null) {
            if (o.cityCd != null)
                return false
        }
        else if (this.cityCd != o.cityCd)
            return false
        if (this.lineCd == null) {
            if (o.lineCd != null)
                return false
        }
        else if (this.lineCd != o.lineCd)
            return false
        if (this.stationCd == null) {
            if (o.stationCd != null)
                return false
        }
        else if (this.stationCd != o.stationCd)
            return false
        if (this.rent == null) {
            if (o.rent != null)
                return false
        }
        else if (this.rent != o.rent)
            return false
        if (this.layoutRoomCount == null) {
            if (o.layoutRoomCount != null)
                return false
        }
        else if (this.layoutRoomCount != o.layoutRoomCount)
            return false
        if (this.exclusiveArea == null) {
            if (o.exclusiveArea != null)
                return false
        }
        else if (this.exclusiveArea != o.exclusiveArea)
            return false
        if (this.propertyType == null) {
            if (o.propertyType != null)
                return false
        }
        else if (this.propertyType != o.propertyType)
            return false
        if (this.preference_1 == null) {
            if (o.preference_1 != null)
                return false
        }
        else if (this.preference_1 != o.preference_1)
            return false
        if (this.preference_2 == null) {
            if (o.preference_2 != null)
                return false
        }
        else if (this.preference_2 != o.preference_2)
            return false
        if (this.preference_3 == null) {
            if (o.preference_3 != null)
                return false
        }
        else if (this.preference_3 != o.preference_3)
            return false
        if (this.preference_4 == null) {
            if (o.preference_4 != null)
                return false
        }
        else if (this.preference_4 != o.preference_4)
            return false
        if (this.preference_5 == null) {
            if (o.preference_5 != null)
                return false
        }
        else if (this.preference_5 != o.preference_5)
            return false
        if (this.preference_6 == null) {
            if (o.preference_6 != null)
                return false
        }
        else if (this.preference_6 != o.preference_6)
            return false
        if (this.preference_7 == null) {
            if (o.preference_7 != null)
                return false
        }
        else if (this.preference_7 != o.preference_7)
            return false
        if (this.preference_8 == null) {
            if (o.preference_8 != null)
                return false
        }
        else if (this.preference_8 != o.preference_8)
            return false
        if (this.preference_9 == null) {
            if (o.preference_9 != null)
                return false
        }
        else if (this.preference_9 != o.preference_9)
            return false
        if (this.preference_10 == null) {
            if (o.preference_10 != null)
                return false
        }
        else if (this.preference_10 != o.preference_10)
            return false
        if (this.preference_11 == null) {
            if (o.preference_11 != null)
                return false
        }
        else if (this.preference_11 != o.preference_11)
            return false
        if (this.preference_12 == null) {
            if (o.preference_12 != null)
                return false
        }
        else if (this.preference_12 != o.preference_12)
            return false
        if (this.preference_13 == null) {
            if (o.preference_13 != null)
                return false
        }
        else if (this.preference_13 != o.preference_13)
            return false
        if (this.preference_14 == null) {
            if (o.preference_14 != null)
                return false
        }
        else if (this.preference_14 != o.preference_14)
            return false
        if (this.preference_15 == null) {
            if (o.preference_15 != null)
                return false
        }
        else if (this.preference_15 != o.preference_15)
            return false
        if (this.preference_16 == null) {
            if (o.preference_16 != null)
                return false
        }
        else if (this.preference_16 != o.preference_16)
            return false
        if (this.preference_17 == null) {
            if (o.preference_17 != null)
                return false
        }
        else if (this.preference_17 != o.preference_17)
            return false
        if (this.preference_18 == null) {
            if (o.preference_18 != null)
                return false
        }
        else if (this.preference_18 != o.preference_18)
            return false
        if (this.preference_19 == null) {
            if (o.preference_19 != null)
                return false
        }
        else if (this.preference_19 != o.preference_19)
            return false
        if (this.preference_20 == null) {
            if (o.preference_20 != null)
                return false
        }
        else if (this.preference_20 != o.preference_20)
            return false
        if (this.preference_21 == null) {
            if (o.preference_21 != null)
                return false
        }
        else if (this.preference_21 != o.preference_21)
            return false
        if (this.preference_22 == null) {
            if (o.preference_22 != null)
                return false
        }
        else if (this.preference_22 != o.preference_22)
            return false
        if (this.preference_23 == null) {
            if (o.preference_23 != null)
                return false
        }
        else if (this.preference_23 != o.preference_23)
            return false
        if (this.preference_24 == null) {
            if (o.preference_24 != null)
                return false
        }
        else if (this.preference_24 != o.preference_24)
            return false
        if (this.preference_25 == null) {
            if (o.preference_25 != null)
                return false
        }
        else if (this.preference_25 != o.preference_25)
            return false
        if (this.preference_26 == null) {
            if (o.preference_26 != null)
                return false
        }
        else if (this.preference_26 != o.preference_26)
            return false
        if (this.preference_27 == null) {
            if (o.preference_27 != null)
                return false
        }
        else if (this.preference_27 != o.preference_27)
            return false
        if (this.preference_28 == null) {
            if (o.preference_28 != null)
                return false
        }
        else if (this.preference_28 != o.preference_28)
            return false
        if (this.preference_29 == null) {
            if (o.preference_29 != null)
                return false
        }
        else if (this.preference_29 != o.preference_29)
            return false
        if (this.preference_30 == null) {
            if (o.preference_30 != null)
                return false
        }
        else if (this.preference_30 != o.preference_30)
            return false
        if (this.preference_31 == null) {
            if (o.preference_31 != null)
                return false
        }
        else if (this.preference_31 != o.preference_31)
            return false
        if (this.preference_32 == null) {
            if (o.preference_32 != null)
                return false
        }
        else if (this.preference_32 != o.preference_32)
            return false
        if (this.preference_33 == null) {
            if (o.preference_33 != null)
                return false
        }
        else if (this.preference_33 != o.preference_33)
            return false
        if (this.preference_34 == null) {
            if (o.preference_34 != null)
                return false
        }
        else if (this.preference_34 != o.preference_34)
            return false
        if (this.preference_35 == null) {
            if (o.preference_35 != null)
                return false
        }
        else if (this.preference_35 != o.preference_35)
            return false
        if (this.preference_36 == null) {
            if (o.preference_36 != null)
                return false
        }
        else if (this.preference_36 != o.preference_36)
            return false
        if (this.preference_37 == null) {
            if (o.preference_37 != null)
                return false
        }
        else if (this.preference_37 != o.preference_37)
            return false
        if (this.preference_38 == null) {
            if (o.preference_38 != null)
                return false
        }
        else if (this.preference_38 != o.preference_38)
            return false
        if (this.preference_39 == null) {
            if (o.preference_39 != null)
                return false
        }
        else if (this.preference_39 != o.preference_39)
            return false
        if (this.preference_40 == null) {
            if (o.preference_40 != null)
                return false
        }
        else if (this.preference_40 != o.preference_40)
            return false
        if (this.preference_41 == null) {
            if (o.preference_41 != null)
                return false
        }
        else if (this.preference_41 != o.preference_41)
            return false
        if (this.preference_42 == null) {
            if (o.preference_42 != null)
                return false
        }
        else if (this.preference_42 != o.preference_42)
            return false
        if (this.preference_43 == null) {
            if (o.preference_43 != null)
                return false
        }
        else if (this.preference_43 != o.preference_43)
            return false
        if (this.preference_44 == null) {
            if (o.preference_44 != null)
                return false
        }
        else if (this.preference_44 != o.preference_44)
            return false
        if (this.preference_45 == null) {
            if (o.preference_45 != null)
                return false
        }
        else if (this.preference_45 != o.preference_45)
            return false
        if (this.preference_46 == null) {
            if (o.preference_46 != null)
                return false
        }
        else if (this.preference_46 != o.preference_46)
            return false
        if (this.preference_47 == null) {
            if (o.preference_47 != null)
                return false
        }
        else if (this.preference_47 != o.preference_47)
            return false
        if (this.preference_48 == null) {
            if (o.preference_48 != null)
                return false
        }
        else if (this.preference_48 != o.preference_48)
            return false
        if (this.preference_49 == null) {
            if (o.preference_49 != null)
                return false
        }
        else if (this.preference_49 != o.preference_49)
            return false
        if (this.preference_50 == null) {
            if (o.preference_50 != null)
                return false
        }
        else if (this.preference_50 != o.preference_50)
            return false
        if (this.preference_51 == null) {
            if (o.preference_51 != null)
                return false
        }
        else if (this.preference_51 != o.preference_51)
            return false
        if (this.preference_52 == null) {
            if (o.preference_52 != null)
                return false
        }
        else if (this.preference_52 != o.preference_52)
            return false
        if (this.preference_53 == null) {
            if (o.preference_53 != null)
                return false
        }
        else if (this.preference_53 != o.preference_53)
            return false
        if (this.preference_54 == null) {
            if (o.preference_54 != null)
                return false
        }
        else if (this.preference_54 != o.preference_54)
            return false
        if (this.preference_55 == null) {
            if (o.preference_55 != null)
                return false
        }
        else if (this.preference_55 != o.preference_55)
            return false
        if (this.preference_56 == null) {
            if (o.preference_56 != null)
                return false
        }
        else if (this.preference_56 != o.preference_56)
            return false
        if (this.preference_57 == null) {
            if (o.preference_57 != null)
                return false
        }
        else if (this.preference_57 != o.preference_57)
            return false
        if (this.preference_58 == null) {
            if (o.preference_58 != null)
                return false
        }
        else if (this.preference_58 != o.preference_58)
            return false
        if (this.preference_59 == null) {
            if (o.preference_59 != null)
                return false
        }
        else if (this.preference_59 != o.preference_59)
            return false
        if (this.preference_60 == null) {
            if (o.preference_60 != null)
                return false
        }
        else if (this.preference_60 != o.preference_60)
            return false
        if (this.preference_99 == null) {
            if (o.preference_99 != null)
                return false
        }
        else if (this.preference_99 != o.preference_99)
            return false
        if (this.preferenceNewBuild == null) {
            if (o.preferenceNewBuild != null)
                return false
        }
        else if (this.preferenceNewBuild != o.preferenceNewBuild)
            return false
        if (this.preferenceCornerRoom == null) {
            if (o.preferenceCornerRoom != null)
                return false
        }
        else if (this.preferenceCornerRoom != o.preferenceCornerRoom)
            return false
        if (this.preferenceAbove_2ndFloor == null) {
            if (o.preferenceAbove_2ndFloor != null)
                return false
        }
        else if (this.preferenceAbove_2ndFloor != o.preferenceAbove_2ndFloor)
            return false
        if (this.lineName == null) {
            if (o.lineName != null)
                return false
        }
        else if (this.lineName != o.lineName)
            return false
        if (this.stationName == null) {
            if (o.stationName != null)
                return false
        }
        else if (this.stationName != o.stationName)
            return false
        if (this.busStopName == null) {
            if (o.busStopName != null)
                return false
        }
        else if (this.busStopName != o.busStopName)
            return false
        if (this.busTime == null) {
            if (o.busTime != null)
                return false
        }
        else if (this.busTime != o.busTime)
            return false
        if (this.walkingTime == null) {
            if (o.walkingTime != null)
                return false
        }
        else if (this.walkingTime != o.walkingTime)
            return false
        if (this.distance == null) {
            if (o.distance != null)
                return false
        }
        else if (this.distance != o.distance)
            return false
        if (this.keyMoney == null) {
            if (o.keyMoney != null)
                return false
        }
        else if (this.keyMoney != o.keyMoney)
            return false
        if (this.deposit == null) {
            if (o.deposit != null)
                return false
        }
        else if (this.deposit != o.deposit)
            return false
        if (this.neighborhoodAssociationFee == null) {
            if (o.neighborhoodAssociationFee != null)
                return false
        }
        else if (this.neighborhoodAssociationFee != o.neighborhoodAssociationFee)
            return false
        if (this.commonServiceFee == null) {
            if (o.commonServiceFee != null)
                return false
        }
        else if (this.commonServiceFee != o.commonServiceFee)
            return false
        if (this.roomTypeName == null) {
            if (o.roomTypeName != null)
                return false
        }
        else if (this.roomTypeName != o.roomTypeName)
            return false
        if (this.layoutType == null) {
            if (o.layoutType != null)
                return false
        }
        else if (this.layoutType != o.layoutType)
            return false
        if (this.layout == null) {
            if (o.layout != null)
                return false
        }
        else if (this.layout != o.layout)
            return false
        if (this.layoutDetails == null) {
            if (o.layoutDetails != null)
                return false
        }
        else if (this.layoutDetails != o.layoutDetails)
            return false
        if (this.parkingType == null) {
            if (o.parkingType != null)
                return false
        }
        else if (this.parkingType != o.parkingType)
            return false
        if (this.parkingFee == null) {
            if (o.parkingFee != null)
                return false
        }
        else if (this.parkingFee != o.parkingFee)
            return false
        if (this.constructionYearMonth == null) {
            if (o.constructionYearMonth != null)
                return false
        }
        else if (this.constructionYearMonth != o.constructionYearMonth)
            return false
        if (this.handlingStoreCompany == null) {
            if (o.handlingStoreCompany != null)
                return false
        }
        else if (this.handlingStoreCompany != o.handlingStoreCompany)
            return false
        if (this.locationListingArea == null) {
            if (o.locationListingArea != null)
                return false
        }
        else if (this.locationListingArea != o.locationListingArea)
            return false
        if (this.floorNumber == null) {
            if (o.floorNumber != null)
                return false
        }
        else if (this.floorNumber != o.floorNumber)
            return false
        if (this.direction == null) {
            if (o.direction != null)
                return false
        }
        else if (this.direction != o.direction)
            return false
        if (this.roomPosition == null) {
            if (o.roomPosition != null)
                return false
        }
        else if (this.roomPosition != o.roomPosition)
            return false
        if (this.availableMoveInYearMonth == null) {
            if (o.availableMoveInYearMonth != null)
                return false
        }
        else if (this.availableMoveInYearMonth != o.availableMoveInYearMonth)
            return false
        if (this.transportation == null) {
            if (o.transportation != null)
                return false
        }
        else if (this.transportation != o.transportation)
            return false
        if (this.equipment == null) {
            if (o.equipment != null)
                return false
        }
        else if (this.equipment != o.equipment)
            return false
        if (this.notes == null) {
            if (o.notes != null)
                return false
        }
        else if (this.notes != o.notes)
            return false
        if (this.inquiryBranchName == null) {
            if (o.inquiryBranchName != null)
                return false
        }
        else if (this.inquiryBranchName != o.inquiryBranchName)
            return false
        if (this.branchPhoneNumber == null) {
            if (o.branchPhoneNumber != null)
                return false
        }
        else if (this.branchPhoneNumber != o.branchPhoneNumber)
            return false
        if (this.branchFaxNumber == null) {
            if (o.branchFaxNumber != null)
                return false
        }
        else if (this.branchFaxNumber != o.branchFaxNumber)
            return false
        if (this.transactionType == null) {
            if (o.transactionType != null)
                return false
        }
        else if (this.transactionType != o.transactionType)
            return false
        if (this.buildingName == null) {
            if (o.buildingName != null)
                return false
        }
        else if (this.buildingName != o.buildingName)
            return false
        if (this.structureName == null) {
            if (o.structureName != null)
                return false
        }
        else if (this.structureName != o.structureName)
            return false
        if (this.agentAssignableType == null) {
            if (o.agentAssignableType != null)
                return false
        }
        else if (this.agentAssignableType != o.agentAssignableType)
            return false
        if (this.subleaseType == null) {
            if (o.subleaseType != null)
                return false
        }
        else if (this.subleaseType != o.subleaseType)
            return false
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.creator == null) {
            if (o.creator != null)
                return false
        }
        else if (this.creator != o.creator)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.branchAddress == null) {
            if (o.branchAddress != null)
                return false
        }
        else if (this.branchAddress != o.branchAddress)
            return false
        if (this.recommendationComment == null) {
            if (o.recommendationComment != null)
                return false
        }
        else if (this.recommendationComment != o.recommendationComment)
            return false
        if (this.completionYearMonth == null) {
            if (o.completionYearMonth != null)
                return false
        }
        else if (this.completionYearMonth != o.completionYearMonth)
            return false
        if (this.propertyPostalCode == null) {
            if (o.propertyPostalCode != null)
                return false
        }
        else if (this.propertyPostalCode != o.propertyPostalCode)
            return false
        if (this.vacateNoticeDate == null) {
            if (o.vacateNoticeDate != null)
                return false
        }
        else if (this.vacateNoticeDate != o.vacateNoticeDate)
            return false
        if (this.expectedMoveOutDate == null) {
            if (o.expectedMoveOutDate != null)
                return false
        }
        else if (this.expectedMoveOutDate != o.expectedMoveOutDate)
            return false
        if (this.moveOutDate == null) {
            if (o.moveOutDate != null)
                return false
        }
        else if (this.moveOutDate != o.moveOutDate)
            return false
        if (this.expectedCompletionDate == null) {
            if (o.expectedCompletionDate != null)
                return false
        }
        else if (this.expectedCompletionDate != o.expectedCompletionDate)
            return false
        if (this.availableMoveInDate == null) {
            if (o.availableMoveInDate != null)
                return false
        }
        else if (this.availableMoveInDate != o.availableMoveInDate)
            return false
        if (this.moveInApplicationDate == null) {
            if (o.moveInApplicationDate != null)
                return false
        }
        else if (this.moveInApplicationDate != o.moveInApplicationDate)
            return false
        if (this.depositDate == null) {
            if (o.depositDate != null)
                return false
        }
        else if (this.depositDate != o.depositDate)
            return false
        if (this.balanceCollectionDate == null) {
            if (o.balanceCollectionDate != null)
                return false
        }
        else if (this.balanceCollectionDate != o.balanceCollectionDate)
            return false
        if (this.moveInDate == null) {
            if (o.moveInDate != null)
                return false
        }
        else if (this.moveInDate != o.moveInDate)
            return false
        if (this.completionDate == null) {
            if (o.completionDate != null)
                return false
        }
        else if (this.completionDate != o.completionDate)
            return false
        if (this.tenantRecruitmentCollectionDate == null) {
            if (o.tenantRecruitmentCollectionDate != null)
                return false
        }
        else if (this.tenantRecruitmentCollectionDate != o.tenantRecruitmentCollectionDate)
            return false
        if (this.tenant == null) {
            if (o.tenant != null)
                return false
        }
        else if (this.tenant != o.tenant)
            return false
        if (this.owner == null) {
            if (o.owner != null)
                return false
        }
        else if (this.owner != o.owner)
            return false
        if (this.customerAgentBranchCd == null) {
            if (o.customerAgentBranchCd != null)
                return false
        }
        else if (this.customerAgentBranchCd != o.customerAgentBranchCd)
            return false
        if (this.customerAgentDepartmentCd == null) {
            if (o.customerAgentDepartmentCd != null)
                return false
        }
        else if (this.customerAgentDepartmentCd != o.customerAgentDepartmentCd)
            return false
        if (this.customerAgentEmployeeCd == null) {
            if (o.customerAgentEmployeeCd != null)
                return false
        }
        else if (this.customerAgentEmployeeCd != o.customerAgentEmployeeCd)
            return false
        if (this.rentTax == null) {
            if (o.rentTax != null)
                return false
        }
        else if (this.rentTax != o.rentTax)
            return false
        if (this.keyMoneyTax == null) {
            if (o.keyMoneyTax != null)
                return false
        }
        else if (this.keyMoneyTax != o.keyMoneyTax)
            return false
        if (this.keyMoneyTotal == null) {
            if (o.keyMoneyTotal != null)
                return false
        }
        else if (this.keyMoneyTotal != o.keyMoneyTotal)
            return false
        if (this.commonServiceFeeTax == null) {
            if (o.commonServiceFeeTax != null)
                return false
        }
        else if (this.commonServiceFeeTax != o.commonServiceFeeTax)
            return false
        if (this.parkingFeeTax == null) {
            if (o.parkingFeeTax != null)
                return false
        }
        else if (this.parkingFeeTax != o.parkingFeeTax)
            return false
        if (this.roomNumber == null) {
            if (o.roomNumber != null)
                return false
        }
        else if (this.roomNumber != o.roomNumber)
            return false
        if (this.newExistingFlag == null) {
            if (o.newExistingFlag != null)
                return false
        }
        else if (this.newExistingFlag != o.newExistingFlag)
            return false
        if (this.roomStatusType == null) {
            if (o.roomStatusType != null)
                return false
        }
        else if (this.roomStatusType != o.roomStatusType)
            return false
        if (this.recordStatusType == null) {
            if (o.recordStatusType != null)
                return false
        }
        else if (this.recordStatusType != o.recordStatusType)
            return false
        if (this.ffUsagePeriod == null) {
            if (o.ffUsagePeriod != null)
                return false
        }
        else if (this.ffUsagePeriod != o.ffUsagePeriod)
            return false
        if (this.adPayableAmount == null) {
            if (o.adPayableAmount != null)
                return false
        }
        else if (this.adPayableAmount != o.adPayableAmount)
            return false
        if (this.locationCity == null) {
            if (o.locationCity != null)
                return false
        }
        else if (this.locationCity != o.locationCity)
            return false
        if (this.tenantContractNumber == null) {
            if (o.tenantContractNumber != null)
                return false
        }
        else if (this.tenantContractNumber != o.tenantContractNumber)
            return false
        if (this.ownerContact == null) {
            if (o.ownerContact != null)
                return false
        }
        else if (this.ownerContact != o.ownerContact)
            return false
        if (this.vacantPeriod == null) {
            if (o.vacantPeriod != null)
                return false
        }
        else if (this.vacantPeriod != o.vacantPeriod)
            return false
        if (this.floorArea_1f == null) {
            if (o.floorArea_1f != null)
                return false
        }
        else if (this.floorArea_1f != o.floorArea_1f)
            return false
        if (this.floorArea_2f == null) {
            if (o.floorArea_2f != null)
                return false
        }
        else if (this.floorArea_2f != o.floorArea_2f)
            return false
        if (this.floorArea_3f == null) {
            if (o.floorArea_3f != null)
                return false
        }
        else if (this.floorArea_3f != o.floorArea_3f)
            return false
        if (this.recruitmentCreationDate == null) {
            if (o.recruitmentCreationDate != null)
                return false
        }
        else if (this.recruitmentCreationDate != o.recruitmentCreationDate)
            return false
        if (this.recruitmentApprovalDate == null) {
            if (o.recruitmentApprovalDate != null)
                return false
        }
        else if (this.recruitmentApprovalDate != o.recruitmentApprovalDate)
            return false
        if (this.moveOutInspectionDate == null) {
            if (o.moveOutInspectionDate != null)
                return false
        }
        else if (this.moveOutInspectionDate != o.moveOutInspectionDate)
            return false
        if (this.restorationDate == null) {
            if (o.restorationDate != null)
                return false
        }
        else if (this.restorationDate != o.restorationDate)
            return false
        if (this.restorationCompletionDate == null) {
            if (o.restorationCompletionDate != null)
                return false
        }
        else if (this.restorationCompletionDate != o.restorationCompletionDate)
            return false
        if (this.vacantBookingDate == null) {
            if (o.vacantBookingDate != null)
                return false
        }
        else if (this.vacantBookingDate != o.vacantBookingDate)
            return false
        if (this.vacantBookingCompletionDate == null) {
            if (o.vacantBookingCompletionDate != null)
                return false
        }
        else if (this.vacantBookingCompletionDate != o.vacantBookingCompletionDate)
            return false
        if (this.additionalKeyMoney == null) {
            if (o.additionalKeyMoney != null)
                return false
        }
        else if (this.additionalKeyMoney != o.additionalKeyMoney)
            return false
        if (this.mutualAidJoinSign == null) {
            if (o.mutualAidJoinSign != null)
                return false
        }
        else if (this.mutualAidJoinSign != o.mutualAidJoinSign)
            return false
        if (this.rentalType == null) {
            if (o.rentalType != null)
                return false
        }
        else if (this.rentalType != o.rentalType)
            return false
        if (this.specialRentalType == null) {
            if (o.specialRentalType != null)
                return false
        }
        else if (this.specialRentalType != o.specialRentalType)
            return false
        if (this.distance2 == null) {
            if (o.distance2 != null)
                return false
        }
        else if (this.distance2 != o.distance2)
            return false
        if (this.approvalType == null) {
            if (o.approvalType != null)
                return false
        }
        else if (this.approvalType != o.approvalType)
            return false
        if (this.recordSeparator == null) {
            if (o.recordSeparator != null)
                return false
        }
        else if (this.recordSeparator != o.recordSeparator)
            return false
        if (this.changeType == null) {
            if (o.changeType != null)
                return false
        }
        else if (this.changeType != o.changeType)
            return false
        if (this.noDepositFlag == null) {
            if (o.noDepositFlag != null)
                return false
        }
        else if (this.noDepositFlag != o.noDepositFlag)
            return false
        if (this.campaignTargetFlag == null) {
            if (o.campaignTargetFlag != null)
                return false
        }
        else if (this.campaignTargetFlag != o.campaignTargetFlag)
            return false
        if (this.preference_61 == null) {
            if (o.preference_61 != null)
                return false
        }
        else if (this.preference_61 != o.preference_61)
            return false
        if (this.preference_62 == null) {
            if (o.preference_62 != null)
                return false
        }
        else if (this.preference_62 != o.preference_62)
            return false
        if (this.preference_63 == null) {
            if (o.preference_63 != null)
                return false
        }
        else if (this.preference_63 != o.preference_63)
            return false
        if (this.preference_64 == null) {
            if (o.preference_64 != null)
                return false
        }
        else if (this.preference_64 != o.preference_64)
            return false
        if (this.preference_65 == null) {
            if (o.preference_65 != null)
                return false
        }
        else if (this.preference_65 != o.preference_65)
            return false
        if (this.preference_66 == null) {
            if (o.preference_66 != null)
                return false
        }
        else if (this.preference_66 != o.preference_66)
            return false
        if (this.preference_67 == null) {
            if (o.preference_67 != null)
                return false
        }
        else if (this.preference_67 != o.preference_67)
            return false
        if (this.preference_68 == null) {
            if (o.preference_68 != null)
                return false
        }
        else if (this.preference_68 != o.preference_68)
            return false
        if (this.preference_69 == null) {
            if (o.preference_69 != null)
                return false
        }
        else if (this.preference_69 != o.preference_69)
            return false
        if (this.preference_70 == null) {
            if (o.preference_70 != null)
                return false
        }
        else if (this.preference_70 != o.preference_70)
            return false
        if (this.preference_71 == null) {
            if (o.preference_71 != null)
                return false
        }
        else if (this.preference_71 != o.preference_71)
            return false
        if (this.preference_72 == null) {
            if (o.preference_72 != null)
                return false
        }
        else if (this.preference_72 != o.preference_72)
            return false
        if (this.preference_73 == null) {
            if (o.preference_73 != null)
                return false
        }
        else if (this.preference_73 != o.preference_73)
            return false
        if (this.preference_74 == null) {
            if (o.preference_74 != null)
                return false
        }
        else if (this.preference_74 != o.preference_74)
            return false
        if (this.preference_75 == null) {
            if (o.preference_75 != null)
                return false
        }
        else if (this.preference_75 != o.preference_75)
            return false
        if (this.preference_76 == null) {
            if (o.preference_76 != null)
                return false
        }
        else if (this.preference_76 != o.preference_76)
            return false
        if (this.preference_77 == null) {
            if (o.preference_77 != null)
                return false
        }
        else if (this.preference_77 != o.preference_77)
            return false
        if (this.preference_78 == null) {
            if (o.preference_78 != null)
                return false
        }
        else if (this.preference_78 != o.preference_78)
            return false
        if (this.preference_79 == null) {
            if (o.preference_79 != null)
                return false
        }
        else if (this.preference_79 != o.preference_79)
            return false
        if (this.preference_80 == null) {
            if (o.preference_80 != null)
                return false
        }
        else if (this.preference_80 != o.preference_80)
            return false
        if (this.preference_81 == null) {
            if (o.preference_81 != null)
                return false
        }
        else if (this.preference_81 != o.preference_81)
            return false
        if (this.preference_82 == null) {
            if (o.preference_82 != null)
                return false
        }
        else if (this.preference_82 != o.preference_82)
            return false
        if (this.preference_83 == null) {
            if (o.preference_83 != null)
                return false
        }
        else if (this.preference_83 != o.preference_83)
            return false
        if (this.preference_84 == null) {
            if (o.preference_84 != null)
                return false
        }
        else if (this.preference_84 != o.preference_84)
            return false
        if (this.preference_85 == null) {
            if (o.preference_85 != null)
                return false
        }
        else if (this.preference_85 != o.preference_85)
            return false
        if (this.preference_86 == null) {
            if (o.preference_86 != null)
                return false
        }
        else if (this.preference_86 != o.preference_86)
            return false
        if (this.preference_87 == null) {
            if (o.preference_87 != null)
                return false
        }
        else if (this.preference_87 != o.preference_87)
            return false
        if (this.preference_88 == null) {
            if (o.preference_88 != null)
                return false
        }
        else if (this.preference_88 != o.preference_88)
            return false
        if (this.preference_89 == null) {
            if (o.preference_89 != null)
                return false
        }
        else if (this.preference_89 != o.preference_89)
            return false
        if (this.preference_90 == null) {
            if (o.preference_90 != null)
                return false
        }
        else if (this.preference_90 != o.preference_90)
            return false
        if (this.preference_91 == null) {
            if (o.preference_91 != null)
                return false
        }
        else if (this.preference_91 != o.preference_91)
            return false
        if (this.preference_92 == null) {
            if (o.preference_92 != null)
                return false
        }
        else if (this.preference_92 != o.preference_92)
            return false
        if (this.preference_93 == null) {
            if (o.preference_93 != null)
                return false
        }
        else if (this.preference_93 != o.preference_93)
            return false
        if (this.preference_94 == null) {
            if (o.preference_94 != null)
                return false
        }
        else if (this.preference_94 != o.preference_94)
            return false
        if (this.preference_95 == null) {
            if (o.preference_95 != null)
                return false
        }
        else if (this.preference_95 != o.preference_95)
            return false
        if (this.preference_96 == null) {
            if (o.preference_96 != null)
                return false
        }
        else if (this.preference_96 != o.preference_96)
            return false
        if (this.preference_97 == null) {
            if (o.preference_97 != null)
                return false
        }
        else if (this.preference_97 != o.preference_97)
            return false
        if (this.preference_98 == null) {
            if (o.preference_98 != null)
                return false
        }
        else if (this.preference_98 != o.preference_98)
            return false
        if (this.propertyAddress == null) {
            if (o.propertyAddress != null)
                return false
        }
        else if (this.propertyAddress != o.propertyAddress)
            return false
        if (this.propertyAddressDetail == null) {
            if (o.propertyAddressDetail != null)
                return false
        }
        else if (this.propertyAddressDetail != o.propertyAddressDetail)
            return false
        if (this.serviceRoomSign == null) {
            if (o.serviceRoomSign != null)
                return false
        }
        else if (this.serviceRoomSign != o.serviceRoomSign)
            return false
        if (this.highVoltageBulkReceipt == null) {
            if (o.highVoltageBulkReceipt != null)
                return false
        }
        else if (this.highVoltageBulkReceipt != o.highVoltageBulkReceipt)
            return false
        if (this.highRentalSign == null) {
            if (o.highRentalSign != null)
                return false
        }
        else if (this.highRentalSign != o.highRentalSign)
            return false
        if (this.solarDiscountTarget == null) {
            if (o.solarDiscountTarget != null)
                return false
        }
        else if (this.solarDiscountTarget != o.solarDiscountTarget)
            return false
        if (this.cleaningCostFixed == null) {
            if (o.cleaningCostFixed != null)
                return false
        }
        else if (this.cleaningCostFixed != o.cleaningCostFixed)
            return false
        if (this.previousRent == null) {
            if (o.previousRent != null)
                return false
        }
        else if (this.previousRent != o.previousRent)
            return false
        if (this.existingReviewUpdateDate == null) {
            if (o.existingReviewUpdateDate != null)
                return false
        }
        else if (this.existingReviewUpdateDate != o.existingReviewUpdateDate)
            return false
        if (this.moveOutInspectionTime == null) {
            if (o.moveOutInspectionTime != null)
                return false
        }
        else if (this.moveOutInspectionTime != o.moveOutInspectionTime)
            return false
        if (this.recruitmentStartDate == null) {
            if (o.recruitmentStartDate != null)
                return false
        }
        else if (this.recruitmentStartDate != o.recruitmentStartDate)
            return false
        if (this.cleaningCostTotal == null) {
            if (o.cleaningCostTotal != null)
                return false
        }
        else if (this.cleaningCostTotal != o.cleaningCostTotal)
            return false
        if (this.discountInitialValueSign == null) {
            if (o.discountInitialValueSign != null)
                return false
        }
        else if (this.discountInitialValueSign != o.discountInitialValueSign)
            return false
        if (this.flagReserve_7 == null) {
            if (o.flagReserve_7 != null)
                return false
        }
        else if (this.flagReserve_7 != o.flagReserve_7)
            return false
        if (this.petFlag == null) {
            if (o.petFlag != null)
                return false
        }
        else if (this.petFlag != o.petFlag)
            return false
        if (this.flagReserve_9 == null) {
            if (o.flagReserve_9 != null)
                return false
        }
        else if (this.flagReserve_9 != o.flagReserve_9)
            return false
        if (this.flagReserve_10 == null) {
            if (o.flagReserve_10 != null)
                return false
        }
        else if (this.flagReserve_10 != o.flagReserve_10)
            return false
        if (this.challengeStartDate == null) {
            if (o.challengeStartDate != null)
                return false
        }
        else if (this.challengeStartDate != o.challengeStartDate)
            return false
        if (this.challengeEndDate == null) {
            if (o.challengeEndDate != null)
                return false
        }
        else if (this.challengeEndDate != o.challengeEndDate)
            return false
        if (this.applicationEndDate == null) {
            if (o.applicationEndDate != null)
                return false
        }
        else if (this.applicationEndDate != o.applicationEndDate)
            return false
        if (this.moveInEndDate == null) {
            if (o.moveInEndDate != null)
                return false
        }
        else if (this.moveInEndDate != o.moveInEndDate)
            return false
        if (this.additionalReleaseDate == null) {
            if (o.additionalReleaseDate != null)
                return false
        }
        else if (this.additionalReleaseDate != o.additionalReleaseDate)
            return false
        if (this.recruitmentRent == null) {
            if (o.recruitmentRent != null)
                return false
        }
        else if (this.recruitmentRent != o.recruitmentRent)
            return false
        if (this.challengeAdditionalAmount == null) {
            if (o.challengeAdditionalAmount != null)
                return false
        }
        else if (this.challengeAdditionalAmount != o.challengeAdditionalAmount)
            return false
        if (this.reviewRent == null) {
            if (o.reviewRent != null)
                return false
        }
        else if (this.reviewRent != o.reviewRent)
            return false
        if (this.dateReserve_11 == null) {
            if (o.dateReserve_11 != null)
                return false
        }
        else if (this.dateReserve_11 != o.dateReserve_11)
            return false
        if (this.dateReserve_12 == null) {
            if (o.dateReserve_12 != null)
                return false
        }
        else if (this.dateReserve_12 != o.dateReserve_12)
            return false
        if (this.dateReserve_13 == null) {
            if (o.dateReserve_13 != null)
                return false
        }
        else if (this.dateReserve_13 != o.dateReserve_13)
            return false
        if (this.amountReserve_1 == null) {
            if (o.amountReserve_1 != null)
                return false
        }
        else if (this.amountReserve_1 != o.amountReserve_1)
            return false
        if (this.amountReserve_2 == null) {
            if (o.amountReserve_2 != null)
                return false
        }
        else if (this.amountReserve_2 != o.amountReserve_2)
            return false
        if (this.amountReserve_3 == null) {
            if (o.amountReserve_3 != null)
                return false
        }
        else if (this.amountReserve_3 != o.amountReserve_3)
            return false
        if (this.commonServiceFeeBase == null) {
            if (o.commonServiceFeeBase != null)
                return false
        }
        else if (this.commonServiceFeeBase != o.commonServiceFeeBase)
            return false
        if (this.generalCableTvBase == null) {
            if (o.generalCableTvBase != null)
                return false
        }
        else if (this.generalCableTvBase != o.generalCableTvBase)
            return false
        if (this.generalCableTvTax == null) {
            if (o.generalCableTvTax != null)
                return false
        }
        else if (this.generalCableTvTax != o.generalCableTvTax)
            return false
        if (this.generalInternetBase == null) {
            if (o.generalInternetBase != null)
                return false
        }
        else if (this.generalInternetBase != o.generalInternetBase)
            return false
        if (this.generalInternetTax == null) {
            if (o.generalInternetTax != null)
                return false
        }
        else if (this.generalInternetTax != o.generalInternetTax)
            return false
        if (this.generalWaterQualityBase == null) {
            if (o.generalWaterQualityBase != null)
                return false
        }
        else if (this.generalWaterQualityBase != o.generalWaterQualityBase)
            return false
        if (this.generalWaterQualityTax == null) {
            if (o.generalWaterQualityTax != null)
                return false
        }
        else if (this.generalWaterQualityTax != o.generalWaterQualityTax)
            return false
        if (this.generalTenantWaterBase == null) {
            if (o.generalTenantWaterBase != null)
                return false
        }
        else if (this.generalTenantWaterBase != o.generalTenantWaterBase)
            return false
        if (this.generalTenantWaterTax == null) {
            if (o.generalTenantWaterTax != null)
                return false
        }
        else if (this.generalTenantWaterTax != o.generalTenantWaterTax)
            return false
        if (this.generalDrainUseBase == null) {
            if (o.generalDrainUseBase != null)
                return false
        }
        else if (this.generalDrainUseBase != o.generalDrainUseBase)
            return false
        if (this.generalDrainUseTax == null) {
            if (o.generalDrainUseTax != null)
                return false
        }
        else if (this.generalDrainUseTax != o.generalDrainUseTax)
            return false
        if (this.generalGarbageCollectionBase == null) {
            if (o.generalGarbageCollectionBase != null)
                return false
        }
        else if (this.generalGarbageCollectionBase != o.generalGarbageCollectionBase)
            return false
        if (this.generalGarbageCollectionTax == null) {
            if (o.generalGarbageCollectionTax != null)
                return false
        }
        else if (this.generalGarbageCollectionTax != o.generalGarbageCollectionTax)
            return false
        if (this.generalSharedAntennaBase == null) {
            if (o.generalSharedAntennaBase != null)
                return false
        }
        else if (this.generalSharedAntennaBase != o.generalSharedAntennaBase)
            return false
        if (this.generalSharedAntennaTax == null) {
            if (o.generalSharedAntennaTax != null)
                return false
        }
        else if (this.generalSharedAntennaTax != o.generalSharedAntennaTax)
            return false
        if (this.generalOwnerCleaningBase == null) {
            if (o.generalOwnerCleaningBase != null)
                return false
        }
        else if (this.generalOwnerCleaningBase != o.generalOwnerCleaningBase)
            return false
        if (this.generalOwnerCleaningTax == null) {
            if (o.generalOwnerCleaningTax != null)
                return false
        }
        else if (this.generalOwnerCleaningTax != o.generalOwnerCleaningTax)
            return false
        if (this.generalBuildingMaintenanceBase == null) {
            if (o.generalBuildingMaintenanceBase != null)
                return false
        }
        else if (this.generalBuildingMaintenanceBase != o.generalBuildingMaintenanceBase)
            return false
        if (this.generalBuildingMaintenanceTax == null) {
            if (o.generalBuildingMaintenanceTax != null)
                return false
        }
        else if (this.generalBuildingMaintenanceTax != o.generalBuildingMaintenanceTax)
            return false
        if (this.generalBuildingManagementBase == null) {
            if (o.generalBuildingManagementBase != null)
                return false
        }
        else if (this.generalBuildingManagementBase != o.generalBuildingManagementBase)
            return false
        if (this.generalBuildingManagementTax == null) {
            if (o.generalBuildingManagementTax != null)
                return false
        }
        else if (this.generalBuildingManagementTax != o.generalBuildingManagementTax)
            return false
        if (this.generalNeighborhoodAssocBase == null) {
            if (o.generalNeighborhoodAssocBase != null)
                return false
        }
        else if (this.generalNeighborhoodAssocBase != o.generalNeighborhoodAssocBase)
            return false
        if (this.generalNeighborhoodAssocTax == null) {
            if (o.generalNeighborhoodAssocTax != null)
                return false
        }
        else if (this.generalNeighborhoodAssocTax != o.generalNeighborhoodAssocTax)
            return false
        if (this.generalNeighborhoodOtherBase == null) {
            if (o.generalNeighborhoodOtherBase != null)
                return false
        }
        else if (this.generalNeighborhoodOtherBase != o.generalNeighborhoodOtherBase)
            return false
        if (this.generalNeighborhoodOtherTax == null) {
            if (o.generalNeighborhoodOtherTax != null)
                return false
        }
        else if (this.generalNeighborhoodOtherTax != o.generalNeighborhoodOtherTax)
            return false
        if (this.generalRepaymentAgentBase == null) {
            if (o.generalRepaymentAgentBase != null)
                return false
        }
        else if (this.generalRepaymentAgentBase != o.generalRepaymentAgentBase)
            return false
        if (this.generalRepaymentAgentTax == null) {
            if (o.generalRepaymentAgentTax != null)
                return false
        }
        else if (this.generalRepaymentAgentTax != o.generalRepaymentAgentTax)
            return false
        if (this.generalHlCommissionBase == null) {
            if (o.generalHlCommissionBase != null)
                return false
        }
        else if (this.generalHlCommissionBase != o.generalHlCommissionBase)
            return false
        if (this.generalHlCommissionTax == null) {
            if (o.generalHlCommissionTax != null)
                return false
        }
        else if (this.generalHlCommissionTax != o.generalHlCommissionTax)
            return false
        if (this.generalFurnishedBase == null) {
            if (o.generalFurnishedBase != null)
                return false
        }
        else if (this.generalFurnishedBase != o.generalFurnishedBase)
            return false
        if (this.generalFurnishedTax == null) {
            if (o.generalFurnishedTax != null)
                return false
        }
        else if (this.generalFurnishedTax != o.generalFurnishedTax)
            return false
        if (this.generalTenantDepositBase == null) {
            if (o.generalTenantDepositBase != null)
                return false
        }
        else if (this.generalTenantDepositBase != o.generalTenantDepositBase)
            return false
        if (this.generalTenantDepositTax == null) {
            if (o.generalTenantDepositTax != null)
                return false
        }
        else if (this.generalTenantDepositTax != o.generalTenantDepositTax)
            return false
        if (this.generalRentalBase == null) {
            if (o.generalRentalBase != null)
                return false
        }
        else if (this.generalRentalBase != o.generalRentalBase)
            return false
        if (this.generalRentalTax == null) {
            if (o.generalRentalTax != null)
                return false
        }
        else if (this.generalRentalTax != o.generalRentalTax)
            return false
        if (this.reserveAmount_1Base == null) {
            if (o.reserveAmount_1Base != null)
                return false
        }
        else if (this.reserveAmount_1Base != o.reserveAmount_1Base)
            return false
        if (this.reserveAmount_1Tax == null) {
            if (o.reserveAmount_1Tax != null)
                return false
        }
        else if (this.reserveAmount_1Tax != o.reserveAmount_1Tax)
            return false
        if (this.reserveAmount_2Base == null) {
            if (o.reserveAmount_2Base != null)
                return false
        }
        else if (this.reserveAmount_2Base != o.reserveAmount_2Base)
            return false
        if (this.reserveAmount_2Tax == null) {
            if (o.reserveAmount_2Tax != null)
                return false
        }
        else if (this.reserveAmount_2Tax != o.reserveAmount_2Tax)
            return false
        if (this.reserveAmount_3Base == null) {
            if (o.reserveAmount_3Base != null)
                return false
        }
        else if (this.reserveAmount_3Base != o.reserveAmount_3Base)
            return false
        if (this.reserveAmount_3Tax == null) {
            if (o.reserveAmount_3Tax != null)
                return false
        }
        else if (this.reserveAmount_3Tax != o.reserveAmount_3Tax)
            return false
        if (this.flagReserve_11 == null) {
            if (o.flagReserve_11 != null)
                return false
        }
        else if (this.flagReserve_11 != o.flagReserve_11)
            return false
        if (this.flagReserve_12 == null) {
            if (o.flagReserve_12 != null)
                return false
        }
        else if (this.flagReserve_12 != o.flagReserve_12)
            return false
        if (this.bundleWater == null) {
            if (o.bundleWater != null)
                return false
        }
        else if (this.bundleWater != o.bundleWater)
            return false
        if (this.bundleElectricity == null) {
            if (o.bundleElectricity != null)
                return false
        }
        else if (this.bundleElectricity != o.bundleElectricity)
            return false
        if (this.bundleGas == null) {
            if (o.bundleGas != null)
                return false
        }
        else if (this.bundleGas != o.bundleGas)
            return false
        if (this.category_2digitReserve_1 == null) {
            if (o.category_2digitReserve_1 != null)
                return false
        }
        else if (this.category_2digitReserve_1 != o.category_2digitReserve_1)
            return false
        if (this.category_2digitReserve_2 == null) {
            if (o.category_2digitReserve_2 != null)
                return false
        }
        else if (this.category_2digitReserve_2 != o.category_2digitReserve_2)
            return false
        if (this.category_2digitReserve_3 == null) {
            if (o.category_2digitReserve_3 != null)
                return false
        }
        else if (this.category_2digitReserve_3 != o.category_2digitReserve_3)
            return false
        if (this.category_2digitReserve_4 == null) {
            if (o.category_2digitReserve_4 != null)
                return false
        }
        else if (this.category_2digitReserve_4 != o.category_2digitReserve_4)
            return false
        if (this.category_2digitReserve_5 == null) {
            if (o.category_2digitReserve_5 != null)
                return false
        }
        else if (this.category_2digitReserve_5 != o.category_2digitReserve_5)
            return false
        if (this.amountReserve_4 == null) {
            if (o.amountReserve_4 != null)
                return false
        }
        else if (this.amountReserve_4 != o.amountReserve_4)
            return false
        if (this.amountReserve_5 == null) {
            if (o.amountReserve_5 != null)
                return false
        }
        else if (this.amountReserve_5 != o.amountReserve_5)
            return false
        if (this.amountReserve_6 == null) {
            if (o.amountReserve_6 != null)
                return false
        }
        else if (this.amountReserve_6 != o.amountReserve_6)
            return false
        if (this.amountReserve_7 == null) {
            if (o.amountReserve_7 != null)
                return false
        }
        else if (this.amountReserve_7 != o.amountReserve_7)
            return false
        if (this.amountReserve_8 == null) {
            if (o.amountReserve_8 != null)
                return false
        }
        else if (this.amountReserve_8 != o.amountReserve_8)
            return false
        if (this.dateReserve_14 == null) {
            if (o.dateReserve_14 != null)
                return false
        }
        else if (this.dateReserve_14 != o.dateReserve_14)
            return false
        if (this.dateReserve_15 == null) {
            if (o.dateReserve_15 != null)
                return false
        }
        else if (this.dateReserve_15 != o.dateReserve_15)
            return false
        if (this.dateReserve_16 == null) {
            if (o.dateReserve_16 != null)
                return false
        }
        else if (this.dateReserve_16 != o.dateReserve_16)
            return false
        if (this.dateReserve_17 == null) {
            if (o.dateReserve_17 != null)
                return false
        }
        else if (this.dateReserve_17 != o.dateReserve_17)
            return false
        if (this.dateReserve_18 == null) {
            if (o.dateReserve_18 != null)
                return false
        }
        else if (this.dateReserve_18 != o.dateReserve_18)
            return false
        if (this.category_1digitReserve_1 == null) {
            if (o.category_1digitReserve_1 != null)
                return false
        }
        else if (this.category_1digitReserve_1 != o.category_1digitReserve_1)
            return false
        if (this.category_1digitReserve_2 == null) {
            if (o.category_1digitReserve_2 != null)
                return false
        }
        else if (this.category_1digitReserve_2 != o.category_1digitReserve_2)
            return false
        if (this.category_1digitReserve_3 == null) {
            if (o.category_1digitReserve_3 != null)
                return false
        }
        else if (this.category_1digitReserve_3 != o.category_1digitReserve_3)
            return false
        if (this.category_1digitReserve_4 == null) {
            if (o.category_1digitReserve_4 != null)
                return false
        }
        else if (this.category_1digitReserve_4 != o.category_1digitReserve_4)
            return false
        if (this.category_1digitReserve_5 == null) {
            if (o.category_1digitReserve_5 != null)
                return false
        }
        else if (this.category_1digitReserve_5 != o.category_1digitReserve_5)
            return false
        if (this.leasingStoreCd == null) {
            if (o.leasingStoreCd != null)
                return false
        }
        else if (this.leasingStoreCd != o.leasingStoreCd)
            return false
        if (this.managementBranchCd == null) {
            if (o.managementBranchCd != null)
                return false
        }
        else if (this.managementBranchCd != o.managementBranchCd)
            return false
        if (this.salesOfficeCd == null) {
            if (o.salesOfficeCd != null)
                return false
        }
        else if (this.salesOfficeCd != o.salesOfficeCd)
            return false
        if (this.screeningBranchCd == null) {
            if (o.screeningBranchCd != null)
                return false
        }
        else if (this.screeningBranchCd != o.screeningBranchCd)
            return false
        if (this.preference_100 == null) {
            if (o.preference_100 != null)
                return false
        }
        else if (this.preference_100 != o.preference_100)
            return false
        if (this.preference_101 == null) {
            if (o.preference_101 != null)
                return false
        }
        else if (this.preference_101 != o.preference_101)
            return false
        if (this.preference_102 == null) {
            if (o.preference_102 != null)
                return false
        }
        else if (this.preference_102 != o.preference_102)
            return false
        if (this.preference_103 == null) {
            if (o.preference_103 != null)
                return false
        }
        else if (this.preference_103 != o.preference_103)
            return false
        if (this.preference_104 == null) {
            if (o.preference_104 != null)
                return false
        }
        else if (this.preference_104 != o.preference_104)
            return false
        if (this.preference_105 == null) {
            if (o.preference_105 != null)
                return false
        }
        else if (this.preference_105 != o.preference_105)
            return false
        if (this.preference_106 == null) {
            if (o.preference_106 != null)
                return false
        }
        else if (this.preference_106 != o.preference_106)
            return false
        if (this.preference_107 == null) {
            if (o.preference_107 != null)
                return false
        }
        else if (this.preference_107 != o.preference_107)
            return false
        if (this.preference_108 == null) {
            if (o.preference_108 != null)
                return false
        }
        else if (this.preference_108 != o.preference_108)
            return false
        if (this.preference_109 == null) {
            if (o.preference_109 != null)
                return false
        }
        else if (this.preference_109 != o.preference_109)
            return false
        if (this.preference_110 == null) {
            if (o.preference_110 != null)
                return false
        }
        else if (this.preference_110 != o.preference_110)
            return false
        if (this.preference_111 == null) {
            if (o.preference_111 != null)
                return false
        }
        else if (this.preference_111 != o.preference_111)
            return false
        if (this.preference_112 == null) {
            if (o.preference_112 != null)
                return false
        }
        else if (this.preference_112 != o.preference_112)
            return false
        if (this.preference_113 == null) {
            if (o.preference_113 != null)
                return false
        }
        else if (this.preference_113 != o.preference_113)
            return false
        if (this.preference_114 == null) {
            if (o.preference_114 != null)
                return false
        }
        else if (this.preference_114 != o.preference_114)
            return false
        if (this.preference_115 == null) {
            if (o.preference_115 != null)
                return false
        }
        else if (this.preference_115 != o.preference_115)
            return false
        if (this.preference_116 == null) {
            if (o.preference_116 != null)
                return false
        }
        else if (this.preference_116 != o.preference_116)
            return false
        if (this.preference_117 == null) {
            if (o.preference_117 != null)
                return false
        }
        else if (this.preference_117 != o.preference_117)
            return false
        if (this.preference_118 == null) {
            if (o.preference_118 != null)
                return false
        }
        else if (this.preference_118 != o.preference_118)
            return false
        if (this.preference_119 == null) {
            if (o.preference_119 != null)
                return false
        }
        else if (this.preference_119 != o.preference_119)
            return false
        if (this.preference_120 == null) {
            if (o.preference_120 != null)
                return false
        }
        else if (this.preference_120 != o.preference_120)
            return false
        if (this.preference_121 == null) {
            if (o.preference_121 != null)
                return false
        }
        else if (this.preference_121 != o.preference_121)
            return false
        if (this.preference_122 == null) {
            if (o.preference_122 != null)
                return false
        }
        else if (this.preference_122 != o.preference_122)
            return false
        if (this.preference_123 == null) {
            if (o.preference_123 != null)
                return false
        }
        else if (this.preference_123 != o.preference_123)
            return false
        if (this.preference_124 == null) {
            if (o.preference_124 != null)
                return false
        }
        else if (this.preference_124 != o.preference_124)
            return false
        if (this.preference_125 == null) {
            if (o.preference_125 != null)
                return false
        }
        else if (this.preference_125 != o.preference_125)
            return false
        if (this.preference_126 == null) {
            if (o.preference_126 != null)
                return false
        }
        else if (this.preference_126 != o.preference_126)
            return false
        if (this.preference_127 == null) {
            if (o.preference_127 != null)
                return false
        }
        else if (this.preference_127 != o.preference_127)
            return false
        if (this.preference_128 == null) {
            if (o.preference_128 != null)
                return false
        }
        else if (this.preference_128 != o.preference_128)
            return false
        if (this.preference_129 == null) {
            if (o.preference_129 != null)
                return false
        }
        else if (this.preference_129 != o.preference_129)
            return false
        if (this.preference_130 == null) {
            if (o.preference_130 != null)
                return false
        }
        else if (this.preference_130 != o.preference_130)
            return false
        if (this.preference_131 == null) {
            if (o.preference_131 != null)
                return false
        }
        else if (this.preference_131 != o.preference_131)
            return false
        if (this.preference_132 == null) {
            if (o.preference_132 != null)
                return false
        }
        else if (this.preference_132 != o.preference_132)
            return false
        if (this.preference_133 == null) {
            if (o.preference_133 != null)
                return false
        }
        else if (this.preference_133 != o.preference_133)
            return false
        if (this.preference_134 == null) {
            if (o.preference_134 != null)
                return false
        }
        else if (this.preference_134 != o.preference_134)
            return false
        if (this.preference_135 == null) {
            if (o.preference_135 != null)
                return false
        }
        else if (this.preference_135 != o.preference_135)
            return false
        if (this.preference_136 == null) {
            if (o.preference_136 != null)
                return false
        }
        else if (this.preference_136 != o.preference_136)
            return false
        if (this.preference_137 == null) {
            if (o.preference_137 != null)
                return false
        }
        else if (this.preference_137 != o.preference_137)
            return false
        if (this.preference_138 == null) {
            if (o.preference_138 != null)
                return false
        }
        else if (this.preference_138 != o.preference_138)
            return false
        if (this.preference_139 == null) {
            if (o.preference_139 != null)
                return false
        }
        else if (this.preference_139 != o.preference_139)
            return false
        if (this.preference_140 == null) {
            if (o.preference_140 != null)
                return false
        }
        else if (this.preference_140 != o.preference_140)
            return false
        if (this.preference_141 == null) {
            if (o.preference_141 != null)
                return false
        }
        else if (this.preference_141 != o.preference_141)
            return false
        if (this.preference_142 == null) {
            if (o.preference_142 != null)
                return false
        }
        else if (this.preference_142 != o.preference_142)
            return false
        if (this.preference_143 == null) {
            if (o.preference_143 != null)
                return false
        }
        else if (this.preference_143 != o.preference_143)
            return false
        if (this.preference_144 == null) {
            if (o.preference_144 != null)
                return false
        }
        else if (this.preference_144 != o.preference_144)
            return false
        if (this.preference_145 == null) {
            if (o.preference_145 != null)
                return false
        }
        else if (this.preference_145 != o.preference_145)
            return false
        if (this.preference_146 == null) {
            if (o.preference_146 != null)
                return false
        }
        else if (this.preference_146 != o.preference_146)
            return false
        if (this.preference_147 == null) {
            if (o.preference_147 != null)
                return false
        }
        else if (this.preference_147 != o.preference_147)
            return false
        if (this.preference_148 == null) {
            if (o.preference_148 != null)
                return false
        }
        else if (this.preference_148 != o.preference_148)
            return false
        if (this.preference_149 == null) {
            if (o.preference_149 != null)
                return false
        }
        else if (this.preference_149 != o.preference_149)
            return false
        if (this.preference_150 == null) {
            if (o.preference_150 != null)
                return false
        }
        else if (this.preference_150 != o.preference_150)
            return false
        if (this.preference_151 == null) {
            if (o.preference_151 != null)
                return false
        }
        else if (this.preference_151 != o.preference_151)
            return false
        if (this.preference_152 == null) {
            if (o.preference_152 != null)
                return false
        }
        else if (this.preference_152 != o.preference_152)
            return false
        if (this.preference_153 == null) {
            if (o.preference_153 != null)
                return false
        }
        else if (this.preference_153 != o.preference_153)
            return false
        if (this.preference_154 == null) {
            if (o.preference_154 != null)
                return false
        }
        else if (this.preference_154 != o.preference_154)
            return false
        if (this.preference_155 == null) {
            if (o.preference_155 != null)
                return false
        }
        else if (this.preference_155 != o.preference_155)
            return false
        if (this.preference_156 == null) {
            if (o.preference_156 != null)
                return false
        }
        else if (this.preference_156 != o.preference_156)
            return false
        if (this.preference_157 == null) {
            if (o.preference_157 != null)
                return false
        }
        else if (this.preference_157 != o.preference_157)
            return false
        if (this.preference_158 == null) {
            if (o.preference_158 != null)
                return false
        }
        else if (this.preference_158 != o.preference_158)
            return false
        if (this.preference_159 == null) {
            if (o.preference_159 != null)
                return false
        }
        else if (this.preference_159 != o.preference_159)
            return false
        if (this.preference_160 == null) {
            if (o.preference_160 != null)
                return false
        }
        else if (this.preference_160 != o.preference_160)
            return false
        if (this.preference_161 == null) {
            if (o.preference_161 != null)
                return false
        }
        else if (this.preference_161 != o.preference_161)
            return false
        if (this.preference_162 == null) {
            if (o.preference_162 != null)
                return false
        }
        else if (this.preference_162 != o.preference_162)
            return false
        if (this.preference_163 == null) {
            if (o.preference_163 != null)
                return false
        }
        else if (this.preference_163 != o.preference_163)
            return false
        if (this.preference_164 == null) {
            if (o.preference_164 != null)
                return false
        }
        else if (this.preference_164 != o.preference_164)
            return false
        if (this.preference_165 == null) {
            if (o.preference_165 != null)
                return false
        }
        else if (this.preference_165 != o.preference_165)
            return false
        if (this.preference_166 == null) {
            if (o.preference_166 != null)
                return false
        }
        else if (this.preference_166 != o.preference_166)
            return false
        if (this.preference_167 == null) {
            if (o.preference_167 != null)
                return false
        }
        else if (this.preference_167 != o.preference_167)
            return false
        if (this.preference_168 == null) {
            if (o.preference_168 != null)
                return false
        }
        else if (this.preference_168 != o.preference_168)
            return false
        if (this.preference_169 == null) {
            if (o.preference_169 != null)
                return false
        }
        else if (this.preference_169 != o.preference_169)
            return false
        if (this.preference_170 == null) {
            if (o.preference_170 != null)
                return false
        }
        else if (this.preference_170 != o.preference_170)
            return false
        if (this.preference_171 == null) {
            if (o.preference_171 != null)
                return false
        }
        else if (this.preference_171 != o.preference_171)
            return false
        if (this.preference_172 == null) {
            if (o.preference_172 != null)
                return false
        }
        else if (this.preference_172 != o.preference_172)
            return false
        if (this.preference_173 == null) {
            if (o.preference_173 != null)
                return false
        }
        else if (this.preference_173 != o.preference_173)
            return false
        if (this.preference_174 == null) {
            if (o.preference_174 != null)
                return false
        }
        else if (this.preference_174 != o.preference_174)
            return false
        if (this.preference_175 == null) {
            if (o.preference_175 != null)
                return false
        }
        else if (this.preference_175 != o.preference_175)
            return false
        if (this.preference_176 == null) {
            if (o.preference_176 != null)
                return false
        }
        else if (this.preference_176 != o.preference_176)
            return false
        if (this.preference_177 == null) {
            if (o.preference_177 != null)
                return false
        }
        else if (this.preference_177 != o.preference_177)
            return false
        if (this.preference_178 == null) {
            if (o.preference_178 != null)
                return false
        }
        else if (this.preference_178 != o.preference_178)
            return false
        if (this.preference_179 == null) {
            if (o.preference_179 != null)
                return false
        }
        else if (this.preference_179 != o.preference_179)
            return false
        if (this.preference_180 == null) {
            if (o.preference_180 != null)
                return false
        }
        else if (this.preference_180 != o.preference_180)
            return false
        if (this.preference_181 == null) {
            if (o.preference_181 != null)
                return false
        }
        else if (this.preference_181 != o.preference_181)
            return false
        if (this.preference_182 == null) {
            if (o.preference_182 != null)
                return false
        }
        else if (this.preference_182 != o.preference_182)
            return false
        if (this.preference_183 == null) {
            if (o.preference_183 != null)
                return false
        }
        else if (this.preference_183 != o.preference_183)
            return false
        if (this.preference_184 == null) {
            if (o.preference_184 != null)
                return false
        }
        else if (this.preference_184 != o.preference_184)
            return false
        if (this.preference_185 == null) {
            if (o.preference_185 != null)
                return false
        }
        else if (this.preference_185 != o.preference_185)
            return false
        if (this.preference_186 == null) {
            if (o.preference_186 != null)
                return false
        }
        else if (this.preference_186 != o.preference_186)
            return false
        if (this.preference_187 == null) {
            if (o.preference_187 != null)
                return false
        }
        else if (this.preference_187 != o.preference_187)
            return false
        if (this.preference_188 == null) {
            if (o.preference_188 != null)
                return false
        }
        else if (this.preference_188 != o.preference_188)
            return false
        if (this.preference_189 == null) {
            if (o.preference_189 != null)
                return false
        }
        else if (this.preference_189 != o.preference_189)
            return false
        if (this.preference_190 == null) {
            if (o.preference_190 != null)
                return false
        }
        else if (this.preference_190 != o.preference_190)
            return false
        if (this.preference_191 == null) {
            if (o.preference_191 != null)
                return false
        }
        else if (this.preference_191 != o.preference_191)
            return false
        if (this.preference_192 == null) {
            if (o.preference_192 != null)
                return false
        }
        else if (this.preference_192 != o.preference_192)
            return false
        if (this.preference_193 == null) {
            if (o.preference_193 != null)
                return false
        }
        else if (this.preference_193 != o.preference_193)
            return false
        if (this.preference_194 == null) {
            if (o.preference_194 != null)
                return false
        }
        else if (this.preference_194 != o.preference_194)
            return false
        if (this.preference_195 == null) {
            if (o.preference_195 != null)
                return false
        }
        else if (this.preference_195 != o.preference_195)
            return false
        if (this.preference_196 == null) {
            if (o.preference_196 != null)
                return false
        }
        else if (this.preference_196 != o.preference_196)
            return false
        if (this.preference_197 == null) {
            if (o.preference_197 != null)
                return false
        }
        else if (this.preference_197 != o.preference_197)
            return false
        if (this.preference_198 == null) {
            if (o.preference_198 != null)
                return false
        }
        else if (this.preference_198 != o.preference_198)
            return false
        if (this.preference_199 == null) {
            if (o.preference_199 != null)
                return false
        }
        else if (this.preference_199 != o.preference_199)
            return false
        if (this.marketingBranchOfficeCd == null) {
            if (o.marketingBranchOfficeCd != null)
                return false
        }
        else if (this.marketingBranchOfficeCd != o.marketingBranchOfficeCd)
            return false
        if (this.propertySituation == null) {
            if (o.propertySituation != null)
                return false
        }
        else if (this.propertySituation != o.propertySituation)
            return false
        if (this.prefectureCityCd == null) {
            if (o.prefectureCityCd != null)
                return false
        }
        else if (this.prefectureCityCd != o.prefectureCityCd)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + this.recordType.hashCode()
        result = prime * result + this.propertyCdType.hashCode()
        result = prime * result + (if (this.propertyCdPart1 == null) 0 else this.propertyCdPart1.hashCode())
        result = prime * result + this.propertyBuildingCd.hashCode()
        result = prime * result + (if (this.propertyCdPart2 == null) 0 else this.propertyCdPart2.hashCode())
        result = prime * result + this.propertyRoomCd.hashCode()
        result = prime * result + (if (this.deleteFlag == null) 0 else this.deleteFlag.hashCode())
        result = prime * result + (if (this.customerCompanyCd == null) 0 else this.customerCompanyCd.hashCode())
        result = prime * result + (if (this.customerBranchCd == null) 0 else this.customerBranchCd.hashCode())
        result = prime * result + (if (this.customerDepartmentCd == null) 0 else this.customerDepartmentCd.hashCode())
        result = prime * result + (if (this.customerCompletionFlag == null) 0 else this.customerCompletionFlag.hashCode())
        result = prime * result + (if (this.prefectureCd == null) 0 else this.prefectureCd.hashCode())
        result = prime * result + (if (this.cityCd == null) 0 else this.cityCd.hashCode())
        result = prime * result + (if (this.lineCd == null) 0 else this.lineCd.hashCode())
        result = prime * result + (if (this.stationCd == null) 0 else this.stationCd.hashCode())
        result = prime * result + (if (this.rent == null) 0 else this.rent.hashCode())
        result = prime * result + (if (this.layoutRoomCount == null) 0 else this.layoutRoomCount.hashCode())
        result = prime * result + (if (this.exclusiveArea == null) 0 else this.exclusiveArea.hashCode())
        result = prime * result + (if (this.propertyType == null) 0 else this.propertyType.hashCode())
        result = prime * result + (if (this.preference_1 == null) 0 else this.preference_1.hashCode())
        result = prime * result + (if (this.preference_2 == null) 0 else this.preference_2.hashCode())
        result = prime * result + (if (this.preference_3 == null) 0 else this.preference_3.hashCode())
        result = prime * result + (if (this.preference_4 == null) 0 else this.preference_4.hashCode())
        result = prime * result + (if (this.preference_5 == null) 0 else this.preference_5.hashCode())
        result = prime * result + (if (this.preference_6 == null) 0 else this.preference_6.hashCode())
        result = prime * result + (if (this.preference_7 == null) 0 else this.preference_7.hashCode())
        result = prime * result + (if (this.preference_8 == null) 0 else this.preference_8.hashCode())
        result = prime * result + (if (this.preference_9 == null) 0 else this.preference_9.hashCode())
        result = prime * result + (if (this.preference_10 == null) 0 else this.preference_10.hashCode())
        result = prime * result + (if (this.preference_11 == null) 0 else this.preference_11.hashCode())
        result = prime * result + (if (this.preference_12 == null) 0 else this.preference_12.hashCode())
        result = prime * result + (if (this.preference_13 == null) 0 else this.preference_13.hashCode())
        result = prime * result + (if (this.preference_14 == null) 0 else this.preference_14.hashCode())
        result = prime * result + (if (this.preference_15 == null) 0 else this.preference_15.hashCode())
        result = prime * result + (if (this.preference_16 == null) 0 else this.preference_16.hashCode())
        result = prime * result + (if (this.preference_17 == null) 0 else this.preference_17.hashCode())
        result = prime * result + (if (this.preference_18 == null) 0 else this.preference_18.hashCode())
        result = prime * result + (if (this.preference_19 == null) 0 else this.preference_19.hashCode())
        result = prime * result + (if (this.preference_20 == null) 0 else this.preference_20.hashCode())
        result = prime * result + (if (this.preference_21 == null) 0 else this.preference_21.hashCode())
        result = prime * result + (if (this.preference_22 == null) 0 else this.preference_22.hashCode())
        result = prime * result + (if (this.preference_23 == null) 0 else this.preference_23.hashCode())
        result = prime * result + (if (this.preference_24 == null) 0 else this.preference_24.hashCode())
        result = prime * result + (if (this.preference_25 == null) 0 else this.preference_25.hashCode())
        result = prime * result + (if (this.preference_26 == null) 0 else this.preference_26.hashCode())
        result = prime * result + (if (this.preference_27 == null) 0 else this.preference_27.hashCode())
        result = prime * result + (if (this.preference_28 == null) 0 else this.preference_28.hashCode())
        result = prime * result + (if (this.preference_29 == null) 0 else this.preference_29.hashCode())
        result = prime * result + (if (this.preference_30 == null) 0 else this.preference_30.hashCode())
        result = prime * result + (if (this.preference_31 == null) 0 else this.preference_31.hashCode())
        result = prime * result + (if (this.preference_32 == null) 0 else this.preference_32.hashCode())
        result = prime * result + (if (this.preference_33 == null) 0 else this.preference_33.hashCode())
        result = prime * result + (if (this.preference_34 == null) 0 else this.preference_34.hashCode())
        result = prime * result + (if (this.preference_35 == null) 0 else this.preference_35.hashCode())
        result = prime * result + (if (this.preference_36 == null) 0 else this.preference_36.hashCode())
        result = prime * result + (if (this.preference_37 == null) 0 else this.preference_37.hashCode())
        result = prime * result + (if (this.preference_38 == null) 0 else this.preference_38.hashCode())
        result = prime * result + (if (this.preference_39 == null) 0 else this.preference_39.hashCode())
        result = prime * result + (if (this.preference_40 == null) 0 else this.preference_40.hashCode())
        result = prime * result + (if (this.preference_41 == null) 0 else this.preference_41.hashCode())
        result = prime * result + (if (this.preference_42 == null) 0 else this.preference_42.hashCode())
        result = prime * result + (if (this.preference_43 == null) 0 else this.preference_43.hashCode())
        result = prime * result + (if (this.preference_44 == null) 0 else this.preference_44.hashCode())
        result = prime * result + (if (this.preference_45 == null) 0 else this.preference_45.hashCode())
        result = prime * result + (if (this.preference_46 == null) 0 else this.preference_46.hashCode())
        result = prime * result + (if (this.preference_47 == null) 0 else this.preference_47.hashCode())
        result = prime * result + (if (this.preference_48 == null) 0 else this.preference_48.hashCode())
        result = prime * result + (if (this.preference_49 == null) 0 else this.preference_49.hashCode())
        result = prime * result + (if (this.preference_50 == null) 0 else this.preference_50.hashCode())
        result = prime * result + (if (this.preference_51 == null) 0 else this.preference_51.hashCode())
        result = prime * result + (if (this.preference_52 == null) 0 else this.preference_52.hashCode())
        result = prime * result + (if (this.preference_53 == null) 0 else this.preference_53.hashCode())
        result = prime * result + (if (this.preference_54 == null) 0 else this.preference_54.hashCode())
        result = prime * result + (if (this.preference_55 == null) 0 else this.preference_55.hashCode())
        result = prime * result + (if (this.preference_56 == null) 0 else this.preference_56.hashCode())
        result = prime * result + (if (this.preference_57 == null) 0 else this.preference_57.hashCode())
        result = prime * result + (if (this.preference_58 == null) 0 else this.preference_58.hashCode())
        result = prime * result + (if (this.preference_59 == null) 0 else this.preference_59.hashCode())
        result = prime * result + (if (this.preference_60 == null) 0 else this.preference_60.hashCode())
        result = prime * result + (if (this.preference_99 == null) 0 else this.preference_99.hashCode())
        result = prime * result + (if (this.preferenceNewBuild == null) 0 else this.preferenceNewBuild.hashCode())
        result = prime * result + (if (this.preferenceCornerRoom == null) 0 else this.preferenceCornerRoom.hashCode())
        result = prime * result + (if (this.preferenceAbove_2ndFloor == null) 0 else this.preferenceAbove_2ndFloor.hashCode())
        result = prime * result + (if (this.lineName == null) 0 else this.lineName.hashCode())
        result = prime * result + (if (this.stationName == null) 0 else this.stationName.hashCode())
        result = prime * result + (if (this.busStopName == null) 0 else this.busStopName.hashCode())
        result = prime * result + (if (this.busTime == null) 0 else this.busTime.hashCode())
        result = prime * result + (if (this.walkingTime == null) 0 else this.walkingTime.hashCode())
        result = prime * result + (if (this.distance == null) 0 else this.distance.hashCode())
        result = prime * result + (if (this.keyMoney == null) 0 else this.keyMoney.hashCode())
        result = prime * result + (if (this.deposit == null) 0 else this.deposit.hashCode())
        result = prime * result + (if (this.neighborhoodAssociationFee == null) 0 else this.neighborhoodAssociationFee.hashCode())
        result = prime * result + (if (this.commonServiceFee == null) 0 else this.commonServiceFee.hashCode())
        result = prime * result + (if (this.roomTypeName == null) 0 else this.roomTypeName.hashCode())
        result = prime * result + (if (this.layoutType == null) 0 else this.layoutType.hashCode())
        result = prime * result + (if (this.layout == null) 0 else this.layout.hashCode())
        result = prime * result + (if (this.layoutDetails == null) 0 else this.layoutDetails.hashCode())
        result = prime * result + (if (this.parkingType == null) 0 else this.parkingType.hashCode())
        result = prime * result + (if (this.parkingFee == null) 0 else this.parkingFee.hashCode())
        result = prime * result + (if (this.constructionYearMonth == null) 0 else this.constructionYearMonth.hashCode())
        result = prime * result + (if (this.handlingStoreCompany == null) 0 else this.handlingStoreCompany.hashCode())
        result = prime * result + (if (this.locationListingArea == null) 0 else this.locationListingArea.hashCode())
        result = prime * result + (if (this.floorNumber == null) 0 else this.floorNumber.hashCode())
        result = prime * result + (if (this.direction == null) 0 else this.direction.hashCode())
        result = prime * result + (if (this.roomPosition == null) 0 else this.roomPosition.hashCode())
        result = prime * result + (if (this.availableMoveInYearMonth == null) 0 else this.availableMoveInYearMonth.hashCode())
        result = prime * result + (if (this.transportation == null) 0 else this.transportation.hashCode())
        result = prime * result + (if (this.equipment == null) 0 else this.equipment.hashCode())
        result = prime * result + (if (this.notes == null) 0 else this.notes.hashCode())
        result = prime * result + (if (this.inquiryBranchName == null) 0 else this.inquiryBranchName.hashCode())
        result = prime * result + (if (this.branchPhoneNumber == null) 0 else this.branchPhoneNumber.hashCode())
        result = prime * result + (if (this.branchFaxNumber == null) 0 else this.branchFaxNumber.hashCode())
        result = prime * result + (if (this.transactionType == null) 0 else this.transactionType.hashCode())
        result = prime * result + (if (this.buildingName == null) 0 else this.buildingName.hashCode())
        result = prime * result + (if (this.structureName == null) 0 else this.structureName.hashCode())
        result = prime * result + (if (this.agentAssignableType == null) 0 else this.agentAssignableType.hashCode())
        result = prime * result + (if (this.subleaseType == null) 0 else this.subleaseType.hashCode())
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.creator == null) 0 else this.creator.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.branchAddress == null) 0 else this.branchAddress.hashCode())
        result = prime * result + (if (this.recommendationComment == null) 0 else this.recommendationComment.hashCode())
        result = prime * result + (if (this.completionYearMonth == null) 0 else this.completionYearMonth.hashCode())
        result = prime * result + (if (this.propertyPostalCode == null) 0 else this.propertyPostalCode.hashCode())
        result = prime * result + (if (this.vacateNoticeDate == null) 0 else this.vacateNoticeDate.hashCode())
        result = prime * result + (if (this.expectedMoveOutDate == null) 0 else this.expectedMoveOutDate.hashCode())
        result = prime * result + (if (this.moveOutDate == null) 0 else this.moveOutDate.hashCode())
        result = prime * result + (if (this.expectedCompletionDate == null) 0 else this.expectedCompletionDate.hashCode())
        result = prime * result + (if (this.availableMoveInDate == null) 0 else this.availableMoveInDate.hashCode())
        result = prime * result + (if (this.moveInApplicationDate == null) 0 else this.moveInApplicationDate.hashCode())
        result = prime * result + (if (this.depositDate == null) 0 else this.depositDate.hashCode())
        result = prime * result + (if (this.balanceCollectionDate == null) 0 else this.balanceCollectionDate.hashCode())
        result = prime * result + (if (this.moveInDate == null) 0 else this.moveInDate.hashCode())
        result = prime * result + (if (this.completionDate == null) 0 else this.completionDate.hashCode())
        result = prime * result + (if (this.tenantRecruitmentCollectionDate == null) 0 else this.tenantRecruitmentCollectionDate.hashCode())
        result = prime * result + (if (this.tenant == null) 0 else this.tenant.hashCode())
        result = prime * result + (if (this.owner == null) 0 else this.owner.hashCode())
        result = prime * result + (if (this.customerAgentBranchCd == null) 0 else this.customerAgentBranchCd.hashCode())
        result = prime * result + (if (this.customerAgentDepartmentCd == null) 0 else this.customerAgentDepartmentCd.hashCode())
        result = prime * result + (if (this.customerAgentEmployeeCd == null) 0 else this.customerAgentEmployeeCd.hashCode())
        result = prime * result + (if (this.rentTax == null) 0 else this.rentTax.hashCode())
        result = prime * result + (if (this.keyMoneyTax == null) 0 else this.keyMoneyTax.hashCode())
        result = prime * result + (if (this.keyMoneyTotal == null) 0 else this.keyMoneyTotal.hashCode())
        result = prime * result + (if (this.commonServiceFeeTax == null) 0 else this.commonServiceFeeTax.hashCode())
        result = prime * result + (if (this.parkingFeeTax == null) 0 else this.parkingFeeTax.hashCode())
        result = prime * result + (if (this.roomNumber == null) 0 else this.roomNumber.hashCode())
        result = prime * result + (if (this.newExistingFlag == null) 0 else this.newExistingFlag.hashCode())
        result = prime * result + (if (this.roomStatusType == null) 0 else this.roomStatusType.hashCode())
        result = prime * result + (if (this.recordStatusType == null) 0 else this.recordStatusType.hashCode())
        result = prime * result + (if (this.ffUsagePeriod == null) 0 else this.ffUsagePeriod.hashCode())
        result = prime * result + (if (this.adPayableAmount == null) 0 else this.adPayableAmount.hashCode())
        result = prime * result + (if (this.locationCity == null) 0 else this.locationCity.hashCode())
        result = prime * result + (if (this.tenantContractNumber == null) 0 else this.tenantContractNumber.hashCode())
        result = prime * result + (if (this.ownerContact == null) 0 else this.ownerContact.hashCode())
        result = prime * result + (if (this.vacantPeriod == null) 0 else this.vacantPeriod.hashCode())
        result = prime * result + (if (this.floorArea_1f == null) 0 else this.floorArea_1f.hashCode())
        result = prime * result + (if (this.floorArea_2f == null) 0 else this.floorArea_2f.hashCode())
        result = prime * result + (if (this.floorArea_3f == null) 0 else this.floorArea_3f.hashCode())
        result = prime * result + (if (this.recruitmentCreationDate == null) 0 else this.recruitmentCreationDate.hashCode())
        result = prime * result + (if (this.recruitmentApprovalDate == null) 0 else this.recruitmentApprovalDate.hashCode())
        result = prime * result + (if (this.moveOutInspectionDate == null) 0 else this.moveOutInspectionDate.hashCode())
        result = prime * result + (if (this.restorationDate == null) 0 else this.restorationDate.hashCode())
        result = prime * result + (if (this.restorationCompletionDate == null) 0 else this.restorationCompletionDate.hashCode())
        result = prime * result + (if (this.vacantBookingDate == null) 0 else this.vacantBookingDate.hashCode())
        result = prime * result + (if (this.vacantBookingCompletionDate == null) 0 else this.vacantBookingCompletionDate.hashCode())
        result = prime * result + (if (this.additionalKeyMoney == null) 0 else this.additionalKeyMoney.hashCode())
        result = prime * result + (if (this.mutualAidJoinSign == null) 0 else this.mutualAidJoinSign.hashCode())
        result = prime * result + (if (this.rentalType == null) 0 else this.rentalType.hashCode())
        result = prime * result + (if (this.specialRentalType == null) 0 else this.specialRentalType.hashCode())
        result = prime * result + (if (this.distance2 == null) 0 else this.distance2.hashCode())
        result = prime * result + (if (this.approvalType == null) 0 else this.approvalType.hashCode())
        result = prime * result + (if (this.recordSeparator == null) 0 else this.recordSeparator.hashCode())
        result = prime * result + (if (this.changeType == null) 0 else this.changeType.hashCode())
        result = prime * result + (if (this.noDepositFlag == null) 0 else this.noDepositFlag.hashCode())
        result = prime * result + (if (this.campaignTargetFlag == null) 0 else this.campaignTargetFlag.hashCode())
        result = prime * result + (if (this.preference_61 == null) 0 else this.preference_61.hashCode())
        result = prime * result + (if (this.preference_62 == null) 0 else this.preference_62.hashCode())
        result = prime * result + (if (this.preference_63 == null) 0 else this.preference_63.hashCode())
        result = prime * result + (if (this.preference_64 == null) 0 else this.preference_64.hashCode())
        result = prime * result + (if (this.preference_65 == null) 0 else this.preference_65.hashCode())
        result = prime * result + (if (this.preference_66 == null) 0 else this.preference_66.hashCode())
        result = prime * result + (if (this.preference_67 == null) 0 else this.preference_67.hashCode())
        result = prime * result + (if (this.preference_68 == null) 0 else this.preference_68.hashCode())
        result = prime * result + (if (this.preference_69 == null) 0 else this.preference_69.hashCode())
        result = prime * result + (if (this.preference_70 == null) 0 else this.preference_70.hashCode())
        result = prime * result + (if (this.preference_71 == null) 0 else this.preference_71.hashCode())
        result = prime * result + (if (this.preference_72 == null) 0 else this.preference_72.hashCode())
        result = prime * result + (if (this.preference_73 == null) 0 else this.preference_73.hashCode())
        result = prime * result + (if (this.preference_74 == null) 0 else this.preference_74.hashCode())
        result = prime * result + (if (this.preference_75 == null) 0 else this.preference_75.hashCode())
        result = prime * result + (if (this.preference_76 == null) 0 else this.preference_76.hashCode())
        result = prime * result + (if (this.preference_77 == null) 0 else this.preference_77.hashCode())
        result = prime * result + (if (this.preference_78 == null) 0 else this.preference_78.hashCode())
        result = prime * result + (if (this.preference_79 == null) 0 else this.preference_79.hashCode())
        result = prime * result + (if (this.preference_80 == null) 0 else this.preference_80.hashCode())
        result = prime * result + (if (this.preference_81 == null) 0 else this.preference_81.hashCode())
        result = prime * result + (if (this.preference_82 == null) 0 else this.preference_82.hashCode())
        result = prime * result + (if (this.preference_83 == null) 0 else this.preference_83.hashCode())
        result = prime * result + (if (this.preference_84 == null) 0 else this.preference_84.hashCode())
        result = prime * result + (if (this.preference_85 == null) 0 else this.preference_85.hashCode())
        result = prime * result + (if (this.preference_86 == null) 0 else this.preference_86.hashCode())
        result = prime * result + (if (this.preference_87 == null) 0 else this.preference_87.hashCode())
        result = prime * result + (if (this.preference_88 == null) 0 else this.preference_88.hashCode())
        result = prime * result + (if (this.preference_89 == null) 0 else this.preference_89.hashCode())
        result = prime * result + (if (this.preference_90 == null) 0 else this.preference_90.hashCode())
        result = prime * result + (if (this.preference_91 == null) 0 else this.preference_91.hashCode())
        result = prime * result + (if (this.preference_92 == null) 0 else this.preference_92.hashCode())
        result = prime * result + (if (this.preference_93 == null) 0 else this.preference_93.hashCode())
        result = prime * result + (if (this.preference_94 == null) 0 else this.preference_94.hashCode())
        result = prime * result + (if (this.preference_95 == null) 0 else this.preference_95.hashCode())
        result = prime * result + (if (this.preference_96 == null) 0 else this.preference_96.hashCode())
        result = prime * result + (if (this.preference_97 == null) 0 else this.preference_97.hashCode())
        result = prime * result + (if (this.preference_98 == null) 0 else this.preference_98.hashCode())
        result = prime * result + (if (this.propertyAddress == null) 0 else this.propertyAddress.hashCode())
        result = prime * result + (if (this.propertyAddressDetail == null) 0 else this.propertyAddressDetail.hashCode())
        result = prime * result + (if (this.serviceRoomSign == null) 0 else this.serviceRoomSign.hashCode())
        result = prime * result + (if (this.highVoltageBulkReceipt == null) 0 else this.highVoltageBulkReceipt.hashCode())
        result = prime * result + (if (this.highRentalSign == null) 0 else this.highRentalSign.hashCode())
        result = prime * result + (if (this.solarDiscountTarget == null) 0 else this.solarDiscountTarget.hashCode())
        result = prime * result + (if (this.cleaningCostFixed == null) 0 else this.cleaningCostFixed.hashCode())
        result = prime * result + (if (this.previousRent == null) 0 else this.previousRent.hashCode())
        result = prime * result + (if (this.existingReviewUpdateDate == null) 0 else this.existingReviewUpdateDate.hashCode())
        result = prime * result + (if (this.moveOutInspectionTime == null) 0 else this.moveOutInspectionTime.hashCode())
        result = prime * result + (if (this.recruitmentStartDate == null) 0 else this.recruitmentStartDate.hashCode())
        result = prime * result + (if (this.cleaningCostTotal == null) 0 else this.cleaningCostTotal.hashCode())
        result = prime * result + (if (this.discountInitialValueSign == null) 0 else this.discountInitialValueSign.hashCode())
        result = prime * result + (if (this.flagReserve_7 == null) 0 else this.flagReserve_7.hashCode())
        result = prime * result + (if (this.petFlag == null) 0 else this.petFlag.hashCode())
        result = prime * result + (if (this.flagReserve_9 == null) 0 else this.flagReserve_9.hashCode())
        result = prime * result + (if (this.flagReserve_10 == null) 0 else this.flagReserve_10.hashCode())
        result = prime * result + (if (this.challengeStartDate == null) 0 else this.challengeStartDate.hashCode())
        result = prime * result + (if (this.challengeEndDate == null) 0 else this.challengeEndDate.hashCode())
        result = prime * result + (if (this.applicationEndDate == null) 0 else this.applicationEndDate.hashCode())
        result = prime * result + (if (this.moveInEndDate == null) 0 else this.moveInEndDate.hashCode())
        result = prime * result + (if (this.additionalReleaseDate == null) 0 else this.additionalReleaseDate.hashCode())
        result = prime * result + (if (this.recruitmentRent == null) 0 else this.recruitmentRent.hashCode())
        result = prime * result + (if (this.challengeAdditionalAmount == null) 0 else this.challengeAdditionalAmount.hashCode())
        result = prime * result + (if (this.reviewRent == null) 0 else this.reviewRent.hashCode())
        result = prime * result + (if (this.dateReserve_11 == null) 0 else this.dateReserve_11.hashCode())
        result = prime * result + (if (this.dateReserve_12 == null) 0 else this.dateReserve_12.hashCode())
        result = prime * result + (if (this.dateReserve_13 == null) 0 else this.dateReserve_13.hashCode())
        result = prime * result + (if (this.amountReserve_1 == null) 0 else this.amountReserve_1.hashCode())
        result = prime * result + (if (this.amountReserve_2 == null) 0 else this.amountReserve_2.hashCode())
        result = prime * result + (if (this.amountReserve_3 == null) 0 else this.amountReserve_3.hashCode())
        result = prime * result + (if (this.commonServiceFeeBase == null) 0 else this.commonServiceFeeBase.hashCode())
        result = prime * result + (if (this.generalCableTvBase == null) 0 else this.generalCableTvBase.hashCode())
        result = prime * result + (if (this.generalCableTvTax == null) 0 else this.generalCableTvTax.hashCode())
        result = prime * result + (if (this.generalInternetBase == null) 0 else this.generalInternetBase.hashCode())
        result = prime * result + (if (this.generalInternetTax == null) 0 else this.generalInternetTax.hashCode())
        result = prime * result + (if (this.generalWaterQualityBase == null) 0 else this.generalWaterQualityBase.hashCode())
        result = prime * result + (if (this.generalWaterQualityTax == null) 0 else this.generalWaterQualityTax.hashCode())
        result = prime * result + (if (this.generalTenantWaterBase == null) 0 else this.generalTenantWaterBase.hashCode())
        result = prime * result + (if (this.generalTenantWaterTax == null) 0 else this.generalTenantWaterTax.hashCode())
        result = prime * result + (if (this.generalDrainUseBase == null) 0 else this.generalDrainUseBase.hashCode())
        result = prime * result + (if (this.generalDrainUseTax == null) 0 else this.generalDrainUseTax.hashCode())
        result = prime * result + (if (this.generalGarbageCollectionBase == null) 0 else this.generalGarbageCollectionBase.hashCode())
        result = prime * result + (if (this.generalGarbageCollectionTax == null) 0 else this.generalGarbageCollectionTax.hashCode())
        result = prime * result + (if (this.generalSharedAntennaBase == null) 0 else this.generalSharedAntennaBase.hashCode())
        result = prime * result + (if (this.generalSharedAntennaTax == null) 0 else this.generalSharedAntennaTax.hashCode())
        result = prime * result + (if (this.generalOwnerCleaningBase == null) 0 else this.generalOwnerCleaningBase.hashCode())
        result = prime * result + (if (this.generalOwnerCleaningTax == null) 0 else this.generalOwnerCleaningTax.hashCode())
        result = prime * result + (if (this.generalBuildingMaintenanceBase == null) 0 else this.generalBuildingMaintenanceBase.hashCode())
        result = prime * result + (if (this.generalBuildingMaintenanceTax == null) 0 else this.generalBuildingMaintenanceTax.hashCode())
        result = prime * result + (if (this.generalBuildingManagementBase == null) 0 else this.generalBuildingManagementBase.hashCode())
        result = prime * result + (if (this.generalBuildingManagementTax == null) 0 else this.generalBuildingManagementTax.hashCode())
        result = prime * result + (if (this.generalNeighborhoodAssocBase == null) 0 else this.generalNeighborhoodAssocBase.hashCode())
        result = prime * result + (if (this.generalNeighborhoodAssocTax == null) 0 else this.generalNeighborhoodAssocTax.hashCode())
        result = prime * result + (if (this.generalNeighborhoodOtherBase == null) 0 else this.generalNeighborhoodOtherBase.hashCode())
        result = prime * result + (if (this.generalNeighborhoodOtherTax == null) 0 else this.generalNeighborhoodOtherTax.hashCode())
        result = prime * result + (if (this.generalRepaymentAgentBase == null) 0 else this.generalRepaymentAgentBase.hashCode())
        result = prime * result + (if (this.generalRepaymentAgentTax == null) 0 else this.generalRepaymentAgentTax.hashCode())
        result = prime * result + (if (this.generalHlCommissionBase == null) 0 else this.generalHlCommissionBase.hashCode())
        result = prime * result + (if (this.generalHlCommissionTax == null) 0 else this.generalHlCommissionTax.hashCode())
        result = prime * result + (if (this.generalFurnishedBase == null) 0 else this.generalFurnishedBase.hashCode())
        result = prime * result + (if (this.generalFurnishedTax == null) 0 else this.generalFurnishedTax.hashCode())
        result = prime * result + (if (this.generalTenantDepositBase == null) 0 else this.generalTenantDepositBase.hashCode())
        result = prime * result + (if (this.generalTenantDepositTax == null) 0 else this.generalTenantDepositTax.hashCode())
        result = prime * result + (if (this.generalRentalBase == null) 0 else this.generalRentalBase.hashCode())
        result = prime * result + (if (this.generalRentalTax == null) 0 else this.generalRentalTax.hashCode())
        result = prime * result + (if (this.reserveAmount_1Base == null) 0 else this.reserveAmount_1Base.hashCode())
        result = prime * result + (if (this.reserveAmount_1Tax == null) 0 else this.reserveAmount_1Tax.hashCode())
        result = prime * result + (if (this.reserveAmount_2Base == null) 0 else this.reserveAmount_2Base.hashCode())
        result = prime * result + (if (this.reserveAmount_2Tax == null) 0 else this.reserveAmount_2Tax.hashCode())
        result = prime * result + (if (this.reserveAmount_3Base == null) 0 else this.reserveAmount_3Base.hashCode())
        result = prime * result + (if (this.reserveAmount_3Tax == null) 0 else this.reserveAmount_3Tax.hashCode())
        result = prime * result + (if (this.flagReserve_11 == null) 0 else this.flagReserve_11.hashCode())
        result = prime * result + (if (this.flagReserve_12 == null) 0 else this.flagReserve_12.hashCode())
        result = prime * result + (if (this.bundleWater == null) 0 else this.bundleWater.hashCode())
        result = prime * result + (if (this.bundleElectricity == null) 0 else this.bundleElectricity.hashCode())
        result = prime * result + (if (this.bundleGas == null) 0 else this.bundleGas.hashCode())
        result = prime * result + (if (this.category_2digitReserve_1 == null) 0 else this.category_2digitReserve_1.hashCode())
        result = prime * result + (if (this.category_2digitReserve_2 == null) 0 else this.category_2digitReserve_2.hashCode())
        result = prime * result + (if (this.category_2digitReserve_3 == null) 0 else this.category_2digitReserve_3.hashCode())
        result = prime * result + (if (this.category_2digitReserve_4 == null) 0 else this.category_2digitReserve_4.hashCode())
        result = prime * result + (if (this.category_2digitReserve_5 == null) 0 else this.category_2digitReserve_5.hashCode())
        result = prime * result + (if (this.amountReserve_4 == null) 0 else this.amountReserve_4.hashCode())
        result = prime * result + (if (this.amountReserve_5 == null) 0 else this.amountReserve_5.hashCode())
        result = prime * result + (if (this.amountReserve_6 == null) 0 else this.amountReserve_6.hashCode())
        result = prime * result + (if (this.amountReserve_7 == null) 0 else this.amountReserve_7.hashCode())
        result = prime * result + (if (this.amountReserve_8 == null) 0 else this.amountReserve_8.hashCode())
        result = prime * result + (if (this.dateReserve_14 == null) 0 else this.dateReserve_14.hashCode())
        result = prime * result + (if (this.dateReserve_15 == null) 0 else this.dateReserve_15.hashCode())
        result = prime * result + (if (this.dateReserve_16 == null) 0 else this.dateReserve_16.hashCode())
        result = prime * result + (if (this.dateReserve_17 == null) 0 else this.dateReserve_17.hashCode())
        result = prime * result + (if (this.dateReserve_18 == null) 0 else this.dateReserve_18.hashCode())
        result = prime * result + (if (this.category_1digitReserve_1 == null) 0 else this.category_1digitReserve_1.hashCode())
        result = prime * result + (if (this.category_1digitReserve_2 == null) 0 else this.category_1digitReserve_2.hashCode())
        result = prime * result + (if (this.category_1digitReserve_3 == null) 0 else this.category_1digitReserve_3.hashCode())
        result = prime * result + (if (this.category_1digitReserve_4 == null) 0 else this.category_1digitReserve_4.hashCode())
        result = prime * result + (if (this.category_1digitReserve_5 == null) 0 else this.category_1digitReserve_5.hashCode())
        result = prime * result + (if (this.leasingStoreCd == null) 0 else this.leasingStoreCd.hashCode())
        result = prime * result + (if (this.managementBranchCd == null) 0 else this.managementBranchCd.hashCode())
        result = prime * result + (if (this.salesOfficeCd == null) 0 else this.salesOfficeCd.hashCode())
        result = prime * result + (if (this.screeningBranchCd == null) 0 else this.screeningBranchCd.hashCode())
        result = prime * result + (if (this.preference_100 == null) 0 else this.preference_100.hashCode())
        result = prime * result + (if (this.preference_101 == null) 0 else this.preference_101.hashCode())
        result = prime * result + (if (this.preference_102 == null) 0 else this.preference_102.hashCode())
        result = prime * result + (if (this.preference_103 == null) 0 else this.preference_103.hashCode())
        result = prime * result + (if (this.preference_104 == null) 0 else this.preference_104.hashCode())
        result = prime * result + (if (this.preference_105 == null) 0 else this.preference_105.hashCode())
        result = prime * result + (if (this.preference_106 == null) 0 else this.preference_106.hashCode())
        result = prime * result + (if (this.preference_107 == null) 0 else this.preference_107.hashCode())
        result = prime * result + (if (this.preference_108 == null) 0 else this.preference_108.hashCode())
        result = prime * result + (if (this.preference_109 == null) 0 else this.preference_109.hashCode())
        result = prime * result + (if (this.preference_110 == null) 0 else this.preference_110.hashCode())
        result = prime * result + (if (this.preference_111 == null) 0 else this.preference_111.hashCode())
        result = prime * result + (if (this.preference_112 == null) 0 else this.preference_112.hashCode())
        result = prime * result + (if (this.preference_113 == null) 0 else this.preference_113.hashCode())
        result = prime * result + (if (this.preference_114 == null) 0 else this.preference_114.hashCode())
        result = prime * result + (if (this.preference_115 == null) 0 else this.preference_115.hashCode())
        result = prime * result + (if (this.preference_116 == null) 0 else this.preference_116.hashCode())
        result = prime * result + (if (this.preference_117 == null) 0 else this.preference_117.hashCode())
        result = prime * result + (if (this.preference_118 == null) 0 else this.preference_118.hashCode())
        result = prime * result + (if (this.preference_119 == null) 0 else this.preference_119.hashCode())
        result = prime * result + (if (this.preference_120 == null) 0 else this.preference_120.hashCode())
        result = prime * result + (if (this.preference_121 == null) 0 else this.preference_121.hashCode())
        result = prime * result + (if (this.preference_122 == null) 0 else this.preference_122.hashCode())
        result = prime * result + (if (this.preference_123 == null) 0 else this.preference_123.hashCode())
        result = prime * result + (if (this.preference_124 == null) 0 else this.preference_124.hashCode())
        result = prime * result + (if (this.preference_125 == null) 0 else this.preference_125.hashCode())
        result = prime * result + (if (this.preference_126 == null) 0 else this.preference_126.hashCode())
        result = prime * result + (if (this.preference_127 == null) 0 else this.preference_127.hashCode())
        result = prime * result + (if (this.preference_128 == null) 0 else this.preference_128.hashCode())
        result = prime * result + (if (this.preference_129 == null) 0 else this.preference_129.hashCode())
        result = prime * result + (if (this.preference_130 == null) 0 else this.preference_130.hashCode())
        result = prime * result + (if (this.preference_131 == null) 0 else this.preference_131.hashCode())
        result = prime * result + (if (this.preference_132 == null) 0 else this.preference_132.hashCode())
        result = prime * result + (if (this.preference_133 == null) 0 else this.preference_133.hashCode())
        result = prime * result + (if (this.preference_134 == null) 0 else this.preference_134.hashCode())
        result = prime * result + (if (this.preference_135 == null) 0 else this.preference_135.hashCode())
        result = prime * result + (if (this.preference_136 == null) 0 else this.preference_136.hashCode())
        result = prime * result + (if (this.preference_137 == null) 0 else this.preference_137.hashCode())
        result = prime * result + (if (this.preference_138 == null) 0 else this.preference_138.hashCode())
        result = prime * result + (if (this.preference_139 == null) 0 else this.preference_139.hashCode())
        result = prime * result + (if (this.preference_140 == null) 0 else this.preference_140.hashCode())
        result = prime * result + (if (this.preference_141 == null) 0 else this.preference_141.hashCode())
        result = prime * result + (if (this.preference_142 == null) 0 else this.preference_142.hashCode())
        result = prime * result + (if (this.preference_143 == null) 0 else this.preference_143.hashCode())
        result = prime * result + (if (this.preference_144 == null) 0 else this.preference_144.hashCode())
        result = prime * result + (if (this.preference_145 == null) 0 else this.preference_145.hashCode())
        result = prime * result + (if (this.preference_146 == null) 0 else this.preference_146.hashCode())
        result = prime * result + (if (this.preference_147 == null) 0 else this.preference_147.hashCode())
        result = prime * result + (if (this.preference_148 == null) 0 else this.preference_148.hashCode())
        result = prime * result + (if (this.preference_149 == null) 0 else this.preference_149.hashCode())
        result = prime * result + (if (this.preference_150 == null) 0 else this.preference_150.hashCode())
        result = prime * result + (if (this.preference_151 == null) 0 else this.preference_151.hashCode())
        result = prime * result + (if (this.preference_152 == null) 0 else this.preference_152.hashCode())
        result = prime * result + (if (this.preference_153 == null) 0 else this.preference_153.hashCode())
        result = prime * result + (if (this.preference_154 == null) 0 else this.preference_154.hashCode())
        result = prime * result + (if (this.preference_155 == null) 0 else this.preference_155.hashCode())
        result = prime * result + (if (this.preference_156 == null) 0 else this.preference_156.hashCode())
        result = prime * result + (if (this.preference_157 == null) 0 else this.preference_157.hashCode())
        result = prime * result + (if (this.preference_158 == null) 0 else this.preference_158.hashCode())
        result = prime * result + (if (this.preference_159 == null) 0 else this.preference_159.hashCode())
        result = prime * result + (if (this.preference_160 == null) 0 else this.preference_160.hashCode())
        result = prime * result + (if (this.preference_161 == null) 0 else this.preference_161.hashCode())
        result = prime * result + (if (this.preference_162 == null) 0 else this.preference_162.hashCode())
        result = prime * result + (if (this.preference_163 == null) 0 else this.preference_163.hashCode())
        result = prime * result + (if (this.preference_164 == null) 0 else this.preference_164.hashCode())
        result = prime * result + (if (this.preference_165 == null) 0 else this.preference_165.hashCode())
        result = prime * result + (if (this.preference_166 == null) 0 else this.preference_166.hashCode())
        result = prime * result + (if (this.preference_167 == null) 0 else this.preference_167.hashCode())
        result = prime * result + (if (this.preference_168 == null) 0 else this.preference_168.hashCode())
        result = prime * result + (if (this.preference_169 == null) 0 else this.preference_169.hashCode())
        result = prime * result + (if (this.preference_170 == null) 0 else this.preference_170.hashCode())
        result = prime * result + (if (this.preference_171 == null) 0 else this.preference_171.hashCode())
        result = prime * result + (if (this.preference_172 == null) 0 else this.preference_172.hashCode())
        result = prime * result + (if (this.preference_173 == null) 0 else this.preference_173.hashCode())
        result = prime * result + (if (this.preference_174 == null) 0 else this.preference_174.hashCode())
        result = prime * result + (if (this.preference_175 == null) 0 else this.preference_175.hashCode())
        result = prime * result + (if (this.preference_176 == null) 0 else this.preference_176.hashCode())
        result = prime * result + (if (this.preference_177 == null) 0 else this.preference_177.hashCode())
        result = prime * result + (if (this.preference_178 == null) 0 else this.preference_178.hashCode())
        result = prime * result + (if (this.preference_179 == null) 0 else this.preference_179.hashCode())
        result = prime * result + (if (this.preference_180 == null) 0 else this.preference_180.hashCode())
        result = prime * result + (if (this.preference_181 == null) 0 else this.preference_181.hashCode())
        result = prime * result + (if (this.preference_182 == null) 0 else this.preference_182.hashCode())
        result = prime * result + (if (this.preference_183 == null) 0 else this.preference_183.hashCode())
        result = prime * result + (if (this.preference_184 == null) 0 else this.preference_184.hashCode())
        result = prime * result + (if (this.preference_185 == null) 0 else this.preference_185.hashCode())
        result = prime * result + (if (this.preference_186 == null) 0 else this.preference_186.hashCode())
        result = prime * result + (if (this.preference_187 == null) 0 else this.preference_187.hashCode())
        result = prime * result + (if (this.preference_188 == null) 0 else this.preference_188.hashCode())
        result = prime * result + (if (this.preference_189 == null) 0 else this.preference_189.hashCode())
        result = prime * result + (if (this.preference_190 == null) 0 else this.preference_190.hashCode())
        result = prime * result + (if (this.preference_191 == null) 0 else this.preference_191.hashCode())
        result = prime * result + (if (this.preference_192 == null) 0 else this.preference_192.hashCode())
        result = prime * result + (if (this.preference_193 == null) 0 else this.preference_193.hashCode())
        result = prime * result + (if (this.preference_194 == null) 0 else this.preference_194.hashCode())
        result = prime * result + (if (this.preference_195 == null) 0 else this.preference_195.hashCode())
        result = prime * result + (if (this.preference_196 == null) 0 else this.preference_196.hashCode())
        result = prime * result + (if (this.preference_197 == null) 0 else this.preference_197.hashCode())
        result = prime * result + (if (this.preference_198 == null) 0 else this.preference_198.hashCode())
        result = prime * result + (if (this.preference_199 == null) 0 else this.preference_199.hashCode())
        result = prime * result + (if (this.marketingBranchOfficeCd == null) 0 else this.marketingBranchOfficeCd.hashCode())
        result = prime * result + (if (this.propertySituation == null) 0 else this.propertySituation.hashCode())
        result = prime * result + (if (this.prefectureCityCd == null) 0 else this.prefectureCityCd.hashCode())
        return result
    }
}
