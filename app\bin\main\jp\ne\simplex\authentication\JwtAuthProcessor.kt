package jp.ne.simplex.authentication

import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.repository.aws.SecretManagerRepository
import jp.ne.simplex.authentication.AuthTokenType.ACCESS_TOKEN
import jp.ne.simplex.authentication.AuthTokenType.REFRESH_TOKEN
import jp.ne.simplex.shared.StringExtension.Companion.toMap
import org.springframework.stereotype.Component
import java.time.LocalDateTime

@Component
class JwtAuthProcessor(
    private val authConfig: AuthConfig,
    private val secretManager: SecretManagerRepository,
) {
    fun gen(employee: Employee): JwtAuthToken {
        val currentDateTime = LocalDateTime.now()

        return JwtAuthToken(
            generateToken(
                employee,
                currentDateTime,
                currentDateTime.plusSeconds(getPeriod(ACCESS_TOKEN)),
                getSecretKey(ACCESS_TOKEN),
            ),
            generateToken(
                employee,
                currentDateTime,
                currentDateTime.plusSeconds(getPeriod(REFRESH_TOKEN)),
                getSecretKey(REFRESH_TOKEN),
            ),
        )
    }

    fun verify(tokenType: AuthTokenType, tokenValue: String): AuthInfo.Jwt {
        return CustomJwt
            .verifier(getSecretKey(tokenType))
            .withClaimPresence()
            .verify(tokenValue)
    }

    private fun generateToken(
        employee: Employee,
        currentDateTime: LocalDateTime,
        expireDateTime: LocalDateTime,
        secretKey: String,
    ): String {
        return CustomJwt
            .builder()
            .withClaim(employee)
            .withMeta(currentDateTime, expireDateTime)
            .sign(secretKey)
    }

    private fun getSecretKey(tokenType: AuthTokenType): String {
        val secretId = when (tokenType) {
            ACCESS_TOKEN -> authConfig.jwt.accessToken.secretId
            REFRESH_TOKEN -> authConfig.jwt.refreshToken.secretId
        }

        return secretManager.getValue(secretId).toMap()["jwt_key"] as String
    }

    private fun getPeriod(tokenType: AuthTokenType): Long {
        return when (tokenType) {
            ACCESS_TOKEN -> authConfig.jwt.accessToken.validityPeriod
            REFRESH_TOKEN -> authConfig.jwt.refreshToken.validityPeriod
        }
    }

}

enum class AuthTokenType {
    ACCESS_TOKEN,
    REFRESH_TOKEN;
}
