/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.FixedItemFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.FixedItemFilePojo

import org.jooq.impl.TableRecordImpl


/**
 * 固定項目ファイル 既存システム物理名: HVX10P
 */
@Suppress("UNCHECKED_CAST")
open class FixedItemFileRecord private constructor() : TableRecordImpl<FixedItemFileRecord>(FixedItemFileTable.FIXED_ITEM_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creationProgram: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var creationTerminalId: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var creationResponsibleCd: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateDate: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var updateTime: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var updateProgram: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var updateTerminalId: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var updateResponsibleCd: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(10, value)
        get(): Byte? = get(10) as Byte?

    open var itemId: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var key1: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var key2: Long?
        set(value): Unit = set(13, value)
        get(): Long? = get(13) as Long?

    open var dataKanji1: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var dataKanji2: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var dataString1: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var dataString2: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var dataNumber: Long?
        set(value): Unit = set(18, value)
        get(): Long? = get(18) as Long?

    open var dataDecimal4v3: BigDecimal?
        set(value): Unit = set(19, value)
        get(): BigDecimal? = get(19) as BigDecimal?

    open var itemName: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    /**
     * Create a detached, initialised FixedItemFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creationProgram: String? = null, creationTerminalId: String? = null, creationResponsibleCd: String? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgram: String? = null, updateTerminalId: String? = null, updateResponsibleCd: String? = null, logicalDeleteSign: Byte? = null, itemId: String? = null, key1: String? = null, key2: Long? = null, dataKanji1: String? = null, dataKanji2: String? = null, dataString1: String? = null, dataString2: String? = null, dataNumber: Long? = null, dataDecimal4v3: BigDecimal? = null, itemName: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creationProgram = creationProgram
        this.creationTerminalId = creationTerminalId
        this.creationResponsibleCd = creationResponsibleCd
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgram = updateProgram
        this.updateTerminalId = updateTerminalId
        this.updateResponsibleCd = updateResponsibleCd
        this.logicalDeleteSign = logicalDeleteSign
        this.itemId = itemId
        this.key1 = key1
        this.key2 = key2
        this.dataKanji1 = dataKanji1
        this.dataKanji2 = dataKanji2
        this.dataString1 = dataString1
        this.dataString2 = dataString2
        this.dataNumber = dataNumber
        this.dataDecimal4v3 = dataDecimal4v3
        this.itemName = itemName
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised FixedItemFileRecord
     */
    constructor(value: FixedItemFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creationProgram = value.creationProgram
            this.creationTerminalId = value.creationTerminalId
            this.creationResponsibleCd = value.creationResponsibleCd
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgram = value.updateProgram
            this.updateTerminalId = value.updateTerminalId
            this.updateResponsibleCd = value.updateResponsibleCd
            this.logicalDeleteSign = value.logicalDeleteSign
            this.itemId = value.itemId
            this.key1 = value.key1
            this.key2 = value.key2
            this.dataKanji1 = value.dataKanji1
            this.dataKanji2 = value.dataKanji2
            this.dataString1 = value.dataString1
            this.dataString2 = value.dataString2
            this.dataNumber = value.dataNumber
            this.dataDecimal4v3 = value.dataDecimal4v3
            this.itemName = value.itemName
            resetChangedOnNotNull()
        }
    }
}
