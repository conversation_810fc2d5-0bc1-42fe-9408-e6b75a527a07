#!/bin/bash
# shellcheck disable=SC2164

export DB_NAME='gpb'
export POSTGRES_USER='postgres'
export POSTGRES_PASSWORD='simplex'
export APP_USER='app'
export APP_USER_PASSWORD='simplex'
export OPE_USER='ope'
export OPE_USER_PASSWORD='simplex'
export READONLY_USER='read-only'
export READONLY_USER_PASSWORD='simplex'
export DATA_TABLESPACE='pg_default'
export IDX_TABLESPACE='pg_default'

export DBHOST='stg2-propetech-infra-rdsaurora6c66f7da-8dm2j4kdugnb.cluster-cfseiag02r46.ap-northeast-1.rds.amazonaws.com'
export DBPORT='5432'
