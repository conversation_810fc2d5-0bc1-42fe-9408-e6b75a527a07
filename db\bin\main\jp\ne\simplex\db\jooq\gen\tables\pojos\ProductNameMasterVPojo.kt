/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * This class is generated by jOOQ.
 */
@Suppress("UNCHECKED_CAST")
data class ProductNameMasterVPojo(
    var productNameCode: Short? = null,
    var productName: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: ProductNameMasterVPojo = other as ProductNameMasterVPojo
        if (this.productNameCode == null) {
            if (o.productNameCode != null)
                return false
        }
        else if (this.productNameCode != o.productNameCode)
            return false
        if (this.productName == null) {
            if (o.productName != null)
                return false
        }
        else if (this.productName != o.productName)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.productNameCode == null) 0 else this.productNameCode.hashCode())
        result = prime * result + (if (this.productName == null) 0 else this.productName.hashCode())
        return result
    }
}
