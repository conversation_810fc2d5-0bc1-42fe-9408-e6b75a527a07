-- TABLE: RAILWAY_LINE_MASTER(沿線マスタ)

CREATE TABLE RAILWAY_LINE_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LINE_CODE                                    varchar(4)        NOT NULL    
,    LINE_NAME                                    varchar(20)                   
,    LINE_SHORT_NAME                              varchar(12)                   
,    LINE_NAME_KANA                               varchar(28)                   
,    START_YEAR_MONTH                             numeric(6,0)                  
,    END_YEAR_MONTH                               numeric(6,0)                  
,    BLOCK_CODE                                   varchar(1)                    
,    LINE_CODE_3_DIGIT                            varchar(3)                    
,    CONSTRAINT PK_RAILWAY_LINE_MASTER PRIMARY KEY (LINE_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE RAILWAY_LINE_MASTER IS '沿線マスタ 既存システム物理名: EZE51P';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: EZE01D';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: EZE02H';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: EZE03D';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: EZE04H';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EZE05N';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.UPDATER IS '更新者 既存システム物理名: EZE06C';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.LINE_CODE IS '沿線コード 既存システム物理名: EZEAPC';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.LINE_NAME IS '沿線名 既存システム物理名: EZE11M';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.LINE_SHORT_NAME IS '沿線略称名 既存システム物理名: EZE12M';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.LINE_NAME_KANA IS '沿線名カナ読み 既存システム物理名: EZE13M';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.START_YEAR_MONTH IS '開始年月 既存システム物理名: EZE14D';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.END_YEAR_MONTH IS '終了年月 既存システム物理名: EZE15D';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.BLOCK_CODE IS 'ブロックコード 既存システム物理名: EZE16C';
COMMENT ON COLUMN RAILWAY_LINE_MASTER.LINE_CODE_3_DIGIT IS '沿線コード(3桁) 既存システム物理名: EZE17C';
