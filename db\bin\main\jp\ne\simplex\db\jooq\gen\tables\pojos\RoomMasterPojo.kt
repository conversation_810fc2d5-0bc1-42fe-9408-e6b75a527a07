/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable
import java.math.BigDecimal


/**
 * 部屋マスタ 既存システム物理名: ECNE0P
 */
@Suppress("UNCHECKED_CAST")
data class RoomMasterPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updater: String? = null,
    var logicalDeleteSign: Byte? = null,
    var buildingCode: String? = null,
    var roomCode: String? = null,
    var roomNumber: String? = null,
    var roomParentChildCode: String? = null,
    var ownerCode_10: String? = null,
    var bulkLeaseSign: Byte? = null,
    var setPropertySign: Byte? = null,
    var declarationRequestCollectionSign: Byte? = null,
    var layoutCategory: String? = null,
    var layoutDetails: String? = null,
    var floorNumber: Short? = null,
    var occupancyMediationSign: Byte? = null,
    var occupantCategory: String? = null,
    var mutualAidAssociationSign: Byte? = null,
    var mutualAidAssociationReferralCount: Byte? = null,
    var mutualAidAssocCashReceiptAmount: Int? = null,
    var managementSign: Byte? = null,
    var managementUnitCategory: String? = null,
    var referralCount: Byte? = null,
    var managementContractCashReceiptAmount: Int? = null,
    var initialAssessmentReviewNumber: String? = null,
    var officeFloorAreaSquareMeters: BigDecimal? = null,
    var residentialAreaSquareMeters: BigDecimal? = null,
    var parkingAvailabilitySign: Byte? = null,
    var initialOccupancyDate: Int? = null,
    var occupancyStatusSign: Byte? = null,
    var expectedAvailableDate: Int? = null,
    var storeExclusiveAreaSquareMeters: BigDecimal? = null,
    var balconyArea: BigDecimal? = null,
    var roomPositionCategory: String? = null,
    var orientationCategory: String? = null,
    var roomType: String? = null,
    var studioRoomSign: Byte? = null,
    var serviceRoomSign: Byte? = null,
    var privateGardenAvailabilitySign: Byte? = null,
    var underfloorStorageAvailabilitySign: Byte? = null,
    var roomEquipmentCode: String? = null,
    var roomFeatureCode: String? = null,
    var newOrUsedCategory: String? = null,
    var currentlyAvailableSign: Byte? = null,
    var remainingCollectionDate: Int? = null,
    var frontFreeRentSign: Byte? = null,
    var frontFreeRentMonths: Short? = null,
    var frontFreeRentAmount: Int? = null,
    var additionalAdvertisingMonths: BigDecimal? = null,
    var rentalAssessmentStatusCategory: String? = null,
    var priorityInformationCode: String? = null,
    var assessmentReviewNumber: String? = null,
    var firstFloorAreaSquareMeters: BigDecimal? = null,
    var secondFloorAreaSquareMeters: BigDecimal? = null,
    var thirdFloorAreaSquareMeters: BigDecimal? = null,
    var interfaceSign: Byte? = null,
    var referralExtractionRequiredSign: Byte? = null,
    var dataMigrationKey_1: String? = null,
    var dataMigrationKey_2: String? = null,
    var roomChangedSign: Byte? = null,
    var rocky: String? = null,
    var categoryA: String? = null,
    var categoryB: String? = null,
    var equipmentCode: String? = null,
    var newGuaranteeRate: BigDecimal? = null,
    var newManagementGuaranteeRate: BigDecimal? = null,
    var contractMutualAidFeeRate: BigDecimal? = null,
    var revivalTargetRoom: Byte? = null,
    var specialPreferredRentCategory: String? = null,
    var nonStandardCategory: Byte? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: RoomMasterPojo = other as RoomMasterPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.logicalDeleteSign == null) {
            if (o.logicalDeleteSign != null)
                return false
        }
        else if (this.logicalDeleteSign != o.logicalDeleteSign)
            return false
        if (this.buildingCode == null) {
            if (o.buildingCode != null)
                return false
        }
        else if (this.buildingCode != o.buildingCode)
            return false
        if (this.roomCode == null) {
            if (o.roomCode != null)
                return false
        }
        else if (this.roomCode != o.roomCode)
            return false
        if (this.roomNumber == null) {
            if (o.roomNumber != null)
                return false
        }
        else if (this.roomNumber != o.roomNumber)
            return false
        if (this.roomParentChildCode == null) {
            if (o.roomParentChildCode != null)
                return false
        }
        else if (this.roomParentChildCode != o.roomParentChildCode)
            return false
        if (this.ownerCode_10 == null) {
            if (o.ownerCode_10 != null)
                return false
        }
        else if (this.ownerCode_10 != o.ownerCode_10)
            return false
        if (this.bulkLeaseSign == null) {
            if (o.bulkLeaseSign != null)
                return false
        }
        else if (this.bulkLeaseSign != o.bulkLeaseSign)
            return false
        if (this.setPropertySign == null) {
            if (o.setPropertySign != null)
                return false
        }
        else if (this.setPropertySign != o.setPropertySign)
            return false
        if (this.declarationRequestCollectionSign == null) {
            if (o.declarationRequestCollectionSign != null)
                return false
        }
        else if (this.declarationRequestCollectionSign != o.declarationRequestCollectionSign)
            return false
        if (this.layoutCategory == null) {
            if (o.layoutCategory != null)
                return false
        }
        else if (this.layoutCategory != o.layoutCategory)
            return false
        if (this.layoutDetails == null) {
            if (o.layoutDetails != null)
                return false
        }
        else if (this.layoutDetails != o.layoutDetails)
            return false
        if (this.floorNumber == null) {
            if (o.floorNumber != null)
                return false
        }
        else if (this.floorNumber != o.floorNumber)
            return false
        if (this.occupancyMediationSign == null) {
            if (o.occupancyMediationSign != null)
                return false
        }
        else if (this.occupancyMediationSign != o.occupancyMediationSign)
            return false
        if (this.occupantCategory == null) {
            if (o.occupantCategory != null)
                return false
        }
        else if (this.occupantCategory != o.occupantCategory)
            return false
        if (this.mutualAidAssociationSign == null) {
            if (o.mutualAidAssociationSign != null)
                return false
        }
        else if (this.mutualAidAssociationSign != o.mutualAidAssociationSign)
            return false
        if (this.mutualAidAssociationReferralCount == null) {
            if (o.mutualAidAssociationReferralCount != null)
                return false
        }
        else if (this.mutualAidAssociationReferralCount != o.mutualAidAssociationReferralCount)
            return false
        if (this.mutualAidAssocCashReceiptAmount == null) {
            if (o.mutualAidAssocCashReceiptAmount != null)
                return false
        }
        else if (this.mutualAidAssocCashReceiptAmount != o.mutualAidAssocCashReceiptAmount)
            return false
        if (this.managementSign == null) {
            if (o.managementSign != null)
                return false
        }
        else if (this.managementSign != o.managementSign)
            return false
        if (this.managementUnitCategory == null) {
            if (o.managementUnitCategory != null)
                return false
        }
        else if (this.managementUnitCategory != o.managementUnitCategory)
            return false
        if (this.referralCount == null) {
            if (o.referralCount != null)
                return false
        }
        else if (this.referralCount != o.referralCount)
            return false
        if (this.managementContractCashReceiptAmount == null) {
            if (o.managementContractCashReceiptAmount != null)
                return false
        }
        else if (this.managementContractCashReceiptAmount != o.managementContractCashReceiptAmount)
            return false
        if (this.initialAssessmentReviewNumber == null) {
            if (o.initialAssessmentReviewNumber != null)
                return false
        }
        else if (this.initialAssessmentReviewNumber != o.initialAssessmentReviewNumber)
            return false
        if (this.officeFloorAreaSquareMeters == null) {
            if (o.officeFloorAreaSquareMeters != null)
                return false
        }
        else if (this.officeFloorAreaSquareMeters != o.officeFloorAreaSquareMeters)
            return false
        if (this.residentialAreaSquareMeters == null) {
            if (o.residentialAreaSquareMeters != null)
                return false
        }
        else if (this.residentialAreaSquareMeters != o.residentialAreaSquareMeters)
            return false
        if (this.parkingAvailabilitySign == null) {
            if (o.parkingAvailabilitySign != null)
                return false
        }
        else if (this.parkingAvailabilitySign != o.parkingAvailabilitySign)
            return false
        if (this.initialOccupancyDate == null) {
            if (o.initialOccupancyDate != null)
                return false
        }
        else if (this.initialOccupancyDate != o.initialOccupancyDate)
            return false
        if (this.occupancyStatusSign == null) {
            if (o.occupancyStatusSign != null)
                return false
        }
        else if (this.occupancyStatusSign != o.occupancyStatusSign)
            return false
        if (this.expectedAvailableDate == null) {
            if (o.expectedAvailableDate != null)
                return false
        }
        else if (this.expectedAvailableDate != o.expectedAvailableDate)
            return false
        if (this.storeExclusiveAreaSquareMeters == null) {
            if (o.storeExclusiveAreaSquareMeters != null)
                return false
        }
        else if (this.storeExclusiveAreaSquareMeters != o.storeExclusiveAreaSquareMeters)
            return false
        if (this.balconyArea == null) {
            if (o.balconyArea != null)
                return false
        }
        else if (this.balconyArea != o.balconyArea)
            return false
        if (this.roomPositionCategory == null) {
            if (o.roomPositionCategory != null)
                return false
        }
        else if (this.roomPositionCategory != o.roomPositionCategory)
            return false
        if (this.orientationCategory == null) {
            if (o.orientationCategory != null)
                return false
        }
        else if (this.orientationCategory != o.orientationCategory)
            return false
        if (this.roomType == null) {
            if (o.roomType != null)
                return false
        }
        else if (this.roomType != o.roomType)
            return false
        if (this.studioRoomSign == null) {
            if (o.studioRoomSign != null)
                return false
        }
        else if (this.studioRoomSign != o.studioRoomSign)
            return false
        if (this.serviceRoomSign == null) {
            if (o.serviceRoomSign != null)
                return false
        }
        else if (this.serviceRoomSign != o.serviceRoomSign)
            return false
        if (this.privateGardenAvailabilitySign == null) {
            if (o.privateGardenAvailabilitySign != null)
                return false
        }
        else if (this.privateGardenAvailabilitySign != o.privateGardenAvailabilitySign)
            return false
        if (this.underfloorStorageAvailabilitySign == null) {
            if (o.underfloorStorageAvailabilitySign != null)
                return false
        }
        else if (this.underfloorStorageAvailabilitySign != o.underfloorStorageAvailabilitySign)
            return false
        if (this.roomEquipmentCode == null) {
            if (o.roomEquipmentCode != null)
                return false
        }
        else if (this.roomEquipmentCode != o.roomEquipmentCode)
            return false
        if (this.roomFeatureCode == null) {
            if (o.roomFeatureCode != null)
                return false
        }
        else if (this.roomFeatureCode != o.roomFeatureCode)
            return false
        if (this.newOrUsedCategory == null) {
            if (o.newOrUsedCategory != null)
                return false
        }
        else if (this.newOrUsedCategory != o.newOrUsedCategory)
            return false
        if (this.currentlyAvailableSign == null) {
            if (o.currentlyAvailableSign != null)
                return false
        }
        else if (this.currentlyAvailableSign != o.currentlyAvailableSign)
            return false
        if (this.remainingCollectionDate == null) {
            if (o.remainingCollectionDate != null)
                return false
        }
        else if (this.remainingCollectionDate != o.remainingCollectionDate)
            return false
        if (this.frontFreeRentSign == null) {
            if (o.frontFreeRentSign != null)
                return false
        }
        else if (this.frontFreeRentSign != o.frontFreeRentSign)
            return false
        if (this.frontFreeRentMonths == null) {
            if (o.frontFreeRentMonths != null)
                return false
        }
        else if (this.frontFreeRentMonths != o.frontFreeRentMonths)
            return false
        if (this.frontFreeRentAmount == null) {
            if (o.frontFreeRentAmount != null)
                return false
        }
        else if (this.frontFreeRentAmount != o.frontFreeRentAmount)
            return false
        if (this.additionalAdvertisingMonths == null) {
            if (o.additionalAdvertisingMonths != null)
                return false
        }
        else if (this.additionalAdvertisingMonths != o.additionalAdvertisingMonths)
            return false
        if (this.rentalAssessmentStatusCategory == null) {
            if (o.rentalAssessmentStatusCategory != null)
                return false
        }
        else if (this.rentalAssessmentStatusCategory != o.rentalAssessmentStatusCategory)
            return false
        if (this.priorityInformationCode == null) {
            if (o.priorityInformationCode != null)
                return false
        }
        else if (this.priorityInformationCode != o.priorityInformationCode)
            return false
        if (this.assessmentReviewNumber == null) {
            if (o.assessmentReviewNumber != null)
                return false
        }
        else if (this.assessmentReviewNumber != o.assessmentReviewNumber)
            return false
        if (this.firstFloorAreaSquareMeters == null) {
            if (o.firstFloorAreaSquareMeters != null)
                return false
        }
        else if (this.firstFloorAreaSquareMeters != o.firstFloorAreaSquareMeters)
            return false
        if (this.secondFloorAreaSquareMeters == null) {
            if (o.secondFloorAreaSquareMeters != null)
                return false
        }
        else if (this.secondFloorAreaSquareMeters != o.secondFloorAreaSquareMeters)
            return false
        if (this.thirdFloorAreaSquareMeters == null) {
            if (o.thirdFloorAreaSquareMeters != null)
                return false
        }
        else if (this.thirdFloorAreaSquareMeters != o.thirdFloorAreaSquareMeters)
            return false
        if (this.interfaceSign == null) {
            if (o.interfaceSign != null)
                return false
        }
        else if (this.interfaceSign != o.interfaceSign)
            return false
        if (this.referralExtractionRequiredSign == null) {
            if (o.referralExtractionRequiredSign != null)
                return false
        }
        else if (this.referralExtractionRequiredSign != o.referralExtractionRequiredSign)
            return false
        if (this.dataMigrationKey_1 == null) {
            if (o.dataMigrationKey_1 != null)
                return false
        }
        else if (this.dataMigrationKey_1 != o.dataMigrationKey_1)
            return false
        if (this.dataMigrationKey_2 == null) {
            if (o.dataMigrationKey_2 != null)
                return false
        }
        else if (this.dataMigrationKey_2 != o.dataMigrationKey_2)
            return false
        if (this.roomChangedSign == null) {
            if (o.roomChangedSign != null)
                return false
        }
        else if (this.roomChangedSign != o.roomChangedSign)
            return false
        if (this.rocky == null) {
            if (o.rocky != null)
                return false
        }
        else if (this.rocky != o.rocky)
            return false
        if (this.categoryA == null) {
            if (o.categoryA != null)
                return false
        }
        else if (this.categoryA != o.categoryA)
            return false
        if (this.categoryB == null) {
            if (o.categoryB != null)
                return false
        }
        else if (this.categoryB != o.categoryB)
            return false
        if (this.equipmentCode == null) {
            if (o.equipmentCode != null)
                return false
        }
        else if (this.equipmentCode != o.equipmentCode)
            return false
        if (this.newGuaranteeRate == null) {
            if (o.newGuaranteeRate != null)
                return false
        }
        else if (this.newGuaranteeRate != o.newGuaranteeRate)
            return false
        if (this.newManagementGuaranteeRate == null) {
            if (o.newManagementGuaranteeRate != null)
                return false
        }
        else if (this.newManagementGuaranteeRate != o.newManagementGuaranteeRate)
            return false
        if (this.contractMutualAidFeeRate == null) {
            if (o.contractMutualAidFeeRate != null)
                return false
        }
        else if (this.contractMutualAidFeeRate != o.contractMutualAidFeeRate)
            return false
        if (this.revivalTargetRoom == null) {
            if (o.revivalTargetRoom != null)
                return false
        }
        else if (this.revivalTargetRoom != o.revivalTargetRoom)
            return false
        if (this.specialPreferredRentCategory == null) {
            if (o.specialPreferredRentCategory != null)
                return false
        }
        else if (this.specialPreferredRentCategory != o.specialPreferredRentCategory)
            return false
        if (this.nonStandardCategory == null) {
            if (o.nonStandardCategory != null)
                return false
        }
        else if (this.nonStandardCategory != o.nonStandardCategory)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.logicalDeleteSign == null) 0 else this.logicalDeleteSign.hashCode())
        result = prime * result + (if (this.buildingCode == null) 0 else this.buildingCode.hashCode())
        result = prime * result + (if (this.roomCode == null) 0 else this.roomCode.hashCode())
        result = prime * result + (if (this.roomNumber == null) 0 else this.roomNumber.hashCode())
        result = prime * result + (if (this.roomParentChildCode == null) 0 else this.roomParentChildCode.hashCode())
        result = prime * result + (if (this.ownerCode_10 == null) 0 else this.ownerCode_10.hashCode())
        result = prime * result + (if (this.bulkLeaseSign == null) 0 else this.bulkLeaseSign.hashCode())
        result = prime * result + (if (this.setPropertySign == null) 0 else this.setPropertySign.hashCode())
        result = prime * result + (if (this.declarationRequestCollectionSign == null) 0 else this.declarationRequestCollectionSign.hashCode())
        result = prime * result + (if (this.layoutCategory == null) 0 else this.layoutCategory.hashCode())
        result = prime * result + (if (this.layoutDetails == null) 0 else this.layoutDetails.hashCode())
        result = prime * result + (if (this.floorNumber == null) 0 else this.floorNumber.hashCode())
        result = prime * result + (if (this.occupancyMediationSign == null) 0 else this.occupancyMediationSign.hashCode())
        result = prime * result + (if (this.occupantCategory == null) 0 else this.occupantCategory.hashCode())
        result = prime * result + (if (this.mutualAidAssociationSign == null) 0 else this.mutualAidAssociationSign.hashCode())
        result = prime * result + (if (this.mutualAidAssociationReferralCount == null) 0 else this.mutualAidAssociationReferralCount.hashCode())
        result = prime * result + (if (this.mutualAidAssocCashReceiptAmount == null) 0 else this.mutualAidAssocCashReceiptAmount.hashCode())
        result = prime * result + (if (this.managementSign == null) 0 else this.managementSign.hashCode())
        result = prime * result + (if (this.managementUnitCategory == null) 0 else this.managementUnitCategory.hashCode())
        result = prime * result + (if (this.referralCount == null) 0 else this.referralCount.hashCode())
        result = prime * result + (if (this.managementContractCashReceiptAmount == null) 0 else this.managementContractCashReceiptAmount.hashCode())
        result = prime * result + (if (this.initialAssessmentReviewNumber == null) 0 else this.initialAssessmentReviewNumber.hashCode())
        result = prime * result + (if (this.officeFloorAreaSquareMeters == null) 0 else this.officeFloorAreaSquareMeters.hashCode())
        result = prime * result + (if (this.residentialAreaSquareMeters == null) 0 else this.residentialAreaSquareMeters.hashCode())
        result = prime * result + (if (this.parkingAvailabilitySign == null) 0 else this.parkingAvailabilitySign.hashCode())
        result = prime * result + (if (this.initialOccupancyDate == null) 0 else this.initialOccupancyDate.hashCode())
        result = prime * result + (if (this.occupancyStatusSign == null) 0 else this.occupancyStatusSign.hashCode())
        result = prime * result + (if (this.expectedAvailableDate == null) 0 else this.expectedAvailableDate.hashCode())
        result = prime * result + (if (this.storeExclusiveAreaSquareMeters == null) 0 else this.storeExclusiveAreaSquareMeters.hashCode())
        result = prime * result + (if (this.balconyArea == null) 0 else this.balconyArea.hashCode())
        result = prime * result + (if (this.roomPositionCategory == null) 0 else this.roomPositionCategory.hashCode())
        result = prime * result + (if (this.orientationCategory == null) 0 else this.orientationCategory.hashCode())
        result = prime * result + (if (this.roomType == null) 0 else this.roomType.hashCode())
        result = prime * result + (if (this.studioRoomSign == null) 0 else this.studioRoomSign.hashCode())
        result = prime * result + (if (this.serviceRoomSign == null) 0 else this.serviceRoomSign.hashCode())
        result = prime * result + (if (this.privateGardenAvailabilitySign == null) 0 else this.privateGardenAvailabilitySign.hashCode())
        result = prime * result + (if (this.underfloorStorageAvailabilitySign == null) 0 else this.underfloorStorageAvailabilitySign.hashCode())
        result = prime * result + (if (this.roomEquipmentCode == null) 0 else this.roomEquipmentCode.hashCode())
        result = prime * result + (if (this.roomFeatureCode == null) 0 else this.roomFeatureCode.hashCode())
        result = prime * result + (if (this.newOrUsedCategory == null) 0 else this.newOrUsedCategory.hashCode())
        result = prime * result + (if (this.currentlyAvailableSign == null) 0 else this.currentlyAvailableSign.hashCode())
        result = prime * result + (if (this.remainingCollectionDate == null) 0 else this.remainingCollectionDate.hashCode())
        result = prime * result + (if (this.frontFreeRentSign == null) 0 else this.frontFreeRentSign.hashCode())
        result = prime * result + (if (this.frontFreeRentMonths == null) 0 else this.frontFreeRentMonths.hashCode())
        result = prime * result + (if (this.frontFreeRentAmount == null) 0 else this.frontFreeRentAmount.hashCode())
        result = prime * result + (if (this.additionalAdvertisingMonths == null) 0 else this.additionalAdvertisingMonths.hashCode())
        result = prime * result + (if (this.rentalAssessmentStatusCategory == null) 0 else this.rentalAssessmentStatusCategory.hashCode())
        result = prime * result + (if (this.priorityInformationCode == null) 0 else this.priorityInformationCode.hashCode())
        result = prime * result + (if (this.assessmentReviewNumber == null) 0 else this.assessmentReviewNumber.hashCode())
        result = prime * result + (if (this.firstFloorAreaSquareMeters == null) 0 else this.firstFloorAreaSquareMeters.hashCode())
        result = prime * result + (if (this.secondFloorAreaSquareMeters == null) 0 else this.secondFloorAreaSquareMeters.hashCode())
        result = prime * result + (if (this.thirdFloorAreaSquareMeters == null) 0 else this.thirdFloorAreaSquareMeters.hashCode())
        result = prime * result + (if (this.interfaceSign == null) 0 else this.interfaceSign.hashCode())
        result = prime * result + (if (this.referralExtractionRequiredSign == null) 0 else this.referralExtractionRequiredSign.hashCode())
        result = prime * result + (if (this.dataMigrationKey_1 == null) 0 else this.dataMigrationKey_1.hashCode())
        result = prime * result + (if (this.dataMigrationKey_2 == null) 0 else this.dataMigrationKey_2.hashCode())
        result = prime * result + (if (this.roomChangedSign == null) 0 else this.roomChangedSign.hashCode())
        result = prime * result + (if (this.rocky == null) 0 else this.rocky.hashCode())
        result = prime * result + (if (this.categoryA == null) 0 else this.categoryA.hashCode())
        result = prime * result + (if (this.categoryB == null) 0 else this.categoryB.hashCode())
        result = prime * result + (if (this.equipmentCode == null) 0 else this.equipmentCode.hashCode())
        result = prime * result + (if (this.newGuaranteeRate == null) 0 else this.newGuaranteeRate.hashCode())
        result = prime * result + (if (this.newManagementGuaranteeRate == null) 0 else this.newManagementGuaranteeRate.hashCode())
        result = prime * result + (if (this.contractMutualAidFeeRate == null) 0 else this.contractMutualAidFeeRate.hashCode())
        result = prime * result + (if (this.revivalTargetRoom == null) 0 else this.revivalTargetRoom.hashCode())
        result = prime * result + (if (this.specialPreferredRentCategory == null) 0 else this.specialPreferredRentCategory.hashCode())
        result = prime * result + (if (this.nonStandardCategory == null) 0 else this.nonStandardCategory.hashCode())
        return result
    }
}
