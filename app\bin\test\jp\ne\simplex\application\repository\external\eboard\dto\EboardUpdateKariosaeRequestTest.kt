package jp.ne.simplex.application.repository.external.eboard.dto

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import jp.ne.simplex.application.model.Company
import jp.ne.simplex.stub.*
import java.time.LocalDate

class EboardUpdateKariosaeRequestTest : FunSpec({

    context("仮押さえ登録リクエストを作成できること") {
        val rtr = stubOwnCompanyRegisterTemporaryReservation(
            buildingCode = "*********",
            roomCode = "01010",
            assignedBranchName = "渋谷支店",
            assignedBranchCode = "541",
            assignedBranchCompany = Company.DaitouKentakuPartners,
            assignedEmployeeCode = "000011",
            assignedEmployeeName = "山田太郎",
            scheduledMoveInDate = LocalDate.of(2024, 12, 19),
            updateDate = "20241215",
            updateTime = "124335",
            comment = "テストテスト"
        )

        test("Officeが指定されている場合、大東建託パートナーズによる仮押さえと判断できる項目が設定されること") {
            val office = stubOffice("983", "渋谷")
            val actual = EboardUpdateKariosaeRequest.of(rtr, office)

            actual.tatemonoCd.shouldBe(rtr.getId().buildingCode.value)
            actual.heyaCd.shouldBe(rtr.getId().roomCode.value)
            actual.updKbn.shouldBe("1")
            actual.nYoteiDate.shouldBe("20241219")
            actual.firmId.shouldBe("DKP${office.code.value}")
            actual.firmName.shouldBe(rtr.assignedBranch.company.name)
            actual.branchName.shouldBe("${office.name.value}営業所")
            actual.regName.shouldBe(rtr.assignedEmployee.name.kanji)
            actual.staffCd.shouldBe(rtr.assignedEmployee.code.value)
            actual.comment.shouldBe(rtr.comment.value)
        }

        test("Officeが指定されていない場合、大東建託リーシングによる仮押さえと判断できる項目が設定されること") {
            val actual = EboardUpdateKariosaeRequest.of(rtr, null)

            actual.tatemonoCd.shouldBe(rtr.getId().buildingCode.value)
            actual.heyaCd.shouldBe(rtr.getId().roomCode.value)
            actual.updKbn.shouldBe("1")
            actual.nYoteiDate.shouldBe("20241219")
            actual.firmId.shouldBe("DKL${rtr.assignedBranch.code.getPrefix()}")
            actual.firmName.shouldBe(rtr.assignedBranch.company.name)
            actual.branchName.shouldBe(rtr.assignedBranch.name.value)
            actual.regName.shouldBe(rtr.assignedEmployee.name.kanji)
            actual.staffCd.shouldBe(rtr.assignedEmployee.code.value)
            actual.comment.shouldBe(rtr.comment.value)
        }
    }

    context("仮押さえ解除リクエストを作成できること") {
        val ctr = stubCancelTemporaryReservation()

        test("他社によって登録された仮押さえを解除する場合、他社情報を元にAPIリクエストを作成できること") {
            val canceled = stubOtherCompanyTemporaryReservationInfo(
                buildingCode = ctr.getId().buildingCode.value,
                roomCode = ctr.getId().roomCode.value,
                scheduledMoveInDate = LocalDate.of(2024, 4, 15),
                comment = "テスト",
                otherCompanyCode = "E804382",
                otherCompanyName = "キ◯ルーム不動産",
                otherCompanyStoreName = "渋谷支店",
                otherCompanyStaffName = "田中次郎",
            )

            val actual = EboardUpdateKariosaeRequest.of(ctr, canceled, null)!!

            actual.tatemonoCd.shouldBe(ctr.getId().buildingCode.value)
            actual.heyaCd.shouldBe(ctr.getId().roomCode.value)
            actual.updKbn.shouldBe("0")
            actual.nYoteiDate.shouldBe("20240415")
            actual.firmId.shouldBe(canceled.otherCompanyInfo.companyCode)
            actual.firmName.shouldBe(canceled.otherCompanyInfo.companyName)
            actual.branchName.shouldBe(canceled.otherCompanyInfo.storeName)
            actual.regName.shouldBe(canceled.otherCompanyInfo.staffName)
            actual.staffCd.shouldBe(null)
            actual.comment.shouldBe(ctr.comment.value)
        }

        context("自社によって登録された仮押さえを解除する場合") {
            val canceled = stubOwnCompanyTemporaryReservationInfo(
                buildingCode = ctr.getId().buildingCode.value,
                roomCode = ctr.getId().roomCode.value,
                scheduledMoveInDate = LocalDate.of(2024, 4, 15),
                comment = "テスト",
                assignedBranchName = "渋谷支店",
                assignedBranchCode = "541",
                assignedBranchCompany = Company.DaitouKentakuPartners,
                assignedEmployeeCode = "000011",
                assignedEmployeeName = "山田太郎",
            )
            test("Officeが指定されている場合、大東建託パートナーズによる仮押さえと判断できる項目が設定されること") {
                val office = stubOffice("983", "渋谷")
                val actual = EboardUpdateKariosaeRequest.of(ctr, canceled, office)!!

                actual.tatemonoCd.shouldBe(ctr.getId().buildingCode.value)
                actual.heyaCd.shouldBe(ctr.getId().roomCode.value)
                actual.updKbn.shouldBe("0")
                actual.nYoteiDate.shouldBe("20240415")
                actual.firmId.shouldBe("DKP${office.code.value}")
                actual.firmName.shouldBe(canceled.assignedBranch.company.name)
                actual.branchName.shouldBe("${office.name.value}営業所")
                actual.regName.shouldBe(canceled.assignedEmployee.name.kanji)
                actual.staffCd.shouldBe(null)
                actual.comment.shouldBe(ctr.comment.value)
            }

            test("Officeが指定されていない場合、大東建託リーシングによる仮押さえと判断できる項目が設定されること") {
                val actual = EboardUpdateKariosaeRequest.of(ctr, canceled, null)!!

                actual.tatemonoCd.shouldBe(ctr.getId().buildingCode.value)
                actual.heyaCd.shouldBe(ctr.getId().roomCode.value)
                actual.updKbn.shouldBe("0")
                actual.nYoteiDate.shouldBe("20240415")
                actual.firmId.shouldBe("DKL${canceled.assignedBranch.code.getPrefix()}")
                actual.firmName.shouldBe(canceled.assignedBranch.company.name)
                actual.branchName.shouldBe(canceled.assignedBranch.name.value)
                actual.regName.shouldBe(canceled.assignedEmployee.name.kanji)
                actual.staffCd.shouldBe(null)
                actual.comment.shouldBe(ctr.comment.value)
            }
        }

    }

})
