-- TABLE: LATEST_PRODUCT_MASTER(最新商品マスタ)

CREATE TABLE LATEST_PRODUCT_MASTER(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    PRODUCT_NAME_CD                              numeric(3)                    
,    PRODUCT_CD_BRANCH                            numeric(2)                    
,    PRODUCT_NAME                                 varchar(42)                   
,    PRODUCT_GRADE_NAME                           varchar(22)                   
) TABLESPACE :TS_TBL;

COMMENT ON TABLE LATEST_PRODUCT_MASTER IS '最新商品マスタ 既存システム物理名: EGJKCP';
COMMENT ON COLUMN LATEST_PRODUCT_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: EGJ01D @290';
COMMENT ON COLUMN LATEST_PRODUCT_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: EGJ02H @290';
COMMENT ON COLUMN LATEST_PRODUCT_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: EGJ03D';
COMMENT ON COLUMN LATEST_PRODUCT_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: EGJ04H';
COMMENT ON COLUMN LATEST_PRODUCT_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EGJ05N';
COMMENT ON COLUMN LATEST_PRODUCT_MASTER.UPDATER IS '更新者 既存システム物理名: EGJ06C';
COMMENT ON COLUMN LATEST_PRODUCT_MASTER.PRODUCT_NAME_CD IS '商品名称コード 既存システム物理名: BGJS21';
COMMENT ON COLUMN LATEST_PRODUCT_MASTER.PRODUCT_CD_BRANCH IS '商品コード枝番 既存システム物理名: BGKS24';
COMMENT ON COLUMN LATEST_PRODUCT_MASTER.PRODUCT_NAME IS '商品名称 既存システム物理名: BGJS22';
COMMENT ON COLUMN LATEST_PRODUCT_MASTER.PRODUCT_GRADE_NAME IS '商品グレード名称 既存システム物理名: BGKG04';
