-- TABLE: BUILDING_INFO_MASTER(建物情報マスタ)

CREATE TABLE BUILDING_INFO_MASTER(
     BUILDING_CODE                                varchar(9)        NOT NULL    
,    CUSTOMER_BRANCH_CODE                         varchar(3)                    
,    MANAGEMENT_<PERSON>ANCH_CODE                       varchar(3)                    
,    PREFECTURE_CODE                              varchar(2)                    
,    CITY_CODE                                    varchar(3)                    
,    LINE_CODE                                    varchar(4)                    
,    STATION_CODE                                 varchar(4)                    
,    LINE_NAME                                    varchar(42)                   
,    STATION_NAME                                 varchar(32)                   
,    BUS_STOP_NAME                                varchar(22)                   
,    BUS_TIME                                     numeric(3,0)                  
,    WALKING_TIME                                 numeric(3,0)                  
,    DISTANCE                                     numeric(3,0)                  
,    CONSTRUCTION_YEAR_MONTH                      numeric(6,0)                  
,    LOCATION                                     varchar(257)                  
,    TOTAL_FLOORS                                 numeric(3,0)                  
,    MAX_UNITS_PER_FLOOR                          numeric(3,0)                  
,    T<PERSON>NSPORTATION                               varchar(152)                  
,    NOTES                                        varchar(257)                  
,    PROPERTY_NAME                                varchar(84)                   
,    STRUCTURE_NAME                               varchar(32)                   
,    POSTAL_CODE                                  varchar(7)                    
,    OWNER                                        varchar(42)                   
,    GAS_COMPANY_NAME                             varchar(42)                   
,    GAS_COMPANY_CONTACT                          varchar(15)                   
,    WATER_COMPANY_NAME                           varchar(42)                   
,    WATER_COMPANY_CONTACT                        varchar(15)                   
,    ELECTRICITY_COMPANY_NAME                     varchar(42)                   
,    ELECTRICITY_COMPANY_CONTACT                  varchar(15)                   
,    BUILDING_EQUIPMENT                           varchar(152)                  
,    VACANT_ROOMS                                 numeric(3,0)                  
,    VACANT_PARKING_SPOTS                         numeric(3,0)                  
,    MIN_PARKING_FEE                              numeric(9,0)                  
,    MAX_PARKING_FEE                              numeric(9,0)                  
,    LOCATION_CITY                                varchar(62)                   
,    WATER_SUPPLY_TYPE                            varchar(1)                    
,    WATER_METER_TYPE                             varchar(1)                    
,    POWER_SUPPLY_COMPANY_CODE                    varchar(2)                    
,    ELECTRICITY_METER_TYPE                       varchar(1)                    
,    GAS_TYPE                                     varchar(1)                    
,    GAS_METER_TYPE                               varchar(1)                    
,    TOILET_TYPE                                  varchar(1)                    
,    POWER_PRESENCE                               numeric(1,0)                  
,    DRAINAGE_TYPE                                varchar(1)                    
,    SEPTIC_TANK_CALCULATION                      numeric(5,0)                  
,    TELEPHONE_TYPE                               varchar(1)                    
,    BUILDING_STRUCTURE_TYPE                      varchar(2)                    
,    ROOF_TYPE                                    varchar(2)                    
,    URBAN_PLANNING_AREA_TYPE                     varchar(2)                    
,    LAND_USE_TYPE                                varchar(2)                    
,    FIRE_PROTECTION_AREA_TYPE                    varchar(2)                    
,    PLANNED_ROAD_SIGN                            numeric(1,0)                  
,    PROJECT_PLAN_DECISION_SIGN                   numeric(1,0)                  
,    BUSINESS_YEAR                                numeric(6,0)                  
,    BUILDING_TYPE_CODE                           varchar(3)                    
,    CONTRACT_BRANCH                              varchar(6)                    
,    CONSTRUCTION_BRANCH                          varchar(6)                    
,    BUILDING_CONFIRMATION_DATE                   numeric(8,0)                  
,    EXTERIOR_CONSTRUCTION_START_DATE             numeric(8,0)                  
,    GOVERNMENT_INSPECTION_DATE                   numeric(8,0)                  
,    GOVERNMENT_INSPECTION_COMPLETION_DATE        numeric(8,0)                  
,    INTERNAL_INSPECTION_DATE                     numeric(8,0)                  
,    INTERNAL_INSPECTION_COMPLETION_DATE          numeric(8,0)                  
,    FINAL_INSPECTION_DATE                        numeric(8,0)                  
,    FINAL_INSPECTION_COMPLETION_DATE             numeric(8,0)                  
,    EXPECTED_COMPLETION_DATE                     numeric(8,0)                  
,    COMPLETION_DATE                              numeric(8,0)                  
,    MOVE_IN_DATE                                 numeric(8,0)                  
,    START_DATE                                   numeric(8,0)                  
,    PUBLIC_LOAN_SIGN                             numeric(1,0)                  
,    FLETS_SUPPORT_CD                             numeric(1,0)                  
,    SKY_PERFECT_TV_SUPPORT_CD                    numeric(1,0)                  
,    PROPERTY_ADDRESS                             varchar(74)                   
,    PROPERTY_ADDRESS_DETAIL                      varchar(62)                   
,    ON_SITE_VACANT_PARKING_SPOTS                 numeric(3,0)                  
,    FLAG_RESERVE_1                               numeric(1,0)                  
,    FLAG_RESERVE_2                               numeric(1,0)                  
,    FLAG_RESERVE_3                               numeric(1,0)                  
,    FLAG_RESERVE_4                               numeric(1,0)                  
,    FLAG_RESERVE_5                               numeric(1,0)                  
,    LEASING_STORE_CD                             varchar(3)                    
,    BRANCH_OFFICE_CD                             varchar(3)                    
,    REVIEW_BRANCH_CD                             varchar(3)                    
,    MARKETING_BRANCH_OFFICE_CD                   varchar(3)                    
,    END_                                         varchar(1)                    
,    CONSTRAINT PK_BUILDING_INFO_MASTER PRIMARY KEY (BUILDING_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BUILDING_INFO_MASTER IS '建物情報マスタ 既存システム物理名: EMUR2P';
COMMENT ON COLUMN BUILDING_INFO_MASTER.BUILDING_CODE IS '建物コード 既存システム物理名: EMEABC リーシング対応';
COMMENT ON COLUMN BUILDING_INFO_MASTER.CUSTOMER_BRANCH_CODE IS '客付支店コード 既存システム物理名: EME03C リーシング対応';
COMMENT ON COLUMN BUILDING_INFO_MASTER.MANAGEMENT_BRANCH_CODE IS '管理支店コード 既存システム物理名: EME04C リーシング対応';
COMMENT ON COLUMN BUILDING_INFO_MASTER.PREFECTURE_CODE IS '県コード 既存システム物理名: EME05C';
COMMENT ON COLUMN BUILDING_INFO_MASTER.CITY_CODE IS '市区群町村コード 既存システム物理名: EME06C';
COMMENT ON COLUMN BUILDING_INFO_MASTER.LINE_CODE IS '路線コード 既存システム物理名: EME07C';
COMMENT ON COLUMN BUILDING_INFO_MASTER.STATION_CODE IS '駅コード 既存システム物理名: EME08C';
COMMENT ON COLUMN BUILDING_INFO_MASTER.LINE_NAME IS '路線名称 既存システム物理名: EMERSM';
COMMENT ON COLUMN BUILDING_INFO_MASTER.STATION_NAME IS '駅名称 既存システム物理名: EMEEKM';
COMMENT ON COLUMN BUILDING_INFO_MASTER.BUS_STOP_NAME IS 'バス停名称 既存システム物理名: EMEBTM';
COMMENT ON COLUMN BUILDING_INFO_MASTER.BUS_TIME IS 'バス時間 既存システム物理名: EMEBTT';
COMMENT ON COLUMN BUILDING_INFO_MASTER.WALKING_TIME IS '徒歩時間 既存システム物理名: EMETTT';
COMMENT ON COLUMN BUILDING_INFO_MASTER.DISTANCE IS '距離 既存システム物理名: EMEKYO';
COMMENT ON COLUMN BUILDING_INFO_MASTER.CONSTRUCTION_YEAR_MONTH IS '築年月(完工年月) 既存システム物理名: EMEKAN';
COMMENT ON COLUMN BUILDING_INFO_MASTER.LOCATION IS '所在地 既存システム物理名: EMESZT';
COMMENT ON COLUMN BUILDING_INFO_MASTER.TOTAL_FLOORS IS '総階数 既存システム物理名: EMESKI';
COMMENT ON COLUMN BUILDING_INFO_MASTER.MAX_UNITS_PER_FLOOR IS '階最大戸数 既存システム物理名: EMEKMK';
COMMENT ON COLUMN BUILDING_INFO_MASTER.TRANSPORTATION IS '交通 既存システム物理名: EMEKTU';
COMMENT ON COLUMN BUILDING_INFO_MASTER.NOTES IS '備考 既存システム物理名: EMEBKO';
COMMENT ON COLUMN BUILDING_INFO_MASTER.PROPERTY_NAME IS '物件名称 既存システム物理名: EMEBKM';
COMMENT ON COLUMN BUILDING_INFO_MASTER.STRUCTURE_NAME IS '構造名称 既存システム物理名: EMEKZM';
COMMENT ON COLUMN BUILDING_INFO_MASTER.POSTAL_CODE IS '郵便番号 既存システム物理名: EMEYUB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.OWNER IS '施主様 既存システム物理名: EMESSM';
COMMENT ON COLUMN BUILDING_INFO_MASTER.GAS_COMPANY_NAME IS 'ガス会社名 既存システム物理名: EMEGSK';
COMMENT ON COLUMN BUILDING_INFO_MASTER.GAS_COMPANY_CONTACT IS 'ガス会社連絡先 既存システム物理名: EMEGSR';
COMMENT ON COLUMN BUILDING_INFO_MASTER.WATER_COMPANY_NAME IS '水道局会社名 既存システム物理名: EMESDK';
COMMENT ON COLUMN BUILDING_INFO_MASTER.WATER_COMPANY_CONTACT IS '水道局会社連絡先 既存システム物理名: EMESDR';
COMMENT ON COLUMN BUILDING_INFO_MASTER.ELECTRICITY_COMPANY_NAME IS '電気会社名 既存システム物理名: EMEDKK';
COMMENT ON COLUMN BUILDING_INFO_MASTER.ELECTRICITY_COMPANY_CONTACT IS '電気会社連絡先 既存システム物理名: EMEDKR';
COMMENT ON COLUMN BUILDING_INFO_MASTER.BUILDING_EQUIPMENT IS '建物設備 既存システム物理名: EMETMS';
COMMENT ON COLUMN BUILDING_INFO_MASTER.VACANT_ROOMS IS '空き部屋数 既存システム物理名: EMEAHS';
COMMENT ON COLUMN BUILDING_INFO_MASTER.VACANT_PARKING_SPOTS IS '空き駐車場数 既存システム物理名: EMEAPS';
COMMENT ON COLUMN BUILDING_INFO_MASTER.MIN_PARKING_FEE IS '最低駐車料 既存システム物理名: EMELPK';
COMMENT ON COLUMN BUILDING_INFO_MASTER.MAX_PARKING_FEE IS '最高駐車料 既存システム物理名: EMEHPK';
COMMENT ON COLUMN BUILDING_INFO_MASTER.LOCATION_CITY IS '所在地(市区郡) 既存システム物理名: EMESZS';
COMMENT ON COLUMN BUILDING_INFO_MASTER.WATER_SUPPLY_TYPE IS '重説用水道区分 既存システム物理名: EMECPB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.WATER_METER_TYPE IS '水道メーター区分 既存システム物理名: EMECQB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.POWER_SUPPLY_COMPANY_CODE IS '電力供給会社コード 既存システム物理名: EMECMB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.ELECTRICITY_METER_TYPE IS '電気メーター区分 既存システム物理名: EMECNB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.GAS_TYPE IS 'ガス区分 既存システム物理名: EMEFFB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.GAS_METER_TYPE IS 'ガスメーター区分 既存システム物理名: EMECOB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.TOILET_TYPE IS 'トイレ区分 既存システム物理名: EMEFOB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.POWER_PRESENCE IS '動力有無サイン 既存システム物理名: EME30S';
COMMENT ON COLUMN BUILDING_INFO_MASTER.DRAINAGE_TYPE IS '排水区分 既存システム物理名: EMEEDB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.SEPTIC_TANK_CALCULATION IS '浄化槽算定 既存システム物理名: EME27Q';
COMMENT ON COLUMN BUILDING_INFO_MASTER.TELEPHONE_TYPE IS '電話区分 既存システム物理名: EMEGEB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.BUILDING_STRUCTURE_TYPE IS '建物構造区分 既存システム物理名: EMEBEB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.ROOF_TYPE IS '屋根区分 既存システム物理名: EMEBGB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.URBAN_PLANNING_AREA_TYPE IS '都市計画区域区分 既存システム物理名: EMECVB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.LAND_USE_TYPE IS '用途地域区分 既存システム物理名: EMEBAB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.FIRE_PROTECTION_AREA_TYPE IS '防火地域区分 既存システム物理名: EMECWB';
COMMENT ON COLUMN BUILDING_INFO_MASTER.PLANNED_ROAD_SIGN IS '計画道路予定サイン 既存システム物理名: EME31S';
COMMENT ON COLUMN BUILDING_INFO_MASTER.PROJECT_PLAN_DECISION_SIGN IS '事業計画決定サイン 既存システム物理名: EME71T';
COMMENT ON COLUMN BUILDING_INFO_MASTER.BUSINESS_YEAR IS '事業年度 既存システム物理名: EME32D';
COMMENT ON COLUMN BUILDING_INFO_MASTER.BUILDING_TYPE_CODE IS '建物種別コード 既存システム物理名: EMEADC';
COMMENT ON COLUMN BUILDING_INFO_MASTER.CONTRACT_BRANCH IS '契約支店 既存システム物理名: EME53C';
COMMENT ON COLUMN BUILDING_INFO_MASTER.CONSTRUCTION_BRANCH IS '施工支店 既存システム物理名: EMES04';
COMMENT ON COLUMN BUILDING_INFO_MASTER.BUILDING_CONFIRMATION_DATE IS '建築確認許可日 既存システム物理名: EMEKYD';
COMMENT ON COLUMN BUILDING_INFO_MASTER.EXTERIOR_CONSTRUCTION_START_DATE IS '外交着工日 既存システム物理名: EMEGCD';
COMMENT ON COLUMN BUILDING_INFO_MASTER.GOVERNMENT_INSPECTION_DATE IS '行政検査予定日 既存システム物理名: EMEGYD';
COMMENT ON COLUMN BUILDING_INFO_MASTER.GOVERNMENT_INSPECTION_COMPLETION_DATE IS '行政検査完了日 既存システム物理名: EMEGKD';
COMMENT ON COLUMN BUILDING_INFO_MASTER.INTERNAL_INSPECTION_DATE IS '社内検査予定日 既存システム物理名: EMESYD';
COMMENT ON COLUMN BUILDING_INFO_MASTER.INTERNAL_INSPECTION_COMPLETION_DATE IS '社内検査完了日 既存システム物理名: EMESKD';
COMMENT ON COLUMN BUILDING_INFO_MASTER.FINAL_INSPECTION_DATE IS '完成検査予定日 既存システム物理名: EMEK1D';
COMMENT ON COLUMN BUILDING_INFO_MASTER.FINAL_INSPECTION_COMPLETION_DATE IS '完成検査完了日 既存システム物理名: EMEK2D';
COMMENT ON COLUMN BUILDING_INFO_MASTER.EXPECTED_COMPLETION_DATE IS '完成予定日 既存システム物理名: EMEK3D';
COMMENT ON COLUMN BUILDING_INFO_MASTER.COMPLETION_DATE IS '完成完了日 既存システム物理名: EMEK4D';
COMMENT ON COLUMN BUILDING_INFO_MASTER.MOVE_IN_DATE IS '入居日 既存システム物理名: EME09D';
COMMENT ON COLUMN BUILDING_INFO_MASTER.START_DATE IS '開始日 既存システム物理名: EMEC2D';
COMMENT ON COLUMN BUILDING_INFO_MASTER.PUBLIC_LOAN_SIGN IS '公庫融資サイン 既存システム物理名: EME42S';
COMMENT ON COLUMN BUILDING_INFO_MASTER.FLETS_SUPPORT_CD IS 'フレッツ対応CD 既存システム物理名: EMEFRC';
COMMENT ON COLUMN BUILDING_INFO_MASTER.SKY_PERFECT_TV_SUPPORT_CD IS 'スカパー対応CD 既存システム物理名: EMESKC';
COMMENT ON COLUMN BUILDING_INFO_MASTER.PROPERTY_ADDRESS IS '物件住所 既存システム物理名: EMEJST';
COMMENT ON COLUMN BUILDING_INFO_MASTER.PROPERTY_ADDRESS_DETAIL IS '物件住所詳細 既存システム物理名: EMEJSJ';
COMMENT ON COLUMN BUILDING_INFO_MASTER.ON_SITE_VACANT_PARKING_SPOTS IS '敷地内空駐車場台数 既存システム物理名: EMEASS';
COMMENT ON COLUMN BUILDING_INFO_MASTER.FLAG_RESERVE_1 IS 'フラグ予備1 既存システム物理名: EMEA1S';
COMMENT ON COLUMN BUILDING_INFO_MASTER.FLAG_RESERVE_2 IS 'フラグ予備2 既存システム物理名: EMEA2S';
COMMENT ON COLUMN BUILDING_INFO_MASTER.FLAG_RESERVE_3 IS 'フラグ予備3 既存システム物理名: EMEA3S';
COMMENT ON COLUMN BUILDING_INFO_MASTER.FLAG_RESERVE_4 IS 'フラグ予備4 既存システム物理名: EMEA4S';
COMMENT ON COLUMN BUILDING_INFO_MASTER.FLAG_RESERVE_5 IS 'フラグ予備5 既存システム物理名: EMEA5S';
COMMENT ON COLUMN BUILDING_INFO_MASTER.LEASING_STORE_CD IS 'リーシング店舗CD 既存システム物理名: EMEA6C';
COMMENT ON COLUMN BUILDING_INFO_MASTER.BRANCH_OFFICE_CD IS '営業所CD 既存システム物理名: EMEA7C';
COMMENT ON COLUMN BUILDING_INFO_MASTER.REVIEW_BRANCH_CD IS '審査支店CD 既存システム物理名: EMEA8C';
COMMENT ON COLUMN BUILDING_INFO_MASTER.MARKETING_BRANCH_OFFICE_CD IS 'マーケティング営業所ＣＤ 既存システム物理名: EMEA9C';
COMMENT ON COLUMN BUILDING_INFO_MASTER.END_ IS 'END 既存システム物理名: EMEEND';
