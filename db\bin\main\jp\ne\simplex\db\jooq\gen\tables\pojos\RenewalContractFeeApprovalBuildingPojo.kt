/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 更新契約手数料承諾建物 既存システム物理名: FVI90P
 */
@Suppress("UNCHECKED_CAST")
data class RenewalContractFeeApprovalBuildingPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var creationProgramId: String? = null,
    var creationTerminalId: String? = null,
    var creationResponsibleId: String? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updateTerminalId: String? = null,
    var updateResponsibleId: String? = null,
    var deleteFlag: Byte? = null,
    var buildingCd: String? = null,
    var approvalDate: Int? = null,
    var status: Byte? = null,
    var approvalRejectionInputPerson: String? = null,
    var renewalFeeCategory: Short? = null,
    var renewalFeeCategoryRegistrationDate: Int? = null,
    var renewalFeeCategoryInputPerson: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: RenewalContractFeeApprovalBuildingPojo = other as RenewalContractFeeApprovalBuildingPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.creationProgramId == null) {
            if (o.creationProgramId != null)
                return false
        }
        else if (this.creationProgramId != o.creationProgramId)
            return false
        if (this.creationTerminalId == null) {
            if (o.creationTerminalId != null)
                return false
        }
        else if (this.creationTerminalId != o.creationTerminalId)
            return false
        if (this.creationResponsibleId == null) {
            if (o.creationResponsibleId != null)
                return false
        }
        else if (this.creationResponsibleId != o.creationResponsibleId)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updateTerminalId == null) {
            if (o.updateTerminalId != null)
                return false
        }
        else if (this.updateTerminalId != o.updateTerminalId)
            return false
        if (this.updateResponsibleId == null) {
            if (o.updateResponsibleId != null)
                return false
        }
        else if (this.updateResponsibleId != o.updateResponsibleId)
            return false
        if (this.deleteFlag == null) {
            if (o.deleteFlag != null)
                return false
        }
        else if (this.deleteFlag != o.deleteFlag)
            return false
        if (this.buildingCd == null) {
            if (o.buildingCd != null)
                return false
        }
        else if (this.buildingCd != o.buildingCd)
            return false
        if (this.approvalDate == null) {
            if (o.approvalDate != null)
                return false
        }
        else if (this.approvalDate != o.approvalDate)
            return false
        if (this.status == null) {
            if (o.status != null)
                return false
        }
        else if (this.status != o.status)
            return false
        if (this.approvalRejectionInputPerson == null) {
            if (o.approvalRejectionInputPerson != null)
                return false
        }
        else if (this.approvalRejectionInputPerson != o.approvalRejectionInputPerson)
            return false
        if (this.renewalFeeCategory == null) {
            if (o.renewalFeeCategory != null)
                return false
        }
        else if (this.renewalFeeCategory != o.renewalFeeCategory)
            return false
        if (this.renewalFeeCategoryRegistrationDate == null) {
            if (o.renewalFeeCategoryRegistrationDate != null)
                return false
        }
        else if (this.renewalFeeCategoryRegistrationDate != o.renewalFeeCategoryRegistrationDate)
            return false
        if (this.renewalFeeCategoryInputPerson == null) {
            if (o.renewalFeeCategoryInputPerson != null)
                return false
        }
        else if (this.renewalFeeCategoryInputPerson != o.renewalFeeCategoryInputPerson)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.creationProgramId == null) 0 else this.creationProgramId.hashCode())
        result = prime * result + (if (this.creationTerminalId == null) 0 else this.creationTerminalId.hashCode())
        result = prime * result + (if (this.creationResponsibleId == null) 0 else this.creationResponsibleId.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updateTerminalId == null) 0 else this.updateTerminalId.hashCode())
        result = prime * result + (if (this.updateResponsibleId == null) 0 else this.updateResponsibleId.hashCode())
        result = prime * result + (if (this.deleteFlag == null) 0 else this.deleteFlag.hashCode())
        result = prime * result + (if (this.buildingCd == null) 0 else this.buildingCd.hashCode())
        result = prime * result + (if (this.approvalDate == null) 0 else this.approvalDate.hashCode())
        result = prime * result + (if (this.status == null) 0 else this.status.hashCode())
        result = prime * result + (if (this.approvalRejectionInputPerson == null) 0 else this.approvalRejectionInputPerson.hashCode())
        result = prime * result + (if (this.renewalFeeCategory == null) 0 else this.renewalFeeCategory.hashCode())
        result = prime * result + (if (this.renewalFeeCategoryRegistrationDate == null) 0 else this.renewalFeeCategoryRegistrationDate.hashCode())
        result = prime * result + (if (this.renewalFeeCategoryInputPerson == null) 0 else this.renewalFeeCategoryInputPerson.hashCode())
        return result
    }
}
