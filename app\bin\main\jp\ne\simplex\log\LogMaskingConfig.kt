package jp.ne.simplex.log

import com.fasterxml.jackson.core.JsonStreamContext
import net.logstash.logback.mask.ValueMasker
import java.util.*
import java.util.regex.Pattern
import java.util.stream.Collectors

class LogMaskingConfig : ValueMasker {
    private val manager = MaskTargetManager()

    companion object {
        private const val MASK_VALUE = "******"

        private val maskTargetField = setOf("uri", "message", "stack_trace")
    }

    override fun mask(context: JsonStreamContext?, arg: Any?): Any {
        if (arg == null) return ""

        if (maskTargetField.contains(context?.currentName) && arg is CharSequence) {
            val value = arg.toString()

            return maskValue(value)
        }

        return arg
    }

    fun maskValue(value: String): String {
        var maskedValue = value

        manager.getMaskFieldNames().forEach { prop ->
            maskedValue =
                if (maskedValue.startsWith("{") && maskedValue.endsWith("}")) {
                    // Json形式の文字列をマスキング
                    maskJsonPattern(maskedValue, prop)
                } else if (maskedValue.matches(Regex("^(https?://\\S+)?([a-zA-Z0-9]+=[^\\s　]+&?)+"))) {
                    // QueryParam形式の文字列をマスキング
                    maskQueryParamPattern(maskedValue, prop)
                } else {
                    // その他形式の文字列はここではマスキングせずログを出している場所でマスキングする
                    maskedValue
                }
        }

        return maskedValue
    }

    // "password": "1234" -> "password": "***MASK***"
    private fun maskJsonPattern(target: String, key: String): String {
        val delimiter = ","
        val pattern = Regex("""("$key":\s*")(.*")""")
        val maskedFormat = """"$key": "$MASK_VALUE""""

        return Arrays.stream(Pattern.compile(delimiter).split(target))
            .map {
                it.replace(pattern, maskedFormat)
            }
            .collect(Collectors.joining(delimiter))
    }

    // password=1234 -> password=***MASK***
    private fun maskQueryParamPattern(target: String, key: String): String {
        val delimiter = "&"
        val pattern = Regex("$key=.*")
        val maskedFormat = "$key=$MASK_VALUE"

        return Arrays.stream(Pattern.compile(delimiter).split(target))
            .map {
                it.replace(pattern, maskedFormat)
            }
            .collect(Collectors.joining(delimiter))
    }
}
