package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Office
import jp.ne.simplex.application.repository.db.extension.NewOfficeMasterEx.Companion.getOffice
import jp.ne.simplex.db.jooq.gen.tables.pojos.NewOfficeMasterPojo
import jp.ne.simplex.db.jooq.gen.tables.references.NEW_OFFICE_MASTER
import org.jooq.DSLContext
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository

@Repository
class OfficeRepository(
    private val context: DSLContext,
    private val officeBranchMappingRepository: OfficeBranchMappingRepositoryInterface,
) : OfficeRepositoryInterface {

    companion object {
        private val log = LoggerFactory.getLogger(OfficeRepository::class.java)
    }

    override fun findBy(officeCode: Office.Code?): Office? {
        return context.select().from(NEW_OFFICE_MASTER)
            .where(NEW_OFFICE_MASTER.BUSINESS_OFFICE_CODE.eq(officeCode?.value))
            .fetchInto(NewOfficeMasterPojo::class.java).firstOrNull()?.getOffice()
    }

    override fun findBy(branchCode: Branch.Code?): Office? {
        return try {
            this.findBy(officeBranchMappingRepository.get(branchCode))
        } catch (_: Exception) {
            log.warn("branchCode = ${branchCode?.getValue()} record not found in daito_building_management_table or new_office_master.")
            null
        }
    }
}

interface OfficeRepositoryInterface {

    fun findBy(officeCode: Office.Code?): Office?

    fun findBy(branchCode: Branch.Code?): Office?
}
