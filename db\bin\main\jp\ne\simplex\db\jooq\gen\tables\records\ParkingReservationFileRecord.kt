/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ParkingReservationFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingReservationFilePojo

import org.jooq.Record4
import org.jooq.impl.UpdatableRecordImpl


/**
 * 駐車場予約ファイル 既存システム物理名: ERC30P
 */
@Suppress("UNCHECKED_CAST")
open class ParkingReservationFileRecord private constructor() : UpdatableRecordImpl<ParkingReservationFileRecord>(ParkingReservationFileTable.PARKING_RESERVATION_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var updateProgramId: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(7, value)
        get(): Byte? = get(7) as Byte?

    open var orderCode: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    open var receptionDate: Int
        set(value): Unit = set(9, value)
        get(): Int = get(9) as Int

    open var sequenceNumber: Short
        set(value): Unit = set(10, value)
        get(): Short = get(10) as Short

    open var statusDivision: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var reserverName: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var roomCode: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var contactTel: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var receptionStaff: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var remarks: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var buildingNumber: String
        set(value): Unit = set(17, value)
        get(): String = get(17) as String

    open var asDeleteDate: Int?
        set(value): Unit = set(18, value)
        get(): Int? = get(18) as Int?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record4<String?, Int?, Short?, String?> = super.key() as Record4<String?, Int?, Short?, String?>

    /**
     * Create a detached, initialised ParkingReservationFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, updateProgramId: String? = null, logicalDeleteSign: Byte? = null, orderCode: String, receptionDate: Int, sequenceNumber: Short, statusDivision: String? = null, reserverName: String? = null, roomCode: String? = null, contactTel: String? = null, receptionStaff: String? = null, remarks: String? = null, buildingNumber: String, asDeleteDate: Int? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.updateProgramId = updateProgramId
        this.logicalDeleteSign = logicalDeleteSign
        this.orderCode = orderCode
        this.receptionDate = receptionDate
        this.sequenceNumber = sequenceNumber
        this.statusDivision = statusDivision
        this.reserverName = reserverName
        this.roomCode = roomCode
        this.contactTel = contactTel
        this.receptionStaff = receptionStaff
        this.remarks = remarks
        this.buildingNumber = buildingNumber
        this.asDeleteDate = asDeleteDate
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ParkingReservationFileRecord
     */
    constructor(value: ParkingReservationFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.updateProgramId = value.updateProgramId
            this.logicalDeleteSign = value.logicalDeleteSign
            this.orderCode = value.orderCode
            this.receptionDate = value.receptionDate
            this.sequenceNumber = value.sequenceNumber
            this.statusDivision = value.statusDivision
            this.reserverName = value.reserverName
            this.roomCode = value.roomCode
            this.contactTel = value.contactTel
            this.receptionStaff = value.receptionStaff
            this.remarks = value.remarks
            this.buildingNumber = value.buildingNumber
            this.asDeleteDate = value.asDeleteDate
            resetChangedOnNotNull()
        }
    }
}
