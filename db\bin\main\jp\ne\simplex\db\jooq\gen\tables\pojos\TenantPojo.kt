/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable
import java.math.BigDecimal


/**
 * テナント 既存システム物理名: EEA10P
 */
@Suppress("UNCHECKED_CAST")
data class TenantPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updater: String? = null,
    var logicalDeleteSign: Byte? = null,
    var tenantCode: String,
    var integratedClientCode: String? = null,
    var tenantNameKanji: String? = null,
    var tenantNameKanji_2: String? = null,
    var tenantNameKana: String? = null,
    var transferVerificationKana1: String? = null,
    var transferVerificationKana2: String? = null,
    var headquartersNameKanji: String? = null,
    var headquartersNameKana: String? = null,
    var searchKana: String? = null,
    var personalCorporateDivision: String? = null,
    var corporateStatusDivision: String? = null,
    var corporatePositionDivision: String? = null,
    var representativeNameKanji: String? = null,
    var representativeNameKana: String? = null,
    var representativeSearchKana: String? = null,
    var tenantDepartment: String? = null,
    var tenantContactName: String? = null,
    var postalCode: String? = null,
    var prefectureCode: String? = null,
    var cityCode: String? = null,
    var townCode: String? = null,
    var addressDetail: String? = null,
    var buildingName: String? = null,
    var phoneNumber: String? = null,
    var faxNumber: String? = null,
    var postalCode_2: String? = null,
    var prefectureCode_2: String? = null,
    var cityCode_2: String? = null,
    var townCode_2: String? = null,
    var addressDetail_2: String? = null,
    var buildingName_2: String? = null,
    var phoneNumber_2: String? = null,
    var faxNumber_2: String? = null,
    var industryCode: String? = null,
    var birthDate: Int? = null,
    var workplaceName: String? = null,
    var workplacePrefectureCode: String? = null,
    var workplaceCityCode: String? = null,
    var workplaceTownCode: String? = null,
    var workplaceAddressDetail: String? = null,
    var workplaceBuildingName: String? = null,
    var workplaceIndustryCode: String? = null,
    var workplacePhoneNumber: String? = null,
    var violationHistorySign: Byte? = null,
    var nonManagedPropertySign: Byte? = null,
    var interfaceSign: Byte? = null,
    var corporateHousingAgency: Byte? = null,
    var yearsOfService: BigDecimal? = null,
    var industryPersonal: String? = null,
    var personalAnnualIncome: Int? = null,
    var cohabitantAnnualIncome: Int? = null,
    var gender: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: TenantPojo = other as TenantPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.logicalDeleteSign == null) {
            if (o.logicalDeleteSign != null)
                return false
        }
        else if (this.logicalDeleteSign != o.logicalDeleteSign)
            return false
        if (this.tenantCode != o.tenantCode)
            return false
        if (this.integratedClientCode == null) {
            if (o.integratedClientCode != null)
                return false
        }
        else if (this.integratedClientCode != o.integratedClientCode)
            return false
        if (this.tenantNameKanji == null) {
            if (o.tenantNameKanji != null)
                return false
        }
        else if (this.tenantNameKanji != o.tenantNameKanji)
            return false
        if (this.tenantNameKanji_2 == null) {
            if (o.tenantNameKanji_2 != null)
                return false
        }
        else if (this.tenantNameKanji_2 != o.tenantNameKanji_2)
            return false
        if (this.tenantNameKana == null) {
            if (o.tenantNameKana != null)
                return false
        }
        else if (this.tenantNameKana != o.tenantNameKana)
            return false
        if (this.transferVerificationKana1 == null) {
            if (o.transferVerificationKana1 != null)
                return false
        }
        else if (this.transferVerificationKana1 != o.transferVerificationKana1)
            return false
        if (this.transferVerificationKana2 == null) {
            if (o.transferVerificationKana2 != null)
                return false
        }
        else if (this.transferVerificationKana2 != o.transferVerificationKana2)
            return false
        if (this.headquartersNameKanji == null) {
            if (o.headquartersNameKanji != null)
                return false
        }
        else if (this.headquartersNameKanji != o.headquartersNameKanji)
            return false
        if (this.headquartersNameKana == null) {
            if (o.headquartersNameKana != null)
                return false
        }
        else if (this.headquartersNameKana != o.headquartersNameKana)
            return false
        if (this.searchKana == null) {
            if (o.searchKana != null)
                return false
        }
        else if (this.searchKana != o.searchKana)
            return false
        if (this.personalCorporateDivision == null) {
            if (o.personalCorporateDivision != null)
                return false
        }
        else if (this.personalCorporateDivision != o.personalCorporateDivision)
            return false
        if (this.corporateStatusDivision == null) {
            if (o.corporateStatusDivision != null)
                return false
        }
        else if (this.corporateStatusDivision != o.corporateStatusDivision)
            return false
        if (this.corporatePositionDivision == null) {
            if (o.corporatePositionDivision != null)
                return false
        }
        else if (this.corporatePositionDivision != o.corporatePositionDivision)
            return false
        if (this.representativeNameKanji == null) {
            if (o.representativeNameKanji != null)
                return false
        }
        else if (this.representativeNameKanji != o.representativeNameKanji)
            return false
        if (this.representativeNameKana == null) {
            if (o.representativeNameKana != null)
                return false
        }
        else if (this.representativeNameKana != o.representativeNameKana)
            return false
        if (this.representativeSearchKana == null) {
            if (o.representativeSearchKana != null)
                return false
        }
        else if (this.representativeSearchKana != o.representativeSearchKana)
            return false
        if (this.tenantDepartment == null) {
            if (o.tenantDepartment != null)
                return false
        }
        else if (this.tenantDepartment != o.tenantDepartment)
            return false
        if (this.tenantContactName == null) {
            if (o.tenantContactName != null)
                return false
        }
        else if (this.tenantContactName != o.tenantContactName)
            return false
        if (this.postalCode == null) {
            if (o.postalCode != null)
                return false
        }
        else if (this.postalCode != o.postalCode)
            return false
        if (this.prefectureCode == null) {
            if (o.prefectureCode != null)
                return false
        }
        else if (this.prefectureCode != o.prefectureCode)
            return false
        if (this.cityCode == null) {
            if (o.cityCode != null)
                return false
        }
        else if (this.cityCode != o.cityCode)
            return false
        if (this.townCode == null) {
            if (o.townCode != null)
                return false
        }
        else if (this.townCode != o.townCode)
            return false
        if (this.addressDetail == null) {
            if (o.addressDetail != null)
                return false
        }
        else if (this.addressDetail != o.addressDetail)
            return false
        if (this.buildingName == null) {
            if (o.buildingName != null)
                return false
        }
        else if (this.buildingName != o.buildingName)
            return false
        if (this.phoneNumber == null) {
            if (o.phoneNumber != null)
                return false
        }
        else if (this.phoneNumber != o.phoneNumber)
            return false
        if (this.faxNumber == null) {
            if (o.faxNumber != null)
                return false
        }
        else if (this.faxNumber != o.faxNumber)
            return false
        if (this.postalCode_2 == null) {
            if (o.postalCode_2 != null)
                return false
        }
        else if (this.postalCode_2 != o.postalCode_2)
            return false
        if (this.prefectureCode_2 == null) {
            if (o.prefectureCode_2 != null)
                return false
        }
        else if (this.prefectureCode_2 != o.prefectureCode_2)
            return false
        if (this.cityCode_2 == null) {
            if (o.cityCode_2 != null)
                return false
        }
        else if (this.cityCode_2 != o.cityCode_2)
            return false
        if (this.townCode_2 == null) {
            if (o.townCode_2 != null)
                return false
        }
        else if (this.townCode_2 != o.townCode_2)
            return false
        if (this.addressDetail_2 == null) {
            if (o.addressDetail_2 != null)
                return false
        }
        else if (this.addressDetail_2 != o.addressDetail_2)
            return false
        if (this.buildingName_2 == null) {
            if (o.buildingName_2 != null)
                return false
        }
        else if (this.buildingName_2 != o.buildingName_2)
            return false
        if (this.phoneNumber_2 == null) {
            if (o.phoneNumber_2 != null)
                return false
        }
        else if (this.phoneNumber_2 != o.phoneNumber_2)
            return false
        if (this.faxNumber_2 == null) {
            if (o.faxNumber_2 != null)
                return false
        }
        else if (this.faxNumber_2 != o.faxNumber_2)
            return false
        if (this.industryCode == null) {
            if (o.industryCode != null)
                return false
        }
        else if (this.industryCode != o.industryCode)
            return false
        if (this.birthDate == null) {
            if (o.birthDate != null)
                return false
        }
        else if (this.birthDate != o.birthDate)
            return false
        if (this.workplaceName == null) {
            if (o.workplaceName != null)
                return false
        }
        else if (this.workplaceName != o.workplaceName)
            return false
        if (this.workplacePrefectureCode == null) {
            if (o.workplacePrefectureCode != null)
                return false
        }
        else if (this.workplacePrefectureCode != o.workplacePrefectureCode)
            return false
        if (this.workplaceCityCode == null) {
            if (o.workplaceCityCode != null)
                return false
        }
        else if (this.workplaceCityCode != o.workplaceCityCode)
            return false
        if (this.workplaceTownCode == null) {
            if (o.workplaceTownCode != null)
                return false
        }
        else if (this.workplaceTownCode != o.workplaceTownCode)
            return false
        if (this.workplaceAddressDetail == null) {
            if (o.workplaceAddressDetail != null)
                return false
        }
        else if (this.workplaceAddressDetail != o.workplaceAddressDetail)
            return false
        if (this.workplaceBuildingName == null) {
            if (o.workplaceBuildingName != null)
                return false
        }
        else if (this.workplaceBuildingName != o.workplaceBuildingName)
            return false
        if (this.workplaceIndustryCode == null) {
            if (o.workplaceIndustryCode != null)
                return false
        }
        else if (this.workplaceIndustryCode != o.workplaceIndustryCode)
            return false
        if (this.workplacePhoneNumber == null) {
            if (o.workplacePhoneNumber != null)
                return false
        }
        else if (this.workplacePhoneNumber != o.workplacePhoneNumber)
            return false
        if (this.violationHistorySign == null) {
            if (o.violationHistorySign != null)
                return false
        }
        else if (this.violationHistorySign != o.violationHistorySign)
            return false
        if (this.nonManagedPropertySign == null) {
            if (o.nonManagedPropertySign != null)
                return false
        }
        else if (this.nonManagedPropertySign != o.nonManagedPropertySign)
            return false
        if (this.interfaceSign == null) {
            if (o.interfaceSign != null)
                return false
        }
        else if (this.interfaceSign != o.interfaceSign)
            return false
        if (this.corporateHousingAgency == null) {
            if (o.corporateHousingAgency != null)
                return false
        }
        else if (this.corporateHousingAgency != o.corporateHousingAgency)
            return false
        if (this.yearsOfService == null) {
            if (o.yearsOfService != null)
                return false
        }
        else if (this.yearsOfService != o.yearsOfService)
            return false
        if (this.industryPersonal == null) {
            if (o.industryPersonal != null)
                return false
        }
        else if (this.industryPersonal != o.industryPersonal)
            return false
        if (this.personalAnnualIncome == null) {
            if (o.personalAnnualIncome != null)
                return false
        }
        else if (this.personalAnnualIncome != o.personalAnnualIncome)
            return false
        if (this.cohabitantAnnualIncome == null) {
            if (o.cohabitantAnnualIncome != null)
                return false
        }
        else if (this.cohabitantAnnualIncome != o.cohabitantAnnualIncome)
            return false
        if (this.gender == null) {
            if (o.gender != null)
                return false
        }
        else if (this.gender != o.gender)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.logicalDeleteSign == null) 0 else this.logicalDeleteSign.hashCode())
        result = prime * result + this.tenantCode.hashCode()
        result = prime * result + (if (this.integratedClientCode == null) 0 else this.integratedClientCode.hashCode())
        result = prime * result + (if (this.tenantNameKanji == null) 0 else this.tenantNameKanji.hashCode())
        result = prime * result + (if (this.tenantNameKanji_2 == null) 0 else this.tenantNameKanji_2.hashCode())
        result = prime * result + (if (this.tenantNameKana == null) 0 else this.tenantNameKana.hashCode())
        result = prime * result + (if (this.transferVerificationKana1 == null) 0 else this.transferVerificationKana1.hashCode())
        result = prime * result + (if (this.transferVerificationKana2 == null) 0 else this.transferVerificationKana2.hashCode())
        result = prime * result + (if (this.headquartersNameKanji == null) 0 else this.headquartersNameKanji.hashCode())
        result = prime * result + (if (this.headquartersNameKana == null) 0 else this.headquartersNameKana.hashCode())
        result = prime * result + (if (this.searchKana == null) 0 else this.searchKana.hashCode())
        result = prime * result + (if (this.personalCorporateDivision == null) 0 else this.personalCorporateDivision.hashCode())
        result = prime * result + (if (this.corporateStatusDivision == null) 0 else this.corporateStatusDivision.hashCode())
        result = prime * result + (if (this.corporatePositionDivision == null) 0 else this.corporatePositionDivision.hashCode())
        result = prime * result + (if (this.representativeNameKanji == null) 0 else this.representativeNameKanji.hashCode())
        result = prime * result + (if (this.representativeNameKana == null) 0 else this.representativeNameKana.hashCode())
        result = prime * result + (if (this.representativeSearchKana == null) 0 else this.representativeSearchKana.hashCode())
        result = prime * result + (if (this.tenantDepartment == null) 0 else this.tenantDepartment.hashCode())
        result = prime * result + (if (this.tenantContactName == null) 0 else this.tenantContactName.hashCode())
        result = prime * result + (if (this.postalCode == null) 0 else this.postalCode.hashCode())
        result = prime * result + (if (this.prefectureCode == null) 0 else this.prefectureCode.hashCode())
        result = prime * result + (if (this.cityCode == null) 0 else this.cityCode.hashCode())
        result = prime * result + (if (this.townCode == null) 0 else this.townCode.hashCode())
        result = prime * result + (if (this.addressDetail == null) 0 else this.addressDetail.hashCode())
        result = prime * result + (if (this.buildingName == null) 0 else this.buildingName.hashCode())
        result = prime * result + (if (this.phoneNumber == null) 0 else this.phoneNumber.hashCode())
        result = prime * result + (if (this.faxNumber == null) 0 else this.faxNumber.hashCode())
        result = prime * result + (if (this.postalCode_2 == null) 0 else this.postalCode_2.hashCode())
        result = prime * result + (if (this.prefectureCode_2 == null) 0 else this.prefectureCode_2.hashCode())
        result = prime * result + (if (this.cityCode_2 == null) 0 else this.cityCode_2.hashCode())
        result = prime * result + (if (this.townCode_2 == null) 0 else this.townCode_2.hashCode())
        result = prime * result + (if (this.addressDetail_2 == null) 0 else this.addressDetail_2.hashCode())
        result = prime * result + (if (this.buildingName_2 == null) 0 else this.buildingName_2.hashCode())
        result = prime * result + (if (this.phoneNumber_2 == null) 0 else this.phoneNumber_2.hashCode())
        result = prime * result + (if (this.faxNumber_2 == null) 0 else this.faxNumber_2.hashCode())
        result = prime * result + (if (this.industryCode == null) 0 else this.industryCode.hashCode())
        result = prime * result + (if (this.birthDate == null) 0 else this.birthDate.hashCode())
        result = prime * result + (if (this.workplaceName == null) 0 else this.workplaceName.hashCode())
        result = prime * result + (if (this.workplacePrefectureCode == null) 0 else this.workplacePrefectureCode.hashCode())
        result = prime * result + (if (this.workplaceCityCode == null) 0 else this.workplaceCityCode.hashCode())
        result = prime * result + (if (this.workplaceTownCode == null) 0 else this.workplaceTownCode.hashCode())
        result = prime * result + (if (this.workplaceAddressDetail == null) 0 else this.workplaceAddressDetail.hashCode())
        result = prime * result + (if (this.workplaceBuildingName == null) 0 else this.workplaceBuildingName.hashCode())
        result = prime * result + (if (this.workplaceIndustryCode == null) 0 else this.workplaceIndustryCode.hashCode())
        result = prime * result + (if (this.workplacePhoneNumber == null) 0 else this.workplacePhoneNumber.hashCode())
        result = prime * result + (if (this.violationHistorySign == null) 0 else this.violationHistorySign.hashCode())
        result = prime * result + (if (this.nonManagedPropertySign == null) 0 else this.nonManagedPropertySign.hashCode())
        result = prime * result + (if (this.interfaceSign == null) 0 else this.interfaceSign.hashCode())
        result = prime * result + (if (this.corporateHousingAgency == null) 0 else this.corporateHousingAgency.hashCode())
        result = prime * result + (if (this.yearsOfService == null) 0 else this.yearsOfService.hashCode())
        result = prime * result + (if (this.industryPersonal == null) 0 else this.industryPersonal.hashCode())
        result = prime * result + (if (this.personalAnnualIncome == null) 0 else this.personalAnnualIncome.hashCode())
        result = prime * result + (if (this.cohabitantAnnualIncome == null) 0 else this.cohabitantAnnualIncome.hashCode())
        result = prime * result + (if (this.gender == null) 0 else this.gender.hashCode())
        return result
    }
}
