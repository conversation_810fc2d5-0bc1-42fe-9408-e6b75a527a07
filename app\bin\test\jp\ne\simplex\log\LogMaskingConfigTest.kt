package jp.ne.simplex.log

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class LogMaskingConfigTest {

    private val target = LogMaskingConfig()

    @Nested
    @DisplayName("JsonBody形式のログ")
    inner class Scenario1 {

        @Test
        @DisplayName("個人情報がない場合は、そのまま出力されること")
        fun case_01() {
            val input = "{\"id\": \"ID00000002\", \"keyword\": \"value\"}"
            val expected = "{\"id\": \"ID00000002\", \"keyword\": \"value\"}"
            val actual = target.maskValue(input)

            assertEquals(expected, actual)
        }
    }

    @Nested
    @DisplayName("QueryParam形式のログ")
    inner class Scenario2 {

        @Nested
        @DisplayName("個人情報がある場合はマスキングされて出力されること")
        inner class Scenario2x1 {

            @Test
            @DisplayName("個人情報がない文字列は、そのまま出力されること")
            fun case_01() {
                val input = "https://test.co.jp?id=ID00000002&keyword=value"
                val expected = "https://test.co.jp?id=ID00000002&keyword=value"
                val actual = target.maskValue(input)

                assertEquals(expected, actual)
            }

            @Test
            @DisplayName("個人情報がある文字列は、マスキングされて出力されること")
            fun case_02() {
                val input =
                    "https://test.co.jp?id=ID00000002&password=abcd1234&employeeName=あいうえお"
                val expected =
                    "https://test.co.jp?id=ID00000002&password=******&employeeName=******"
                val actual = target.maskValue(input)

                assertEquals(expected, actual)
            }
        }

        @Nested
        @DisplayName("プロトコルに関わらずマスキングが行われること")
        inner class Scenario2x2 {

            @Test
            @DisplayName("httpで始まる文字列はマスキングされること")
            fun case_01() {
                val input =
                    "http://test.co.jp?id=ID00000002&password=abcd1234"
                val expected =
                    "http://test.co.jp?id=ID00000002&password=******"
                val actual = target.maskValue(input)

                assertEquals(expected, actual)
            }

            @Test
            @DisplayName("httpsで始まる文字列はマスキングされること")
            fun case_02() {
                val input =
                    "https://test.co.jp?id=ID00000002&password=abcd1234"
                val expected =
                    "https://test.co.jp?id=ID00000002&password=******"
                val actual = target.maskValue(input)

                assertEquals(expected, actual)
            }
        }

        @Nested
        @DisplayName("エンドポイントの有無に関わらずマスキングが行われること")
        inner class Scenario2x3 {

            @Test
            @DisplayName("エンドポイントがない文字列はマスキングされること")
            fun case_01() {
                val input = "id=ID00000002&password=abcd1234"
                val expected = "id=ID00000002&password=******"
                val actual = target.maskValue(input)

                assertEquals(expected, actual)
            }

            @Test
            @DisplayName("エンドポイントがある文字列はマスキングされること")
            fun case_02() {
                val input =
                    "https://test.co.jp?id=ID00000002&password=abcd1234"
                val expected =
                    "https://test.co.jp?id=ID00000002&password=******"
                val actual = target.maskValue(input)

                assertEquals(expected, actual)
            }
        }

        @Nested
        @DisplayName("クエリパラメータの数によらずマスキングが行われること")
        inner class Scenario2x4 {

            @Test
            @DisplayName("クエリパラメータが0個の文字列はそのまま表示されること")
            fun case_01() {
                val input = "https://test.co.jp"
                val expected = "https://test.co.jp"
                val actual = target.maskValue(input)

                assertEquals(expected, actual)
            }

            @Test
            @DisplayName("クエリパラメータが1個の文字列はマスキングされること")
            fun case_02() {
                val input = "https://test.co.jp&password=abcd1234"
                val expected = "https://test.co.jp&password=******"
                val actual = target.maskValue(input)

                assertEquals(expected, actual)
            }

            @Test
            @DisplayName("クエリパラメータが複数の文字列はマスキングされること")
            fun case_03() {
                val input = "https://test.co.jp?password=abcd1234"
                val expected = "https://test.co.jp?password=******"
                val actual = target.maskValue(input)

                assertEquals(expected, actual)
            }
        }

        @Nested
        @DisplayName("空白があるとマスキングがされないこと")
        inner class Scenario2x5 {

            @Test
            @DisplayName("クエリパラメータに半角の空白がある文字列は全体がマスキングされないこと")
            fun case_01() {
                val input = "https://test.co.jp?password= abcd1234"
                val expected = "https://test.co.jp?password= abcd1234"
                val actual = target.maskValue(input)

                assertEquals(expected, actual)
            }

            @Test
            @DisplayName("クエリパラメータに全角の空白がある文字列は全体がマスキングされないこと")
            fun case_02() {
                val input = "https://test.co.jp?password=　abcd1234"
                val expected = "https://test.co.jp?password=　abcd1234"
                val actual = target.maskValue(input)

                assertEquals(expected, actual)
            }

            @Test
            @DisplayName("クエリパラメータに空白がない文字列はマスキングされること")
            fun case_03() {
                val input = "https://test.co.jp?password=abcd1234"
                val expected = "https://test.co.jp?password=******"
                val actual = target.maskValue(input)

                assertEquals(expected, actual)
            }
        }
    }
}
