-- TABLE: PARKING_CONTRACT_POSSIBILITY(駐車場契約可否)

CREATE TABLE PARKING_CONTRACT_POSSIBILITY(
     ORDER_CODE                                   varchar(7)        NOT NULL    
,    IS_FIRST_PARKING_CONTRACT_POSSIBLE           varchar(1)                    
,    IS_SECOND_PARKING_CONTRACT_POSSIBLE          varchar(1)                    
,    IS_AUTO_JUDGE                                varchar(1)                    
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    DELETE_FLAG                                  varchar(1)        NOT NULL    
,    CONSTRAINT PK_PARKING_CONTRACT_POSSIBILIT PRIMARY KEY (ORDER_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PARKING_CONTRACT_POSSIBILITY IS '駐車場契約可否 既存システム物理名: -';
COMMENT ON COLUMN PARKING_CONTRACT_POSSIBILITY.ORDER_CODE IS '受注コード 既存システム物理名: -';
COMMENT ON COLUMN PARKING_CONTRACT_POSSIBILITY.IS_FIRST_PARKING_CONTRACT_POSSIBLE IS '駐車場一台目契約可否 既存システム物理名: - 0: 可, 1: 不可, 2: 要問合せ';
COMMENT ON COLUMN PARKING_CONTRACT_POSSIBILITY.IS_SECOND_PARKING_CONTRACT_POSSIBLE IS '駐車場二台目契約可否 既存システム物理名: - 0: 可, 1: 不可, 2: 要問合せ';
COMMENT ON COLUMN PARKING_CONTRACT_POSSIBILITY.IS_AUTO_JUDGE IS '契約可否自動判定有無 既存システム物理名: - 0: 自動判定なし, 1: 自動判定あり';
COMMENT ON COLUMN PARKING_CONTRACT_POSSIBILITY.CREATION_DATE IS '作成年月日 既存システム物理名: -';
COMMENT ON COLUMN PARKING_CONTRACT_POSSIBILITY.CREATION_TIME IS '作成時刻 既存システム物理名: -';
COMMENT ON COLUMN PARKING_CONTRACT_POSSIBILITY.CREATOR IS '作成者 既存システム物理名: -';
COMMENT ON COLUMN PARKING_CONTRACT_POSSIBILITY.UPDATE_DATE IS '更新年月日 既存システム物理名: -';
COMMENT ON COLUMN PARKING_CONTRACT_POSSIBILITY.UPDATE_TIME IS '更新時刻 既存システム物理名: -';
COMMENT ON COLUMN PARKING_CONTRACT_POSSIBILITY.UPDATER IS '更新者 既存システム物理名: -';
COMMENT ON COLUMN PARKING_CONTRACT_POSSIBILITY.DELETE_FLAG IS '削除フラグ 既存システム物理名: -';
