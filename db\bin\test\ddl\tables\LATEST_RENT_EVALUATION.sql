-- TABLE: LATEST_RENT_EVALUATION(最新家賃査定)

CREATE TABLE LATEST_RENT_EVALUATION(
     CREATION_MONTH                               numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_MONTH                                 numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    ASSESSMENT_REVIEW_NUMBER                     varchar(8)        NOT NULL    
,    LATEST_RENT_ASSESSMENT_HISTORY               varchar(2)        NOT NULL    
,    BUILDING_CODE                                varchar(9)                    
,    PROPERTY_CODE                                varchar(5)                    
,    ROOM_PARKING_DIVISION                        varchar(1)                    
,    TAX_DIVISION                                 varchar(1)                    
,    KEY_MONEY_MONTHS                             numeric(3,1)                  
,    DEPOSIT_MONTHS                               numeric(3,1)                  
,    DEPRECIATION_MONTHS                          numeric(3,1)                  
,    <PERSON><PERSON><PERSON>_MONEY_AMOUNT                             numeric(9,0)                  
,    <PERSON><PERSON>Y_MONEY_INOUT_DIVISION                     varchar(1)                    
,    ADDITIONAL_KEY_MONEY_AMOUNT                  numeric(9,0)                  
,    DEPOSIT_AMOUNT                               numeric(9,0)                  
,    DEPRECIATION                                 numeric(9,0)                  
,    RENT                                         numeric(9,0)                  
,    RENT_INOUT_DIVISION                          varchar(1)                    
,    PARKING_FEE_COMBINED_SIGN                    numeric(1,0)                  
,    PARKING_FEE                                  numeric(9,0)                  
,    PARKING_FEE_INOUT_DIVISION                   varchar(1)                    
,    COMMON_FEE                                   numeric(9,0)                  
,    COMMON_FEE_INOUT_DIVISION                    varchar(1)                    
,    NEIGHBORHOOD_ASSOCIATION_FEE                 numeric(9,0)                  
,    NEW_RENT_ADDED                               numeric(9,0)                  
,    STANDARD_RENT_FOR_COOP                       numeric(9,0)                  
,    INITIAL_SET_RENT                             numeric(9,0)                  
,    MANAGEMENT_FEE                               numeric(9,0)                  
,    MANAGEMENT_FEE_INOUT_DIVISION                varchar(1)                    
,    MANAGEMENT_FEE_DISCOUNT_AMOUNT               numeric(9,0)                  
,    COOP_FEE                                     numeric(9,0)                  
,    COOP_BENEFIT_AMOUNT                          numeric(9,0)                  
,    MNG_CONTRACT_INITIAL_PAYMENT                 numeric(9,0)                  
,    MNG_CONTRACT_INITIAL_PAYMENT_INOUT           varchar(1)                    
,    TENANT_REGISTRATION_FEE                      numeric(9,0)                  
,    TENANT_REGISTRATION_FEE_INOUT                varchar(1)                    
,    MAINTENANCE_FEE                              numeric(9,0)                  
,    MAINTENANCE_FEE_INOUT_DIVISION               varchar(1)                    
,    NEIGHBORHOOD_FEE_PAID_BY_DAITO               numeric(9,0)                  
,    GUARANTEE_DIVISION                           varchar(1)                    
,    COOP_MEMBERSHIP_FEE                          numeric(9,0)                  
,    WATER_MANAGEMENT_FEE                         numeric(9,0)                  
,    LATEST_RENT_ASSESSMENT_APPROVER              varchar(6)                    
,    LATEST_RENT_ASSESSMENT_DATE                  numeric(8,0)                  
,    BROKER_APPLICATION_CLEAR_SIGN                numeric(1,0)                  
,    BROKER_APPLICATION_OUTPUT_DATE               numeric(8,0)                  
,    OUTPUT_TAX_RATE_DIVISION                     varchar(1)                    
,    BUILDING_UNIT_OUTPUT_COUNT                   numeric(2,0)                  
,    ROOM_UNIT_OUTPUT_COUNT                       numeric(2,0)                  
,    BROKER_APPLICATION_COLLECTION_DIVISION       varchar(1)                    
,    BROKER_APPLICATION_OUTPUT_UNIT_DIVISION      varchar(1)                    
,    MULTIPLE_OUTPUT_NUMBER                       numeric(4,0)                  
,    BROKER_APPLICATION_FORM_NUMBER               varchar(7)                    
,    BROKER_APPLICATION_APPROVAL_DATE             numeric(8,0)                  
,    BROKER_APPLICATION_COLLECTION_DATE           numeric(8,0)                  
,    INITIAL_COLLECTION_ENTRY_DATE                numeric(8,0)                  
,    BROKER_APPLICATION_RESPONSIBLE_BRANCH        varchar(6)                    
,    BROKER_APPLICATION_RESPONSIBLE_PERSON        varchar(6)                    
,    ROOM_CODE                                    varchar(5)                    
,    DATA_MIGRATION_ORIGIN_KEY1                   varchar(15)                   
,    DATA_MIGRATION_ORIGIN_KEY2                   varchar(15)                   
,    TRANSMISSION_DIVISION                        varchar(1)                    
,    CONTRACT_RENT                                numeric(9,0)                  
,    NON_STANDARD_DIVISION                        numeric(1,0)                  
,    COLLECTION_RESPONSIBLE_PERSON_CODE           varchar(6)                    
,    RECRUITMENT_START_DATE                       numeric(8,0)                  
,    POST_VACANCY_COLLECTION_DATE                 numeric(8,0)                  
,    CHALLENGE_END_DATE                           numeric(8,0)                  
,    CHALLENG_RENT                                numeric(9,0)                  
,    TENANCY_ONE_YEAR_OVER_SIGN                   numeric(1,0)                  
,    RECRUITMENT_RENT                             numeric(9,0)                  
,    CONSTRAINT UQ_LATEST_RENT_EVALUATION UNIQUE (BUILDING_CODE, PROPERTY_CODE, ROOM_PARKING_DIVISION)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE LATEST_RENT_EVALUATION IS '最新家賃査定 既存システム物理名: EAC30P';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.CREATION_MONTH IS '作成年月 既存システム物理名: EAC01D 駐車場区分=2';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.CREATION_TIME IS '作成時刻 既存システム物理名: EAC02H';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.UPDATE_MONTH IS '更新年月 既存システム物理名: EAC03D';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.UPDATE_TIME IS '更新時刻 既存システム物理名: EAC04H';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EAC05N';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.UPDATER IS '更新者 既存システム物理名: EAC06C';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.ASSESSMENT_REVIEW_NUMBER IS '査定審査書番号 既存システム物理名: EACBHN';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.LATEST_RENT_ASSESSMENT_HISTORY IS '最新家賃査定履歴連 既存システム物理名: EAC08N';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.BUILDING_CODE IS '建物コード 既存システム物理名: EACABC';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.PROPERTY_CODE IS '物件コード 既存システム物理名: EAC37C';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.ROOM_PARKING_DIVISION IS '部屋・駐車場区分 既存システム物理名: EACFVB';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.TAX_DIVISION IS '課税区分 既存システム物理名: EACBKB';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.KEY_MONEY_MONTHS IS '礼金(権利金)月数 既存システム物理名: EAC09Q';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.DEPOSIT_MONTHS IS '保証金(敷金)月数 既存システム物理名: EAC10Q';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.DEPRECIATION_MONTHS IS '償却費月数 既存システム物理名: EAC13Q';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.KEY_MONEY_AMOUNT IS '礼金(権利金)金額 既存システム物理名: EAC11A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.KEY_MONEY_INOUT_DIVISION IS '礼金内外区分 既存システム物理名: EAC42B';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.ADDITIONAL_KEY_MONEY_AMOUNT IS '上乗礼金額(内額) 既存システム物理名: EAC43A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.DEPOSIT_AMOUNT IS '保証金(敷金)金額 既存システム物理名: EAC12A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.DEPRECIATION IS '償却費 既存システム物理名: EAC14A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.RENT IS '家賃 既存システム物理名: EAC15A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.RENT_INOUT_DIVISION IS '家賃内外区分 既存システム物理名: EAC44B';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.PARKING_FEE_COMBINED_SIGN IS '駐車料合算サイン 既存システム物理名: EAC16S';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.PARKING_FEE IS '駐車料 既存システム物理名: EAC17A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.PARKING_FEE_INOUT_DIVISION IS '駐車料内外区分 既存システム物理名: EAC45B';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.COMMON_FEE IS '共益費 既存システム物理名: EAC18A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.COMMON_FEE_INOUT_DIVISION IS '共益費内外区分 既存システム物理名: EAC46B';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.NEIGHBORHOOD_ASSOCIATION_FEE IS '町内会費(回収) 既存システム物理名: EAC47A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.NEW_RENT_ADDED IS '新築上乗家賃 既存システム物理名: EAC23A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.STANDARD_RENT_FOR_COOP IS '共済会用基準家賃 既存システム物理名: EAC36A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.INITIAL_SET_RENT IS '初回設定賃料 既存システム物理名: EAC57A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.MANAGEMENT_FEE IS '管理費 既存システム物理名: EAC20A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.MANAGEMENT_FEE_INOUT_DIVISION IS '管理費内外区分 既存システム物理名: EAC48B';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.MANAGEMENT_FEE_DISCOUNT_AMOUNT IS '管理費値引き額 既存システム物理名: EAC49A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.COOP_FEE IS '共済会費 既存システム物理名: EAC21A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.COOP_BENEFIT_AMOUNT IS '共済給付額 既存システム物理名: EACO7A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.MNG_CONTRACT_INITIAL_PAYMENT IS '管理契約時金 既存システム物理名: EAC39A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.MNG_CONTRACT_INITIAL_PAYMENT_INOUT IS '管理契約時金内外 既存システム物理名: EAC50B';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.TENANT_REGISTRATION_FEE IS 'テナント登録料 既存システム物理名: EAC51A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.TENANT_REGISTRATION_FEE_INOUT IS 'テナント登録料内外 既存システム物理名: EAC52B';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.MAINTENANCE_FEE IS '維持費 既存システム物理名: EAC19A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.MAINTENANCE_FEE_INOUT_DIVISION IS '維持費内外区分 既存システム物理名: EAC53B';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.NEIGHBORHOOD_FEE_PAID_BY_DAITO IS '町内会費(大東払) 既存システム物理名: EAC54A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.GUARANTEE_DIVISION IS '保証区分 既存システム物理名: EACBQB';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.COOP_MEMBERSHIP_FEE IS '共済会加入金 既存システム物理名: EAC24A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.WATER_MANAGEMENT_FEE IS '水道管理料 既存システム物理名: EAC56A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.LATEST_RENT_ASSESSMENT_APPROVER IS '最新家賃査定承認者 既存システム物理名: EAC25C';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.LATEST_RENT_ASSESSMENT_DATE IS '最新家賃査定日 既存システム物理名: EAC26D';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.BROKER_APPLICATION_CLEAR_SIGN IS '斡旋申込書クリアサイン 既存システム物理名: EAC40S';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.BROKER_APPLICATION_OUTPUT_DATE IS '斡旋申込書出力日 既存システム物理名: EAC27D';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.OUTPUT_TAX_RATE_DIVISION IS '出力時消費税率区分 既存システム物理名: EAC55B';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.BUILDING_UNIT_OUTPUT_COUNT IS '建物単位出力回数 既存システム物理名: EAC34T';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.ROOM_UNIT_OUTPUT_COUNT IS '部屋単位出力回数 既存システム物理名: EAC38T';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.BROKER_APPLICATION_COLLECTION_DIVISION IS '斡旋申込書回収区分 既存システム物理名: EAC32B';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.BROKER_APPLICATION_OUTPUT_UNIT_DIVISION IS '斡旋申込書出力単位区分 既存システム物理名: EACGBB';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.MULTIPLE_OUTPUT_NUMBER IS '複数出力時番号 既存システム物理名: EAC22N';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.BROKER_APPLICATION_FORM_NUMBER IS '斡旋申込書帳票番号 既存システム物理名: EAC33N';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.BROKER_APPLICATION_APPROVAL_DATE IS '斡旋申込書承認日 既存システム物理名: EAC41D';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.BROKER_APPLICATION_COLLECTION_DATE IS '斡旋申込書回収日 既存システム物理名: EAC28D';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.INITIAL_COLLECTION_ENTRY_DATE IS '初回回収入力日 既存システム物理名: EAC58D';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.BROKER_APPLICATION_RESPONSIBLE_BRANCH IS '斡旋申込書担当支店 既存システム物理名: EAC31C';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.BROKER_APPLICATION_RESPONSIBLE_PERSON IS '斡旋申込書担当者コ 既存システム物理名: EAC35C';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.ROOM_CODE IS '部屋コード 既存システム物理名: EACACC';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.DATA_MIGRATION_ORIGIN_KEY1 IS 'データ移行元キー1 既存システム物理名: EAC29N';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.DATA_MIGRATION_ORIGIN_KEY2 IS 'データ移行元キー2 既存システム物理名: EAC30N';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.TRANSMISSION_DIVISION IS '送信区分 既存システム物理名: EAC59K';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.CONTRACT_RENT IS '契約家賃 既存システム物理名: EACKKY';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.NON_STANDARD_DIVISION IS '定型外区分 既存システム物理名: EACTGS';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.COLLECTION_RESPONSIBLE_PERSON_CODE IS '回収担当者コード 既存システム物理名: EAC60C';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.RECRUITMENT_START_DATE IS '募集開始日 既存システム物理名: EAC61D';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.POST_VACANCY_COLLECTION_DATE IS '明渡直後回収日 既存システム物理名: EAC62D';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.CHALLENGE_END_DATE IS 'チャレンジ終了日 既存システム物理名: EAC63D';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.CHALLENG_RENT IS 'チャレンジ家賃 既存システム物理名: EAC64A';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.TENANCY_ONE_YEAR_OVER_SIGN IS '入居期間１年超サイン 既存システム物理名: EAC65S';
COMMENT ON COLUMN LATEST_RENT_EVALUATION.RECRUITMENT_RENT IS '募集家賃 既存システム物理名: EAC66A';
