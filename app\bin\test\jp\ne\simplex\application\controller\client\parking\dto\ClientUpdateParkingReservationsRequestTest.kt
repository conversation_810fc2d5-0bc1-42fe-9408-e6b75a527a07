package jp.ne.simplex.application.controller.client.parking.dto

import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FunSpec
import jp.ne.simplex.application.model.ParkingReservation
import jp.ne.simplex.exception.ClientValidationException

class ClientUpdateParkingReservationsRequestTest : FunSpec({

    context("駐車場予約情報更新APIのリクエストのバリデーション") {
        context("駐車場予約更新レコードのバリデーション") {
            val baseRecord = ClientParkingReservationsUpdateRequest.Record(
                parkingReservationId = "91c7e3d0-53ab-5f26-8f48-7794e2ba8150",
                reservationStatus = ParkingReservation.Status.TENTATIVE,
                reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                reserveStartDate = null,
                reserveEndDate = null,
                receptionStaff = "シンプレクス受付担当者",
                reserverName = "シンプレクス予約者",
                reserverTel = "03-1234-5678",
                remarks = "駐車場予約のメモです"
            )

            context("駐車場予約IDの文字数は36文字のみ許容すること") {
                test("駐車場予約IDを35文字の場合、ClientValidationExceptionがスローされること") {
                    shouldThrow<ClientValidationException> {
                        baseRecord.copy(
                            parkingReservationId = "◯".repeat(35)
                        ).toServiceInterface()
                    }
                }
                test("駐車場予約IDを36文字の場合、Exceptionがスローされないこと") {
                    shouldNotThrow<Exception> {
                        baseRecord.copy(
                            parkingReservationId = "◯".repeat(36)
                        ).toServiceInterface()
                    }
                }
                test("駐車場予約IDを37文字の場合、ClientValidationExceptionがスローされること") {
                    shouldThrow<ClientValidationException> {
                        baseRecord.copy(
                            parkingReservationId = "◯".repeat(37)
                        ).toServiceInterface()
                    }
                }
            }

            context("受付担当者が指定されている場合は42文字以内のみ許容すること") {
                test("受付担当者を指定されていない場合、Exceptionがスローされないこと") {
                    shouldNotThrow<Exception> {
                        baseRecord.copy(
                            receptionStaff = null
                        ).toServiceInterface()
                    }
                }
                test("受付担当者が42文字の場合、Exceptionがスローされないこと") {
                    shouldNotThrow<Exception> {
                        baseRecord.copy(
                            receptionStaff = "◯".repeat(42)
                        ).toServiceInterface()
                    }
                }
                test("受付担当者が43文字の場合、ClientValidationExceptionがスローされること") {
                    shouldThrow<ClientValidationException> {
                        baseRecord.copy(
                            receptionStaff = "◯".repeat(43)
                        ).toServiceInterface()
                    }
                }
            }

            context("利用者氏名が指定されている場合は42文字以内のみ許容すること") {
                test("利用者氏名を指定されていない場合、Exceptionがスローされないこと") {
                    shouldNotThrow<Exception> {
                        baseRecord.copy(
                            reserverName = null
                        ).toServiceInterface()
                    }
                }
                test("利用者氏名が42文字の場合、Exceptionがスローされないこと") {
                    shouldNotThrow<Exception> {
                        baseRecord.copy(
                            reserverName = "◯".repeat(42)
                        ).toServiceInterface()
                    }
                }
                test("利用者氏名が43文字の場合、ClientValidationExceptionがスローされること") {
                    shouldThrow<ClientValidationException> {
                        baseRecord.copy(
                            reserverName = "◯".repeat(43)
                        ).toServiceInterface()
                    }
                }
            }

            context("予約種別が申込(手動), 作業, 場所変更以外は許容しないこと") {
                test("予約種別が申込(手動), 作業, 場所変更以外の場合、ClientValidationExceptionがスローされること") {
                    ParkingReservation.Type.entries.filter {
                        it != ParkingReservation.Type.MANUAL_APPLICATION
                                && it != ParkingReservation.Type.WORK
                                && it != ParkingReservation.Type.REPLACE
                    }.forEach {
                        shouldThrow<ClientValidationException> {
                            baseRecord.copy(
                                reservationType = it
                            ).toServiceInterface()
                        }
                    }
                }
            }

            context("予約種別が申込(手動)の場合のバリデーション") {
                val baseRecordManual = baseRecord.copy(
                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION
                )

                context("予約状態が仮申込, 予約中, キャンセル以外は許容しないこと") {
                    test("予約状態が仮申込, 予約中, キャンセル以外の場合、ClientValidationExceptionがスローされること") {
                        ParkingReservation.Status.entries.filter {
                            it != ParkingReservation.Status.TENTATIVE
                                    && it != ParkingReservation.Status.RESERVATION
                                    && it != ParkingReservation.Status.CANCEL
                        }.forEach {
                            shouldThrow<ClientValidationException> {
                                baseRecordManual.copy(
                                    reservationStatus = it
                                ).toServiceInterface()
                            }
                        }
                    }
                }

                context("予約状態が仮申込の場合のバリデーション") {
                    val baseRecordManualTentative = baseRecordManual.copy(
                        reservationStatus = ParkingReservation.Status.TENTATIVE
                    )
                    context("予約開始日と予約終了日の指定は許容しないこと") {
                        test("予約開始日が指定されている場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordManualTentative.copy(
                                    reserveStartDate = "20250101"
                                ).toServiceInterface()
                            }
                        }
                        test("予約終了日が指定されている場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordManualTentative.copy(
                                    reserveEndDate = "20250101"
                                ).toServiceInterface()
                            }
                        }
                        test("予約開始日と予約終了日が指定されていない場合、Exceptionがスローされないこと") {
                            shouldNotThrow<Exception> {
                                baseRecordManualTentative.copy(
                                    reserveStartDate = null,
                                    reserveEndDate = null
                                ).toServiceInterface()
                            }
                        }
                    }
                }

                context("予約状態が予約中の場合のバリデーション") {
                    val baseRecordManualReservation = baseRecordManual.copy(
                        reservationStatus = ParkingReservation.Status.RESERVATION
                    )
                    context("予約開始日が必須で指定されること") {
                        test("予約開始日が指定されていない場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordManualReservation.copy(
                                    reserveStartDate = null
                                ).toServiceInterface()
                            }
                        }
                        test("予約開始日が不正な日付の場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordManualReservation.copy(
                                    reserveStartDate = "99999999"
                                ).toServiceInterface()
                            }
                        }
                    }

                    context("予約終了日の指定は許容しないこと") {
                        test("予約終了日が指定されている場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordManualReservation.copy(
                                    reserveEndDate = "20250101"
                                ).toServiceInterface()
                            }
                        }
                    }

                    test("予約開始日に日付が指定されて予約終了日が指定なしの場合、Exceptionがスローされないこと") {
                        shouldNotThrow<Exception> {
                            baseRecordManualReservation.copy(
                                reserveStartDate = "20250101",
                                reserveEndDate = null
                            ).toServiceInterface()
                        }
                    }
                }
            }

            context("予約種別が作業の場合のバリデーション") {
                val baseRecordWork = baseRecord.copy(
                    reservationType = ParkingReservation.Type.WORK
                )

                context("予約状態が予約中, キャンセル以外は許容しないこと") {
                    test("予約状態が予約中, キャンセル以外の場合、ClientValidationExceptionがスローされること") {
                        ParkingReservation.Status.entries.filter {
                            it != ParkingReservation.Status.RESERVATION
                                    && it != ParkingReservation.Status.CANCEL
                        }.forEach {
                            shouldThrow<ClientValidationException> {
                                baseRecordWork.copy(
                                    reservationStatus = it
                                ).toServiceInterface()
                            }
                        }
                    }
                }

                context("予約状態が予約中の場合のバリデーション") {
                    val baseRecordWorkReservation = baseRecordWork.copy(
                        reservationStatus = ParkingReservation.Status.RESERVATION
                    )
                    context("予約開始日と予約終了日が必須で指定されること") {
                        test("予約開始日が指定されていない場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordWorkReservation.copy(
                                    reserveStartDate = null
                                ).toServiceInterface()
                            }
                        }
                        test("予約開始日が不正な日付の場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordWorkReservation.copy(
                                    reserveStartDate = "99999999"
                                ).toServiceInterface()
                            }
                        }
                        test("予約終了日が指定されていない場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordWorkReservation.copy(
                                    reserveEndDate = null
                                ).toServiceInterface()
                            }
                        }
                        test("予約終了日が不正な日付の場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordWorkReservation.copy(
                                    reserveEndDate = "99999999"
                                ).toServiceInterface()
                            }
                        }
                    }

                    context("予約開始日から予約終了日までが31日以内でないと許容しないこと") {
                        test("予約開始日が予約終了日以降の場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordWorkReservation.copy(
                                    reserveStartDate = "20250102",
                                    reserveEndDate = "20250101"
                                ).toServiceInterface()
                            }
                        }
                        test("予約開始日が予約終了日と同じ場合、Exceptionがスローされないこと") {
                            shouldNotThrow<Exception> {
                                baseRecordWorkReservation.copy(
                                    reserveStartDate = "20250101",
                                    reserveEndDate = "20250101"
                                ).toServiceInterface()
                            }
                        }
                        test("予約終了日が予約開始日の32日後の場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordWorkReservation.copy(
                                    reserveStartDate = "20250101",
                                    reserveEndDate = "20250202"
                                ).toServiceInterface()
                            }
                        }
                        test("予約終了日が予約開始日の31日後の場合、Exceptionがスローされないこと") {
                            shouldNotThrow<Exception> {
                                baseRecordWorkReservation.copy(
                                    reserveStartDate = "20250101",
                                    reserveEndDate = "20250201"
                                ).toServiceInterface()
                            }
                        }
                    }
                }
            }

            context("予約種別が場所変更の場合のバリデーション") {
                val baseRecordReplace = baseRecord.copy(
                    reservationType = ParkingReservation.Type.REPLACE
                )

                context("予約状態が予約中, キャンセル以外は許容しないこと") {
                    test("予約状態が予約中, キャンセル以外の場合、ClientValidationExceptionがスローされること") {
                        ParkingReservation.Status.entries.filter {
                            it != ParkingReservation.Status.RESERVATION
                                    && it != ParkingReservation.Status.CANCEL
                        }.forEach {
                            shouldThrow<ClientValidationException> {
                                baseRecordReplace.copy(
                                    reservationStatus = it
                                ).toServiceInterface()
                            }
                        }
                    }
                }

                context("予約状態が予約中の場合のバリデーション") {
                    val baseRecordReplaceReservation = baseRecordReplace.copy(
                        reservationStatus = ParkingReservation.Status.RESERVATION
                    )
                    context("予約開始日と予約終了日の指定は許容しないこと") {
                        test("予約開始日が指定されている場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordReplaceReservation.copy(
                                    reserveStartDate = "20250101"
                                ).toServiceInterface()
                            }
                        }
                        test("予約終了日が指定されている場合、ClientValidationExceptionがスローされること") {
                            shouldThrow<ClientValidationException> {
                                baseRecordReplaceReservation.copy(
                                    reserveEndDate = "20250101"
                                ).toServiceInterface()
                            }
                        }
                        test("予約開始日と予約終了日が指定されていない場合、Exceptionがスローされないこと") {
                            shouldNotThrow<Exception> {
                                baseRecordReplaceReservation.copy(
                                    reserveStartDate = null,
                                    reserveEndDate = null
                                ).toServiceInterface()
                            }
                        }
                    }
                }
            }
        }
    }
})
