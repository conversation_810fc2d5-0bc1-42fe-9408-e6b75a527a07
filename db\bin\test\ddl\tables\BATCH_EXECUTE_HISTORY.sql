-- TABLE: BATCH_EXECUTE_HISTORY(定期バッチ実行履歴)

CREATE TABLE BATCH_EXECUTE_HISTORY(
     EXECUTION_DATETIME                           timestamp         NOT NULL    
,    EXECUTION_DATE                               varchar(8)        NOT NULL    
,    BATCH_TYPE                                   varchar(1)        NOT NULL    
,    CONSTRAINT PK_BATCH_EXECUTE_HISTORY PRIMARY KEY (EXECUTION_DATE, BATCH_TYPE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BATCH_EXECUTE_HISTORY IS '定期バッチ実行履歴 既存システム物理名: -';
COMMENT ON COLUMN BATCH_EXECUTE_HISTORY.EXECUTION_DATETIME IS '実行日時 既存システム物理名: -';
COMMENT ON COLUMN BATCH_EXECUTE_HISTORY.EXECUTION_DATE IS '実行日付 既存システム物理名: -';
COMMENT ON COLUMN BATCH_EXECUTE_HISTORY.BATCH_TYPE IS 'バッチ区分 既存システム物理名: -';
