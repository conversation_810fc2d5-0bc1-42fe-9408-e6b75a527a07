/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.GasparBuildingInfoMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.GasparBuildingInfoMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * ガスパル建物情報マスタ 既存システム物理名: FFD10P
 */
@Suppress("UNCHECKED_CAST")
open class GasparBuildingInfoMasterRecord private constructor() : TableRecordImpl<GasparBuildingInfoMasterRecord>(GasparBuildingInfoMasterTable.GASPAR_BUILDING_INFO_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creationProgramId: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var creationTerminalId: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var creationResponsibleCd: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateDate: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var updateTime: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var updateTerminalId: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var updateResponsibleCd: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(10, value)
        get(): Byte? = get(10) as Byte?

    open var buildingCd: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var orderCd: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var salesOfficeCd: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var propertyResponsibleCd: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var managementBranchCd: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var managementResponsibleCd: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var northOrderCd: Short?
        set(value): Unit = set(17, value)
        get(): Short? = get(17) as Short?

    open var daikenBranchCd: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var transferDestinationType: Byte?
        set(value): Unit = set(19, value)
        get(): Byte? = get(19) as Byte?

    /**
     * Create a detached, initialised GasparBuildingInfoMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creationProgramId: String? = null, creationTerminalId: String? = null, creationResponsibleCd: String? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updateTerminalId: String? = null, updateResponsibleCd: String? = null, logicalDeleteSign: Byte? = null, buildingCd: String? = null, orderCd: String? = null, salesOfficeCd: String? = null, propertyResponsibleCd: String? = null, managementBranchCd: String? = null, managementResponsibleCd: String? = null, northOrderCd: Short? = null, daikenBranchCd: String? = null, transferDestinationType: Byte? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creationProgramId = creationProgramId
        this.creationTerminalId = creationTerminalId
        this.creationResponsibleCd = creationResponsibleCd
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updateTerminalId = updateTerminalId
        this.updateResponsibleCd = updateResponsibleCd
        this.logicalDeleteSign = logicalDeleteSign
        this.buildingCd = buildingCd
        this.orderCd = orderCd
        this.salesOfficeCd = salesOfficeCd
        this.propertyResponsibleCd = propertyResponsibleCd
        this.managementBranchCd = managementBranchCd
        this.managementResponsibleCd = managementResponsibleCd
        this.northOrderCd = northOrderCd
        this.daikenBranchCd = daikenBranchCd
        this.transferDestinationType = transferDestinationType
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised GasparBuildingInfoMasterRecord
     */
    constructor(value: GasparBuildingInfoMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creationProgramId = value.creationProgramId
            this.creationTerminalId = value.creationTerminalId
            this.creationResponsibleCd = value.creationResponsibleCd
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updateTerminalId = value.updateTerminalId
            this.updateResponsibleCd = value.updateResponsibleCd
            this.logicalDeleteSign = value.logicalDeleteSign
            this.buildingCd = value.buildingCd
            this.orderCd = value.orderCd
            this.salesOfficeCd = value.salesOfficeCd
            this.propertyResponsibleCd = value.propertyResponsibleCd
            this.managementBranchCd = value.managementBranchCd
            this.managementResponsibleCd = value.managementResponsibleCd
            this.northOrderCd = value.northOrderCd
            this.daikenBranchCd = value.daikenBranchCd
            this.transferDestinationType = value.transferDestinationType
            resetChangedOnNotNull()
        }
    }
}
