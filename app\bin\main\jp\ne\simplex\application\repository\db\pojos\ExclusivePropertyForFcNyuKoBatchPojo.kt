package jp.ne.simplex.application.repository.db.pojos

import jp.ne.simplex.application.model.*
import jp.ne.simplex.shared.BooleanExtension.Companion.toInt
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import org.slf4j.LoggerFactory

class ExclusivePropertyForFcNyuKoBatchPojo(
    val id: Long,
    val buildingCode: String,
    val roomCode: String,
    val buildingName: String,
    val roomNumber: String,
    val exclusiveFrom: Int,
    val exclusiveTo: Int,
    val companyType: Int,
    val earlyClosureFlag: String,
    val salesOfficeCode: String?,
    val creationDate: Int,
    val creator: String? = null,
    val updateDate: Int,
    val updater: String? = null,
    var eCode: String? = null,

    ) {
    companion object {
        private val log = LoggerFactory.getLogger(ExclusivePropertyForFcNyuKoBatchPojo::class.java)
    }

    fun toExclusivePropertyForFcNyuKoBatch(): ExclusivePropertyInfo? {
        return try {
            ExclusivePropertyInfo(
                id = ExclusiveProperty.Id.of(id),
                propertyId = Property.Id(Building.Code.of(buildingCode), Room.Code.of(roomCode)),
                buildingName = buildingName,
                roomNumber = Room.Number.of(roomNumber),
                exclusiveRange = DateRange.of(
                    from = exclusiveFrom.toString().yyyyMMdd(),
                    to = exclusiveTo.toString().yyyyMMdd(),
                    allowSameDate = true,
                ),
                exclusiveTarget = ExclusiveProperty.ExclusiveTarget(
                    ExclusiveProperty.CompanyType.fromValue(
                        companyType
                    )!!, Agent.ECode.of(eCode.toString())
                ),
                exclusiveTargetName = null,
                earlyClosureFlag = earlyClosureFlag.toInt() == true.toInt(),
                createDate = creationDate.toString().yyyyMMdd(),
                creator = creator.let { Employee.Name.of(it) },
                creatorAffiliationOfficeCode = salesOfficeCode?.let { Office.Code.of(it) },
                updateDate = updateDate.toString().yyyyMMdd(),
                updater = updater.let { Employee.Name.of(it) },
            )
        } catch (e: Exception) {
            log.warn("Invalid ExclusivePropertyTargetPojo. ExclusivePropertyTargetPojo=${this}", e)
            null
        }
    }

}
