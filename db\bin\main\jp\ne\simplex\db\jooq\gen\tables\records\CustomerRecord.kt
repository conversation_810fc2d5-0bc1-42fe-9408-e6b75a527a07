/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.CustomerTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.CustomerPojo

import org.jooq.Record3
import org.jooq.impl.UpdatableRecordImpl


/**
 * 顧客 既存システム物理名: AXCIFP
 */
@Suppress("UNCHECKED_CAST")
open class CustomerRecord private constructor() : UpdatableRecordImpl<CustomerRecord>(CustomerTable.CUSTOMER) {

    open var clientCategory: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var clientCode: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var clientBranchCode: Short
        set(value): Unit = set(2, value)
        get(): Short = get(2) as Short

    open var integratedClientCode: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var clientNameKanji: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var clientNameKana: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var searchClientNameKana: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var transferMatchingName_1: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var transferMatchingName_2: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var addressCode: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var addressDetail: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var buildingName: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var postalCode: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var clientPhoneNumber: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var bankCode: Short?
        set(value): Unit = set(14, value)
        get(): Short? = get(14) as Short?

    open var bankBranchCode: Short?
        set(value): Unit = set(15, value)
        get(): Short? = get(15) as Short?

    open var accountType: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var accountNumber: Long?
        set(value): Unit = set(17, value)
        get(): Long? = get(17) as Long?

    open var accountName: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var clientContractor: Byte?
        set(value): Unit = set(19, value)
        get(): Byte? = get(19) as Byte?

    open var clientLandlord: Byte?
        set(value): Unit = set(20, value)
        get(): Byte? = get(20) as Byte?

    open var clientPayee: Byte?
        set(value): Unit = set(21, value)
        get(): Byte? = get(21) as Byte?

    open var clientRegistrationDate: Int?
        set(value): Unit = set(22, value)
        get(): Int? = get(22) as Int?

    open var clientModificationDate: Int?
        set(value): Unit = set(23, value)
        get(): Int? = get(23) as Int?

    open var clientDeletionDate: Int?
        set(value): Unit = set(24, value)
        get(): Int? = get(24) as Int?

    open var creatorCode: Int?
        set(value): Unit = set(25, value)
        get(): Int? = get(25) as Int?

    open var clientParentChildCode: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var clientParentChildBranch: Short?
        set(value): Unit = set(27, value)
        get(): Short? = get(27) as Short?

    open var consolidationCode: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var consolidationBranch: Short?
        set(value): Unit = set(29, value)
        get(): Short? = get(29) as Short?

    open var industryCode: Short?
        set(value): Unit = set(30, value)
        get(): Short? = get(30) as Short?

    open var careerCode_1: Short?
        set(value): Unit = set(31, value)
        get(): Short? = get(31) as Short?

    open var careerCode_2: Short?
        set(value): Unit = set(32, value)
        get(): Short? = get(32) as Short?

    open var careerCode_3: Short?
        set(value): Unit = set(33, value)
        get(): Short? = get(33) as Short?

    open var clientApplicationCategory: Byte?
        set(value): Unit = set(34, value)
        get(): Byte? = get(34) as Byte?

    open var creationDate: Int?
        set(value): Unit = set(35, value)
        get(): Int? = get(35) as Int?

    open var creationTime: Int?
        set(value): Unit = set(36, value)
        get(): Int? = get(36) as Int?

    open var updateDate: Int?
        set(value): Unit = set(37, value)
        get(): Int? = get(37) as Int?

    open var updateTime: Int?
        set(value): Unit = set(38, value)
        get(): Int? = get(38) as Int?

    open var updateProgram: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var userId: String?
        set(value): Unit = set(40, value)
        get(): String? = get(40) as String?

    open var deleteFlag: String?
        set(value): Unit = set(41, value)
        get(): String? = get(41) as String?

    open var landlordTaxCategory: String?
        set(value): Unit = set(42, value)
        get(): String? = get(42) as String?

    open var dateOfBirth: Int?
        set(value): Unit = set(43, value)
        get(): Int? = get(43) as Int?

    open var survivalCategory: Byte?
        set(value): Unit = set(44, value)
        get(): Byte? = get(44) as Byte?

    open var deathYearMonth: Int?
        set(value): Unit = set(45, value)
        get(): Int? = get(45) as Int?

    open var corporationCategory: Byte?
        set(value): Unit = set(46, value)
        get(): Byte? = get(46) as Byte?

    open var occupation_1: Short?
        set(value): Unit = set(47, value)
        get(): Short? = get(47) as Short?

    open var occupation_2: Short?
        set(value): Unit = set(48, value)
        get(): Short? = get(48) as Short?

    open var occupation_3: Short?
        set(value): Unit = set(49, value)
        get(): Short? = get(49) as Short?

    open var occupation_4: Short?
        set(value): Unit = set(50, value)
        get(): Short? = get(50) as Short?

    open var remoteLandlordCategory: String?
        set(value): Unit = set(51, value)
        get(): String? = get(51) as String?

    open var creatorCodeSt: String?
        set(value): Unit = set(52, value)
        get(): String? = get(52) as String?

    open var dateOfDeath: Int?
        set(value): Unit = set(53, value)
        get(): Int? = get(53) as Int?

    open var clientOccupationCode: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var clientFaxNumber: String?
        set(value): Unit = set(55, value)
        get(): String? = get(55) as String?

    open var landlordTenantCategory: String?
        set(value): Unit = set(56, value)
        get(): String? = get(56) as String?

    open var clientGender: String?
        set(value): Unit = set(57, value)
        get(): String? = get(57) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record3<String?, String?, Short?> = super.key() as Record3<String?, String?, Short?>

    /**
     * Create a detached, initialised CustomerRecord
     */
    constructor(clientCategory: String, clientCode: String, clientBranchCode: Short, integratedClientCode: String? = null, clientNameKanji: String? = null, clientNameKana: String? = null, searchClientNameKana: String? = null, transferMatchingName_1: String? = null, transferMatchingName_2: String? = null, addressCode: String? = null, addressDetail: String? = null, buildingName: String? = null, postalCode: String? = null, clientPhoneNumber: String? = null, bankCode: Short? = null, bankBranchCode: Short? = null, accountType: String? = null, accountNumber: Long? = null, accountName: String? = null, clientContractor: Byte? = null, clientLandlord: Byte? = null, clientPayee: Byte? = null, clientRegistrationDate: Int? = null, clientModificationDate: Int? = null, clientDeletionDate: Int? = null, creatorCode: Int? = null, clientParentChildCode: String? = null, clientParentChildBranch: Short? = null, consolidationCode: String? = null, consolidationBranch: Short? = null, industryCode: Short? = null, careerCode_1: Short? = null, careerCode_2: Short? = null, careerCode_3: Short? = null, clientApplicationCategory: Byte? = null, creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgram: String? = null, userId: String? = null, deleteFlag: String? = null, landlordTaxCategory: String? = null, dateOfBirth: Int? = null, survivalCategory: Byte? = null, deathYearMonth: Int? = null, corporationCategory: Byte? = null, occupation_1: Short? = null, occupation_2: Short? = null, occupation_3: Short? = null, occupation_4: Short? = null, remoteLandlordCategory: String? = null, creatorCodeSt: String? = null, dateOfDeath: Int? = null, clientOccupationCode: String? = null, clientFaxNumber: String? = null, landlordTenantCategory: String? = null, clientGender: String? = null): this() {
        this.clientCategory = clientCategory
        this.clientCode = clientCode
        this.clientBranchCode = clientBranchCode
        this.integratedClientCode = integratedClientCode
        this.clientNameKanji = clientNameKanji
        this.clientNameKana = clientNameKana
        this.searchClientNameKana = searchClientNameKana
        this.transferMatchingName_1 = transferMatchingName_1
        this.transferMatchingName_2 = transferMatchingName_2
        this.addressCode = addressCode
        this.addressDetail = addressDetail
        this.buildingName = buildingName
        this.postalCode = postalCode
        this.clientPhoneNumber = clientPhoneNumber
        this.bankCode = bankCode
        this.bankBranchCode = bankBranchCode
        this.accountType = accountType
        this.accountNumber = accountNumber
        this.accountName = accountName
        this.clientContractor = clientContractor
        this.clientLandlord = clientLandlord
        this.clientPayee = clientPayee
        this.clientRegistrationDate = clientRegistrationDate
        this.clientModificationDate = clientModificationDate
        this.clientDeletionDate = clientDeletionDate
        this.creatorCode = creatorCode
        this.clientParentChildCode = clientParentChildCode
        this.clientParentChildBranch = clientParentChildBranch
        this.consolidationCode = consolidationCode
        this.consolidationBranch = consolidationBranch
        this.industryCode = industryCode
        this.careerCode_1 = careerCode_1
        this.careerCode_2 = careerCode_2
        this.careerCode_3 = careerCode_3
        this.clientApplicationCategory = clientApplicationCategory
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgram = updateProgram
        this.userId = userId
        this.deleteFlag = deleteFlag
        this.landlordTaxCategory = landlordTaxCategory
        this.dateOfBirth = dateOfBirth
        this.survivalCategory = survivalCategory
        this.deathYearMonth = deathYearMonth
        this.corporationCategory = corporationCategory
        this.occupation_1 = occupation_1
        this.occupation_2 = occupation_2
        this.occupation_3 = occupation_3
        this.occupation_4 = occupation_4
        this.remoteLandlordCategory = remoteLandlordCategory
        this.creatorCodeSt = creatorCodeSt
        this.dateOfDeath = dateOfDeath
        this.clientOccupationCode = clientOccupationCode
        this.clientFaxNumber = clientFaxNumber
        this.landlordTenantCategory = landlordTenantCategory
        this.clientGender = clientGender
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised CustomerRecord
     */
    constructor(value: CustomerPojo?): this() {
        if (value != null) {
            this.clientCategory = value.clientCategory
            this.clientCode = value.clientCode
            this.clientBranchCode = value.clientBranchCode
            this.integratedClientCode = value.integratedClientCode
            this.clientNameKanji = value.clientNameKanji
            this.clientNameKana = value.clientNameKana
            this.searchClientNameKana = value.searchClientNameKana
            this.transferMatchingName_1 = value.transferMatchingName_1
            this.transferMatchingName_2 = value.transferMatchingName_2
            this.addressCode = value.addressCode
            this.addressDetail = value.addressDetail
            this.buildingName = value.buildingName
            this.postalCode = value.postalCode
            this.clientPhoneNumber = value.clientPhoneNumber
            this.bankCode = value.bankCode
            this.bankBranchCode = value.bankBranchCode
            this.accountType = value.accountType
            this.accountNumber = value.accountNumber
            this.accountName = value.accountName
            this.clientContractor = value.clientContractor
            this.clientLandlord = value.clientLandlord
            this.clientPayee = value.clientPayee
            this.clientRegistrationDate = value.clientRegistrationDate
            this.clientModificationDate = value.clientModificationDate
            this.clientDeletionDate = value.clientDeletionDate
            this.creatorCode = value.creatorCode
            this.clientParentChildCode = value.clientParentChildCode
            this.clientParentChildBranch = value.clientParentChildBranch
            this.consolidationCode = value.consolidationCode
            this.consolidationBranch = value.consolidationBranch
            this.industryCode = value.industryCode
            this.careerCode_1 = value.careerCode_1
            this.careerCode_2 = value.careerCode_2
            this.careerCode_3 = value.careerCode_3
            this.clientApplicationCategory = value.clientApplicationCategory
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgram = value.updateProgram
            this.userId = value.userId
            this.deleteFlag = value.deleteFlag
            this.landlordTaxCategory = value.landlordTaxCategory
            this.dateOfBirth = value.dateOfBirth
            this.survivalCategory = value.survivalCategory
            this.deathYearMonth = value.deathYearMonth
            this.corporationCategory = value.corporationCategory
            this.occupation_1 = value.occupation_1
            this.occupation_2 = value.occupation_2
            this.occupation_3 = value.occupation_3
            this.occupation_4 = value.occupation_4
            this.remoteLandlordCategory = value.remoteLandlordCategory
            this.creatorCodeSt = value.creatorCodeSt
            this.dateOfDeath = value.dateOfDeath
            this.clientOccupationCode = value.clientOccupationCode
            this.clientFaxNumber = value.clientFaxNumber
            this.landlordTenantCategory = value.landlordTenantCategory
            this.clientGender = value.clientGender
            resetChangedOnNotNull()
        }
    }
}
