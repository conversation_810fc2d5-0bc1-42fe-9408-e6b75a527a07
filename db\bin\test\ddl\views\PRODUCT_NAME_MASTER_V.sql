-- VIEW: PRODUCT_NAME_MASTER_V(商品名称マスタマスタビュー)

CREATE VIEW PRODUCT_NAME_MASTER_V AS
SELECT
	PNM.PRODUCT_NAME_CODE,
	PNM.PRODUCT_NAME
FROM
	PRODUCT_NAME_MASTER PNM
WHERE
	EXISTS (
	SELECT
		1
	FROM
		(
		SELECT
			PRODUCT_NAME_CODE,
			MAX(EFFECTIVE_START_DATE) AS EFFECTIVE_START_DATE
		FROM
			PRODUCT_NAME_MASTER
	    WHERE
		    EFFECTIVE_START_DATE <= TO_CHAR(CURRENT_TIMESTAMP, 'yyyymmdd')::numeric
			AND (DELETE_FLAG = '' OR DELETE_FLAG IS NULL)
		GROUP BY
			PRODUCT_NAME_CODE) PNMM
	WHERE
		PNM.PRODUCT_NAME_CODE = PNMM.PRODUCT_NAME_CODE
		AND PNM.EFFECTIVE_START_DATE = PNMM.EFFECTIVE_START_DATE
);
