/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable
import java.math.BigDecimal


/**
 * 仮契約書 既存システム物理名: HCC35P
 */
@Suppress("UNCHECKED_CAST")
data class TemporaryContractPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var creationProgramId: String? = null,
    var creationTerminalId: String? = null,
    var creationResponsibleCd: String? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updateTerminalId: String? = null,
    var updateResponsibleCd: String? = null,
    var logicalDeleteFlag: String? = null,
    var buildingCd: String? = null,
    var effectiveStartDate: Int? = null,
    var effectiveEndDate: Int? = null,
    var contractType: String? = null,
    var dataManagementNo: Short? = null,
    var conclusionCategory: String? = null,
    var managementContractStartDate: Int? = null,
    var managementContractEndDate: Int? = null,
    var contractOutputManagementNo: Short? = null,
    var confirmProofOutputDate: Int? = null,
    var contractCollectionInputDate: Int? = null,
    var contractApprovalCategory: String? = null,
    var contractApprover: Int? = null,
    var contractApprovalDate: Int? = null,
    var contractOutputDate: Int? = null,
    var contractConclusionCategory: Byte? = null,
    var conclusionInputDate: Int? = null,
    var managementContractExpectedDate: Int? = null,
    var managementContractDate: Int? = null,
    var headOfficeApplicationCategory: String? = null,
    var headOfficeReceptionCategory: String? = null,
    var headOfficeReceptionDate: Int? = null,
    var nonStandardApprovalDate: Int? = null,
    var nonStandardApprover: String? = null,
    var nonStandardApplication: String? = null,
    var agreementTerminationDate: Int? = null,
    var agreementTerminationDate2: Int? = null,
    var agreementTerminationReason: String? = null,
    var nameChangeProgressNo: Int? = null,
    var nameChangeReasonCategory: String? = null,
    var notificationNo: Int? = null,
    var agreementRegistrationNo: String? = null,
    var oldAgreementNo: String? = null,
    var managementDelegatorCd: String? = null,
    var managementDelegatorName: String? = null,
    var delegatorPostalCode: String? = null,
    var delegatorAddressCd1: String? = null,
    var delegatorAddressCd2: String? = null,
    var delegatorAddressCd3: String? = null,
    var delegatorAddressDetail: String? = null,
    var delegatorBuildingName: String? = null,
    var delegatorPhoneNo: String? = null,
    var ownerTaxCategory: String? = null,
    var constructionCategory: String? = null,
    var buildingType: String? = null,
    var roomPurpose1Business: String? = null,
    var roomPurpose2Residential: String? = null,
    var roomPurpose3Parking: String? = null,
    var roomPurpose4Tr: String? = null,
    var roomPurpose5Other: String? = null,
    var loanCategory: String? = null,
    var totalUnitsBusiness: Short? = null,
    var totalUnitsResidential: Short? = null,
    var totalParkingUnits: Short? = null,
    var managedUnitsBusiness: Short? = null,
    var managedUnitsResidential: Short? = null,
    var managedParkingUnits: Short? = null,
    var contractForm: String? = null,
    var managementCategory: String? = null,
    var managementPartnership: String? = null,
    var partnershipType: String? = null,
    var managementPartner: String? = null,
    var maintenancePartner: String? = null,
    var detailsIssue: String? = null,
    var managementFormCategory: String? = null,
    var specialRent: String? = null,
    var comCommunityPartnership: String? = null,
    var proRataDays: Byte? = null,
    var excludedProRataDays: Byte? = null,
    var nonStandardFlag: String? = null,
    var newOwnerNameAtNameChange: String? = null,
    var ownerAddressAtNameChange: String? = null,
    var ownerTransferAccountOwnerCd: String? = null,
    var detailsConsolidationUnit: String? = null,
    var transferAccountDivision: String? = null,
    var recipientCd: String? = null,
    var previousRecipientCd: String? = null,
    var renewalFeeAcquisition: String? = null,
    var renewalFeeAcquisitionMonths: Byte? = null,
    var renewalFeeCommissionRate: Short? = null,
    var guarantorNotRequiredApprovalFlag: String? = null,
    var fixedTermRentalContractConclusion: String? = null,
    var waterFeeManagementFeeCollection: String? = null,
    var waterMeterCategory: String? = null,
    var gasCategory: String? = null,
    var sharedOwnershipCd1: String? = null,
    var delegatorSharedInterest1: BigDecimal? = null,
    var sharedOwnershipCd2: String? = null,
    var delegatorSharedInterest2: BigDecimal? = null,
    var sharedOwnershipCd3: String? = null,
    var delegatorSharedInterest3: BigDecimal? = null,
    var waterReadingCategory: String? = null,
    var waterFeeCollection: String? = null,
    var depositHandlingCategory: String? = null,
    var communityFeeManagementCollection: String? = null,
    var communityFeeManagementPayment: String? = null,
    var gasReadingCategory: String? = null,
    var petsAllowedCategory: String? = null,
    var rentTransferAccountCategory: String? = null,
    var monthlyRentTransferAccount: String? = null,
    var commonFeeTransferAccount: String? = null,
    var commonFeeTransferAccountCd: String? = null,
    var exclusiveBrokeragePeriod: String? = null,
    var mutualAidAssociationEnrollment: String? = null,
    var depositSettlementMethod: String? = null,
    var depositManagementDelegatorRate: Short? = null,
    var departureManagementServiceTermination: String? = null,
    var buildingInspectionServiceSitePatrol: String? = null,
    var vacantRoomManagementService: String? = null,
    var waterFeeReading: String? = null,
    var commonEquipmentMaintenance: String? = null,
    var otherMaintenance: String? = null,
    var maintenanceFeeCollectionTarget: String? = null,
    var maintenanceFeeUnit: String? = null,
    var maintenanceFeeTotal: Int? = null,
    var maintenanceFeeDaitoShare: Int? = null,
    var tenantSettlementService: String? = null,
    var tenantSettlementMngFeeDeduction: String? = null,
    var departureSettlementService: String? = null,
    var salesDepartAchieveAccountingCategory: String? = null,
    var moveInOutManagement: String? = null,
    var mngDepartmentLeaseContractInput: String? = null,
    var contractCategory: String? = null,
    var siteArea: BigDecimal? = null,
    var otherPurposes: String? = null,
    var otherPurposesContent: String? = null,
    var specialContractNo: String? = null,
    var additionalTask1: String? = null,
    var additionalTask2: String? = null,
    var managementBranchPhoneNo: String? = null,
    var managementContractTrInitial: Int? = null,
    var managementContractTrNext: Int? = null,
    var specialClausesIncluded: String? = null,
    var maintenanceItemsIncluded: String? = null,
    var managementDelegationDataIncluded: String? = null,
    var mngDelegationContentDataIncluded: String? = null,
    var agreementNoChangeManagementNo: Short? = null,
    var transferDate: Int? = null,
    var recordNewOldCategory: String? = null,
    var initialSetupFlag: String? = null,
    var contractEndDate: Int? = null,
    var operationStartDate: Int? = null,
    var maintenanceServiceCategory: String? = null,
    var simultaneousContractOutput: String? = null,
    var outputControlCategory: String? = null,
    var managementStartExpectedDate: Int? = null,
    var autoCreationCategory: String? = null,
    var maintenanceDelegationCreationCategory: String? = null,
    var previousConclusionDate: Int? = null,
    var mainteDelegationOnlyUpdateCategory: String? = null,
    var mainteDelegationContractOutputTarget: String? = null,
    var mainteConclusionExpectedDate: Int? = null,
    var mainteConclusionDate: Int? = null,
    var conclusionExpectedDateRequired: String? = null,
    var houseComStoreCd: String? = null,
    var mngStartDateChangeContractChange: String? = null,
    var businessNewGuaranteeCategory: String? = null,
    var repairSpecialClause: String? = null,
    var repairSpecialClausePeriod: Byte? = null,
    var inputManagementNo: Short? = null,
    var nonLeaseUseResidential: String? = null,
    var nonLeaseUseBusiness: String? = null,
    var nonLeaseUseParking: String? = null,
    var buildingStructure: String? = null,
    var buildingFloors: Byte? = null,
    var buildingAddressCd1: String? = null,
    var buildingAddressCd2: String? = null,
    var buildingAddressCd3: String? = null,
    var buildingAddressDetail: String? = null,
    var meterCount: Short? = null,
    var waterUsageIncluded: String? = null,
    var waterUsageMonthly: Int? = null,
    var maintenanceItemBIncluded: String? = null,
    var maintenanceItemBMonthly: Int? = null,
    var maintenanceItemAIncluded: String? = null,
    var maintenanceItemAMonthly: Int? = null,
    var rentalAdjustmentIncluded: String? = null,
    var rentalAdjustmentMonthly: Int? = null,
    var maintenanceFeeAdjustmentIncluded: String? = null,
    var maintenanceFeeAdjustmentMonthly: Int? = null,
    var repairFeeAdjustmentIncluded: String? = null,
    var repairFeeAdjustmentMonthly: Int? = null,
    var communityFeeAdjustmentIncluded: String? = null,
    var communityFeeAdjustmentMonthly: Int? = null,
    var catvAdjustmentIncluded: String? = null,
    var catvAdjustmentMonthly: Int? = null,
    var otherAdjustmentDescription: String? = null,
    var otherAdjustmentIncluded: String? = null,
    var otherAdjustmentMonthly: Int? = null,
    var businessNonLeaseAdjustmentIncluded: String? = null,
    var businessNonLeaseAdjustmentMonthly: Int? = null,
    var aboveDescription: String? = null,
    var aboveIncluded: String? = null,
    var aboveMonthly: Int? = null,
    var aboveAdjustmentCategory: String? = null,
    var subleaseRentalAssessmentTotal: Int? = null,
    var rentalAdjustmentAmount: Int? = null,
    var leaseRate: BigDecimal? = null,
    var leaseRental: Int? = null,
    var adjustmentAmount: Int? = null,
    var adjustmentCategory: String? = null,
    var leasePaymentRental: Int? = null,
    var consumptionTax: Int? = null,
    var contractBranchCd: String? = null,
    var parkingAddressCd1: String? = null,
    var parkingAddressCd2: String? = null,
    var parkingAddressCd3: String? = null,
    var parkingAddressDetail: String? = null,
    var leaseAssessmentParkingSpaces: Short? = null,
    var leaseNonAssessmentParkingSpaces: Short? = null,
    var leasedResidentialUnits: Short? = null,
    var leasedBusinessUnits: Short? = null,
    var otherItem1: String? = null,
    var otherAdjustment1: Int? = null,
    var otherItem2: String? = null,
    var otherAdjustment2: Int? = null,
    var otherItem3: String? = null,
    var otherAdjustment3: Int? = null,
    var otherItem4: String? = null,
    var otherAdjustment4: Int? = null,
    var otherItem5: String? = null,
    var otherAdjustment5: Int? = null,
    var bulkSwitchSign: String? = null,
    var switchPaymentMethod: Byte? = null,
    var daitoBulkRoomParkingDbUpdateDate: Int? = null,
    var switchType: String? = null,
    var expirationPaymentMethod: Byte? = null,
    var nonJoinedRoomsKyosai: Short? = null,
    var joinedRoomsKyosai: Short? = null,
    var managementOnlyContractOutput: Byte? = null,
    var maintenanceShortfall: Int? = null,
    var managementFeeRate: BigDecimal? = null,
    var reserve: Int? = null,
    var expirationType: String? = null,
    var nonLeaseUseTr: String? = null,
    var rentRevisionProcessingCategory: String? = null,
    var changeContractTargetCategory: String? = null,
    var confirmationOutputTargetCategory: String? = null,
    var receptionCategory: String? = null,
    var meterCount2: Short? = null,
    var waterUsageIncluded2: String? = null,
    var waterUsageMonthly2: Int? = null,
    var maintenanceItemBIncluded2: String? = null,
    var maintenanceItemBMonthly2: Int? = null,
    var maintenanceItemAIncluded2: String? = null,
    var maintenanceItemAMonthly2: Int? = null,
    var rentAdjustmentIncluded: String? = null,
    var rentAdjustmentMonthly: Int? = null,
    var maintenanceFeeAdjustmentIncluded2: String? = null,
    var maintenanceFeeAdjustmentMonthly2: Int? = null,
    var repairFeeAdjustmentIncluded2: String? = null,
    var repairFeeAdjustmentMonthly2: Int? = null,
    var communityFeeAdjustmentIncluded2: String? = null,
    var communityFeeAdjustmentMonthly2: Int? = null,
    var catvAdjustmentIncluded2: String? = null,
    var catvAdjustmentMonthly2: Int? = null,
    var otherAdjustmentDescription2: String? = null,
    var otherAdjustmentIncluded2: String? = null,
    var otherAdjustmentMonthly2: Int? = null,
    var businessNonLeaseAdjustmentIncluded2: String? = null,
    var businessNonLeaseAdjustmentMonthly2: Int? = null,
    var aboveDescription2: String? = null,
    var aboveIncluded2: String? = null,
    var aboveMonthly2: Int? = null,
    var aboveAdjustmentCategory2: String? = null,
    var subleaseRentAssessmentTotal: Int? = null,
    var rentAdjustmentAmount: Int? = null,
    var leaseRate2: BigDecimal? = null,
    var leaseRental2: Int? = null,
    var adjustmentAmount2: Int? = null,
    var adjustmentCategory2: String? = null,
    var leasePaymentRental2: Int? = null,
    var consumptionTax2: Int? = null,
    var maintenanceShortfall2: Int? = null,
    var managementFeeRate2: BigDecimal? = null,
    var preRevisionMinimumParkingFee: Int? = null,
    var preRevisionParkingCount: Short? = null,
    var postRevisionMinimumParkingFee: Int? = null,
    var postRevisionParkingCount: Short? = null,
    var subleaseRateUnder_10: BigDecimal? = null,
    var subleaseRateUnder_20: BigDecimal? = null,
    var subleaseRate_20AndAbove: BigDecimal? = null,
    var vacancyRateUnder_10: BigDecimal? = null,
    var vacancyRateUnder_20: BigDecimal? = null,
    var vacancyRate_20AndAbove: BigDecimal? = null,
    var maintenanceRequiredAmount: Int? = null,
    var leaseRoomMaintenanceFee: Int? = null,
    var managedRoomMaintenanceFee: Int? = null,
    var ownerDelayRoomMaintenanceFee: Int? = null,
    var maintenanceShortfall3: Int? = null,
    var generalApplicationNo1: String? = null,
    var generalApplicationNo2: Byte? = null,
    var generalApplicationNo3: Short? = null,
    var rentPrepaymentConfirmationCategory: String? = null,
    var checkSheetConfirmation: String? = null,
    var contentConfirmationDate: Int? = null,
    var maintenanceContentConfirmationDate: Int? = null,
    var consumptionTaxCalculationBaseDate: Int? = null,
    var consumptionTaxRate: BigDecimal? = null,
    var consumptionTaxRevisionFlag: Byte? = null,
    var parkingTotalSpaces: Short? = null,
    var leaseSpaces: Short? = null,
    var nonLeaseSpaces: Short? = null,
    var subleaseParkingFeeTotal: Int? = null,
    var parkingFeeAdjustmentAmount: Int? = null,
    var standardParkingFee: Int? = null,
    var leaseAssessmentParkingFee: Int? = null,
    var leaseRate3: BigDecimal? = null,
    var leaseParkingFee: Int? = null,
    var includedConsumptionTax: Int? = null,
    var rentIncreaseCategory: String? = null,
    var noteCode: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: TemporaryContractPojo = other as TemporaryContractPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.creationProgramId == null) {
            if (o.creationProgramId != null)
                return false
        }
        else if (this.creationProgramId != o.creationProgramId)
            return false
        if (this.creationTerminalId == null) {
            if (o.creationTerminalId != null)
                return false
        }
        else if (this.creationTerminalId != o.creationTerminalId)
            return false
        if (this.creationResponsibleCd == null) {
            if (o.creationResponsibleCd != null)
                return false
        }
        else if (this.creationResponsibleCd != o.creationResponsibleCd)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updateTerminalId == null) {
            if (o.updateTerminalId != null)
                return false
        }
        else if (this.updateTerminalId != o.updateTerminalId)
            return false
        if (this.updateResponsibleCd == null) {
            if (o.updateResponsibleCd != null)
                return false
        }
        else if (this.updateResponsibleCd != o.updateResponsibleCd)
            return false
        if (this.logicalDeleteFlag == null) {
            if (o.logicalDeleteFlag != null)
                return false
        }
        else if (this.logicalDeleteFlag != o.logicalDeleteFlag)
            return false
        if (this.buildingCd == null) {
            if (o.buildingCd != null)
                return false
        }
        else if (this.buildingCd != o.buildingCd)
            return false
        if (this.effectiveStartDate == null) {
            if (o.effectiveStartDate != null)
                return false
        }
        else if (this.effectiveStartDate != o.effectiveStartDate)
            return false
        if (this.effectiveEndDate == null) {
            if (o.effectiveEndDate != null)
                return false
        }
        else if (this.effectiveEndDate != o.effectiveEndDate)
            return false
        if (this.contractType == null) {
            if (o.contractType != null)
                return false
        }
        else if (this.contractType != o.contractType)
            return false
        if (this.dataManagementNo == null) {
            if (o.dataManagementNo != null)
                return false
        }
        else if (this.dataManagementNo != o.dataManagementNo)
            return false
        if (this.conclusionCategory == null) {
            if (o.conclusionCategory != null)
                return false
        }
        else if (this.conclusionCategory != o.conclusionCategory)
            return false
        if (this.managementContractStartDate == null) {
            if (o.managementContractStartDate != null)
                return false
        }
        else if (this.managementContractStartDate != o.managementContractStartDate)
            return false
        if (this.managementContractEndDate == null) {
            if (o.managementContractEndDate != null)
                return false
        }
        else if (this.managementContractEndDate != o.managementContractEndDate)
            return false
        if (this.contractOutputManagementNo == null) {
            if (o.contractOutputManagementNo != null)
                return false
        }
        else if (this.contractOutputManagementNo != o.contractOutputManagementNo)
            return false
        if (this.confirmProofOutputDate == null) {
            if (o.confirmProofOutputDate != null)
                return false
        }
        else if (this.confirmProofOutputDate != o.confirmProofOutputDate)
            return false
        if (this.contractCollectionInputDate == null) {
            if (o.contractCollectionInputDate != null)
                return false
        }
        else if (this.contractCollectionInputDate != o.contractCollectionInputDate)
            return false
        if (this.contractApprovalCategory == null) {
            if (o.contractApprovalCategory != null)
                return false
        }
        else if (this.contractApprovalCategory != o.contractApprovalCategory)
            return false
        if (this.contractApprover == null) {
            if (o.contractApprover != null)
                return false
        }
        else if (this.contractApprover != o.contractApprover)
            return false
        if (this.contractApprovalDate == null) {
            if (o.contractApprovalDate != null)
                return false
        }
        else if (this.contractApprovalDate != o.contractApprovalDate)
            return false
        if (this.contractOutputDate == null) {
            if (o.contractOutputDate != null)
                return false
        }
        else if (this.contractOutputDate != o.contractOutputDate)
            return false
        if (this.contractConclusionCategory == null) {
            if (o.contractConclusionCategory != null)
                return false
        }
        else if (this.contractConclusionCategory != o.contractConclusionCategory)
            return false
        if (this.conclusionInputDate == null) {
            if (o.conclusionInputDate != null)
                return false
        }
        else if (this.conclusionInputDate != o.conclusionInputDate)
            return false
        if (this.managementContractExpectedDate == null) {
            if (o.managementContractExpectedDate != null)
                return false
        }
        else if (this.managementContractExpectedDate != o.managementContractExpectedDate)
            return false
        if (this.managementContractDate == null) {
            if (o.managementContractDate != null)
                return false
        }
        else if (this.managementContractDate != o.managementContractDate)
            return false
        if (this.headOfficeApplicationCategory == null) {
            if (o.headOfficeApplicationCategory != null)
                return false
        }
        else if (this.headOfficeApplicationCategory != o.headOfficeApplicationCategory)
            return false
        if (this.headOfficeReceptionCategory == null) {
            if (o.headOfficeReceptionCategory != null)
                return false
        }
        else if (this.headOfficeReceptionCategory != o.headOfficeReceptionCategory)
            return false
        if (this.headOfficeReceptionDate == null) {
            if (o.headOfficeReceptionDate != null)
                return false
        }
        else if (this.headOfficeReceptionDate != o.headOfficeReceptionDate)
            return false
        if (this.nonStandardApprovalDate == null) {
            if (o.nonStandardApprovalDate != null)
                return false
        }
        else if (this.nonStandardApprovalDate != o.nonStandardApprovalDate)
            return false
        if (this.nonStandardApprover == null) {
            if (o.nonStandardApprover != null)
                return false
        }
        else if (this.nonStandardApprover != o.nonStandardApprover)
            return false
        if (this.nonStandardApplication == null) {
            if (o.nonStandardApplication != null)
                return false
        }
        else if (this.nonStandardApplication != o.nonStandardApplication)
            return false
        if (this.agreementTerminationDate == null) {
            if (o.agreementTerminationDate != null)
                return false
        }
        else if (this.agreementTerminationDate != o.agreementTerminationDate)
            return false
        if (this.agreementTerminationDate2 == null) {
            if (o.agreementTerminationDate2 != null)
                return false
        }
        else if (this.agreementTerminationDate2 != o.agreementTerminationDate2)
            return false
        if (this.agreementTerminationReason == null) {
            if (o.agreementTerminationReason != null)
                return false
        }
        else if (this.agreementTerminationReason != o.agreementTerminationReason)
            return false
        if (this.nameChangeProgressNo == null) {
            if (o.nameChangeProgressNo != null)
                return false
        }
        else if (this.nameChangeProgressNo != o.nameChangeProgressNo)
            return false
        if (this.nameChangeReasonCategory == null) {
            if (o.nameChangeReasonCategory != null)
                return false
        }
        else if (this.nameChangeReasonCategory != o.nameChangeReasonCategory)
            return false
        if (this.notificationNo == null) {
            if (o.notificationNo != null)
                return false
        }
        else if (this.notificationNo != o.notificationNo)
            return false
        if (this.agreementRegistrationNo == null) {
            if (o.agreementRegistrationNo != null)
                return false
        }
        else if (this.agreementRegistrationNo != o.agreementRegistrationNo)
            return false
        if (this.oldAgreementNo == null) {
            if (o.oldAgreementNo != null)
                return false
        }
        else if (this.oldAgreementNo != o.oldAgreementNo)
            return false
        if (this.managementDelegatorCd == null) {
            if (o.managementDelegatorCd != null)
                return false
        }
        else if (this.managementDelegatorCd != o.managementDelegatorCd)
            return false
        if (this.managementDelegatorName == null) {
            if (o.managementDelegatorName != null)
                return false
        }
        else if (this.managementDelegatorName != o.managementDelegatorName)
            return false
        if (this.delegatorPostalCode == null) {
            if (o.delegatorPostalCode != null)
                return false
        }
        else if (this.delegatorPostalCode != o.delegatorPostalCode)
            return false
        if (this.delegatorAddressCd1 == null) {
            if (o.delegatorAddressCd1 != null)
                return false
        }
        else if (this.delegatorAddressCd1 != o.delegatorAddressCd1)
            return false
        if (this.delegatorAddressCd2 == null) {
            if (o.delegatorAddressCd2 != null)
                return false
        }
        else if (this.delegatorAddressCd2 != o.delegatorAddressCd2)
            return false
        if (this.delegatorAddressCd3 == null) {
            if (o.delegatorAddressCd3 != null)
                return false
        }
        else if (this.delegatorAddressCd3 != o.delegatorAddressCd3)
            return false
        if (this.delegatorAddressDetail == null) {
            if (o.delegatorAddressDetail != null)
                return false
        }
        else if (this.delegatorAddressDetail != o.delegatorAddressDetail)
            return false
        if (this.delegatorBuildingName == null) {
            if (o.delegatorBuildingName != null)
                return false
        }
        else if (this.delegatorBuildingName != o.delegatorBuildingName)
            return false
        if (this.delegatorPhoneNo == null) {
            if (o.delegatorPhoneNo != null)
                return false
        }
        else if (this.delegatorPhoneNo != o.delegatorPhoneNo)
            return false
        if (this.ownerTaxCategory == null) {
            if (o.ownerTaxCategory != null)
                return false
        }
        else if (this.ownerTaxCategory != o.ownerTaxCategory)
            return false
        if (this.constructionCategory == null) {
            if (o.constructionCategory != null)
                return false
        }
        else if (this.constructionCategory != o.constructionCategory)
            return false
        if (this.buildingType == null) {
            if (o.buildingType != null)
                return false
        }
        else if (this.buildingType != o.buildingType)
            return false
        if (this.roomPurpose1Business == null) {
            if (o.roomPurpose1Business != null)
                return false
        }
        else if (this.roomPurpose1Business != o.roomPurpose1Business)
            return false
        if (this.roomPurpose2Residential == null) {
            if (o.roomPurpose2Residential != null)
                return false
        }
        else if (this.roomPurpose2Residential != o.roomPurpose2Residential)
            return false
        if (this.roomPurpose3Parking == null) {
            if (o.roomPurpose3Parking != null)
                return false
        }
        else if (this.roomPurpose3Parking != o.roomPurpose3Parking)
            return false
        if (this.roomPurpose4Tr == null) {
            if (o.roomPurpose4Tr != null)
                return false
        }
        else if (this.roomPurpose4Tr != o.roomPurpose4Tr)
            return false
        if (this.roomPurpose5Other == null) {
            if (o.roomPurpose5Other != null)
                return false
        }
        else if (this.roomPurpose5Other != o.roomPurpose5Other)
            return false
        if (this.loanCategory == null) {
            if (o.loanCategory != null)
                return false
        }
        else if (this.loanCategory != o.loanCategory)
            return false
        if (this.totalUnitsBusiness == null) {
            if (o.totalUnitsBusiness != null)
                return false
        }
        else if (this.totalUnitsBusiness != o.totalUnitsBusiness)
            return false
        if (this.totalUnitsResidential == null) {
            if (o.totalUnitsResidential != null)
                return false
        }
        else if (this.totalUnitsResidential != o.totalUnitsResidential)
            return false
        if (this.totalParkingUnits == null) {
            if (o.totalParkingUnits != null)
                return false
        }
        else if (this.totalParkingUnits != o.totalParkingUnits)
            return false
        if (this.managedUnitsBusiness == null) {
            if (o.managedUnitsBusiness != null)
                return false
        }
        else if (this.managedUnitsBusiness != o.managedUnitsBusiness)
            return false
        if (this.managedUnitsResidential == null) {
            if (o.managedUnitsResidential != null)
                return false
        }
        else if (this.managedUnitsResidential != o.managedUnitsResidential)
            return false
        if (this.managedParkingUnits == null) {
            if (o.managedParkingUnits != null)
                return false
        }
        else if (this.managedParkingUnits != o.managedParkingUnits)
            return false
        if (this.contractForm == null) {
            if (o.contractForm != null)
                return false
        }
        else if (this.contractForm != o.contractForm)
            return false
        if (this.managementCategory == null) {
            if (o.managementCategory != null)
                return false
        }
        else if (this.managementCategory != o.managementCategory)
            return false
        if (this.managementPartnership == null) {
            if (o.managementPartnership != null)
                return false
        }
        else if (this.managementPartnership != o.managementPartnership)
            return false
        if (this.partnershipType == null) {
            if (o.partnershipType != null)
                return false
        }
        else if (this.partnershipType != o.partnershipType)
            return false
        if (this.managementPartner == null) {
            if (o.managementPartner != null)
                return false
        }
        else if (this.managementPartner != o.managementPartner)
            return false
        if (this.maintenancePartner == null) {
            if (o.maintenancePartner != null)
                return false
        }
        else if (this.maintenancePartner != o.maintenancePartner)
            return false
        if (this.detailsIssue == null) {
            if (o.detailsIssue != null)
                return false
        }
        else if (this.detailsIssue != o.detailsIssue)
            return false
        if (this.managementFormCategory == null) {
            if (o.managementFormCategory != null)
                return false
        }
        else if (this.managementFormCategory != o.managementFormCategory)
            return false
        if (this.specialRent == null) {
            if (o.specialRent != null)
                return false
        }
        else if (this.specialRent != o.specialRent)
            return false
        if (this.comCommunityPartnership == null) {
            if (o.comCommunityPartnership != null)
                return false
        }
        else if (this.comCommunityPartnership != o.comCommunityPartnership)
            return false
        if (this.proRataDays == null) {
            if (o.proRataDays != null)
                return false
        }
        else if (this.proRataDays != o.proRataDays)
            return false
        if (this.excludedProRataDays == null) {
            if (o.excludedProRataDays != null)
                return false
        }
        else if (this.excludedProRataDays != o.excludedProRataDays)
            return false
        if (this.nonStandardFlag == null) {
            if (o.nonStandardFlag != null)
                return false
        }
        else if (this.nonStandardFlag != o.nonStandardFlag)
            return false
        if (this.newOwnerNameAtNameChange == null) {
            if (o.newOwnerNameAtNameChange != null)
                return false
        }
        else if (this.newOwnerNameAtNameChange != o.newOwnerNameAtNameChange)
            return false
        if (this.ownerAddressAtNameChange == null) {
            if (o.ownerAddressAtNameChange != null)
                return false
        }
        else if (this.ownerAddressAtNameChange != o.ownerAddressAtNameChange)
            return false
        if (this.ownerTransferAccountOwnerCd == null) {
            if (o.ownerTransferAccountOwnerCd != null)
                return false
        }
        else if (this.ownerTransferAccountOwnerCd != o.ownerTransferAccountOwnerCd)
            return false
        if (this.detailsConsolidationUnit == null) {
            if (o.detailsConsolidationUnit != null)
                return false
        }
        else if (this.detailsConsolidationUnit != o.detailsConsolidationUnit)
            return false
        if (this.transferAccountDivision == null) {
            if (o.transferAccountDivision != null)
                return false
        }
        else if (this.transferAccountDivision != o.transferAccountDivision)
            return false
        if (this.recipientCd == null) {
            if (o.recipientCd != null)
                return false
        }
        else if (this.recipientCd != o.recipientCd)
            return false
        if (this.previousRecipientCd == null) {
            if (o.previousRecipientCd != null)
                return false
        }
        else if (this.previousRecipientCd != o.previousRecipientCd)
            return false
        if (this.renewalFeeAcquisition == null) {
            if (o.renewalFeeAcquisition != null)
                return false
        }
        else if (this.renewalFeeAcquisition != o.renewalFeeAcquisition)
            return false
        if (this.renewalFeeAcquisitionMonths == null) {
            if (o.renewalFeeAcquisitionMonths != null)
                return false
        }
        else if (this.renewalFeeAcquisitionMonths != o.renewalFeeAcquisitionMonths)
            return false
        if (this.renewalFeeCommissionRate == null) {
            if (o.renewalFeeCommissionRate != null)
                return false
        }
        else if (this.renewalFeeCommissionRate != o.renewalFeeCommissionRate)
            return false
        if (this.guarantorNotRequiredApprovalFlag == null) {
            if (o.guarantorNotRequiredApprovalFlag != null)
                return false
        }
        else if (this.guarantorNotRequiredApprovalFlag != o.guarantorNotRequiredApprovalFlag)
            return false
        if (this.fixedTermRentalContractConclusion == null) {
            if (o.fixedTermRentalContractConclusion != null)
                return false
        }
        else if (this.fixedTermRentalContractConclusion != o.fixedTermRentalContractConclusion)
            return false
        if (this.waterFeeManagementFeeCollection == null) {
            if (o.waterFeeManagementFeeCollection != null)
                return false
        }
        else if (this.waterFeeManagementFeeCollection != o.waterFeeManagementFeeCollection)
            return false
        if (this.waterMeterCategory == null) {
            if (o.waterMeterCategory != null)
                return false
        }
        else if (this.waterMeterCategory != o.waterMeterCategory)
            return false
        if (this.gasCategory == null) {
            if (o.gasCategory != null)
                return false
        }
        else if (this.gasCategory != o.gasCategory)
            return false
        if (this.sharedOwnershipCd1 == null) {
            if (o.sharedOwnershipCd1 != null)
                return false
        }
        else if (this.sharedOwnershipCd1 != o.sharedOwnershipCd1)
            return false
        if (this.delegatorSharedInterest1 == null) {
            if (o.delegatorSharedInterest1 != null)
                return false
        }
        else if (this.delegatorSharedInterest1 != o.delegatorSharedInterest1)
            return false
        if (this.sharedOwnershipCd2 == null) {
            if (o.sharedOwnershipCd2 != null)
                return false
        }
        else if (this.sharedOwnershipCd2 != o.sharedOwnershipCd2)
            return false
        if (this.delegatorSharedInterest2 == null) {
            if (o.delegatorSharedInterest2 != null)
                return false
        }
        else if (this.delegatorSharedInterest2 != o.delegatorSharedInterest2)
            return false
        if (this.sharedOwnershipCd3 == null) {
            if (o.sharedOwnershipCd3 != null)
                return false
        }
        else if (this.sharedOwnershipCd3 != o.sharedOwnershipCd3)
            return false
        if (this.delegatorSharedInterest3 == null) {
            if (o.delegatorSharedInterest3 != null)
                return false
        }
        else if (this.delegatorSharedInterest3 != o.delegatorSharedInterest3)
            return false
        if (this.waterReadingCategory == null) {
            if (o.waterReadingCategory != null)
                return false
        }
        else if (this.waterReadingCategory != o.waterReadingCategory)
            return false
        if (this.waterFeeCollection == null) {
            if (o.waterFeeCollection != null)
                return false
        }
        else if (this.waterFeeCollection != o.waterFeeCollection)
            return false
        if (this.depositHandlingCategory == null) {
            if (o.depositHandlingCategory != null)
                return false
        }
        else if (this.depositHandlingCategory != o.depositHandlingCategory)
            return false
        if (this.communityFeeManagementCollection == null) {
            if (o.communityFeeManagementCollection != null)
                return false
        }
        else if (this.communityFeeManagementCollection != o.communityFeeManagementCollection)
            return false
        if (this.communityFeeManagementPayment == null) {
            if (o.communityFeeManagementPayment != null)
                return false
        }
        else if (this.communityFeeManagementPayment != o.communityFeeManagementPayment)
            return false
        if (this.gasReadingCategory == null) {
            if (o.gasReadingCategory != null)
                return false
        }
        else if (this.gasReadingCategory != o.gasReadingCategory)
            return false
        if (this.petsAllowedCategory == null) {
            if (o.petsAllowedCategory != null)
                return false
        }
        else if (this.petsAllowedCategory != o.petsAllowedCategory)
            return false
        if (this.rentTransferAccountCategory == null) {
            if (o.rentTransferAccountCategory != null)
                return false
        }
        else if (this.rentTransferAccountCategory != o.rentTransferAccountCategory)
            return false
        if (this.monthlyRentTransferAccount == null) {
            if (o.monthlyRentTransferAccount != null)
                return false
        }
        else if (this.monthlyRentTransferAccount != o.monthlyRentTransferAccount)
            return false
        if (this.commonFeeTransferAccount == null) {
            if (o.commonFeeTransferAccount != null)
                return false
        }
        else if (this.commonFeeTransferAccount != o.commonFeeTransferAccount)
            return false
        if (this.commonFeeTransferAccountCd == null) {
            if (o.commonFeeTransferAccountCd != null)
                return false
        }
        else if (this.commonFeeTransferAccountCd != o.commonFeeTransferAccountCd)
            return false
        if (this.exclusiveBrokeragePeriod == null) {
            if (o.exclusiveBrokeragePeriod != null)
                return false
        }
        else if (this.exclusiveBrokeragePeriod != o.exclusiveBrokeragePeriod)
            return false
        if (this.mutualAidAssociationEnrollment == null) {
            if (o.mutualAidAssociationEnrollment != null)
                return false
        }
        else if (this.mutualAidAssociationEnrollment != o.mutualAidAssociationEnrollment)
            return false
        if (this.depositSettlementMethod == null) {
            if (o.depositSettlementMethod != null)
                return false
        }
        else if (this.depositSettlementMethod != o.depositSettlementMethod)
            return false
        if (this.depositManagementDelegatorRate == null) {
            if (o.depositManagementDelegatorRate != null)
                return false
        }
        else if (this.depositManagementDelegatorRate != o.depositManagementDelegatorRate)
            return false
        if (this.departureManagementServiceTermination == null) {
            if (o.departureManagementServiceTermination != null)
                return false
        }
        else if (this.departureManagementServiceTermination != o.departureManagementServiceTermination)
            return false
        if (this.buildingInspectionServiceSitePatrol == null) {
            if (o.buildingInspectionServiceSitePatrol != null)
                return false
        }
        else if (this.buildingInspectionServiceSitePatrol != o.buildingInspectionServiceSitePatrol)
            return false
        if (this.vacantRoomManagementService == null) {
            if (o.vacantRoomManagementService != null)
                return false
        }
        else if (this.vacantRoomManagementService != o.vacantRoomManagementService)
            return false
        if (this.waterFeeReading == null) {
            if (o.waterFeeReading != null)
                return false
        }
        else if (this.waterFeeReading != o.waterFeeReading)
            return false
        if (this.commonEquipmentMaintenance == null) {
            if (o.commonEquipmentMaintenance != null)
                return false
        }
        else if (this.commonEquipmentMaintenance != o.commonEquipmentMaintenance)
            return false
        if (this.otherMaintenance == null) {
            if (o.otherMaintenance != null)
                return false
        }
        else if (this.otherMaintenance != o.otherMaintenance)
            return false
        if (this.maintenanceFeeCollectionTarget == null) {
            if (o.maintenanceFeeCollectionTarget != null)
                return false
        }
        else if (this.maintenanceFeeCollectionTarget != o.maintenanceFeeCollectionTarget)
            return false
        if (this.maintenanceFeeUnit == null) {
            if (o.maintenanceFeeUnit != null)
                return false
        }
        else if (this.maintenanceFeeUnit != o.maintenanceFeeUnit)
            return false
        if (this.maintenanceFeeTotal == null) {
            if (o.maintenanceFeeTotal != null)
                return false
        }
        else if (this.maintenanceFeeTotal != o.maintenanceFeeTotal)
            return false
        if (this.maintenanceFeeDaitoShare == null) {
            if (o.maintenanceFeeDaitoShare != null)
                return false
        }
        else if (this.maintenanceFeeDaitoShare != o.maintenanceFeeDaitoShare)
            return false
        if (this.tenantSettlementService == null) {
            if (o.tenantSettlementService != null)
                return false
        }
        else if (this.tenantSettlementService != o.tenantSettlementService)
            return false
        if (this.tenantSettlementMngFeeDeduction == null) {
            if (o.tenantSettlementMngFeeDeduction != null)
                return false
        }
        else if (this.tenantSettlementMngFeeDeduction != o.tenantSettlementMngFeeDeduction)
            return false
        if (this.departureSettlementService == null) {
            if (o.departureSettlementService != null)
                return false
        }
        else if (this.departureSettlementService != o.departureSettlementService)
            return false
        if (this.salesDepartAchieveAccountingCategory == null) {
            if (o.salesDepartAchieveAccountingCategory != null)
                return false
        }
        else if (this.salesDepartAchieveAccountingCategory != o.salesDepartAchieveAccountingCategory)
            return false
        if (this.moveInOutManagement == null) {
            if (o.moveInOutManagement != null)
                return false
        }
        else if (this.moveInOutManagement != o.moveInOutManagement)
            return false
        if (this.mngDepartmentLeaseContractInput == null) {
            if (o.mngDepartmentLeaseContractInput != null)
                return false
        }
        else if (this.mngDepartmentLeaseContractInput != o.mngDepartmentLeaseContractInput)
            return false
        if (this.contractCategory == null) {
            if (o.contractCategory != null)
                return false
        }
        else if (this.contractCategory != o.contractCategory)
            return false
        if (this.siteArea == null) {
            if (o.siteArea != null)
                return false
        }
        else if (this.siteArea != o.siteArea)
            return false
        if (this.otherPurposes == null) {
            if (o.otherPurposes != null)
                return false
        }
        else if (this.otherPurposes != o.otherPurposes)
            return false
        if (this.otherPurposesContent == null) {
            if (o.otherPurposesContent != null)
                return false
        }
        else if (this.otherPurposesContent != o.otherPurposesContent)
            return false
        if (this.specialContractNo == null) {
            if (o.specialContractNo != null)
                return false
        }
        else if (this.specialContractNo != o.specialContractNo)
            return false
        if (this.additionalTask1 == null) {
            if (o.additionalTask1 != null)
                return false
        }
        else if (this.additionalTask1 != o.additionalTask1)
            return false
        if (this.additionalTask2 == null) {
            if (o.additionalTask2 != null)
                return false
        }
        else if (this.additionalTask2 != o.additionalTask2)
            return false
        if (this.managementBranchPhoneNo == null) {
            if (o.managementBranchPhoneNo != null)
                return false
        }
        else if (this.managementBranchPhoneNo != o.managementBranchPhoneNo)
            return false
        if (this.managementContractTrInitial == null) {
            if (o.managementContractTrInitial != null)
                return false
        }
        else if (this.managementContractTrInitial != o.managementContractTrInitial)
            return false
        if (this.managementContractTrNext == null) {
            if (o.managementContractTrNext != null)
                return false
        }
        else if (this.managementContractTrNext != o.managementContractTrNext)
            return false
        if (this.specialClausesIncluded == null) {
            if (o.specialClausesIncluded != null)
                return false
        }
        else if (this.specialClausesIncluded != o.specialClausesIncluded)
            return false
        if (this.maintenanceItemsIncluded == null) {
            if (o.maintenanceItemsIncluded != null)
                return false
        }
        else if (this.maintenanceItemsIncluded != o.maintenanceItemsIncluded)
            return false
        if (this.managementDelegationDataIncluded == null) {
            if (o.managementDelegationDataIncluded != null)
                return false
        }
        else if (this.managementDelegationDataIncluded != o.managementDelegationDataIncluded)
            return false
        if (this.mngDelegationContentDataIncluded == null) {
            if (o.mngDelegationContentDataIncluded != null)
                return false
        }
        else if (this.mngDelegationContentDataIncluded != o.mngDelegationContentDataIncluded)
            return false
        if (this.agreementNoChangeManagementNo == null) {
            if (o.agreementNoChangeManagementNo != null)
                return false
        }
        else if (this.agreementNoChangeManagementNo != o.agreementNoChangeManagementNo)
            return false
        if (this.transferDate == null) {
            if (o.transferDate != null)
                return false
        }
        else if (this.transferDate != o.transferDate)
            return false
        if (this.recordNewOldCategory == null) {
            if (o.recordNewOldCategory != null)
                return false
        }
        else if (this.recordNewOldCategory != o.recordNewOldCategory)
            return false
        if (this.initialSetupFlag == null) {
            if (o.initialSetupFlag != null)
                return false
        }
        else if (this.initialSetupFlag != o.initialSetupFlag)
            return false
        if (this.contractEndDate == null) {
            if (o.contractEndDate != null)
                return false
        }
        else if (this.contractEndDate != o.contractEndDate)
            return false
        if (this.operationStartDate == null) {
            if (o.operationStartDate != null)
                return false
        }
        else if (this.operationStartDate != o.operationStartDate)
            return false
        if (this.maintenanceServiceCategory == null) {
            if (o.maintenanceServiceCategory != null)
                return false
        }
        else if (this.maintenanceServiceCategory != o.maintenanceServiceCategory)
            return false
        if (this.simultaneousContractOutput == null) {
            if (o.simultaneousContractOutput != null)
                return false
        }
        else if (this.simultaneousContractOutput != o.simultaneousContractOutput)
            return false
        if (this.outputControlCategory == null) {
            if (o.outputControlCategory != null)
                return false
        }
        else if (this.outputControlCategory != o.outputControlCategory)
            return false
        if (this.managementStartExpectedDate == null) {
            if (o.managementStartExpectedDate != null)
                return false
        }
        else if (this.managementStartExpectedDate != o.managementStartExpectedDate)
            return false
        if (this.autoCreationCategory == null) {
            if (o.autoCreationCategory != null)
                return false
        }
        else if (this.autoCreationCategory != o.autoCreationCategory)
            return false
        if (this.maintenanceDelegationCreationCategory == null) {
            if (o.maintenanceDelegationCreationCategory != null)
                return false
        }
        else if (this.maintenanceDelegationCreationCategory != o.maintenanceDelegationCreationCategory)
            return false
        if (this.previousConclusionDate == null) {
            if (o.previousConclusionDate != null)
                return false
        }
        else if (this.previousConclusionDate != o.previousConclusionDate)
            return false
        if (this.mainteDelegationOnlyUpdateCategory == null) {
            if (o.mainteDelegationOnlyUpdateCategory != null)
                return false
        }
        else if (this.mainteDelegationOnlyUpdateCategory != o.mainteDelegationOnlyUpdateCategory)
            return false
        if (this.mainteDelegationContractOutputTarget == null) {
            if (o.mainteDelegationContractOutputTarget != null)
                return false
        }
        else if (this.mainteDelegationContractOutputTarget != o.mainteDelegationContractOutputTarget)
            return false
        if (this.mainteConclusionExpectedDate == null) {
            if (o.mainteConclusionExpectedDate != null)
                return false
        }
        else if (this.mainteConclusionExpectedDate != o.mainteConclusionExpectedDate)
            return false
        if (this.mainteConclusionDate == null) {
            if (o.mainteConclusionDate != null)
                return false
        }
        else if (this.mainteConclusionDate != o.mainteConclusionDate)
            return false
        if (this.conclusionExpectedDateRequired == null) {
            if (o.conclusionExpectedDateRequired != null)
                return false
        }
        else if (this.conclusionExpectedDateRequired != o.conclusionExpectedDateRequired)
            return false
        if (this.houseComStoreCd == null) {
            if (o.houseComStoreCd != null)
                return false
        }
        else if (this.houseComStoreCd != o.houseComStoreCd)
            return false
        if (this.mngStartDateChangeContractChange == null) {
            if (o.mngStartDateChangeContractChange != null)
                return false
        }
        else if (this.mngStartDateChangeContractChange != o.mngStartDateChangeContractChange)
            return false
        if (this.businessNewGuaranteeCategory == null) {
            if (o.businessNewGuaranteeCategory != null)
                return false
        }
        else if (this.businessNewGuaranteeCategory != o.businessNewGuaranteeCategory)
            return false
        if (this.repairSpecialClause == null) {
            if (o.repairSpecialClause != null)
                return false
        }
        else if (this.repairSpecialClause != o.repairSpecialClause)
            return false
        if (this.repairSpecialClausePeriod == null) {
            if (o.repairSpecialClausePeriod != null)
                return false
        }
        else if (this.repairSpecialClausePeriod != o.repairSpecialClausePeriod)
            return false
        if (this.inputManagementNo == null) {
            if (o.inputManagementNo != null)
                return false
        }
        else if (this.inputManagementNo != o.inputManagementNo)
            return false
        if (this.nonLeaseUseResidential == null) {
            if (o.nonLeaseUseResidential != null)
                return false
        }
        else if (this.nonLeaseUseResidential != o.nonLeaseUseResidential)
            return false
        if (this.nonLeaseUseBusiness == null) {
            if (o.nonLeaseUseBusiness != null)
                return false
        }
        else if (this.nonLeaseUseBusiness != o.nonLeaseUseBusiness)
            return false
        if (this.nonLeaseUseParking == null) {
            if (o.nonLeaseUseParking != null)
                return false
        }
        else if (this.nonLeaseUseParking != o.nonLeaseUseParking)
            return false
        if (this.buildingStructure == null) {
            if (o.buildingStructure != null)
                return false
        }
        else if (this.buildingStructure != o.buildingStructure)
            return false
        if (this.buildingFloors == null) {
            if (o.buildingFloors != null)
                return false
        }
        else if (this.buildingFloors != o.buildingFloors)
            return false
        if (this.buildingAddressCd1 == null) {
            if (o.buildingAddressCd1 != null)
                return false
        }
        else if (this.buildingAddressCd1 != o.buildingAddressCd1)
            return false
        if (this.buildingAddressCd2 == null) {
            if (o.buildingAddressCd2 != null)
                return false
        }
        else if (this.buildingAddressCd2 != o.buildingAddressCd2)
            return false
        if (this.buildingAddressCd3 == null) {
            if (o.buildingAddressCd3 != null)
                return false
        }
        else if (this.buildingAddressCd3 != o.buildingAddressCd3)
            return false
        if (this.buildingAddressDetail == null) {
            if (o.buildingAddressDetail != null)
                return false
        }
        else if (this.buildingAddressDetail != o.buildingAddressDetail)
            return false
        if (this.meterCount == null) {
            if (o.meterCount != null)
                return false
        }
        else if (this.meterCount != o.meterCount)
            return false
        if (this.waterUsageIncluded == null) {
            if (o.waterUsageIncluded != null)
                return false
        }
        else if (this.waterUsageIncluded != o.waterUsageIncluded)
            return false
        if (this.waterUsageMonthly == null) {
            if (o.waterUsageMonthly != null)
                return false
        }
        else if (this.waterUsageMonthly != o.waterUsageMonthly)
            return false
        if (this.maintenanceItemBIncluded == null) {
            if (o.maintenanceItemBIncluded != null)
                return false
        }
        else if (this.maintenanceItemBIncluded != o.maintenanceItemBIncluded)
            return false
        if (this.maintenanceItemBMonthly == null) {
            if (o.maintenanceItemBMonthly != null)
                return false
        }
        else if (this.maintenanceItemBMonthly != o.maintenanceItemBMonthly)
            return false
        if (this.maintenanceItemAIncluded == null) {
            if (o.maintenanceItemAIncluded != null)
                return false
        }
        else if (this.maintenanceItemAIncluded != o.maintenanceItemAIncluded)
            return false
        if (this.maintenanceItemAMonthly == null) {
            if (o.maintenanceItemAMonthly != null)
                return false
        }
        else if (this.maintenanceItemAMonthly != o.maintenanceItemAMonthly)
            return false
        if (this.rentalAdjustmentIncluded == null) {
            if (o.rentalAdjustmentIncluded != null)
                return false
        }
        else if (this.rentalAdjustmentIncluded != o.rentalAdjustmentIncluded)
            return false
        if (this.rentalAdjustmentMonthly == null) {
            if (o.rentalAdjustmentMonthly != null)
                return false
        }
        else if (this.rentalAdjustmentMonthly != o.rentalAdjustmentMonthly)
            return false
        if (this.maintenanceFeeAdjustmentIncluded == null) {
            if (o.maintenanceFeeAdjustmentIncluded != null)
                return false
        }
        else if (this.maintenanceFeeAdjustmentIncluded != o.maintenanceFeeAdjustmentIncluded)
            return false
        if (this.maintenanceFeeAdjustmentMonthly == null) {
            if (o.maintenanceFeeAdjustmentMonthly != null)
                return false
        }
        else if (this.maintenanceFeeAdjustmentMonthly != o.maintenanceFeeAdjustmentMonthly)
            return false
        if (this.repairFeeAdjustmentIncluded == null) {
            if (o.repairFeeAdjustmentIncluded != null)
                return false
        }
        else if (this.repairFeeAdjustmentIncluded != o.repairFeeAdjustmentIncluded)
            return false
        if (this.repairFeeAdjustmentMonthly == null) {
            if (o.repairFeeAdjustmentMonthly != null)
                return false
        }
        else if (this.repairFeeAdjustmentMonthly != o.repairFeeAdjustmentMonthly)
            return false
        if (this.communityFeeAdjustmentIncluded == null) {
            if (o.communityFeeAdjustmentIncluded != null)
                return false
        }
        else if (this.communityFeeAdjustmentIncluded != o.communityFeeAdjustmentIncluded)
            return false
        if (this.communityFeeAdjustmentMonthly == null) {
            if (o.communityFeeAdjustmentMonthly != null)
                return false
        }
        else if (this.communityFeeAdjustmentMonthly != o.communityFeeAdjustmentMonthly)
            return false
        if (this.catvAdjustmentIncluded == null) {
            if (o.catvAdjustmentIncluded != null)
                return false
        }
        else if (this.catvAdjustmentIncluded != o.catvAdjustmentIncluded)
            return false
        if (this.catvAdjustmentMonthly == null) {
            if (o.catvAdjustmentMonthly != null)
                return false
        }
        else if (this.catvAdjustmentMonthly != o.catvAdjustmentMonthly)
            return false
        if (this.otherAdjustmentDescription == null) {
            if (o.otherAdjustmentDescription != null)
                return false
        }
        else if (this.otherAdjustmentDescription != o.otherAdjustmentDescription)
            return false
        if (this.otherAdjustmentIncluded == null) {
            if (o.otherAdjustmentIncluded != null)
                return false
        }
        else if (this.otherAdjustmentIncluded != o.otherAdjustmentIncluded)
            return false
        if (this.otherAdjustmentMonthly == null) {
            if (o.otherAdjustmentMonthly != null)
                return false
        }
        else if (this.otherAdjustmentMonthly != o.otherAdjustmentMonthly)
            return false
        if (this.businessNonLeaseAdjustmentIncluded == null) {
            if (o.businessNonLeaseAdjustmentIncluded != null)
                return false
        }
        else if (this.businessNonLeaseAdjustmentIncluded != o.businessNonLeaseAdjustmentIncluded)
            return false
        if (this.businessNonLeaseAdjustmentMonthly == null) {
            if (o.businessNonLeaseAdjustmentMonthly != null)
                return false
        }
        else if (this.businessNonLeaseAdjustmentMonthly != o.businessNonLeaseAdjustmentMonthly)
            return false
        if (this.aboveDescription == null) {
            if (o.aboveDescription != null)
                return false
        }
        else if (this.aboveDescription != o.aboveDescription)
            return false
        if (this.aboveIncluded == null) {
            if (o.aboveIncluded != null)
                return false
        }
        else if (this.aboveIncluded != o.aboveIncluded)
            return false
        if (this.aboveMonthly == null) {
            if (o.aboveMonthly != null)
                return false
        }
        else if (this.aboveMonthly != o.aboveMonthly)
            return false
        if (this.aboveAdjustmentCategory == null) {
            if (o.aboveAdjustmentCategory != null)
                return false
        }
        else if (this.aboveAdjustmentCategory != o.aboveAdjustmentCategory)
            return false
        if (this.subleaseRentalAssessmentTotal == null) {
            if (o.subleaseRentalAssessmentTotal != null)
                return false
        }
        else if (this.subleaseRentalAssessmentTotal != o.subleaseRentalAssessmentTotal)
            return false
        if (this.rentalAdjustmentAmount == null) {
            if (o.rentalAdjustmentAmount != null)
                return false
        }
        else if (this.rentalAdjustmentAmount != o.rentalAdjustmentAmount)
            return false
        if (this.leaseRate == null) {
            if (o.leaseRate != null)
                return false
        }
        else if (this.leaseRate != o.leaseRate)
            return false
        if (this.leaseRental == null) {
            if (o.leaseRental != null)
                return false
        }
        else if (this.leaseRental != o.leaseRental)
            return false
        if (this.adjustmentAmount == null) {
            if (o.adjustmentAmount != null)
                return false
        }
        else if (this.adjustmentAmount != o.adjustmentAmount)
            return false
        if (this.adjustmentCategory == null) {
            if (o.adjustmentCategory != null)
                return false
        }
        else if (this.adjustmentCategory != o.adjustmentCategory)
            return false
        if (this.leasePaymentRental == null) {
            if (o.leasePaymentRental != null)
                return false
        }
        else if (this.leasePaymentRental != o.leasePaymentRental)
            return false
        if (this.consumptionTax == null) {
            if (o.consumptionTax != null)
                return false
        }
        else if (this.consumptionTax != o.consumptionTax)
            return false
        if (this.contractBranchCd == null) {
            if (o.contractBranchCd != null)
                return false
        }
        else if (this.contractBranchCd != o.contractBranchCd)
            return false
        if (this.parkingAddressCd1 == null) {
            if (o.parkingAddressCd1 != null)
                return false
        }
        else if (this.parkingAddressCd1 != o.parkingAddressCd1)
            return false
        if (this.parkingAddressCd2 == null) {
            if (o.parkingAddressCd2 != null)
                return false
        }
        else if (this.parkingAddressCd2 != o.parkingAddressCd2)
            return false
        if (this.parkingAddressCd3 == null) {
            if (o.parkingAddressCd3 != null)
                return false
        }
        else if (this.parkingAddressCd3 != o.parkingAddressCd3)
            return false
        if (this.parkingAddressDetail == null) {
            if (o.parkingAddressDetail != null)
                return false
        }
        else if (this.parkingAddressDetail != o.parkingAddressDetail)
            return false
        if (this.leaseAssessmentParkingSpaces == null) {
            if (o.leaseAssessmentParkingSpaces != null)
                return false
        }
        else if (this.leaseAssessmentParkingSpaces != o.leaseAssessmentParkingSpaces)
            return false
        if (this.leaseNonAssessmentParkingSpaces == null) {
            if (o.leaseNonAssessmentParkingSpaces != null)
                return false
        }
        else if (this.leaseNonAssessmentParkingSpaces != o.leaseNonAssessmentParkingSpaces)
            return false
        if (this.leasedResidentialUnits == null) {
            if (o.leasedResidentialUnits != null)
                return false
        }
        else if (this.leasedResidentialUnits != o.leasedResidentialUnits)
            return false
        if (this.leasedBusinessUnits == null) {
            if (o.leasedBusinessUnits != null)
                return false
        }
        else if (this.leasedBusinessUnits != o.leasedBusinessUnits)
            return false
        if (this.otherItem1 == null) {
            if (o.otherItem1 != null)
                return false
        }
        else if (this.otherItem1 != o.otherItem1)
            return false
        if (this.otherAdjustment1 == null) {
            if (o.otherAdjustment1 != null)
                return false
        }
        else if (this.otherAdjustment1 != o.otherAdjustment1)
            return false
        if (this.otherItem2 == null) {
            if (o.otherItem2 != null)
                return false
        }
        else if (this.otherItem2 != o.otherItem2)
            return false
        if (this.otherAdjustment2 == null) {
            if (o.otherAdjustment2 != null)
                return false
        }
        else if (this.otherAdjustment2 != o.otherAdjustment2)
            return false
        if (this.otherItem3 == null) {
            if (o.otherItem3 != null)
                return false
        }
        else if (this.otherItem3 != o.otherItem3)
            return false
        if (this.otherAdjustment3 == null) {
            if (o.otherAdjustment3 != null)
                return false
        }
        else if (this.otherAdjustment3 != o.otherAdjustment3)
            return false
        if (this.otherItem4 == null) {
            if (o.otherItem4 != null)
                return false
        }
        else if (this.otherItem4 != o.otherItem4)
            return false
        if (this.otherAdjustment4 == null) {
            if (o.otherAdjustment4 != null)
                return false
        }
        else if (this.otherAdjustment4 != o.otherAdjustment4)
            return false
        if (this.otherItem5 == null) {
            if (o.otherItem5 != null)
                return false
        }
        else if (this.otherItem5 != o.otherItem5)
            return false
        if (this.otherAdjustment5 == null) {
            if (o.otherAdjustment5 != null)
                return false
        }
        else if (this.otherAdjustment5 != o.otherAdjustment5)
            return false
        if (this.bulkSwitchSign == null) {
            if (o.bulkSwitchSign != null)
                return false
        }
        else if (this.bulkSwitchSign != o.bulkSwitchSign)
            return false
        if (this.switchPaymentMethod == null) {
            if (o.switchPaymentMethod != null)
                return false
        }
        else if (this.switchPaymentMethod != o.switchPaymentMethod)
            return false
        if (this.daitoBulkRoomParkingDbUpdateDate == null) {
            if (o.daitoBulkRoomParkingDbUpdateDate != null)
                return false
        }
        else if (this.daitoBulkRoomParkingDbUpdateDate != o.daitoBulkRoomParkingDbUpdateDate)
            return false
        if (this.switchType == null) {
            if (o.switchType != null)
                return false
        }
        else if (this.switchType != o.switchType)
            return false
        if (this.expirationPaymentMethod == null) {
            if (o.expirationPaymentMethod != null)
                return false
        }
        else if (this.expirationPaymentMethod != o.expirationPaymentMethod)
            return false
        if (this.nonJoinedRoomsKyosai == null) {
            if (o.nonJoinedRoomsKyosai != null)
                return false
        }
        else if (this.nonJoinedRoomsKyosai != o.nonJoinedRoomsKyosai)
            return false
        if (this.joinedRoomsKyosai == null) {
            if (o.joinedRoomsKyosai != null)
                return false
        }
        else if (this.joinedRoomsKyosai != o.joinedRoomsKyosai)
            return false
        if (this.managementOnlyContractOutput == null) {
            if (o.managementOnlyContractOutput != null)
                return false
        }
        else if (this.managementOnlyContractOutput != o.managementOnlyContractOutput)
            return false
        if (this.maintenanceShortfall == null) {
            if (o.maintenanceShortfall != null)
                return false
        }
        else if (this.maintenanceShortfall != o.maintenanceShortfall)
            return false
        if (this.managementFeeRate == null) {
            if (o.managementFeeRate != null)
                return false
        }
        else if (this.managementFeeRate != o.managementFeeRate)
            return false
        if (this.reserve == null) {
            if (o.reserve != null)
                return false
        }
        else if (this.reserve != o.reserve)
            return false
        if (this.expirationType == null) {
            if (o.expirationType != null)
                return false
        }
        else if (this.expirationType != o.expirationType)
            return false
        if (this.nonLeaseUseTr == null) {
            if (o.nonLeaseUseTr != null)
                return false
        }
        else if (this.nonLeaseUseTr != o.nonLeaseUseTr)
            return false
        if (this.rentRevisionProcessingCategory == null) {
            if (o.rentRevisionProcessingCategory != null)
                return false
        }
        else if (this.rentRevisionProcessingCategory != o.rentRevisionProcessingCategory)
            return false
        if (this.changeContractTargetCategory == null) {
            if (o.changeContractTargetCategory != null)
                return false
        }
        else if (this.changeContractTargetCategory != o.changeContractTargetCategory)
            return false
        if (this.confirmationOutputTargetCategory == null) {
            if (o.confirmationOutputTargetCategory != null)
                return false
        }
        else if (this.confirmationOutputTargetCategory != o.confirmationOutputTargetCategory)
            return false
        if (this.receptionCategory == null) {
            if (o.receptionCategory != null)
                return false
        }
        else if (this.receptionCategory != o.receptionCategory)
            return false
        if (this.meterCount2 == null) {
            if (o.meterCount2 != null)
                return false
        }
        else if (this.meterCount2 != o.meterCount2)
            return false
        if (this.waterUsageIncluded2 == null) {
            if (o.waterUsageIncluded2 != null)
                return false
        }
        else if (this.waterUsageIncluded2 != o.waterUsageIncluded2)
            return false
        if (this.waterUsageMonthly2 == null) {
            if (o.waterUsageMonthly2 != null)
                return false
        }
        else if (this.waterUsageMonthly2 != o.waterUsageMonthly2)
            return false
        if (this.maintenanceItemBIncluded2 == null) {
            if (o.maintenanceItemBIncluded2 != null)
                return false
        }
        else if (this.maintenanceItemBIncluded2 != o.maintenanceItemBIncluded2)
            return false
        if (this.maintenanceItemBMonthly2 == null) {
            if (o.maintenanceItemBMonthly2 != null)
                return false
        }
        else if (this.maintenanceItemBMonthly2 != o.maintenanceItemBMonthly2)
            return false
        if (this.maintenanceItemAIncluded2 == null) {
            if (o.maintenanceItemAIncluded2 != null)
                return false
        }
        else if (this.maintenanceItemAIncluded2 != o.maintenanceItemAIncluded2)
            return false
        if (this.maintenanceItemAMonthly2 == null) {
            if (o.maintenanceItemAMonthly2 != null)
                return false
        }
        else if (this.maintenanceItemAMonthly2 != o.maintenanceItemAMonthly2)
            return false
        if (this.rentAdjustmentIncluded == null) {
            if (o.rentAdjustmentIncluded != null)
                return false
        }
        else if (this.rentAdjustmentIncluded != o.rentAdjustmentIncluded)
            return false
        if (this.rentAdjustmentMonthly == null) {
            if (o.rentAdjustmentMonthly != null)
                return false
        }
        else if (this.rentAdjustmentMonthly != o.rentAdjustmentMonthly)
            return false
        if (this.maintenanceFeeAdjustmentIncluded2 == null) {
            if (o.maintenanceFeeAdjustmentIncluded2 != null)
                return false
        }
        else if (this.maintenanceFeeAdjustmentIncluded2 != o.maintenanceFeeAdjustmentIncluded2)
            return false
        if (this.maintenanceFeeAdjustmentMonthly2 == null) {
            if (o.maintenanceFeeAdjustmentMonthly2 != null)
                return false
        }
        else if (this.maintenanceFeeAdjustmentMonthly2 != o.maintenanceFeeAdjustmentMonthly2)
            return false
        if (this.repairFeeAdjustmentIncluded2 == null) {
            if (o.repairFeeAdjustmentIncluded2 != null)
                return false
        }
        else if (this.repairFeeAdjustmentIncluded2 != o.repairFeeAdjustmentIncluded2)
            return false
        if (this.repairFeeAdjustmentMonthly2 == null) {
            if (o.repairFeeAdjustmentMonthly2 != null)
                return false
        }
        else if (this.repairFeeAdjustmentMonthly2 != o.repairFeeAdjustmentMonthly2)
            return false
        if (this.communityFeeAdjustmentIncluded2 == null) {
            if (o.communityFeeAdjustmentIncluded2 != null)
                return false
        }
        else if (this.communityFeeAdjustmentIncluded2 != o.communityFeeAdjustmentIncluded2)
            return false
        if (this.communityFeeAdjustmentMonthly2 == null) {
            if (o.communityFeeAdjustmentMonthly2 != null)
                return false
        }
        else if (this.communityFeeAdjustmentMonthly2 != o.communityFeeAdjustmentMonthly2)
            return false
        if (this.catvAdjustmentIncluded2 == null) {
            if (o.catvAdjustmentIncluded2 != null)
                return false
        }
        else if (this.catvAdjustmentIncluded2 != o.catvAdjustmentIncluded2)
            return false
        if (this.catvAdjustmentMonthly2 == null) {
            if (o.catvAdjustmentMonthly2 != null)
                return false
        }
        else if (this.catvAdjustmentMonthly2 != o.catvAdjustmentMonthly2)
            return false
        if (this.otherAdjustmentDescription2 == null) {
            if (o.otherAdjustmentDescription2 != null)
                return false
        }
        else if (this.otherAdjustmentDescription2 != o.otherAdjustmentDescription2)
            return false
        if (this.otherAdjustmentIncluded2 == null) {
            if (o.otherAdjustmentIncluded2 != null)
                return false
        }
        else if (this.otherAdjustmentIncluded2 != o.otherAdjustmentIncluded2)
            return false
        if (this.otherAdjustmentMonthly2 == null) {
            if (o.otherAdjustmentMonthly2 != null)
                return false
        }
        else if (this.otherAdjustmentMonthly2 != o.otherAdjustmentMonthly2)
            return false
        if (this.businessNonLeaseAdjustmentIncluded2 == null) {
            if (o.businessNonLeaseAdjustmentIncluded2 != null)
                return false
        }
        else if (this.businessNonLeaseAdjustmentIncluded2 != o.businessNonLeaseAdjustmentIncluded2)
            return false
        if (this.businessNonLeaseAdjustmentMonthly2 == null) {
            if (o.businessNonLeaseAdjustmentMonthly2 != null)
                return false
        }
        else if (this.businessNonLeaseAdjustmentMonthly2 != o.businessNonLeaseAdjustmentMonthly2)
            return false
        if (this.aboveDescription2 == null) {
            if (o.aboveDescription2 != null)
                return false
        }
        else if (this.aboveDescription2 != o.aboveDescription2)
            return false
        if (this.aboveIncluded2 == null) {
            if (o.aboveIncluded2 != null)
                return false
        }
        else if (this.aboveIncluded2 != o.aboveIncluded2)
            return false
        if (this.aboveMonthly2 == null) {
            if (o.aboveMonthly2 != null)
                return false
        }
        else if (this.aboveMonthly2 != o.aboveMonthly2)
            return false
        if (this.aboveAdjustmentCategory2 == null) {
            if (o.aboveAdjustmentCategory2 != null)
                return false
        }
        else if (this.aboveAdjustmentCategory2 != o.aboveAdjustmentCategory2)
            return false
        if (this.subleaseRentAssessmentTotal == null) {
            if (o.subleaseRentAssessmentTotal != null)
                return false
        }
        else if (this.subleaseRentAssessmentTotal != o.subleaseRentAssessmentTotal)
            return false
        if (this.rentAdjustmentAmount == null) {
            if (o.rentAdjustmentAmount != null)
                return false
        }
        else if (this.rentAdjustmentAmount != o.rentAdjustmentAmount)
            return false
        if (this.leaseRate2 == null) {
            if (o.leaseRate2 != null)
                return false
        }
        else if (this.leaseRate2 != o.leaseRate2)
            return false
        if (this.leaseRental2 == null) {
            if (o.leaseRental2 != null)
                return false
        }
        else if (this.leaseRental2 != o.leaseRental2)
            return false
        if (this.adjustmentAmount2 == null) {
            if (o.adjustmentAmount2 != null)
                return false
        }
        else if (this.adjustmentAmount2 != o.adjustmentAmount2)
            return false
        if (this.adjustmentCategory2 == null) {
            if (o.adjustmentCategory2 != null)
                return false
        }
        else if (this.adjustmentCategory2 != o.adjustmentCategory2)
            return false
        if (this.leasePaymentRental2 == null) {
            if (o.leasePaymentRental2 != null)
                return false
        }
        else if (this.leasePaymentRental2 != o.leasePaymentRental2)
            return false
        if (this.consumptionTax2 == null) {
            if (o.consumptionTax2 != null)
                return false
        }
        else if (this.consumptionTax2 != o.consumptionTax2)
            return false
        if (this.maintenanceShortfall2 == null) {
            if (o.maintenanceShortfall2 != null)
                return false
        }
        else if (this.maintenanceShortfall2 != o.maintenanceShortfall2)
            return false
        if (this.managementFeeRate2 == null) {
            if (o.managementFeeRate2 != null)
                return false
        }
        else if (this.managementFeeRate2 != o.managementFeeRate2)
            return false
        if (this.preRevisionMinimumParkingFee == null) {
            if (o.preRevisionMinimumParkingFee != null)
                return false
        }
        else if (this.preRevisionMinimumParkingFee != o.preRevisionMinimumParkingFee)
            return false
        if (this.preRevisionParkingCount == null) {
            if (o.preRevisionParkingCount != null)
                return false
        }
        else if (this.preRevisionParkingCount != o.preRevisionParkingCount)
            return false
        if (this.postRevisionMinimumParkingFee == null) {
            if (o.postRevisionMinimumParkingFee != null)
                return false
        }
        else if (this.postRevisionMinimumParkingFee != o.postRevisionMinimumParkingFee)
            return false
        if (this.postRevisionParkingCount == null) {
            if (o.postRevisionParkingCount != null)
                return false
        }
        else if (this.postRevisionParkingCount != o.postRevisionParkingCount)
            return false
        if (this.subleaseRateUnder_10 == null) {
            if (o.subleaseRateUnder_10 != null)
                return false
        }
        else if (this.subleaseRateUnder_10 != o.subleaseRateUnder_10)
            return false
        if (this.subleaseRateUnder_20 == null) {
            if (o.subleaseRateUnder_20 != null)
                return false
        }
        else if (this.subleaseRateUnder_20 != o.subleaseRateUnder_20)
            return false
        if (this.subleaseRate_20AndAbove == null) {
            if (o.subleaseRate_20AndAbove != null)
                return false
        }
        else if (this.subleaseRate_20AndAbove != o.subleaseRate_20AndAbove)
            return false
        if (this.vacancyRateUnder_10 == null) {
            if (o.vacancyRateUnder_10 != null)
                return false
        }
        else if (this.vacancyRateUnder_10 != o.vacancyRateUnder_10)
            return false
        if (this.vacancyRateUnder_20 == null) {
            if (o.vacancyRateUnder_20 != null)
                return false
        }
        else if (this.vacancyRateUnder_20 != o.vacancyRateUnder_20)
            return false
        if (this.vacancyRate_20AndAbove == null) {
            if (o.vacancyRate_20AndAbove != null)
                return false
        }
        else if (this.vacancyRate_20AndAbove != o.vacancyRate_20AndAbove)
            return false
        if (this.maintenanceRequiredAmount == null) {
            if (o.maintenanceRequiredAmount != null)
                return false
        }
        else if (this.maintenanceRequiredAmount != o.maintenanceRequiredAmount)
            return false
        if (this.leaseRoomMaintenanceFee == null) {
            if (o.leaseRoomMaintenanceFee != null)
                return false
        }
        else if (this.leaseRoomMaintenanceFee != o.leaseRoomMaintenanceFee)
            return false
        if (this.managedRoomMaintenanceFee == null) {
            if (o.managedRoomMaintenanceFee != null)
                return false
        }
        else if (this.managedRoomMaintenanceFee != o.managedRoomMaintenanceFee)
            return false
        if (this.ownerDelayRoomMaintenanceFee == null) {
            if (o.ownerDelayRoomMaintenanceFee != null)
                return false
        }
        else if (this.ownerDelayRoomMaintenanceFee != o.ownerDelayRoomMaintenanceFee)
            return false
        if (this.maintenanceShortfall3 == null) {
            if (o.maintenanceShortfall3 != null)
                return false
        }
        else if (this.maintenanceShortfall3 != o.maintenanceShortfall3)
            return false
        if (this.generalApplicationNo1 == null) {
            if (o.generalApplicationNo1 != null)
                return false
        }
        else if (this.generalApplicationNo1 != o.generalApplicationNo1)
            return false
        if (this.generalApplicationNo2 == null) {
            if (o.generalApplicationNo2 != null)
                return false
        }
        else if (this.generalApplicationNo2 != o.generalApplicationNo2)
            return false
        if (this.generalApplicationNo3 == null) {
            if (o.generalApplicationNo3 != null)
                return false
        }
        else if (this.generalApplicationNo3 != o.generalApplicationNo3)
            return false
        if (this.rentPrepaymentConfirmationCategory == null) {
            if (o.rentPrepaymentConfirmationCategory != null)
                return false
        }
        else if (this.rentPrepaymentConfirmationCategory != o.rentPrepaymentConfirmationCategory)
            return false
        if (this.checkSheetConfirmation == null) {
            if (o.checkSheetConfirmation != null)
                return false
        }
        else if (this.checkSheetConfirmation != o.checkSheetConfirmation)
            return false
        if (this.contentConfirmationDate == null) {
            if (o.contentConfirmationDate != null)
                return false
        }
        else if (this.contentConfirmationDate != o.contentConfirmationDate)
            return false
        if (this.maintenanceContentConfirmationDate == null) {
            if (o.maintenanceContentConfirmationDate != null)
                return false
        }
        else if (this.maintenanceContentConfirmationDate != o.maintenanceContentConfirmationDate)
            return false
        if (this.consumptionTaxCalculationBaseDate == null) {
            if (o.consumptionTaxCalculationBaseDate != null)
                return false
        }
        else if (this.consumptionTaxCalculationBaseDate != o.consumptionTaxCalculationBaseDate)
            return false
        if (this.consumptionTaxRate == null) {
            if (o.consumptionTaxRate != null)
                return false
        }
        else if (this.consumptionTaxRate != o.consumptionTaxRate)
            return false
        if (this.consumptionTaxRevisionFlag == null) {
            if (o.consumptionTaxRevisionFlag != null)
                return false
        }
        else if (this.consumptionTaxRevisionFlag != o.consumptionTaxRevisionFlag)
            return false
        if (this.parkingTotalSpaces == null) {
            if (o.parkingTotalSpaces != null)
                return false
        }
        else if (this.parkingTotalSpaces != o.parkingTotalSpaces)
            return false
        if (this.leaseSpaces == null) {
            if (o.leaseSpaces != null)
                return false
        }
        else if (this.leaseSpaces != o.leaseSpaces)
            return false
        if (this.nonLeaseSpaces == null) {
            if (o.nonLeaseSpaces != null)
                return false
        }
        else if (this.nonLeaseSpaces != o.nonLeaseSpaces)
            return false
        if (this.subleaseParkingFeeTotal == null) {
            if (o.subleaseParkingFeeTotal != null)
                return false
        }
        else if (this.subleaseParkingFeeTotal != o.subleaseParkingFeeTotal)
            return false
        if (this.parkingFeeAdjustmentAmount == null) {
            if (o.parkingFeeAdjustmentAmount != null)
                return false
        }
        else if (this.parkingFeeAdjustmentAmount != o.parkingFeeAdjustmentAmount)
            return false
        if (this.standardParkingFee == null) {
            if (o.standardParkingFee != null)
                return false
        }
        else if (this.standardParkingFee != o.standardParkingFee)
            return false
        if (this.leaseAssessmentParkingFee == null) {
            if (o.leaseAssessmentParkingFee != null)
                return false
        }
        else if (this.leaseAssessmentParkingFee != o.leaseAssessmentParkingFee)
            return false
        if (this.leaseRate3 == null) {
            if (o.leaseRate3 != null)
                return false
        }
        else if (this.leaseRate3 != o.leaseRate3)
            return false
        if (this.leaseParkingFee == null) {
            if (o.leaseParkingFee != null)
                return false
        }
        else if (this.leaseParkingFee != o.leaseParkingFee)
            return false
        if (this.includedConsumptionTax == null) {
            if (o.includedConsumptionTax != null)
                return false
        }
        else if (this.includedConsumptionTax != o.includedConsumptionTax)
            return false
        if (this.rentIncreaseCategory == null) {
            if (o.rentIncreaseCategory != null)
                return false
        }
        else if (this.rentIncreaseCategory != o.rentIncreaseCategory)
            return false
        if (this.noteCode == null) {
            if (o.noteCode != null)
                return false
        }
        else if (this.noteCode != o.noteCode)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.creationProgramId == null) 0 else this.creationProgramId.hashCode())
        result = prime * result + (if (this.creationTerminalId == null) 0 else this.creationTerminalId.hashCode())
        result = prime * result + (if (this.creationResponsibleCd == null) 0 else this.creationResponsibleCd.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updateTerminalId == null) 0 else this.updateTerminalId.hashCode())
        result = prime * result + (if (this.updateResponsibleCd == null) 0 else this.updateResponsibleCd.hashCode())
        result = prime * result + (if (this.logicalDeleteFlag == null) 0 else this.logicalDeleteFlag.hashCode())
        result = prime * result + (if (this.buildingCd == null) 0 else this.buildingCd.hashCode())
        result = prime * result + (if (this.effectiveStartDate == null) 0 else this.effectiveStartDate.hashCode())
        result = prime * result + (if (this.effectiveEndDate == null) 0 else this.effectiveEndDate.hashCode())
        result = prime * result + (if (this.contractType == null) 0 else this.contractType.hashCode())
        result = prime * result + (if (this.dataManagementNo == null) 0 else this.dataManagementNo.hashCode())
        result = prime * result + (if (this.conclusionCategory == null) 0 else this.conclusionCategory.hashCode())
        result = prime * result + (if (this.managementContractStartDate == null) 0 else this.managementContractStartDate.hashCode())
        result = prime * result + (if (this.managementContractEndDate == null) 0 else this.managementContractEndDate.hashCode())
        result = prime * result + (if (this.contractOutputManagementNo == null) 0 else this.contractOutputManagementNo.hashCode())
        result = prime * result + (if (this.confirmProofOutputDate == null) 0 else this.confirmProofOutputDate.hashCode())
        result = prime * result + (if (this.contractCollectionInputDate == null) 0 else this.contractCollectionInputDate.hashCode())
        result = prime * result + (if (this.contractApprovalCategory == null) 0 else this.contractApprovalCategory.hashCode())
        result = prime * result + (if (this.contractApprover == null) 0 else this.contractApprover.hashCode())
        result = prime * result + (if (this.contractApprovalDate == null) 0 else this.contractApprovalDate.hashCode())
        result = prime * result + (if (this.contractOutputDate == null) 0 else this.contractOutputDate.hashCode())
        result = prime * result + (if (this.contractConclusionCategory == null) 0 else this.contractConclusionCategory.hashCode())
        result = prime * result + (if (this.conclusionInputDate == null) 0 else this.conclusionInputDate.hashCode())
        result = prime * result + (if (this.managementContractExpectedDate == null) 0 else this.managementContractExpectedDate.hashCode())
        result = prime * result + (if (this.managementContractDate == null) 0 else this.managementContractDate.hashCode())
        result = prime * result + (if (this.headOfficeApplicationCategory == null) 0 else this.headOfficeApplicationCategory.hashCode())
        result = prime * result + (if (this.headOfficeReceptionCategory == null) 0 else this.headOfficeReceptionCategory.hashCode())
        result = prime * result + (if (this.headOfficeReceptionDate == null) 0 else this.headOfficeReceptionDate.hashCode())
        result = prime * result + (if (this.nonStandardApprovalDate == null) 0 else this.nonStandardApprovalDate.hashCode())
        result = prime * result + (if (this.nonStandardApprover == null) 0 else this.nonStandardApprover.hashCode())
        result = prime * result + (if (this.nonStandardApplication == null) 0 else this.nonStandardApplication.hashCode())
        result = prime * result + (if (this.agreementTerminationDate == null) 0 else this.agreementTerminationDate.hashCode())
        result = prime * result + (if (this.agreementTerminationDate2 == null) 0 else this.agreementTerminationDate2.hashCode())
        result = prime * result + (if (this.agreementTerminationReason == null) 0 else this.agreementTerminationReason.hashCode())
        result = prime * result + (if (this.nameChangeProgressNo == null) 0 else this.nameChangeProgressNo.hashCode())
        result = prime * result + (if (this.nameChangeReasonCategory == null) 0 else this.nameChangeReasonCategory.hashCode())
        result = prime * result + (if (this.notificationNo == null) 0 else this.notificationNo.hashCode())
        result = prime * result + (if (this.agreementRegistrationNo == null) 0 else this.agreementRegistrationNo.hashCode())
        result = prime * result + (if (this.oldAgreementNo == null) 0 else this.oldAgreementNo.hashCode())
        result = prime * result + (if (this.managementDelegatorCd == null) 0 else this.managementDelegatorCd.hashCode())
        result = prime * result + (if (this.managementDelegatorName == null) 0 else this.managementDelegatorName.hashCode())
        result = prime * result + (if (this.delegatorPostalCode == null) 0 else this.delegatorPostalCode.hashCode())
        result = prime * result + (if (this.delegatorAddressCd1 == null) 0 else this.delegatorAddressCd1.hashCode())
        result = prime * result + (if (this.delegatorAddressCd2 == null) 0 else this.delegatorAddressCd2.hashCode())
        result = prime * result + (if (this.delegatorAddressCd3 == null) 0 else this.delegatorAddressCd3.hashCode())
        result = prime * result + (if (this.delegatorAddressDetail == null) 0 else this.delegatorAddressDetail.hashCode())
        result = prime * result + (if (this.delegatorBuildingName == null) 0 else this.delegatorBuildingName.hashCode())
        result = prime * result + (if (this.delegatorPhoneNo == null) 0 else this.delegatorPhoneNo.hashCode())
        result = prime * result + (if (this.ownerTaxCategory == null) 0 else this.ownerTaxCategory.hashCode())
        result = prime * result + (if (this.constructionCategory == null) 0 else this.constructionCategory.hashCode())
        result = prime * result + (if (this.buildingType == null) 0 else this.buildingType.hashCode())
        result = prime * result + (if (this.roomPurpose1Business == null) 0 else this.roomPurpose1Business.hashCode())
        result = prime * result + (if (this.roomPurpose2Residential == null) 0 else this.roomPurpose2Residential.hashCode())
        result = prime * result + (if (this.roomPurpose3Parking == null) 0 else this.roomPurpose3Parking.hashCode())
        result = prime * result + (if (this.roomPurpose4Tr == null) 0 else this.roomPurpose4Tr.hashCode())
        result = prime * result + (if (this.roomPurpose5Other == null) 0 else this.roomPurpose5Other.hashCode())
        result = prime * result + (if (this.loanCategory == null) 0 else this.loanCategory.hashCode())
        result = prime * result + (if (this.totalUnitsBusiness == null) 0 else this.totalUnitsBusiness.hashCode())
        result = prime * result + (if (this.totalUnitsResidential == null) 0 else this.totalUnitsResidential.hashCode())
        result = prime * result + (if (this.totalParkingUnits == null) 0 else this.totalParkingUnits.hashCode())
        result = prime * result + (if (this.managedUnitsBusiness == null) 0 else this.managedUnitsBusiness.hashCode())
        result = prime * result + (if (this.managedUnitsResidential == null) 0 else this.managedUnitsResidential.hashCode())
        result = prime * result + (if (this.managedParkingUnits == null) 0 else this.managedParkingUnits.hashCode())
        result = prime * result + (if (this.contractForm == null) 0 else this.contractForm.hashCode())
        result = prime * result + (if (this.managementCategory == null) 0 else this.managementCategory.hashCode())
        result = prime * result + (if (this.managementPartnership == null) 0 else this.managementPartnership.hashCode())
        result = prime * result + (if (this.partnershipType == null) 0 else this.partnershipType.hashCode())
        result = prime * result + (if (this.managementPartner == null) 0 else this.managementPartner.hashCode())
        result = prime * result + (if (this.maintenancePartner == null) 0 else this.maintenancePartner.hashCode())
        result = prime * result + (if (this.detailsIssue == null) 0 else this.detailsIssue.hashCode())
        result = prime * result + (if (this.managementFormCategory == null) 0 else this.managementFormCategory.hashCode())
        result = prime * result + (if (this.specialRent == null) 0 else this.specialRent.hashCode())
        result = prime * result + (if (this.comCommunityPartnership == null) 0 else this.comCommunityPartnership.hashCode())
        result = prime * result + (if (this.proRataDays == null) 0 else this.proRataDays.hashCode())
        result = prime * result + (if (this.excludedProRataDays == null) 0 else this.excludedProRataDays.hashCode())
        result = prime * result + (if (this.nonStandardFlag == null) 0 else this.nonStandardFlag.hashCode())
        result = prime * result + (if (this.newOwnerNameAtNameChange == null) 0 else this.newOwnerNameAtNameChange.hashCode())
        result = prime * result + (if (this.ownerAddressAtNameChange == null) 0 else this.ownerAddressAtNameChange.hashCode())
        result = prime * result + (if (this.ownerTransferAccountOwnerCd == null) 0 else this.ownerTransferAccountOwnerCd.hashCode())
        result = prime * result + (if (this.detailsConsolidationUnit == null) 0 else this.detailsConsolidationUnit.hashCode())
        result = prime * result + (if (this.transferAccountDivision == null) 0 else this.transferAccountDivision.hashCode())
        result = prime * result + (if (this.recipientCd == null) 0 else this.recipientCd.hashCode())
        result = prime * result + (if (this.previousRecipientCd == null) 0 else this.previousRecipientCd.hashCode())
        result = prime * result + (if (this.renewalFeeAcquisition == null) 0 else this.renewalFeeAcquisition.hashCode())
        result = prime * result + (if (this.renewalFeeAcquisitionMonths == null) 0 else this.renewalFeeAcquisitionMonths.hashCode())
        result = prime * result + (if (this.renewalFeeCommissionRate == null) 0 else this.renewalFeeCommissionRate.hashCode())
        result = prime * result + (if (this.guarantorNotRequiredApprovalFlag == null) 0 else this.guarantorNotRequiredApprovalFlag.hashCode())
        result = prime * result + (if (this.fixedTermRentalContractConclusion == null) 0 else this.fixedTermRentalContractConclusion.hashCode())
        result = prime * result + (if (this.waterFeeManagementFeeCollection == null) 0 else this.waterFeeManagementFeeCollection.hashCode())
        result = prime * result + (if (this.waterMeterCategory == null) 0 else this.waterMeterCategory.hashCode())
        result = prime * result + (if (this.gasCategory == null) 0 else this.gasCategory.hashCode())
        result = prime * result + (if (this.sharedOwnershipCd1 == null) 0 else this.sharedOwnershipCd1.hashCode())
        result = prime * result + (if (this.delegatorSharedInterest1 == null) 0 else this.delegatorSharedInterest1.hashCode())
        result = prime * result + (if (this.sharedOwnershipCd2 == null) 0 else this.sharedOwnershipCd2.hashCode())
        result = prime * result + (if (this.delegatorSharedInterest2 == null) 0 else this.delegatorSharedInterest2.hashCode())
        result = prime * result + (if (this.sharedOwnershipCd3 == null) 0 else this.sharedOwnershipCd3.hashCode())
        result = prime * result + (if (this.delegatorSharedInterest3 == null) 0 else this.delegatorSharedInterest3.hashCode())
        result = prime * result + (if (this.waterReadingCategory == null) 0 else this.waterReadingCategory.hashCode())
        result = prime * result + (if (this.waterFeeCollection == null) 0 else this.waterFeeCollection.hashCode())
        result = prime * result + (if (this.depositHandlingCategory == null) 0 else this.depositHandlingCategory.hashCode())
        result = prime * result + (if (this.communityFeeManagementCollection == null) 0 else this.communityFeeManagementCollection.hashCode())
        result = prime * result + (if (this.communityFeeManagementPayment == null) 0 else this.communityFeeManagementPayment.hashCode())
        result = prime * result + (if (this.gasReadingCategory == null) 0 else this.gasReadingCategory.hashCode())
        result = prime * result + (if (this.petsAllowedCategory == null) 0 else this.petsAllowedCategory.hashCode())
        result = prime * result + (if (this.rentTransferAccountCategory == null) 0 else this.rentTransferAccountCategory.hashCode())
        result = prime * result + (if (this.monthlyRentTransferAccount == null) 0 else this.monthlyRentTransferAccount.hashCode())
        result = prime * result + (if (this.commonFeeTransferAccount == null) 0 else this.commonFeeTransferAccount.hashCode())
        result = prime * result + (if (this.commonFeeTransferAccountCd == null) 0 else this.commonFeeTransferAccountCd.hashCode())
        result = prime * result + (if (this.exclusiveBrokeragePeriod == null) 0 else this.exclusiveBrokeragePeriod.hashCode())
        result = prime * result + (if (this.mutualAidAssociationEnrollment == null) 0 else this.mutualAidAssociationEnrollment.hashCode())
        result = prime * result + (if (this.depositSettlementMethod == null) 0 else this.depositSettlementMethod.hashCode())
        result = prime * result + (if (this.depositManagementDelegatorRate == null) 0 else this.depositManagementDelegatorRate.hashCode())
        result = prime * result + (if (this.departureManagementServiceTermination == null) 0 else this.departureManagementServiceTermination.hashCode())
        result = prime * result + (if (this.buildingInspectionServiceSitePatrol == null) 0 else this.buildingInspectionServiceSitePatrol.hashCode())
        result = prime * result + (if (this.vacantRoomManagementService == null) 0 else this.vacantRoomManagementService.hashCode())
        result = prime * result + (if (this.waterFeeReading == null) 0 else this.waterFeeReading.hashCode())
        result = prime * result + (if (this.commonEquipmentMaintenance == null) 0 else this.commonEquipmentMaintenance.hashCode())
        result = prime * result + (if (this.otherMaintenance == null) 0 else this.otherMaintenance.hashCode())
        result = prime * result + (if (this.maintenanceFeeCollectionTarget == null) 0 else this.maintenanceFeeCollectionTarget.hashCode())
        result = prime * result + (if (this.maintenanceFeeUnit == null) 0 else this.maintenanceFeeUnit.hashCode())
        result = prime * result + (if (this.maintenanceFeeTotal == null) 0 else this.maintenanceFeeTotal.hashCode())
        result = prime * result + (if (this.maintenanceFeeDaitoShare == null) 0 else this.maintenanceFeeDaitoShare.hashCode())
        result = prime * result + (if (this.tenantSettlementService == null) 0 else this.tenantSettlementService.hashCode())
        result = prime * result + (if (this.tenantSettlementMngFeeDeduction == null) 0 else this.tenantSettlementMngFeeDeduction.hashCode())
        result = prime * result + (if (this.departureSettlementService == null) 0 else this.departureSettlementService.hashCode())
        result = prime * result + (if (this.salesDepartAchieveAccountingCategory == null) 0 else this.salesDepartAchieveAccountingCategory.hashCode())
        result = prime * result + (if (this.moveInOutManagement == null) 0 else this.moveInOutManagement.hashCode())
        result = prime * result + (if (this.mngDepartmentLeaseContractInput == null) 0 else this.mngDepartmentLeaseContractInput.hashCode())
        result = prime * result + (if (this.contractCategory == null) 0 else this.contractCategory.hashCode())
        result = prime * result + (if (this.siteArea == null) 0 else this.siteArea.hashCode())
        result = prime * result + (if (this.otherPurposes == null) 0 else this.otherPurposes.hashCode())
        result = prime * result + (if (this.otherPurposesContent == null) 0 else this.otherPurposesContent.hashCode())
        result = prime * result + (if (this.specialContractNo == null) 0 else this.specialContractNo.hashCode())
        result = prime * result + (if (this.additionalTask1 == null) 0 else this.additionalTask1.hashCode())
        result = prime * result + (if (this.additionalTask2 == null) 0 else this.additionalTask2.hashCode())
        result = prime * result + (if (this.managementBranchPhoneNo == null) 0 else this.managementBranchPhoneNo.hashCode())
        result = prime * result + (if (this.managementContractTrInitial == null) 0 else this.managementContractTrInitial.hashCode())
        result = prime * result + (if (this.managementContractTrNext == null) 0 else this.managementContractTrNext.hashCode())
        result = prime * result + (if (this.specialClausesIncluded == null) 0 else this.specialClausesIncluded.hashCode())
        result = prime * result + (if (this.maintenanceItemsIncluded == null) 0 else this.maintenanceItemsIncluded.hashCode())
        result = prime * result + (if (this.managementDelegationDataIncluded == null) 0 else this.managementDelegationDataIncluded.hashCode())
        result = prime * result + (if (this.mngDelegationContentDataIncluded == null) 0 else this.mngDelegationContentDataIncluded.hashCode())
        result = prime * result + (if (this.agreementNoChangeManagementNo == null) 0 else this.agreementNoChangeManagementNo.hashCode())
        result = prime * result + (if (this.transferDate == null) 0 else this.transferDate.hashCode())
        result = prime * result + (if (this.recordNewOldCategory == null) 0 else this.recordNewOldCategory.hashCode())
        result = prime * result + (if (this.initialSetupFlag == null) 0 else this.initialSetupFlag.hashCode())
        result = prime * result + (if (this.contractEndDate == null) 0 else this.contractEndDate.hashCode())
        result = prime * result + (if (this.operationStartDate == null) 0 else this.operationStartDate.hashCode())
        result = prime * result + (if (this.maintenanceServiceCategory == null) 0 else this.maintenanceServiceCategory.hashCode())
        result = prime * result + (if (this.simultaneousContractOutput == null) 0 else this.simultaneousContractOutput.hashCode())
        result = prime * result + (if (this.outputControlCategory == null) 0 else this.outputControlCategory.hashCode())
        result = prime * result + (if (this.managementStartExpectedDate == null) 0 else this.managementStartExpectedDate.hashCode())
        result = prime * result + (if (this.autoCreationCategory == null) 0 else this.autoCreationCategory.hashCode())
        result = prime * result + (if (this.maintenanceDelegationCreationCategory == null) 0 else this.maintenanceDelegationCreationCategory.hashCode())
        result = prime * result + (if (this.previousConclusionDate == null) 0 else this.previousConclusionDate.hashCode())
        result = prime * result + (if (this.mainteDelegationOnlyUpdateCategory == null) 0 else this.mainteDelegationOnlyUpdateCategory.hashCode())
        result = prime * result + (if (this.mainteDelegationContractOutputTarget == null) 0 else this.mainteDelegationContractOutputTarget.hashCode())
        result = prime * result + (if (this.mainteConclusionExpectedDate == null) 0 else this.mainteConclusionExpectedDate.hashCode())
        result = prime * result + (if (this.mainteConclusionDate == null) 0 else this.mainteConclusionDate.hashCode())
        result = prime * result + (if (this.conclusionExpectedDateRequired == null) 0 else this.conclusionExpectedDateRequired.hashCode())
        result = prime * result + (if (this.houseComStoreCd == null) 0 else this.houseComStoreCd.hashCode())
        result = prime * result + (if (this.mngStartDateChangeContractChange == null) 0 else this.mngStartDateChangeContractChange.hashCode())
        result = prime * result + (if (this.businessNewGuaranteeCategory == null) 0 else this.businessNewGuaranteeCategory.hashCode())
        result = prime * result + (if (this.repairSpecialClause == null) 0 else this.repairSpecialClause.hashCode())
        result = prime * result + (if (this.repairSpecialClausePeriod == null) 0 else this.repairSpecialClausePeriod.hashCode())
        result = prime * result + (if (this.inputManagementNo == null) 0 else this.inputManagementNo.hashCode())
        result = prime * result + (if (this.nonLeaseUseResidential == null) 0 else this.nonLeaseUseResidential.hashCode())
        result = prime * result + (if (this.nonLeaseUseBusiness == null) 0 else this.nonLeaseUseBusiness.hashCode())
        result = prime * result + (if (this.nonLeaseUseParking == null) 0 else this.nonLeaseUseParking.hashCode())
        result = prime * result + (if (this.buildingStructure == null) 0 else this.buildingStructure.hashCode())
        result = prime * result + (if (this.buildingFloors == null) 0 else this.buildingFloors.hashCode())
        result = prime * result + (if (this.buildingAddressCd1 == null) 0 else this.buildingAddressCd1.hashCode())
        result = prime * result + (if (this.buildingAddressCd2 == null) 0 else this.buildingAddressCd2.hashCode())
        result = prime * result + (if (this.buildingAddressCd3 == null) 0 else this.buildingAddressCd3.hashCode())
        result = prime * result + (if (this.buildingAddressDetail == null) 0 else this.buildingAddressDetail.hashCode())
        result = prime * result + (if (this.meterCount == null) 0 else this.meterCount.hashCode())
        result = prime * result + (if (this.waterUsageIncluded == null) 0 else this.waterUsageIncluded.hashCode())
        result = prime * result + (if (this.waterUsageMonthly == null) 0 else this.waterUsageMonthly.hashCode())
        result = prime * result + (if (this.maintenanceItemBIncluded == null) 0 else this.maintenanceItemBIncluded.hashCode())
        result = prime * result + (if (this.maintenanceItemBMonthly == null) 0 else this.maintenanceItemBMonthly.hashCode())
        result = prime * result + (if (this.maintenanceItemAIncluded == null) 0 else this.maintenanceItemAIncluded.hashCode())
        result = prime * result + (if (this.maintenanceItemAMonthly == null) 0 else this.maintenanceItemAMonthly.hashCode())
        result = prime * result + (if (this.rentalAdjustmentIncluded == null) 0 else this.rentalAdjustmentIncluded.hashCode())
        result = prime * result + (if (this.rentalAdjustmentMonthly == null) 0 else this.rentalAdjustmentMonthly.hashCode())
        result = prime * result + (if (this.maintenanceFeeAdjustmentIncluded == null) 0 else this.maintenanceFeeAdjustmentIncluded.hashCode())
        result = prime * result + (if (this.maintenanceFeeAdjustmentMonthly == null) 0 else this.maintenanceFeeAdjustmentMonthly.hashCode())
        result = prime * result + (if (this.repairFeeAdjustmentIncluded == null) 0 else this.repairFeeAdjustmentIncluded.hashCode())
        result = prime * result + (if (this.repairFeeAdjustmentMonthly == null) 0 else this.repairFeeAdjustmentMonthly.hashCode())
        result = prime * result + (if (this.communityFeeAdjustmentIncluded == null) 0 else this.communityFeeAdjustmentIncluded.hashCode())
        result = prime * result + (if (this.communityFeeAdjustmentMonthly == null) 0 else this.communityFeeAdjustmentMonthly.hashCode())
        result = prime * result + (if (this.catvAdjustmentIncluded == null) 0 else this.catvAdjustmentIncluded.hashCode())
        result = prime * result + (if (this.catvAdjustmentMonthly == null) 0 else this.catvAdjustmentMonthly.hashCode())
        result = prime * result + (if (this.otherAdjustmentDescription == null) 0 else this.otherAdjustmentDescription.hashCode())
        result = prime * result + (if (this.otherAdjustmentIncluded == null) 0 else this.otherAdjustmentIncluded.hashCode())
        result = prime * result + (if (this.otherAdjustmentMonthly == null) 0 else this.otherAdjustmentMonthly.hashCode())
        result = prime * result + (if (this.businessNonLeaseAdjustmentIncluded == null) 0 else this.businessNonLeaseAdjustmentIncluded.hashCode())
        result = prime * result + (if (this.businessNonLeaseAdjustmentMonthly == null) 0 else this.businessNonLeaseAdjustmentMonthly.hashCode())
        result = prime * result + (if (this.aboveDescription == null) 0 else this.aboveDescription.hashCode())
        result = prime * result + (if (this.aboveIncluded == null) 0 else this.aboveIncluded.hashCode())
        result = prime * result + (if (this.aboveMonthly == null) 0 else this.aboveMonthly.hashCode())
        result = prime * result + (if (this.aboveAdjustmentCategory == null) 0 else this.aboveAdjustmentCategory.hashCode())
        result = prime * result + (if (this.subleaseRentalAssessmentTotal == null) 0 else this.subleaseRentalAssessmentTotal.hashCode())
        result = prime * result + (if (this.rentalAdjustmentAmount == null) 0 else this.rentalAdjustmentAmount.hashCode())
        result = prime * result + (if (this.leaseRate == null) 0 else this.leaseRate.hashCode())
        result = prime * result + (if (this.leaseRental == null) 0 else this.leaseRental.hashCode())
        result = prime * result + (if (this.adjustmentAmount == null) 0 else this.adjustmentAmount.hashCode())
        result = prime * result + (if (this.adjustmentCategory == null) 0 else this.adjustmentCategory.hashCode())
        result = prime * result + (if (this.leasePaymentRental == null) 0 else this.leasePaymentRental.hashCode())
        result = prime * result + (if (this.consumptionTax == null) 0 else this.consumptionTax.hashCode())
        result = prime * result + (if (this.contractBranchCd == null) 0 else this.contractBranchCd.hashCode())
        result = prime * result + (if (this.parkingAddressCd1 == null) 0 else this.parkingAddressCd1.hashCode())
        result = prime * result + (if (this.parkingAddressCd2 == null) 0 else this.parkingAddressCd2.hashCode())
        result = prime * result + (if (this.parkingAddressCd3 == null) 0 else this.parkingAddressCd3.hashCode())
        result = prime * result + (if (this.parkingAddressDetail == null) 0 else this.parkingAddressDetail.hashCode())
        result = prime * result + (if (this.leaseAssessmentParkingSpaces == null) 0 else this.leaseAssessmentParkingSpaces.hashCode())
        result = prime * result + (if (this.leaseNonAssessmentParkingSpaces == null) 0 else this.leaseNonAssessmentParkingSpaces.hashCode())
        result = prime * result + (if (this.leasedResidentialUnits == null) 0 else this.leasedResidentialUnits.hashCode())
        result = prime * result + (if (this.leasedBusinessUnits == null) 0 else this.leasedBusinessUnits.hashCode())
        result = prime * result + (if (this.otherItem1 == null) 0 else this.otherItem1.hashCode())
        result = prime * result + (if (this.otherAdjustment1 == null) 0 else this.otherAdjustment1.hashCode())
        result = prime * result + (if (this.otherItem2 == null) 0 else this.otherItem2.hashCode())
        result = prime * result + (if (this.otherAdjustment2 == null) 0 else this.otherAdjustment2.hashCode())
        result = prime * result + (if (this.otherItem3 == null) 0 else this.otherItem3.hashCode())
        result = prime * result + (if (this.otherAdjustment3 == null) 0 else this.otherAdjustment3.hashCode())
        result = prime * result + (if (this.otherItem4 == null) 0 else this.otherItem4.hashCode())
        result = prime * result + (if (this.otherAdjustment4 == null) 0 else this.otherAdjustment4.hashCode())
        result = prime * result + (if (this.otherItem5 == null) 0 else this.otherItem5.hashCode())
        result = prime * result + (if (this.otherAdjustment5 == null) 0 else this.otherAdjustment5.hashCode())
        result = prime * result + (if (this.bulkSwitchSign == null) 0 else this.bulkSwitchSign.hashCode())
        result = prime * result + (if (this.switchPaymentMethod == null) 0 else this.switchPaymentMethod.hashCode())
        result = prime * result + (if (this.daitoBulkRoomParkingDbUpdateDate == null) 0 else this.daitoBulkRoomParkingDbUpdateDate.hashCode())
        result = prime * result + (if (this.switchType == null) 0 else this.switchType.hashCode())
        result = prime * result + (if (this.expirationPaymentMethod == null) 0 else this.expirationPaymentMethod.hashCode())
        result = prime * result + (if (this.nonJoinedRoomsKyosai == null) 0 else this.nonJoinedRoomsKyosai.hashCode())
        result = prime * result + (if (this.joinedRoomsKyosai == null) 0 else this.joinedRoomsKyosai.hashCode())
        result = prime * result + (if (this.managementOnlyContractOutput == null) 0 else this.managementOnlyContractOutput.hashCode())
        result = prime * result + (if (this.maintenanceShortfall == null) 0 else this.maintenanceShortfall.hashCode())
        result = prime * result + (if (this.managementFeeRate == null) 0 else this.managementFeeRate.hashCode())
        result = prime * result + (if (this.reserve == null) 0 else this.reserve.hashCode())
        result = prime * result + (if (this.expirationType == null) 0 else this.expirationType.hashCode())
        result = prime * result + (if (this.nonLeaseUseTr == null) 0 else this.nonLeaseUseTr.hashCode())
        result = prime * result + (if (this.rentRevisionProcessingCategory == null) 0 else this.rentRevisionProcessingCategory.hashCode())
        result = prime * result + (if (this.changeContractTargetCategory == null) 0 else this.changeContractTargetCategory.hashCode())
        result = prime * result + (if (this.confirmationOutputTargetCategory == null) 0 else this.confirmationOutputTargetCategory.hashCode())
        result = prime * result + (if (this.receptionCategory == null) 0 else this.receptionCategory.hashCode())
        result = prime * result + (if (this.meterCount2 == null) 0 else this.meterCount2.hashCode())
        result = prime * result + (if (this.waterUsageIncluded2 == null) 0 else this.waterUsageIncluded2.hashCode())
        result = prime * result + (if (this.waterUsageMonthly2 == null) 0 else this.waterUsageMonthly2.hashCode())
        result = prime * result + (if (this.maintenanceItemBIncluded2 == null) 0 else this.maintenanceItemBIncluded2.hashCode())
        result = prime * result + (if (this.maintenanceItemBMonthly2 == null) 0 else this.maintenanceItemBMonthly2.hashCode())
        result = prime * result + (if (this.maintenanceItemAIncluded2 == null) 0 else this.maintenanceItemAIncluded2.hashCode())
        result = prime * result + (if (this.maintenanceItemAMonthly2 == null) 0 else this.maintenanceItemAMonthly2.hashCode())
        result = prime * result + (if (this.rentAdjustmentIncluded == null) 0 else this.rentAdjustmentIncluded.hashCode())
        result = prime * result + (if (this.rentAdjustmentMonthly == null) 0 else this.rentAdjustmentMonthly.hashCode())
        result = prime * result + (if (this.maintenanceFeeAdjustmentIncluded2 == null) 0 else this.maintenanceFeeAdjustmentIncluded2.hashCode())
        result = prime * result + (if (this.maintenanceFeeAdjustmentMonthly2 == null) 0 else this.maintenanceFeeAdjustmentMonthly2.hashCode())
        result = prime * result + (if (this.repairFeeAdjustmentIncluded2 == null) 0 else this.repairFeeAdjustmentIncluded2.hashCode())
        result = prime * result + (if (this.repairFeeAdjustmentMonthly2 == null) 0 else this.repairFeeAdjustmentMonthly2.hashCode())
        result = prime * result + (if (this.communityFeeAdjustmentIncluded2 == null) 0 else this.communityFeeAdjustmentIncluded2.hashCode())
        result = prime * result + (if (this.communityFeeAdjustmentMonthly2 == null) 0 else this.communityFeeAdjustmentMonthly2.hashCode())
        result = prime * result + (if (this.catvAdjustmentIncluded2 == null) 0 else this.catvAdjustmentIncluded2.hashCode())
        result = prime * result + (if (this.catvAdjustmentMonthly2 == null) 0 else this.catvAdjustmentMonthly2.hashCode())
        result = prime * result + (if (this.otherAdjustmentDescription2 == null) 0 else this.otherAdjustmentDescription2.hashCode())
        result = prime * result + (if (this.otherAdjustmentIncluded2 == null) 0 else this.otherAdjustmentIncluded2.hashCode())
        result = prime * result + (if (this.otherAdjustmentMonthly2 == null) 0 else this.otherAdjustmentMonthly2.hashCode())
        result = prime * result + (if (this.businessNonLeaseAdjustmentIncluded2 == null) 0 else this.businessNonLeaseAdjustmentIncluded2.hashCode())
        result = prime * result + (if (this.businessNonLeaseAdjustmentMonthly2 == null) 0 else this.businessNonLeaseAdjustmentMonthly2.hashCode())
        result = prime * result + (if (this.aboveDescription2 == null) 0 else this.aboveDescription2.hashCode())
        result = prime * result + (if (this.aboveIncluded2 == null) 0 else this.aboveIncluded2.hashCode())
        result = prime * result + (if (this.aboveMonthly2 == null) 0 else this.aboveMonthly2.hashCode())
        result = prime * result + (if (this.aboveAdjustmentCategory2 == null) 0 else this.aboveAdjustmentCategory2.hashCode())
        result = prime * result + (if (this.subleaseRentAssessmentTotal == null) 0 else this.subleaseRentAssessmentTotal.hashCode())
        result = prime * result + (if (this.rentAdjustmentAmount == null) 0 else this.rentAdjustmentAmount.hashCode())
        result = prime * result + (if (this.leaseRate2 == null) 0 else this.leaseRate2.hashCode())
        result = prime * result + (if (this.leaseRental2 == null) 0 else this.leaseRental2.hashCode())
        result = prime * result + (if (this.adjustmentAmount2 == null) 0 else this.adjustmentAmount2.hashCode())
        result = prime * result + (if (this.adjustmentCategory2 == null) 0 else this.adjustmentCategory2.hashCode())
        result = prime * result + (if (this.leasePaymentRental2 == null) 0 else this.leasePaymentRental2.hashCode())
        result = prime * result + (if (this.consumptionTax2 == null) 0 else this.consumptionTax2.hashCode())
        result = prime * result + (if (this.maintenanceShortfall2 == null) 0 else this.maintenanceShortfall2.hashCode())
        result = prime * result + (if (this.managementFeeRate2 == null) 0 else this.managementFeeRate2.hashCode())
        result = prime * result + (if (this.preRevisionMinimumParkingFee == null) 0 else this.preRevisionMinimumParkingFee.hashCode())
        result = prime * result + (if (this.preRevisionParkingCount == null) 0 else this.preRevisionParkingCount.hashCode())
        result = prime * result + (if (this.postRevisionMinimumParkingFee == null) 0 else this.postRevisionMinimumParkingFee.hashCode())
        result = prime * result + (if (this.postRevisionParkingCount == null) 0 else this.postRevisionParkingCount.hashCode())
        result = prime * result + (if (this.subleaseRateUnder_10 == null) 0 else this.subleaseRateUnder_10.hashCode())
        result = prime * result + (if (this.subleaseRateUnder_20 == null) 0 else this.subleaseRateUnder_20.hashCode())
        result = prime * result + (if (this.subleaseRate_20AndAbove == null) 0 else this.subleaseRate_20AndAbove.hashCode())
        result = prime * result + (if (this.vacancyRateUnder_10 == null) 0 else this.vacancyRateUnder_10.hashCode())
        result = prime * result + (if (this.vacancyRateUnder_20 == null) 0 else this.vacancyRateUnder_20.hashCode())
        result = prime * result + (if (this.vacancyRate_20AndAbove == null) 0 else this.vacancyRate_20AndAbove.hashCode())
        result = prime * result + (if (this.maintenanceRequiredAmount == null) 0 else this.maintenanceRequiredAmount.hashCode())
        result = prime * result + (if (this.leaseRoomMaintenanceFee == null) 0 else this.leaseRoomMaintenanceFee.hashCode())
        result = prime * result + (if (this.managedRoomMaintenanceFee == null) 0 else this.managedRoomMaintenanceFee.hashCode())
        result = prime * result + (if (this.ownerDelayRoomMaintenanceFee == null) 0 else this.ownerDelayRoomMaintenanceFee.hashCode())
        result = prime * result + (if (this.maintenanceShortfall3 == null) 0 else this.maintenanceShortfall3.hashCode())
        result = prime * result + (if (this.generalApplicationNo1 == null) 0 else this.generalApplicationNo1.hashCode())
        result = prime * result + (if (this.generalApplicationNo2 == null) 0 else this.generalApplicationNo2.hashCode())
        result = prime * result + (if (this.generalApplicationNo3 == null) 0 else this.generalApplicationNo3.hashCode())
        result = prime * result + (if (this.rentPrepaymentConfirmationCategory == null) 0 else this.rentPrepaymentConfirmationCategory.hashCode())
        result = prime * result + (if (this.checkSheetConfirmation == null) 0 else this.checkSheetConfirmation.hashCode())
        result = prime * result + (if (this.contentConfirmationDate == null) 0 else this.contentConfirmationDate.hashCode())
        result = prime * result + (if (this.maintenanceContentConfirmationDate == null) 0 else this.maintenanceContentConfirmationDate.hashCode())
        result = prime * result + (if (this.consumptionTaxCalculationBaseDate == null) 0 else this.consumptionTaxCalculationBaseDate.hashCode())
        result = prime * result + (if (this.consumptionTaxRate == null) 0 else this.consumptionTaxRate.hashCode())
        result = prime * result + (if (this.consumptionTaxRevisionFlag == null) 0 else this.consumptionTaxRevisionFlag.hashCode())
        result = prime * result + (if (this.parkingTotalSpaces == null) 0 else this.parkingTotalSpaces.hashCode())
        result = prime * result + (if (this.leaseSpaces == null) 0 else this.leaseSpaces.hashCode())
        result = prime * result + (if (this.nonLeaseSpaces == null) 0 else this.nonLeaseSpaces.hashCode())
        result = prime * result + (if (this.subleaseParkingFeeTotal == null) 0 else this.subleaseParkingFeeTotal.hashCode())
        result = prime * result + (if (this.parkingFeeAdjustmentAmount == null) 0 else this.parkingFeeAdjustmentAmount.hashCode())
        result = prime * result + (if (this.standardParkingFee == null) 0 else this.standardParkingFee.hashCode())
        result = prime * result + (if (this.leaseAssessmentParkingFee == null) 0 else this.leaseAssessmentParkingFee.hashCode())
        result = prime * result + (if (this.leaseRate3 == null) 0 else this.leaseRate3.hashCode())
        result = prime * result + (if (this.leaseParkingFee == null) 0 else this.leaseParkingFee.hashCode())
        result = prime * result + (if (this.includedConsumptionTax == null) 0 else this.includedConsumptionTax.hashCode())
        result = prime * result + (if (this.rentIncreaseCategory == null) 0 else this.rentIncreaseCategory.hashCode())
        result = prime * result + (if (this.noteCode == null) 0 else this.noteCode.hashCode())
        return result
    }
}
