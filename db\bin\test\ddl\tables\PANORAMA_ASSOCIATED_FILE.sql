-- TABLE: PANORAMA_ASSOCIATED_FILE(パノラマ関連付けファイル)

CREATE TABLE PANORAMA_ASSOCIATED_FILE(
     PORTAL_GOOD_ROOM_MEMBER_ID                   numeric(8,0)                  
,    PANORAMA_ID                                  numeric(9,0)                  
,    BUILDING_CD                                  varchar(9)        NOT NULL    
,    ROOM_CD                                      varchar(5)        NOT NULL    
,    LOGICAL_DELETE_FLAG                          varchar(1)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    CREATION_DATE                                numeric(8,0)                  
,    UPDATE_TIME2                                 numeric(6,0)                  
,    UPDATER_ID                                   varchar(10)                   
,    PANORAMA_TYPE                                numeric(2,0)                  
,    CONSTRAINT PK_PANORAMA_ASSOCIATED_FILE PRIMARY KEY (BUILDING_CD, ROOM_CD)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PANORAMA_ASSOCIATED_FILE IS 'パノラマ関連付けファイル 既存システム物理名: ERA10P';
COMMENT ON COLUMN PANORAMA_ASSOCIATED_FILE.PORTAL_GOOD_ROOM_MEMBER_ID IS 'ポータル版 いい部屋会員ID 既存システム物理名: A1001C 1:魚眼画像 2:スイングパノラマ 3:静止画 4:THETA画像';
COMMENT ON COLUMN PANORAMA_ASSOCIATED_FILE.PANORAMA_ID IS 'パノラマID 既存システム物理名: A1002C';
COMMENT ON COLUMN PANORAMA_ASSOCIATED_FILE.BUILDING_CD IS '建物CD 既存システム物理名: A1003C';
COMMENT ON COLUMN PANORAMA_ASSOCIATED_FILE.ROOM_CD IS '部屋CD 既存システム物理名: A1004C';
COMMENT ON COLUMN PANORAMA_ASSOCIATED_FILE.LOGICAL_DELETE_FLAG IS '論理削除サイン 既存システム物理名: A1005S';
COMMENT ON COLUMN PANORAMA_ASSOCIATED_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: A1006D';
COMMENT ON COLUMN PANORAMA_ASSOCIATED_FILE.UPDATE_TIME IS '更新時刻 既存システム物理名: A1007T';
COMMENT ON COLUMN PANORAMA_ASSOCIATED_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: A1008D';
COMMENT ON COLUMN PANORAMA_ASSOCIATED_FILE.UPDATE_TIME2 IS '更新時刻 既存システム物理名: A1009T';
COMMENT ON COLUMN PANORAMA_ASSOCIATED_FILE.UPDATER_ID IS '更新者ID 既存システム物理名: A1010C';
COMMENT ON COLUMN PANORAMA_ASSOCIATED_FILE.PANORAMA_TYPE IS 'パノラマ種別 既存システム物理名: A1011C';
