/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.OfficeMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.OfficeMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 事業所マスタ 既存システム物理名: XXJGYP
 */
@Suppress("UNCHECKED_CAST")
open class OfficeMasterRecord private constructor() : TableRecordImpl<OfficeMasterRecord>(OfficeMasterTable.OFFICE_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var reflectionDate: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var deleteFlag: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var officeCode: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var officeKana: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var officeName: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var officeAbbreviation_2: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var officeAbbreviation_3: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var officeAbbreviation_4: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var representativeLocationCode: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var parentChildCategory: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var parentOfficeCode: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var branchOpeningDate: Int?
        set(value): Unit = set(17, value)
        get(): Int? = get(17) as Int?

    open var branchEstablishmentDate: Int?
        set(value): Unit = set(18, value)
        get(): Int? = get(18) as Int?

    open var regionCode: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    /**
     * Create a detached, initialised OfficeMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, reflectionDate: Int? = null, deleteFlag: String? = null, officeCode: String? = null, officeKana: String? = null, officeName: String? = null, officeAbbreviation_2: String? = null, officeAbbreviation_3: String? = null, officeAbbreviation_4: String? = null, representativeLocationCode: String? = null, parentChildCategory: String? = null, parentOfficeCode: String? = null, branchOpeningDate: Int? = null, branchEstablishmentDate: Int? = null, regionCode: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.reflectionDate = reflectionDate
        this.deleteFlag = deleteFlag
        this.officeCode = officeCode
        this.officeKana = officeKana
        this.officeName = officeName
        this.officeAbbreviation_2 = officeAbbreviation_2
        this.officeAbbreviation_3 = officeAbbreviation_3
        this.officeAbbreviation_4 = officeAbbreviation_4
        this.representativeLocationCode = representativeLocationCode
        this.parentChildCategory = parentChildCategory
        this.parentOfficeCode = parentOfficeCode
        this.branchOpeningDate = branchOpeningDate
        this.branchEstablishmentDate = branchEstablishmentDate
        this.regionCode = regionCode
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised OfficeMasterRecord
     */
    constructor(value: OfficeMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.reflectionDate = value.reflectionDate
            this.deleteFlag = value.deleteFlag
            this.officeCode = value.officeCode
            this.officeKana = value.officeKana
            this.officeName = value.officeName
            this.officeAbbreviation_2 = value.officeAbbreviation_2
            this.officeAbbreviation_3 = value.officeAbbreviation_3
            this.officeAbbreviation_4 = value.officeAbbreviation_4
            this.representativeLocationCode = value.representativeLocationCode
            this.parentChildCategory = value.parentChildCategory
            this.parentOfficeCode = value.parentOfficeCode
            this.branchOpeningDate = value.branchOpeningDate
            this.branchEstablishmentDate = value.branchEstablishmentDate
            this.regionCode = value.regionCode
            resetChangedOnNotNull()
        }
    }
}
