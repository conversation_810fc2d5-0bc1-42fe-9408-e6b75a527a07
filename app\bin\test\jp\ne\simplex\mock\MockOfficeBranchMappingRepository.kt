package jp.ne.simplex.mock

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Office
import jp.ne.simplex.application.repository.db.OfficeBranchMappingRepositoryInterface

class MockOfficeBranchMappingRepository(
    private val getByOfficeCodeFunc: (officeCode: Office.Code?) -> Branch.Code = { _ ->
        Branch.Code.of("")
    },
    private val getByBranchCodeFunc: (branchCode: Branch.Code?) -> Office.Code = { _ ->
        Office.Code.of("")
    },
) : OfficeBranchMappingRepositoryInterface {
    override fun get(officeCode: Office.Code?): Branch.Code {
        return getByOfficeCodeFunc(officeCode)
    }

    override fun get(branchCode: Branch.Code?): Office.Code {
        return getByBranchCodeFunc(branchCode)
    }
}
