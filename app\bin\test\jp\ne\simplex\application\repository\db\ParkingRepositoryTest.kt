package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.db.jooq.gen.tables.pojos.ThirtyFiveYearBulkBuildingFileContPojo
import jp.ne.simplex.db.jooq.gen.tables.records.ThirtyFiveYearBulkBuildingFileContRecord
import jp.ne.simplex.db.jooq.gen.tables.references.*
import jp.ne.simplex.stub.*
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class ParkingRepositoryTest : AbstractTestContainerTest() {
    private lateinit var repository: ParkingRepository

    override fun beforeEach() {
        repository = ParkingRepository(dslContext)
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PARKING, ROOM_MASTER, BUILDING_INFO_MASTER,
            CONTRACT, THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT, TEMPORARY_CONTRACT)
    }

    @Nested
    @DisplayName("受注コードリスト取得の検証")
    inner class Scenario1 {

        @BeforeEach
        fun setup() {
            dslContext.saveParkingPojo(
                stubParkingPojo(
                    buildingCode = "000024301",
                    parkingLotCode = "701",
                ),
                stubParkingPojo(
                    buildingCode = "000024301",
                    parkingLotCode = "702",
                ),
                stubParkingPojo(
                    buildingCode = "000024302",
                    parkingLotCode = "101",
                ),
                stubParkingPojo(
                    buildingCode = "000014301",
                    parkingLotCode = "101",
                ),
                stubParkingPojo(
                    buildingCode = "000014401",
                    parkingLotCode = "101",
                ),
            )
        }

        @Test
        @DisplayName("駐車場テーブルに存在する全ての受注コード（建物コードの上7桁）が昇順のリストで取得できること")
        fun case01() {
            val results = repository.getAllOrderCodes()
            val expected = mutableListOf(
                Building.OrderCode.of("0000143"),
                Building.OrderCode.of("0000144"),
                Building.OrderCode.of("0000243")
            )
            assertEquals(expected, results)
        }
    }

    @Nested
    @DisplayName("駐車場二台目契約可否取得の検証")
    inner class Scenario2 {
        @BeforeEach
        fun setup() {
            dslContext.saveRoomMasterPojo(
                stubRoomMasterPojo(buildingCode = "000024301", roomCode = "701"),
                stubRoomMasterPojo(buildingCode = "000024301", roomCode = "702"),
                stubRoomMasterPojo(buildingCode = "000024301", roomCode = "703"),
                stubRoomMasterPojo(buildingCode = "000024301", roomCode = "704"),
                stubRoomMasterPojo(buildingCode = "000024302", roomCode = "101"),
                stubRoomMasterPojo(
                    buildingCode = "000024101",
                    roomCode = "101",
                    logicalDeleteSign = 1
                ),
            )
        }

        @Test
        @DisplayName("該当する部屋数が0件")
        fun case01() {
            val result = repository.getRoomCountByOrderCode(Building.OrderCode.of("0000241"))

            assertEquals(0, result)
        }

        @Test
        @DisplayName("該当する部屋数が複数件")
        fun case02() {
            val result = repository.getRoomCountByOrderCode(Building.OrderCode.of("0000243"))

            assertEquals(5, result)
        }
    }

    @Nested
    @DisplayName("空き部屋数取得の検証")
    inner class Scenario3 {
        @BeforeEach
        fun setup() {
            dslContext.saveBuildingInfoMasterPojo(
                stubBuildingInfoMasterPojo(buildingCode = "000024101", vacantRooms = 0),
                stubBuildingInfoMasterPojo(buildingCode = "000024301", vacantRooms = 3),
                stubBuildingInfoMasterPojo(buildingCode = "000024302", vacantRooms = 3),
                stubBuildingInfoMasterPojo(buildingCode = "000024303"),
            )
        }

        @Test
        @DisplayName("該当する部屋数が0件")
        fun case01() {
            val result =
                repository.getVacancyRoomCountByOrderCode(Building.OrderCode.of("0000241"))

            assertEquals(0, result)
        }

        @Test
        @DisplayName("該当する部屋数が複数件")
        fun case02() {
            val result =
                repository.getVacancyRoomCountByOrderCode(Building.OrderCode.of("0000243"))

            assertEquals(3, result) // 複数棟の場合、全ての棟に複数棟合算されている値が入っている
        }
    }

    @Nested
    @DisplayName("一括借上タイプが3の契約が紐付く建物取得の検証")
    inner class Scenario4 {
        @BeforeEach
        fun setup() {
            dslContext.saveContractPojo(
                stubContractPojo(
                    effectiveStartDate = 0,
                    effectiveEndDate = 99999999,
                    buildingCd = "000024301",
                    initialSetupSign = null,
                    contractType = "08000",
                    logicalDeleteSign = "0"
                ),
            )
            dslContext.saveTemporaryContractPojo(
                stubTemporaryContractPojo(
                    effectiveStartDate = 0,
                    effectiveEndDate = 99999999,
                    buildingCd = "000024302",
                    initialSetupFlag = "1",
                    contractType = "08000",
                    logicalDeleteFlag = "0"
                ),
            )
            dslContext.save(
                THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT,
                recordConstructor = { p: ThirtyFiveYearBulkBuildingFileContPojo -> ThirtyFiveYearBulkBuildingFileContRecord(p) },
                pojos = listOf(
                    ThirtyFiveYearBulkBuildingFileContPojo(
                        buildingCd = "000024301",
                        effectiveDate = 0,
                        bulkLeaseType = 3,
                        logicalDeleteSign = "0"
                    ),
                    ThirtyFiveYearBulkBuildingFileContPojo(
                        buildingCd = "000024302",
                        effectiveDate = 0,
                        bulkLeaseType = 3,
                        logicalDeleteSign = "0"
                    )
                )
            )
        }

        @Test
        @DisplayName("建物数が2件取得できる")
        fun case01() {
            val result =
                repository.getThirtyFiveYearBulkBuildingContract(Building.OrderCode.of("0000243"))
            assertEquals(
                listOf(
                    Building.Code.of("000024301"),
                    Building.Code.of("000024302")
                ), result
            )
        }
    }
}
