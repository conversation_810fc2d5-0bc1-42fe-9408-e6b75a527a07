/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.CalendarMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.CalendarMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * カレンダーマスタ 既存システム物理名: XXYMDP
 */
@Suppress("UNCHECKED_CAST")
open class CalendarMasterRecord private constructor() : TableRecordImpl<CalendarMasterRecord>(CalendarMasterTable.CALENDAR_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var reflectionDate: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var deleteFlag: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var yearMonth: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var daysInMonth: Byte?
        set(value): Unit = set(9, value)
        get(): Byte? = get(9) as Byte?

    open var companyWorkDays: Byte?
        set(value): Unit = set(10, value)
        get(): Byte? = get(10) as Byte?

    open var cumulativeDaysPrevMonthEnd: Short?
        set(value): Unit = set(11, value)
        get(): Short? = get(11) as Short?

    open var weekdayPrevMonthEnd: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var weekday_01: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var holiday_01: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var companyHoliday_01: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var bankHoliday_01: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var cumulativeAnnualDays_01: Short?
        set(value): Unit = set(17, value)
        get(): Short? = get(17) as Short?

    open var weekday_02: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var holiday_02: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var companyHoliday_02: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var bankHoliday_02: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var cumulativeAnnualDays_02: Short?
        set(value): Unit = set(22, value)
        get(): Short? = get(22) as Short?

    open var weekday_03: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var holiday_03: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var companyHoliday_03: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var bankHoliday_03: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var cumulativeAnnualDays_03: Short?
        set(value): Unit = set(27, value)
        get(): Short? = get(27) as Short?

    open var weekday_04: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var holiday_04: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var companyHoliday_04: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var bankHoliday_04: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var cumulativeAnnualDays_04: Short?
        set(value): Unit = set(32, value)
        get(): Short? = get(32) as Short?

    open var weekday_05: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var holiday_05: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var companyHoliday_05: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var bankHoliday_05: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var cumulativeAnnualDays_05: Short?
        set(value): Unit = set(37, value)
        get(): Short? = get(37) as Short?

    open var weekday_06: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var holiday_06: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var companyHoliday_06: String?
        set(value): Unit = set(40, value)
        get(): String? = get(40) as String?

    open var bankHoliday_06: String?
        set(value): Unit = set(41, value)
        get(): String? = get(41) as String?

    open var cumulativeAnnualDays_06: Short?
        set(value): Unit = set(42, value)
        get(): Short? = get(42) as Short?

    open var weekday_07: String?
        set(value): Unit = set(43, value)
        get(): String? = get(43) as String?

    open var holiday_07: String?
        set(value): Unit = set(44, value)
        get(): String? = get(44) as String?

    open var companyHoliday_07: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var bankHoliday_07: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var cumulativeAnnualDays_07: Short?
        set(value): Unit = set(47, value)
        get(): Short? = get(47) as Short?

    open var weekday_08: String?
        set(value): Unit = set(48, value)
        get(): String? = get(48) as String?

    open var holiday_08: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    open var companyHoliday_08: String?
        set(value): Unit = set(50, value)
        get(): String? = get(50) as String?

    open var bankHoliday_08: String?
        set(value): Unit = set(51, value)
        get(): String? = get(51) as String?

    open var cumulativeAnnualDays_08: Short?
        set(value): Unit = set(52, value)
        get(): Short? = get(52) as Short?

    open var weekday_09: String?
        set(value): Unit = set(53, value)
        get(): String? = get(53) as String?

    open var holiday_09: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var companyHoliday_09: String?
        set(value): Unit = set(55, value)
        get(): String? = get(55) as String?

    open var bankHoliday_09: String?
        set(value): Unit = set(56, value)
        get(): String? = get(56) as String?

    open var cumulativeAnnualDays_09: Short?
        set(value): Unit = set(57, value)
        get(): Short? = get(57) as Short?

    open var weekday_10: String?
        set(value): Unit = set(58, value)
        get(): String? = get(58) as String?

    open var holiday_10: String?
        set(value): Unit = set(59, value)
        get(): String? = get(59) as String?

    open var companyHoliday_10: String?
        set(value): Unit = set(60, value)
        get(): String? = get(60) as String?

    open var bankHoliday_10: String?
        set(value): Unit = set(61, value)
        get(): String? = get(61) as String?

    open var cumulativeAnnualDays_10: Short?
        set(value): Unit = set(62, value)
        get(): Short? = get(62) as Short?

    open var weekday_11: String?
        set(value): Unit = set(63, value)
        get(): String? = get(63) as String?

    open var holiday_11: String?
        set(value): Unit = set(64, value)
        get(): String? = get(64) as String?

    open var companyHoliday_11: String?
        set(value): Unit = set(65, value)
        get(): String? = get(65) as String?

    open var bankHoliday_11: String?
        set(value): Unit = set(66, value)
        get(): String? = get(66) as String?

    open var cumulativeAnnualDays_11: Short?
        set(value): Unit = set(67, value)
        get(): Short? = get(67) as Short?

    open var weekday_12: String?
        set(value): Unit = set(68, value)
        get(): String? = get(68) as String?

    open var holiday_12: String?
        set(value): Unit = set(69, value)
        get(): String? = get(69) as String?

    open var companyHoliday_12: String?
        set(value): Unit = set(70, value)
        get(): String? = get(70) as String?

    open var bankHoliday_12: String?
        set(value): Unit = set(71, value)
        get(): String? = get(71) as String?

    open var cumulativeAnnualDays_12: Short?
        set(value): Unit = set(72, value)
        get(): Short? = get(72) as Short?

    open var weekday_13: String?
        set(value): Unit = set(73, value)
        get(): String? = get(73) as String?

    open var holiday_13: String?
        set(value): Unit = set(74, value)
        get(): String? = get(74) as String?

    open var companyHoliday_13: String?
        set(value): Unit = set(75, value)
        get(): String? = get(75) as String?

    open var bankHoliday_13: String?
        set(value): Unit = set(76, value)
        get(): String? = get(76) as String?

    open var cumulativeAnnualDays_13: Short?
        set(value): Unit = set(77, value)
        get(): Short? = get(77) as Short?

    open var weekday_14: String?
        set(value): Unit = set(78, value)
        get(): String? = get(78) as String?

    open var holiday_14: String?
        set(value): Unit = set(79, value)
        get(): String? = get(79) as String?

    open var companyHoliday_14: String?
        set(value): Unit = set(80, value)
        get(): String? = get(80) as String?

    open var bankHoliday_14: String?
        set(value): Unit = set(81, value)
        get(): String? = get(81) as String?

    open var cumulativeAnnualDays_14: Short?
        set(value): Unit = set(82, value)
        get(): Short? = get(82) as Short?

    open var weekday_15: String?
        set(value): Unit = set(83, value)
        get(): String? = get(83) as String?

    open var holiday_15: String?
        set(value): Unit = set(84, value)
        get(): String? = get(84) as String?

    open var companyHoliday_15: String?
        set(value): Unit = set(85, value)
        get(): String? = get(85) as String?

    open var bankHoliday_15: String?
        set(value): Unit = set(86, value)
        get(): String? = get(86) as String?

    open var cumulativeAnnualDays_15: Short?
        set(value): Unit = set(87, value)
        get(): Short? = get(87) as Short?

    open var weekday_16: String?
        set(value): Unit = set(88, value)
        get(): String? = get(88) as String?

    open var holiday_16: String?
        set(value): Unit = set(89, value)
        get(): String? = get(89) as String?

    open var companyHoliday_16: String?
        set(value): Unit = set(90, value)
        get(): String? = get(90) as String?

    open var bankHoliday_16: String?
        set(value): Unit = set(91, value)
        get(): String? = get(91) as String?

    open var cumulativeAnnualDays_16: Short?
        set(value): Unit = set(92, value)
        get(): Short? = get(92) as Short?

    open var weekday_17: String?
        set(value): Unit = set(93, value)
        get(): String? = get(93) as String?

    open var holiday_17: String?
        set(value): Unit = set(94, value)
        get(): String? = get(94) as String?

    open var companyHoliday_17: String?
        set(value): Unit = set(95, value)
        get(): String? = get(95) as String?

    open var bankHoliday_17: String?
        set(value): Unit = set(96, value)
        get(): String? = get(96) as String?

    open var cumulativeAnnualDays_17: Short?
        set(value): Unit = set(97, value)
        get(): Short? = get(97) as Short?

    open var weekday_18: String?
        set(value): Unit = set(98, value)
        get(): String? = get(98) as String?

    open var holiday_18: String?
        set(value): Unit = set(99, value)
        get(): String? = get(99) as String?

    open var companyHoliday_18: String?
        set(value): Unit = set(100, value)
        get(): String? = get(100) as String?

    open var bankHoliday_18: String?
        set(value): Unit = set(101, value)
        get(): String? = get(101) as String?

    open var cumulativeAnnualDays_18: Short?
        set(value): Unit = set(102, value)
        get(): Short? = get(102) as Short?

    open var weekday_19: String?
        set(value): Unit = set(103, value)
        get(): String? = get(103) as String?

    open var holiday_19: String?
        set(value): Unit = set(104, value)
        get(): String? = get(104) as String?

    open var companyHoliday_19: String?
        set(value): Unit = set(105, value)
        get(): String? = get(105) as String?

    open var bankHoliday_19: String?
        set(value): Unit = set(106, value)
        get(): String? = get(106) as String?

    open var cumulativeAnnualDays_19: Short?
        set(value): Unit = set(107, value)
        get(): Short? = get(107) as Short?

    open var weekday_20: String?
        set(value): Unit = set(108, value)
        get(): String? = get(108) as String?

    open var holiday_20: String?
        set(value): Unit = set(109, value)
        get(): String? = get(109) as String?

    open var companyHoliday_20: String?
        set(value): Unit = set(110, value)
        get(): String? = get(110) as String?

    open var bankHoliday_20: String?
        set(value): Unit = set(111, value)
        get(): String? = get(111) as String?

    open var cumulativeAnnualDays_20: Short?
        set(value): Unit = set(112, value)
        get(): Short? = get(112) as Short?

    open var weekday_21: String?
        set(value): Unit = set(113, value)
        get(): String? = get(113) as String?

    open var holiday_21: String?
        set(value): Unit = set(114, value)
        get(): String? = get(114) as String?

    open var companyHoliday_21: String?
        set(value): Unit = set(115, value)
        get(): String? = get(115) as String?

    open var bankHoliday_21: String?
        set(value): Unit = set(116, value)
        get(): String? = get(116) as String?

    open var cumulativeAnnualDays_21: Short?
        set(value): Unit = set(117, value)
        get(): Short? = get(117) as Short?

    open var weekday_22: String?
        set(value): Unit = set(118, value)
        get(): String? = get(118) as String?

    open var holiday_22: String?
        set(value): Unit = set(119, value)
        get(): String? = get(119) as String?

    open var companyHoliday_22: String?
        set(value): Unit = set(120, value)
        get(): String? = get(120) as String?

    open var bankHoliday_22: String?
        set(value): Unit = set(121, value)
        get(): String? = get(121) as String?

    open var cumulativeAnnualDays_22: Short?
        set(value): Unit = set(122, value)
        get(): Short? = get(122) as Short?

    open var weekday_23: String?
        set(value): Unit = set(123, value)
        get(): String? = get(123) as String?

    open var holiday_23: String?
        set(value): Unit = set(124, value)
        get(): String? = get(124) as String?

    open var companyHoliday_23: String?
        set(value): Unit = set(125, value)
        get(): String? = get(125) as String?

    open var bankHoliday_23: String?
        set(value): Unit = set(126, value)
        get(): String? = get(126) as String?

    open var cumulativeAnnualDays_23: Short?
        set(value): Unit = set(127, value)
        get(): Short? = get(127) as Short?

    open var weekday_24: String?
        set(value): Unit = set(128, value)
        get(): String? = get(128) as String?

    open var holiday_24: String?
        set(value): Unit = set(129, value)
        get(): String? = get(129) as String?

    open var companyHoliday_24: String?
        set(value): Unit = set(130, value)
        get(): String? = get(130) as String?

    open var bankHoliday_24: String?
        set(value): Unit = set(131, value)
        get(): String? = get(131) as String?

    open var cumulativeAnnualDays_24: Short?
        set(value): Unit = set(132, value)
        get(): Short? = get(132) as Short?

    open var weekday_25: String?
        set(value): Unit = set(133, value)
        get(): String? = get(133) as String?

    open var holiday_25: String?
        set(value): Unit = set(134, value)
        get(): String? = get(134) as String?

    open var companyHoliday_25: String?
        set(value): Unit = set(135, value)
        get(): String? = get(135) as String?

    open var bankHoliday_25: String?
        set(value): Unit = set(136, value)
        get(): String? = get(136) as String?

    open var cumulativeAnnualDays_25: Short?
        set(value): Unit = set(137, value)
        get(): Short? = get(137) as Short?

    open var weekday_26: String?
        set(value): Unit = set(138, value)
        get(): String? = get(138) as String?

    open var holiday_26: String?
        set(value): Unit = set(139, value)
        get(): String? = get(139) as String?

    open var companyHoliday_26: String?
        set(value): Unit = set(140, value)
        get(): String? = get(140) as String?

    open var bankHoliday_26: String?
        set(value): Unit = set(141, value)
        get(): String? = get(141) as String?

    open var cumulativeAnnualDays_26: Short?
        set(value): Unit = set(142, value)
        get(): Short? = get(142) as Short?

    open var weekday_27: String?
        set(value): Unit = set(143, value)
        get(): String? = get(143) as String?

    open var holiday_27: String?
        set(value): Unit = set(144, value)
        get(): String? = get(144) as String?

    open var companyHoliday_27: String?
        set(value): Unit = set(145, value)
        get(): String? = get(145) as String?

    open var bankHoliday_27: String?
        set(value): Unit = set(146, value)
        get(): String? = get(146) as String?

    open var cumulativeAnnualDays_27: Short?
        set(value): Unit = set(147, value)
        get(): Short? = get(147) as Short?

    open var weekday_28: String?
        set(value): Unit = set(148, value)
        get(): String? = get(148) as String?

    open var holiday_28: String?
        set(value): Unit = set(149, value)
        get(): String? = get(149) as String?

    open var companyHoliday_28: String?
        set(value): Unit = set(150, value)
        get(): String? = get(150) as String?

    open var bankHoliday_28: String?
        set(value): Unit = set(151, value)
        get(): String? = get(151) as String?

    open var cumulativeAnnualDays_28: Short?
        set(value): Unit = set(152, value)
        get(): Short? = get(152) as Short?

    open var weekday_29: String?
        set(value): Unit = set(153, value)
        get(): String? = get(153) as String?

    open var holiday_29: String?
        set(value): Unit = set(154, value)
        get(): String? = get(154) as String?

    open var companyHoliday_29: String?
        set(value): Unit = set(155, value)
        get(): String? = get(155) as String?

    open var bankHoliday_29: String?
        set(value): Unit = set(156, value)
        get(): String? = get(156) as String?

    open var cumulativeAnnualDays_29: Short?
        set(value): Unit = set(157, value)
        get(): Short? = get(157) as Short?

    open var weekday_30: String?
        set(value): Unit = set(158, value)
        get(): String? = get(158) as String?

    open var holiday_30: String?
        set(value): Unit = set(159, value)
        get(): String? = get(159) as String?

    open var companyHoliday_30: String?
        set(value): Unit = set(160, value)
        get(): String? = get(160) as String?

    open var bankHoliday_30: String?
        set(value): Unit = set(161, value)
        get(): String? = get(161) as String?

    open var cumulativeAnnualDays_30: Short?
        set(value): Unit = set(162, value)
        get(): Short? = get(162) as Short?

    open var weekday_31: String?
        set(value): Unit = set(163, value)
        get(): String? = get(163) as String?

    open var holiday_31: String?
        set(value): Unit = set(164, value)
        get(): String? = get(164) as String?

    open var companyHoliday_31: String?
        set(value): Unit = set(165, value)
        get(): String? = get(165) as String?

    open var bankHoliday_31: String?
        set(value): Unit = set(166, value)
        get(): String? = get(166) as String?

    open var cumulativeAnnualDays_31: Short?
        set(value): Unit = set(167, value)
        get(): Short? = get(167) as Short?

    open var salesCompanyHoliday_01: String?
        set(value): Unit = set(168, value)
        get(): String? = get(168) as String?

    open var salesCompanyHoliday_02: String?
        set(value): Unit = set(169, value)
        get(): String? = get(169) as String?

    open var salesCompanyHoliday_03: String?
        set(value): Unit = set(170, value)
        get(): String? = get(170) as String?

    open var salesCompanyHoliday_04: String?
        set(value): Unit = set(171, value)
        get(): String? = get(171) as String?

    open var salesCompanyHoliday_05: String?
        set(value): Unit = set(172, value)
        get(): String? = get(172) as String?

    open var salesCompanyHoliday_06: String?
        set(value): Unit = set(173, value)
        get(): String? = get(173) as String?

    open var salesCompanyHoliday_07: String?
        set(value): Unit = set(174, value)
        get(): String? = get(174) as String?

    open var salesCompanyHoliday_08: String?
        set(value): Unit = set(175, value)
        get(): String? = get(175) as String?

    open var salesCompanyHoliday_09: String?
        set(value): Unit = set(176, value)
        get(): String? = get(176) as String?

    open var salesCompanyHoliday_10: String?
        set(value): Unit = set(177, value)
        get(): String? = get(177) as String?

    open var salesCompanyHoliday_11: String?
        set(value): Unit = set(178, value)
        get(): String? = get(178) as String?

    open var salesCompanyHoliday_12: String?
        set(value): Unit = set(179, value)
        get(): String? = get(179) as String?

    open var salesCompanyHoliday_13: String?
        set(value): Unit = set(180, value)
        get(): String? = get(180) as String?

    open var salesCompanyHoliday_14: String?
        set(value): Unit = set(181, value)
        get(): String? = get(181) as String?

    open var salesCompanyHoliday_15: String?
        set(value): Unit = set(182, value)
        get(): String? = get(182) as String?

    open var salesCompanyHoliday_16: String?
        set(value): Unit = set(183, value)
        get(): String? = get(183) as String?

    open var salesCompanyHoliday_17: String?
        set(value): Unit = set(184, value)
        get(): String? = get(184) as String?

    open var salesCompanyHoliday_18: String?
        set(value): Unit = set(185, value)
        get(): String? = get(185) as String?

    open var salesCompanyHoliday_19: String?
        set(value): Unit = set(186, value)
        get(): String? = get(186) as String?

    open var salesCompanyHoliday_20: String?
        set(value): Unit = set(187, value)
        get(): String? = get(187) as String?

    open var salesCompanyHoliday_21: String?
        set(value): Unit = set(188, value)
        get(): String? = get(188) as String?

    open var salesCompanyHoliday_22: String?
        set(value): Unit = set(189, value)
        get(): String? = get(189) as String?

    open var salesCompanyHoliday_23: String?
        set(value): Unit = set(190, value)
        get(): String? = get(190) as String?

    open var salesCompanyHoliday_24: String?
        set(value): Unit = set(191, value)
        get(): String? = get(191) as String?

    open var salesCompanyHoliday_25: String?
        set(value): Unit = set(192, value)
        get(): String? = get(192) as String?

    open var salesCompanyHoliday_26: String?
        set(value): Unit = set(193, value)
        get(): String? = get(193) as String?

    open var salesCompanyHoliday_27: String?
        set(value): Unit = set(194, value)
        get(): String? = get(194) as String?

    open var salesCompanyHoliday_28: String?
        set(value): Unit = set(195, value)
        get(): String? = get(195) as String?

    open var salesCompanyHoliday_29: String?
        set(value): Unit = set(196, value)
        get(): String? = get(196) as String?

    open var salesCompanyHoliday_30: String?
        set(value): Unit = set(197, value)
        get(): String? = get(197) as String?

    open var salesCompanyHoliday_31: String?
        set(value): Unit = set(198, value)
        get(): String? = get(198) as String?

    open var xxxx_01: String?
        set(value): Unit = set(199, value)
        get(): String? = get(199) as String?

    open var xxxx_02: String?
        set(value): Unit = set(200, value)
        get(): String? = get(200) as String?

    open var xxxx_03: String?
        set(value): Unit = set(201, value)
        get(): String? = get(201) as String?

    open var xxxx_04: String?
        set(value): Unit = set(202, value)
        get(): String? = get(202) as String?

    open var xxxx_05: String?
        set(value): Unit = set(203, value)
        get(): String? = get(203) as String?

    open var xxxx_06: String?
        set(value): Unit = set(204, value)
        get(): String? = get(204) as String?

    open var xxxx_07: String?
        set(value): Unit = set(205, value)
        get(): String? = get(205) as String?

    open var xxxx_08: String?
        set(value): Unit = set(206, value)
        get(): String? = get(206) as String?

    open var xxxx_09: String?
        set(value): Unit = set(207, value)
        get(): String? = get(207) as String?

    open var xxxx_10: String?
        set(value): Unit = set(208, value)
        get(): String? = get(208) as String?

    open var xxxx_11: String?
        set(value): Unit = set(209, value)
        get(): String? = get(209) as String?

    open var xxxx_12: String?
        set(value): Unit = set(210, value)
        get(): String? = get(210) as String?

    open var xxxx_13: String?
        set(value): Unit = set(211, value)
        get(): String? = get(211) as String?

    open var xxxx_14: String?
        set(value): Unit = set(212, value)
        get(): String? = get(212) as String?

    open var xxxx_15: String?
        set(value): Unit = set(213, value)
        get(): String? = get(213) as String?

    open var xxxx_16: String?
        set(value): Unit = set(214, value)
        get(): String? = get(214) as String?

    open var xxxx_17: String?
        set(value): Unit = set(215, value)
        get(): String? = get(215) as String?

    open var xxxx_18: String?
        set(value): Unit = set(216, value)
        get(): String? = get(216) as String?

    open var xxxx_19: String?
        set(value): Unit = set(217, value)
        get(): String? = get(217) as String?

    open var xxxx_20: String?
        set(value): Unit = set(218, value)
        get(): String? = get(218) as String?

    open var xxxx_21: String?
        set(value): Unit = set(219, value)
        get(): String? = get(219) as String?

    open var xxxx_22: String?
        set(value): Unit = set(220, value)
        get(): String? = get(220) as String?

    open var xxxx_23: String?
        set(value): Unit = set(221, value)
        get(): String? = get(221) as String?

    open var xxxx_24: String?
        set(value): Unit = set(222, value)
        get(): String? = get(222) as String?

    open var xxxx_25: String?
        set(value): Unit = set(223, value)
        get(): String? = get(223) as String?

    open var xxxx_26: String?
        set(value): Unit = set(224, value)
        get(): String? = get(224) as String?

    open var xxxx_27: String?
        set(value): Unit = set(225, value)
        get(): String? = get(225) as String?

    open var xxxx_28: String?
        set(value): Unit = set(226, value)
        get(): String? = get(226) as String?

    open var xxxx_29: String?
        set(value): Unit = set(227, value)
        get(): String? = get(227) as String?

    open var xxxx_30: String?
        set(value): Unit = set(228, value)
        get(): String? = get(228) as String?

    open var xxxx_31: String?
        set(value): Unit = set(229, value)
        get(): String? = get(229) as String?

    open var xxxxAlt_01: String?
        set(value): Unit = set(230, value)
        get(): String? = get(230) as String?

    open var xxxxAlt_02: String?
        set(value): Unit = set(231, value)
        get(): String? = get(231) as String?

    open var xxxxAlt_03: String?
        set(value): Unit = set(232, value)
        get(): String? = get(232) as String?

    open var xxxxAlt_04: String?
        set(value): Unit = set(233, value)
        get(): String? = get(233) as String?

    open var xxxxAlt_05: String?
        set(value): Unit = set(234, value)
        get(): String? = get(234) as String?

    open var xxxxAlt_06: String?
        set(value): Unit = set(235, value)
        get(): String? = get(235) as String?

    open var xxxxAlt_07: String?
        set(value): Unit = set(236, value)
        get(): String? = get(236) as String?

    open var xxxxAlt_08: String?
        set(value): Unit = set(237, value)
        get(): String? = get(237) as String?

    open var xxxxAlt_09: String?
        set(value): Unit = set(238, value)
        get(): String? = get(238) as String?

    open var xxxxAlt_10: String?
        set(value): Unit = set(239, value)
        get(): String? = get(239) as String?

    open var xxxxAlt_11: String?
        set(value): Unit = set(240, value)
        get(): String? = get(240) as String?

    open var xxxxAlt_12: String?
        set(value): Unit = set(241, value)
        get(): String? = get(241) as String?

    open var xxxxAlt_13: String?
        set(value): Unit = set(242, value)
        get(): String? = get(242) as String?

    open var xxxxAlt_14: String?
        set(value): Unit = set(243, value)
        get(): String? = get(243) as String?

    open var xxxxAlt_15: String?
        set(value): Unit = set(244, value)
        get(): String? = get(244) as String?

    open var xxxxAlt_16: String?
        set(value): Unit = set(245, value)
        get(): String? = get(245) as String?

    open var xxxxAlt_17: String?
        set(value): Unit = set(246, value)
        get(): String? = get(246) as String?

    open var xxxxAlt_18: String?
        set(value): Unit = set(247, value)
        get(): String? = get(247) as String?

    open var xxxxAlt_19: String?
        set(value): Unit = set(248, value)
        get(): String? = get(248) as String?

    open var xxxxAlt_20: String?
        set(value): Unit = set(249, value)
        get(): String? = get(249) as String?

    open var xxxxAlt_21: String?
        set(value): Unit = set(250, value)
        get(): String? = get(250) as String?

    open var xxxxAlt_22: String?
        set(value): Unit = set(251, value)
        get(): String? = get(251) as String?

    open var xxxxAlt_23: String?
        set(value): Unit = set(252, value)
        get(): String? = get(252) as String?

    open var xxxxAlt_24: String?
        set(value): Unit = set(253, value)
        get(): String? = get(253) as String?

    open var xxxxAlt_25: String?
        set(value): Unit = set(254, value)
        get(): String? = get(254) as String?

    open var xxxxAlt_26: String?
        set(value): Unit = set(255, value)
        get(): String? = get(255) as String?

    open var xxxxAlt_27: String?
        set(value): Unit = set(256, value)
        get(): String? = get(256) as String?

    open var xxxxAlt_28: String?
        set(value): Unit = set(257, value)
        get(): String? = get(257) as String?

    open var xxxxAlt_29: String?
        set(value): Unit = set(258, value)
        get(): String? = get(258) as String?

    open var xxxxAlt_30: String?
        set(value): Unit = set(259, value)
        get(): String? = get(259) as String?

    open var xxxxAlt_31: String?
        set(value): Unit = set(260, value)
        get(): String? = get(260) as String?

    open var xxxxAlt2_01: String?
        set(value): Unit = set(261, value)
        get(): String? = get(261) as String?

    open var xxxxAlt2_02: String?
        set(value): Unit = set(262, value)
        get(): String? = get(262) as String?

    open var xxxxAlt2_03: String?
        set(value): Unit = set(263, value)
        get(): String? = get(263) as String?

    open var xxxxAlt2_04: String?
        set(value): Unit = set(264, value)
        get(): String? = get(264) as String?

    open var xxxxAlt2_05: String?
        set(value): Unit = set(265, value)
        get(): String? = get(265) as String?

    open var xxxxAlt2_06: String?
        set(value): Unit = set(266, value)
        get(): String? = get(266) as String?

    open var xxxxAlt2_07: String?
        set(value): Unit = set(267, value)
        get(): String? = get(267) as String?

    open var xxxxAlt2_08: String?
        set(value): Unit = set(268, value)
        get(): String? = get(268) as String?

    open var xxxxAlt2_09: String?
        set(value): Unit = set(269, value)
        get(): String? = get(269) as String?

    open var xxxxAlt2_10: String?
        set(value): Unit = set(270, value)
        get(): String? = get(270) as String?

    open var xxxxAlt2_11: String?
        set(value): Unit = set(271, value)
        get(): String? = get(271) as String?

    open var xxxxAlt2_12: String?
        set(value): Unit = set(272, value)
        get(): String? = get(272) as String?

    open var xxxxAlt2_13: String?
        set(value): Unit = set(273, value)
        get(): String? = get(273) as String?

    open var xxxxAlt2_14: String?
        set(value): Unit = set(274, value)
        get(): String? = get(274) as String?

    open var xxxxAlt2_15: String?
        set(value): Unit = set(275, value)
        get(): String? = get(275) as String?

    open var xxxxAlt2_16: String?
        set(value): Unit = set(276, value)
        get(): String? = get(276) as String?

    open var xxxxAlt2_17: String?
        set(value): Unit = set(277, value)
        get(): String? = get(277) as String?

    open var xxxxAlt2_18: String?
        set(value): Unit = set(278, value)
        get(): String? = get(278) as String?

    open var xxxxAlt2_19: String?
        set(value): Unit = set(279, value)
        get(): String? = get(279) as String?

    open var xxxxAlt2_20: String?
        set(value): Unit = set(280, value)
        get(): String? = get(280) as String?

    open var xxxxAlt2_21: String?
        set(value): Unit = set(281, value)
        get(): String? = get(281) as String?

    open var xxxxAlt2_22: String?
        set(value): Unit = set(282, value)
        get(): String? = get(282) as String?

    open var xxxxAlt2_23: String?
        set(value): Unit = set(283, value)
        get(): String? = get(283) as String?

    open var xxxxAlt2_24: String?
        set(value): Unit = set(284, value)
        get(): String? = get(284) as String?

    open var xxxxAlt2_25: String?
        set(value): Unit = set(285, value)
        get(): String? = get(285) as String?

    open var xxxxAlt2_26: String?
        set(value): Unit = set(286, value)
        get(): String? = get(286) as String?

    open var xxxxAlt2_27: String?
        set(value): Unit = set(287, value)
        get(): String? = get(287) as String?

    open var xxxxAlt2_28: String?
        set(value): Unit = set(288, value)
        get(): String? = get(288) as String?

    open var xxxxAlt2_29: String?
        set(value): Unit = set(289, value)
        get(): String? = get(289) as String?

    open var xxxxAlt2_30: String?
        set(value): Unit = set(290, value)
        get(): String? = get(290) as String?

    open var xxxxAlt2_31: String?
        set(value): Unit = set(291, value)
        get(): String? = get(291) as String?

    /**
     * Create a detached, initialised CalendarMasterRecord
     */
    constructor(value: CalendarMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.reflectionDate = value.reflectionDate
            this.deleteFlag = value.deleteFlag
            this.yearMonth = value.yearMonth
            this.daysInMonth = value.daysInMonth
            this.companyWorkDays = value.companyWorkDays
            this.cumulativeDaysPrevMonthEnd = value.cumulativeDaysPrevMonthEnd
            this.weekdayPrevMonthEnd = value.weekdayPrevMonthEnd
            this.weekday_01 = value.weekday_01
            this.holiday_01 = value.holiday_01
            this.companyHoliday_01 = value.companyHoliday_01
            this.bankHoliday_01 = value.bankHoliday_01
            this.cumulativeAnnualDays_01 = value.cumulativeAnnualDays_01
            this.weekday_02 = value.weekday_02
            this.holiday_02 = value.holiday_02
            this.companyHoliday_02 = value.companyHoliday_02
            this.bankHoliday_02 = value.bankHoliday_02
            this.cumulativeAnnualDays_02 = value.cumulativeAnnualDays_02
            this.weekday_03 = value.weekday_03
            this.holiday_03 = value.holiday_03
            this.companyHoliday_03 = value.companyHoliday_03
            this.bankHoliday_03 = value.bankHoliday_03
            this.cumulativeAnnualDays_03 = value.cumulativeAnnualDays_03
            this.weekday_04 = value.weekday_04
            this.holiday_04 = value.holiday_04
            this.companyHoliday_04 = value.companyHoliday_04
            this.bankHoliday_04 = value.bankHoliday_04
            this.cumulativeAnnualDays_04 = value.cumulativeAnnualDays_04
            this.weekday_05 = value.weekday_05
            this.holiday_05 = value.holiday_05
            this.companyHoliday_05 = value.companyHoliday_05
            this.bankHoliday_05 = value.bankHoliday_05
            this.cumulativeAnnualDays_05 = value.cumulativeAnnualDays_05
            this.weekday_06 = value.weekday_06
            this.holiday_06 = value.holiday_06
            this.companyHoliday_06 = value.companyHoliday_06
            this.bankHoliday_06 = value.bankHoliday_06
            this.cumulativeAnnualDays_06 = value.cumulativeAnnualDays_06
            this.weekday_07 = value.weekday_07
            this.holiday_07 = value.holiday_07
            this.companyHoliday_07 = value.companyHoliday_07
            this.bankHoliday_07 = value.bankHoliday_07
            this.cumulativeAnnualDays_07 = value.cumulativeAnnualDays_07
            this.weekday_08 = value.weekday_08
            this.holiday_08 = value.holiday_08
            this.companyHoliday_08 = value.companyHoliday_08
            this.bankHoliday_08 = value.bankHoliday_08
            this.cumulativeAnnualDays_08 = value.cumulativeAnnualDays_08
            this.weekday_09 = value.weekday_09
            this.holiday_09 = value.holiday_09
            this.companyHoliday_09 = value.companyHoliday_09
            this.bankHoliday_09 = value.bankHoliday_09
            this.cumulativeAnnualDays_09 = value.cumulativeAnnualDays_09
            this.weekday_10 = value.weekday_10
            this.holiday_10 = value.holiday_10
            this.companyHoliday_10 = value.companyHoliday_10
            this.bankHoliday_10 = value.bankHoliday_10
            this.cumulativeAnnualDays_10 = value.cumulativeAnnualDays_10
            this.weekday_11 = value.weekday_11
            this.holiday_11 = value.holiday_11
            this.companyHoliday_11 = value.companyHoliday_11
            this.bankHoliday_11 = value.bankHoliday_11
            this.cumulativeAnnualDays_11 = value.cumulativeAnnualDays_11
            this.weekday_12 = value.weekday_12
            this.holiday_12 = value.holiday_12
            this.companyHoliday_12 = value.companyHoliday_12
            this.bankHoliday_12 = value.bankHoliday_12
            this.cumulativeAnnualDays_12 = value.cumulativeAnnualDays_12
            this.weekday_13 = value.weekday_13
            this.holiday_13 = value.holiday_13
            this.companyHoliday_13 = value.companyHoliday_13
            this.bankHoliday_13 = value.bankHoliday_13
            this.cumulativeAnnualDays_13 = value.cumulativeAnnualDays_13
            this.weekday_14 = value.weekday_14
            this.holiday_14 = value.holiday_14
            this.companyHoliday_14 = value.companyHoliday_14
            this.bankHoliday_14 = value.bankHoliday_14
            this.cumulativeAnnualDays_14 = value.cumulativeAnnualDays_14
            this.weekday_15 = value.weekday_15
            this.holiday_15 = value.holiday_15
            this.companyHoliday_15 = value.companyHoliday_15
            this.bankHoliday_15 = value.bankHoliday_15
            this.cumulativeAnnualDays_15 = value.cumulativeAnnualDays_15
            this.weekday_16 = value.weekday_16
            this.holiday_16 = value.holiday_16
            this.companyHoliday_16 = value.companyHoliday_16
            this.bankHoliday_16 = value.bankHoliday_16
            this.cumulativeAnnualDays_16 = value.cumulativeAnnualDays_16
            this.weekday_17 = value.weekday_17
            this.holiday_17 = value.holiday_17
            this.companyHoliday_17 = value.companyHoliday_17
            this.bankHoliday_17 = value.bankHoliday_17
            this.cumulativeAnnualDays_17 = value.cumulativeAnnualDays_17
            this.weekday_18 = value.weekday_18
            this.holiday_18 = value.holiday_18
            this.companyHoliday_18 = value.companyHoliday_18
            this.bankHoliday_18 = value.bankHoliday_18
            this.cumulativeAnnualDays_18 = value.cumulativeAnnualDays_18
            this.weekday_19 = value.weekday_19
            this.holiday_19 = value.holiday_19
            this.companyHoliday_19 = value.companyHoliday_19
            this.bankHoliday_19 = value.bankHoliday_19
            this.cumulativeAnnualDays_19 = value.cumulativeAnnualDays_19
            this.weekday_20 = value.weekday_20
            this.holiday_20 = value.holiday_20
            this.companyHoliday_20 = value.companyHoliday_20
            this.bankHoliday_20 = value.bankHoliday_20
            this.cumulativeAnnualDays_20 = value.cumulativeAnnualDays_20
            this.weekday_21 = value.weekday_21
            this.holiday_21 = value.holiday_21
            this.companyHoliday_21 = value.companyHoliday_21
            this.bankHoliday_21 = value.bankHoliday_21
            this.cumulativeAnnualDays_21 = value.cumulativeAnnualDays_21
            this.weekday_22 = value.weekday_22
            this.holiday_22 = value.holiday_22
            this.companyHoliday_22 = value.companyHoliday_22
            this.bankHoliday_22 = value.bankHoliday_22
            this.cumulativeAnnualDays_22 = value.cumulativeAnnualDays_22
            this.weekday_23 = value.weekday_23
            this.holiday_23 = value.holiday_23
            this.companyHoliday_23 = value.companyHoliday_23
            this.bankHoliday_23 = value.bankHoliday_23
            this.cumulativeAnnualDays_23 = value.cumulativeAnnualDays_23
            this.weekday_24 = value.weekday_24
            this.holiday_24 = value.holiday_24
            this.companyHoliday_24 = value.companyHoliday_24
            this.bankHoliday_24 = value.bankHoliday_24
            this.cumulativeAnnualDays_24 = value.cumulativeAnnualDays_24
            this.weekday_25 = value.weekday_25
            this.holiday_25 = value.holiday_25
            this.companyHoliday_25 = value.companyHoliday_25
            this.bankHoliday_25 = value.bankHoliday_25
            this.cumulativeAnnualDays_25 = value.cumulativeAnnualDays_25
            this.weekday_26 = value.weekday_26
            this.holiday_26 = value.holiday_26
            this.companyHoliday_26 = value.companyHoliday_26
            this.bankHoliday_26 = value.bankHoliday_26
            this.cumulativeAnnualDays_26 = value.cumulativeAnnualDays_26
            this.weekday_27 = value.weekday_27
            this.holiday_27 = value.holiday_27
            this.companyHoliday_27 = value.companyHoliday_27
            this.bankHoliday_27 = value.bankHoliday_27
            this.cumulativeAnnualDays_27 = value.cumulativeAnnualDays_27
            this.weekday_28 = value.weekday_28
            this.holiday_28 = value.holiday_28
            this.companyHoliday_28 = value.companyHoliday_28
            this.bankHoliday_28 = value.bankHoliday_28
            this.cumulativeAnnualDays_28 = value.cumulativeAnnualDays_28
            this.weekday_29 = value.weekday_29
            this.holiday_29 = value.holiday_29
            this.companyHoliday_29 = value.companyHoliday_29
            this.bankHoliday_29 = value.bankHoliday_29
            this.cumulativeAnnualDays_29 = value.cumulativeAnnualDays_29
            this.weekday_30 = value.weekday_30
            this.holiday_30 = value.holiday_30
            this.companyHoliday_30 = value.companyHoliday_30
            this.bankHoliday_30 = value.bankHoliday_30
            this.cumulativeAnnualDays_30 = value.cumulativeAnnualDays_30
            this.weekday_31 = value.weekday_31
            this.holiday_31 = value.holiday_31
            this.companyHoliday_31 = value.companyHoliday_31
            this.bankHoliday_31 = value.bankHoliday_31
            this.cumulativeAnnualDays_31 = value.cumulativeAnnualDays_31
            this.salesCompanyHoliday_01 = value.salesCompanyHoliday_01
            this.salesCompanyHoliday_02 = value.salesCompanyHoliday_02
            this.salesCompanyHoliday_03 = value.salesCompanyHoliday_03
            this.salesCompanyHoliday_04 = value.salesCompanyHoliday_04
            this.salesCompanyHoliday_05 = value.salesCompanyHoliday_05
            this.salesCompanyHoliday_06 = value.salesCompanyHoliday_06
            this.salesCompanyHoliday_07 = value.salesCompanyHoliday_07
            this.salesCompanyHoliday_08 = value.salesCompanyHoliday_08
            this.salesCompanyHoliday_09 = value.salesCompanyHoliday_09
            this.salesCompanyHoliday_10 = value.salesCompanyHoliday_10
            this.salesCompanyHoliday_11 = value.salesCompanyHoliday_11
            this.salesCompanyHoliday_12 = value.salesCompanyHoliday_12
            this.salesCompanyHoliday_13 = value.salesCompanyHoliday_13
            this.salesCompanyHoliday_14 = value.salesCompanyHoliday_14
            this.salesCompanyHoliday_15 = value.salesCompanyHoliday_15
            this.salesCompanyHoliday_16 = value.salesCompanyHoliday_16
            this.salesCompanyHoliday_17 = value.salesCompanyHoliday_17
            this.salesCompanyHoliday_18 = value.salesCompanyHoliday_18
            this.salesCompanyHoliday_19 = value.salesCompanyHoliday_19
            this.salesCompanyHoliday_20 = value.salesCompanyHoliday_20
            this.salesCompanyHoliday_21 = value.salesCompanyHoliday_21
            this.salesCompanyHoliday_22 = value.salesCompanyHoliday_22
            this.salesCompanyHoliday_23 = value.salesCompanyHoliday_23
            this.salesCompanyHoliday_24 = value.salesCompanyHoliday_24
            this.salesCompanyHoliday_25 = value.salesCompanyHoliday_25
            this.salesCompanyHoliday_26 = value.salesCompanyHoliday_26
            this.salesCompanyHoliday_27 = value.salesCompanyHoliday_27
            this.salesCompanyHoliday_28 = value.salesCompanyHoliday_28
            this.salesCompanyHoliday_29 = value.salesCompanyHoliday_29
            this.salesCompanyHoliday_30 = value.salesCompanyHoliday_30
            this.salesCompanyHoliday_31 = value.salesCompanyHoliday_31
            this.xxxx_01 = value.xxxx_01
            this.xxxx_02 = value.xxxx_02
            this.xxxx_03 = value.xxxx_03
            this.xxxx_04 = value.xxxx_04
            this.xxxx_05 = value.xxxx_05
            this.xxxx_06 = value.xxxx_06
            this.xxxx_07 = value.xxxx_07
            this.xxxx_08 = value.xxxx_08
            this.xxxx_09 = value.xxxx_09
            this.xxxx_10 = value.xxxx_10
            this.xxxx_11 = value.xxxx_11
            this.xxxx_12 = value.xxxx_12
            this.xxxx_13 = value.xxxx_13
            this.xxxx_14 = value.xxxx_14
            this.xxxx_15 = value.xxxx_15
            this.xxxx_16 = value.xxxx_16
            this.xxxx_17 = value.xxxx_17
            this.xxxx_18 = value.xxxx_18
            this.xxxx_19 = value.xxxx_19
            this.xxxx_20 = value.xxxx_20
            this.xxxx_21 = value.xxxx_21
            this.xxxx_22 = value.xxxx_22
            this.xxxx_23 = value.xxxx_23
            this.xxxx_24 = value.xxxx_24
            this.xxxx_25 = value.xxxx_25
            this.xxxx_26 = value.xxxx_26
            this.xxxx_27 = value.xxxx_27
            this.xxxx_28 = value.xxxx_28
            this.xxxx_29 = value.xxxx_29
            this.xxxx_30 = value.xxxx_30
            this.xxxx_31 = value.xxxx_31
            this.xxxxAlt_01 = value.xxxxAlt_01
            this.xxxxAlt_02 = value.xxxxAlt_02
            this.xxxxAlt_03 = value.xxxxAlt_03
            this.xxxxAlt_04 = value.xxxxAlt_04
            this.xxxxAlt_05 = value.xxxxAlt_05
            this.xxxxAlt_06 = value.xxxxAlt_06
            this.xxxxAlt_07 = value.xxxxAlt_07
            this.xxxxAlt_08 = value.xxxxAlt_08
            this.xxxxAlt_09 = value.xxxxAlt_09
            this.xxxxAlt_10 = value.xxxxAlt_10
            this.xxxxAlt_11 = value.xxxxAlt_11
            this.xxxxAlt_12 = value.xxxxAlt_12
            this.xxxxAlt_13 = value.xxxxAlt_13
            this.xxxxAlt_14 = value.xxxxAlt_14
            this.xxxxAlt_15 = value.xxxxAlt_15
            this.xxxxAlt_16 = value.xxxxAlt_16
            this.xxxxAlt_17 = value.xxxxAlt_17
            this.xxxxAlt_18 = value.xxxxAlt_18
            this.xxxxAlt_19 = value.xxxxAlt_19
            this.xxxxAlt_20 = value.xxxxAlt_20
            this.xxxxAlt_21 = value.xxxxAlt_21
            this.xxxxAlt_22 = value.xxxxAlt_22
            this.xxxxAlt_23 = value.xxxxAlt_23
            this.xxxxAlt_24 = value.xxxxAlt_24
            this.xxxxAlt_25 = value.xxxxAlt_25
            this.xxxxAlt_26 = value.xxxxAlt_26
            this.xxxxAlt_27 = value.xxxxAlt_27
            this.xxxxAlt_28 = value.xxxxAlt_28
            this.xxxxAlt_29 = value.xxxxAlt_29
            this.xxxxAlt_30 = value.xxxxAlt_30
            this.xxxxAlt_31 = value.xxxxAlt_31
            this.xxxxAlt2_01 = value.xxxxAlt2_01
            this.xxxxAlt2_02 = value.xxxxAlt2_02
            this.xxxxAlt2_03 = value.xxxxAlt2_03
            this.xxxxAlt2_04 = value.xxxxAlt2_04
            this.xxxxAlt2_05 = value.xxxxAlt2_05
            this.xxxxAlt2_06 = value.xxxxAlt2_06
            this.xxxxAlt2_07 = value.xxxxAlt2_07
            this.xxxxAlt2_08 = value.xxxxAlt2_08
            this.xxxxAlt2_09 = value.xxxxAlt2_09
            this.xxxxAlt2_10 = value.xxxxAlt2_10
            this.xxxxAlt2_11 = value.xxxxAlt2_11
            this.xxxxAlt2_12 = value.xxxxAlt2_12
            this.xxxxAlt2_13 = value.xxxxAlt2_13
            this.xxxxAlt2_14 = value.xxxxAlt2_14
            this.xxxxAlt2_15 = value.xxxxAlt2_15
            this.xxxxAlt2_16 = value.xxxxAlt2_16
            this.xxxxAlt2_17 = value.xxxxAlt2_17
            this.xxxxAlt2_18 = value.xxxxAlt2_18
            this.xxxxAlt2_19 = value.xxxxAlt2_19
            this.xxxxAlt2_20 = value.xxxxAlt2_20
            this.xxxxAlt2_21 = value.xxxxAlt2_21
            this.xxxxAlt2_22 = value.xxxxAlt2_22
            this.xxxxAlt2_23 = value.xxxxAlt2_23
            this.xxxxAlt2_24 = value.xxxxAlt2_24
            this.xxxxAlt2_25 = value.xxxxAlt2_25
            this.xxxxAlt2_26 = value.xxxxAlt2_26
            this.xxxxAlt2_27 = value.xxxxAlt2_27
            this.xxxxAlt2_28 = value.xxxxAlt2_28
            this.xxxxAlt2_29 = value.xxxxAlt2_29
            this.xxxxAlt2_30 = value.xxxxAlt2_30
            this.xxxxAlt2_31 = value.xxxxAlt2_31
            resetChangedOnNotNull()
        }
    }
}
