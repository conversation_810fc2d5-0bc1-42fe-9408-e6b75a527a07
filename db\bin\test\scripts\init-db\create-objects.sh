#!/bin/bash
#引数 $1:DB名, $2:スキーマ名 $3:ユーザ, $4:パスワード, $5:Dataテーブルスペース, $6:indexテーブルスペース

# shellcheck disable=SC2034
PGPASSWORD=${POSTGRES_PASSWORD} # psql接続用
DBNAME=$1
SCHEMA_NAME=$2
USER=$3
PASSWORD=$4
DATA_TABLESPACE=$5
IDX_TABLESPACE=$6

echo "/**************** ${DBNAME} ${SCHEMA_NAME}スキーマ作成 ****************/"
echo

psql -U "${POSTGRES_USER}" -h "${DBHOST}" -p "${DBPORT}" -d "${DBNAME}" -f recreateSchema.sql -v SCHEMA_NAME="${SCHEMA_NAME}" -v USER_NAME="${USER}"

echo "/**************** ${DBNAME} ${SCHEMA_NAME}スキーマのTable作成 ****************/"
echo

# shellcheck disable=SC2164
cd "${DDL_DIR}"
PGPASSWORD=${PASSWORD} # psql接続用
psql -U "${USER}" -h "${DBHOST}" -p "${DBPORT}" -d "${DBNAME}" -f createTables.sql -v TS_TBL="${DATA_TABLESPACE}" -v TS_IDX="${IDX_TABLESPACE}"

echo "/**************** ${DBNAME} ${SCHEMA_NAME}スキーマのIndex作成 ****************/"
echo

psql -U "${USER}" -h "${DBHOST}" -p "${DBPORT}" -d "${DBNAME}" -f createIndexes.sql -v TS_IDX="${IDX_TABLESPACE}"


echo "/**************** ${DBNAME} ${SCHEMA_NAME}スキーマのView作成 ****************/"
echo

psql -U "${USER}" -h "${DBHOST}" -p "${DBPORT}" -d "${DBNAME}" -f createViews.sql

echo "/**************** ${DBNAME} ${SCHEMA_NAME}スキーマのFunction作成 ****************/"
echo

psql -U "${USER}" -h "${DBHOST}" -p "${DBPORT}" -d "${DBNAME}" -f createFunctions.sql
