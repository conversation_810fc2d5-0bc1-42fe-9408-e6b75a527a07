/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 地域マスタ 既存システム物理名: JXH1MP
 */
@Suppress("UNCHECKED_CAST")
data class RegionMasterPojo(
    var groupCode: String,
    var departmentCode: String,
    var useStartDate: Int,
    var useFinishDate: Int? = null,
    var regionCode_1: String? = null,
    var regionName_1: String? = null,
    var regionOrder_1: String? = null,
    var regionCode_2: String? = null,
    var regionName_2: String? = null,
    var regionOrder_2: String? = null,
    var regionCode_3: String? = null,
    var regionName_3: String? = null,
    var regionOrder_3: String? = null,
    var regionCode_4: String? = null,
    var regionName_4: String? = null,
    var regionOrder_4: String? = null,
    var regionCode_5: String? = null,
    var regionName_5: String? = null,
    var regionOrder_5: String? = null,
    var salesDepartmentSuperior: String? = null,
    @Suppress("INAPPLICABLE_JVM_NAME")
    @set:JvmName("setIsDeleted")
    var isDeleted: String? = null,
    var createUser: String? = null,
    var createProgramId: String? = null,
    var createDate: Int? = null,
    var createTime: Int? = null,
    var updateUser: String? = null,
    var updateProgramId: String? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var deviceId: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: RegionMasterPojo = other as RegionMasterPojo
        if (this.groupCode != o.groupCode)
            return false
        if (this.departmentCode != o.departmentCode)
            return false
        if (this.useStartDate != o.useStartDate)
            return false
        if (this.useFinishDate == null) {
            if (o.useFinishDate != null)
                return false
        }
        else if (this.useFinishDate != o.useFinishDate)
            return false
        if (this.regionCode_1 == null) {
            if (o.regionCode_1 != null)
                return false
        }
        else if (this.regionCode_1 != o.regionCode_1)
            return false
        if (this.regionName_1 == null) {
            if (o.regionName_1 != null)
                return false
        }
        else if (this.regionName_1 != o.regionName_1)
            return false
        if (this.regionOrder_1 == null) {
            if (o.regionOrder_1 != null)
                return false
        }
        else if (this.regionOrder_1 != o.regionOrder_1)
            return false
        if (this.regionCode_2 == null) {
            if (o.regionCode_2 != null)
                return false
        }
        else if (this.regionCode_2 != o.regionCode_2)
            return false
        if (this.regionName_2 == null) {
            if (o.regionName_2 != null)
                return false
        }
        else if (this.regionName_2 != o.regionName_2)
            return false
        if (this.regionOrder_2 == null) {
            if (o.regionOrder_2 != null)
                return false
        }
        else if (this.regionOrder_2 != o.regionOrder_2)
            return false
        if (this.regionCode_3 == null) {
            if (o.regionCode_3 != null)
                return false
        }
        else if (this.regionCode_3 != o.regionCode_3)
            return false
        if (this.regionName_3 == null) {
            if (o.regionName_3 != null)
                return false
        }
        else if (this.regionName_3 != o.regionName_3)
            return false
        if (this.regionOrder_3 == null) {
            if (o.regionOrder_3 != null)
                return false
        }
        else if (this.regionOrder_3 != o.regionOrder_3)
            return false
        if (this.regionCode_4 == null) {
            if (o.regionCode_4 != null)
                return false
        }
        else if (this.regionCode_4 != o.regionCode_4)
            return false
        if (this.regionName_4 == null) {
            if (o.regionName_4 != null)
                return false
        }
        else if (this.regionName_4 != o.regionName_4)
            return false
        if (this.regionOrder_4 == null) {
            if (o.regionOrder_4 != null)
                return false
        }
        else if (this.regionOrder_4 != o.regionOrder_4)
            return false
        if (this.regionCode_5 == null) {
            if (o.regionCode_5 != null)
                return false
        }
        else if (this.regionCode_5 != o.regionCode_5)
            return false
        if (this.regionName_5 == null) {
            if (o.regionName_5 != null)
                return false
        }
        else if (this.regionName_5 != o.regionName_5)
            return false
        if (this.regionOrder_5 == null) {
            if (o.regionOrder_5 != null)
                return false
        }
        else if (this.regionOrder_5 != o.regionOrder_5)
            return false
        if (this.salesDepartmentSuperior == null) {
            if (o.salesDepartmentSuperior != null)
                return false
        }
        else if (this.salesDepartmentSuperior != o.salesDepartmentSuperior)
            return false
        if (this.isDeleted == null) {
            if (o.isDeleted != null)
                return false
        }
        else if (this.isDeleted != o.isDeleted)
            return false
        if (this.createUser == null) {
            if (o.createUser != null)
                return false
        }
        else if (this.createUser != o.createUser)
            return false
        if (this.createProgramId == null) {
            if (o.createProgramId != null)
                return false
        }
        else if (this.createProgramId != o.createProgramId)
            return false
        if (this.createDate == null) {
            if (o.createDate != null)
                return false
        }
        else if (this.createDate != o.createDate)
            return false
        if (this.createTime == null) {
            if (o.createTime != null)
                return false
        }
        else if (this.createTime != o.createTime)
            return false
        if (this.updateUser == null) {
            if (o.updateUser != null)
                return false
        }
        else if (this.updateUser != o.updateUser)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.deviceId == null) {
            if (o.deviceId != null)
                return false
        }
        else if (this.deviceId != o.deviceId)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + this.groupCode.hashCode()
        result = prime * result + this.departmentCode.hashCode()
        result = prime * result + this.useStartDate.hashCode()
        result = prime * result + (if (this.useFinishDate == null) 0 else this.useFinishDate.hashCode())
        result = prime * result + (if (this.regionCode_1 == null) 0 else this.regionCode_1.hashCode())
        result = prime * result + (if (this.regionName_1 == null) 0 else this.regionName_1.hashCode())
        result = prime * result + (if (this.regionOrder_1 == null) 0 else this.regionOrder_1.hashCode())
        result = prime * result + (if (this.regionCode_2 == null) 0 else this.regionCode_2.hashCode())
        result = prime * result + (if (this.regionName_2 == null) 0 else this.regionName_2.hashCode())
        result = prime * result + (if (this.regionOrder_2 == null) 0 else this.regionOrder_2.hashCode())
        result = prime * result + (if (this.regionCode_3 == null) 0 else this.regionCode_3.hashCode())
        result = prime * result + (if (this.regionName_3 == null) 0 else this.regionName_3.hashCode())
        result = prime * result + (if (this.regionOrder_3 == null) 0 else this.regionOrder_3.hashCode())
        result = prime * result + (if (this.regionCode_4 == null) 0 else this.regionCode_4.hashCode())
        result = prime * result + (if (this.regionName_4 == null) 0 else this.regionName_4.hashCode())
        result = prime * result + (if (this.regionOrder_4 == null) 0 else this.regionOrder_4.hashCode())
        result = prime * result + (if (this.regionCode_5 == null) 0 else this.regionCode_5.hashCode())
        result = prime * result + (if (this.regionName_5 == null) 0 else this.regionName_5.hashCode())
        result = prime * result + (if (this.regionOrder_5 == null) 0 else this.regionOrder_5.hashCode())
        result = prime * result + (if (this.salesDepartmentSuperior == null) 0 else this.salesDepartmentSuperior.hashCode())
        result = prime * result + (if (this.isDeleted == null) 0 else this.isDeleted.hashCode())
        result = prime * result + (if (this.createUser == null) 0 else this.createUser.hashCode())
        result = prime * result + (if (this.createProgramId == null) 0 else this.createProgramId.hashCode())
        result = prime * result + (if (this.createDate == null) 0 else this.createDate.hashCode())
        result = prime * result + (if (this.createTime == null) 0 else this.createTime.hashCode())
        result = prime * result + (if (this.updateUser == null) 0 else this.updateUser.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.deviceId == null) 0 else this.deviceId.hashCode())
        return result
    }
}
