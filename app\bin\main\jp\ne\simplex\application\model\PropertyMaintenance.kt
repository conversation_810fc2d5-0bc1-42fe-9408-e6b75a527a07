package jp.ne.simplex.application.model

interface PropertyMaintenance {
    enum class PublishStatus(val value: Int) {
        PUBLIC(1), // 公開
        PRIVATE(0) // 非公開
        ;

        companion object {
            fun fromValue(value: Int?): PublishStatus {
                return PublishStatus.entries.find { it.value == value } ?: PRIVATE
            }
        }
    }

    data class AdFf(
        /** AD(大家や賃貸管理会社が仲介業者に対して支払う広告料)費用 **/
        val advertisementFee: Int?,

        /** フロントフリーレント（一定期間、家賃が無料になる契約のこと）の期間 **/
        val frontFreerentPeriod: Float?,
    )

}

class UpdatePropertyMaintenance(
    val id: Property.Id,
    private val publishStatus: PropertyMaintenance.PublishStatus,
    private val adFf: PropertyMaintenance.AdFf
) : PropertyMaintenance {

    fun getUpdatePublishStatus(): PublishStatus {
        return PublishStatus()
    }

    fun getUpdatePublishStatusWithUpState(
        property: Property,
        upState: Property.UpState
    ): PublishStatusWithUpState {
        return PublishStatusWithUpState(property, upState)
    }

    fun getUpdateAdFf(property: Property): AdFf {
        return AdFf(property)
    }

    inner class PublishStatus {
        val id = <EMAIL>
        val publishStatus = <EMAIL>
    }

    inner class PublishStatusWithUpState(property: Property, upState: Property.UpState) {
        val id = <EMAIL>
        val publishStatus = <EMAIL>
        val propertyType = property.getType()
        val propertyUpState = upState
    }

    inner class AdFf(property: Property) {
        val id = <EMAIL>
        val adFf = <EMAIL>
        val propertyType = property.getType()
    }

    data class Result<SUCCESS, FAILED>(
        val success: SUCCESS,
        val failed: FAILED
    )

    data class FailedDetail(
        val id: Property.Id,
        val adFfUpdateResult: Status,
        val publicInstructionResult: Status
    ) {
        enum class Status {
            SUCCESS,
            FAILED,
            SKIP
        }
    }

    companion object {
        private fun List<UpdatePropertyMaintenance>.associate(
            propertyList: List<Property>
        ): List<Pair<UpdatePropertyMaintenance, Property>> {
            return this
                .mapNotNull { updatePropertyMaintenance ->
                    propertyList.find { it.id == updatePropertyMaintenance.id }
                        ?.let { Pair(updatePropertyMaintenance, it) }
                }
        }

        fun List<UpdatePropertyMaintenance>.filterBy(ids: List<Property.Id>): List<UpdatePropertyMaintenance> {
            return this.filter { receiverElem -> ids.any { receiverElem.id == it } }
        }

        fun List<UpdatePropertyMaintenance>.getUpdatePublishStatus(
            propertyList: List<Property>
        ): List<PublishStatus> {
            return this
                .associate(propertyList)
                .map { it.first.getUpdatePublishStatus() }
        }

        fun List<UpdatePropertyMaintenance>.getUpdatePublishStatusWithUpState(
            propertyList: List<Property>, propertyUpStates: Map<Property.Id, Property.UpState?>
        ): List<PublishStatusWithUpState> {
            return this
                .associate(propertyList)
                .mapNotNull { pair ->
                    propertyUpStates[pair.second.id]
                        ?.let { pair.first.getUpdatePublishStatusWithUpState(pair.second, it) }
                }
        }

        fun List<UpdatePropertyMaintenance>.getUpdateAdFf(
            propertyList: List<Property>
        ): List<AdFf> {
            return this.associate(propertyList).map { it.first.getUpdateAdFf(it.second) }
        }

        fun List<UpdatePropertyMaintenance>.filterByPublicInstruction(): List<UpdatePropertyMaintenance> {
            return this.filter { it.publishStatus == PropertyMaintenance.PublishStatus.PUBLIC }
        }

        fun onTemporaryReservationRegister(
            propertyId: Property.Id,
        ): UpdatePropertyMaintenance {
            return UpdatePropertyMaintenance(
                id = propertyId,
                publishStatus = PropertyMaintenance.PublishStatus.PRIVATE,
                adFf = PropertyMaintenance.AdFf(
                    advertisementFee = null,
                    frontFreerentPeriod = null
                )
            )
        }
    }
}

class PropertyMaintenanceInfo(
    val propertyId: Property.Id,
    val publishStatus: PropertyMaintenance.PublishStatus,
    val publishStatusBeforeTemporaryReserved: PropertyMaintenance.PublishStatus,
) : PropertyMaintenance
