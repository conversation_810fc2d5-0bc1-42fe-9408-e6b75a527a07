/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.BranchFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BranchFilePojo

import org.jooq.impl.TableRecordImpl


/**
 * 支店ファイル 既存システム物理名: EMUSIP
 */
@Suppress("UNCHECKED_CAST")
open class BranchFileRecord private constructor() : TableRecordImpl<BranchFileRecord>(BranchFileTable.BRANCH_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var lastUpdateProgram: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var branchCode: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var branchName: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var postalCode: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var phoneNumber: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var faxNumber: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var address: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var licenseNumber: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    /**
     * Create a detached, initialised BranchFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, lastUpdateProgram: String? = null, branchCode: String? = null, branchName: String? = null, postalCode: String? = null, phoneNumber: String? = null, faxNumber: String? = null, address: String? = null, licenseNumber: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.lastUpdateProgram = lastUpdateProgram
        this.branchCode = branchCode
        this.branchName = branchName
        this.postalCode = postalCode
        this.phoneNumber = phoneNumber
        this.faxNumber = faxNumber
        this.address = address
        this.licenseNumber = licenseNumber
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BranchFileRecord
     */
    constructor(value: BranchFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.lastUpdateProgram = value.lastUpdateProgram
            this.branchCode = value.branchCode
            this.branchName = value.branchName
            this.postalCode = value.postalCode
            this.phoneNumber = value.phoneNumber
            this.faxNumber = value.faxNumber
            this.address = value.address
            this.licenseNumber = value.licenseNumber
            resetChangedOnNotNull()
        }
    }
}
