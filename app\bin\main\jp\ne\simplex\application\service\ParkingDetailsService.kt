package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.Parking
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.VacancyParkingLotTarget
import jp.ne.simplex.application.repository.db.ParkingDetailsRepositoryInterface
import jp.ne.simplex.application.repository.db.ParkingRepositoryInterface
import jp.ne.simplex.application.repository.db.VacantParkingListRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.shared.MultiThreadRunBlocking.Companion.runAsyncTasks
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.util.*

@Service
class ParkingDetailsService(
    private val parkingDetailsRepository: ParkingDetailsRepositoryInterface,
    private val parkingRepository: ParkingRepositoryInterface,
    private val vacantParkingListRepository: VacantParkingListRepositoryInterface,
) {

    companion object {
        private val log = LoggerFactory.getLogger(ParkingDetailsService::class.java)
    }

    /**
     * 受注コードに紐づく駐車場情報リストを取得する。
     * forClientがtrueの場合、
     * 1. 建物契約形態を考慮して35年一括借上げの建物を取得する。
     * 2. 部屋テナント契約はテナント契約一括残集ファイルを考慮して取得する。
     */
    fun getParkingList(
        orderCode: Building.OrderCode,
        forClient: Boolean = false,
        authInfo: AuthInfo? = null,
    ): List<Parking> {
        return parkingDetailsRepository
            .findParkingDetailByOrderCode(orderCode, forClient, authInfo)
    }

    /**
     * 建物・駐車場コードに紐づく駐車場区画を取得する。
     * 合算先・元の駐車場区画も含めて取得するため、DB問い合わせは受注コード単位となる。
     */
    fun getParkingLot(parkingLotId: ParkingLot.Id): ParkingLot? {
        val parkingList = getParkingList(parkingLotId.buildingCode.getOrderCode())
        return parkingList.find {
            parkingLotId.buildingCode == it.building.code
        }?.parkingLotList?.find {
            parkingLotId == it.id
        }
    }

    /**
     * WelcomeParkバッチ処理用に全ての空き駐車場情報を取得します。
     */
    fun getAllVacancyParkingTarget(): List<VacancyParkingLotTarget> {

        val vacancyParkingLotList =
            Collections.synchronizedList(mutableListOf<VacancyParkingLotTarget>())

        // 建物マスタ.建物CDチェック用SQL
        val parkingLotIdMap = parkingRepository.findParkingLotIdMapForWelcomePark()

        // 合算元駐車場IDセット
        val selfParkingLotIdSet: Set<ParkingLot.Id> = parkingLotIdMap[true] ?: emptySet()

        // 合算先駐車場IDセット
        val consolidatedParkingLotIdSet: Set<ParkingLot.Id> = parkingLotIdMap[false] ?: emptySet()

        val orderCodeSet: Set<Building.OrderCode> = selfParkingLotIdSet
            .map { it.buildingCode.getOrderCode() }
            .toSet()

        // 空き駐車場ＤＢをクリア
        vacantParkingListRepository.delete()

        log.info("Start get all vacancy parking for WelcomePark batch. count=${orderCodeSet.size}")

        // 受注コードでループする
        runAsyncTasks(10, orderCodeSet.toList()) { orderCode ->

            // 個別駐車場情報を取得
            val parkingList: List<Parking> =
                parkingDetailsRepository.findParkingDetailForWelcomeParkBatch(orderCode)

            val start = System.currentTimeMillis()
            // 個別駐車場情報編集
            for (parking in parkingList) {
                val parkingLotList = parking.parkingLotList
                for (parkingLot in parkingLotList) {
                    log.info("Check parkingLotId=${parkingLot.id}, status=${parkingLot.parkingStatusDivision}, fee=${parkingLot.parkingFee}")

                    //WELCOMEの場合、合算原因で状態変わるとか可能性がないと思いますが、一旦合算先と自体取得するようにする
                    if (consolidatedParkingLotIdSet.contains(parkingLot.id)
                        || selfParkingLotIdSet.contains(parkingLot.id)
                    ) {
                        val parkingLotId = parkingLot.id

                        if (!selfParkingLotIdSet.contains(parkingLotId)
                            || (ParkingLot.StatusDivision.VACANT != parkingLot.parkingStatusDivision)
                            || parkingLot.parkingFee == null
                            || 0 >= parkingLot.parkingFee
                        ) { //0円条件追加
                            continue
                        }

                        vacancyParkingLotList.add(
                            VacancyParkingLotTarget(
                                id = parkingLotId,
                                parkingLotNumber = parkingLot.localDisplayNumber,
                                parkingFee = parkingLot.parkingFee,
                                bulkLeaseFlag = parkingLot.bulkLeaseFlag,
                                assessmentDivision = parkingLot.assessmentDivision,
                                parkingLotCategory = parkingLot.parkingLotCategory
                            )
                        )

                        vacantParkingListRepository.save(
                            parkingLotId,
                            parkingLot.localDisplayNumber
                        )
                    }
                }
            }
            log.info("complete processing orderCode=${orderCode.value}. latency=${System.currentTimeMillis() - start}ms")
        }

        return vacancyParkingLotList
    }

    /**
     * 受注コードに紐づく駐車場の部屋数を取得する。
     */
    fun getRoomCounts(orderCode: Building.OrderCode): Int {
        return parkingRepository.getRoomCountByOrderCode(orderCode)
    }

    /**
     * 受注コードに紐づく空き部屋数を取得する。
     */
    fun getVacantRooms(orderCode: Building.OrderCode): Int {
        return parkingRepository.getVacancyRoomCountByOrderCode(orderCode)
    }
}
