/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ParkingContractPossibilityTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingContractPossibilityPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 駐車場契約可否 既存システム物理名: -
 */
@Suppress("UNCHECKED_CAST")
open class ParkingContractPossibilityRecord private constructor() : UpdatableRecordImpl<ParkingContractPossibilityRecord>(ParkingContractPossibilityTable.PARKING_CONTRACT_POSSIBILITY) {

    open var orderCode: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    @Suppress("INAPPLICABLE_JVM_NAME")
    @set:JvmName("setIsFirstParkingContractPossible")
    open var isFirstParkingContractPossible: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    @Suppress("INAPPLICABLE_JVM_NAME")
    @set:JvmName("setIsSecondParkingContractPossible")
    open var isSecondParkingContractPossible: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    @Suppress("INAPPLICABLE_JVM_NAME")
    @set:JvmName("setIsAutoJudge")
    open var isAutoJudge: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var creationDate: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var creationTime: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var creator: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var updateDate: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var updateTime: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var updater: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var deleteFlag: String
        set(value): Unit = set(10, value)
        get(): String = get(10) as String

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised ParkingContractPossibilityRecord
     */
    constructor(orderCode: String, isFirstParkingContractPossible: String? = null, isSecondParkingContractPossible: String? = null, isAutoJudge: String? = null, creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, deleteFlag: String): this() {
        this.orderCode = orderCode
        this.isFirstParkingContractPossible = isFirstParkingContractPossible
        this.isSecondParkingContractPossible = isSecondParkingContractPossible
        this.isAutoJudge = isAutoJudge
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.deleteFlag = deleteFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ParkingContractPossibilityRecord
     */
    constructor(value: ParkingContractPossibilityPojo?): this() {
        if (value != null) {
            this.orderCode = value.orderCode
            this.isFirstParkingContractPossible = value.isFirstParkingContractPossible
            this.isSecondParkingContractPossible = value.isSecondParkingContractPossible
            this.isAutoJudge = value.isAutoJudge
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            resetChangedOnNotNull()
        }
    }
}
