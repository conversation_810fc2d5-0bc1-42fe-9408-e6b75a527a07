truncate table LATEST_PRODUCT_MASTER;
insert into LATEST_PRODUCT_MASTER (CREATION_DATE, CREATION_TIME, UPDATE_DATE, UPDATE_TIME, UPDATE_PROGRAM_ID, UPDATER, PRODUCT_NAME_CD, PRODUCT_CD_BRANCH, PRODUCT_NAME, PRODUCT_GRADE_NAME) values
 (20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 0, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', null)
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 1, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－０１')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 2, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－０２')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 3, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－０３')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 4, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－０４')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 5, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－０５')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 6, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－０６')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 7, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－０７')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 8, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－０８')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 9, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－０９')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 10, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－１０')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 11, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－１１')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 12, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－１２')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 13, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－１３')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 14, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－１４')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 15, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－１５')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 16, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－１６')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 17, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－１７')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 18, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－１８')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 19, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－１９')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 20, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－２０')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 21, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－２１')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 22, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－２２')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 23, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－２３')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 24, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－２４')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 25, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－２５')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 26, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－２６')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 27, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－２７')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 28, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－２８')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 29, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－２９')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 30, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－３０')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 31, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－３１')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 32, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－３２')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 33, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－３３')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 34, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－３４')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 35, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－３５')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 36, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－３６')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 37, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－３７')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 38, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－３８')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 39, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－３９')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 40, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－４０')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 41, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－４１')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 42, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－４２')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 43, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－４３')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 44, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－４４')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 45, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－４５')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 46, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－４６')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 47, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－４７')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 48, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－４８')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 49, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－４９')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 50, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－５０')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 51, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－５１')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 52, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－５２')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 53, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－５３')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 54, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－５４')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 55, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－５５')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 56, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－５６')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 57, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－５７')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 58, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－５８')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 59, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－５９')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 60, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－６０')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 61, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－６１')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 62, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－６２')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 63, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－６３')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 64, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－６４')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 65, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－６５')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 66, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－６６')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 67, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－６７')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 0, 68, 'ＤＡＩＴＯＨＯＵＳＥ　ＡシリーズＳＴＤ', 'Ａ－６８')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 1, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '０１－ＢＳ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 2, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '０２－ＢＳ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 3, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '０３－ＢＳ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 4, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '０４－ＢＳ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 5, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '０５－ＢＳ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 6, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '０６－ＢＳ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 7, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '０７－ＢＮ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 8, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '０８－ＢＮ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 9, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '０９－ＢＮ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 10, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '１０－ＢＥ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 11, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '１１－ＢＥ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 12, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '１２－ＢＥ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 13, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '１３－ＢＳ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 14, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '１４－ＢＳ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 15, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '１５－ＢＮ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 16, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '１６－ＢＮ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 17, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', '１７－ＢＥ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 21, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０１')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 22, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０１　Ｆ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 23, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０２')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 24, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０２　Ｆ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 25, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０３')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 26, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０３　Ｆ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 27, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０４')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 28, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０４　Ｆ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 29, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０５')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 30, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０５　Ｆ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 31, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０６')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 32, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０６　Ｆ')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 33, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０７')
,(20231018, 111343, 20231018, 111343, 'BATCH', 'BATCH', 1, 34, 'ＤＡＩＴＯＨＯＵＳＥ　Ｂシリ－ズ', 'Ｂ－０７　Ｆ')
;
