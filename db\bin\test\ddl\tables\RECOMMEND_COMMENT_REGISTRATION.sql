-- TABLE: RECOMMEND_COMMENT_REGISTRATION(おすすめコメント登録)

CREATE TABLE RECOMMEND_COMMENT_REGISTRATION(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    BUILDING_CD                                  varchar(9)        NOT NULL    
,    ROOM_CD                                      varchar(5)        NOT NULL    
,    REGISTRANT_NAME                              varchar(42)                   
,    COMMENT                                      varchar(402)                  
,    CONSTRAINT PK_RECOMMEND_COMMENT_REGISTRAT PRIMARY KEY (BUILDING_CD, ROOM_CD)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE RECOMMEND_COMMENT_REGISTRATION IS 'おすすめコメント登録 既存システム物理名: EMEOSP';
COMMENT ON COLUMN RECOMMEND_COMMENT_REGISTRATION.CREATION_DATE IS '作成年月日 既存システム物理名: EMEO1D';
COMMENT ON COLUMN RECOMMEND_COMMENT_REGISTRATION.CREATION_TIME IS '作成時刻 既存システム物理名: EMEO2T';
COMMENT ON COLUMN RECOMMEND_COMMENT_REGISTRATION.CREATOR IS '作成者 既存システム物理名: EMEO3C';
COMMENT ON COLUMN RECOMMEND_COMMENT_REGISTRATION.UPDATE_DATE IS '更新年月日 既存システム物理名: EMEO4D';
COMMENT ON COLUMN RECOMMEND_COMMENT_REGISTRATION.UPDATE_TIME IS '更新時刻 既存システム物理名: EMEO5T';
COMMENT ON COLUMN RECOMMEND_COMMENT_REGISTRATION.UPDATER IS '更新者 既存システム物理名: EMEO6C';
COMMENT ON COLUMN RECOMMEND_COMMENT_REGISTRATION.BUILDING_CD IS '建物CD 既存システム物理名: EMEO7N';
COMMENT ON COLUMN RECOMMEND_COMMENT_REGISTRATION.ROOM_CD IS '部屋CD 既存システム物理名: EMEO8N';
COMMENT ON COLUMN RECOMMEND_COMMENT_REGISTRATION.REGISTRANT_NAME IS '登録者名 既存システム物理名: EMEO9M';
COMMENT ON COLUMN RECOMMEND_COMMENT_REGISTRATION.COMMENT IS 'コメント 既存システム物理名: EME10M';
