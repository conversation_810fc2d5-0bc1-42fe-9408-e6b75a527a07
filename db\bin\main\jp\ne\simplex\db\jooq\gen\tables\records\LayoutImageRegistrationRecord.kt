/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.LayoutImageRegistrationTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.LayoutImageRegistrationPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 配置図画像登録 既存システム物理名: EMEPHP
 */
@Suppress("UNCHECKED_CAST")
open class LayoutImageRegistrationRecord private constructor() : UpdatableRecordImpl<LayoutImageRegistrationRecord>(LayoutImageRegistrationTable.LAYOUT_IMAGE_REGISTRATION) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var buildingCode: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised LayoutImageRegistrationRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, buildingCode: String): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.buildingCode = buildingCode
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised LayoutImageRegistrationRecord
     */
    constructor(value: LayoutImageRegistrationPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.buildingCode = value.buildingCode
            resetChangedOnNotNull()
        }
    }
}
