package jp.ne.simplex.application.repository.external.eboard

import jp.ne.simplex.application.repository.external.Method
import jp.ne.simplex.application.repository.external.eboard.config.EboardAuthResultCode
import jp.ne.simplex.application.repository.external.eboard.config.EboardResultCode
import jp.ne.simplex.application.repository.external.eboard.config.EboardTokenManager
import jp.ne.simplex.application.repository.external.eboard.dto.EboardTokenDpIssueRequest
import jp.ne.simplex.exception.ExternalApiConnectionException
import jp.ne.simplex.exception.ExternalApiServerException
import jp.ne.simplex.exception.ExternalApiUnauthorizedException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.client.match.MockRestRequestMatchers.*
import org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess
import kotlin.test.fail

class EboardRestConfigTest1 : EboardRestConfigTestBase() {

    @Nested
    @DisplayName("いい物件ボードAPIコール時に発生したエラーのハンドリング処理の検証")
    inner class Scenario1 {

        @Nested
        @DisplayName("想定外のJsonフォーマット（想定している項目がない）で、レスポンスを受信した場合")
        inner class Scenario1x1 {

            private val invalidJsonResponse = """
                { "code": 0, "details": "invalid json format" }
            """.trimIndent()

            @Test
            @DisplayName("Exceptionはスローされないこと")
            fun case1() {
                val request = TestRequest()

                server.expect(requestTo("$ENDPOINT${request.getApiPath().value}"))
                    .andRespond(
                        withSuccess(invalidJsonResponse, MediaType.APPLICATION_JSON)
                    )

                EboardTokenManager.updateToken("dummy_token")

                try {
                    restClient.call(
                        method = Method.GET,
                        path = request.getApiPath().value,
                        request = request,
                        responseClass = TestResponse::class.java
                    )
                } catch (e: Exception) {
                    fail()
                } finally {
                    EboardTokenManager.clear()
                }
            }
        }

        @Nested
        @DisplayName("想定通りのJsonフォーマットで、レスポンスを受信した場合")
        inner class Scenario1x2 {

            @Test
            @DisplayName("5xx系のレスポンスが返却された場合、想定通りのExceptionがスローされること")
            fun case1() {
                val request = TestRequest()

                server.expect(requestTo("$ENDPOINT${request.getApiPath().value}"))
                    .andRespond(
                        TestResponseCreator
                            .withServerError(
                                """{ "result": 103, "message": "その他の障害が発生しました" }"""
                            )
                    )

                EboardTokenManager.updateToken("dummy_token")

                try {
                    restClient.call(
                        method = Method.GET,
                        path = request.getApiPath().value,
                        request = request,
                        responseClass = TestResponse::class.java
                    )

                    // Exceptionがスローされない場合、ここでテストがこける
                    fail()
                } catch (e: Exception) {
                    assertEquals(e::class.java, ExternalApiConnectionException::class.java)
                } finally {
                    EboardTokenManager.clear()
                }
            }
        }
    }
}

// 認証API以外の通常のAPIのエラーハンドリングのテスト
class EboardRestConfigTest2 : EboardRestConfigTestBase() {
    data class CaseInput(
        val resultCode: EboardResultCode,
        val exceptionClazz: Class<*>,
    )

    companion object {
        @JvmStatic
        fun testCases() = listOf(
            CaseInput(
                EboardResultCode.PARAMETER_INVALID,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardResultCode.PARAMETER_MISSING,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardResultCode.NO_PROPERTIES_FOUND,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardResultCode.NO_STORES_FOUND,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardResultCode.MAX_COUNT_EXCEEDED,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardResultCode.DATA_ALREADY_EXISTS,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardResultCode.ROOM_NOT_FOUND,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardResultCode.AUTH_FAILED,
                ExternalApiUnauthorizedException::class.java,
            ),
            CaseInput(
                EboardResultCode.TOKEN_EXPIRED,
                ExternalApiUnauthorizedException::class.java,
            ),
            CaseInput(
                EboardResultCode.DATABASE_ERROR,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardResultCode.MAIL_SEND_ERROR,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardResultCode.OTHER_ERROR,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardResultCode.MESSAGE_NONE,
                ExternalApiServerException::class.java,
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("testCases")
    @DisplayName("トークンが設定されている場合、認証API以外のAPIは、レスポンスのresultの値に応じたExceptionがスローされること")
    fun case1(input: CaseInput) {
        val token = "dummy_access_token"
        EboardTokenManager.updateToken(token)

        val request = TestRequest()

        server.expect(requestTo("${ENDPOINT}${request.getApiPath().value}"))
            .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $token"))
            .andRespond(
                withSuccess(
                    """
                        {
                            "result": ${input.resultCode.code},
                            "message": "${input.resultCode.message}"
                        }
                    """.trimIndent(),
                    MediaType.APPLICATION_JSON
                )
            )

        try {
            restClient.call(
                method = Method.GET,
                path = request.getApiPath().value,
                request = request,
                responseClass = TestResponse::class.java
            )

            // Exceptionがスローされない場合、ここでテストがこける
            fail()
        } catch (e: Exception) {
            assertEquals(e::class.java, input.exceptionClazz)
        } finally {
            EboardTokenManager.clear()
        }
    }

    @ParameterizedTest
    @MethodSource("testCases")
    @DisplayName("トークンが設定されていない場合、認証API以外のAPIは、常に認証エラーになること")
    fun case2(input: CaseInput) {
        EboardTokenManager.clear()

        val request = TestRequest()

        server.expect(requestTo("${ENDPOINT}${request.getApiPath().value}"))
            .andRespond(
                withSuccess(
                    """
                        {
                            "result": ${input.resultCode.code},
                            "message": "${input.resultCode.message}"
                        }
                    """.trimIndent(),
                    MediaType.APPLICATION_JSON
                )
            )

        try {
            restClient.call(
                method = Method.GET,
                path = request.getApiPath().value,
                request = request,
                responseClass = TestResponse::class.java
            )

            // Exceptionがスローされない場合、ここでテストがこける
            fail()
        } catch (e: Exception) {
            assertEquals(e::class.java, ExternalApiUnauthorizedException::class.java)
        }
    }
}

// 認証APIのエラーハンドリングのテスト
class EboardRestConfigTest3 : EboardRestConfigTestBase() {
    data class CaseInput(
        val resultCode: EboardAuthResultCode,
        val exceptionClazz: Class<*>,
    )

    companion object {
        @JvmStatic
        fun testCases() = listOf(
            CaseInput(
                EboardAuthResultCode.PARAMETER_MISSING,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardAuthResultCode.INTERNAL_ERROR,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardAuthResultCode.USER_NOT_FOUND,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardAuthResultCode.PARAMETER_INVALID,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardAuthResultCode.MASTER_DATA_MISSING,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardAuthResultCode.IP_NOT_ALLOWED,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardAuthResultCode.SERVICE_RESTRICTED,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardAuthResultCode.OUT_OF_SERVICE_HOURS,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardAuthResultCode.DB_IP_INVALID,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardAuthResultCode.MASTER_KEY_EXPIRED,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardAuthResultCode.MASTER_KEY_RETRIEVAL_FAILED,
                ExternalApiServerException::class.java,
            ),
            CaseInput(
                EboardAuthResultCode.ENCRYPTION_FAILED,
                ExternalApiServerException::class.java,
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("testCases")
    @DisplayName("認証APIの場合、レスポンスのerror_codeの値に応じたExceptionがスローされること")
    fun case(input: CaseInput) {
        val request = EboardTokenDpIssueRequest("dummy", "dummy", "dummy")

        server.expect(requestTo("${ENDPOINT}${request.getApiPath().value}"))
            .andExpect(headerDoesNotExist(HttpHeaders.AUTHORIZATION))
            .andRespond(
                withSuccess(
                    """
                        {
                            "error_code": "${input.resultCode.code}",
                            "error_id": "fryt540-@8ea@fg-0rgr:",
                            "error_msg": "${input.resultCode.message}"
                        }
                    """.trimIndent(),
                    MediaType.APPLICATION_JSON
                )
            )

        try {
            restClient.call(
                method = Method.POST,
                path = request.getApiPath().value,
                request = request,
                responseClass = TestResponse::class.java
            )

            // Exceptionがスローされない場合、ここでテストがこける
            fail()
        } catch (e: Exception) {
            assertEquals(e::class.java, input.exceptionClazz)
        }
    }
}


