/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 各サイトコメント登録 既存システム物理名: EMEKKP
 */
@Suppress("UNCHECKED_CAST")
data class SiteCommentRegistrationPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var creator: String? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updater: String? = null,
    var buildingCd: String,
    var roomCd: String,
    var linkedCompanyCd: String,
    var linkedCompanyTextType: String,
    var comment: String? = null
): Serializable {


    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: SiteCommentRegistrationPojo = other as SiteCommentRegistrationPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.creator == null) {
            if (o.creator != null)
                return false
        }
        else if (this.creator != o.creator)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.buildingCd != o.buildingCd)
            return false
        if (this.roomCd != o.roomCd)
            return false
        if (this.linkedCompanyCd != o.linkedCompanyCd)
            return false
        if (this.linkedCompanyTextType != o.linkedCompanyTextType)
            return false
        if (this.comment == null) {
            if (o.comment != null)
                return false
        }
        else if (this.comment != o.comment)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.creator == null) 0 else this.creator.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + this.buildingCd.hashCode()
        result = prime * result + this.roomCd.hashCode()
        result = prime * result + this.linkedCompanyCd.hashCode()
        result = prime * result + this.linkedCompanyTextType.hashCode()
        result = prime * result + (if (this.comment == null) 0 else this.comment.hashCode())
        return result
    }
}
