-- TABLE: EXCLUSIVE_PROPERTY(先行公開)

CREATE TABLE EXCLUSIVE_PROPERTY(
     ID                                           numeric(18,0)     NOT NULL    
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    ROOM_CODE                                    varchar(5)        NOT NULL    
,    SALES_OFFICE_CODE                            varchar(3)                    
,    EXCLUSIVE_FROM                               numeric(8,0)      NOT NULL    
,    EXCLUSIVE_TO                                 numeric(8,0)      NOT NULL    
,    COMPANY_TYPE                                 numeric(1,0)      NOT NULL    
,    EARLY_CLOSURE_FLAG                           varchar(1)        NOT NULL    
,    CREATION_DATE                                numeric(8,0)      NOT NULL    
,    CREATION_TIME                                numeric(6,0)      NOT NULL    
,    CREATOR                                      varchar(6)        NOT NULL    
,    UPDATE_DATE                                  numeric(8,0)      NOT NULL    
,    UPDATE_TIME                                  numeric(6,0)      NOT NULL    
,    UPDATER                                      varchar(6)        NOT NULL    
,    DELETE_FLAG                                  varchar(1)        NOT NULL    
,    CONSTRAINT PK_EXCLUSIVE_PROPERTY PRIMARY KEY (ID)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE EXCLUSIVE_PROPERTY IS '先行公開 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.ID IS '先行公開ID 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.BUILDING_CODE IS '建物CD 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.ROOM_CODE IS '部屋CD 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.SALES_OFFICE_CODE IS '営業所CD 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.EXCLUSIVE_FROM IS '先行期間From 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.EXCLUSIVE_TO IS '先行期間To 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.COMPANY_TYPE IS '先行先種別 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.EARLY_CLOSURE_FLAG IS '早期終了フラグ 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.CREATION_DATE IS '作成年月日 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.CREATION_TIME IS '作成時刻 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.CREATOR IS '作成者 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.UPDATE_DATE IS '更新年月日 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.UPDATE_TIME IS '更新時刻 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.UPDATER IS '更新者 既存システム物理名: -';
COMMENT ON COLUMN EXCLUSIVE_PROPERTY.DELETE_FLAG IS '削除フラグ 既存システム物理名: -';
