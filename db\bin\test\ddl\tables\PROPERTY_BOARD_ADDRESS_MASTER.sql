-- TABLE: PROPERTY_BOARD_ADDRESS_MASTER(物件ボード用住所M)

CREATE TABLE PROPERTY_BOARD_ADDRESS_MASTER(
     PREFECTURE_CD                                varchar(2)        NOT NULL    
,    CITY_CD                                      varchar(3)        NOT NULL    
,    PREFECTURE_NAME                              varchar(18)                   
,    CITY_NAME                                    varchar(18)                   
,    PREFECTURE_CITY_NAME                         varchar(32)                   
,    CONSTRAINT PK_PROPERTY_BOARD_ADDRESS_MAST PRIMARY KEY (PREFECTURE_CD, CITY_CD)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PROPERTY_BOARD_ADDRESS_MASTER IS '物件ボード用住所M 既存システム物理名: EMEADP';
COMMENT ON COLUMN PROPERTY_BOARD_ADDRESS_MASTER.PREFECTURE_CD IS '県CD 既存システム物理名: EMEA1N';
COMMENT ON COLUMN PROPERTY_BOARD_ADDRESS_MASTER.CITY_CD IS '市区CD 既存システム物理名: EMEA2N';
COMMENT ON COLUMN PROPERTY_BOARD_ADDRESS_MASTER.PREFECTURE_NAME IS '県名 既存システム物理名: EMEA3N';
COMMENT ON COLUMN PROPERTY_BOARD_ADDRESS_MASTER.CITY_NAME IS '市区名 既存システム物理名: EMEA4N';
COMMENT ON COLUMN PROPERTY_BOARD_ADDRESS_MASTER.PREFECTURE_CITY_NAME IS '県市区名 既存システム物理名: EMEA5N';
