/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.AccidentPropertyManagementTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.AccidentPropertyManagementPojo

import org.jooq.impl.TableRecordImpl


/**
 * 事故物件管理 既存システム物理名: FEJ1BP
 */
@Suppress("UNCHECKED_CAST")
open class AccidentPropertyManagementRecord private constructor() : TableRecordImpl<AccidentPropertyManagementRecord>(AccidentPropertyManagementTable.ACCIDENT_PROPERTY_MANAGEMENT) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var buildingCode: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var roomCode: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var tenantContractNumber: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var incidentCategory: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var targetCategory: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var occurrenceDate: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    open var noticePeriodExpirationDate: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var noticePeriod: Byte?
        set(value): Unit = set(14, value)
        get(): Byte? = get(14) as Byte?

    open var specialContractCd: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var incidentDetail_1: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var incidentDetail_2: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var incidentDetail_3: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var incidentDetail_4: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var incidentDetail_5: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var incidentDetail_6: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var specialContractCd_2: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var subCategory: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var generalApplicationNo: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var issueCaseNo: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    /**
     * Create a detached, initialised AccidentPropertyManagementRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteSign: Byte? = null, buildingCode: String? = null, roomCode: String? = null, tenantContractNumber: String? = null, incidentCategory: String? = null, targetCategory: String? = null, occurrenceDate: Int? = null, noticePeriodExpirationDate: Int? = null, noticePeriod: Byte? = null, specialContractCd: String? = null, incidentDetail_1: String? = null, incidentDetail_2: String? = null, incidentDetail_3: String? = null, incidentDetail_4: String? = null, incidentDetail_5: String? = null, incidentDetail_6: String? = null, specialContractCd_2: String? = null, subCategory: String? = null, generalApplicationNo: String? = null, issueCaseNo: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteSign = logicalDeleteSign
        this.buildingCode = buildingCode
        this.roomCode = roomCode
        this.tenantContractNumber = tenantContractNumber
        this.incidentCategory = incidentCategory
        this.targetCategory = targetCategory
        this.occurrenceDate = occurrenceDate
        this.noticePeriodExpirationDate = noticePeriodExpirationDate
        this.noticePeriod = noticePeriod
        this.specialContractCd = specialContractCd
        this.incidentDetail_1 = incidentDetail_1
        this.incidentDetail_2 = incidentDetail_2
        this.incidentDetail_3 = incidentDetail_3
        this.incidentDetail_4 = incidentDetail_4
        this.incidentDetail_5 = incidentDetail_5
        this.incidentDetail_6 = incidentDetail_6
        this.specialContractCd_2 = specialContractCd_2
        this.subCategory = subCategory
        this.generalApplicationNo = generalApplicationNo
        this.issueCaseNo = issueCaseNo
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised AccidentPropertyManagementRecord
     */
    constructor(value: AccidentPropertyManagementPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteSign = value.logicalDeleteSign
            this.buildingCode = value.buildingCode
            this.roomCode = value.roomCode
            this.tenantContractNumber = value.tenantContractNumber
            this.incidentCategory = value.incidentCategory
            this.targetCategory = value.targetCategory
            this.occurrenceDate = value.occurrenceDate
            this.noticePeriodExpirationDate = value.noticePeriodExpirationDate
            this.noticePeriod = value.noticePeriod
            this.specialContractCd = value.specialContractCd
            this.incidentDetail_1 = value.incidentDetail_1
            this.incidentDetail_2 = value.incidentDetail_2
            this.incidentDetail_3 = value.incidentDetail_3
            this.incidentDetail_4 = value.incidentDetail_4
            this.incidentDetail_5 = value.incidentDetail_5
            this.incidentDetail_6 = value.incidentDetail_6
            this.specialContractCd_2 = value.specialContractCd_2
            this.subCategory = value.subCategory
            this.generalApplicationNo = value.generalApplicationNo
            this.issueCaseNo = value.issueCaseNo
            resetChangedOnNotNull()
        }
    }
}
