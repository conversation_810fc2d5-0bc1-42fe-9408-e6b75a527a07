/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.BuildingTypeMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingTypeMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 建物種別マスタ 既存システム物理名: XXHUSP
 */
@Suppress("UNCHECKED_CAST")
open class BuildingTypeMasterRecord private constructor() : TableRecordImpl<BuildingTypeMasterRecord>(BuildingTypeMasterTable.BUILDING_TYPE_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var reflectionDate: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var deleteFlag: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var buildingTypeCode: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var name: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var abbreviation_1: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var abbreviation_2: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var typeCategory: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var usageCategory: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var buildingActualSeq: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var buildingActualCd: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var buildingActualName: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    /**
     * Create a detached, initialised BuildingTypeMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, reflectionDate: Int? = null, deleteFlag: String? = null, buildingTypeCode: String? = null, name: String? = null, abbreviation_1: String? = null, abbreviation_2: String? = null, typeCategory: String? = null, usageCategory: String? = null, buildingActualSeq: String? = null, buildingActualCd: String? = null, buildingActualName: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.reflectionDate = reflectionDate
        this.deleteFlag = deleteFlag
        this.buildingTypeCode = buildingTypeCode
        this.name = name
        this.abbreviation_1 = abbreviation_1
        this.abbreviation_2 = abbreviation_2
        this.typeCategory = typeCategory
        this.usageCategory = usageCategory
        this.buildingActualSeq = buildingActualSeq
        this.buildingActualCd = buildingActualCd
        this.buildingActualName = buildingActualName
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BuildingTypeMasterRecord
     */
    constructor(value: BuildingTypeMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.reflectionDate = value.reflectionDate
            this.deleteFlag = value.deleteFlag
            this.buildingTypeCode = value.buildingTypeCode
            this.name = value.name
            this.abbreviation_1 = value.abbreviation_1
            this.abbreviation_2 = value.abbreviation_2
            this.typeCategory = value.typeCategory
            this.usageCategory = value.usageCategory
            this.buildingActualSeq = value.buildingActualSeq
            this.buildingActualCd = value.buildingActualCd
            this.buildingActualName = value.buildingActualName
            resetChangedOnNotNull()
        }
    }
}
