-- TABLE: IMAGE_COMMENT_MASTER(画像コメントマスタ)

CREATE TABLE IMAGE_COMMENT_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(9,0)                  
,    IMAGE_COMMENT_CODE                           varchar(3)                    
,    IMAGE_COMMENT                                varchar(84)                   
) TABLESPACE :TS_TBL;

COMMENT ON TABLE IMAGE_COMMENT_MASTER IS '画像コメントマスタ 既存システム物理名: ERCOMP';
COMMENT ON COLUMN IMAGE_COMMENT_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: ERC01D';
COMMENT ON COLUMN IMAGE_COMMENT_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: ERC02H';
COMMENT ON COLUMN IMAGE_COMMENT_MASTER.CREATOR IS '作成者 既存システム物理名: ERC03C';
COMMENT ON COLUMN IMAGE_COMMENT_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: ERC04D';
COMMENT ON COLUMN IMAGE_COMMENT_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: ERC05H';
COMMENT ON COLUMN IMAGE_COMMENT_MASTER.UPDATER IS '更新者 既存システム物理名: ERC06C';
COMMENT ON COLUMN IMAGE_COMMENT_MASTER.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: ERC07S';
COMMENT ON COLUMN IMAGE_COMMENT_MASTER.IMAGE_COMMENT_CODE IS '画像コメントコード 既存システム物理名: ERC08C';
COMMENT ON COLUMN IMAGE_COMMENT_MASTER.IMAGE_COMMENT IS '画像コメント 既存システム物理名: ERC09X';
