package jp.ne.simplex.configuration.saml

import jp.ne.simplex.application.repository.aws.SecretManagerRepository
import jp.ne.simplex.authentication.saml.SingleSignOnConfig
import org.opensaml.security.x509.X509Support
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.saml2.core.Saml2X509Credential
import org.springframework.security.saml2.provider.service.registration.*
import java.security.cert.X509Certificate

/**
 * spring-security-samlの設定
 */
@Configuration
@EnableWebSecurity
class Saml2SecurityConfig(
    private val ssoConfig: SingleSignOnConfig,
    private val secretManager: SecretManagerRepository
) {
    companion object {
        private val log = LoggerFactory.getLogger(Saml2SecurityConfig::class.java)
    }

    @Bean
    @Profile("!dev")
    fun relyingPartyRegistrations(): RelyingPartyRegistrationRepository? {
        return setUpRelyingPartyRegistrations(
            ssoConfig.saml.entityId,
            ssoConfig.saml.dkLinkUrl + "/saml",
            ssoConfig.saml.dkLinkUrl + "/api/v2/auth/acs",
            null
        )
    }

    @Bean
    @Profile("dev")
    fun relyingPartyRegistrationsDev(): RelyingPartyRegistrationRepository? {
        return setUpRelyingPartyRegistrations(
            ssoConfig.saml.entityId,
            ssoConfig.saml.dkLinkUrl + "/saml",
            ssoConfig.saml.dkLinkUrl.dropLast(4) + "8082/api/v2/auth/acs",
            ssoConfig.saml.entityId + "/protocol/saml/descriptor",
        )
    }

    private fun setUpRelyingPartyRegistrations(
        assertingEntityId: String, relyingEntityId: String, endpoint: String, metadataUrl: String?):
        RelyingPartyRegistrationRepository? {
        if (!ssoConfig.enabled) return null // SSOが無効の場合設定不要

        val metadata = getMetadata(metadataUrl)
        val builder = if (metadata !== null) {
            // メタデータを取得できた場合は詳細設定が不要となる
            RelyingPartyRegistration.withAssertingPartyMetadata(metadata)
        } else {
            // メタデータを取得できなかった場合は証明書データ等の設定が必要となる
            RelyingPartyRegistration
                .withRegistrationId(assertingEntityId)
                .assertingPartyMetadata { party: AssertingPartyMetadata.Builder<*> ->
                    party
                        .entityId(assertingEntityId) // match SAML Issuer
                        .wantAuthnRequestsSigned(false)
                        .singleSignOnServiceLocation(assertingEntityId) // idp initiatedの場合値は何でも良い
                        .verificationX509Credentials { c: MutableCollection<Saml2X509Credential?> ->
                            c.add(getCredential())
                        }
                }
        }
        builder
            .entityId(relyingEntityId) // match SAML Audience
            .assertionConsumerServiceLocation(endpoint) // post endpoint
        return InMemoryRelyingPartyRegistrationRepository(builder.build())
    }

    /**
     * SAMLレスポンス署名検証用の証明書をSecretManagerから取得し、復号化して設定します。
     * SecretManagerには証明書テキストを改行コードありで保持します。
     * SecretManagerから取得できなかった場合、取得できても証明書が不正な場合はエラーとなりサーバー起動に失敗します。
     */
    private fun getCredential(): Saml2X509Credential {
        val cert = secretManager.getValue(ssoConfig.saml.secretId)
        val certificate: X509Certificate? = X509Support.decodeCertificate(cert.toByteArray())
        return Saml2X509Credential.verification(certificate)
    }

    /**
     * idpが提供するurlからメタデータを取得します。
     * ネットワーク経路都合上、ローカル環境での検証時のみ利用可能です。
     */
    private fun getMetadata(url: String?): AssertingPartyMetadata? {
        if (url === null) return null
        return try {
            val repo = OpenSaml4AssertingPartyMetadataRepository.withTrustedMetadataLocation(url).build()
            repo?.findByEntityId(ssoConfig.saml.entityId)
        } catch (ex: Exception) {
            // ロギングは自動で行われるためここでは不要
            null
        }
    }

}
