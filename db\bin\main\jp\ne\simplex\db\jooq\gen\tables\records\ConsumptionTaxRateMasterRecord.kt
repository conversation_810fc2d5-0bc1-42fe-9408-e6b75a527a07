/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.ConsumptionTaxRateMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ConsumptionTaxRateMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 消費税率マスタ 既存システム物理名: EZJA0P
 */
@Suppress("UNCHECKED_CAST")
open class ConsumptionTaxRateMasterRecord private constructor() : TableRecordImpl<ConsumptionTaxRateMasterRecord>(ConsumptionTaxRateMasterTable.CONSUMPTION_TAX_RATE_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var consumptionTaxManagementCode: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var effectiveStartDate: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var effectiveEndDate: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var nationalTaxConsumptionPercent: BigDecimal?
        set(value): Unit = set(9, value)
        get(): BigDecimal? = get(9) as BigDecimal?

    open var localTaxConsumptionPercent: BigDecimal?
        set(value): Unit = set(10, value)
        get(): BigDecimal? = get(10) as BigDecimal?

    /**
     * Create a detached, initialised ConsumptionTaxRateMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, consumptionTaxManagementCode: String? = null, effectiveStartDate: Int? = null, effectiveEndDate: Int? = null, nationalTaxConsumptionPercent: BigDecimal? = null, localTaxConsumptionPercent: BigDecimal? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.consumptionTaxManagementCode = consumptionTaxManagementCode
        this.effectiveStartDate = effectiveStartDate
        this.effectiveEndDate = effectiveEndDate
        this.nationalTaxConsumptionPercent = nationalTaxConsumptionPercent
        this.localTaxConsumptionPercent = localTaxConsumptionPercent
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ConsumptionTaxRateMasterRecord
     */
    constructor(value: ConsumptionTaxRateMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.consumptionTaxManagementCode = value.consumptionTaxManagementCode
            this.effectiveStartDate = value.effectiveStartDate
            this.effectiveEndDate = value.effectiveEndDate
            this.nationalTaxConsumptionPercent = value.nationalTaxConsumptionPercent
            this.localTaxConsumptionPercent = value.localTaxConsumptionPercent
            resetChangedOnNotNull()
        }
    }
}
