/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.TenantSalesMaintenanceInfoTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.TenantSalesMaintenanceInfoPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * テナント営業メンテナンス情報 既存システム物理名: EMETMP
 */
@Suppress("UNCHECKED_CAST")
open class TenantSalesMaintenanceInfoRecord private constructor() : UpdatableRecordImpl<TenantSalesMaintenanceInfoRecord>(TenantSalesMaintenanceInfoTable.TENANT_SALES_MAINTENANCE_INFO) {

    open var branchCd: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var residentialBusinessUseDivision: Byte
        set(value): Unit = set(1, value)
        get(): Byte = get(1) as Byte

    open var sortOrder1: Byte?
        set(value): Unit = set(2, value)
        get(): Byte? = get(2) as Byte?

    open var sortOrder2: Byte?
        set(value): Unit = set(3, value)
        get(): Byte? = get(3) as Byte?

    open var sortOrder3: Byte?
        set(value): Unit = set(4, value)
        get(): Byte? = get(4) as Byte?

    open var sortOrder4: Byte?
        set(value): Unit = set(5, value)
        get(): Byte? = get(5) as Byte?

    open var sortOrder5: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var ascDescKey1: Byte?
        set(value): Unit = set(7, value)
        get(): Byte? = get(7) as Byte?

    open var ascDescKey2: Byte?
        set(value): Unit = set(8, value)
        get(): Byte? = get(8) as Byte?

    open var ascDescKey3: Byte?
        set(value): Unit = set(9, value)
        get(): Byte? = get(9) as Byte?

    open var ascDescKey4: Byte?
        set(value): Unit = set(10, value)
        get(): Byte? = get(10) as Byte?

    open var ascDescKey5: Byte?
        set(value): Unit = set(11, value)
        get(): Byte? = get(11) as Byte?

    open var displayDivision1: Byte?
        set(value): Unit = set(12, value)
        get(): Byte? = get(12) as Byte?

    open var displayDivision2: Byte?
        set(value): Unit = set(13, value)
        get(): Byte? = get(13) as Byte?

    open var displayDivision3: Byte?
        set(value): Unit = set(14, value)
        get(): Byte? = get(14) as Byte?

    open var displayDivision4: Byte?
        set(value): Unit = set(15, value)
        get(): Byte? = get(15) as Byte?

    open var displayDivision5: Byte?
        set(value): Unit = set(16, value)
        get(): Byte? = get(16) as Byte?

    open var displayDivision6: Byte?
        set(value): Unit = set(17, value)
        get(): Byte? = get(17) as Byte?

    open var displayDivision7: Byte?
        set(value): Unit = set(18, value)
        get(): Byte? = get(18) as Byte?

    open var displayDivision8: Byte?
        set(value): Unit = set(19, value)
        get(): Byte? = get(19) as Byte?

    open var displayDivision9: Byte?
        set(value): Unit = set(20, value)
        get(): Byte? = get(20) as Byte?

    open var displayDivision10: Byte?
        set(value): Unit = set(21, value)
        get(): Byte? = get(21) as Byte?

    open var displayDivision11: Byte?
        set(value): Unit = set(22, value)
        get(): Byte? = get(22) as Byte?

    open var displayDivision12: Byte?
        set(value): Unit = set(23, value)
        get(): Byte? = get(23) as Byte?

    open var displayDivision13: Byte?
        set(value): Unit = set(24, value)
        get(): Byte? = get(24) as Byte?

    open var displayDivision14: Byte?
        set(value): Unit = set(25, value)
        get(): Byte? = get(25) as Byte?

    open var displayDivision15: Byte?
        set(value): Unit = set(26, value)
        get(): Byte? = get(26) as Byte?

    open var displayDivision16: Byte?
        set(value): Unit = set(27, value)
        get(): Byte? = get(27) as Byte?

    open var displayDivision17: Byte?
        set(value): Unit = set(28, value)
        get(): Byte? = get(28) as Byte?

    open var displayDivision18: Byte?
        set(value): Unit = set(29, value)
        get(): Byte? = get(29) as Byte?

    open var displayDivision19: Byte?
        set(value): Unit = set(30, value)
        get(): Byte? = get(30) as Byte?

    open var displayDivision20: Byte?
        set(value): Unit = set(31, value)
        get(): Byte? = get(31) as Byte?

    open var displayDivision21: Byte?
        set(value): Unit = set(32, value)
        get(): Byte? = get(32) as Byte?

    open var displayDivision22: Byte?
        set(value): Unit = set(33, value)
        get(): Byte? = get(33) as Byte?

    open var displayDivision23: Byte?
        set(value): Unit = set(34, value)
        get(): Byte? = get(34) as Byte?

    open var displayDivision24: Byte?
        set(value): Unit = set(35, value)
        get(): Byte? = get(35) as Byte?

    open var displayDivision25: Byte?
        set(value): Unit = set(36, value)
        get(): Byte? = get(36) as Byte?

    open var displayDivision26: Byte?
        set(value): Unit = set(37, value)
        get(): Byte? = get(37) as Byte?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, Byte?> = super.key() as Record2<String?, Byte?>

    /**
     * Create a detached, initialised TenantSalesMaintenanceInfoRecord
     */
    constructor(branchCd: String, residentialBusinessUseDivision: Byte, sortOrder1: Byte? = null, sortOrder2: Byte? = null, sortOrder3: Byte? = null, sortOrder4: Byte? = null, sortOrder5: Byte? = null, ascDescKey1: Byte? = null, ascDescKey2: Byte? = null, ascDescKey3: Byte? = null, ascDescKey4: Byte? = null, ascDescKey5: Byte? = null, displayDivision1: Byte? = null, displayDivision2: Byte? = null, displayDivision3: Byte? = null, displayDivision4: Byte? = null, displayDivision5: Byte? = null, displayDivision6: Byte? = null, displayDivision7: Byte? = null, displayDivision8: Byte? = null, displayDivision9: Byte? = null, displayDivision10: Byte? = null, displayDivision11: Byte? = null, displayDivision12: Byte? = null, displayDivision13: Byte? = null, displayDivision14: Byte? = null, displayDivision15: Byte? = null, displayDivision16: Byte? = null, displayDivision17: Byte? = null, displayDivision18: Byte? = null, displayDivision19: Byte? = null, displayDivision20: Byte? = null, displayDivision21: Byte? = null, displayDivision22: Byte? = null, displayDivision23: Byte? = null, displayDivision24: Byte? = null, displayDivision25: Byte? = null, displayDivision26: Byte? = null): this() {
        this.branchCd = branchCd
        this.residentialBusinessUseDivision = residentialBusinessUseDivision
        this.sortOrder1 = sortOrder1
        this.sortOrder2 = sortOrder2
        this.sortOrder3 = sortOrder3
        this.sortOrder4 = sortOrder4
        this.sortOrder5 = sortOrder5
        this.ascDescKey1 = ascDescKey1
        this.ascDescKey2 = ascDescKey2
        this.ascDescKey3 = ascDescKey3
        this.ascDescKey4 = ascDescKey4
        this.ascDescKey5 = ascDescKey5
        this.displayDivision1 = displayDivision1
        this.displayDivision2 = displayDivision2
        this.displayDivision3 = displayDivision3
        this.displayDivision4 = displayDivision4
        this.displayDivision5 = displayDivision5
        this.displayDivision6 = displayDivision6
        this.displayDivision7 = displayDivision7
        this.displayDivision8 = displayDivision8
        this.displayDivision9 = displayDivision9
        this.displayDivision10 = displayDivision10
        this.displayDivision11 = displayDivision11
        this.displayDivision12 = displayDivision12
        this.displayDivision13 = displayDivision13
        this.displayDivision14 = displayDivision14
        this.displayDivision15 = displayDivision15
        this.displayDivision16 = displayDivision16
        this.displayDivision17 = displayDivision17
        this.displayDivision18 = displayDivision18
        this.displayDivision19 = displayDivision19
        this.displayDivision20 = displayDivision20
        this.displayDivision21 = displayDivision21
        this.displayDivision22 = displayDivision22
        this.displayDivision23 = displayDivision23
        this.displayDivision24 = displayDivision24
        this.displayDivision25 = displayDivision25
        this.displayDivision26 = displayDivision26
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised TenantSalesMaintenanceInfoRecord
     */
    constructor(value: TenantSalesMaintenanceInfoPojo?): this() {
        if (value != null) {
            this.branchCd = value.branchCd
            this.residentialBusinessUseDivision = value.residentialBusinessUseDivision
            this.sortOrder1 = value.sortOrder1
            this.sortOrder2 = value.sortOrder2
            this.sortOrder3 = value.sortOrder3
            this.sortOrder4 = value.sortOrder4
            this.sortOrder5 = value.sortOrder5
            this.ascDescKey1 = value.ascDescKey1
            this.ascDescKey2 = value.ascDescKey2
            this.ascDescKey3 = value.ascDescKey3
            this.ascDescKey4 = value.ascDescKey4
            this.ascDescKey5 = value.ascDescKey5
            this.displayDivision1 = value.displayDivision1
            this.displayDivision2 = value.displayDivision2
            this.displayDivision3 = value.displayDivision3
            this.displayDivision4 = value.displayDivision4
            this.displayDivision5 = value.displayDivision5
            this.displayDivision6 = value.displayDivision6
            this.displayDivision7 = value.displayDivision7
            this.displayDivision8 = value.displayDivision8
            this.displayDivision9 = value.displayDivision9
            this.displayDivision10 = value.displayDivision10
            this.displayDivision11 = value.displayDivision11
            this.displayDivision12 = value.displayDivision12
            this.displayDivision13 = value.displayDivision13
            this.displayDivision14 = value.displayDivision14
            this.displayDivision15 = value.displayDivision15
            this.displayDivision16 = value.displayDivision16
            this.displayDivision17 = value.displayDivision17
            this.displayDivision18 = value.displayDivision18
            this.displayDivision19 = value.displayDivision19
            this.displayDivision20 = value.displayDivision20
            this.displayDivision21 = value.displayDivision21
            this.displayDivision22 = value.displayDivision22
            this.displayDivision23 = value.displayDivision23
            this.displayDivision24 = value.displayDivision24
            this.displayDivision25 = value.displayDivision25
            this.displayDivision26 = value.displayDivision26
            resetChangedOnNotNull()
        }
    }
}
