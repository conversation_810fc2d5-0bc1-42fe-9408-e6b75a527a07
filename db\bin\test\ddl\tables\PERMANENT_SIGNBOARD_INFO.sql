-- TABLE: PERMANENT_SIGNBOARD_INFO(常設看板情報)

CREATE TABLE PERMANENT_SIGNBOARD_INFO(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    BUILDING_CD                                  varchar(9)        NOT NULL    
,    IMAGE_FILE_NAME_1                            varchar(50)                   
,    IMAGE_REGISTRATION_DATE_1                    numeric(8,0)                  
,    IMAGE_REGISTRATION_TIME_1                    numeric(6,0)                  
,    INSTALLATION_DATE_1                          numeric(8,0)                  
,    REPLACEMENT_DATE_1                           numeric(8,0)                  
,    PREVIOUS_REPLACEMENT_DATE_1                  numeric(8,0)                  
,    REMOVAL_DATE_1                               numeric(8,0)                  
,    CONTRACTOR_CD_E_1                            varchar(9)                    
,    WORK_PERFORMED_1                             numeric(1,0)                  
,    FRAME_1                                      numeric(1,0)                  
,    IMAGE_FILE_NAME_2                            varchar(50)                   
,    IMAGE_REGISTRATION_DATE_2                    numeric(8,0)                  
,    IMAGE_REGISTRATION_TIME_2                    numeric(6,0)                  
,    INSTALLATION_DATE_2                          numeric(8,0)                  
,    REPLACEMENT_DATE_2                           numeric(8,0)                  
,    PREVIOUS_REPLACEMENT_DATE_2                  numeric(8,0)                  
,    REMOVAL_DATE_2                               numeric(8,0)                  
,    CONTRACTOR_CD_E_2                            varchar(9)                    
,    WORK_PERFORMED_2                             numeric(1,0)                  
,    FRAME_2                                      numeric(1,0)                  
,    IMAGE_FILE_NAME_3                            varchar(50)                   
,    IMAGE_REGISTRATION_DATE_3                    numeric(8,0)                  
,    IMAGE_REGISTRATION_TIME_3                    numeric(6,0)                  
,    INSTALLATION_DATE_3                          numeric(8,0)                  
,    REPLACEMENT_DATE_3                           numeric(8,0)                  
,    PREVIOUS_REPLACEMENT_DATE_3                  numeric(8,0)                  
,    REMOVAL_DATE_3                               numeric(8,0)                  
,    CONTRACTOR_CD_E_3                            varchar(9)                    
,    WORK_PERFORMED_3                             numeric(1,0)                  
,    FRAME_3                                      numeric(1,0)                  
,    IMAGE_FILE_NAME_4                            varchar(50)                   
,    IMAGE_REGISTRATION_DATE_4                    numeric(8,0)                  
,    IMAGE_REGISTRATION_TIME_4                    numeric(6,0)                  
,    INSTALLATION_DATE_4                          numeric(8,0)                  
,    REPLACEMENT_DATE_4                           numeric(8,0)                  
,    PREVIOUS_REPLACEMENT_DATE_4                  numeric(8,0)                  
,    REMOVAL_DATE_4                               numeric(8,0)                  
,    CONTRACTOR_CD_E_4                            varchar(9)                    
,    WORK_PERFORMED_4                             numeric(1,0)                  
,    FRAME_4                                      numeric(1,0)                  
,    IMAGE_FILE_NAME_5                            varchar(50)                   
,    IMAGE_REGISTRATION_DATE_5                    numeric(8,0)                  
,    IMAGE_REGISTRATION_TIME_5                    numeric(6,0)                  
,    INSTALLATION_DATE_5                          numeric(8,0)                  
,    REPLACEMENT_DATE_5                           numeric(8,0)                  
,    PREVIOUS_REPLACEMENT_DATE_5                  numeric(8,0)                  
,    REMOVAL_DATE_5                               numeric(8,0)                  
,    CONTRACTOR_CD_E_5                            varchar(9)                    
,    WORK_PERFORMED_5                             numeric(1,0)                  
,    FRAME_5                                      numeric(1,0)                  
,    IMAGE_FILE_NAME_6                            varchar(50)                   
,    IMAGE_REGISTRATION_DATE_6                    numeric(8,0)                  
,    IMAGE_REGISTRATION_TIME_6                    numeric(6,0)                  
,    INSTALLATION_DATE_6                          numeric(8,0)                  
,    REPLACEMENT_DATE_6                           numeric(8,0)                  
,    PREVIOUS_REPLACEMENT_DATE_6                  numeric(8,0)                  
,    REMOVAL_DATE_6                               numeric(8,0)                  
,    CONTRACTOR_CD_E_6                            varchar(9)                    
,    WORK_PERFORMED_6                             numeric(1,0)                  
,    FRAME_6                                      numeric(1,0)                  
,    IMAGE_FILE_NAME_7                            varchar(50)                   
,    IMAGE_REGISTRATION_DATE_7                    numeric(8,0)                  
,    IMAGE_REGISTRATION_TIME_7                    numeric(6,0)                  
,    INSTALLATION_DATE_7                          numeric(8,0)                  
,    REPLACEMENT_DATE_7                           numeric(8,0)                  
,    PREVIOUS_REPLACEMENT_DATE_7                  numeric(8,0)                  
,    REMOVAL_DATE_7                               numeric(8,0)                  
,    CONTRACTOR_CD_E_7                            varchar(9)                    
,    WORK_PERFORMED_7                             numeric(1,0)                  
,    FRAME_7                                      numeric(1,0)                  
,    IMAGE_FILE_NAME_8                            varchar(50)                   
,    IMAGE_REGISTRATION_DATE_8                    numeric(8,0)                  
,    IMAGE_REGISTRATION_TIME_8                    numeric(6,0)                  
,    INSTALLATION_DATE_8                          numeric(8,0)                  
,    REPLACEMENT_DATE_8                           numeric(8,0)                  
,    PREVIOUS_REPLACEMENT_DATE_8                  numeric(8,0)                  
,    REMOVAL_DATE_8                               numeric(8,0)                  
,    CONTRACTOR_CD_E_8                            varchar(9)                    
,    WORK_PERFORMED_8                             numeric(1,0)                  
,    FRAME_8                                      numeric(1,0)                  
,    IMAGE_FILE_NAME_9                            varchar(50)                   
,    IMAGE_REGISTRATION_DATE_9                    numeric(8,0)                  
,    IMAGE_REGISTRATION_TIME_9                    numeric(6,0)                  
,    INSTALLATION_DATE_9                          numeric(8,0)                  
,    REPLACEMENT_DATE_9                           numeric(8,0)                  
,    PREVIOUS_REPLACEMENT_DATE_9                  numeric(8,0)                  
,    REMOVAL_DATE_9                               numeric(8,0)                  
,    CONTRACTOR_CD_E_9                            varchar(9)                    
,    WORK_PERFORMED_9                             numeric(1,0)                  
,    FRAME_9                                      numeric(1,0)                  
,    IMAGE_FILE_NAME_10                           varchar(50)                   
,    IMAGE_REGISTRATION_DATE_10                   numeric(8,0)                  
,    IMAGE_REGISTRATION_TIME_10                   numeric(6,0)                  
,    INSTALLATION_DATE_10                         numeric(8,0)                  
,    REPLACEMENT_DATE_10                          numeric(8,0)                  
,    PREVIOUS_REPLACEMENT_DATE_10                 numeric(8,0)                  
,    REMOVAL_DATE_10                              numeric(8,0)                  
,    CONTRACTOR_CD_E_10                           varchar(9)                    
,    WORK_PERFORMED_10                            numeric(1,0)                  
,    FRAME_10                                     numeric(1,0)                  
,    NEXT_REPLACEMENT_DATE_1                      numeric(8,0)                  
,    NEXT_REPLACEMENT_DATE_2                      numeric(8,0)                  
,    NEXT_REPLACEMENT_DATE_3                      numeric(8,0)                  
,    NEXT_REPLACEMENT_DATE_4                      numeric(8,0)                  
,    NEXT_REPLACEMENT_DATE_5                      numeric(8,0)                  
,    NEXT_REPLACEMENT_DATE_6                      numeric(8,0)                  
,    NEXT_REPLACEMENT_DATE_7                      numeric(8,0)                  
,    NEXT_REPLACEMENT_DATE_8                      numeric(8,0)                  
,    NEXT_REPLACEMENT_DATE_9                      numeric(8,0)                  
,    NEXT_REPLACEMENT_DATE_10                     numeric(8,0)                  
,    CONSTRAINT PK_PERMANENT_SIGNBOARD_INFO PRIMARY KEY (BUILDING_CD)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PERMANENT_SIGNBOARD_INFO IS '常設看板情報 既存システム物理名: EPSK1P';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.CREATION_DATE IS '作成年月日 既存システム物理名: EPS01D YYYYMMDD';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.CREATION_TIME IS '作成時刻 既存システム物理名: EPS02H HHMMSS';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.UPDATE_DATE IS '更新年月日 既存システム物理名: EPS03D YYYYMMDD';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.UPDATE_TIME IS '更新時刻 既存システム物理名: EPS04H YYYYMMDD';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EPS05N YYYYMMDD';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.UPDATER IS '更新者 既存システム物理名: EPS06C YYYYMMDD';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.BUILDING_CD IS '建物CD 既存システム物理名: EPS07C';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_FILE_NAME_1 IS '画像ファイル名1 既存システム物理名: EPS10M';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_DATE_1 IS '画像登録日1 既存システム物理名: EPS11D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_TIME_1 IS '画像登録時間1 既存システム物理名: EPS12H';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.INSTALLATION_DATE_1 IS '設置日1 既存システム物理名: EPS13D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REPLACEMENT_DATE_1 IS '交換日1 既存システム物理名: EPS14D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.PREVIOUS_REPLACEMENT_DATE_1 IS '前回交換日1 既存システム物理名: EPS15D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REMOVAL_DATE_1 IS '撤去日1 既存システム物理名: EPS16D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.CONTRACTOR_CD_E_1 IS '業者CD(Ｅコード)1 既存システム物理名: EPS17C';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.WORK_PERFORMED_1 IS '実施作業1 既存システム物理名: EPS18N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.FRAME_1 IS 'フレーム1 既存システム物理名: EPS19N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_FILE_NAME_2 IS '画像ファイル名2 既存システム物理名: EPS20M';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_DATE_2 IS '画像登録日2 既存システム物理名: EPS21D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_TIME_2 IS '画像登録時間2 既存システム物理名: EPS22H';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.INSTALLATION_DATE_2 IS '設置日2 既存システム物理名: EPS23D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REPLACEMENT_DATE_2 IS '交換日2 既存システム物理名: EPS24D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.PREVIOUS_REPLACEMENT_DATE_2 IS '前回交換日2 既存システム物理名: EPS25D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REMOVAL_DATE_2 IS '撤去日2 既存システム物理名: EPS26D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.CONTRACTOR_CD_E_2 IS '業者CD(Ｅコード)2 既存システム物理名: EPS27C';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.WORK_PERFORMED_2 IS '実施作業2 既存システム物理名: EPS28N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.FRAME_2 IS 'フレーム2 既存システム物理名: EPS29N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_FILE_NAME_3 IS '画像ファイル名3 既存システム物理名: EPS30M';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_DATE_3 IS '画像登録日3 既存システム物理名: EPS31D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_TIME_3 IS '画像登録時間3 既存システム物理名: EPS32H';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.INSTALLATION_DATE_3 IS '設置日3 既存システム物理名: EPS33D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REPLACEMENT_DATE_3 IS '交換日3 既存システム物理名: EPS34D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.PREVIOUS_REPLACEMENT_DATE_3 IS '前回交換日3 既存システム物理名: EPS35D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REMOVAL_DATE_3 IS '撤去日3 既存システム物理名: EPS36D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.CONTRACTOR_CD_E_3 IS '業者CD(Ｅコード)3 既存システム物理名: EPS37C';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.WORK_PERFORMED_3 IS '実施作業3 既存システム物理名: EPS38N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.FRAME_3 IS 'フレーム3 既存システム物理名: EPS39N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_FILE_NAME_4 IS '画像ファイル名4 既存システム物理名: EPS40M';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_DATE_4 IS '画像登録日4 既存システム物理名: EPS41D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_TIME_4 IS '画像登録時間4 既存システム物理名: EPS42H';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.INSTALLATION_DATE_4 IS '設置日4 既存システム物理名: EPS43D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REPLACEMENT_DATE_4 IS '交換日4 既存システム物理名: EPS44D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.PREVIOUS_REPLACEMENT_DATE_4 IS '前回交換日4 既存システム物理名: EPS45D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REMOVAL_DATE_4 IS '撤去日4 既存システム物理名: EPS46D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.CONTRACTOR_CD_E_4 IS '業者CD(Ｅコード)4 既存システム物理名: EPS47C';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.WORK_PERFORMED_4 IS '実施作業4 既存システム物理名: EPS48N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.FRAME_4 IS 'フレーム4 既存システム物理名: EPS49N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_FILE_NAME_5 IS '画像ファイル名5 既存システム物理名: EPS50M';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_DATE_5 IS '画像登録日5 既存システム物理名: EPS51D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_TIME_5 IS '画像登録時間5 既存システム物理名: EPS52H';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.INSTALLATION_DATE_5 IS '設置日5 既存システム物理名: EPS53D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REPLACEMENT_DATE_5 IS '交換日5 既存システム物理名: EPS54D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.PREVIOUS_REPLACEMENT_DATE_5 IS '前回交換日5 既存システム物理名: EPS55D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REMOVAL_DATE_5 IS '撤去日5 既存システム物理名: EPS56D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.CONTRACTOR_CD_E_5 IS '業者CD(Ｅコード)5 既存システム物理名: EPS57C';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.WORK_PERFORMED_5 IS '実施作業5 既存システム物理名: EPS58N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.FRAME_5 IS 'フレーム5 既存システム物理名: EPS59N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_FILE_NAME_6 IS '画像ファイル名6 既存システム物理名: EPS60M';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_DATE_6 IS '画像登録日6 既存システム物理名: EPS61D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_TIME_6 IS '画像登録時間6 既存システム物理名: EPS62H';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.INSTALLATION_DATE_6 IS '設置日6 既存システム物理名: EPS63D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REPLACEMENT_DATE_6 IS '交換日6 既存システム物理名: EPS64D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.PREVIOUS_REPLACEMENT_DATE_6 IS '前回交換日6 既存システム物理名: EPS65D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REMOVAL_DATE_6 IS '撤去日6 既存システム物理名: EPS66D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.CONTRACTOR_CD_E_6 IS '業者CD(Ｅコード)6 既存システム物理名: EPS67C';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.WORK_PERFORMED_6 IS '実施作業6 既存システム物理名: EPS68N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.FRAME_6 IS 'フレーム6 既存システム物理名: EPS69N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_FILE_NAME_7 IS '画像ファイル名7 既存システム物理名: EPS70M';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_DATE_7 IS '画像登録日7 既存システム物理名: EPS71D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_TIME_7 IS '画像登録時間7 既存システム物理名: EPS72H';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.INSTALLATION_DATE_7 IS '設置日7 既存システム物理名: EPS73D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REPLACEMENT_DATE_7 IS '交換日7 既存システム物理名: EPS74D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.PREVIOUS_REPLACEMENT_DATE_7 IS '前回交換日7 既存システム物理名: EPS75D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REMOVAL_DATE_7 IS '撤去日7 既存システム物理名: EPS76D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.CONTRACTOR_CD_E_7 IS '業者CD(Ｅコード)7 既存システム物理名: EPS77C';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.WORK_PERFORMED_7 IS '実施作業7 既存システム物理名: EPS78N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.FRAME_7 IS 'フレーム7 既存システム物理名: EPS79N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_FILE_NAME_8 IS '画像ファイル名8 既存システム物理名: EPS80M';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_DATE_8 IS '画像登録日8 既存システム物理名: EPS81D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_TIME_8 IS '画像登録時間8 既存システム物理名: EPS82H';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.INSTALLATION_DATE_8 IS '設置日8 既存システム物理名: EPS83D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REPLACEMENT_DATE_8 IS '交換日8 既存システム物理名: EPS84D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.PREVIOUS_REPLACEMENT_DATE_8 IS '前回交換日8 既存システム物理名: EPS85D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REMOVAL_DATE_8 IS '撤去日8 既存システム物理名: EPS86D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.CONTRACTOR_CD_E_8 IS '業者CD(Ｅコード)8 既存システム物理名: EPS87C';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.WORK_PERFORMED_8 IS '実施作業8 既存システム物理名: EPS88N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.FRAME_8 IS 'フレーム8 既存システム物理名: EPS89N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_FILE_NAME_9 IS '画像ファイル名9 既存システム物理名: EPS90M';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_DATE_9 IS '画像登録日9 既存システム物理名: EPS91D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_TIME_9 IS '画像登録時間9 既存システム物理名: EPS92H';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.INSTALLATION_DATE_9 IS '設置日9 既存システム物理名: EPS93D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REPLACEMENT_DATE_9 IS '交換日9 既存システム物理名: EPS94D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.PREVIOUS_REPLACEMENT_DATE_9 IS '前回交換日9 既存システム物理名: EPS95D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REMOVAL_DATE_9 IS '撤去日9 既存システム物理名: EPS96D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.CONTRACTOR_CD_E_9 IS '業者CD(Ｅコード)9 既存システム物理名: EPS97C';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.WORK_PERFORMED_9 IS '実施作業9 既存システム物理名: EPS98N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.FRAME_9 IS 'フレーム9 既存システム物理名: EPS99N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_FILE_NAME_10 IS '画像ファイル名10 既存システム物理名: EP100M';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_DATE_10 IS '画像登録日10 既存システム物理名: EP101D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.IMAGE_REGISTRATION_TIME_10 IS '画像登録時間10 既存システム物理名: EP102H';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.INSTALLATION_DATE_10 IS '設置日10 既存システム物理名: EP103D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REPLACEMENT_DATE_10 IS '交換日10 既存システム物理名: EP104D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.PREVIOUS_REPLACEMENT_DATE_10 IS '前回交換日10 既存システム物理名: EP105D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.REMOVAL_DATE_10 IS '撤去日10 既存システム物理名: EP106D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.CONTRACTOR_CD_E_10 IS '業者CD(Ｅコード)10 既存システム物理名: EP107C';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.WORK_PERFORMED_10 IS '実施作業10 既存システム物理名: EP108N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.FRAME_10 IS 'フレーム10 既存システム物理名: EP109N';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.NEXT_REPLACEMENT_DATE_1 IS '次回交換日1 既存システム物理名: EP110D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.NEXT_REPLACEMENT_DATE_2 IS '次回交換日2 既存システム物理名: EP111D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.NEXT_REPLACEMENT_DATE_3 IS '次回交換日3 既存システム物理名: EP112D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.NEXT_REPLACEMENT_DATE_4 IS '次回交換日4 既存システム物理名: EP113D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.NEXT_REPLACEMENT_DATE_5 IS '次回交換日5 既存システム物理名: EP114D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.NEXT_REPLACEMENT_DATE_6 IS '次回交換日6 既存システム物理名: EP115D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.NEXT_REPLACEMENT_DATE_7 IS '次回交換日7 既存システム物理名: EP116D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.NEXT_REPLACEMENT_DATE_8 IS '次回交換日8 既存システム物理名: EP117D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.NEXT_REPLACEMENT_DATE_9 IS '次回交換日9 既存システム物理名: EP118D';
COMMENT ON COLUMN PERMANENT_SIGNBOARD_INFO.NEXT_REPLACEMENT_DATE_10 IS '次回交換日10 既存システム物理名: EP119D';
