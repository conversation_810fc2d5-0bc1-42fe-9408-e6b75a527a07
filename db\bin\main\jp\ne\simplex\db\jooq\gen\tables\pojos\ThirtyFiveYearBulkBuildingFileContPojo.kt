/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 35年一括建物ファイル(契約書) 既存システム物理名: HC360P
 */
@Suppress("UNCHECKED_CAST")
data class ThirtyFiveYearBulkBuildingFileContPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var creationProgramId: String? = null,
    var creationTerminalId: String? = null,
    var creationResponsibleCd: String? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updateTerminalId: String? = null,
    var updateResponsibleCd: String? = null,
    var logicalDeleteSign: String? = null,
    var buildingCd: String? = null,
    var effectiveDate: Int? = null,
    var contractOutputManagementNo: Short? = null,
    var bulkLeaseType: Byte? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: ThirtyFiveYearBulkBuildingFileContPojo = other as ThirtyFiveYearBulkBuildingFileContPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.creationProgramId == null) {
            if (o.creationProgramId != null)
                return false
        }
        else if (this.creationProgramId != o.creationProgramId)
            return false
        if (this.creationTerminalId == null) {
            if (o.creationTerminalId != null)
                return false
        }
        else if (this.creationTerminalId != o.creationTerminalId)
            return false
        if (this.creationResponsibleCd == null) {
            if (o.creationResponsibleCd != null)
                return false
        }
        else if (this.creationResponsibleCd != o.creationResponsibleCd)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updateTerminalId == null) {
            if (o.updateTerminalId != null)
                return false
        }
        else if (this.updateTerminalId != o.updateTerminalId)
            return false
        if (this.updateResponsibleCd == null) {
            if (o.updateResponsibleCd != null)
                return false
        }
        else if (this.updateResponsibleCd != o.updateResponsibleCd)
            return false
        if (this.logicalDeleteSign == null) {
            if (o.logicalDeleteSign != null)
                return false
        }
        else if (this.logicalDeleteSign != o.logicalDeleteSign)
            return false
        if (this.buildingCd == null) {
            if (o.buildingCd != null)
                return false
        }
        else if (this.buildingCd != o.buildingCd)
            return false
        if (this.effectiveDate == null) {
            if (o.effectiveDate != null)
                return false
        }
        else if (this.effectiveDate != o.effectiveDate)
            return false
        if (this.contractOutputManagementNo == null) {
            if (o.contractOutputManagementNo != null)
                return false
        }
        else if (this.contractOutputManagementNo != o.contractOutputManagementNo)
            return false
        if (this.bulkLeaseType == null) {
            if (o.bulkLeaseType != null)
                return false
        }
        else if (this.bulkLeaseType != o.bulkLeaseType)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.creationProgramId == null) 0 else this.creationProgramId.hashCode())
        result = prime * result + (if (this.creationTerminalId == null) 0 else this.creationTerminalId.hashCode())
        result = prime * result + (if (this.creationResponsibleCd == null) 0 else this.creationResponsibleCd.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updateTerminalId == null) 0 else this.updateTerminalId.hashCode())
        result = prime * result + (if (this.updateResponsibleCd == null) 0 else this.updateResponsibleCd.hashCode())
        result = prime * result + (if (this.logicalDeleteSign == null) 0 else this.logicalDeleteSign.hashCode())
        result = prime * result + (if (this.buildingCd == null) 0 else this.buildingCd.hashCode())
        result = prime * result + (if (this.effectiveDate == null) 0 else this.effectiveDate.hashCode())
        result = prime * result + (if (this.contractOutputManagementNo == null) 0 else this.contractOutputManagementNo.hashCode())
        result = prime * result + (if (this.bulkLeaseType == null) 0 else this.bulkLeaseType.hashCode())
        return result
    }
}
