-- TABLE: PARKING_VEHICLE_INFO_FILE(駐車場車種情報ファイル)

CREATE TABLE PARKING_VEHICLE_INFO_FILE(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(10)                   
,    CREATION_PROGRAM_ID                          varchar(10)                   
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(10)                   
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    TENANT_CONTRACT_NUMBER                       varchar(8)        NOT NULL    
,    ROOM_CODE                                    varchar(5)                    
,    TANDEM_SIGN                                  varchar(1)                    
,    LAND_TRANSPORT_NAME_1                        varchar(14)                   
,    TYPE_1                                       varchar(9)                    
,    BUSINESS_CATEGORY_1                          varchar(4)                    
,    LEFT_NUMBER_1                                varchar(5)                    
,    RIGHT_NUMBER_1                               varchar(5)                    
,    MANUFACTURER_DIVISION_1                      numeric(2,0)                  
,    CAR_MODEL_NAME_1                             varchar(44)                   
,    LIGHT_VEHICLE_SIGN_1                         numeric(1,0)                  
,    LAND_TRANSPORT_NAME_2                        varchar(14)                   
,    TYPE_2                                       varchar(9)                    
,    BUSINESS_CATEGORY_2                          varchar(4)                    
,    LEFT_NUMBER_2                                varchar(5)                    
,    RIGHT_NUMBER_2                               varchar(5)                    
,    MANUFACTURER_DIVISION_2                      numeric(2,0)                  
,    CAR_MODEL_NAME_2                             varchar(44)                   
,    LIGHT_VEHICLE_SIGN_2                         numeric(1,0)                  
,    LAND_TRANSPORT_NAME_3                        varchar(14)                   
,    TYPE_3                                       varchar(9)                    
,    BUSINESS_CATEGORY_3                          varchar(4)                    
,    LEFT_NUMBER_3                                varchar(5)                    
,    RIGHT_NUMBER_3                               varchar(5)                    
,    MANUFACTURER_DIVISION_3                      numeric(2,0)                  
,    CAR_MODEL_NAME_3                             varchar(44)                   
,    LIGHT_VEHICLE_SIGN_3                         numeric(1,0)                  
,    LAND_TRANSPORT_NAME_4                        varchar(14)                   
,    TYPE_4                                       varchar(9)                    
,    BUSINESS_CATEGORY_4                          varchar(4)                    
,    LEFT_NUMBER_4                                varchar(5)                    
,    RIGHT_NUMBER_4                               varchar(5)                    
,    MANUFACTURER_DIVISION_4                      numeric(2,0)                  
,    CAR_MODEL_NAME_4                             varchar(44)                   
,    LIGHT_VEHICLE_SIGN_4                         numeric(1,0)                  
,    LAND_TRANSPORT_NAME_5                        varchar(14)                   
,    TYPE_5                                       varchar(9)                    
,    BUSINESS_CATEGORY_5                          varchar(4)                    
,    LEFT_NUMBER_5                                varchar(5)                    
,    RIGHT_NUMBER_5                               varchar(5)                    
,    MANUFACTURER_DIVISION_5                      numeric(2,0)                  
,    CAR_MODEL_NAME_5                             varchar(44)                   
,    LIGHT_VEHICLE_SIGN_5                         numeric(1,0)                  
,    PARKING_CERT_ISSUE_SIGN_1                    numeric(1,0)                  
,    PARKING_CERT_COMMENT_1                       varchar(75)                   
,    PARKING_CERT_ISSUE_SIGN_2                    numeric(1,0)                  
,    PARKING_CERT_COMMENT_2                       varchar(75)                   
,    PARKING_CERT_ISSUE_SIGN_3                    numeric(1,0)                  
,    PARKING_CERT_COMMENT_3                       varchar(75)                   
,    PARKING_CERT_ISSUE_SIGN_4                    numeric(1,0)                  
,    PARKING_CERT_COMMENT_4                       varchar(75)                   
,    PARKING_CERT_ISSUE_SIGN_5                    numeric(1,0)                  
,    PARKING_CERT_COMMENT_5                       varchar(75)                   
,    CONSTRAINT PK_PARKING_VEHICLE_INFO_FILE PRIMARY KEY (TENANT_CONTRACT_NUMBER)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PARKING_VEHICLE_INFO_FILE IS '駐車場車種情報ファイル 既存システム物理名: ERA30P';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: ERASTD 1台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.CREATION_TIME IS '作成時刻 既存システム物理名: ERASTT 1台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.CREATOR IS '作成者 既存システム物理名: ERASTS 1台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.CREATION_PROGRAM_ID IS '作成プログラムID 既存システム物理名: ERAPID 1台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: EME03D 1台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.UPDATE_TIME IS '更新時刻 既存システム物理名: EME03T 1台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.UPDATER IS '更新者 既存システム物理名: EME04C 1台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EMEKNF 1台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER IS 'テ契番号 既存システム物理名: ERA05C 2台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.ROOM_CODE IS '部屋コード 既存システム物理名: ERA06C 2台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.TANDEM_SIGN IS '縦列サイン 既存システム物理名: ERA07C 2台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LAND_TRANSPORT_NAME_1 IS '陸事名 既存システム物理名: ERA08C 2台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.TYPE_1 IS '種別 既存システム物理名: ERA09C 2台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.BUSINESS_CATEGORY_1 IS '業態 既存システム物理名: ERA10C 2台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LEFT_NUMBER_1 IS '左ナンバー 既存システム物理名: ERA11C 2台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.RIGHT_NUMBER_1 IS '右ナンバー 既存システム物理名: ERA12C 2台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.MANUFACTURER_DIVISION_1 IS 'メーカー区分 既存システム物理名: ERA13C 3台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.CAR_MODEL_NAME_1 IS '車種名 既存システム物理名: ERA14C 3台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LIGHT_VEHICLE_SIGN_1 IS '軽サイン 既存システム物理名: ERA15C 3台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LAND_TRANSPORT_NAME_2 IS '陸事名 既存システム物理名: ERA16C 3台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.TYPE_2 IS '種別 既存システム物理名: ERA17C 3台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.BUSINESS_CATEGORY_2 IS '業態 既存システム物理名: ERA18C 3台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LEFT_NUMBER_2 IS '左ナンバー 既存システム物理名: ERA19C 3台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.RIGHT_NUMBER_2 IS '右ナンバー 既存システム物理名: ERA20C 3台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.MANUFACTURER_DIVISION_2 IS 'メーカー区分 既存システム物理名: ERA23C 4台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.CAR_MODEL_NAME_2 IS '車種名 既存システム物理名: ERA24C 4台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LIGHT_VEHICLE_SIGN_2 IS '軽サイン 既存システム物理名: ERA25C 4台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LAND_TRANSPORT_NAME_3 IS '陸事名 既存システム物理名: ERA26C 4台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.TYPE_3 IS '種別 既存システム物理名: ERA27C 4台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.BUSINESS_CATEGORY_3 IS '業態 既存システム物理名: ERA28C 4台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LEFT_NUMBER_3 IS '左ナンバー 既存システム物理名: ERA29C 4台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.RIGHT_NUMBER_3 IS '右ナンバー 既存システム物理名: ERA30C 4台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.MANUFACTURER_DIVISION_3 IS 'メーカー区分 既存システム物理名: ERA33C 5台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.CAR_MODEL_NAME_3 IS '車種名 既存システム物理名: ERA34C 5台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LIGHT_VEHICLE_SIGN_3 IS '軽サイン 既存システム物理名: ERA35C 5台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LAND_TRANSPORT_NAME_4 IS '陸事名 既存システム物理名: ERA36C 5台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.TYPE_4 IS '種別 既存システム物理名: ERA37C 5台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.BUSINESS_CATEGORY_4 IS '業態 既存システム物理名: ERA38C 5台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LEFT_NUMBER_4 IS '左ナンバー 既存システム物理名: ERA39C 5台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.RIGHT_NUMBER_4 IS '右ナンバー 既存システム物理名: ERA40C 5台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.MANUFACTURER_DIVISION_4 IS 'メーカー区分 既存システム物理名: ERA43C 1台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.CAR_MODEL_NAME_4 IS '車種名 既存システム物理名: ERA44C 1台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LIGHT_VEHICLE_SIGN_4 IS '軽サイン 既存システム物理名: ERA45C 2台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LAND_TRANSPORT_NAME_5 IS '陸事名 既存システム物理名: ERA46C 2台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.TYPE_5 IS '種別 既存システム物理名: ERA47C 3台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.BUSINESS_CATEGORY_5 IS '業態 既存システム物理名: ERA48C 3台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LEFT_NUMBER_5 IS '左ナンバー 既存システム物理名: ERA49C 4台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.RIGHT_NUMBER_5 IS '右ナンバー 既存システム物理名: ERA50C 4台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.MANUFACTURER_DIVISION_5 IS 'メーカー区分 既存システム物理名: ERA53C 5台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.CAR_MODEL_NAME_5 IS '車種名 既存システム物理名: ERA54C 5台目';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.LIGHT_VEHICLE_SIGN_5 IS '軽サイン 既存システム物理名: ERA55C';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.PARKING_CERT_ISSUE_SIGN_1 IS '車庫証明発給サイン 既存システム物理名: ERA56S';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.PARKING_CERT_COMMENT_1 IS '車庫証明コメント 既存システム物理名: ERA57X';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.PARKING_CERT_ISSUE_SIGN_2 IS '車庫証明発給サイン 既存システム物理名: ERA58S';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.PARKING_CERT_COMMENT_2 IS '車庫証明コメント 既存システム物理名: ERA59X';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.PARKING_CERT_ISSUE_SIGN_3 IS '車庫証明発給サイン 既存システム物理名: ERA60S';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.PARKING_CERT_COMMENT_3 IS '車庫証明コメント 既存システム物理名: ERA61X';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.PARKING_CERT_ISSUE_SIGN_4 IS '車庫証明発給サイン 既存システム物理名: ERA62S';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.PARKING_CERT_COMMENT_4 IS '車庫証明コメント 既存システム物理名: ERA63X';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.PARKING_CERT_ISSUE_SIGN_5 IS '車庫証明発給サイン 既存システム物理名: ERA64S';
COMMENT ON COLUMN PARKING_VEHICLE_INFO_FILE.PARKING_CERT_COMMENT_5 IS '車庫証明コメント 既存システム物理名: ERA65X';
