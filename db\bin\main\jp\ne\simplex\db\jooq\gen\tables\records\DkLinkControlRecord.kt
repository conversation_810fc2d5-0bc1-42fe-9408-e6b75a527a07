/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.DkLinkControlTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.DkLinkControlPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * DKリンク制御 既存システム物理名: -
 */
@Suppress("UNCHECKED_CAST")
open class DkLinkControlRecord private constructor() : UpdatableRecordImpl<DkLinkControlRecord>(DkLinkControlTable.DK_LINK_CONTROL) {

    open var key: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var value: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    open var comment: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised DkLinkControlRecord
     */
    constructor(key: String, value: String? = null, comment: String? = null): this() {
        this.key = key
        this.value = value
        this.comment = comment
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised DkLinkControlRecord
     */
    constructor(value: DkLinkControlPojo?): this() {
        if (value != null) {
            this.key = value.key
            this.value = value.value
            this.comment = value.comment
            resetChangedOnNotNull()
        }
    }
}
