package jp.ne.simplex.application.controller.client.parking.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.CancelParkingReservation
import jp.ne.simplex.application.model.ParkingReservation
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.shared.StringExtension.Companion.isAllCharsFullWidthConvertible

data class ClientParkingReservationCancelRequest(

    @JsonProperty("parkingReservationId")
    @field:Schema(
        description = "駐車場予約を一意に示すID",
        example = "91c7e3d0-53ab-5f26-8f48-7794e2ba8150"
    )
    val parkingReservationId: String,

    @JsonProperty("remarks")
    @field:Schema(description = "備考", example = "備考")
    val remarks: String?,

    ) {
    fun toServiceInterface(): CancelParkingReservation {

        // 予約メモの文字列チェック(全角or全角に変換できるもののみ受け付ける)
        if (remarks != null && !remarks.isAllCharsFullWidthConvertible()) {
            throw ClientValidationException(ErrorMessage.INVALID_CHAR.format("予約メモ"))
        }

        try {
            return CancelParkingReservation.of(
                id = ParkingReservation.Id.of(parkingReservationId),
                remarks = remarks?.let { ParkingReservation.Remarks.of(it) },
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }
}
