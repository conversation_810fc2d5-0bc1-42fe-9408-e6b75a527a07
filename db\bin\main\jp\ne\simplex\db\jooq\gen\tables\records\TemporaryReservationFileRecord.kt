/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.TemporaryReservationFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.TemporaryReservationFilePojo

import org.jooq.impl.TableRecordImpl


/**
 * 仮押さえファイル 既存システム物理名: ERA03P
 */
@Suppress("UNCHECKED_CAST")
open class TemporaryReservationFileRecord private constructor() : TableRecordImpl<TemporaryReservationFileRecord>(TemporaryReservationFileTable.TEMPORARY_RESERVATION_FILE) {

    open var buildingCd: String?
        set(value): Unit = set(0, value)
        get(): String? = get(0) as String?

    open var roomCd: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    open var status: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var applicationScheduledDate: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var state: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var applicationScheduledPersonCd: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var customerRepCd: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var customerRepBranchCd: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var customerRepShozokuCd: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var comment: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var contractFormECode: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var listComment: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var registrationDate: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var linkCdRegistrationTime: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var otherCompanyFlag: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var otherCompanyMemberId: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var otherCompanyName: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var otherCompanyStoreName: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var otherCompanyRepName: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    /**
     * Create a detached, initialised TemporaryReservationFileRecord
     */
    constructor(buildingCd: String? = null, roomCd: String? = null, status: String? = null, applicationScheduledDate: String? = null, state: String? = null, applicationScheduledPersonCd: String? = null, customerRepCd: String? = null, customerRepBranchCd: String? = null, customerRepShozokuCd: String? = null, comment: String? = null, contractFormECode: String? = null, listComment: String? = null, registrationDate: String? = null, linkCdRegistrationTime: String? = null, otherCompanyFlag: String? = null, otherCompanyMemberId: String? = null, otherCompanyName: String? = null, otherCompanyStoreName: String? = null, otherCompanyRepName: String? = null): this() {
        this.buildingCd = buildingCd
        this.roomCd = roomCd
        this.status = status
        this.applicationScheduledDate = applicationScheduledDate
        this.state = state
        this.applicationScheduledPersonCd = applicationScheduledPersonCd
        this.customerRepCd = customerRepCd
        this.customerRepBranchCd = customerRepBranchCd
        this.customerRepShozokuCd = customerRepShozokuCd
        this.comment = comment
        this.contractFormECode = contractFormECode
        this.listComment = listComment
        this.registrationDate = registrationDate
        this.linkCdRegistrationTime = linkCdRegistrationTime
        this.otherCompanyFlag = otherCompanyFlag
        this.otherCompanyMemberId = otherCompanyMemberId
        this.otherCompanyName = otherCompanyName
        this.otherCompanyStoreName = otherCompanyStoreName
        this.otherCompanyRepName = otherCompanyRepName
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised TemporaryReservationFileRecord
     */
    constructor(value: TemporaryReservationFilePojo?): this() {
        if (value != null) {
            this.buildingCd = value.buildingCd
            this.roomCd = value.roomCd
            this.status = value.status
            this.applicationScheduledDate = value.applicationScheduledDate
            this.state = value.state
            this.applicationScheduledPersonCd = value.applicationScheduledPersonCd
            this.customerRepCd = value.customerRepCd
            this.customerRepBranchCd = value.customerRepBranchCd
            this.customerRepShozokuCd = value.customerRepShozokuCd
            this.comment = value.comment
            this.contractFormECode = value.contractFormECode
            this.listComment = value.listComment
            this.registrationDate = value.registrationDate
            this.linkCdRegistrationTime = value.linkCdRegistrationTime
            this.otherCompanyFlag = value.otherCompanyFlag
            this.otherCompanyMemberId = value.otherCompanyMemberId
            this.otherCompanyName = value.otherCompanyName
            this.otherCompanyStoreName = value.otherCompanyStoreName
            this.otherCompanyRepName = value.otherCompanyRepName
            resetChangedOnNotNull()
        }
    }
}
