package jp.ne.simplex.application.controller.client.exclusive.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.model.ExclusivePropertiesSearch
import jp.ne.simplex.application.model.ExclusiveProperty
import jp.ne.simplex.application.model.Room
import jp.ne.simplex.application.model.PublishStatus
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.enums.PerPage
import jp.ne.simplex.shared.enums.SortOrder

@Schema(description = "先行公開検索のリクエスト")
data class ClientExclusivePropertiesSearchRequest(
    @JsonProperty("page")
    @field:Schema(description = "表示ページ", example = "1")
    val page: Int,

    @JsonProperty("perPage")
    @field:Schema(description = "各ページに表示する件数", example = "25")
    val perPage: Int,

    @JsonProperty("sortBy")
    @field:Schema(
        description = "ソートする項目（複数指定可能）",
        example = """["BUILDING_CODE", "ROOM_CD"]"""
    )
    val sortBy: List<String>,

    @JsonProperty("sortOrder")
    @field:Schema(description = "ソート順", example = "ASC")
    val sortOrder: String,

    @JsonProperty("buildingCode")
    @field:Schema(description = "建物CD", example = "000000000")
    val buildingCode: String?,

    @JsonProperty("roomCode")
    @field:Schema(description = "部屋CD", example = "00000")
    val roomCode: String?,

    @JsonProperty("exclusiveFrom")
    @field:Schema(description = "先行期間From", example = "20190116")
    val exclusiveFrom: String?,

    @JsonProperty("exclusiveTo")
    @field:Schema(description = "先行期間To", example = "20191216")
    val exclusiveTo: String?,

    @JsonProperty("exclusiveTarget")
    @field:Schema(description = "先行先", example = "リーシング")
    val exclusiveTarget: String?,

    @JsonProperty("listingSituationTypeList")
    @field:Schema(description = "掲載状況", example = "1")
    val listingSituationTypeList: List<String>,

    @JsonProperty("companyTypeList")
    @field:Schema(description = "先行先種別", example = "1")
    // React側で空のリストを送ることが難しいため、空のリストも許容したい場合はnullも許容する
    val companyTypeList: List<String>?,

    @JsonProperty("propertyName")
    @field:Schema(description = "物件名", example = "カルム○ア○タ○オ○ 406")
    val propertyName: String?,

    @JsonProperty("createDate")
    @field:Schema(description = "作成日", example = "20190116")
    val createDate: String?,

    @JsonProperty("creator")
    @field:Schema(description = "作成者", example = "成田 空港")
    val creator: String?,

    @JsonProperty("officeCode")
    @field:Schema(description = "営業所", example = "名古屋")
    val officeCode: String?,
) {

    // Service層の Interface に変換する
    fun toServiceInterface(): ExclusivePropertiesSearch {

        try {
            return ExclusivePropertiesSearch(
                page = page,
                perPage = PerPage.fromValue(perPage)!!,
                sortBy = sortBy.map { key -> ExclusivePropertiesSearch.SortBy.fromValue(key)!! },
                sortOrder = SortOrder.fromString(sortOrder),
                idList = null,
                buildingCode = buildingCode,
                roomCode = roomCode,
                exclusiveFrom = exclusiveFrom?.yyyyMMdd(),
                exclusiveTo = exclusiveTo?.yyyyMMdd(),
                exclusiveTarget = exclusiveTarget,
                listingSituationTypeList = listingSituationTypeList.map { listingSituationType ->
                    PublishStatus.fromValue(
                        listingSituationType.toByte()
                    )!!
                },
                companyTypeList = companyTypeList?.map {
                    ExclusiveProperty.CompanyType.fromValue(it.toInt())!!
                },
                propertyName = propertyName,
                createDate = createDate?.yyyyMMdd(),
                creator = creator,
                officeCode = officeCode,

            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }
}
