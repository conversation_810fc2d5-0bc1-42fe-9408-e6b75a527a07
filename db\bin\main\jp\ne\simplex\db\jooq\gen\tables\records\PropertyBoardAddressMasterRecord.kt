/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.PropertyBoardAddressMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.PropertyBoardAddressMasterPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 物件ボード用住所M 既存システム物理名: EMEADP
 */
@Suppress("UNCHECKED_CAST")
open class PropertyBoardAddressMasterRecord private constructor() : UpdatableRecordImpl<PropertyBoardAddressMasterRecord>(PropertyBoardAddressMasterTable.PROPERTY_BOARD_ADDRESS_MASTER) {

    open var prefectureCd: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var cityCd: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var prefectureName: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var cityName: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var prefectureCityName: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised PropertyBoardAddressMasterRecord
     */
    constructor(prefectureCd: String, cityCd: String, prefectureName: String? = null, cityName: String? = null, prefectureCityName: String? = null): this() {
        this.prefectureCd = prefectureCd
        this.cityCd = cityCd
        this.prefectureName = prefectureName
        this.cityName = cityName
        this.prefectureCityName = prefectureCityName
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised PropertyBoardAddressMasterRecord
     */
    constructor(value: PropertyBoardAddressMasterPojo?): this() {
        if (value != null) {
            this.prefectureCd = value.prefectureCd
            this.cityCd = value.cityCd
            this.prefectureName = value.prefectureName
            this.cityName = value.cityName
            this.prefectureCityName = value.prefectureCityName
            resetChangedOnNotNull()
        }
    }
}
