package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.Parking
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.repository.db.extension.ParkingDetailEx.Companion.applyEBoard
import jp.ne.simplex.application.repository.db.pojos.AggregateTenantContractPojo
import jp.ne.simplex.application.repository.db.pojos.ParkingDetailPojo
import jp.ne.simplex.application.repository.db.pojos.ParkingDetailPojo.Companion.toParkingList
import jp.ne.simplex.application.repository.db.pojos.ParkingDetailPojo.Companion.toParkingOnlyBuildingList
import jp.ne.simplex.application.repository.db.pojos.PreviousParkingTenantContractPojo
import jp.ne.simplex.application.repository.db.pojos.PropertyTenantContractPojo
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.db.jooq.gen.tables.references.*
import org.jooq.DSLContext
import org.jooq.impl.DSL.*
import org.springframework.stereotype.Repository

@Repository
class ParkingDetailsRepository(
    private val context: DSLContext,
    private val parkingRepository: ParkingRepositoryInterface,
    private val parkingReservationRepository: ParkingReservationRepositoryInterface,
) :
    ParkingDetailsRepositoryInterface {

    companion object {
        private const val LATEST_TENANT_CONTRACT_SQL = "(SELECT" +
                "  p.building_code," +
                "  p.parking_code," +
                "  substring(max(" +
                "    case" +
                "      WHEN(" +
                "        p.logical_delete_sign != 1" +
                "        OR g.tenant_contract_number IS NULL" +
                "      ) THEN case" +
                "        WHEN p.contract_effective_start_date = 0 THEN 99999999" +
                "        ELSE p.contract_effective_start_date" +
                "      end" +
                "      ELSE case" +
                "        WHEN g.contract_effective_start_date = 0 THEN 99999999" +
                "        ELSE g.contract_effective_start_date" +
                "      end" +
                "    end || p.tenant_contract_number" +
                "  ), 9, 8) AS ecbkey" +
                " FROM" +
                "  app.tenant_contract AS p" +
                "  LEFT JOIN app.tenant_contract AS g" +
                "   ON p.aggregate_contract_number = g.tenant_contract_number AND g.building_code LIKE '%s'" +
                " WHERE" +
                "  p.building_code LIKE '%s'" +
                "  AND p.parking_code IS NOT NULL" +
                "  AND (" +
                "    p.logical_delete_sign != 1" +
                "    OR p.aggregate_contract_number IS NOT NULL" +
                "  )" +
                "  AND p.current_state_division <> '27'" +
                "  AND (" +
                "    p.current_state_division <> '90'" +
                "    OR p.contract_effective_start_date != 0" +
                "  )" +
                "  AND (" +
                "    p.move_out_date = 0" +
                "    OR (" +
                "      p.move_out_date != 0" +
                "      AND p.move_in_scheduled_date <= p.move_out_date" +
                "    )" +
                "  )" +
                "  AND (" +
                "    g.current_state_division IS NULL" +
                "    OR g.logical_delete_sign = 0" +
                "  )" +
                "  AND (" +
                "    g.current_state_division IS NULL" +
                "    OR g.current_state_division <> '27'" +
                "  )" +
                "  AND (" +
                "    g.current_state_division IS NULL" +
                "    OR g.current_state_division <> '90'" +
                "    OR g.contract_effective_start_date != 0" +
                "  )" +
                "  AND (" +
                "    g.current_state_division IS NULL" +
                "    OR g.move_out_date = 0" +
                "    OR (" +
                "      g.move_out_date != 0" +
                "      AND g.move_in_scheduled_date <= g.move_out_date" +
                "    )" +
                "  )" +
                " GROUP BY" +
                "  p.building_code," +
                "  p.parking_code) as y"
    }

    /** 駐車場詳細区画一覧取得（受注コード指定） */
    override fun findParkingDetailByOrderCode(
        orderCode: Building.OrderCode,
        forClient: Boolean,
        authInfo: AuthInfo?,
    ): List<Parking> {
        val pojoList = findParkingDetailPojoByOrderCode(orderCode)
        val thirtyFiveYearBulkBuildings =
            when (forClient) {
                true -> parkingRepository.getThirtyFiveYearBulkBuildingContract(orderCode)
                false -> emptyList()
            }
        // 区画が全て非表示とされた建物棟も返却するために別途変換しておく。
        val parkingBuildings = pojoList.toParkingOnlyBuildingList(thirtyFiveYearBulkBuildings)
        val parkingReservationMap =
            when (pojoList.isNotEmpty()) {
                true -> parkingReservationRepository.findActiveParkingReservationByOrderCode(
                    orderCode
                ).groupBy { ParkingLot.Id(it.buildingCode, it.parkingLotCode!!) }

                false -> emptyMap()
            }
        //  変換後の建物棟パターンは以下の通り。
        //  1. 区画を持つ建物棟（リストにいる）
        //  2. 区画を最初からもっていない建物棟（リストにいる）
        //  3. 区画を持っていたが、全て非表示と判断された建物棟（リストにいない）
        val parkingMap = pojoList.applyEBoard(this, false, forClient)
            .toParkingList(parkingReservationMap, thirtyFiveYearBulkBuildings, authInfo)
            .associateBy { it.building.code }
        return parkingBuildings.map { parkingBuilding ->
            parkingMap[parkingBuilding.building.code] ?: parkingBuilding // 上記3のケースをここでカバーする
        }.filter { forClient || it.parkingLotList.isNotEmpty() } // 外部APIの場合は区画のある建物のみを返却する
    }

    override fun findParkingDetailPojoByOrderCode(orderCode: Building.OrderCode): List<ParkingDetailPojo> {
        // DK_LINK_CONTROLテーブルのサブクエリ
        val fDkLinkControl = context.select(
            coalesce(DK_LINK_CONTROL.VALUE, inline("0"))
        ).from(DK_LINK_CONTROL)
            .where(DK_LINK_CONTROL.KEY.eq("ALLOW_ALL_PARKING_LOT_AVAILABILITY_EDIT"))

        // PARKINGテーブルのサブクエリ（既存）
        val fParking = context.select(
            PARKING.BUILDING_CODE,
            PARKING.PARKING_LOT_NUMBER,
            PARKING.PARKING_LOT_CODE,
            right(PARKING.BUILDING_CODE, 2).`as`("TOU"),
            PARKING.LOGICAL_DELETE_FLAG,
            PARKING.CONSOLIDATED_BUILDING_CODE,
            PARKING.CONSOLIDATED_PARKING_CODE,
            PARKING.TRANSFERRED_BUILDING_CODE,
            PARKING.BULK_LEASE_FLAG,
            PARKING.PARKING_CATEGORY,
            PARKING.OFF_SITE_PARKING_CATEGORY,
            field(fDkLinkControl).`as`("ALLOW_ALL_PARKING_LOT_AVAILABILITY_EDIT")
        )
            .from(PARKING)
            .where(
                and(
                    PARKING.BUILDING_CODE.like("${orderCode.value}%")
                        .or(PARKING.CONSOLIDATED_BUILDING_CODE.like("${orderCode.value}%"))
                )
                    .and(
                        PARKING.LOGICAL_DELETE_FLAG.eq(0)
                            .or(PARKING.CONSOLIDATED_BUILDING_CODE.isNotNull())
                    )
            )

        // BUILDING_MASTERテーブルのサブクエリ（既存）
        val fBuildingMaster = context.select(
            BUILDING_MASTER.BUILDING_CODE,
            BUILDING_MASTER.BUILDING_NAME,
            BUILDING_MASTER.BULK_LEASE_FLAG,
            BUILDING_MASTER.ORDER_CODE,
            BUILDING_MASTER.ADDRESS_DETAIL,
            BUILDING_MASTER.PREFECTURE_CODE,
            BUILDING_MASTER.CITY_CODE,
            BUILDING_MASTER.TOWN_CODE,
            BUILDING_MASTER.LANDLORD_CODE,
            BUILDING_MASTER.COMPLETION_DELIVERY_DATE,
            `when`(
                DAITO_BULK_LEASE_CONTRACT_DETAILS.THIRTY_FIVE_YEAR_LUMP_SUM_CATEGORY.isNotNull(),
                DAITO_BULK_LEASE_CONTRACT_DETAILS.THIRTY_FIVE_YEAR_LUMP_SUM_CATEGORY.cast(String::class.java)
            ).`when`(
                CONTRACT_FILE.ORIGINAL_CONTRACT_DATE.isNotNull()
                    .and(CONTRACT_FILE.ORIGINAL_CONTRACT_DATE.ge(20151201)),
                inline("2")
            ).otherwise(inline("")).`as`("THIRTY_FIVE_YEAR_LUMP_SUM_CATEGORY"),
            BUILDING_MASTER.MARKETING_BRANCH_OFFICE_CD
        ).from(BUILDING_MASTER)
            .leftJoin(DAITO_BULK_LEASE_CONTRACT_DETAILS)
            .on(BUILDING_MASTER.BUILDING_CODE.eq(DAITO_BULK_LEASE_CONTRACT_DETAILS.BUILDING_CD))
            .and(
                DAITO_BULK_LEASE_CONTRACT_DETAILS.DELETE_SIGN.isNull()
                    .or(DAITO_BULK_LEASE_CONTRACT_DETAILS.DELETE_SIGN.eq("0"))
            )
            .leftJoin(CONTRACT_FILE)
            .on(BUILDING_MASTER.ORDER_CODE.cast(Int::class.java).eq(CONTRACT_FILE.CONTRACT_CD))
            .and(CONTRACT_FILE.RECORD_DELETE_FLAG.eq(0))
            .and(CONTRACT_FILE.CONTRACT_ADDITIONAL_CD.eq(0))
            .where(
                BUILDING_MASTER.BUILDING_CODE.like("${orderCode.value}%")
            )
            .and(
                BUILDING_MASTER.LOGICAL_DELETE_FLAG.isNull()
                    .or(BUILDING_MASTER.LOGICAL_DELETE_FLAG.eq(0))
            )

        // PARKING_VEHICLE_INFO_FILEテーブルのサブクエリ（既存）
        val fParkingVehicleInfoFile = context.select(
            PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER,
            PARKING_VEHICLE_INFO_FILE.LAND_TRANSPORT_NAME_1,
            PARKING_VEHICLE_INFO_FILE.TYPE_1,
            PARKING_VEHICLE_INFO_FILE.BUSINESS_CATEGORY_1,
            PARKING_VEHICLE_INFO_FILE.LEFT_NUMBER_1,
            PARKING_VEHICLE_INFO_FILE.RIGHT_NUMBER_1,
            PARKING_VEHICLE_INFO_FILE.MANUFACTURER_DIVISION_1,
            PARKING_VEHICLE_INFO_FILE.CAR_MODEL_NAME_1,
            PARKING_VEHICLE_INFO_FILE.LIGHT_VEHICLE_SIGN_1,
            PARKING_VEHICLE_INFO_FILE.TANDEM_SIGN,
            PARKING_VEHICLE_INFO_FILE.PARKING_CERT_ISSUE_SIGN_1,
            PARKING_VEHICLE_INFO_FILE.PARKING_CERT_COMMENT_1,
        ).from(PARKING_VEHICLE_INFO_FILE)

        // BULK_LEASE_PARKINGテーブルのサブクエリ（既存）
        val fBulkLeaseParking = context.select(
            BULK_LEASE_PARKING.BUILDING_CODE,
            BULK_LEASE_PARKING.PARKING_CODE,
            BULK_LEASE_PARKING.ASSESSMENT_DIVISION
        ).from(BULK_LEASE_PARKING)
            .where(BULK_LEASE_PARKING.BUILDING_CODE.like("${orderCode.value}%"))

        // OFF_SITE_PARKINGテーブルのサブクエリ（既存）
        val fOffSiteParking = context.select(
            OFF_SITE_PARKING.ORDER_CD,
            OFF_SITE_PARKING.PARKING_CD,
            OFF_SITE_PARKING.DISTANCE
        ).from(OFF_SITE_PARKING)
            .where(OFF_SITE_PARKING.ORDER_CD.eq(orderCode.value))
            .and(
                OFF_SITE_PARKING.LOGICAL_DELETE_SIGN.isNull()
                    .or(OFF_SITE_PARKING.LOGICAL_DELETE_SIGN.eq(0))
            )

        // DAITO_BULK_LEASE_CONTRACT_DETAILSテーブルのサブクエリ（既存）
        val fDaitoBulkLeaseContractDetails = context.select(
            DAITO_BULK_LEASE_CONTRACT_DETAILS.BUILDING_CD,
            DAITO_BULK_LEASE_CONTRACT_DETAILS.THIRTY_FIVE_YEAR_LUMP_SUM_CATEGORY
        ).from(DAITO_BULK_LEASE_CONTRACT_DETAILS)
            .where(DAITO_BULK_LEASE_CONTRACT_DETAILS.BUILDING_CD.like("${orderCode.value}%"))
            .and(
                DAITO_BULK_LEASE_CONTRACT_DETAILS.DELETE_SIGN.isNull()
                    .or(DAITO_BULK_LEASE_CONTRACT_DETAILS.DELETE_SIGN.eq("0"))
            )

        // CONTRACT_FILEテーブルのサブクエリ（既存）
        val fContractFile = context.selectDistinct(
            inline("2")
        ).from(CONTRACT_FILE)
            .where(
                fBuildingMaster.field(BUILDING_MASTER.ORDER_CODE)?.cast(Int::class.java)
                    ?.eq(CONTRACT_FILE.CONTRACT_CD)
            )

        // TENANTテーブルのサブクエリ（既存）
        val fTenant = context.select(
            TENANT.TENANT_CODE,
            TENANT.TENANT_NAME_KANJI,
        ).from(TENANT)

        val fTenantContract = context.select(
            TENANT_CONTRACT.TENANT_CONTRACT_NUMBER,
            TENANT_CONTRACT.BUILDING_CODE,
            TENANT_CONTRACT.PARKING_CODE,
            TENANT_CONTRACT.ROOM_CODE,
            TENANT_CONTRACT.TENANT_NAME,
            TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE,
            TENANT_CONTRACT.CONTRACT_EXPIRY_DATE,
            TENANT_CONTRACT.CONTRACT_EFFECTIVE_END_DATE,
            TENANT_CONTRACT.CURRENT_STATE_DIVISION,
            TENANT_CONTRACT.MODIFICATION_STATE_DIVISION,
            TENANT_CONTRACT.MOVE_IN_START_PROCESSED_SIGN,
            TENANT_CONTRACT.MOVE_OUT_DATE,
            TENANT_CONTRACT.VACATE_SCHEDULED_DATE,
            TENANT_CONTRACT.CANCELLATION_SIGN,
            TENANT_CONTRACT.LOGICAL_DELETE_SIGN,
            TENANT_CONTRACT.TENANT_CODE,
            TENANT_CONTRACT.VACATE_NOTICE_DATE,
            TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER
        ).from(TENANT_CONTRACT)
            .where(TENANT_CONTRACT.BUILDING_CODE.like("${orderCode.value}%"))

        // TENANT_CONTRACTテーブルのサブクエリ（既存）
        val fLatestTenantContract = context.select(
            fTenantContract.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER),
            fTenantContract.field(TENANT_CONTRACT.BUILDING_CODE),
            fTenantContract.field(TENANT_CONTRACT.PARKING_CODE),
            fTenantContract.field(TENANT_CONTRACT.ROOM_CODE),
            fTenantContract.field(TENANT_CONTRACT.TENANT_NAME),
            fTenantContract.field(TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE),
            fTenant.field(TENANT.TENANT_NAME_KANJI),
            fTenantContract.field(TENANT_CONTRACT.CONTRACT_EXPIRY_DATE),
            fTenantContract.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_END_DATE),
            fTenantContract.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION),
            fTenantContract.field(TENANT_CONTRACT.MODIFICATION_STATE_DIVISION),
            fTenantContract.field(TENANT_CONTRACT.MOVE_IN_START_PROCESSED_SIGN),
            fTenantContract.field(TENANT_CONTRACT.MOVE_OUT_DATE),
            fTenantContract.field(TENANT_CONTRACT.VACATE_SCHEDULED_DATE),
            fTenantContract.field(TENANT_CONTRACT.CANCELLATION_SIGN),
            fTenantContract.field(TENANT_CONTRACT.LOGICAL_DELETE_SIGN),
            fTenantContract.field(TENANT_CONTRACT.TENANT_CODE),
            fTenantContract.field(TENANT_CONTRACT.VACATE_NOTICE_DATE),
            fTenantContract.field(TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER)
        ).from(fTenantContract)
            .innerJoin(
                raw(
                    String.format(
                        LATEST_TENANT_CONTRACT_SQL,
                        "${orderCode.value}%",
                        "${orderCode.value}%"
                    )
                )
            )
            .on(
                fTenantContract.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER)
                    ?.eq(field("y.ecbkey", String::class.java))
            )
            .leftJoin(fTenant)
            .on(
                substring(fTenantContract.field(TENANT_CONTRACT.TENANT_CODE)!!, 1, 8).concat("0")
                    .eq(fTenant.field(TENANT.TENANT_CODE))
            )
            .where(
                fTenantContract.field(TENANT_CONTRACT.BUILDING_CODE)?.like("${orderCode.value}%")
            )

        // ADDRESS_MASTERテーブルのサブクエリ
        val fAddressMaster = context.select(
            ADDRESS_MASTER.ADDRESS_CODE,
            ADDRESS_MASTER.POSTAL_CODE,
            ADDRESS_MASTER.PREFECTURE_KANJI_NAME,
            ADDRESS_MASTER.CITY_KANJI_NAME,
            ADDRESS_MASTER.TOWN_KANJI_NAME,
        ).from(ADDRESS_MASTER)

        // CUSTOMERテーブルのサブクエリ
        val fCustomer = context.select(
            CUSTOMER.INTEGRATED_CLIENT_CODE,
            CUSTOMER.CLIENT_NAME_KANJI
        ).from(CUSTOMER)

        // BUILDING_INFO_MASTERのサブクエリ
        val fBuildingInfoMaster = context.select(
            BUILDING_INFO_MASTER.MARKETING_BRANCH_OFFICE_CD,
            BUILDING_INFO_MASTER.BUILDING_CODE,
        ).from(BUILDING_INFO_MASTER)
            .where(BUILDING_INFO_MASTER.BUILDING_CODE.like("${orderCode.value}%"))

        // PARKING_INFO_MASTERテーブルのサブクエリ
        val fParkingInfo = context.select(
            PARKING_INFO_MASTER.BUILDING_CODE,
            PARKING_INFO_MASTER.PARKING_CODE,
            PARKING_INFO_MASTER.SPECIAL_CONTRACT_FLAG,
            PARKING_INFO_MASTER.BROKER_APPLICATION_POSSIBILITY
        ).from(PARKING_INFO_MASTER)
            .where(PARKING_INFO_MASTER.BUILDING_CODE.like("${orderCode.value}%"))

        // ROOM_MASTERのサブクエリ
        val fRoomMaster = context.select(
            ROOM_MASTER.BUILDING_CODE,
            ROOM_MASTER.ROOM_CODE,
            ROOM_MASTER.ROOM_NUMBER
        ).from(ROOM_MASTER)

        // PARKING_ADDITIONAL_INFO_MASTERテーブルのサブクエリ
        val fParkingAdditionalInfoMaster = context.select(
            PARKING_ADDITIONAL_INFO_MASTER.BUILDING_CODE,
            PARKING_ADDITIONAL_INFO_MASTER.PARKING_CODE,
            PARKING_ADDITIONAL_INFO_MASTER.ROOM_BUILDING_CODE,
            PARKING_ADDITIONAL_INFO_MASTER.ROOM_ROOM_CODE,
            fRoomMaster.field(ROOM_MASTER.ROOM_NUMBER)?.`as`("ROOM_ROOM_NUMBER")
        ).from(PARKING_ADDITIONAL_INFO_MASTER)
            .leftJoin(fRoomMaster)
            .on(PARKING_ADDITIONAL_INFO_MASTER.ROOM_BUILDING_CODE.eq(fRoomMaster.field(ROOM_MASTER.BUILDING_CODE)))
            .and(PARKING_ADDITIONAL_INFO_MASTER.ROOM_ROOM_CODE.eq(fRoomMaster.field(ROOM_MASTER.ROOM_CODE)))
            .where(PARKING_ADDITIONAL_INFO_MASTER.BUILDING_CODE.like("${orderCode.value}%"))

        // PARKING_ENABLEテーブルのサブクエリ
        val fParkingEnable = context.select(
            PARKING_ENABLE.PARKING_LOT_ENABLE,
            PARKING_ENABLE.BUILDING_CODE,
            PARKING_ENABLE.PARKING_LOT_CODE,
        ).from(PARKING_ENABLE)
            .where(PARKING_ENABLE.BUILDING_CODE.like("${orderCode.value}%"))
            .and(PARKING_ENABLE.DELETE_FLAG.eq("0"))

        // LATEST_RENT_EVALUATIONテーブルのサブクエリ
        val fLatestRentEvaluation = context.select(
            LATEST_RENT_EVALUATION.BUILDING_CODE,
            LATEST_RENT_EVALUATION.PROPERTY_CODE,
            LATEST_RENT_EVALUATION.KEY_MONEY_AMOUNT,
            LATEST_RENT_EVALUATION.DEPOSIT_AMOUNT,
            LATEST_RENT_EVALUATION.PARKING_FEE,
            `when`(
                LATEST_RENT_EVALUATION.TAX_DIVISION.eq("1").and(
                    LATEST_RENT_EVALUATION.PARKING_FEE_INOUT_DIVISION.eq("1")
                ),
                inline("1")
            ).otherwise(inline("0")).`as`("PARKING_FEE_IN_TAX"),
            `when`(
                LATEST_RENT_EVALUATION.TAX_DIVISION.eq("1").and(
                    LATEST_RENT_EVALUATION.KEY_MONEY_INOUT_DIVISION.eq("1")
                ),
                inline("1")
            ).otherwise(inline("0")).`as`("KEY_MONEY_IN_TAX"),
            LATEST_RENT_EVALUATION.BROKER_APPLICATION_COLLECTION_DIVISION,
            LATEST_RENT_EVALUATION.STANDARD_RENT_FOR_COOP
        ).from(LATEST_RENT_EVALUATION)
            .where(LATEST_RENT_EVALUATION.BUILDING_CODE.like("${orderCode.value}%"))
            .and(LATEST_RENT_EVALUATION.ROOM_PARKING_DIVISION.eq("2"))

        // 各サブクエリのカラムを取得
        val fParkingBuildingCode = fBuildingMaster.field(BUILDING_MASTER.BUILDING_CODE)!!
        val fParkingLotCode = fParking.field(PARKING.PARKING_LOT_CODE)

        // メインクエリ
        return context.select(
            fParkingBuildingCode,
            fParkingLotCode,
            fBuildingMaster.field(BUILDING_MASTER.BUILDING_NAME),
            fBuildingMaster.field(BUILDING_MASTER.BULK_LEASE_FLAG)
                ?.`as`("BUILDING_BULK_LEASE_FLAG"),
            fBuildingMaster.field(BUILDING_MASTER.ADDRESS_DETAIL),
            fBuildingMaster.field(BUILDING_MASTER.COMPLETION_DELIVERY_DATE),
            fBuildingMaster.field("THIRTY_FIVE_YEAR_LUMP_SUM_CATEGORY")
                ?.`as`("BUILDING_THIRTY_FIVE_YEAR_LUMP_SUM_CATEGORY"),
            fLatestTenantContract.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER),
            fLatestTenantContract.field(TENANT_CONTRACT.ROOM_CODE),
            fParking.field(PARKING.PARKING_LOT_NUMBER),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.LAND_TRANSPORT_NAME_1)
                ?.`as`("LAND_TRANSPORT_NAME"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.TYPE_1)?.`as`("TYPE"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.BUSINESS_CATEGORY_1)
                ?.`as`("BUSINESS_CATEGORY"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.LEFT_NUMBER_1)
                ?.`as`("LEFT_NUMBER"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.RIGHT_NUMBER_1)
                ?.`as`("RIGHT_NUMBER"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.MANUFACTURER_DIVISION_1)
                ?.`as`("MANUFACTURER_DIVISION"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.CAR_MODEL_NAME_1)
                ?.`as`("CAR_MODEL_NAME"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.LIGHT_VEHICLE_SIGN_1)
                ?.`as`("LIGHT_VEHICLE_SIGN"),
            fLatestTenantContract.field(TENANT_CONTRACT.TENANT_NAME), // 駐車場テナント契約入居者名
            fLatestTenantContract.field(TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE),
            fLatestTenantContract.field(TENANT.TENANT_NAME_KANJI), // 駐車場テナント契約者名
            fLatestTenantContract.field(TENANT_CONTRACT.CONTRACT_EXPIRY_DATE),
            fLatestTenantContract.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_END_DATE),
            fLatestTenantContract.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION),
            fLatestTenantContract.field(TENANT_CONTRACT.MODIFICATION_STATE_DIVISION),
            fLatestTenantContract.field(TENANT_CONTRACT.MOVE_IN_START_PROCESSED_SIGN),
            fLatestTenantContract.field(TENANT_CONTRACT.MOVE_OUT_DATE),
            fLatestTenantContract.field(TENANT_CONTRACT.VACATE_SCHEDULED_DATE),
            fParking.field("TOU"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.TANDEM_SIGN),
            fLatestTenantContract.field(TENANT_CONTRACT.CANCELLATION_SIGN),
            fParking.field(PARKING.LOGICAL_DELETE_FLAG),
            fParking.field(PARKING.CONSOLIDATED_BUILDING_CODE),
            fParking.field(PARKING.CONSOLIDATED_PARKING_CODE),
            fParking.field(PARKING.TRANSFERRED_BUILDING_CODE),
            fLatestTenantContract.field(TENANT_CONTRACT.LOGICAL_DELETE_SIGN),
            fLatestTenantContract.field(TENANT_CONTRACT.VACATE_NOTICE_DATE),
            fBulkLeaseParking.field(BULK_LEASE_PARKING.ASSESSMENT_DIVISION),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.PARKING_CERT_ISSUE_SIGN_1)
                ?.`as`("PARKING_CERT_ISSUE_SIGN"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.PARKING_CERT_COMMENT_1)
                ?.`as`("PARKING_CERT_COMMENT"),
            fParking.field(PARKING.BULK_LEASE_FLAG),
            fParking.field(PARKING.PARKING_CATEGORY),
            fLatestTenantContract.field(TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER),
            `when`(
                fDaitoBulkLeaseContractDetails.field(DAITO_BULK_LEASE_CONTRACT_DETAILS.THIRTY_FIVE_YEAR_LUMP_SUM_CATEGORY)
                    ?.isNotNull(),
                fDaitoBulkLeaseContractDetails.field(DAITO_BULK_LEASE_CONTRACT_DETAILS.THIRTY_FIVE_YEAR_LUMP_SUM_CATEGORY)
                    ?.cast(String::class.java)
            ).`when`(
                fBuildingMaster.field(BUILDING_MASTER.ORDER_CODE)?.isNotNull(),
                fContractFile
            ).otherwise(inline("")).`as`("THIRTY_FIVE_YEAR_LUMP_SUM_CATEGORY"),
            fParking.field(PARKING.OFF_SITE_PARKING_CATEGORY),
            fOffSiteParking.field(OFF_SITE_PARKING.DISTANCE),
            //ここから新規テーブル
            fAddressMaster.field(ADDRESS_MASTER.POSTAL_CODE),
            fAddressMaster.field(ADDRESS_MASTER.PREFECTURE_KANJI_NAME),
            fAddressMaster.field(ADDRESS_MASTER.CITY_KANJI_NAME),
            fAddressMaster.field(ADDRESS_MASTER.TOWN_KANJI_NAME),
            fCustomer.field(CUSTOMER.CLIENT_NAME_KANJI), // 家主名
            fBuildingInfoMaster.field(BUILDING_INFO_MASTER.MARKETING_BRANCH_OFFICE_CD)
                ?.`as`("BUSINESS_OFFICE_CODE"),
            fBuildingMaster.field(BUILDING_MASTER.MARKETING_BRANCH_OFFICE_CD)
                ?.`as`("BUSINESS_OFFICE_CODE2"),
            fParkingInfo.field(PARKING_INFO_MASTER.SPECIAL_CONTRACT_FLAG),
            fParkingInfo.field(PARKING_INFO_MASTER.BROKER_APPLICATION_POSSIBILITY),
            fParkingAdditionalInfoMaster.field(PARKING_ADDITIONAL_INFO_MASTER.ROOM_BUILDING_CODE),
            fParkingAdditionalInfoMaster.field(PARKING_ADDITIONAL_INFO_MASTER.ROOM_ROOM_CODE),
            fParkingAdditionalInfoMaster.field("ROOM_ROOM_NUMBER"),
            fParkingEnable.field(PARKING_ENABLE.PARKING_LOT_ENABLE),
            fParking.field("ALLOW_ALL_PARKING_LOT_AVAILABILITY_EDIT"),
            fLatestRentEvaluation.field(LATEST_RENT_EVALUATION.KEY_MONEY_AMOUNT),
            fLatestRentEvaluation.field(LATEST_RENT_EVALUATION.DEPOSIT_AMOUNT),
            fLatestRentEvaluation.field(LATEST_RENT_EVALUATION.PARKING_FEE),
            fLatestRentEvaluation.field("PARKING_FEE_IN_TAX"),
            fLatestRentEvaluation.field("KEY_MONEY_IN_TAX"),
            fLatestRentEvaluation.field(LATEST_RENT_EVALUATION.BROKER_APPLICATION_COLLECTION_DIVISION),
            fLatestRentEvaluation.field(LATEST_RENT_EVALUATION.STANDARD_RENT_FOR_COOP),
        ).from(fBuildingMaster)
            .leftJoin(fParking)
            .on(fParkingBuildingCode.eq(fParking.field(PARKING.BUILDING_CODE)))
            .leftJoin(fLatestTenantContract)
            .on(fParkingBuildingCode.eq(fLatestTenantContract.field(TENANT_CONTRACT.BUILDING_CODE)))
            .and(fParkingLotCode?.eq(fLatestTenantContract.field(TENANT_CONTRACT.PARKING_CODE)))
            .leftJoin(fParkingVehicleInfoFile)
            .on(
                fLatestTenantContract.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER)
                    ?.eq(fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER))
            )
            .leftJoin(fBulkLeaseParking)
            .on(fParkingBuildingCode.eq(fBulkLeaseParking.field(BULK_LEASE_PARKING.BUILDING_CODE)))
            .and(fParkingLotCode?.eq(fBulkLeaseParking.field(BULK_LEASE_PARKING.PARKING_CODE)))
            .leftJoin(fDaitoBulkLeaseContractDetails)
            .on(
                fParkingBuildingCode.eq(
                    fDaitoBulkLeaseContractDetails.field(
                        DAITO_BULK_LEASE_CONTRACT_DETAILS.BUILDING_CD
                    )
                )
            )
            .leftJoin(fOffSiteParking)
            .on(
                substring(fParkingBuildingCode, 1, 7)
                    .eq(fOffSiteParking.field(OFF_SITE_PARKING.ORDER_CD))
            )
            .and(fParkingLotCode?.eq(fOffSiteParking.field(OFF_SITE_PARKING.PARKING_CD)))
            // ここから新規テーブル
            .leftJoin(fAddressMaster)
            .on(
                fAddressMaster.field(ADDRESS_MASTER.ADDRESS_CODE)?.eq(
                    fBuildingMaster.field(BUILDING_MASTER.PREFECTURE_CODE)?.concat(
                        fBuildingMaster.field(BUILDING_MASTER.CITY_CODE)
                            ?.concat(fBuildingMaster.field(BUILDING_MASTER.TOWN_CODE))
                    )
                )
            )
            .leftJoin(fCustomer)
            .on(
                fBuildingMaster.field(BUILDING_MASTER.LANDLORD_CODE)?.concat("00")
                    ?.eq(fCustomer.field(CUSTOMER.INTEGRATED_CLIENT_CODE))
            )
            .leftJoin(fBuildingInfoMaster)
            .on(fParkingBuildingCode.eq(fBuildingInfoMaster.field(BUILDING_INFO_MASTER.BUILDING_CODE)))
            .leftJoin(fParkingInfo)
            .on(fParkingBuildingCode.eq(fParkingInfo.field(PARKING_INFO_MASTER.BUILDING_CODE)))
            .and(fParkingLotCode?.eq(fParkingInfo.field(PARKING_INFO_MASTER.PARKING_CODE)))
            .leftJoin(fParkingAdditionalInfoMaster)
            .on(
                fParkingBuildingCode.eq(
                    fParkingAdditionalInfoMaster.field(
                        PARKING_ADDITIONAL_INFO_MASTER.BUILDING_CODE
                    )
                )
            )
            .and(
                fParkingLotCode?.eq(
                    fParkingAdditionalInfoMaster.field(
                        PARKING_ADDITIONAL_INFO_MASTER.PARKING_CODE
                    )
                )
            )
            .leftJoin(fParkingEnable)
            .on(fParkingBuildingCode.eq(fParkingEnable.field(PARKING_ENABLE.BUILDING_CODE)))
            .and(fParkingLotCode?.eq(fParkingEnable.field(PARKING_ENABLE.PARKING_LOT_CODE)))
            .leftJoin(fLatestRentEvaluation)
            .on(fParkingBuildingCode.eq(fLatestRentEvaluation.field(LATEST_RENT_EVALUATION.BUILDING_CODE)))
            .and(fParkingLotCode?.eq(fLatestRentEvaluation.field(LATEST_RENT_EVALUATION.PROPERTY_CODE)))
            .orderBy(fParkingBuildingCode.asc(), fParkingLotCode?.asc())
            .fetchInto(ParkingDetailPojo::class.java)
    }

    /** 駐車場詳細区画一覧取得（ウェルカムパーク向けバッチ用） */
    override fun findParkingDetailForWelcomeParkBatch(orderCode: Building.OrderCode): List<Parking> {
        // PARKINGテーブルのサブクエリ（既存）
        val fParking = context.select(
            PARKING.BUILDING_CODE,
            PARKING.PARKING_LOT_NUMBER,
            PARKING.PARKING_LOT_CODE,
            PARKING.LOGICAL_DELETE_FLAG,
            PARKING.CONSOLIDATED_BUILDING_CODE,
            PARKING.CONSOLIDATED_PARKING_CODE,
            PARKING.BULK_LEASE_FLAG,
            PARKING.PARKING_CATEGORY,
        )
            .from(PARKING)
            .where(
                and(
                    PARKING.BUILDING_CODE.like("${orderCode.value}%")
                        .or(PARKING.CONSOLIDATED_BUILDING_CODE.like("${orderCode.value}%"))
                )
                    .and(
                        PARKING.LOGICAL_DELETE_FLAG.eq(0)
                            .or(PARKING.CONSOLIDATED_BUILDING_CODE.isNotNull())
                    )
            )

        // BULK_LEASE_PARKINGテーブルのサブクエリ（既存）
        val fBulkLeaseParking = context.select(
            BULK_LEASE_PARKING.BUILDING_CODE,
            BULK_LEASE_PARKING.PARKING_CODE,
            BULK_LEASE_PARKING.ASSESSMENT_DIVISION
        ).from(BULK_LEASE_PARKING)
            .where(BULK_LEASE_PARKING.BUILDING_CODE.like("${orderCode.value}%"))

        val fTenantContract = context.select(
            TENANT_CONTRACT.TENANT_CONTRACT_NUMBER,
            TENANT_CONTRACT.BUILDING_CODE,
            TENANT_CONTRACT.PARKING_CODE,
            TENANT_CONTRACT.ROOM_CODE,
            TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE,
            TENANT_CONTRACT.CURRENT_STATE_DIVISION,
            TENANT_CONTRACT.MODIFICATION_STATE_DIVISION,
            TENANT_CONTRACT.MOVE_IN_START_PROCESSED_SIGN,
            TENANT_CONTRACT.MOVE_OUT_DATE,
            TENANT_CONTRACT.CANCELLATION_SIGN,
            TENANT_CONTRACT.LOGICAL_DELETE_SIGN,
            TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER
        ).from(TENANT_CONTRACT)
            .where(
                TENANT_CONTRACT.BUILDING_CODE.like("${orderCode.value}%")
            )

        // TENANT_CONTRACTテーブルのサブクエリ（既存）
        val fLatestTenantContract = context.select(
            fTenantContract.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER),
            fTenantContract.field(TENANT_CONTRACT.BUILDING_CODE),
            fTenantContract.field(TENANT_CONTRACT.PARKING_CODE),
            fTenantContract.field(TENANT_CONTRACT.ROOM_CODE),
            fTenantContract.field(TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE),
            fTenantContract.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION),
            fTenantContract.field(TENANT_CONTRACT.MODIFICATION_STATE_DIVISION),
            fTenantContract.field(TENANT_CONTRACT.MOVE_IN_START_PROCESSED_SIGN),
            fTenantContract.field(TENANT_CONTRACT.MOVE_OUT_DATE),
            fTenantContract.field(TENANT_CONTRACT.CANCELLATION_SIGN),
            fTenantContract.field(TENANT_CONTRACT.LOGICAL_DELETE_SIGN),
            fTenantContract.field(TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER)
        ).from(fTenantContract)
            .innerJoin(
                raw(
                    String.format(
                        LATEST_TENANT_CONTRACT_SQL,
                        "${orderCode.value}%",
                        "${orderCode.value}%"
                    )
                )
            )
            .on(
                fTenantContract.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER)?.eq(
                    field("y.ecbkey", String::class.java)
                )
            )
            .where(
                fTenantContract.field(TENANT_CONTRACT.BUILDING_CODE)?.like("${orderCode.value}%")
            )

        // LATEST_RENT_EVALUATIONテーブルのサブクエリ（既存）
        val fLatestRentEvaluation = context.select(
            LATEST_RENT_EVALUATION.BUILDING_CODE,
            LATEST_RENT_EVALUATION.PROPERTY_CODE,
            LATEST_RENT_EVALUATION.PARKING_FEE,
            LATEST_RENT_EVALUATION.BROKER_APPLICATION_COLLECTION_DATE
        ).from(LATEST_RENT_EVALUATION)
            .where(LATEST_RENT_EVALUATION.BUILDING_CODE.like("${orderCode.value}%"))
            .and(LATEST_RENT_EVALUATION.ROOM_PARKING_DIVISION.eq("2"))

        // 各サブクエリのカラムを取得
        val fParkingBuildingCode = fParking.field(PARKING.BUILDING_CODE)!!
        val fParkingLotCode = fParking.field(PARKING.PARKING_LOT_CODE)!!

        // メインクエリ
        return context.select(
            fLatestTenantContract.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER),
            fLatestTenantContract.field(TENANT_CONTRACT.ROOM_CODE),
            fParkingBuildingCode,
            fParking.field(PARKING.PARKING_LOT_NUMBER),
            fParkingLotCode,
            fLatestTenantContract.field(TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE),
            fLatestTenantContract.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION),
            fLatestTenantContract.field(TENANT_CONTRACT.MODIFICATION_STATE_DIVISION),
            fLatestTenantContract.field(TENANT_CONTRACT.MOVE_IN_START_PROCESSED_SIGN),
            fLatestTenantContract.field(TENANT_CONTRACT.MOVE_OUT_DATE),
            fLatestTenantContract.field(TENANT_CONTRACT.CANCELLATION_SIGN),
            fParking.field(PARKING.LOGICAL_DELETE_FLAG),
            fParking.field(PARKING.CONSOLIDATED_BUILDING_CODE),
            fParking.field(PARKING.CONSOLIDATED_PARKING_CODE),
            fLatestTenantContract.field(TENANT_CONTRACT.LOGICAL_DELETE_SIGN),
            fBulkLeaseParking.field(BULK_LEASE_PARKING.ASSESSMENT_DIVISION),
            fParking.field(PARKING.BULK_LEASE_FLAG),
            fParking.field(PARKING.PARKING_CATEGORY),
            fLatestTenantContract.field(TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER),
            fParking.field(PARKING.OFF_SITE_PARKING_CATEGORY),
            fLatestRentEvaluation.field(LATEST_RENT_EVALUATION.PARKING_FEE),
            `when`(
                fLatestRentEvaluation.field(LATEST_RENT_EVALUATION.BROKER_APPLICATION_COLLECTION_DATE)
                    ?.eq(0),
                inline(1)
            ).otherwise(inline(0)).`as`("BROKER_APPLICATION_COLLECTION_FLG"),
        ).from(fParking)
            .leftJoin(fLatestTenantContract)
            .on(fParkingBuildingCode.eq(fLatestTenantContract.field(TENANT_CONTRACT.BUILDING_CODE)))
            .and(fParkingLotCode.eq(fLatestTenantContract.field(TENANT_CONTRACT.PARKING_CODE)))
            .leftJoin(fBulkLeaseParking)
            .on(fParkingBuildingCode.eq(fBulkLeaseParking.field(BULK_LEASE_PARKING.BUILDING_CODE)))
            .and(fParkingLotCode.eq(fBulkLeaseParking.field(BULK_LEASE_PARKING.PARKING_CODE)))
            .innerJoin(PARKING_HOURLY_RENTAL_APPROVAL)
            .on(PARKING_HOURLY_RENTAL_APPROVAL.BUILDING_CODE.eq(fParkingBuildingCode))
            .and(PARKING_HOURLY_RENTAL_APPROVAL.PARKING_TIME_RENTAL_CONSENT_TYPE.eq("1"))
            .innerJoin(fLatestRentEvaluation)
            .on(fParkingBuildingCode.eq(fLatestRentEvaluation.field(LATEST_RENT_EVALUATION.BUILDING_CODE)))
            .and(fParkingLotCode.eq(fLatestRentEvaluation.field(LATEST_RENT_EVALUATION.PROPERTY_CODE)))
            .orderBy(fParkingBuildingCode.asc(), fParkingLotCode.asc())
            .fetchInto(ParkingDetailPojo::class.java).applyEBoard(this, isWelcomePark = true, false)
            .toParkingList(emptyMap(), emptyList())
    }

    override fun findAggregateTenantContract(tenantContractNumber: String): AggregateTenantContractPojo? {
        // TENANTテーブルのサブクエリ
        val fTenant = context.select(
            TENANT.TENANT_CODE,
            TENANT.TENANT_NAME_KANJI,
        ).from(TENANT)

        // TENANT_CONTRACTテーブルをサブクエリ化
        val fTenantContract = context
            .select(
                TENANT_CONTRACT.TENANT_NAME,
                TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE,
                TENANT_CONTRACT.CONTRACT_EXPIRY_DATE,
                TENANT_CONTRACT.CONTRACT_EFFECTIVE_END_DATE,
                TENANT_CONTRACT.CURRENT_STATE_DIVISION,
                TENANT_CONTRACT.MODIFICATION_STATE_DIVISION,
                TENANT_CONTRACT.MOVE_IN_START_PROCESSED_SIGN,
                TENANT_CONTRACT.MOVE_OUT_DATE,
                TENANT_CONTRACT.CANCELLATION_SIGN,
                TENANT_CONTRACT.VACATE_SCHEDULED_DATE,
                TENANT_CONTRACT.VACATE_NOTICE_DATE,
                TENANT_CONTRACT.TENANT_CODE
            )
            .from(TENANT_CONTRACT)
            .where(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER.eq(tenantContractNumber))

        // メインクエリ
        return context
            .select(
                fTenantContract.field(TENANT_CONTRACT.TENANT_NAME),
                fTenantContract.field(TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE),
                fTenant.field(TENANT.TENANT_NAME_KANJI),
                fTenantContract.field(TENANT_CONTRACT.CONTRACT_EXPIRY_DATE),
                fTenantContract.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_END_DATE),
                fTenantContract.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION),
                fTenantContract.field(TENANT_CONTRACT.MODIFICATION_STATE_DIVISION),
                fTenantContract.field(TENANT_CONTRACT.MOVE_IN_START_PROCESSED_SIGN),
                fTenantContract.field(TENANT_CONTRACT.MOVE_OUT_DATE),
                fTenantContract.field(TENANT_CONTRACT.CANCELLATION_SIGN),
                fTenantContract.field(TENANT_CONTRACT.VACATE_SCHEDULED_DATE),
                fTenantContract.field(TENANT_CONTRACT.VACATE_NOTICE_DATE),
            )
            .from(fTenantContract)
            .leftJoin(fTenant)
            .on(
                substring(fTenantContract.field(TENANT_CONTRACT.TENANT_CODE), 1, 8).concat("0")
                    .eq(fTenant.field(TENANT.TENANT_CODE))
            )
            .fetchOneInto(AggregateTenantContractPojo::class.java)
    }

    override fun findPreviousTenantContract(
        orderCode: Building.OrderCode,
        parkingCode: String,
        tenantContractNumber: String
    ): PreviousParkingTenantContractPojo? {
        // TENANT_CONTRACTテーブルのサブクエリ
        val fTenantContract = context.select(
            TENANT_CONTRACT.TENANT_CONTRACT_NUMBER,
            TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE,
            TENANT_CONTRACT.CURRENT_STATE_DIVISION,
            TENANT_CONTRACT.LOGICAL_DELETE_SIGN,
            TENANT_CONTRACT.MOVE_OUT_DATE,
            TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE,
        ).from(TENANT_CONTRACT)
            .where(
                (TENANT_CONTRACT.TENANT_CONTRACT_NUMBER.ne(tenantContractNumber))
            )

        val fTenantContractMain = context
            .select(
                TENANT_CONTRACT.BUILDING_CODE,
                `when`(
                    TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE.eq(0),
                    99999999
                ).otherwise(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE)
                    .`as`(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE),
                TENANT_CONTRACT.TENANT_CONTRACT_NUMBER,
                TENANT_CONTRACT.ROOM_CODE,
                TENANT_CONTRACT.TENANT_NAME,
                TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE,
                TENANT_CONTRACT.CONTRACT_EXPIRY_DATE,
                TENANT_CONTRACT.CONTRACT_EFFECTIVE_END_DATE,
                TENANT_CONTRACT.CURRENT_STATE_DIVISION,
                TENANT_CONTRACT.MODIFICATION_STATE_DIVISION,
                TENANT_CONTRACT.MOVE_IN_START_PROCESSED_SIGN,
                TENANT_CONTRACT.MOVE_OUT_DATE,
                TENANT_CONTRACT.CANCELLATION_SIGN,
                TENANT_CONTRACT.LOGICAL_DELETE_SIGN,
                TENANT_CONTRACT.VACATE_SCHEDULED_DATE,
                TENANT_CONTRACT.VACATE_NOTICE_DATE,
                TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER,
                TENANT_CONTRACT.TENANT_CODE,
            )
            .from(TENANT_CONTRACT)
            .where(
                TENANT_CONTRACT.BUILDING_CODE.like("${orderCode.value}%")
                    .and(TENANT_CONTRACT.PARKING_CODE.eq(parkingCode))
                    .and(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER.lt(tenantContractNumber))
                    .and(
                        TENANT_CONTRACT.LOGICAL_DELETE_SIGN.ne(1)
                            .or(TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER.isNotNull())
                    )
                    .and(TENANT_CONTRACT.CURRENT_STATE_DIVISION.ne("27"))
                    .and(
                        TENANT_CONTRACT.CURRENT_STATE_DIVISION.ne("90")
                            .or(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE.ne(0))
                    )
                    .and(
                        TENANT_CONTRACT.MOVE_OUT_DATE.eq(0).or(
                            and(
                                TENANT_CONTRACT.MOVE_OUT_DATE.ne(0)
                                    .and(TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE.le(TENANT_CONTRACT.MOVE_OUT_DATE))
                            )
                        )
                    )
            )

        // PARKING_VEHICLE_INFO_FILEテーブルのサブクエリ
        val fParkingVehicleInfoFile = context.select(
            PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER,
            PARKING_VEHICLE_INFO_FILE.LAND_TRANSPORT_NAME_1,
            PARKING_VEHICLE_INFO_FILE.TYPE_1,
            PARKING_VEHICLE_INFO_FILE.BUSINESS_CATEGORY_1,
            PARKING_VEHICLE_INFO_FILE.LEFT_NUMBER_1,
            PARKING_VEHICLE_INFO_FILE.RIGHT_NUMBER_1,
            PARKING_VEHICLE_INFO_FILE.MANUFACTURER_DIVISION_1,
            PARKING_VEHICLE_INFO_FILE.CAR_MODEL_NAME_1,
            PARKING_VEHICLE_INFO_FILE.LIGHT_VEHICLE_SIGN_1,
            PARKING_VEHICLE_INFO_FILE.TANDEM_SIGN,
            PARKING_VEHICLE_INFO_FILE.PARKING_CERT_ISSUE_SIGN_1,
            PARKING_VEHICLE_INFO_FILE.PARKING_CERT_COMMENT_1,
        ).from(PARKING_VEHICLE_INFO_FILE)

        // TENANTテーブルのサブクエリ
        val fTenant = context.select(
            TENANT.TENANT_CODE,
            TENANT.TENANT_NAME_KANJI,
        ).from(TENANT)

        return context.select(
            `when`(
                fTenantContractMain.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE)?.eq(0),
                99999999
            ).otherwise(fTenantContractMain.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE))
                .`as`(fTenantContractMain.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE)),
            fTenantContractMain.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER),
            fTenantContractMain.field(TENANT_CONTRACT.ROOM_CODE),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.LAND_TRANSPORT_NAME_1)
                ?.`as`("LAND_TRANSPORT_NAME"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.TYPE_1)?.`as`("TYPE"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.BUSINESS_CATEGORY_1)
                ?.`as`("BUSINESS_CATEGORY"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.LEFT_NUMBER_1)
                ?.`as`("LEFT_NUMBER"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.RIGHT_NUMBER_1)
                ?.`as`("RIGHT_NUMBER"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.MANUFACTURER_DIVISION_1)
                ?.`as`("MANUFACTURER_DIVISION"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.CAR_MODEL_NAME_1)
                ?.`as`("CAR_MODEL_NAME"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.LIGHT_VEHICLE_SIGN_1)
                ?.`as`("LIGHT_VEHICLE_SIGN"),
            fTenantContractMain.field(TENANT_CONTRACT.TENANT_NAME),
            fTenantContractMain.field(TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE),
            fTenant.field(TENANT.TENANT_NAME_KANJI),
            fTenantContractMain.field(TENANT_CONTRACT.CONTRACT_EXPIRY_DATE),
            fTenantContractMain.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_END_DATE),
            fTenantContractMain.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION),
            fTenantContractMain.field(TENANT_CONTRACT.MODIFICATION_STATE_DIVISION),
            fTenantContractMain.field(TENANT_CONTRACT.MOVE_IN_START_PROCESSED_SIGN),
            fTenantContractMain.field(TENANT_CONTRACT.MOVE_OUT_DATE),
            fTenantContractMain.field(TENANT_CONTRACT.CANCELLATION_SIGN),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.TANDEM_SIGN),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.PARKING_CERT_ISSUE_SIGN_1)
                ?.`as`("PARKING_CERT_ISSUE_SIGN"),
            fParkingVehicleInfoFile.field(PARKING_VEHICLE_INFO_FILE.PARKING_CERT_COMMENT_1)
                ?.`as`("PARKING_CERT_COMMENT"),
            fTenantContractMain.field(TENANT_CONTRACT.LOGICAL_DELETE_SIGN),
            fTenantContractMain.field(TENANT_CONTRACT.VACATE_SCHEDULED_DATE),
            fTenantContractMain.field(TENANT_CONTRACT.VACATE_NOTICE_DATE),
            fTenantContractMain.field(TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER)
        ).from(fTenantContractMain)
            .leftJoin(fTenantContract)
            .on(
                fTenantContractMain.field(TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER)
                    ?.eq(fTenantContract.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER))
            )
            .leftJoin(fTenant)
            .on(
                substring(fTenantContractMain.field(TENANT_CONTRACT.TENANT_CODE), 1, 8).concat("0")
                    .eq(fTenant.field(TENANT.TENANT_CODE))
            )
            .leftJoin(fParkingVehicleInfoFile)
            .on(
                fTenantContractMain.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER)?.eq(
                    fParkingVehicleInfoFile.field(
                        PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER
                    )
                )
            )
            .where(
                fTenantContractMain.field(TENANT_CONTRACT.BUILDING_CODE)!!
                    .like("${orderCode.value}%")
                    .and(fTenantContractMain.field(TENANT_CONTRACT.PARKING_CODE)?.eq(parkingCode))
                    .and(
                        fTenantContractMain.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER)
                            ?.lt(tenantContractNumber)
                    )
                    .and(
                        fTenantContractMain.field(TENANT_CONTRACT.LOGICAL_DELETE_SIGN)?.ne(1)
                            ?.or(
                                fTenantContractMain.field(TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER)
                                    ?.isNotNull()
                            )
                    )
                    .and(
                        fTenantContractMain.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION)?.ne("27")
                    )
                    .and(
                        fTenantContractMain.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION)?.ne("90")
                            ?.or(
                                fTenantContractMain.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE)
                                    ?.ne(0)
                            )
                    )
                    .and(
                        fTenantContractMain.field(TENANT_CONTRACT.MOVE_OUT_DATE)?.eq(0)?.or(
                            and(
                                fTenantContractMain.field(TENANT_CONTRACT.MOVE_OUT_DATE)?.ne(0)
                                    ?.and(
                                        fTenantContractMain.field(TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE)
                                            ?.le(fTenantContractMain.field(TENANT_CONTRACT.MOVE_OUT_DATE))
                                    )
                            )
                        )
                    )
                    .and(
                        fTenantContract.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION)?.isNull()
                            ?.or(fTenantContract.field(TENANT_CONTRACT.LOGICAL_DELETE_SIGN)?.eq(0))
                    )
                    .and(
                        fTenantContract.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION)?.isNull()
                            ?.or(
                                fTenantContract.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION)
                                    ?.ne("27")
                            )
                    )
                    .and(
                        fTenantContract.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION)?.isNull()
                            ?.or(
                                fTenantContract.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION)
                                    ?.ne("90")
                            )
                            ?.or(
                                fTenantContract.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE)
                                    ?.ne(0)
                            )
                    )
                    .and(
                        fTenantContract.field(TENANT_CONTRACT.CURRENT_STATE_DIVISION)?.isNull()
                            ?.or(fTenantContract.field(TENANT_CONTRACT.MOVE_OUT_DATE)?.eq(0))?.or(
                                fTenantContract.field(TENANT_CONTRACT.MOVE_OUT_DATE)?.ne(0)!!
                                    .and(
                                        fTenantContract.field(TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE)
                                            ?.le(
                                                fTenantContract.field(TENANT_CONTRACT.MOVE_OUT_DATE)
                                            )
                                    )
                            )
                    )
            ).orderBy(
                `when`(
                    fTenantContractMain.field(TENANT_CONTRACT.LOGICAL_DELETE_SIGN)?.ne(1)
                        ?.or(
                            fTenantContract.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER)?.isNull()
                        ),
                    `when`(
                        fTenantContractMain.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE)
                            ?.eq(0), 99999999
                    ).otherwise(fTenantContractMain.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE))
                ).otherwise(
                    `when`(
                        fTenantContract.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE)?.eq(0),
                        99999999
                    ).otherwise(fTenantContract.field(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE))
                ).desc(),
                fTenantContractMain.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER)?.desc()
            ).fetchInto(PreviousParkingTenantContractPojo::class.java).firstOrNull()
    }

    override fun findBulkTenantContract(
        parkingTenantContractNumber: String
    ): PropertyTenantContractPojo? {
        // TENANTテーブルから契約者名を取得
        val fTenant = context.select(
            TENANT.TENANT_CODE,
            TENANT.TENANT_NAME_KANJI,
        ).from(TENANT)

        // tenant_contractから部屋契約情報を取得
        val fPropertyTenantContract = context.select(
            TENANT_CONTRACT.TENANT_CONTRACT_NUMBER,
            TENANT_CONTRACT.TENANT_NAME,
            fTenant.field(TENANT.TENANT_NAME_KANJI),
        ).from(TENANT_CONTRACT)
            .leftJoin(fTenant)
            .on(
                substring(TENANT_CONTRACT.TENANT_CODE, 1, 8).concat("0")
                    .eq(fTenant.field(TENANT.TENANT_CODE))
            )

        // room_masterテーブルから親契約部屋番号を取得
        val fParentRoomMaster = context.select(
            ROOM_MASTER.BUILDING_CODE,
            ROOM_MASTER.ROOM_CODE,
            ROOM_MASTER.ROOM_NUMBER
        ).from(ROOM_MASTER)

        // tenant_contract_bulk_collection_fileテーブルから親契約情報、部屋契約情報を取得
        val fTenantContractBulkCollectionFile = context.select(
            TENANT_CONTRACT_BULK_COLLECTION_FILE.PARKING_TENANT_CONTRACT,
            TENANT_CONTRACT_BULK_COLLECTION_FILE.PARENT_TENANT_BUILDING_CD,
            TENANT_CONTRACT_BULK_COLLECTION_FILE.PARENT_TENANT_ROOM_CD,
            TENANT_CONTRACT_BULK_COLLECTION_FILE.UPDATE_DATE,
            TENANT_CONTRACT_BULK_COLLECTION_FILE.UPDATE_TIME,
            fParentRoomMaster.field(ROOM_MASTER.ROOM_NUMBER),
            fPropertyTenantContract.field(TENANT_CONTRACT.TENANT_NAME),
            fPropertyTenantContract.field(TENANT.TENANT_NAME_KANJI),
        ).from(TENANT_CONTRACT_BULK_COLLECTION_FILE)
            .leftJoin(fParentRoomMaster)
            .on(
                TENANT_CONTRACT_BULK_COLLECTION_FILE.PARENT_TENANT_BUILDING_CD.eq(
                    fParentRoomMaster.field(ROOM_MASTER.BUILDING_CODE)
                )
                    .and(
                        TENANT_CONTRACT_BULK_COLLECTION_FILE.PARENT_TENANT_ROOM_CD.eq(
                            fParentRoomMaster.field(ROOM_MASTER.ROOM_CODE)
                        )
                    )
            )
            .leftJoin(fPropertyTenantContract)
            .on(
                TENANT_CONTRACT_BULK_COLLECTION_FILE.TENANT_CONTRACT_NUMBER.eq(
                    fPropertyTenantContract.field(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER)
                )
            )
            .where(
                TENANT_CONTRACT_BULK_COLLECTION_FILE.DELETION_DATE.isNull()
                    .or(TENANT_CONTRACT_BULK_COLLECTION_FILE.DELETION_DATE.eq(0))
            )

        // room_masterテーブルから現在の契約部屋番号を取得
        val fRoomMaster = context.select(
            ROOM_MASTER.BUILDING_CODE,
            ROOM_MASTER.ROOM_CODE,
            ROOM_MASTER.ROOM_NUMBER
        ).from(ROOM_MASTER)

        // tenant_contractから現在の契約情報を取得
        return context.select(
            coalesce(
                fTenantContractBulkCollectionFile.field(TENANT_CONTRACT_BULK_COLLECTION_FILE.PARENT_TENANT_BUILDING_CD),
                TENANT_CONTRACT.BUILDING_CODE
            ).`as`("TENANT_BUILDING_CODE"),
            coalesce(
                fTenantContractBulkCollectionFile.field(TENANT_CONTRACT_BULK_COLLECTION_FILE.PARENT_TENANT_ROOM_CD),
                TENANT_CONTRACT.ROOM_CODE
            ).`as`("TENANT_ROOM_CODE"),
            coalesce(
                fTenantContractBulkCollectionFile.field(ROOM_MASTER.ROOM_NUMBER),
                fRoomMaster.field(ROOM_MASTER.ROOM_NUMBER),
            ).`as`("TENANT_ROOM_NUMBER"),
            fTenantContractBulkCollectionFile.field(TENANT_CONTRACT.TENANT_NAME),
            fTenantContractBulkCollectionFile.field(TENANT.TENANT_NAME_KANJI),
        ).from(TENANT_CONTRACT)
            .leftJoin(fRoomMaster)
            .on(
                TENANT_CONTRACT.BUILDING_CODE.eq(fRoomMaster.field(ROOM_MASTER.BUILDING_CODE))
                    .and(TENANT_CONTRACT.ROOM_CODE.eq(fRoomMaster.field(ROOM_MASTER.ROOM_CODE)))
            )
            .leftJoin(fTenantContractBulkCollectionFile)
            .on(
                TENANT_CONTRACT.TENANT_CONTRACT_NUMBER.eq(
                    fTenantContractBulkCollectionFile.field(
                        TENANT_CONTRACT_BULK_COLLECTION_FILE.PARKING_TENANT_CONTRACT
                    )
                )
            )
            .where(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER.eq(parkingTenantContractNumber))
            .orderBy(
                fTenantContractBulkCollectionFile.field(TENANT_CONTRACT_BULK_COLLECTION_FILE.UPDATE_DATE)
                    ?.desc(),
                fTenantContractBulkCollectionFile.field(TENANT_CONTRACT_BULK_COLLECTION_FILE.UPDATE_TIME)
                    ?.desc()
            )
            .fetchInto(PropertyTenantContractPojo::class.java).firstOrNull()
    }
}

interface ParkingDetailsRepositoryInterface {

    /** 駐車場詳細取得（受注コード指定） */
    fun findParkingDetailByOrderCode(
        orderCode: Building.OrderCode,
        forClient: Boolean = false,
        authInfo: AuthInfo? = null,
    ): List<Parking>

    /** テストコードのためにメソッドを別途用意 SQL_EB800_VIEW,SQL_EB800_PICTURE,SQLID_EB800_SATEI_VIEW(sql6) */
    fun findParkingDetailPojoByOrderCode(orderCode: Building.OrderCode): List<ParkingDetailPojo>

    /** ウェルカムパーク向けバッチ用駐車場詳細取得（sql2） */
    fun findParkingDetailForWelcomeParkBatch(orderCode: Building.OrderCode): List<Parking>

    /** 入居前合算のテナント契約情報取得 SQL_EB800_VIEW4(sql3) */
    fun findAggregateTenantContract(tenantContractNumber: String): AggregateTenantContractPojo?

    /** 前テナント契約情報取得 SQL_EB800_VIEW3(sql4) */
    fun findPreviousTenantContract(
        orderCode: Building.OrderCode,
        parkingCode: String,
        tenantContractNumber: String
    ): PreviousParkingTenantContractPojo?

    /**
     * テナント契約一括残集ファイル情報取得 SQL_EB800_EDCSELECT(sql5)改
     * いい物件仕様に加えて、部屋テナント契約を取得して部屋番号や入居者情報を取得する
     * ウェルカムパーク向けバッチCSVでは出力対象外項目
     * */
    fun findBulkTenantContract(
        parkingTenantContractNumber: String,
    ): PropertyTenantContractPojo?
}
