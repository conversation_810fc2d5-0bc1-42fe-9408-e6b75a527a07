package jp.ne.simplex.application.controller.external.property

import jp.ne.simplex.application.controller.external.ExternalRootController
import jp.ne.simplex.application.controller.external.property.dto.ExternalTemporaryReservationUpdateRequest
import jp.ne.simplex.application.service.TemporaryReservationService
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.openapi.ApiDefinition
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController

@RestController
class ExternalPropertyController(
    private val temporaryReservationService: TemporaryReservationService,
) : ExternalRootController() {
    @PostMapping("/update-temporary-reservation")
    @ApiDefinition(
        summary = "仮押さえ情報更新",
        description = "いい物件ボードから物件の仮押さえ情報を連携するAPI",
    )
    fun updateTemporaryReservation(
        @RequestBody request: ExternalTemporaryReservationUpdateRequest,
        @AuthenticationPrincipal apiKey: AuthInfo.ApiKey,
    ) {
        return temporaryReservationService.update(
            request.toServiceInterface(),
            apiKey.getRequestUser()
        )
    }
}
