package jp.ne.simplex.application.repository.mail

import jp.ne.simplex.application.model.MailProperty
import java.io.BufferedReader
import java.io.InputStreamReader

interface MailRepository {
    companion object {
        const val ENCODE_UTF_8 = "UTF-8"
    }

    /** メール送信処理 */
    fun sendMailUP(mailProperty: MailProperty)

    fun createSubjectAndBodyFromFile(mailProperty: MailProperty): Pair<String, String> {
        val fileName = mailProperty.mailTemplateType.fileName
        // ClassLoaderを使用してリソースを取得
        val resourceStream = this::class.java.classLoader.getResourceAsStream("$fileName.txt")
            ?: throw IllegalArgumentException("File not found: $fileName.txt")

        BufferedReader(InputStreamReader(resourceStream, ENCODE_UTF_8)).use { reader ->
            val lines = reader.readLines()
            if (lines.isEmpty()) throw IllegalArgumentException("ファイルが空です: $fileName.txt")
            // 最初の行をタイトルとして設定し、残りをメッセージボディとして結合
            return Pair(
                lines[0],
                replaceWords(lines.drop(1).joinToString("\n"), mailProperty.messageParam)
            )
        }
    }

    fun replaceWords(templateText: String, messageParam: Map<String, String>): String {
        return messageParam.entries.fold(templateText) { resultMsg, (key, value) ->
            resultMsg.replace("{$key}", value)
        }
    }
}
