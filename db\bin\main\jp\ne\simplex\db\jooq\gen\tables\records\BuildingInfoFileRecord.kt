/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.BuildingInfoFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingInfoFilePojo

import org.jooq.impl.TableRecordImpl


/**
 * 建物情報ファイル 既存システム物理名: BGZFDP
 */
@Suppress("UNCHECKED_CAST")
open class BuildingInfoFileRecord private constructor() : TableRecordImpl<BuildingInfoFileRecord>(BuildingInfoFileTable.BUILDING_INFO_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgram: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var deleteFlag: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var orderCd: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var additionalCd: Short?
        set(value): Unit = set(8, value)
        get(): Short? = get(8) as Short?

    open var recordCategory: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var changeHistoryNo: Short?
        set(value): Unit = set(10, value)
        get(): Short? = get(10) as Short?

    open var buildingNo: Byte?
        set(value): Unit = set(11, value)
        get(): Byte? = get(11) as Byte?

    open var numberOfBuildings: Short?
        set(value): Unit = set(12, value)
        get(): Short? = get(12) as Short?

    open var siteArea: BigDecimal?
        set(value): Unit = set(13, value)
        get(): BigDecimal? = get(13) as BigDecimal?

    open var buildingCoverageRatio: BigDecimal?
        set(value): Unit = set(14, value)
        get(): BigDecimal? = get(14) as BigDecimal?

    open var floorAreaRatio: BigDecimal?
        set(value): Unit = set(15, value)
        get(): BigDecimal? = get(15) as BigDecimal?

    open var usageAreaCategory: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var fireProtectionAreaCategory: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var zoneCategory: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var snowAccumulationCategory: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var temperatureCategory: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var buildingCategory: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var productNameCd: Short?
        set(value): Unit = set(22, value)
        get(): Short? = get(22) as Short?

    open var productCdBranch: Byte?
        set(value): Unit = set(23, value)
        get(): Byte? = get(23) as Byte?

    open var productSerialNo: Short?
        set(value): Unit = set(24, value)
        get(): Short? = get(24) as Short?

    open var standardSpecialOrderCategory: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var buildingTypeCdSt: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var buildingArea: BigDecimal?
        set(value): Unit = set(27, value)
        get(): BigDecimal? = get(27) as BigDecimal?

    open var totalFloorArea: BigDecimal?
        set(value): Unit = set(28, value)
        get(): BigDecimal? = get(28) as BigDecimal?

    open var eaveHeight: BigDecimal?
        set(value): Unit = set(29, value)
        get(): BigDecimal? = get(29) as BigDecimal?

    open var officeCategory: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var officeArea: BigDecimal?
        set(value): Unit = set(31, value)
        get(): BigDecimal? = get(31) as BigDecimal?

    open var toiletCategory: String?
        set(value): Unit = set(32, value)
        get(): String? = get(32) as String?

    open var septicTankCategory: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var septicTankCapacity: Short?
        set(value): Unit = set(34, value)
        get(): Short? = get(34) as Short?

    open var gasCategory: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var standardRoofSpecBase: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var standardRoofSpecFinish: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var standardWallSpecBase: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var standardWallSpecFinish: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var numberOfAboveGroundFloors: Byte?
        set(value): Unit = set(40, value)
        get(): Byte? = get(40) as Byte?

    open var numberOfBasementFloors: Byte?
        set(value): Unit = set(41, value)
        get(): Byte? = get(41) as Byte?

    open var houseAlignment: Byte?
        set(value): Unit = set(42, value)
        get(): Byte? = get(42) as Byte?

    open var numberOfCommercialUnits: Short?
        set(value): Unit = set(43, value)
        get(): Short? = get(43) as Short?

    open var numberOfResidentialUnits: Short?
        set(value): Unit = set(44, value)
        get(): Short? = get(44) as Short?

    open var structureCategory: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var staircaseType: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var entranceType: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var floorPlanCategory: String?
        set(value): Unit = set(48, value)
        get(): String? = get(48) as String?

    open var shopResidenceCategory: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    open var frontage: BigDecimal?
        set(value): Unit = set(50, value)
        get(): BigDecimal? = get(50) as BigDecimal?

    open var depth: BigDecimal?
        set(value): Unit = set(51, value)
        get(): BigDecimal? = get(51) as BigDecimal?

    open var foundationShape: String?
        set(value): Unit = set(52, value)
        get(): String? = get(52) as String?

    open var specialPurposeAreaCategory01: String?
        set(value): Unit = set(53, value)
        get(): String? = get(53) as String?

    open var specialPurposeAreaCategory02: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var specialPurposeAreaCategory03: String?
        set(value): Unit = set(55, value)
        get(): String? = get(55) as String?

    open var specialPurposeAreaCategory04: String?
        set(value): Unit = set(56, value)
        get(): String? = get(56) as String?

    open var specialPurposeAreaCategory05: String?
        set(value): Unit = set(57, value)
        get(): String? = get(57) as String?

    open var contractAmountTotal: Long?
        set(value): Unit = set(58, value)
        get(): Long? = get(58) as Long?

    open var listPrice: Long?
        set(value): Unit = set(59, value)
        get(): Long? = get(59) as Long?

    open var tacNo: Long?
        set(value): Unit = set(60, value)
        get(): Long? = get(60) as Long?

    open var contractAmountMain: Long?
        set(value): Unit = set(61, value)
        get(): Long? = get(61) as Long?

    open var contractMainTaxExcl: Long?
        set(value): Unit = set(62, value)
        get(): Long? = get(62) as Long?

    open var contractMainTax1: Long?
        set(value): Unit = set(63, value)
        get(): Long? = get(63) as Long?

    open var contractMainTax2: Long?
        set(value): Unit = set(64, value)
        get(): Long? = get(64) as Long?

    open var contractAmountAncillary: Long?
        set(value): Unit = set(65, value)
        get(): Long? = get(65) as Long?

    open var contractAncillaryTaxExcl: Long?
        set(value): Unit = set(66, value)
        get(): Long? = get(66) as Long?

    open var contractAncillaryTax1: Long?
        set(value): Unit = set(67, value)
        get(): Long? = get(67) as Long?

    open var contractAncillaryTax2: Long?
        set(value): Unit = set(68, value)
        get(): Long? = get(68) as Long?

    open var contractAmountExternal: Long?
        set(value): Unit = set(69, value)
        get(): Long? = get(69) as Long?

    open var contractExternalTaxExcl: Long?
        set(value): Unit = set(70, value)
        get(): Long? = get(70) as Long?

    open var contractExternalTax1: Long?
        set(value): Unit = set(71, value)
        get(): Long? = get(71) as Long?

    open var contractExternalTax2: Long?
        set(value): Unit = set(72, value)
        get(): Long? = get(72) as Long?

    open var contractAmountOther: Long?
        set(value): Unit = set(73, value)
        get(): Long? = get(73) as Long?

    open var contractOtherTaxExcl: Long?
        set(value): Unit = set(74, value)
        get(): Long? = get(74) as Long?

    open var contractOtherTax1: Long?
        set(value): Unit = set(75, value)
        get(): Long? = get(75) as Long?

    open var contractOtherTax2: Long?
        set(value): Unit = set(76, value)
        get(): Long? = get(76) as Long?

    open var buildingCd: String?
        set(value): Unit = set(77, value)
        get(): String? = get(77) as String?

    open var orderCdSt: String?
        set(value): Unit = set(78, value)
        get(): String? = get(78) as String?

    open var additionalCdSt: String?
        set(value): Unit = set(79, value)
        get(): String? = get(79) as String?

    open var buildingNoSt: String?
        set(value): Unit = set(80, value)
        get(): String? = get(80) as String?

    open var petFriendlyFlag: String?
        set(value): Unit = set(81, value)
        get(): String? = get(81) as String?

    open var electricityType: String?
        set(value): Unit = set(82, value)
        get(): String? = get(82) as String?

    open var solarBusinessType: String?
        set(value): Unit = set(83, value)
        get(): String? = get(83) as String?

    open var solarPowerOutput: BigDecimal?
        set(value): Unit = set(84, value)
        get(): BigDecimal? = get(84) as BigDecimal?

    open var zehFlag: String?
        set(value): Unit = set(85, value)
        get(): String? = get(85) as String?

    /**
     * Create a detached, initialised BuildingInfoFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgram: String? = null, updater: String? = null, deleteFlag: String? = null, orderCd: Int? = null, additionalCd: Short? = null, recordCategory: String? = null, changeHistoryNo: Short? = null, buildingNo: Byte? = null, numberOfBuildings: Short? = null, siteArea: BigDecimal? = null, buildingCoverageRatio: BigDecimal? = null, floorAreaRatio: BigDecimal? = null, usageAreaCategory: String? = null, fireProtectionAreaCategory: String? = null, zoneCategory: String? = null, snowAccumulationCategory: String? = null, temperatureCategory: String? = null, buildingCategory: String? = null, productNameCd: Short? = null, productCdBranch: Byte? = null, productSerialNo: Short? = null, standardSpecialOrderCategory: String? = null, buildingTypeCdSt: String? = null, buildingArea: BigDecimal? = null, totalFloorArea: BigDecimal? = null, eaveHeight: BigDecimal? = null, officeCategory: String? = null, officeArea: BigDecimal? = null, toiletCategory: String? = null, septicTankCategory: String? = null, septicTankCapacity: Short? = null, gasCategory: String? = null, standardRoofSpecBase: String? = null, standardRoofSpecFinish: String? = null, standardWallSpecBase: String? = null, standardWallSpecFinish: String? = null, numberOfAboveGroundFloors: Byte? = null, numberOfBasementFloors: Byte? = null, houseAlignment: Byte? = null, numberOfCommercialUnits: Short? = null, numberOfResidentialUnits: Short? = null, structureCategory: String? = null, staircaseType: String? = null, entranceType: String? = null, floorPlanCategory: String? = null, shopResidenceCategory: String? = null, frontage: BigDecimal? = null, depth: BigDecimal? = null, foundationShape: String? = null, specialPurposeAreaCategory01: String? = null, specialPurposeAreaCategory02: String? = null, specialPurposeAreaCategory03: String? = null, specialPurposeAreaCategory04: String? = null, specialPurposeAreaCategory05: String? = null, contractAmountTotal: Long? = null, listPrice: Long? = null, tacNo: Long? = null, contractAmountMain: Long? = null, contractMainTaxExcl: Long? = null, contractMainTax1: Long? = null, contractMainTax2: Long? = null, contractAmountAncillary: Long? = null, contractAncillaryTaxExcl: Long? = null, contractAncillaryTax1: Long? = null, contractAncillaryTax2: Long? = null, contractAmountExternal: Long? = null, contractExternalTaxExcl: Long? = null, contractExternalTax1: Long? = null, contractExternalTax2: Long? = null, contractAmountOther: Long? = null, contractOtherTaxExcl: Long? = null, contractOtherTax1: Long? = null, contractOtherTax2: Long? = null, buildingCd: String? = null, orderCdSt: String? = null, additionalCdSt: String? = null, buildingNoSt: String? = null, petFriendlyFlag: String? = null, electricityType: String? = null, solarBusinessType: String? = null, solarPowerOutput: BigDecimal? = null, zehFlag: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgram = updateProgram
        this.updater = updater
        this.deleteFlag = deleteFlag
        this.orderCd = orderCd
        this.additionalCd = additionalCd
        this.recordCategory = recordCategory
        this.changeHistoryNo = changeHistoryNo
        this.buildingNo = buildingNo
        this.numberOfBuildings = numberOfBuildings
        this.siteArea = siteArea
        this.buildingCoverageRatio = buildingCoverageRatio
        this.floorAreaRatio = floorAreaRatio
        this.usageAreaCategory = usageAreaCategory
        this.fireProtectionAreaCategory = fireProtectionAreaCategory
        this.zoneCategory = zoneCategory
        this.snowAccumulationCategory = snowAccumulationCategory
        this.temperatureCategory = temperatureCategory
        this.buildingCategory = buildingCategory
        this.productNameCd = productNameCd
        this.productCdBranch = productCdBranch
        this.productSerialNo = productSerialNo
        this.standardSpecialOrderCategory = standardSpecialOrderCategory
        this.buildingTypeCdSt = buildingTypeCdSt
        this.buildingArea = buildingArea
        this.totalFloorArea = totalFloorArea
        this.eaveHeight = eaveHeight
        this.officeCategory = officeCategory
        this.officeArea = officeArea
        this.toiletCategory = toiletCategory
        this.septicTankCategory = septicTankCategory
        this.septicTankCapacity = septicTankCapacity
        this.gasCategory = gasCategory
        this.standardRoofSpecBase = standardRoofSpecBase
        this.standardRoofSpecFinish = standardRoofSpecFinish
        this.standardWallSpecBase = standardWallSpecBase
        this.standardWallSpecFinish = standardWallSpecFinish
        this.numberOfAboveGroundFloors = numberOfAboveGroundFloors
        this.numberOfBasementFloors = numberOfBasementFloors
        this.houseAlignment = houseAlignment
        this.numberOfCommercialUnits = numberOfCommercialUnits
        this.numberOfResidentialUnits = numberOfResidentialUnits
        this.structureCategory = structureCategory
        this.staircaseType = staircaseType
        this.entranceType = entranceType
        this.floorPlanCategory = floorPlanCategory
        this.shopResidenceCategory = shopResidenceCategory
        this.frontage = frontage
        this.depth = depth
        this.foundationShape = foundationShape
        this.specialPurposeAreaCategory01 = specialPurposeAreaCategory01
        this.specialPurposeAreaCategory02 = specialPurposeAreaCategory02
        this.specialPurposeAreaCategory03 = specialPurposeAreaCategory03
        this.specialPurposeAreaCategory04 = specialPurposeAreaCategory04
        this.specialPurposeAreaCategory05 = specialPurposeAreaCategory05
        this.contractAmountTotal = contractAmountTotal
        this.listPrice = listPrice
        this.tacNo = tacNo
        this.contractAmountMain = contractAmountMain
        this.contractMainTaxExcl = contractMainTaxExcl
        this.contractMainTax1 = contractMainTax1
        this.contractMainTax2 = contractMainTax2
        this.contractAmountAncillary = contractAmountAncillary
        this.contractAncillaryTaxExcl = contractAncillaryTaxExcl
        this.contractAncillaryTax1 = contractAncillaryTax1
        this.contractAncillaryTax2 = contractAncillaryTax2
        this.contractAmountExternal = contractAmountExternal
        this.contractExternalTaxExcl = contractExternalTaxExcl
        this.contractExternalTax1 = contractExternalTax1
        this.contractExternalTax2 = contractExternalTax2
        this.contractAmountOther = contractAmountOther
        this.contractOtherTaxExcl = contractOtherTaxExcl
        this.contractOtherTax1 = contractOtherTax1
        this.contractOtherTax2 = contractOtherTax2
        this.buildingCd = buildingCd
        this.orderCdSt = orderCdSt
        this.additionalCdSt = additionalCdSt
        this.buildingNoSt = buildingNoSt
        this.petFriendlyFlag = petFriendlyFlag
        this.electricityType = electricityType
        this.solarBusinessType = solarBusinessType
        this.solarPowerOutput = solarPowerOutput
        this.zehFlag = zehFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BuildingInfoFileRecord
     */
    constructor(value: BuildingInfoFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgram = value.updateProgram
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            this.orderCd = value.orderCd
            this.additionalCd = value.additionalCd
            this.recordCategory = value.recordCategory
            this.changeHistoryNo = value.changeHistoryNo
            this.buildingNo = value.buildingNo
            this.numberOfBuildings = value.numberOfBuildings
            this.siteArea = value.siteArea
            this.buildingCoverageRatio = value.buildingCoverageRatio
            this.floorAreaRatio = value.floorAreaRatio
            this.usageAreaCategory = value.usageAreaCategory
            this.fireProtectionAreaCategory = value.fireProtectionAreaCategory
            this.zoneCategory = value.zoneCategory
            this.snowAccumulationCategory = value.snowAccumulationCategory
            this.temperatureCategory = value.temperatureCategory
            this.buildingCategory = value.buildingCategory
            this.productNameCd = value.productNameCd
            this.productCdBranch = value.productCdBranch
            this.productSerialNo = value.productSerialNo
            this.standardSpecialOrderCategory = value.standardSpecialOrderCategory
            this.buildingTypeCdSt = value.buildingTypeCdSt
            this.buildingArea = value.buildingArea
            this.totalFloorArea = value.totalFloorArea
            this.eaveHeight = value.eaveHeight
            this.officeCategory = value.officeCategory
            this.officeArea = value.officeArea
            this.toiletCategory = value.toiletCategory
            this.septicTankCategory = value.septicTankCategory
            this.septicTankCapacity = value.septicTankCapacity
            this.gasCategory = value.gasCategory
            this.standardRoofSpecBase = value.standardRoofSpecBase
            this.standardRoofSpecFinish = value.standardRoofSpecFinish
            this.standardWallSpecBase = value.standardWallSpecBase
            this.standardWallSpecFinish = value.standardWallSpecFinish
            this.numberOfAboveGroundFloors = value.numberOfAboveGroundFloors
            this.numberOfBasementFloors = value.numberOfBasementFloors
            this.houseAlignment = value.houseAlignment
            this.numberOfCommercialUnits = value.numberOfCommercialUnits
            this.numberOfResidentialUnits = value.numberOfResidentialUnits
            this.structureCategory = value.structureCategory
            this.staircaseType = value.staircaseType
            this.entranceType = value.entranceType
            this.floorPlanCategory = value.floorPlanCategory
            this.shopResidenceCategory = value.shopResidenceCategory
            this.frontage = value.frontage
            this.depth = value.depth
            this.foundationShape = value.foundationShape
            this.specialPurposeAreaCategory01 = value.specialPurposeAreaCategory01
            this.specialPurposeAreaCategory02 = value.specialPurposeAreaCategory02
            this.specialPurposeAreaCategory03 = value.specialPurposeAreaCategory03
            this.specialPurposeAreaCategory04 = value.specialPurposeAreaCategory04
            this.specialPurposeAreaCategory05 = value.specialPurposeAreaCategory05
            this.contractAmountTotal = value.contractAmountTotal
            this.listPrice = value.listPrice
            this.tacNo = value.tacNo
            this.contractAmountMain = value.contractAmountMain
            this.contractMainTaxExcl = value.contractMainTaxExcl
            this.contractMainTax1 = value.contractMainTax1
            this.contractMainTax2 = value.contractMainTax2
            this.contractAmountAncillary = value.contractAmountAncillary
            this.contractAncillaryTaxExcl = value.contractAncillaryTaxExcl
            this.contractAncillaryTax1 = value.contractAncillaryTax1
            this.contractAncillaryTax2 = value.contractAncillaryTax2
            this.contractAmountExternal = value.contractAmountExternal
            this.contractExternalTaxExcl = value.contractExternalTaxExcl
            this.contractExternalTax1 = value.contractExternalTax1
            this.contractExternalTax2 = value.contractExternalTax2
            this.contractAmountOther = value.contractAmountOther
            this.contractOtherTaxExcl = value.contractOtherTaxExcl
            this.contractOtherTax1 = value.contractOtherTax1
            this.contractOtherTax2 = value.contractOtherTax2
            this.buildingCd = value.buildingCd
            this.orderCdSt = value.orderCdSt
            this.additionalCdSt = value.additionalCdSt
            this.buildingNoSt = value.buildingNoSt
            this.petFriendlyFlag = value.petFriendlyFlag
            this.electricityType = value.electricityType
            this.solarBusinessType = value.solarBusinessType
            this.solarPowerOutput = value.solarPowerOutput
            this.zehFlag = value.zehFlag
            resetChangedOnNotNull()
        }
    }
}
