/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.SiteCommentRegistrationTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.SiteCommentRegistrationPojo

import org.jooq.Record4
import org.jooq.impl.UpdatableRecordImpl


/**
 * 各サイトコメント登録 既存システム物理名: EMEKKP
 */
@Suppress("UNCHECKED_CAST")
open class SiteCommentRegistrationRecord private constructor() : UpdatableRecordImpl<SiteCommentRegistrationRecord>(SiteCommentRegistrationTable.SITE_COMMENT_REGISTRATION) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var buildingCd: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var roomCd: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var linkedCompanyCd: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    open var linkedCompanyTextType: String
        set(value): Unit = set(9, value)
        get(): String = get(9) as String

    open var comment: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record4<String?, String?, String?, String?> = super.key() as Record4<String?, String?, String?, String?>

    /**
     * Create a detached, initialised SiteCommentRegistrationRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, buildingCd: String, roomCd: String, linkedCompanyCd: String, linkedCompanyTextType: String, comment: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.buildingCd = buildingCd
        this.roomCd = roomCd
        this.linkedCompanyCd = linkedCompanyCd
        this.linkedCompanyTextType = linkedCompanyTextType
        this.comment = comment
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised SiteCommentRegistrationRecord
     */
    constructor(value: SiteCommentRegistrationPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.buildingCd = value.buildingCd
            this.roomCd = value.roomCd
            this.linkedCompanyCd = value.linkedCompanyCd
            this.linkedCompanyTextType = value.linkedCompanyTextType
            this.comment = value.comment
            resetChangedOnNotNull()
        }
    }
}
