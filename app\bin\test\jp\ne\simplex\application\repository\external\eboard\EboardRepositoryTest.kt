package jp.ne.simplex.application.repository.external.eboard

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.aws.SecretManagerRepository
import jp.ne.simplex.application.repository.external.eboard.config.EboardApiPath
import jp.ne.simplex.application.repository.external.eboard.config.EboardRestConfig
import jp.ne.simplex.application.repository.external.eboard.config.EboardTokenManager
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.exception.ExternalApiServerException
import jp.ne.simplex.mock.MockOfficeRepository
import jp.ne.simplex.mock.MockSecretManagerClient
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import jp.ne.simplex.stub.*
import org.hamcrest.CoreMatchers.containsString
import org.hamcrest.CoreMatchers.startsWith
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.assertTrue
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.test.web.client.MockRestServiceServer
import org.springframework.test.web.client.match.MockRestRequestMatchers.*
import org.springframework.test.web.client.response.DefaultResponseCreator
import org.springframework.test.web.client.response.MockRestResponseCreators.withBadRequest
import org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess
import org.springframework.util.LinkedMultiValueMap
import org.springframework.web.client.RestClient
import java.time.LocalDate
import kotlin.test.assertEquals

class EboardRepositoryTest {
    companion object {
        private const val SECRET_KEY = "secretId"
        private const val ENDPOINT = "http://example.com"
        private const val TOKEN = "dummy_access_token"
    }

    private val builder = RestClient.builder()

    private val mockServer = MockRestServiceServer.bindTo(builder).ignoreExpectOrder(true).build()

    private val secretManager = MockSecretManagerClient()

    private val repository = EboardRepository(
        EboardRestConfig(ENDPOINT).restClient(builder),
        SECRET_KEY,
        SecretManagerRepository(secretManager),
        MockOfficeRepository(),
    )

    @BeforeEach
    fun setup() {
        EboardTokenManager.updateToken(TOKEN)
    }

    @AfterEach
    fun tearDown() {
        EboardTokenManager.clear()
    }

    @Nested
    @DisplayName("認証APIの検証")
    inner class Scenario1 {

        @Test
        @DisplayName("リクエストボディが適切に生成されること")
        fun case1() {
            // expected
            val requestBody = """
                {
                    "userid": "dummy",
                    "password": "dummy",
                    "passphrase": "dummy"
                }
            """.trimIndent()

            // setup && verify
            secretManager.add(SECRET_KEY, requestBody)

            mockServer.expect(
                requestTo(
                    "$ENDPOINT${EboardApiPath.TOKEN_DP_ISSUE.value}"
                )
            ) // このURLに
                .andExpect(content().json(requestBody)) // ボディを設定して
                .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                .andExpect(header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE))
                .andRespond(  // 以下のレスポンスが返却される
                    withSuccess(
                        """
                        {
                            "expired_dt":"2025-02-03T03:44:19.449Z",
                            "issue_id":"6cb6f18b-845e-4d10-846a-0275d33ca8f1",
                            "accesstoken":"c74f8415-9871-4f51-9458-06cd3d30e224"
                        }
                        """.trimIndent(),
                        MediaType.APPLICATION_JSON
                    )
                )

            // execute
            repository.login()

            // verify
            mockServer.verify()
        }
    }

    @Nested
    @DisplayName("仮押さえ登録/解除APIの検証")
    inner class Scenario2 {

        @Nested
        @DisplayName("リクエストパラメータが適切に生成されること")
        inner class Scenario2x1 {

            @Test
            @DisplayName("任意項目のパラメータ（ここではコメント）が設定済の場合、その項目はリクエストに含まれること")
            fun case1() {
                // setup
                val input = stubOwnCompanyRegisterTemporaryReservation(
                    buildingCode = "*********",
                    roomCode = "01010",
                    assignedBranchCode = "987",
                    assignedBranchName = "渋谷支店",
                    assignedBranchCompany = Company.DaitouKentakuLeasing,
                    assignedEmployeeCode = "000011",
                    assignedEmployeeName = "山田太郎",
                    scheduledMoveInDate = LocalDate.of(2024, 12, 19),
                    comment = "◯◯◯◯、◯◯◯◯◯",
                    updateDate = "123147",
                    updateTime = "20240215",
                )

                val expected =
                    "${ENDPOINT}${EboardApiPath.UPDATE_KARIOSAE.value}" + //
                            "?tatemonoCd=${input.getId().buildingCode.value}" + //
                            "&heyaCd=${input.getId().roomCode.value}" + //
                            "&updKbn=1" + //
                            "&nYoteiDate=20241219" + //
                            "&firmId=DKL${input.assignedBranch.code.getPrefix()}" + //
                            "&firmName=${input.assignedBranch.company.name.urlEncode()}" + //
                            "&branchName=${input.assignedBranch.name.value.urlEncode()}" + //
                            "&regName=${input.assignedEmployee.name.kanji.urlEncode()}" + //
                            "&staffCd=${input.assignedEmployee.code.value}" + //
                            "&comment=${input.comment.value.urlEncode()}"

                // setup && verify
                mockServer.expect(requestTo(expected)) // expected の URLに
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(method(HttpMethod.GET))  // GETリクエストで送信すると
                    .andRespond(
                        withSuccess(
                            """
                            {
                                "result":0,
                                "message":"",
                                "updateResult":"OK"
                            }
                        """.trimIndent(), MediaType.APPLICATION_JSON
                        )
                    )

                // execute
                repository.registerTemporaryReservation(input)

                // verify
                mockServer.verify()
            }

            @Test
            @DisplayName("任意項目のパラメータが未設定（ここではスタッフコード）の場合、その項目はリクエストに含まれないこと")
            fun case2() {
                // setup
                val scheduledMoveInDate = "20241219"

                val input = stubCancelTemporaryReservation(
                    buildingCode = "*********",
                    roomCode = "01010",
                    comment = null,
                    updateDate = "123147",
                    updateTime = "20240215"
                )

                val canceled = stubOwnCompanyTemporaryReservationInfo(
                    assignedBranchCode = "932",
                    assignedBranchCompany = Company.DaitouKentakuLeasing,
                    assignedBranchName = "銀座支店",
                    assignedEmployeeName = "山田太郎",
                    scheduledMoveInDate = scheduledMoveInDate.yyyyMMdd(),
                )

                val expected =
                    "${ENDPOINT}${EboardApiPath.UPDATE_KARIOSAE.value}" + //
                            "?tatemonoCd=${input.getId().buildingCode.value}" + //
                            "&heyaCd=${input.getId().roomCode.value}" + //
                            "&updKbn=0" + //
                            "&nYoteiDate=${scheduledMoveInDate}" + //
                            "&firmId=DKL${canceled.assignedBranch.code.getPrefix()}" + //
                            "&firmName=${canceled.assignedBranch.company.name.urlEncode()}" + //
                            "&branchName=${canceled.assignedBranch.name.value.urlEncode()}" + //
                            "&regName=${canceled.assignedEmployee.name.kanji.urlEncode()}" + //
                            "&comment=" // コメントがnullの時は、空文字

                // setup && verify
                mockServer.expect(requestTo(expected)) // expected の URLに
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(method(HttpMethod.GET))  // GETリクエストで送信すると
                    .andRespond(
                        withSuccess(
                            """
                            {
                                "result":0,
                                "message":"",
                                "updateResult":"OK"
                            }
                        """.trimIndent(), MediaType.APPLICATION_JSON
                        )
                    )

                // execute
                repository.cancelTemporaryReservation(input, canceled)

                // verify
                mockServer.verify()
            }
        }

        @Nested
        @DisplayName("エラーレスポンスを受信した場合、Exceptionがスローされること")
        inner class Scenario2x2 {

            @Test
            @DisplayName("仮おさえ登録処理で、「HTTPステータス200, updateResult=NG」レスポンスを受信した場合、Exceptionがスローされること")
            fun case1() {
                // setup && verify
                mockServer
                    .expect(
                        requestTo(containsString("${ENDPOINT}${EboardApiPath.UPDATE_KARIOSAE.value}"))
                    )
                    .andRespond(
                        withSuccess(
                            """
                            {
                                "result":0,
                                "message":"",
                                "updateResult":"NG"
                            }
                        """.trimIndent(), MediaType.APPLICATION_JSON
                        )
                    )

                // execute
                val result = assertThrows<ExternalApiServerException> {
                    repository.registerTemporaryReservation(
                        stubOwnCompanyRegisterTemporaryReservation()
                    )
                }
                assertEquals(ErrorType.EBOARD_API_ERROR, result.type)
            }

            @Test
            @DisplayName("仮おさえ解除処理で、「HTTPステータス200, updateResult=NG」レスポンスを受信した場合、Exceptionがスローされること")
            fun case2() {
                // setup && verify
                mockServer
                    .expect(
                        requestTo(containsString("${ENDPOINT}${EboardApiPath.UPDATE_KARIOSAE.value}"))
                    )
                    .andRespond(
                        withSuccess(
                            """
                            {
                                "result":0,
                                "message":"",
                                "updateResult":"NG"
                            }
                        """.trimIndent(), MediaType.APPLICATION_JSON
                        )
                    )

                // execute
                val result = assertThrows<ExternalApiServerException> {
                    repository.cancelTemporaryReservation(
                        stubCancelTemporaryReservation(),
                        stubOwnCompanyTemporaryReservationInfo()
                    )
                }
                assertEquals(ErrorType.EBOARD_API_ERROR, result.type)
            }
        }
    }

    @Nested
    @DisplayName("公開指示APIの検証")
    inner class Scenario3 {

        val apiPath = "${ENDPOINT}${EboardApiPath.INSTRUCT_PUBLIC.value}"

        @Nested
        @DisplayName("リクエストパラメータが適切に生成されること")
        inner class Scenario3x1 {

            @Test
            @DisplayName("リクエストボディが適切に生成されること")
            fun case1() {
                // setup
                val request1 = stubUpdatePropertyMaintenance(
                    buildingCode = "*********",
                    roomCode = "01010",
                    publishStatus = PropertyMaintenance.PublishStatus.PUBLIC
                ).getUpdatePublishStatus()

                val request2 = stubUpdatePropertyMaintenance(
                    buildingCode = "*********",
                    roomCode = "02020",
                    publishStatus = PropertyMaintenance.PublishStatus.PRIVATE
                ).getUpdatePublishStatus()

                val requestUser = stubJwtRequestUser()

                // expected
                val requestBody = """
                {
                    "list": [
                        {
                            "tatemonoCd": "${request1.id.buildingCode.value}",
                            "heyaCd": "${request1.id.roomCode.value}",
                            "userId": "${requestUser.value}",
                            "keisai": "${request1.publishStatus.value}"
                        },
                        {
                            "tatemonoCd": "${request2.id.buildingCode.value}",
                            "heyaCd": "${request2.id.roomCode.value}",
                            "userId": "${requestUser.value}",
                            "keisai": "${request2.publishStatus.value}"
                        }
                    ]
                }
            """.trimIndent()

                // setup && verify
                mockServer.expect(requestTo(apiPath)) // このURLに
                    .andExpect(content().json(requestBody)) // ボディを設定して
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE))
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                        {
                            "result":"0",
                            "message":"",
                            "list":[]
                        }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                // execute
                repository.instructPublic(requestUser, listOf(request1, request2))

                // verify
                mockServer.verify()
            }
        }

        @Nested
        @DisplayName("APIレスポンスに応じた関数の戻り値（成功した物件ID/失敗した物件ID）を生成できること")
        inner class Scenario3x2 {

            @Test
            @DisplayName("4,5xx系エラーが帰ってきた場合、成功した物件ID=0, 失敗した物件ID=ALL の戻り値が生成されること")
            fun case1() {
                // setup && verify
                val request1 =
                    stubUpdatePropertyMaintenance(buildingCode = "634623600").getUpdatePublishStatus()
                val request2 =
                    stubUpdatePropertyMaintenance(buildingCode = "143251300").getUpdatePublishStatus()

                mockServer.expect(requestTo(apiPath))
                    .andRespond(withBadRequest())

                // execute
                val result =
                    repository.instructPublic(stubJwtRequestUser(), listOf(request1, request2))

                // verify
                assertEquals(0, result.success.size)
                assertEquals(2, result.failed.size)
            }

            @Test
            @DisplayName("HTTPステータスが200の場合、レスポンスの中身に応じて戻り値が生成されること")
            fun case2() {
                // setup && verify
                val request1 =
                    stubUpdatePropertyMaintenance(buildingCode = "634623600").getUpdatePublishStatus()
                val request2 =
                    stubUpdatePropertyMaintenance(buildingCode = "143251300").getUpdatePublishStatus()
                val request3 =
                    stubUpdatePropertyMaintenance(buildingCode = "543254500").getUpdatePublishStatus()
                val request4 =
                    stubUpdatePropertyMaintenance(buildingCode = "205454904").getUpdatePublishStatus()

                mockServer.expect(requestTo(apiPath))
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                        {
                            "result":"0",
                            "message":"",
                            "list":[
                                {
                                	"tatemonoCd":"${request1.id.buildingCode.value}",
                                	"heyaCd":"${request1.id.roomCode.value}",
                                	"updateResult":"OK",
                                    "up_state": "1",
                                	"message":""
                                },
                                {
                                    "tatemonoCd":"${request2.id.buildingCode.value}",
                                    "heyaCd":"${request2.id.roomCode.value}",
                                    "updateResult":"OK",
                                    "up_state": "5",
                                    "message":""
                                },
                                {
                                	"tatemonoCd":"${request3.id.buildingCode.value}",
                                	"heyaCd":"${request3.id.roomCode.value}",
                                	"updateResult":"NG",
                                    "up_state": "",
                                	"message":"公開指示の値が不正です。"
                                },
                                {
                                	"tatemonoCd":"${request4.id.buildingCode.value}",
                                	"heyaCd":"${request4.id.roomCode.value}",
                                	"updateResult":"OK",
                                    "up_state": "",
                                	"message":"公開指示に変更がないため未更新"
                                }
                            ]
                        }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                // execute
                val result =
                    repository.instructPublic(
                        stubJwtRequestUser(),
                        listOf(request1, request2, request3)
                    )

                // verify
                assertEquals(3, result.success.size)
                assertTrue(result.success.keys.contains(request1.id))
                assertEquals(result.success[request1.id], Property.UpState.PREPARING)
                assertEquals(result.success[request2.id], null)
                assertEquals(result.success[request2.id], null)

                assertEquals(1, result.failed.size)
                assertTrue(result.failed.contains(request3.id))
            }
        }
    }

    @Nested
    @DisplayName("駐車場配置図画像登録・削除API の検証")
    inner class Scenario4 {

        val apiPath = "${ENDPOINT}${EboardApiPath.UPDATE_PARKING_IMAGE.value}"

        @Nested
        @DisplayName("リクエストパラメータが適切に生成されること")
        inner class Scenario4x1 {

            @Test
            @DisplayName("駐車場配置図画像登録のリクエストボディが適切に生成されること")
            fun case1() {
                val requestUser = stubJwtRequestUser()
                val buildingCode = Building.Code.of("000130305")
                val imageFile = stubImageFile()
                val expectedMap = LinkedMultiValueMap<String, Any>().also {
                    it.add("tatemonoCd", buildingCode.value)
                    it.add("userId", requestUser.value)
                    it.add("updateKbn", "1")
                    it.add("InputStream", object : ByteArrayResource(imageFile.imageData) {
                        override fun getFilename() = imageFile.fileName
                    })
                }

                // setup && verify
                mockServer.expect(requestTo(apiPath)) // このURLに
                    .andExpect(content().multipartData(expectedMap)) // ボディを設定して
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(
                        header(
                            HttpHeaders.CONTENT_TYPE,
                            startsWith("multipart/form-data;boundary=")
                        )
                    )
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                            {
                                "result":0,
                                "message":"",
                                "updateResult":"OK"
                            }
                            """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                // execute
                repository.registerParkingImage(requestUser, buildingCode, imageFile)

                // verify
                mockServer.verify()
            }

            @Test
            @DisplayName("駐車場配置図画像削除のリクエストボディが適切に生成されること")
            fun case2() {
                val requestUser = stubJwtRequestUser()
                val buildingCode = Building.Code.of("000130305")
                val expectedMap = LinkedMultiValueMap<String, Any>().also {
                    it.add("tatemonoCd", buildingCode.value)
                    it.add("userId", requestUser.value)
                    it.add("updateKbn", "2")
                }

                // setup && verify
                mockServer.expect(requestTo(apiPath)) // このURLに
                    .andExpect(content().multipartData(expectedMap)) // ボディを設定して
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(
                        header(
                            HttpHeaders.CONTENT_TYPE,
                            startsWith("multipart/form-data;boundary=")
                        )
                    )
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                            {
                                "result":0,
                                "message":"",
                                "updateResult":"OK"
                            }
                            """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                // execute
                repository.deleteParkingImage(requestUser, buildingCode)

                // verify
                mockServer.verify()
            }
        }

        @Nested
        @DisplayName("エラーレスポンスを受信した場合、Exceptionがスローされること")
        inner class Scenario4x2 {

            @Test
            @DisplayName("駐車場配置図画像登録処理で、「HTTPステータス200, updateResult=NG」レスポンスを受信した場合、Exceptionがスローされること")
            fun case1() {
                // setup && verify
                mockServer.expect(
                    requestTo(containsString("${ENDPOINT}${EboardApiPath.UPDATE_PARKING_IMAGE.value}"))
                ).andRespond(
                    withSuccess(
                        """
                        {
                            "result":0,
                            "message":"",
                            "updateResult":"NG"
                        }
                        """.trimIndent(),
                        MediaType.APPLICATION_JSON
                    )
                )

                // execute
                val requestUser = stubJwtRequestUser()
                val buildingCode = Building.Code.of("000130305")
                val imageFile = stubImageFile()
                val result = assertThrows<ExternalApiServerException> {
                    repository.registerParkingImage(requestUser, buildingCode, imageFile)
                }
                assertEquals(ErrorType.EBOARD_API_ERROR, result.type)
            }

            @Test
            @DisplayName("駐車場配置図画像削除処理で、「HTTPステータス200, updateResult=NG」レスポンスを受信した場合、Exceptionがスローされること")
            fun case2() {
                // setup && verify
                mockServer.expect(
                    requestTo(containsString("${ENDPOINT}${EboardApiPath.UPDATE_PARKING_IMAGE.value}"))
                ).andRespond(
                    withSuccess(
                        """
                        {
                            "result":0,
                            "message":"",
                            "updateResult":"NG"
                        }
                        """.trimIndent(),
                        MediaType.APPLICATION_JSON
                    )
                )

                // execute
                val requestUser = stubJwtRequestUser()
                val buildingCode = Building.Code.of("000130305")
                val result = assertThrows<ExternalApiServerException> {
                    repository.deleteParkingImage(requestUser, buildingCode)
                }
                assertEquals(ErrorType.EBOARD_API_ERROR, result.type)
            }
        }
    }

    @Nested
    @DisplayName("ゴミ置場画像登録・削除API の検証")
    inner class Scenario5 {

        val apiPath = "${ENDPOINT}${EboardApiPath.UPDATE_GARBAGE_IMAGE.value}"

        @Nested
        @DisplayName("リクエストパラメータが適切に生成されること")
        inner class Scenario5x1 {

            @Test
            @DisplayName("ゴミ置場画像登録のリクエストボディが適切に生成されること")
            fun case1() {
                val requestUser = stubJwtRequestUser()
                val buildingCode = Building.Code.of("000130305")
                val imageFile =
                    stubImageFile(fileName = "dumpster.jpg", imagePath = "/dumpster.jpg")
                val expectedMap = LinkedMultiValueMap<String, Any>().also {
                    it.add("tatemonoCd", buildingCode.value)
                    it.add("userId", requestUser.value)
                    it.add("updateKbn", "1")
                    it.add("InputStream", object : ByteArrayResource(imageFile.imageData) {
                        override fun getFilename() = imageFile.fileName
                    })
                }

                // setup && verify
                mockServer.expect(requestTo(apiPath)) // このURLに
                    .andExpect(content().multipartData(expectedMap)) // ボディを設定して
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(
                        header(
                            HttpHeaders.CONTENT_TYPE,
                            startsWith("multipart/form-data;boundary=")
                        )
                    )
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                            {
                                "result":0,
                                "message":"",
                                "updateResult":"OK"
                            }
                            """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                // execute
                repository.registerGarbageImage(requestUser, buildingCode, imageFile)

                // verify
                mockServer.verify()
            }

            @Test
            @DisplayName("ゴミ置場画像削除のリクエストボディが適切に生成されること")
            fun case2() {
                val requestUser = stubJwtRequestUser()
                val buildingCode = Building.Code.of("000130305")
                val expectedMap = LinkedMultiValueMap<String, Any>().also {
                    it.add("tatemonoCd", buildingCode.value)
                    it.add("userId", requestUser.value)
                    it.add("updateKbn", "2")
                }

                // setup && verify
                mockServer.expect(requestTo(apiPath)) // このURLに
                    .andExpect(content().multipartData(expectedMap)) // ボディを設定して
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(
                        header(
                            HttpHeaders.CONTENT_TYPE,
                            startsWith("multipart/form-data;boundary=")
                        )
                    )
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                            {
                                "result":0,
                                "message":"",
                                "updateResult":"OK"
                            }
                            """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                // execute
                repository.deleteGarbageImage(requestUser, buildingCode)

                // verify
                mockServer.verify()
            }
        }

        @Nested
        @DisplayName("エラーレスポンスを受信した場合、Exceptionがスローされること")
        inner class Scenario5x2 {

            @Test
            @DisplayName("ゴミ置場画像登録処理で、「HTTPステータス200, updateResult=NG」レスポンスを受信した場合、Exceptionがスローされること")
            fun case1() {
                // setup && verify
                mockServer.expect(
                    requestTo(containsString("${ENDPOINT}${EboardApiPath.UPDATE_GARBAGE_IMAGE.value}"))
                ).andRespond(
                    withSuccess(
                        """
                        {
                            "result":0,
                            "message":"",
                            "updateResult":"NG"
                        }
                        """.trimIndent(),
                        MediaType.APPLICATION_JSON
                    )
                )

                // execute
                val requestUser = stubJwtRequestUser()
                val buildingCode = Building.Code.of("000130305")
                val imageFile =
                    stubImageFile(fileName = "dumpster.jpg", imagePath = "/dumpster.jpg")
                val result = assertThrows<ExternalApiServerException> {
                    repository.registerGarbageImage(requestUser, buildingCode, imageFile)
                }
                assertEquals(ErrorType.EBOARD_API_ERROR, result.type)
            }

            @Test
            @DisplayName("ゴミ置場画像削除処理で、「HTTPステータス200, updateResult=NG」レスポンスを受信した場合、Exceptionがスローされること")
            fun case2() {
                // setup && verify
                mockServer.expect(
                    requestTo(containsString("${ENDPOINT}${EboardApiPath.UPDATE_GARBAGE_IMAGE.value}"))
                ).andRespond(
                    withSuccess(
                        """
                        {
                            "result":0,
                            "message":"",
                            "updateResult":"NG"
                        }
                        """.trimIndent(),
                        MediaType.APPLICATION_JSON
                    )
                )

                // execute
                val requestUser = stubJwtRequestUser()
                val buildingCode = Building.Code.of("000130305")
                val result = assertThrows<ExternalApiServerException> {
                    repository.deleteGarbageImage(requestUser, buildingCode)
                }
                assertEquals(ErrorType.EBOARD_API_ERROR, result.type)
            }
        }
    }

    @Nested
    @DisplayName("駐車場予約APIの検証")
    inner class Scenario6 {

        val apiPathPrefix = "${ENDPOINT}${EboardApiPath.RESERVE_PARKING.value}"

        val successBody: DefaultResponseCreator =
            withSuccess(
                """
                    {
                        "result":0,
                        "message":"",
                        "updateResult":"OK"
                    }
                    """.trimIndent(),
                MediaType.APPLICATION_JSON
            )

        val failureBody: DefaultResponseCreator =
            withSuccess(
                """
                    {
                        "result":0,
                        "message":"",
                        "updateResult":"NG"
                    }
                    """.trimIndent(),
                MediaType.APPLICATION_JSON
            )

        @Nested
        @DisplayName("新規登録の場合・リクエストパラメータが適切に生成されること")
        inner class Scenario6x1 {

            @Test
            @DisplayName("nullableの項目が設定されていない場合、status=0でリクエストパラメータが適切に生成されること")
            fun case1() {
                val input = stubParkingReservationInfo()

                val expected = "${apiPathPrefix}?tatemonoCd=${input.buildingCode.value}" +
                        "&parkingCd=${input.parkingLotCode!!.value}" + // nullableだが、新規登録ではnullになるユースケースなし
                        "&status=0" +
                        "&uketukeDate=${input.receptionDate.yyyyMMdd()}" +
                        "&id=${input.eBoardParkingReservationId.value}"

                mockServer.expect(requestTo(expected))
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(
                        header(
                            HttpHeaders.CONTENT_TYPE,
                            MediaType.APPLICATION_FORM_URLENCODED_VALUE
                        )
                    )
                    .andExpect(method(HttpMethod.POST))
                    .andRespond(successBody)

                repository.reserveParking(stubRegisterParkingReservation(), input)
                mockServer.verify()
            }

            @Test
            @DisplayName("nullableの項目が全て設定されている場合、status=0でリクエストパラメータが適切に生成されること")
            fun case2() {
                val input = stubParkingReservationInfo(
                    reserverName = "受付者サンプル",
                    reserverTel = "090-1234-5678",
                    receptionStaff = "担当者サンプル",
                    remarks = "メモ"
                )

                val expected = "${apiPathPrefix}?tatemonoCd=${input.buildingCode.value}" +
                        "&parkingCd=${input.parkingLotCode!!.value}" +
                        "&status=0" +
                        "&uketukeDate=${input.receptionDate.yyyyMMdd()}" +
                        "&reserveName=${input.reserverName!!.urlEncode()}" +
                        "&tel=${input.reserverTel!!.value}" +
                        "&tantoName=${input.receptionStaff!!.urlEncode()}" +
                        "&bikou=${input.remarks!!.value.urlEncode()}" +
                        "&id=${input.eBoardParkingReservationId.value}"

                mockServer.expect(requestTo(expected))
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(
                        header(
                            HttpHeaders.CONTENT_TYPE,
                            MediaType.APPLICATION_FORM_URLENCODED_VALUE
                        )
                    )
                    .andExpect(method(HttpMethod.POST))
                    .andRespond(successBody)

                repository.reserveParking(stubRegisterParkingReservation(), input)
                mockServer.verify()
            }

            @Test
            @DisplayName("welcomeParkの一日利用の場合、status=4でリクエストパラメータが適切に生成されること")
            fun case3() {
                val input = stubParkingReservationInfo(
                    reservationType = ParkingReservation.Type.ONE_DAY,
                    requestSource = ParkingReservation.RequestSource.WELCOME_PARK,
                )
                val registerParkingReservation = stubRegisterParkingReservation(
                    requestSource = ParkingReservation.RequestSource.WELCOME_PARK,
                    reservationType = ParkingReservation.Type.ONE_DAY
                )

                val expected = "${apiPathPrefix}?tatemonoCd=${input.buildingCode.value}" +
                        "&parkingCd=${input.parkingLotCode!!.value}" +
                        "&status=4" +
                        "&uketukeDate=${input.receptionDate.yyyyMMdd()}" +
                        "&id=${input.eBoardParkingReservationId.value}"

                mockServer.expect(requestTo(expected))
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(
                        header(
                            HttpHeaders.CONTENT_TYPE,
                            MediaType.APPLICATION_FORM_URLENCODED_VALUE
                        )
                    )
                    .andExpect(method(HttpMethod.POST))
                    .andRespond(successBody)

                repository.reserveParking(registerParkingReservation, input)
                mockServer.verify()
            }

            @Test
            @DisplayName("「HTTPステータス200, updateResult=NG」レスポンスを受信した場合、Exceptionがスローされること")
            fun case4() {
                mockServer.expect(requestTo(containsString(apiPathPrefix)))
                    .andRespond(failureBody)

                val input = stubParkingReservationInfo()
                val result = assertThrows<ExternalApiServerException> {
                    repository.reserveParking(stubRegisterParkingReservation(), input)
                }
                assertEquals(ErrorType.EBOARD_API_ERROR, result.type)
            }
        }

        @Nested
        @DisplayName("更新の場合・リクエストパラメータが適切に生成されること")
        inner class Scenario6x2 {

            @Test
            @DisplayName("nullableの項目が設定されていない場合、status=1でリクエストパラメータが適切に生成されること")
            fun case1() {
                val input = stubParkingReservationInfo(
                    parkingLotCode = null
                )

                val expected = "${apiPathPrefix}?tatemonoCd=${input.buildingCode.value}" +
                        "&status=1" +
                        "&uketukeDate=${input.receptionDate.yyyyMMdd()}" +
                        "&id=${input.eBoardParkingReservationId.value}"

                mockServer.expect(requestTo(expected))
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(
                        header(
                            HttpHeaders.CONTENT_TYPE,
                            MediaType.APPLICATION_FORM_URLENCODED_VALUE
                        )
                    )
                    .andExpect(method(HttpMethod.POST))
                    .andRespond(successBody)

                repository.reserveParking(stubUpdateParkingReservation(), input)
                mockServer.verify()
            }

            @Test
            @DisplayName("nullableの項目が全て設定されている場合、status=1でリクエストパラメータが適切に生成されること")
            fun case2() {
                val input = stubParkingReservationInfo(
                    reserverName = "受付者サンプル",
                    reserverTel = "090-1234-5678",
                    receptionStaff = "担当者サンプル",
                    remarks = "メモ"
                )

                val expected = "${apiPathPrefix}?tatemonoCd=${input.buildingCode.value}" +
                        "&parkingCd=${input.parkingLotCode!!.value}" +
                        "&status=1" +
                        "&uketukeDate=${input.receptionDate.yyyyMMdd()}" +
                        "&reserveName=${input.reserverName!!.urlEncode()}" +
                        "&tel=${input.reserverTel!!.value}" +
                        "&tantoName=${input.receptionStaff!!.urlEncode()}" +
                        "&bikou=${input.remarks!!.value.urlEncode()}" +
                        "&id=${input.eBoardParkingReservationId.value}"

                mockServer.expect(requestTo(expected))
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(
                        header(
                            HttpHeaders.CONTENT_TYPE,
                            MediaType.APPLICATION_FORM_URLENCODED_VALUE
                        )
                    )
                    .andExpect(method(HttpMethod.POST))
                    .andRespond(successBody)

                repository.reserveParking(stubUpdateParkingReservation(), input)
                mockServer.verify()
            }

            @Test
            @DisplayName("「HTTPステータス200, updateResult=NG」レスポンスを受信した場合、Exceptionがスローされること")
            fun case3() {
                mockServer.expect(requestTo(containsString(apiPathPrefix)))
                    .andRespond(failureBody)

                val input = stubParkingReservationInfo()
                val result = assertThrows<ExternalApiServerException> {
                    repository.reserveParking(stubUpdateParkingReservation(), input)
                }
                assertEquals(ErrorType.EBOARD_API_ERROR, result.type)
            }
        }

        @Nested
        @DisplayName("キャンセルの場合・リクエストパラメータが適切に生成されること")
        inner class Scenario6x3 {

            @Test
            @DisplayName("nullableの項目が設定されていない場合、status=3でリクエストパラメータが適切に生成されること")
            fun case1() {
                val input = stubParkingReservationInfo(
                    parkingLotCode = null
                )

                val expected = "${apiPathPrefix}?tatemonoCd=${input.buildingCode.value}" +
                        "&status=3" +
                        "&uketukeDate=${input.receptionDate.yyyyMMdd()}" +
                        "&id=${input.eBoardParkingReservationId.value}"

                mockServer.expect(requestTo(expected))
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(
                        header(
                            HttpHeaders.CONTENT_TYPE,
                            MediaType.APPLICATION_FORM_URLENCODED_VALUE
                        )
                    )
                    .andExpect(method(HttpMethod.POST))
                    .andRespond(successBody)

                repository.reserveParking(stubCancelParkingReservation(), input)
                mockServer.verify()
            }

            @Test
            @DisplayName("nullableの項目が全て設定されている場合、status=3でリクエストパラメータが適切に生成されること")
            fun case2() {
                val input = stubParkingReservationInfo(
                    reserverName = "受付者サンプル",
                    reserverTel = "090-1234-5678",
                    receptionStaff = "担当者サンプル",
                    remarks = "メモ"
                )

                val expected = "${apiPathPrefix}?tatemonoCd=${input.buildingCode.value}" +
                        "&parkingCd=${input.parkingLotCode!!.value}" +
                        "&status=3" +
                        "&uketukeDate=${input.receptionDate.yyyyMMdd()}" +
                        "&reserveName=${input.reserverName!!.urlEncode()}" +
                        "&tel=${input.reserverTel!!.value}" +
                        "&tantoName=${input.receptionStaff!!.urlEncode()}" +
                        "&bikou=${input.remarks!!.value.urlEncode()}" +
                        "&id=${input.eBoardParkingReservationId.value}"

                mockServer.expect(requestTo(expected))
                    .andExpect(header(HttpHeaders.AUTHORIZATION, "Bearer $TOKEN"))
                    .andExpect(
                        header(
                            HttpHeaders.CONTENT_TYPE,
                            MediaType.APPLICATION_FORM_URLENCODED_VALUE
                        )
                    )
                    .andExpect(method(HttpMethod.POST))
                    .andRespond(successBody)

                repository.reserveParking(stubCancelParkingReservation(), input)
                mockServer.verify()
            }

            @Test
            @DisplayName("「HTTPステータス200, updateResult=NG」レスポンスを受信した場合、Exceptionがスローされること")
            fun case3() {
                mockServer.expect(requestTo(containsString(apiPathPrefix)))
                    .andRespond(failureBody)

                val input = stubParkingReservationInfo()
                val result = assertThrows<ExternalApiServerException> {
                    repository.reserveParking(stubCancelParkingReservation(), input)
                }
                assertEquals(ErrorType.EBOARD_API_ERROR, result.type)
            }
        }
    }

    private fun String.urlEncode(): String {
        return java.net.URLEncoder.encode(this, "utf8")
    }
}
