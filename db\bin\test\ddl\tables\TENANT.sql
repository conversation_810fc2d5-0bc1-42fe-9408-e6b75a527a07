-- TABLE: TENANT(テナント)

CREATE TABLE TENANT(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1,0)                  
,    TENANT_CODE                                  varchar(9)        NOT NULL    
,    INTEGRATED_CLIENT_CODE                       varchar(10)                   
,    TENANT_NAME_KANJI                            varchar(42)                   
,    TENANT_NAME_KANJI_2                          varchar(28)                   
,    TENANT_NAME_KANA                             varchar(40)                   
,    TRANSFER_VERIFICATION_KANA1                  varchar(25)                   
,    TRANSFER_VERIFICATION_KANA2                  varchar(25)                   
,    HEADQUARTERS_NAME_KANJI                      varchar(22)                   
,    HEADQUARTERS_NAME_KANA                       varchar(10)                   
,    SEARCH_KANA                                  varchar(25)                   
,    PERSONAL_CORPORATE_DIVISION                  varchar(1)                    
,    CORPORATE_STATUS_DIVISION                    varchar(2)                    
,    CORPORATE_POSITION_DIVISION                  varchar(1)                    
,    REPRESENTATIVE_NAME_KANJI                    varchar(42)                   
,    REPRESENTATIVE_NAME_KANA                     varchar(10)                   
,    REPRESENTATIVE_SEARCH_KANA                   varchar(10)                   
,    TENANT_DEPARTMENT                            varchar(12)                   
,    TENANT_CONTACT_NAME                          varchar(12)                   
,    POSTAL_CODE                                  varchar(8)                    
,    PREFECTURE_CODE                              varchar(2)                    
,    CITY_CODE                                    varchar(2)                    
,    TOWN_CODE                                    varchar(6)                    
,    ADDRESS_DETAIL                               varchar(62)                   
,    BUILDING_NAME                                varchar(32)                   
,    PHONE_NUMBER                                 varchar(15)                   
,    FAX_NUMBER                                   varchar(15)                   
,    POSTAL_CODE_2                                varchar(8)                    
,    PREFECTURE_CODE_2                            varchar(2)                    
,    CITY_CODE_2                                  varchar(2)                    
,    TOWN_CODE_2                                  varchar(6)                    
,    ADDRESS_DETAIL_2                             varchar(62)                   
,    BUILDING_NAME_2                              varchar(32)                   
,    PHONE_NUMBER_2                               varchar(15)                   
,    FAX_NUMBER_2                                 varchar(15)                   
,    INDUSTRY_CODE                                varchar(4)                    
,    BIRTH_DATE                                   numeric(8,0)                  
,    WORKPLACE_NAME                               varchar(42)                   
,    WORKPLACE_PREFECTURE_CODE                    varchar(2)                    
,    WORKPLACE_CITY_CODE                          varchar(2)                    
,    WORKPLACE_TOWN_CODE                          varchar(6)                    
,    WORKPLACE_ADDRESS_DETAIL                     varchar(62)                   
,    WORKPLACE_BUILDING_NAME                      varchar(32)                   
,    WORKPLACE_INDUSTRY_CODE                      varchar(4)                    
,    WORKPLACE_PHONE_NUMBER                       varchar(15)                   
,    VIOLATION_HISTORY_SIGN                       numeric(1,0)                  
,    NON_MANAGED_PROPERTY_SIGN                    numeric(1,0)                  
,    INTERFACE_SIGN                               numeric(1,0)                  
,    CORPORATE_HOUSING_AGENCY                     numeric(1,0)                  
,    YEARS_OF_SERVICE                             numeric(3,1)                  
,    INDUSTRY_PERSONAL                            varchar(32)                   
,    PERSONAL_ANNUAL_INCOME                       numeric(9,0)                  
,    COHABITANT_ANNUAL_INCOME                     numeric(9,0)                  
,    GENDER                                       varchar(1)                    
,    CONSTRAINT PK_TENANT PRIMARY KEY (TENANT_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE TENANT IS 'テナント 既存システム物理名: EEA10P';
COMMENT ON COLUMN TENANT.CREATION_DATE IS '作成年月日 既存システム物理名: EEA01D 個人：1 、法人：2';
COMMENT ON COLUMN TENANT.CREATION_TIME IS '作成時刻 既存システム物理名: EEA02H';
COMMENT ON COLUMN TENANT.UPDATE_DATE IS '更新年月日 既存システム物理名: EEA03D';
COMMENT ON COLUMN TENANT.UPDATE_TIME IS '更新時刻 既存システム物理名: EEA04H';
COMMENT ON COLUMN TENANT.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EEA05N';
COMMENT ON COLUMN TENANT.UPDATER IS '更新者 既存システム物理名: EEA06C';
COMMENT ON COLUMN TENANT.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: EEAG9S';
COMMENT ON COLUMN TENANT.TENANT_CODE IS 'テナントコード 既存システム物理名: EEAANC';
COMMENT ON COLUMN TENANT.INTEGRATED_CLIENT_CODE IS '統合取引先コード 既存システム物理名: EEABVC';
COMMENT ON COLUMN TENANT.TENANT_NAME_KANJI IS 'テナント名(漢字) 既存システム物理名: EEA08M';
COMMENT ON COLUMN TENANT.TENANT_NAME_KANJI_2 IS 'テナント名(漢字) 既存システム物理名: EEA09M';
COMMENT ON COLUMN TENANT.TENANT_NAME_KANA IS 'テナント名(仮名) 既存システム物理名: EEA10M';
COMMENT ON COLUMN TENANT.TRANSFER_VERIFICATION_KANA1 IS '振込照合用(仮名1) 既存システム物理名: EEA11M';
COMMENT ON COLUMN TENANT.TRANSFER_VERIFICATION_KANA2 IS '振込照合用(仮名2) 既存システム物理名: EEA37M';
COMMENT ON COLUMN TENANT.HEADQUARTERS_NAME_KANJI IS '本支社名(漢字) 既存システム物理名: EEA12M';
COMMENT ON COLUMN TENANT.HEADQUARTERS_NAME_KANA IS '本支社名(仮名) 既存システム物理名: EEA13M';
COMMENT ON COLUMN TENANT.SEARCH_KANA IS '検索用仮名 既存システム物理名: EEA14M';
COMMENT ON COLUMN TENANT.PERSONAL_CORPORATE_DIVISION IS '個人法人区分 既存システム物理名: EEACCB';
COMMENT ON COLUMN TENANT.CORPORATE_STATUS_DIVISION IS '法人格区分 既存システム物理名: EEACDB';
COMMENT ON COLUMN TENANT.CORPORATE_POSITION_DIVISION IS '法人格前後区分 既存システム物理名: EEACEB';
COMMENT ON COLUMN TENANT.REPRESENTATIVE_NAME_KANJI IS '代表者名(漢字) 既存システム物理名: EEA15M';
COMMENT ON COLUMN TENANT.REPRESENTATIVE_NAME_KANA IS '代表者名(仮名) 既存システム物理名: EEA16M';
COMMENT ON COLUMN TENANT.REPRESENTATIVE_SEARCH_KANA IS '代表者検索用仮名 既存システム物理名: EEA17M';
COMMENT ON COLUMN TENANT.TENANT_DEPARTMENT IS 'テナント担当部署 既存システム物理名: EEA18M';
COMMENT ON COLUMN TENANT.TENANT_CONTACT_NAME IS 'テナント担当者名 既存システム物理名: EEA19M';
COMMENT ON COLUMN TENANT.POSTAL_CODE IS '郵便番号 既存システム物理名: EEA38N';
COMMENT ON COLUMN TENANT.PREFECTURE_CODE IS '都道府県コード 既存システム物理名: EEAAWC';
COMMENT ON COLUMN TENANT.CITY_CODE IS '市区郡コード 既存システム物理名: EEAAXC';
COMMENT ON COLUMN TENANT.TOWN_CODE IS '町村字通称コード 既存システム物理名: EEAAYC';
COMMENT ON COLUMN TENANT.ADDRESS_DETAIL IS '住所詳細 既存システム物理名: EEA20M';
COMMENT ON COLUMN TENANT.BUILDING_NAME IS 'ビル名称 既存システム物理名: EEA39M';
COMMENT ON COLUMN TENANT.PHONE_NUMBER IS '電話番号 既存システム物理名: EEA21N';
COMMENT ON COLUMN TENANT.FAX_NUMBER IS 'FAX番号 既存システム物理名: EEA22N';
COMMENT ON COLUMN TENANT.POSTAL_CODE_2 IS '郵便番号 既存システム物理名: EEA43N';
COMMENT ON COLUMN TENANT.PREFECTURE_CODE_2 IS '都道府県コード 既存システム物理名: EEA44C';
COMMENT ON COLUMN TENANT.CITY_CODE_2 IS '市区郡コード 既存システム物理名: EEA45C';
COMMENT ON COLUMN TENANT.TOWN_CODE_2 IS '町村字通称コード 既存システム物理名: EEA46C';
COMMENT ON COLUMN TENANT.ADDRESS_DETAIL_2 IS '住所詳細 既存システム物理名: EEA47M';
COMMENT ON COLUMN TENANT.BUILDING_NAME_2 IS 'ビル名称 既存システム物理名: EEA48M';
COMMENT ON COLUMN TENANT.PHONE_NUMBER_2 IS '電話番号 既存システム物理名: EEA49N';
COMMENT ON COLUMN TENANT.FAX_NUMBER_2 IS 'FAX番号 既存システム物理名: EEA50N';
COMMENT ON COLUMN TENANT.INDUSTRY_CODE IS '業種コード 既存システム物理名: EEAAVC';
COMMENT ON COLUMN TENANT.BIRTH_DATE IS '生年月日 既存システム物理名: EEA24D';
COMMENT ON COLUMN TENANT.WORKPLACE_NAME IS '勤務先名 既存システム物理名: EEA31M';
COMMENT ON COLUMN TENANT.WORKPLACE_PREFECTURE_CODE IS '都道府県コード 既存システム物理名: EEA32C';
COMMENT ON COLUMN TENANT.WORKPLACE_CITY_CODE IS '市区郡コード 既存システム物理名: EEA33C';
COMMENT ON COLUMN TENANT.WORKPLACE_TOWN_CODE IS '町村字通称コード 既存システム物理名: EEA34C';
COMMENT ON COLUMN TENANT.WORKPLACE_ADDRESS_DETAIL IS '住所詳細 既存システム物理名: EEA35X';
COMMENT ON COLUMN TENANT.WORKPLACE_BUILDING_NAME IS 'ビル名称 既存システム物理名: EEA23M';
COMMENT ON COLUMN TENANT.WORKPLACE_INDUSTRY_CODE IS '業種コード 既存システム物理名: EEA36C';
COMMENT ON COLUMN TENANT.WORKPLACE_PHONE_NUMBER IS '電話番号 既存システム物理名: EEA07N';
COMMENT ON COLUMN TENANT.VIOLATION_HISTORY_SIGN IS '違反経歴サイン 既存システム物理名: EEA41S';
COMMENT ON COLUMN TENANT.NON_MANAGED_PROPERTY_SIGN IS '他社物非管理サイン 既存システム物理名: EEA51S';
COMMENT ON COLUMN TENANT.INTERFACE_SIGN IS 'インターフェースサイン 既存システム物理名: EEA42S';
COMMENT ON COLUMN TENANT.CORPORATE_HOUSING_AGENCY IS '社宅代行 既存システム物理名: EEA43A';
COMMENT ON COLUMN TENANT.YEARS_OF_SERVICE IS '勤続年数 既存システム物理名: EEA52Q';
COMMENT ON COLUMN TENANT.INDUSTRY_PERSONAL IS '業種(個人) 既存システム物理名: EEA53X';
COMMENT ON COLUMN TENANT.PERSONAL_ANNUAL_INCOME IS '本人年収 既存システム物理名: EEA54A';
COMMENT ON COLUMN TENANT.COHABITANT_ANNUAL_INCOME IS '同居人年収 既存システム物理名: EEA55A';
COMMENT ON COLUMN TENANT.GENDER IS '性別 既存システム物理名: EEA56B';
