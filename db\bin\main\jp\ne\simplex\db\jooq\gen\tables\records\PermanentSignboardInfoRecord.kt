/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.PermanentSignboardInfoTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.PermanentSignboardInfoPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 常設看板情報 既存システム物理名: EPSK1P
 */
@Suppress("UNCHECKED_CAST")
open class PermanentSignboardInfoRecord private constructor() : UpdatableRecordImpl<PermanentSignboardInfoRecord>(PermanentSignboardInfoTable.PERMANENT_SIGNBOARD_INFO) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var buildingCd: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var imageFileName_1: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var imageRegistrationDate_1: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var imageRegistrationTime_1: Int?
        set(value): Unit = set(9, value)
        get(): Int? = get(9) as Int?

    open var installationDate_1: Int?
        set(value): Unit = set(10, value)
        get(): Int? = get(10) as Int?

    open var replacementDate_1: Int?
        set(value): Unit = set(11, value)
        get(): Int? = get(11) as Int?

    open var previousReplacementDate_1: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    open var removalDate_1: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var contractorCdE_1: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var workPerformed_1: Byte?
        set(value): Unit = set(15, value)
        get(): Byte? = get(15) as Byte?

    open var frame_1: Byte?
        set(value): Unit = set(16, value)
        get(): Byte? = get(16) as Byte?

    open var imageFileName_2: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var imageRegistrationDate_2: Int?
        set(value): Unit = set(18, value)
        get(): Int? = get(18) as Int?

    open var imageRegistrationTime_2: Int?
        set(value): Unit = set(19, value)
        get(): Int? = get(19) as Int?

    open var installationDate_2: Int?
        set(value): Unit = set(20, value)
        get(): Int? = get(20) as Int?

    open var replacementDate_2: Int?
        set(value): Unit = set(21, value)
        get(): Int? = get(21) as Int?

    open var previousReplacementDate_2: Int?
        set(value): Unit = set(22, value)
        get(): Int? = get(22) as Int?

    open var removalDate_2: Int?
        set(value): Unit = set(23, value)
        get(): Int? = get(23) as Int?

    open var contractorCdE_2: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var workPerformed_2: Byte?
        set(value): Unit = set(25, value)
        get(): Byte? = get(25) as Byte?

    open var frame_2: Byte?
        set(value): Unit = set(26, value)
        get(): Byte? = get(26) as Byte?

    open var imageFileName_3: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var imageRegistrationDate_3: Int?
        set(value): Unit = set(28, value)
        get(): Int? = get(28) as Int?

    open var imageRegistrationTime_3: Int?
        set(value): Unit = set(29, value)
        get(): Int? = get(29) as Int?

    open var installationDate_3: Int?
        set(value): Unit = set(30, value)
        get(): Int? = get(30) as Int?

    open var replacementDate_3: Int?
        set(value): Unit = set(31, value)
        get(): Int? = get(31) as Int?

    open var previousReplacementDate_3: Int?
        set(value): Unit = set(32, value)
        get(): Int? = get(32) as Int?

    open var removalDate_3: Int?
        set(value): Unit = set(33, value)
        get(): Int? = get(33) as Int?

    open var contractorCdE_3: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var workPerformed_3: Byte?
        set(value): Unit = set(35, value)
        get(): Byte? = get(35) as Byte?

    open var frame_3: Byte?
        set(value): Unit = set(36, value)
        get(): Byte? = get(36) as Byte?

    open var imageFileName_4: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var imageRegistrationDate_4: Int?
        set(value): Unit = set(38, value)
        get(): Int? = get(38) as Int?

    open var imageRegistrationTime_4: Int?
        set(value): Unit = set(39, value)
        get(): Int? = get(39) as Int?

    open var installationDate_4: Int?
        set(value): Unit = set(40, value)
        get(): Int? = get(40) as Int?

    open var replacementDate_4: Int?
        set(value): Unit = set(41, value)
        get(): Int? = get(41) as Int?

    open var previousReplacementDate_4: Int?
        set(value): Unit = set(42, value)
        get(): Int? = get(42) as Int?

    open var removalDate_4: Int?
        set(value): Unit = set(43, value)
        get(): Int? = get(43) as Int?

    open var contractorCdE_4: String?
        set(value): Unit = set(44, value)
        get(): String? = get(44) as String?

    open var workPerformed_4: Byte?
        set(value): Unit = set(45, value)
        get(): Byte? = get(45) as Byte?

    open var frame_4: Byte?
        set(value): Unit = set(46, value)
        get(): Byte? = get(46) as Byte?

    open var imageFileName_5: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var imageRegistrationDate_5: Int?
        set(value): Unit = set(48, value)
        get(): Int? = get(48) as Int?

    open var imageRegistrationTime_5: Int?
        set(value): Unit = set(49, value)
        get(): Int? = get(49) as Int?

    open var installationDate_5: Int?
        set(value): Unit = set(50, value)
        get(): Int? = get(50) as Int?

    open var replacementDate_5: Int?
        set(value): Unit = set(51, value)
        get(): Int? = get(51) as Int?

    open var previousReplacementDate_5: Int?
        set(value): Unit = set(52, value)
        get(): Int? = get(52) as Int?

    open var removalDate_5: Int?
        set(value): Unit = set(53, value)
        get(): Int? = get(53) as Int?

    open var contractorCdE_5: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var workPerformed_5: Byte?
        set(value): Unit = set(55, value)
        get(): Byte? = get(55) as Byte?

    open var frame_5: Byte?
        set(value): Unit = set(56, value)
        get(): Byte? = get(56) as Byte?

    open var imageFileName_6: String?
        set(value): Unit = set(57, value)
        get(): String? = get(57) as String?

    open var imageRegistrationDate_6: Int?
        set(value): Unit = set(58, value)
        get(): Int? = get(58) as Int?

    open var imageRegistrationTime_6: Int?
        set(value): Unit = set(59, value)
        get(): Int? = get(59) as Int?

    open var installationDate_6: Int?
        set(value): Unit = set(60, value)
        get(): Int? = get(60) as Int?

    open var replacementDate_6: Int?
        set(value): Unit = set(61, value)
        get(): Int? = get(61) as Int?

    open var previousReplacementDate_6: Int?
        set(value): Unit = set(62, value)
        get(): Int? = get(62) as Int?

    open var removalDate_6: Int?
        set(value): Unit = set(63, value)
        get(): Int? = get(63) as Int?

    open var contractorCdE_6: String?
        set(value): Unit = set(64, value)
        get(): String? = get(64) as String?

    open var workPerformed_6: Byte?
        set(value): Unit = set(65, value)
        get(): Byte? = get(65) as Byte?

    open var frame_6: Byte?
        set(value): Unit = set(66, value)
        get(): Byte? = get(66) as Byte?

    open var imageFileName_7: String?
        set(value): Unit = set(67, value)
        get(): String? = get(67) as String?

    open var imageRegistrationDate_7: Int?
        set(value): Unit = set(68, value)
        get(): Int? = get(68) as Int?

    open var imageRegistrationTime_7: Int?
        set(value): Unit = set(69, value)
        get(): Int? = get(69) as Int?

    open var installationDate_7: Int?
        set(value): Unit = set(70, value)
        get(): Int? = get(70) as Int?

    open var replacementDate_7: Int?
        set(value): Unit = set(71, value)
        get(): Int? = get(71) as Int?

    open var previousReplacementDate_7: Int?
        set(value): Unit = set(72, value)
        get(): Int? = get(72) as Int?

    open var removalDate_7: Int?
        set(value): Unit = set(73, value)
        get(): Int? = get(73) as Int?

    open var contractorCdE_7: String?
        set(value): Unit = set(74, value)
        get(): String? = get(74) as String?

    open var workPerformed_7: Byte?
        set(value): Unit = set(75, value)
        get(): Byte? = get(75) as Byte?

    open var frame_7: Byte?
        set(value): Unit = set(76, value)
        get(): Byte? = get(76) as Byte?

    open var imageFileName_8: String?
        set(value): Unit = set(77, value)
        get(): String? = get(77) as String?

    open var imageRegistrationDate_8: Int?
        set(value): Unit = set(78, value)
        get(): Int? = get(78) as Int?

    open var imageRegistrationTime_8: Int?
        set(value): Unit = set(79, value)
        get(): Int? = get(79) as Int?

    open var installationDate_8: Int?
        set(value): Unit = set(80, value)
        get(): Int? = get(80) as Int?

    open var replacementDate_8: Int?
        set(value): Unit = set(81, value)
        get(): Int? = get(81) as Int?

    open var previousReplacementDate_8: Int?
        set(value): Unit = set(82, value)
        get(): Int? = get(82) as Int?

    open var removalDate_8: Int?
        set(value): Unit = set(83, value)
        get(): Int? = get(83) as Int?

    open var contractorCdE_8: String?
        set(value): Unit = set(84, value)
        get(): String? = get(84) as String?

    open var workPerformed_8: Byte?
        set(value): Unit = set(85, value)
        get(): Byte? = get(85) as Byte?

    open var frame_8: Byte?
        set(value): Unit = set(86, value)
        get(): Byte? = get(86) as Byte?

    open var imageFileName_9: String?
        set(value): Unit = set(87, value)
        get(): String? = get(87) as String?

    open var imageRegistrationDate_9: Int?
        set(value): Unit = set(88, value)
        get(): Int? = get(88) as Int?

    open var imageRegistrationTime_9: Int?
        set(value): Unit = set(89, value)
        get(): Int? = get(89) as Int?

    open var installationDate_9: Int?
        set(value): Unit = set(90, value)
        get(): Int? = get(90) as Int?

    open var replacementDate_9: Int?
        set(value): Unit = set(91, value)
        get(): Int? = get(91) as Int?

    open var previousReplacementDate_9: Int?
        set(value): Unit = set(92, value)
        get(): Int? = get(92) as Int?

    open var removalDate_9: Int?
        set(value): Unit = set(93, value)
        get(): Int? = get(93) as Int?

    open var contractorCdE_9: String?
        set(value): Unit = set(94, value)
        get(): String? = get(94) as String?

    open var workPerformed_9: Byte?
        set(value): Unit = set(95, value)
        get(): Byte? = get(95) as Byte?

    open var frame_9: Byte?
        set(value): Unit = set(96, value)
        get(): Byte? = get(96) as Byte?

    open var imageFileName_10: String?
        set(value): Unit = set(97, value)
        get(): String? = get(97) as String?

    open var imageRegistrationDate_10: Int?
        set(value): Unit = set(98, value)
        get(): Int? = get(98) as Int?

    open var imageRegistrationTime_10: Int?
        set(value): Unit = set(99, value)
        get(): Int? = get(99) as Int?

    open var installationDate_10: Int?
        set(value): Unit = set(100, value)
        get(): Int? = get(100) as Int?

    open var replacementDate_10: Int?
        set(value): Unit = set(101, value)
        get(): Int? = get(101) as Int?

    open var previousReplacementDate_10: Int?
        set(value): Unit = set(102, value)
        get(): Int? = get(102) as Int?

    open var removalDate_10: Int?
        set(value): Unit = set(103, value)
        get(): Int? = get(103) as Int?

    open var contractorCdE_10: String?
        set(value): Unit = set(104, value)
        get(): String? = get(104) as String?

    open var workPerformed_10: Byte?
        set(value): Unit = set(105, value)
        get(): Byte? = get(105) as Byte?

    open var frame_10: Byte?
        set(value): Unit = set(106, value)
        get(): Byte? = get(106) as Byte?

    open var nextReplacementDate_1: Int?
        set(value): Unit = set(107, value)
        get(): Int? = get(107) as Int?

    open var nextReplacementDate_2: Int?
        set(value): Unit = set(108, value)
        get(): Int? = get(108) as Int?

    open var nextReplacementDate_3: Int?
        set(value): Unit = set(109, value)
        get(): Int? = get(109) as Int?

    open var nextReplacementDate_4: Int?
        set(value): Unit = set(110, value)
        get(): Int? = get(110) as Int?

    open var nextReplacementDate_5: Int?
        set(value): Unit = set(111, value)
        get(): Int? = get(111) as Int?

    open var nextReplacementDate_6: Int?
        set(value): Unit = set(112, value)
        get(): Int? = get(112) as Int?

    open var nextReplacementDate_7: Int?
        set(value): Unit = set(113, value)
        get(): Int? = get(113) as Int?

    open var nextReplacementDate_8: Int?
        set(value): Unit = set(114, value)
        get(): Int? = get(114) as Int?

    open var nextReplacementDate_9: Int?
        set(value): Unit = set(115, value)
        get(): Int? = get(115) as Int?

    open var nextReplacementDate_10: Int?
        set(value): Unit = set(116, value)
        get(): Int? = get(116) as Int?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised PermanentSignboardInfoRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, buildingCd: String, imageFileName_1: String? = null, imageRegistrationDate_1: Int? = null, imageRegistrationTime_1: Int? = null, installationDate_1: Int? = null, replacementDate_1: Int? = null, previousReplacementDate_1: Int? = null, removalDate_1: Int? = null, contractorCdE_1: String? = null, workPerformed_1: Byte? = null, frame_1: Byte? = null, imageFileName_2: String? = null, imageRegistrationDate_2: Int? = null, imageRegistrationTime_2: Int? = null, installationDate_2: Int? = null, replacementDate_2: Int? = null, previousReplacementDate_2: Int? = null, removalDate_2: Int? = null, contractorCdE_2: String? = null, workPerformed_2: Byte? = null, frame_2: Byte? = null, imageFileName_3: String? = null, imageRegistrationDate_3: Int? = null, imageRegistrationTime_3: Int? = null, installationDate_3: Int? = null, replacementDate_3: Int? = null, previousReplacementDate_3: Int? = null, removalDate_3: Int? = null, contractorCdE_3: String? = null, workPerformed_3: Byte? = null, frame_3: Byte? = null, imageFileName_4: String? = null, imageRegistrationDate_4: Int? = null, imageRegistrationTime_4: Int? = null, installationDate_4: Int? = null, replacementDate_4: Int? = null, previousReplacementDate_4: Int? = null, removalDate_4: Int? = null, contractorCdE_4: String? = null, workPerformed_4: Byte? = null, frame_4: Byte? = null, imageFileName_5: String? = null, imageRegistrationDate_5: Int? = null, imageRegistrationTime_5: Int? = null, installationDate_5: Int? = null, replacementDate_5: Int? = null, previousReplacementDate_5: Int? = null, removalDate_5: Int? = null, contractorCdE_5: String? = null, workPerformed_5: Byte? = null, frame_5: Byte? = null, imageFileName_6: String? = null, imageRegistrationDate_6: Int? = null, imageRegistrationTime_6: Int? = null, installationDate_6: Int? = null, replacementDate_6: Int? = null, previousReplacementDate_6: Int? = null, removalDate_6: Int? = null, contractorCdE_6: String? = null, workPerformed_6: Byte? = null, frame_6: Byte? = null, imageFileName_7: String? = null, imageRegistrationDate_7: Int? = null, imageRegistrationTime_7: Int? = null, installationDate_7: Int? = null, replacementDate_7: Int? = null, previousReplacementDate_7: Int? = null, removalDate_7: Int? = null, contractorCdE_7: String? = null, workPerformed_7: Byte? = null, frame_7: Byte? = null, imageFileName_8: String? = null, imageRegistrationDate_8: Int? = null, imageRegistrationTime_8: Int? = null, installationDate_8: Int? = null, replacementDate_8: Int? = null, previousReplacementDate_8: Int? = null, removalDate_8: Int? = null, contractorCdE_8: String? = null, workPerformed_8: Byte? = null, frame_8: Byte? = null, imageFileName_9: String? = null, imageRegistrationDate_9: Int? = null, imageRegistrationTime_9: Int? = null, installationDate_9: Int? = null, replacementDate_9: Int? = null, previousReplacementDate_9: Int? = null, removalDate_9: Int? = null, contractorCdE_9: String? = null, workPerformed_9: Byte? = null, frame_9: Byte? = null, imageFileName_10: String? = null, imageRegistrationDate_10: Int? = null, imageRegistrationTime_10: Int? = null, installationDate_10: Int? = null, replacementDate_10: Int? = null, previousReplacementDate_10: Int? = null, removalDate_10: Int? = null, contractorCdE_10: String? = null, workPerformed_10: Byte? = null, frame_10: Byte? = null, nextReplacementDate_1: Int? = null, nextReplacementDate_2: Int? = null, nextReplacementDate_3: Int? = null, nextReplacementDate_4: Int? = null, nextReplacementDate_5: Int? = null, nextReplacementDate_6: Int? = null, nextReplacementDate_7: Int? = null, nextReplacementDate_8: Int? = null, nextReplacementDate_9: Int? = null, nextReplacementDate_10: Int? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.buildingCd = buildingCd
        this.imageFileName_1 = imageFileName_1
        this.imageRegistrationDate_1 = imageRegistrationDate_1
        this.imageRegistrationTime_1 = imageRegistrationTime_1
        this.installationDate_1 = installationDate_1
        this.replacementDate_1 = replacementDate_1
        this.previousReplacementDate_1 = previousReplacementDate_1
        this.removalDate_1 = removalDate_1
        this.contractorCdE_1 = contractorCdE_1
        this.workPerformed_1 = workPerformed_1
        this.frame_1 = frame_1
        this.imageFileName_2 = imageFileName_2
        this.imageRegistrationDate_2 = imageRegistrationDate_2
        this.imageRegistrationTime_2 = imageRegistrationTime_2
        this.installationDate_2 = installationDate_2
        this.replacementDate_2 = replacementDate_2
        this.previousReplacementDate_2 = previousReplacementDate_2
        this.removalDate_2 = removalDate_2
        this.contractorCdE_2 = contractorCdE_2
        this.workPerformed_2 = workPerformed_2
        this.frame_2 = frame_2
        this.imageFileName_3 = imageFileName_3
        this.imageRegistrationDate_3 = imageRegistrationDate_3
        this.imageRegistrationTime_3 = imageRegistrationTime_3
        this.installationDate_3 = installationDate_3
        this.replacementDate_3 = replacementDate_3
        this.previousReplacementDate_3 = previousReplacementDate_3
        this.removalDate_3 = removalDate_3
        this.contractorCdE_3 = contractorCdE_3
        this.workPerformed_3 = workPerformed_3
        this.frame_3 = frame_3
        this.imageFileName_4 = imageFileName_4
        this.imageRegistrationDate_4 = imageRegistrationDate_4
        this.imageRegistrationTime_4 = imageRegistrationTime_4
        this.installationDate_4 = installationDate_4
        this.replacementDate_4 = replacementDate_4
        this.previousReplacementDate_4 = previousReplacementDate_4
        this.removalDate_4 = removalDate_4
        this.contractorCdE_4 = contractorCdE_4
        this.workPerformed_4 = workPerformed_4
        this.frame_4 = frame_4
        this.imageFileName_5 = imageFileName_5
        this.imageRegistrationDate_5 = imageRegistrationDate_5
        this.imageRegistrationTime_5 = imageRegistrationTime_5
        this.installationDate_5 = installationDate_5
        this.replacementDate_5 = replacementDate_5
        this.previousReplacementDate_5 = previousReplacementDate_5
        this.removalDate_5 = removalDate_5
        this.contractorCdE_5 = contractorCdE_5
        this.workPerformed_5 = workPerformed_5
        this.frame_5 = frame_5
        this.imageFileName_6 = imageFileName_6
        this.imageRegistrationDate_6 = imageRegistrationDate_6
        this.imageRegistrationTime_6 = imageRegistrationTime_6
        this.installationDate_6 = installationDate_6
        this.replacementDate_6 = replacementDate_6
        this.previousReplacementDate_6 = previousReplacementDate_6
        this.removalDate_6 = removalDate_6
        this.contractorCdE_6 = contractorCdE_6
        this.workPerformed_6 = workPerformed_6
        this.frame_6 = frame_6
        this.imageFileName_7 = imageFileName_7
        this.imageRegistrationDate_7 = imageRegistrationDate_7
        this.imageRegistrationTime_7 = imageRegistrationTime_7
        this.installationDate_7 = installationDate_7
        this.replacementDate_7 = replacementDate_7
        this.previousReplacementDate_7 = previousReplacementDate_7
        this.removalDate_7 = removalDate_7
        this.contractorCdE_7 = contractorCdE_7
        this.workPerformed_7 = workPerformed_7
        this.frame_7 = frame_7
        this.imageFileName_8 = imageFileName_8
        this.imageRegistrationDate_8 = imageRegistrationDate_8
        this.imageRegistrationTime_8 = imageRegistrationTime_8
        this.installationDate_8 = installationDate_8
        this.replacementDate_8 = replacementDate_8
        this.previousReplacementDate_8 = previousReplacementDate_8
        this.removalDate_8 = removalDate_8
        this.contractorCdE_8 = contractorCdE_8
        this.workPerformed_8 = workPerformed_8
        this.frame_8 = frame_8
        this.imageFileName_9 = imageFileName_9
        this.imageRegistrationDate_9 = imageRegistrationDate_9
        this.imageRegistrationTime_9 = imageRegistrationTime_9
        this.installationDate_9 = installationDate_9
        this.replacementDate_9 = replacementDate_9
        this.previousReplacementDate_9 = previousReplacementDate_9
        this.removalDate_9 = removalDate_9
        this.contractorCdE_9 = contractorCdE_9
        this.workPerformed_9 = workPerformed_9
        this.frame_9 = frame_9
        this.imageFileName_10 = imageFileName_10
        this.imageRegistrationDate_10 = imageRegistrationDate_10
        this.imageRegistrationTime_10 = imageRegistrationTime_10
        this.installationDate_10 = installationDate_10
        this.replacementDate_10 = replacementDate_10
        this.previousReplacementDate_10 = previousReplacementDate_10
        this.removalDate_10 = removalDate_10
        this.contractorCdE_10 = contractorCdE_10
        this.workPerformed_10 = workPerformed_10
        this.frame_10 = frame_10
        this.nextReplacementDate_1 = nextReplacementDate_1
        this.nextReplacementDate_2 = nextReplacementDate_2
        this.nextReplacementDate_3 = nextReplacementDate_3
        this.nextReplacementDate_4 = nextReplacementDate_4
        this.nextReplacementDate_5 = nextReplacementDate_5
        this.nextReplacementDate_6 = nextReplacementDate_6
        this.nextReplacementDate_7 = nextReplacementDate_7
        this.nextReplacementDate_8 = nextReplacementDate_8
        this.nextReplacementDate_9 = nextReplacementDate_9
        this.nextReplacementDate_10 = nextReplacementDate_10
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised PermanentSignboardInfoRecord
     */
    constructor(value: PermanentSignboardInfoPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.buildingCd = value.buildingCd
            this.imageFileName_1 = value.imageFileName_1
            this.imageRegistrationDate_1 = value.imageRegistrationDate_1
            this.imageRegistrationTime_1 = value.imageRegistrationTime_1
            this.installationDate_1 = value.installationDate_1
            this.replacementDate_1 = value.replacementDate_1
            this.previousReplacementDate_1 = value.previousReplacementDate_1
            this.removalDate_1 = value.removalDate_1
            this.contractorCdE_1 = value.contractorCdE_1
            this.workPerformed_1 = value.workPerformed_1
            this.frame_1 = value.frame_1
            this.imageFileName_2 = value.imageFileName_2
            this.imageRegistrationDate_2 = value.imageRegistrationDate_2
            this.imageRegistrationTime_2 = value.imageRegistrationTime_2
            this.installationDate_2 = value.installationDate_2
            this.replacementDate_2 = value.replacementDate_2
            this.previousReplacementDate_2 = value.previousReplacementDate_2
            this.removalDate_2 = value.removalDate_2
            this.contractorCdE_2 = value.contractorCdE_2
            this.workPerformed_2 = value.workPerformed_2
            this.frame_2 = value.frame_2
            this.imageFileName_3 = value.imageFileName_3
            this.imageRegistrationDate_3 = value.imageRegistrationDate_3
            this.imageRegistrationTime_3 = value.imageRegistrationTime_3
            this.installationDate_3 = value.installationDate_3
            this.replacementDate_3 = value.replacementDate_3
            this.previousReplacementDate_3 = value.previousReplacementDate_3
            this.removalDate_3 = value.removalDate_3
            this.contractorCdE_3 = value.contractorCdE_3
            this.workPerformed_3 = value.workPerformed_3
            this.frame_3 = value.frame_3
            this.imageFileName_4 = value.imageFileName_4
            this.imageRegistrationDate_4 = value.imageRegistrationDate_4
            this.imageRegistrationTime_4 = value.imageRegistrationTime_4
            this.installationDate_4 = value.installationDate_4
            this.replacementDate_4 = value.replacementDate_4
            this.previousReplacementDate_4 = value.previousReplacementDate_4
            this.removalDate_4 = value.removalDate_4
            this.contractorCdE_4 = value.contractorCdE_4
            this.workPerformed_4 = value.workPerformed_4
            this.frame_4 = value.frame_4
            this.imageFileName_5 = value.imageFileName_5
            this.imageRegistrationDate_5 = value.imageRegistrationDate_5
            this.imageRegistrationTime_5 = value.imageRegistrationTime_5
            this.installationDate_5 = value.installationDate_5
            this.replacementDate_5 = value.replacementDate_5
            this.previousReplacementDate_5 = value.previousReplacementDate_5
            this.removalDate_5 = value.removalDate_5
            this.contractorCdE_5 = value.contractorCdE_5
            this.workPerformed_5 = value.workPerformed_5
            this.frame_5 = value.frame_5
            this.imageFileName_6 = value.imageFileName_6
            this.imageRegistrationDate_6 = value.imageRegistrationDate_6
            this.imageRegistrationTime_6 = value.imageRegistrationTime_6
            this.installationDate_6 = value.installationDate_6
            this.replacementDate_6 = value.replacementDate_6
            this.previousReplacementDate_6 = value.previousReplacementDate_6
            this.removalDate_6 = value.removalDate_6
            this.contractorCdE_6 = value.contractorCdE_6
            this.workPerformed_6 = value.workPerformed_6
            this.frame_6 = value.frame_6
            this.imageFileName_7 = value.imageFileName_7
            this.imageRegistrationDate_7 = value.imageRegistrationDate_7
            this.imageRegistrationTime_7 = value.imageRegistrationTime_7
            this.installationDate_7 = value.installationDate_7
            this.replacementDate_7 = value.replacementDate_7
            this.previousReplacementDate_7 = value.previousReplacementDate_7
            this.removalDate_7 = value.removalDate_7
            this.contractorCdE_7 = value.contractorCdE_7
            this.workPerformed_7 = value.workPerformed_7
            this.frame_7 = value.frame_7
            this.imageFileName_8 = value.imageFileName_8
            this.imageRegistrationDate_8 = value.imageRegistrationDate_8
            this.imageRegistrationTime_8 = value.imageRegistrationTime_8
            this.installationDate_8 = value.installationDate_8
            this.replacementDate_8 = value.replacementDate_8
            this.previousReplacementDate_8 = value.previousReplacementDate_8
            this.removalDate_8 = value.removalDate_8
            this.contractorCdE_8 = value.contractorCdE_8
            this.workPerformed_8 = value.workPerformed_8
            this.frame_8 = value.frame_8
            this.imageFileName_9 = value.imageFileName_9
            this.imageRegistrationDate_9 = value.imageRegistrationDate_9
            this.imageRegistrationTime_9 = value.imageRegistrationTime_9
            this.installationDate_9 = value.installationDate_9
            this.replacementDate_9 = value.replacementDate_9
            this.previousReplacementDate_9 = value.previousReplacementDate_9
            this.removalDate_9 = value.removalDate_9
            this.contractorCdE_9 = value.contractorCdE_9
            this.workPerformed_9 = value.workPerformed_9
            this.frame_9 = value.frame_9
            this.imageFileName_10 = value.imageFileName_10
            this.imageRegistrationDate_10 = value.imageRegistrationDate_10
            this.imageRegistrationTime_10 = value.imageRegistrationTime_10
            this.installationDate_10 = value.installationDate_10
            this.replacementDate_10 = value.replacementDate_10
            this.previousReplacementDate_10 = value.previousReplacementDate_10
            this.removalDate_10 = value.removalDate_10
            this.contractorCdE_10 = value.contractorCdE_10
            this.workPerformed_10 = value.workPerformed_10
            this.frame_10 = value.frame_10
            this.nextReplacementDate_1 = value.nextReplacementDate_1
            this.nextReplacementDate_2 = value.nextReplacementDate_2
            this.nextReplacementDate_3 = value.nextReplacementDate_3
            this.nextReplacementDate_4 = value.nextReplacementDate_4
            this.nextReplacementDate_5 = value.nextReplacementDate_5
            this.nextReplacementDate_6 = value.nextReplacementDate_6
            this.nextReplacementDate_7 = value.nextReplacementDate_7
            this.nextReplacementDate_8 = value.nextReplacementDate_8
            this.nextReplacementDate_9 = value.nextReplacementDate_9
            this.nextReplacementDate_10 = value.nextReplacementDate_10
            resetChangedOnNotNull()
        }
    }
}
