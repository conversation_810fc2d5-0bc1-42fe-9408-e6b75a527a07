/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.Building_11DigitAddressTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.Building_11DigitAddressPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 建物11桁住所 既存システム物理名: EAD11P
 */
@Suppress("UNCHECKED_CAST")
open class Building_11DigitAddressRecord private constructor() : UpdatableRecordImpl<Building_11DigitAddressRecord>(Building_11DigitAddressTable.BUILDING_11_DIGIT_ADDRESS) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var updateProgramId: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var buildingCode: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var addressCd_11digit: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var addressDetail: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var addressCd_11digitBasic: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var addressDetailBasic: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised Building_11DigitAddressRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, updateProgramId: String? = null, buildingCode: String, addressCd_11digit: String? = null, addressDetail: String? = null, addressCd_11digitBasic: String? = null, addressDetailBasic: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.updateProgramId = updateProgramId
        this.buildingCode = buildingCode
        this.addressCd_11digit = addressCd_11digit
        this.addressDetail = addressDetail
        this.addressCd_11digitBasic = addressCd_11digitBasic
        this.addressDetailBasic = addressDetailBasic
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised Building_11DigitAddressRecord
     */
    constructor(value: Building_11DigitAddressPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.updateProgramId = value.updateProgramId
            this.buildingCode = value.buildingCode
            this.addressCd_11digit = value.addressCd_11digit
            this.addressDetail = value.addressDetail
            this.addressCd_11digitBasic = value.addressCd_11digitBasic
            this.addressDetailBasic = value.addressDetailBasic
            resetChangedOnNotNull()
        }
    }
}
