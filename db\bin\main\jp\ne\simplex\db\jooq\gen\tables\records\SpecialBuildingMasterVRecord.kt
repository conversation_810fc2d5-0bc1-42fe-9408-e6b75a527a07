/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.SpecialBuildingMasterVTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.SpecialBuildingMasterVPojo

import org.jooq.impl.TableRecordImpl


/**
 * This class is generated by jOOQ.
 */
@Suppress("UNCHECKED_CAST")
open class SpecialBuildingMasterVRecord private constructor() : TableRecordImpl<SpecialBuildingMasterVRecord>(SpecialBuildingMasterVTable.SPECIAL_BUILDING_MASTER_V) {

    open var buildingCd: String?
        set(value): Unit = set(0, value)
        get(): String? = get(0) as String?

    open var identificationCategory: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    /**
     * Create a detached, initialised SpecialBuildingMasterVRecord
     */
    constructor(buildingCd: String? = null, identificationCategory: String? = null): this() {
        this.buildingCd = buildingCd
        this.identificationCategory = identificationCategory
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised SpecialBuildingMasterVRecord
     */
    constructor(value: SpecialBuildingMasterVPojo?): this() {
        if (value != null) {
            this.buildingCd = value.buildingCd
            this.identificationCategory = value.identificationCategory
            resetChangedOnNotNull()
        }
    }
}
