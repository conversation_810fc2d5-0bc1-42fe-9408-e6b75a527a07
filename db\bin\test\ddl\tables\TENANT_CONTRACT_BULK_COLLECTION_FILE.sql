-- TABLE: TENANT_CONTRACT_BULK_COLLECTION_FILE(テナント契約一括残集ファイル)

CREATE TABLE TENANT_CONTRACT_BULK_COLLECTION_FILE(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    TENANT_CONTRACT_NUMBER                       varchar(8)                    
,    PARKING_TENANT_CONTRACT                      varchar(8)                    
,    DELETION_DATE                                numeric(8,0)                  
,    REMAINING_COLLECTION_APPROVAL_DATE           numeric(8,0)                  
,    CANCELLATION_APPROVAL_DATE                   numeric(8,0)                  
,    MOVE_IN_APPLICATION_FEE                      numeric(9,0)                  
,    BROKERAGE_FEE                                numeric(9,0)                  
,    BROKERAGE_FEE_TAX                            numeric(9,0)                  
,    AGENT_FEE                                    numeric(9,0)                  
,    AGENT_FEE_TAX                                numeric(9,0)                  
,    ADVERTISING_FEE                              numeric(9,0)                  
,    POST_MOVE_ATTACHMENT_DIVISION                numeric(1,0)                  
,    AGENCY_AUTO_SUM_DIVISION                     numeric(1,0)                  
,    TRANSMISSION_DIVISION                        numeric(1,0)                  
,    TRANSMISSION_DATE                            numeric(8,0)                  
,    TRANSMISSION_TIME                            numeric(6,0)                  
,    PARENT_TENANT_BUILDING_CD                    varchar(9)                    
,    PARENT_TENANT_ROOM_CD                        varchar(5)                    
,    PARENT_TENANT_PARKING_CD                     varchar(3)                    
,    PARENT_TENANT_SURRENDER_PLANNED_DATE         numeric(8,0)                  
,    PK_BUILDING_CD                               varchar(9)                    
,    PK_PARKING_CD                                varchar(3)                    
,    PK_TENANT_SURRENDER_PLANNED_DATE             numeric(8,0)                  
,    PARKING_CONTRACT_FEE                         numeric(9,0)                  
,    PARKING_CONTRACT_FEE_TAX                     numeric(9,0)                  
,    AGENT_PARKING_CONTRACT_FEE                   numeric(9,0)                  
,    AGENT_PARKING_CONTRACT_FEE_TAX               numeric(9,0)                  
) TABLESPACE :TS_TBL;

COMMENT ON TABLE TENANT_CONTRACT_BULK_COLLECTION_FILE IS 'テナント契約一括残集ファイル 既存システム物理名: EDCTNP';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: EDC01D';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.CREATION_TIME IS '作成時刻 既存システム物理名: EDC02H';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: EDC03D';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.UPDATE_TIME IS '更新時刻 既存システム物理名: EDC04H';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EDC05N';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.UPDATER IS '更新者 既存システム物理名: EDC06C';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.TENANT_CONTRACT_NUMBER IS 'テナント契約番号 既存システム物理名: EDCAKN';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.PARKING_TENANT_CONTRACT IS '駐車場テナント契約 既存システム物理名: EDCPKN';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.DELETION_DATE IS '削除年月日 既存システム物理名: EDCDLD';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.REMAINING_COLLECTION_APPROVAL_DATE IS '残集承認日 既存システム物理名: EDCZND';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.CANCELLATION_APPROVAL_DATE IS 'キャンセル承認日 既存システム物理名: EDCCND';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.MOVE_IN_APPLICATION_FEE IS '入居申込金 既存システム物理名: EDCMSA';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.BROKERAGE_FEE IS '仲介手数料 既存システム物理名: EDCTSA';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.BROKERAGE_FEE_TAX IS '仲介手数料(税) 既存システム物理名: EDCTXA';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.AGENT_FEE IS '業者手数料 既存システム物理名: EDCGTA';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.AGENT_FEE_TAX IS '業者手数料(税) 既存システム物理名: EDCGXA';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.ADVERTISING_FEE IS '広告料 既存システム物理名: EDCADA';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.POST_MOVE_ATTACHMENT_DIVISION IS '入居後紐付区分 既存システム物理名: EDCGSS';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.AGENCY_AUTO_SUM_DIVISION IS '代行自動合算区分 既存システム物理名: EDCDGS';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.TRANSMISSION_DIVISION IS '送信区分 既存システム物理名: EDCSNS';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.TRANSMISSION_DATE IS '送信年月日 既存システム物理名: EDC07D';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.TRANSMISSION_TIME IS '送信時刻 既存システム物理名: EDC08H';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.PARENT_TENANT_BUILDING_CD IS '親テ契建物CD 既存システム物理名: EDCABC';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.PARENT_TENANT_ROOM_CD IS '親テ契部屋CD 既存システム物理名: EDCACC';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.PARENT_TENANT_PARKING_CD IS '親テ契駐車場CD 既存システム物理名: EDCBSC';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.PARENT_TENANT_SURRENDER_PLANNED_DATE IS '親テ契明渡予定日 既存システム物理名: EDCAKD';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.PK_BUILDING_CD IS 'PK建物CD 既存システム物理名: EDCPBC';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.PK_PARKING_CD IS 'PK駐車場CD 既存システム物理名: EDCPSC';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.PK_TENANT_SURRENDER_PLANNED_DATE IS 'PKテ契明渡予定日 既存システム物理名: EDCPKD';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.PARKING_CONTRACT_FEE IS '駐車場契約手数料 既存システム物理名: EDC09A';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.PARKING_CONTRACT_FEE_TAX IS '駐車場契約手数料税 既存システム物理名: EDC10A';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.AGENT_PARKING_CONTRACT_FEE IS '業者P契約手数料 既存システム物理名: EDC11A';
COMMENT ON COLUMN TENANT_CONTRACT_BULK_COLLECTION_FILE.AGENT_PARKING_CONTRACT_FEE_TAX IS '業者P契約手数料税 既存システム物理名: EDC12A';
