package jp.ne.simplex.authentication

import jp.ne.simplex.application.model.ExternalSystem
import jp.ne.simplex.application.repository.aws.SecretManagerRepository
import jp.ne.simplex.authentication.AuthInfo.ApiKey
import jp.ne.simplex.configuration.ApplicationConstants
import jp.ne.simplex.mock.MockSecretManagerClient
import jp.ne.simplex.stub.stubAuthConfig
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertNotEquals

class ApikeyAuthProcessorTest {
    private val authConfig = stubAuthConfig()
    private val secretManagerClient = MockSecretManagerClient.of(
        authConfig.apiKey,
        "{\"eboard\":\"$EBOARD_API_KEY\",\"dkPortal\":\"$DK_PORTAL_API_KEY\",\"kimaroomSign\":\"${KIMAROOM_SIGN_API_KEY}\"}"
    )
    private val secretManager = SecretManagerRepository(secretManagerClient)

    val apiKeyAuthProcessor = ApikeyAuthProcessor(secretManager, authConfig)

    @Nested
    @DisplayName("認証が成功すること")
    inner class Scenario1 {
        @Test
        @DisplayName("Secretに存在するキマルームサイン向けのAPIキーと一致、かつ一致したAPIキーのSecretにあるシステム名とサーバーで定義したシステム名が一致した場合、キマルームサインとして認証されること")
        fun case01() {
            val actual = apiKeyAuthProcessor.verify(
                KIMAROOM_SIGN_API_KEY,
                ApplicationConstants.EXTERNAL_SERVLET_PATH + "/parking-detail"
            )
            assertEquals(ApiKey(ExternalSystem.KIMAROOM_SIGN), actual)
            assertNotEquals(ApiKey(ExternalSystem.EBOARD), actual)
            assertNotEquals(ApiKey(ExternalSystem.DK_PORTAL), actual)
        }

        @Test
        @DisplayName("Secretに存在するいい物件ボード向けのAPIキーと一致、かつ一致したAPIキーのSecretにあるシステム名とサーバーで定義したシステム名が一致した場合、いい物件ボードとして認証されること")
        fun case02() {
            val actual = apiKeyAuthProcessor.verify(
                EBOARD_API_KEY,
                ApplicationConstants.EXTERNAL_SERVLET_PATH + "/login/parking-details"
            )
            assertNotEquals(ApiKey(ExternalSystem.KIMAROOM_SIGN), actual)
            assertEquals(ApiKey(ExternalSystem.EBOARD), actual)
            assertNotEquals(ApiKey(ExternalSystem.DK_PORTAL), actual)
        }

        @Test
        @DisplayName("Secretに存在するDK-PORTAL向けのAPIキーと一致、かつ一致したAPIキーのSecretにあるシステム名とサーバーで定義したシステム名が一致した場合、DK-PORTALとして認証されること")
        fun case03() {
            val actual = apiKeyAuthProcessor.verify(
                DK_PORTAL_API_KEY,
                ApplicationConstants.EXTERNAL_SERVLET_PATH + "/parking-detail"
            )
            assertNotEquals(ApiKey(ExternalSystem.KIMAROOM_SIGN), actual)
            assertNotEquals(ApiKey(ExternalSystem.EBOARD), actual)
            assertEquals(ApiKey(ExternalSystem.DK_PORTAL), actual)
        }
    }

    @Nested
    @DisplayName("認証が失敗すること")
    inner class Scenario2 {
        @Test
        @DisplayName("Secretに存在するAPIキーと一致しない場合、エラーが発生すること")
        fun case01() {
            try {
                apiKeyAuthProcessor.verify(
                    "invalid_api_key",
                    ApplicationConstants.EXTERNAL_SERVLET_PATH + "/parking-detail"
                )
            } catch (e: InvalidApiKeyException) {
                assertEquals("API key is invalid. Received API Key is invalid_api_key", e.message)
            }
        }

        @Test
        @DisplayName("Secretに存在する特定のAPIキーと一致、かつ一致したAPIキーのSecretにあるシステム名とサーバーで定義したシステム名が一致しない場合、エラーが発生すること")
        fun case02() {
            val secretManagerClient = MockSecretManagerClient.of(
                authConfig.apiKey,
                "{\"e\":\"$EBOARD_API_KEY\",\"dkPortal\":\"$DK_PORTAL_API_KEY\",\"kimaroomSign\":\"${KIMAROOM_SIGN_API_KEY}\"}"
            )
            val secretManager = SecretManagerRepository(secretManagerClient)

            val apikeyAuthProcessor = ApikeyAuthProcessor(secretManager, authConfig)
            try {
                apikeyAuthProcessor.verify(EBOARD_API_KEY, "/login/parking-details")
            } catch (e: ExternalSystemNameNotFoundException) {
                assertEquals("e in Secret is not found", e.message)
            }
        }

        @Test
        @DisplayName("Secretに存在する特定のAPIキーと一致するが、想定しない外接先からAPIコールされた場合、エラーが発生すること")
        fun case03() {
            try {
                apiKeyAuthProcessor.verify(
                    "invalid_api_key",
                    ApplicationConstants.EXTERNAL_SERVLET_PATH + "/check-parking-reservable-for-welcomepark"
                )
            } catch (e: InvalidApiKeyException) {
                assertEquals("API key is invalid. Received API Key is invalid_api_key", e.message)
            }
        }
    }

    companion object {
        private const val KIMAROOM_SIGN_API_KEY = "5046dbdfa52dd617d4e4a76c0bf591df"
        private const val EBOARD_API_KEY = "1ebdde3429aaabed21a47e5ee3016a53"
        private const val DK_PORTAL_API_KEY = "020942f9c4204c3197d7e0c2fb472e28"
    }
}
