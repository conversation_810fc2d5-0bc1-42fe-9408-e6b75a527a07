/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.CodeMappingTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.CodeMappingPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * This class is generated by jOOQ.
 */
@Suppress("UNCHECKED_CAST")
open class CodeMappingRecord private constructor() : UpdatableRecordImpl<CodeMappingRecord>(CodeMappingTable.CODE_MAPPING) {

    open var fcCode: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var eCode: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised CodeMappingRecord
     */
    constructor(fcCode: String, eCode: String): this() {
        this.fcCode = fcCode
        this.eCode = eCode
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised CodeMappingRecord
     */
    constructor(value: CodeMappingPojo?): this() {
        if (value != null) {
            this.fcCode = value.fcCode
            this.eCode = value.eCode
            resetChangedOnNotNull()
        }
    }
}
