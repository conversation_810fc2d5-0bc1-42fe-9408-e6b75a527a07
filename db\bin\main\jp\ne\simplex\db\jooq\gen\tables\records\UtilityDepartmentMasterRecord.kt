/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.UtilityDepartmentMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.UtilityDepartmentMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * ライフライン部署マスタ 既存システム物理名: YCBLBP
 */
@Suppress("UNCHECKED_CAST")
open class UtilityDepartmentMasterRecord private constructor() : TableRecordImpl<UtilityDepartmentMasterRecord>(UtilityDepartmentMasterTable.UTILITY_DEPARTMENT_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updaterId: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var deleteFlag: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var utilityCategory: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var utilityCompanyCd: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var utilityDepartmentCd: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var utilityDepartmentFullName: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var utilityDepartmentNameKana: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var phoneAreaCode: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var phoneLocalCode: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var phoneSubscriberNumber: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var detailCategory: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var comment: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    /**
     * Create a detached, initialised UtilityDepartmentMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updaterId: String? = null, deleteFlag: Byte? = null, utilityCategory: String? = null, utilityCompanyCd: String? = null, utilityDepartmentCd: String? = null, utilityDepartmentFullName: String? = null, utilityDepartmentNameKana: String? = null, phoneAreaCode: String? = null, phoneLocalCode: String? = null, phoneSubscriberNumber: String? = null, detailCategory: String? = null, comment: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updaterId = updaterId
        this.deleteFlag = deleteFlag
        this.utilityCategory = utilityCategory
        this.utilityCompanyCd = utilityCompanyCd
        this.utilityDepartmentCd = utilityDepartmentCd
        this.utilityDepartmentFullName = utilityDepartmentFullName
        this.utilityDepartmentNameKana = utilityDepartmentNameKana
        this.phoneAreaCode = phoneAreaCode
        this.phoneLocalCode = phoneLocalCode
        this.phoneSubscriberNumber = phoneSubscriberNumber
        this.detailCategory = detailCategory
        this.comment = comment
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised UtilityDepartmentMasterRecord
     */
    constructor(value: UtilityDepartmentMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updaterId = value.updaterId
            this.deleteFlag = value.deleteFlag
            this.utilityCategory = value.utilityCategory
            this.utilityCompanyCd = value.utilityCompanyCd
            this.utilityDepartmentCd = value.utilityDepartmentCd
            this.utilityDepartmentFullName = value.utilityDepartmentFullName
            this.utilityDepartmentNameKana = value.utilityDepartmentNameKana
            this.phoneAreaCode = value.phoneAreaCode
            this.phoneLocalCode = value.phoneLocalCode
            this.phoneSubscriberNumber = value.phoneSubscriberNumber
            this.detailCategory = value.detailCategory
            this.comment = value.comment
            resetChangedOnNotNull()
        }
    }
}
