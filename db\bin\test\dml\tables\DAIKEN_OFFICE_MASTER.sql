truncate table DAIKEN_OFFICE_MASTER;
insert into DAIKEN_OFFICE_MASTER (CREATION_DATE, CREATION_TIME, UPDATE_DATE, UPDATE_TIME, UPDATE_PROGRAM_ID, UPDATE_RESPONSIBLE_CODE, DAIKEN_SALES_OFFICE_CODE, POSTAL_CODE, ADDRESS, BUILDING_NAME, PHONE_NUMBER, FAX_NUMBER, DELETION_DATE, REPRESENT_KENTAKU_MNG_BRANCH_CODE, USAGE_START_DATE, SATELLITE_CATEGORY) values
 (0, 0, 20211027, 161702, 'HKX060R', '500284', 917, 700034, '北海道旭川市四条通８丁目１７０３番地５９', 'ＬＣ１号館　３階', '0166-27-7424', '0166-27-7422', 0, 737, 20090914, '0')
,(0, 0, 20200401, 180139, 'HKX060R', '095737', 639, 800801, '北海道帯広市東１条南１２丁目２－１', 'アーベイン重陽１階', '0155-99-0061', '0155-27-4090', 20200401, 137, 20170423, '0')
,(0, 0, 20181204, 182238, 'HKX060R', '094682', 630, 600008, '北海道札幌市中央区北８条西１８丁目３５－１００', 'エアリービル２Ｆ', '************', '************', 0, 45, 20170409, '0')
,(0, 0, 0, 0, null, null, 752, 30006, '札幌市白石区東札幌６条１丁目２－３０', '札幌三信物流ビル２Ｆ', '************', '************', 0, 126, 20090406, '0')
,(0, 0, 20191129, 133211, 'HKX060R', '093723', 822, 660032, '北海道千歳市北陽１－１６－１', '溝ビルⅦ２０００　１階', '0123-40-3062', '0123-40-3065', 0, 439, 20191201, '0')
,(0, 0, 20240414, 92825, 'HKX060R', '005760', 918, 420932, '北海道函館市湯川町３丁目４６－２', 'プランドル１Ｆ', '0138-59-5953', '0138-59-5952', 0, 439, 20080526, '1')
,(0, 0, 20191009, 93242, 'HKX060R', '093723', 772, 300862, '青森県青森市古川１丁目１０ー２３', '鎌田ビル　２Ｆ', '017-777-5317', '017-777-5320', 0, 74, 20080121, '0')
,(0, 0, 20191009, 93343, 'HKX060R', '093723', 825, 310084, '青森県八戸市十八日町４１－２', 'カーニープレイス八戸ビル８階', '0178-45-7438', '0178-45-7407', 0, 443, 20070611, '0')
,(0, 0, 20191009, 93424, 'HKX060R', '093723', 743, 160844, '秋田県能代市花園町６－２３', 'カインドビル１Ｆ', '0185-54-5992', '0185-54-5991', 0, 75, 20080701, '1')
,(0, 0, 20181204, 182645, 'HKX060R', '094682', 753, 100954, '秋田県秋田市山王沼田町１番１号', '山王沼田町事務所', '018-866-1919', '018-866-1921', 0, 75, 20130804, '0')
,(0, 0, 20191009, 93455, 'HKX060R', '093723', 841, 200127, '岩手県盛岡市前九年１丁目４－６３', 'ドルチエ１０２', '019-645-6208', '019-645-6210', 0, 168, 20100208, '0')
,(0, 0, 20240414, 93118, 'HKX060R', '005760', 883, 240063, '岩手県北上市九年橋３－１０－１', '東北大栄プラスチックビル２階', '0197-64-3674', '0197-64-3653', 0, 168, 20180715, '1')
,(0, 0, 20191009, 93607, 'HKX060R', '093723', 761, 9813133, '宮城県仙台市泉区泉中央１－１３－６', '赤間総業泉中央ビル１階', '022-218-7918', '022-218-7916', 0, 41, 20191005, '0')
,(0, 0, 20210802, 101353, 'FLE500R', '015722', 814, 9840015, '宮城県仙台市若林区荒井５丁目３－１', 'スプリングフィールド１Ｆ', '022-287-0411', '022-287-0020', 0, 40, 20201114, '0')
,(0, 0, 20191009, 93748, 'HKX060R', '093723', 922, 9811227, '宮城県名取市杜せきのした５丁目２０－３', 'Ｂ棟　Ｂ２－５', '022-383-1486', '022-383-1482', 0, 460, 20170219, '0')
,(0, 0, 20210802, 101532, 'FLE500R', '015722', 659, 9980842, '山形県酒田市亀ヶ崎３－８－５', null, '0234-25-6125', '0234-23-8377', 0, 143, 20200808, '0')
,(0, 0, 20191009, 94529, 'HKX060R', '093723', 633, 9900023, '山形県山形市松波１丁目１４－８', null, '023-622-1762', '023-622-1761', 0, 113, 20090914, '0')
,(0, 0, 20220623, 105156, 'HKX060R', '500284', 780, 9608068, '福島県福島市太田町２０－７', '博愛ビル３階', '024-535-7729', '024-535-7712', 0, 119, 20220623, '0')
,(0, 0, 20240414, 93221, 'HKX060R', '005760', 889, 9650007, '福島県会津若松市飯盛１－２－１', null, '0242-26-7203', '0242-26-7211', 0, 38, 20181001, '1')
,(0, 0, 20210802, 101136, 'FLE500R', '015722', 650, 9638871, '郡山市安積荒井１－６０', null, '024-937-3002', '024-947-5181', 0, 38, 20210412, '0')
,(0, 0, 20181204, 183021, 'HKX060R', '094682', 666, 9708036, '福島県いわき市平谷川瀬３丁目５－５', '双藤町ストウ事務所', '0246-21-9604', '0246-21-9601', 0, 114, 20171015, '0')
,(0, 0, 20150413, 172642, 'FLE500R', '093584', 830, 3292756, '栃木県那須塩原市西三島１－１３７', '村川ビル２Ｆ', '0287-38-2681', '0287-38-2685', 20150413, 127, 0, '0')
,(0, 0, 20200413, 94129, 'HKX060R', '092866', 805, 3210952, '栃木県宇都宮市泉ヶ丘６丁目１１－１６', null, '028-664-1834', '028-664-1838', 0, 33, 20200401, '1')
,(0, 0, 20210802, 101333, 'FLE500R', '015722', 651, 3210951, '栃木県宇都宮市越戸町８６－１', 'コリーヌ・エトワール　１階', '028-664-1834', '028-664-1838', 0, 33, 20200524, '0')
,(0, 0, 20210802, 101458, 'FLE500R', '015722', 847, 3260823, '栃木県足利市朝倉町５８３－２', 'アルドーレ２階', '0284-73-5009', '0284-73-7165', 0, 13, 20200524, '0')
,(0, 0, 20211001, 152442, 'HKX060R', '500284', 806, 3230029, '栃木県小山市城北６－２４－１３', null, '0285-20-1552', '0285-20-1550', 0, 42, 20211023, '0')
,(0, 0, 20191009, 100435, 'HKX060R', '093723', 667, 3170064, '茨城県日立市神峰町１丁目９－５', 'カーニープレイス日立２Ｆ', '0294-23-4598', '0294-23-4595', 0, 101, 20091026, '0')
,(0, 0, 20191009, 100510, 'HKX060R', '093723', 702, 3100803, '茨城県水戸市城南３－１０－１７', 'カーニープレイス水戸６Ｆ', '029-233-5121', '029-233-6121', 0, 30, 20170423, '0')
,(0, 0, 20191009, 101158, 'HKX060R', '093723', 685, 3050005, '茨城県つくば市天久保１－１３－１', '三洋ロジュマン１Ｆ', '029-856-0673', '029-856-0687', 0, 43, 20090725, '0')
,(0, 0, 20181204, 183719, 'HKX060R', '094682', 614, 3020034, '茨城県取手市戸頭５丁目２番１号', '関鉄戸頭ビル３階', '0297-78-1185', '0297-78-1179', 0, 130, 20161113, '0')
,(0, 0, 20181204, 183733, 'HKX060R', '094682', 832, 2770005, '千葉県柏市柏６－４－２６', 'ハクゼンビルディング７Ｆ', '04-7163-7813', '04-7163-7821', 0, 36, 20150315, '0')
,(0, 0, 20191009, 101257, 'HKX060R', '093723', 736, 2860033, '千葉県成田市花崎町９４５', 'Ｓｉｇｎ　Ｎａｒｉｔａ　３Ｆ', '0476-23-2486', '0476-23-2457', 0, 19, 20090126, '0')
,(0, 0, 20190328, 122458, 'HKX060R', '094682', 668, 2600025, '千葉県千葉市中央区問屋町１番１号', 'エレル千葉みなとビル２Ｆ', '043-243-8576', '043-243-8563', 0, 34, 20190401, '0')
,(0, 0, 20191009, 101343, 'HKX060R', '093723', 704, 2900054, '千葉県市原市五井中央東２丁目２２－４', '山崎第三ビル', '0436-22-4082', '0436-22-4075', 0, 22, 20090216, '0')
,(0, 0, 20220114, 151641, 'HKX060R', '500284', 892, 2760040, '千葉県八千代市緑が丘西１－１６－３', 'Ｙ’ｓ　Ｍｏｃａ　１階', '047-480-0512', '047-450-0608', 0, 766, 20220212, '0')
,(0, 0, 20181204, 182029, 'HKX060R', '095515', 873, 2702261, '千葉県松戸市常盤平５丁目１８－１', '五香第一生命ビルディング６階', '047-384-9203', '047-384-9206', 0, 698, 20170416, '0')
,(0, 0, 20210802, 93340, 'FLE500R', '015722', 729, 2730032, '千葉県船橋市葛飾町２丁目３４０番地', 'フロントンビル４Ｆ', '047-435-4884', '047-435-4895', 0, 29, 20111107, '0')
,(0, 0, 20200415, 112947, 'HKX060R', '092866', 660, 3730808, '群馬県太田市石原町５４０－５', null, '0276-48-9807', '0276-48-9803', 20200415, 13, 20200401, '1')
,(0, 0, 20181204, 183326, 'HKX060R', '094682', 720, 3710013, '群馬県前橋市西片貝町４－１６－１８', 'アミックビル５階', '027-223-0053', '027-223-0050', 0, 122, 20170416, '0')
,(0, 0, 20181204, 183341, 'HKX060R', '094682', 831, 3700073, '群馬県高崎市緑町３丁目２－２', null, '027-364-2735', '027-364-2731', 0, 35, 20170423, '0')
,(0, 0, 20181204, 183238, 'HKX060R', '094682', 829, 3600822, '埼玉県熊谷市宮本町９８－１', 'ペガソス熊谷', '048-521-8703', '048-521-8699', 0, 27, 20130804, '0')
,(0, 0, 20100510, 71304, 'HKX060R', '045065', 745, 3060234, '古河市上辺見字内手３４３８　１０２号室', null, '0280-33-2611', '0280-33-2612', 0, 102, 20080601, '1')
,(0, 0, 20221013, 112354, 'HKX060R', '500284', 758, 3490212, '埼玉県白岡市新白岡７－１１－１４', 'サニーレジデンスⅡ　１階', '0480-47-0907', '0480-31-8863', 0, 102, 20221015, '0')
,(0, 0, 20191009, 100721, 'HKX060R', '093723', 851, 3620015, '埼玉県上尾市緑丘３－５－２８', 'シンワ緑丘ビル２Ｆ', '048-773-8536', '048-773-8517', 0, 715, 20190204, '0')
,(0, 0, 20210802, 101322, 'FLE500R', '015722', 807, 3370051, '埼玉県さいたま市見沼区東大宮７丁目３７－１', 'Ａ＆ＭＦＡＣＩＬＩＴＩＥＳ２階', '048-681-1865', '048-681-1862', 0, 28, 20170423, '0')
,(0, 0, 20210802, 101343, 'FLE500R', '015722', 681, 3590046, '埼玉県所沢市北所沢町２２６４', 'メイトビル３Ｆ', '04-2994-5404', '04-2994-5403', 0, 39, 20170425, '0')
,(0, 0, 20240214, 93228, 'HKX060R', '500538', 923, 3410018, '埼玉県三郷市早稲田１－１－１', 'ＫＴＴ５ビル　４０２号室', '048-950-3601', '048-957-4822', 0, 820, 20240217, '0')
,(0, 0, 20181204, 182323, 'HKX060R', '095515', 728, 3430813, '埼玉県越谷市越ヶ谷２－２－２７', '越谷平和堂ビル２Ｆ', '048-967-0585', '048-967-0580', 0, 128, 20170312, '0')
,(0, 0, 20220902, 150640, 'FLE500R', '092866', 730, 3440061, '埼玉県春日部市粕壁６８３８－３', 'オーガスタＫＴⅢ　１Ｆ', '048-754-0380', '048-614-8536', 0, 37, 20151011, '0')
,(0, 0, 20220902, 150332, 'FLE500R', '092866', 824, 9500916, '新潟県新潟市中央区米山２丁目６－１', '藤巻第二ビル', '025-243-7486', '025-243-7485', 0, 20, 20120827, '0')
,(0, 0, 20191009, 101046, 'HKX060R', '093723', 610, 9518162, '新潟県新潟市中央区関屋本村町１丁目１１１－１', '関屋本村ビル３階', '025-231-8081', '025-231-8082', 0, 125, 20070611, '0')
,(0, 0, 20181204, 183620, 'HKX060R', '094682', 919, 9402121, '新潟県長岡市喜多町９５０－１', null, '0258-27-1072', '0258-27-1073', 0, 714, 20140914, '0')
,(0, 0, 20240115, 82414, 'HKX060R', '005760', 622, 9430824, '新潟県上越市北城町３－１－２１', '高助北城ビル２Ｆ', '025-521-6012', '025-522-7351', 0, 714, 20090928, '1')
,(0, 0, 20181204, 182309, 'HKX060R', '094682', 788, 3800935, '長野県長野市中御所１丁目１６－１３', '天馬ビル１Ｆ', '026-228-3474', '026-228-3469', 0, 78, 20130609, '0')
,(0, 0, 20191009, 92308, 'HKX060R', '093723', 926, 3860023, '長野県上田市中央西１丁目１５番３４号', '紺屋町ビル２階', '0268-26-0206', '0268-26-0303', 0, 410, 20090824, '0')
,(0, 0, 20181204, 182344, 'HKX060R', '094682', 674, 3900833, '長野県松本市双葉１０－２２', '双葉町ビルＢ　２Ｆ', '0263-26-2739', '0263-41-3633', 0, 79, 20150412, '0')
,(0, 0, 20100510, 71342, 'HKX060R', '045065', 747, 3920013, '諏訪市沖田町４－２５', '沖田Ｍビル１Ｆ', '0266-57-8085', '0266-57-8086', 0, 79, 20090615, '1')
,(0, 0, 20191009, 92350, 'HKX060R', '093723', 676, 9398015, '富山県富山市中川原３１－１', 'ウインドスクエア１０３号室', '076-493-2405', '076-493-2438', 0, 69, 20081110, '0')
,(0, 0, 20240414, 92950, 'HKX060R', '005760', 844, 9330871, '富山県高岡市駅南１丁目８－３４', 'アラヤビル４階', '0766-26-1634', '0766-26-1638', 0, 69, 20180918, '1')
,(0, 0, 20181204, 182435, 'HKX060R', '094682', 632, 9200051, '石川県金沢市二口町二５３－１', '金沢フィットビル５Ｆ', '076-260-7455', '076-260-7452', 0, 21, 20131215, '0')
,(0, 0, 20170714, 165753, 'HKX060R', '093584', 608, 9218163, '金沢市横川７丁目５０番－１', '８７ビル横川７階', '076-242-4390', '076-242-4396', 20170714, 121, 20070611, '0')
,(0, 0, 20200413, 83413, 'HKX060R', '095028', 921, 9230866, '石川県小松市白嶺町１丁目３４番地', 'Ａｓｈｉビル１０１号室', '0761-23-2451', '0761-23-2426', 0, 21, 20170710, '1')
,(0, 0, 20191009, 92536, 'HKX060R', '093723', 769, 9100854, '福井県福井市御幸４丁目２０－１８', 'オノダニビル御幸３Ｆ', '0776-26-0146', '0776-26-0196', 0, 94, 20090629, '0')
,(0, 0, 20220511, 121503, 'HKX060R', '500284', 924, 1200014, '東京都足立区西綾瀬３－１７－５', 'ＵＲＢＡＮ２０１号室', '03-5681-8423', '03-5681-8421', 0, 821, 20220514, '0')
,(0, 0, 20181204, 183057, 'HKX060R', '095515', 890, 1320024, '東京都江戸川区一之江８－４－５', 'アロービル２階', '03-5607-5104', '03-5607-5260', 0, 620, 20130421, '0')
,(0, 0, 20210802, 101158, 'FLE500R', '015722', 858, 1400004, '東京都品川区南品川２－２－１３', '南品川ＪＮビル２階', '03-5781-8002', '03-3474-2339', 0, 738, 20210306, '0')
,(0, 0, 20191009, 101702, 'HKX060R', '093723', 848, 3320012, '埼玉県川口市本町４－３－６', '本町ハイツ２０１号室', '048-223-1684', '048-223-1682', 0, 603, 20070709, '0')
,(0, 0, 20240411, 93429, 'HKX060R', '500538', 787, 3501133, '埼玉県川越市大字砂９１９－１', 'サンストリーム　１Ｆ', '049-238-4360', '049-238-4560', 0, 44, 20240413, '0')
,(0, 0, 20191009, 100834, 'HKX060R', '093723', 895, 3501302, '埼玉県狭山市東三ツ木２０１－１', '泉レジデンス１Ｆ', '04-2952-7198', '04-2952-7109', 0, 716, 20131006, '0')
,(0, 0, 20230118, 101344, 'HKX060R', '500284', 893, 1790085, '東京都練馬区早宮２－１７－３３', 'スカイスクレーパーⅠ　３階', '03-5945-1262', '03-3559-5225', 0, 487, 20230121, '0')
,(0, 0, 20191009, 111445, 'HKX060R', '093723', 894, 1850021, '東京都国分寺市南町２丁目１－３９', 'セラミカⅠ２Ｆ', '042-326-7290', '042-326-7292', 0, 490, 20091221, '0')
,(0, 0, 20211118, 122939, 'FLE500R', '092866', 854, 1900003, '東京都立川市栄町６－１', '立飛ビル７号館７階', '042-536-9383', '042-536-9391', 0, 607, 20211118, '0')
,(0, 0, 20191009, 112105, 'HKX060R', '093723', 898, 1980036, '東京都青梅市河辺町１０－１１－４', '橋本ビル４Ｆ', '0428-21-6815', '0428-21-6752', 0, 621, 20090622, '0')
,(0, 0, 20190624, 92315, 'HKX060R', '095737', 776, 2520231, '神奈川県相模原市中央区相模原６－１－２３', 'シャン・ド・フルール２階', '042-757-6475', '042-753-8605', 0, 176, 20190624, '0')
,(0, 0, 20220902, 150720, 'FLE500R', '092866', 705, 1910041, '東京都日野市南平６－１５－１６', '天城ビル　１Ｆ', '042-592-2922', '042-592-2923', 0, 32, 20181217, '0')
,(0, 0, 20181204, 183239, 'HKX060R', '095515', 707, 4000855, '山梨県甲府市中小河原１丁目１１番１２', '新仙ビル', '055-243-1684', '055-243-1683', 0, 77, 20110516, '0')
,(0, 0, 20230118, 101645, 'HKX060R', '500284', 884, 2130034, '神奈川県川崎市高津区上作延２－５－１１', 'アイリー・ライフ　１階', '044-861-8528', '044-861-8534', 0, 472, 20230123, '0')
,(0, 0, 20181204, 183354, 'HKX060R', '095515', 891, 2240041, '神奈川県横浜市都筑区仲町台１丁目２－２２', 'プラザ仲町台センター３階', '045-944-1742', '045-944-0694', 0, 25, 20160306, '0')
,(0, 0, 20181204, 183422, 'HKX060R', '095515', 901, 2440801, '神奈川県横浜市戸塚区品濃町５４９－２', '三宅ビル　５０３－１号', '045-825-3353', '045-825-3351', 0, 778, 20170827, '0')
,(0, 0, 20210802, 101305, 'FLE500R', '015722', 724, 2510041, '神奈川県藤沢市辻堂神台２－６－３６', null, '0466-30-2525', '0466-30-2526', 0, 24, 20200801, '0')
,(0, 0, 20230324, 105156, 'FLE500R', '300518', 872, 2420001, '神奈川県大和市下鶴間６５６－１', 'つきみ野サウスビル４階', '046-272-1327', '046-272-1304', 0, 717, 20220409, '0')
,(0, 0, 20181204, 183517, 'HKX060R', '095515', 706, 2430032, '神奈川県厚木市恩名１－６－５９', 'ＯＭビル１階－Ａ号室', '046-224-5317', '046-224-5318', 0, 23, 20130616, '0')
,(0, 0, 20230807, 90638, 'FLE500R', '302739', 631, 2540044, '神奈川県平塚市錦町１５－１７', 'ＫＳ管理ビル　２Ｆ', '0463-24-9058', '0463-24-9081', 0, 31, 20170219, '0')
,(0, 0, 20191009, 112424, 'HKX060R', '093723', 789, 2500874, '神奈川県小田原市鴨宮６２２－１', '剱持ビル２階', '0465-49-1694', '0465-49-1693', 0, 139, 20180827, '0')
,(0, 0, 20230405, 92553, 'HKX060R', '500284', 826, 4110824, '静岡県三島市長伏３６－１９', 'ブリランテ　１階', '055-984-1202', '055-984-1205', 0, 18, 20230408, '0')
,(0, 0, 20181204, 182431, 'HKX060R', '095515', 740, 4180026, '静岡県富士宮市西小泉町２９－３', null, '0544-26-1825', '0544-26-1823', 0, 141, 20100517, '0')
,(0, 0, 20191009, 112518, 'HKX060R', '093723', 679, 4228005, '静岡県静岡市駿河区池田８１１', 'シルフィード１Ｆ', '054-265-8885', '054-265-8886', 0, 16, 20120116, '0')
,(0, 0, 20240411, 93738, 'HKX060R', '500538', 855, 4260071, '静岡県藤枝市志太１－７－２２', 'ＡＦＣレジデンスⅩⅦ　１Ｆ', '054-645-8021', '054-643-6880', 0, 609, 20240413, '0')
,(0, 0, 20240414, 93337, 'HKX060R', '005760', 833, 4360022, '静岡県掛川市上張８６２－１', 'ＦＧＫ１Ｆ', '0537-24-8347', '0537-24-8334', 0, 17, 20080825, '1')
,(0, 0, 20240415, 105414, 'HKX060R', '301459', 906, 4380073, '静岡県磐田市二之宮東１８－４', 'ＥＡＳＴＦＬＡＴ’９４', '0538-37-2981', '0538-37-2982', 0, 17, 20090928, '1')
,(0, 0, 20240112, 161528, 'HKX060R', '500538', 678, 4300926, '静岡県浜松市中央区砂山町３５１－１', 'サニービル１階', '053-451-3079', '053-451-3071', 0, 17, 20240101, '0')
,(0, 0, 20231024, 93949, 'HKX060R', '018871', 636, 4340037, '浜松市浜北区沼１５０－１', '浜北中央ビル２階', '053-451-3079', '053-451-3071', 20231024, 134, 20090615, '0')
,(0, 0, 20231013, 91502, 'HKX060R', '500538', 808, 4418087, '愛知県豊橋市牟呂町字東里１１－１', 'イターナル富田新田　１階', '0532-29-0312', '0532-37-7561', 0, 15, 20231014, '0')
,(0, 0, 20191009, 113036, 'HKX060R', '093723', 907, 4420025, '愛知県豊川市東豊町５丁目１６番地', 'パオ１５１１階', '0533-85-2598', '0533-85-2726', 0, 613, 20090721, '0')
,(0, 0, 20191009, 113106, 'HKX060R', '093723', 708, 4440833, '愛知県岡崎市柱曙１丁目３－５', '発知ビル１階', '0564-54-3629', '0564-54-3597', 0, 6, 20070409, '0')
,(0, 0, 20231006, 95026, 'HKX060R', '500538', 781, 4710842, '愛知県豊田市土橋町３－１１４', 'ライトステーション　１階', '0565-25-2361', '0565-25-2362', 0, 10, 20231007, '0')
,(0, 0, 20181204, 184000, 'HKX060R', '095515', 835, 4720023, '愛知県知立市西町亀池３６－２', 'ＯＭビル１Ｆ', '0566-81-6069', '0566-81-6062', 0, 624, 20170423, '0')
,(0, 0, 20191009, 113522, 'HKX060R', '093723', 782, 4750925, '愛知県半田市宮本町３丁目２１７－２１', 'セントラルビル３階', '0569-23-0646', '0569-23-0593', 0, 447, 20070115, '0')
,(0, 0, 20191009, 113614, 'HKX060R', '093723', 815, 4740061, '愛知県大府市共和町２－８－４', null, '0562-48-8616', '0562-48-8612', 0, 3, 20170423, '0')
,(0, 0, 20220202, 154319, 'HKX060R', '500284', 627, 4540869, '愛知県名古屋市中川区荒子２－９８', 'パークサイドグリーン１階', '052-363-5702', '052-362-9281', 0, 1, 20220319, '0')
;
