-- TABLE: NEW_PROPERTY_NEAREST_STATION(新物件最寄駅)

CREATE TABLE NEW_PROPERTY_NEAREST_STATION(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATER                                      varchar(10)                   
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    NEAREST_STATION_BRANCH                       varchar(3)        NOT NULL    
,    LINE_CODE                                    varchar(4)                    
,    STATION_CODE                                 varchar(4)                    
,    BUS_STOP_NAME                                varchar(22)                   
,    RAILWAY_NEAREST_STATION_DISTANCE             numeric(5)                    
,    NEAREST_BUS_STOP_DISTANCE                    numeric(5)                    
,    BUS_RIDE_TIME                                numeric(2)                    
,    LATITUDE_STATION                             numeric(9)                    
,    LONGITUDE_STATION                            numeric(9)                    
,    LATITUDE_BUS_STOP                            numeric(9)                    
,    <PERSON>ON<PERSON>TUDE_BUS_STOP                           numeric(9)                    
,    MAP_SCALE                                    numeric(3)                    
,    DELETE_FLAG                                  varchar(1)                    
,    CONSTRAINT PK_NEW_PROPERTY_NEAREST_STATIO PRIMARY KEY (BUILDING_CODE, NEAREST_STATION_BRANCH)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE NEW_PROPERTY_NEAREST_STATION IS '新物件最寄駅 既存システム物理名: EMES1P';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.CREATION_DATE IS '作成年月日 既存システム物理名: EM001D';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.CREATION_TIME IS '作成時刻 既存システム物理名: EM002H';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.UPDATE_DATE IS '更新年月日 既存システム物理名: EM003D';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.UPDATE_TIME IS '更新時刻 既存システム物理名: EM004H';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.UPDATER IS '更新者 既存システム物理名: EM006C';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.BUILDING_CODE IS '建物CD 既存システム物理名: EM011C';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.NEAREST_STATION_BRANCH IS '最寄り駅枝番 既存システム物理名: EM012N';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.LINE_CODE IS '沿線CD 既存システム物理名: EM013C';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.STATION_CODE IS '駅CD 既存システム物理名: EM014C';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.BUS_STOP_NAME IS 'バス停名称 既存システム物理名: EM017M';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.RAILWAY_NEAREST_STATION_DISTANCE IS '鉄道最寄駅距離 既存システム物理名: EM018Q';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.NEAREST_BUS_STOP_DISTANCE IS '最寄バス距離 既存システム物理名: EM019Q';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.BUS_RIDE_TIME IS 'バス乗車時間 既存システム物理名: EM020H';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.LATITUDE_STATION IS '緯度(駅) 既存システム物理名: EM021L';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.LONGITUDE_STATION IS '経度(駅) 既存システム物理名: EM022L';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.LATITUDE_BUS_STOP IS '緯度(バス停) 既存システム物理名: EM023L';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.LONGITUDE_BUS_STOP IS '経度(バス停) 既存システム物理名: EM024L';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.MAP_SCALE IS 'MAP縮尺 既存システム物理名: EM025R';
COMMENT ON COLUMN NEW_PROPERTY_NEAREST_STATION.DELETE_FLAG IS '削除フラグ 既存システム物理名: EM026D';
