/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ParkingSpecialNotesFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingSpecialNotesFilePojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 駐車場特記事項ファイル 既存システム物理名: ERB30P
 */
@Suppress("UNCHECKED_CAST")
open class ParkingSpecialNotesFileRecord private constructor() : UpdatableRecordImpl<ParkingSpecialNotesFileRecord>(ParkingSpecialNotesFileTable.PARKING_SPECIAL_NOTES_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var updateProgramId: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(7, value)
        get(): Byte? = get(7) as Byte?

    open var buildingCode: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    open var specialNotes: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised ParkingSpecialNotesFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, updateProgramId: String? = null, logicalDeleteSign: Byte? = null, buildingCode: String, specialNotes: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.updateProgramId = updateProgramId
        this.logicalDeleteSign = logicalDeleteSign
        this.buildingCode = buildingCode
        this.specialNotes = specialNotes
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ParkingSpecialNotesFileRecord
     */
    constructor(value: ParkingSpecialNotesFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.updateProgramId = value.updateProgramId
            this.logicalDeleteSign = value.logicalDeleteSign
            this.buildingCode = value.buildingCode
            this.specialNotes = value.specialNotes
            resetChangedOnNotNull()
        }
    }
}
