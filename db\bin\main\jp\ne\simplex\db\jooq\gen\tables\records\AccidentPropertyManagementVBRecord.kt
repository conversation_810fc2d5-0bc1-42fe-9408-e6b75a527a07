/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.AccidentPropertyManagementVBTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.AccidentPropertyManagementVBPojo

import org.jooq.impl.TableRecordImpl


/**
 * This class is generated by jOOQ.
 */
@Suppress("UNCHECKED_CAST")
open class AccidentPropertyManagementVBRecord private constructor() : TableRecordImpl<AccidentPropertyManagementVBRecord>(AccidentPropertyManagementVBTable.ACCIDENT_PROPERTY_MANAGEMENT_V_B) {

    open var noticeFlag: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var targetCategory: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    open var buildingCode: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    /**
     * Create a detached, initialised AccidentPropertyManagementVBRecord
     */
    constructor(noticeFlag: Int? = null, targetCategory: String? = null, buildingCode: String? = null): this() {
        this.noticeFlag = noticeFlag
        this.targetCategory = targetCategory
        this.buildingCode = buildingCode
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised AccidentPropertyManagementVBRecord
     */
    constructor(value: AccidentPropertyManagementVBPojo?): this() {
        if (value != null) {
            this.noticeFlag = value.noticeFlag
            this.targetCategory = value.targetCategory
            this.buildingCode = value.buildingCode
            resetChangedOnNotNull()
        }
    }
}
