package jp.ne.simplex.exception

import java.text.MessageFormat

enum class ErrorMessage(val message: String) {
    // 共通エラーメッセージ
    UNEXPECTED_ERROR("予期せぬエラーが発生しました。"),
    STRING_MAX_LENGTH("{0}は{1}文字以内のみ有効です。"),
    STRING_MIN_LENGTH("{0}は{1}文字以上のみ有効です。"),
    STRING_LENGTH("{0}は{1}文字のみ有効です。"),
    INVALID_DATE_FORMAT("日付は{0}の形式で指定してください。"),
    INVALID_TELEPHONE_NUMBER("電話番号は数字とハイフンのみ有効です。"),
    INVALID_EMAIL_ADDRESS("メールアドレスの形式が無効です。"),
    MISSING_REQUIRED_FIELDS("項目[{0}]は、必須項目です。"),
    INVALID_REQUEST_FORMAT("必須項目が不足、もしくはリクエストのフォーマットが不正です。"),
    DATABASE_UPDATE_CONFLICT_OCCURRED("他のユーザーによって既に更新された可能性があります。"),
    INVALID_BUILDING_CODE("建物コードが不正です。"),
    PROPERTY_MAX_COUNT("項目[{0}]は、{1}件までしか指定できません。"),
    INVALID_DATE_RANGE_FORMAT("不正な期間が指定されています。開始:{0} 終了:{1}"),
    INVALID_CHAR("{0}に使用できない文字が含まれています。"),

    // 認証エラーメッセージ
    AUTHENTICATION_FAILURE("ログインに失敗しました。"),

    // 物件共通エラー
    PROPERTY_NOT_FOUND("物件情報が存在しません。建物CD={0}、部屋CD={1}"),

    // 駐車場情報更新API関連のエラーメッセージ
    PARKING_RESERVATION_CANCEL_INVALID_STATUS("対象の駐車場予約は完了済かキャンセル済のため取消できません。"),
    PARKING_RESERVATION_NOT_FOUND("対象の駐車場予約は存在しません。"),
    PARKING_RESERVATION_TYPE_UNCHANGEABLE("予約種別は変更できません。"),
    PARKING_RESERVATION_CHANGE_INVALID_STATUS("対象の駐車場予約は完了済かキャンセル済のため更新できません。"),
    PARKING_RESERVATION_DATE_CONFLICTED("他の予約と日時が重複しているため、指定の日時に変更はできません。"),
    PARKING_RESERVATION_INVALID_TYPE("予約種別が不正です。"),
    PARKING_RESERVATION_INVALID_STATUS("予約状態が不正です。"),
    PARKING_RESERVATION_START_DATE_REQUIRED("{0}は予約開始日を指定してください。"),
    PARKING_RESERVATION_END_DATE_NOT_ALLOWED("{0}は予約終了日を指定できません。"),
    PARKING_RESERVATION_START_AND_END_DATE_REQUIRED("{0}は予約開始日と終了日を両方指定してください。"),
    PARKING_RESERVATION_START_AND_END_DATE_NOT_ALLOWED("{0}は予約開始日及び予約終了日を指定できません。"),
    PARKING_RESERVATION_START_AND_END_DATE_PERIOD("{0}の予約期間は{1}日以内である必要があります。"),
    PARKING_RESERVATION_INVALID_START_AND_END_DATE("予約開始日は予約終了日以前の日時を指定してください。"),
    PARKING_RESERVATION_INVALID_START_DATE_PLANNED_MOVE_OUT("予約開始日は退去日より後の日時を指定してください。"),
    PARKING_IS_SIGNED("対象駐車場はすでに契約済のため、予約はできません。"),
    PARKING_RESERVATION_REPLACE_UNAVAILABLE("対象駐車場は契約及び申込がないため、場所変更予約はできません。"),
    PARKING_RESERVATION_ALREADY_RESERVED("対象駐車場はすでに予約があるので予約できません。"),
    PARKING_DOES_NOT_EXIST("指定した建物コードと駐車場コードに対応する駐車場が存在しません。"),
    PARKING_IS_NOT_AVAILABLE("対象駐車場は利用不可に設定されているため、予約できません。"),

    // データ不整合エラーメッセージ
    OFFICE_BRANCH_MAPPING_ERROR("営業所と支店コードのマッピングデータにないコードが検出されました。{0}"),

    // 物件関連エラーメッセージ
    INVALID_PROPERTY_SPECIFIED("存在しない物件が指定されています。"),

    // 物件メンテナンス関連エラーメッセージ
    PUBLISH_UNAVAILABLE_PROPERTY_NOT_ALLOWED("公開不可物件は公開指示を公開に変更できません。"),
    PUBLISH_UNAVAILABLE_STATUS_TEMPORARY_RESERVATION("仮押さえ中の物件は公開指示を公開に変更できません。"),
    PUBLISH_UNAVAILABLE_STATUS_APPLICATION_SUBMITTED("申込済みの物件は公開指示を公開に変更できません。"),
    DIRECTION_NOT_SET("方位が未設定の物件は公開指示を公開に変更できません。"),

    // 仮押さえ関連エラーメッセージ
    TEMPORARY_RESERVATION_INVALID_CANCEL_FLAG("仮押さえキャンセルフラグが不正です。「0: 仮押さえ登録, 1: 仮押さえキャンセル」 のいずれかを指定してください。"),
    TEMPORARY_RESERVATION_INVALID_OTHER_COMPANY_FLAG("他社フラグが不正です。「0: false, 1: true」のいずれかを指定してください。"),
    TEMPORARY_RESERVATION_INVALID_BRANCH_INFO("指定された仮押さえ担当支店情報が不正です。"),
    TEMPORARY_RESERVATION_NOT_ALLOWED("当該物件に対する仮押さえ操作は行えません。"),

    // 画像登録関連エラーメッセージ
    INVALID_ORDER_CODE("受注コードが不正です。"),
    INVALID_REGISTER_IMAGE_FILE("登録する画像は{0}KB以下のjpgファイルのみ有効です。"),

    // 先行公開関連
    EXCLUSIVE_PROPERTY_OFFICE_CODE_DUPLICATE("先行公開対象の物件は、既に別の営業所によって先行公開済みもしくは、公開予定のため、変更できません。"),
    EXCLUSIVE_PROPERTY_NOT_FOUND("指定された先行公開物件は存在しません。"),
    UPDATE_EXCLUSIVE_PROPERTY_NOT_ALLOWED("指定された先行公開物件は、既に公開済みのため修正できません。"),
    EXCLUSIVE_PROPERTY_IS_EXPIRED("指定された先行公開物件は、既に先行期間が終了しています。"),
    EXCLUSIVE_PROPERTY_NOT_IN_EXCLUSIVE_PERIOD("指定された先行公開物件は、現在先行公開期間外です。"),
    EXCLUSIVE_PROPERTY_NOT_TO_LEASING("指定された先行公開物件の先行先はリーシングではありません。"),
    REGISTER_EXCLUSIVE_PROPERTY_NOT_ALLOWED("指定された物件は、公開指示済みのため新規登録できません。"),
    REGISTER_EXCLUSIVE_UNAVAILABLE_STATUS_TEMPORARY_RESERVATION("仮押さえ中の物件は先行公開はできません。"),
    REGISTER_EXCLUSIVE_UNAVAILABLE_STATUS_APPLICATION_SUBMITTED("申込済みの物件は先行公開はできません。"),
    REGISTER_EXCLUSIVE_PROPERTY_DIRECTION_NOT_SET("方位が未設定の物件は先行公開はできません。"),
    REGISTER_EXCLUSIVE_PROPERTY_EMPTY_NOT("空き家ではないため、先行公開はできません。"),
    REGISTER_EXCLUSIVE_PROPERTY_MARKETING_BRANCH_NOT_SET("物件に営業所CDが設定されていないため、先行公開はできません。"),

    // 外部API関連のエラーメッセージ
    EXTERNAL_API_AUTHENTICATION_FAILURE("外部APIの認証に失敗しました。"),
    EBOARD_RECEIVED_ERROR_RESPONSE("いい物件ボードからエラーレスポンスを受信しました。{0}"),
    DK_PORTAL_RECEIVED_ERROR_RESPONSE("DKポータルからエラーレスポンスを受信しました。{0}"),
    DK_PORTAL_RECEIVED_UNEXPECTED_RESPONSE("DKポータルから不正なレスポンスを受信しました。"),
    ;

    inner class Detail(val errorMessage: ErrorMessage, vararg args: String) {
        val message: String = MessageFormat.format(<EMAIL>, *args)
    }

    fun format(vararg args: Any): Detail {
        val stringArgs = args.map {
            when (it) {
                is String -> it
                else -> it.toString()
            }
        }.toTypedArray()

        return Detail(this, *stringArgs)
    }
}

