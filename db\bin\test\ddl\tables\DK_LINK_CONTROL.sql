-- TABLE: D<PERSON>_LINK_CONTROL(<PERSON><PERSON><PERSON>ンク制御)

CREATE TABLE DK_LINK_CONTROL(
     <PERSON>E<PERSON>                                          varchar(255)      NOT NULL    
,    VALUE                                        varchar(255)                  
,    COMMENT                                      varchar(255)                  
,    CONSTRAINT PK_DK_LINK_CONTROL PRIMARY KEY (KEY)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE DK_LINK_CONTROL IS 'DKリンク制御 既存システム物理名: -';
COMMENT ON COLUMN DK_LINK_CONTROL.KEY IS '識別子 既存システム物理名: -';
COMMENT ON COLUMN DK_LINK_CONTROL.VALUE IS '値 既存システム物理名: -';
COMMENT ON COLUMN DK_LINK_CONTROL.COMMENT IS 'コメント 既存システム物理名: -';
