package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Company
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.repository.db.extension.EmployeeMasterEx.Companion.getEmployee
import jp.ne.simplex.db.jooq.gen.tables.pojos.EmployeeMasterPojo
import jp.ne.simplex.db.jooq.gen.tables.references.EMPLOYEE_MASTER
import jp.ne.simplex.db.jooq.gen.tables.references.HR_CATEGORY_TABLE_B
import org.jooq.DSLContext
import org.jooq.impl.DSL.left
import org.springframework.stereotype.Repository

@Repository
class EmployeeRepository(
    private val context: DSLContext,
    private val branchRepository: BranchRepositoryInterface,
    private val officeBranchMappingRepository: OfficeBranchMappingRepositoryInterface,
) : EmployeeRepositoryInterface {

    override fun findBy(employeeCode: Employee.Code?): Employee? {
        return context.select().from(EMPLOYEE_MASTER)
            .where(EMPLOYEE_MASTER.EMPLOYEE_NUMBER.eq(employeeCode?.value))
            .fetchOneInto(EmployeeMasterPojo::class.java)?.getEmployee()
    }

    override fun findBy(branch: Branch): List<Employee> {
        val relatedBranchCode = runCatching {
            when (branch.company) {
                // 本社の場合:001の場合はofficeCode:016 = leasing_store_tableのbranch_cd:016 -> leasing_store_cdの327,328 -> kt_all_branch160のbranch_code上3桁:327
                is Company.DaitouKentaku -> branchRepository.getBranchRelatedToLeasing(branch.code)
                is Company.DaitouKentakuPartners -> officeBranchMappingRepository.get(branch.code).value
                else -> branch.code.getPrefix()
            }
        }.getOrElse { branch.code.getPrefix() }

        // EmployeeMasterテーブルから指定した条件で絞りコミを行うクエリを
        val filteredEmployeeMaster = context.select()
            .from(EMPLOYEE_MASTER)
            .where(
                left(EMPLOYEE_MASTER.AFFILIATION_CODE, 3).`in`(
                    listOf(branch.code.getPrefix(), relatedBranchCode)
                )
            )
            .and(EMPLOYEE_MASTER.RESIGNATION_DATE.eq(0))
            .and(
                EMPLOYEE_MASTER.JOB_TYPE_CODE.`in`(
                    listOf(
                        "050", "170", "190", "054", "052", "053", "056", "173", "174",
                        "948", "950", "956", "957",
                    )
                ).or(
                    left(EMPLOYEE_MASTER.AFFILIATION_CODE, 3).`in`(
                        listOf("579", "582")
                    )
                )
            )
            .and(EMPLOYEE_MASTER.POSITION_CODE.lessOrEqual("80"))

        return context.select()
            .from(filteredEmployeeMaster)
            .innerJoin(HR_CATEGORY_TABLE_B)
            .on(HR_CATEGORY_TABLE_B.TYPE_CATEGORY.eq("13"))
            .and(HR_CATEGORY_TABLE_B.CODE.eq(filteredEmployeeMaster.field(EMPLOYEE_MASTER.POSITION_CODE)))
            .orderBy(
                filteredEmployeeMaster.field(EMPLOYEE_MASTER.AFFILIATION_CODE)?.asc(),
                HR_CATEGORY_TABLE_B.CATEGORY_1.asc(),
                filteredEmployeeMaster.field(EMPLOYEE_MASTER.POSITION_CODE)?.asc(),
            )
            .fetchInto(EmployeeMasterPojo::class.java)
            .mapNotNull { it.getEmployee() }
    }

    override fun getAffiliationBranchCode(employeeCode: Employee.Code?): Branch.Code? {
        val employee = findBy(employeeCode) ?: return null
        val officeCode = employee.getOfficeCode() ?: return null

        return runCatching {
            when (employee.company) {
                // 大東建託の場合は支店コード同士のマッピングから取得する
                is Company.DaitouKentaku ->
                    branchRepository.getLeasingRelatedToBranch(Branch.Code.of(officeCode.value))
                // 大東建託パートナーズの場合は営業所と支店コードのマッピングから取得する
                is Company.DaitouKentakuPartners ->
                    officeBranchMappingRepository.get(officeCode)
                // リーシング・その他の場合はそのまま支店コードとなる
                else -> Branch.Code.of(officeCode.value)
            }
        }.getOrElse {
            Branch.Code.of(officeCode.value)
        }
    }
}

interface EmployeeRepositoryInterface {

    /** 指定された従業員コードに紐づく従業員情報を取得する */
    fun findBy(employeeCode: Employee.Code?): Employee?

    /** 指定された支店に所属する従業員一覧を取得する */
    fun findBy(branch: Branch): List<Employee>

    /** 指定された従業員コードに紐づく所属支店コードを取得する */
    fun getAffiliationBranchCode(employeeCode: Employee.Code?): Branch.Code?
}
