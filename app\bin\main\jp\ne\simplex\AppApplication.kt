package jp.ne.simplex

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication

@SpringBootApplication(
    scanBasePackages = ["jp.ne.simplex"],
    exclude = [UserDetailsServiceAutoConfiguration::class]
)
@ConfigurationPropertiesScan
class AppApplication

fun main(args: Array<String>) {
    runApplication<AppApplication>(*args)
}
