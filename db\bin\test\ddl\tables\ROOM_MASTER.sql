-- TABLE: RO<PERSON>_MASTER(部屋マスタ)

CREATE TABLE ROOM_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1,0)                  
,    BUILDING_CODE                                varchar(9)                    
,    ROOM_CODE                                    varchar(5)                    
,    ROOM_NUMBER                                  varchar(4)                    
,    ROOM_PARENT_CHILD_CODE                       varchar(5)                    
,    OWNER_CODE_10                                varchar(10)                   
,    BULK_LEASE_SIGN                              numeric(1,0)                  
,    SET_PROPERTY_SIGN                            numeric(1,0)                  
,    DECLARATION_REQUEST_COLLECTION_SIGN          numeric(1,0)                  
,    LAYOUT_CATEGORY                              varchar(2)                    
,    LAYOUT_DETAILS                               varchar(42)                   
,    FLOOR_NUMBER                                 numeric(3,0)                  
,    OCCUPANCY_MEDIATION_SIGN                     numeric(1,0)                  
,    OCCUPANT_CATEGORY                            varchar(1)                    
,    MUTUAL_AID_ASSOCIATION_SIGN                  numeric(1,0)                  
,    MUTUAL_AID_ASSOCIATION_REFERRAL_COUNT        numeric(1,0)                  
,    MUTUAL_AID_ASSOC_CASH_RECEIPT_AMOUNT         numeric(9,0)                  
,    MANAGEMENT_SIGN                              numeric(1,0)                  
,    MANAGEMENT_UNIT_CATEGORY                     varchar(1)                    
,    REFERRAL_COUNT                               numeric(1,0)                  
,    MANAGEMENT_CONTRACT_CASH_RECEIPT_AMOUNT      numeric(9,0)                  
,    INITIAL_ASSESSMENT_REVIEW_NUMBER             varchar(8)                    
,    OFFICE_FLOOR_AREA_SQUARE_METERS              numeric(7,2)                  
,    RESIDENTIAL_AREA_SQUARE_METERS               numeric(7,2)                  
,    PARKING_AVAILABILITY_SIGN                    numeric(1,0)                  
,    INITIAL_OCCUPANCY_DATE                       numeric(8,0)                  
,    OCCUPANCY_STATUS_SIGN                        numeric(1,0)                  
,    EXPECTED_AVAILABLE_DATE                      numeric(8,0)                  
,    STORE_EXCLUSIVE_AREA_SQUARE_METERS           numeric(7,2)                  
,    BALCONY_AREA                                 numeric(7,2)                  
,    ROOM_POSITION_CATEGORY                       varchar(1)                    
,    ORIENTATION_CATEGORY                         varchar(2)                    
,    ROOM_TYPE                                    varchar(3)                    
,    STUDIO_ROOM_SIGN                             numeric(1,0)                  
,    SERVICE_ROOM_SIGN                            numeric(1,0)                  
,    PRIVATE_GARDEN_AVAILABILITY_SIGN             numeric(1,0)                  
,    UNDERFLOOR_STORAGE_AVAILABILITY_SIGN         numeric(1,0)                  
,    ROOM_EQUIPMENT_CODE                          varchar(24)                   
,    ROOM_FEATURE_CODE                            varchar(6)                    
,    NEW_OR_USED_CATEGORY                         varchar(1)                    
,    CURRENTLY_AVAILABLE_SIGN                     numeric(1,0)                  
,    REMAINING_COLLECTION_DATE                    numeric(8,0)                  
,    FRONT_FREE_RENT_SIGN                         numeric(1,0)                  
,    FRONT_FREE_RENT_MONTHS                       numeric(3,0)                  
,    FRONT_FREE_RENT_AMOUNT                       numeric(9,0)                  
,    ADDITIONAL_ADVERTISING_MONTHS                numeric(3,1)                  
,    RENTAL_ASSESSMENT_STATUS_CATEGORY            varchar(1)                    
,    PRIORITY_INFORMATION_CODE                    varchar(6)                    
,    ASSESSMENT_REVIEW_NUMBER                     varchar(8)                    
,    FIRST_FLOOR_AREA_SQUARE_METERS               numeric(7,2)                  
,    SECOND_FLOOR_AREA_SQUARE_METERS              numeric(7,2)                  
,    THIRD_FLOOR_AREA_SQUARE_METERS               numeric(7,2)                  
,    INTERFACE_SIGN                               numeric(1,0)                  
,    REFERRAL_EXTRACTION_REQUIRED_SIGN            numeric(1,0)                  
,    DATA_MIGRATION_KEY_1                         varchar(15)                   
,    DATA_MIGRATION_KEY_2                         varchar(15)                   
,    ROOM_CHANGED_SIGN                            numeric(1,0)                  
,    ROCKY                                        varchar(1)                    
,    CATEGORY_A                                   varchar(1)                    
,    CATEGORY_B                                   varchar(1)                    
,    EQUIPMENT_CODE                               varchar(30)                   
,    NEW_GUARANTEE_RATE                           numeric(5,2)                  
,    NEW_MANAGEMENT_GUARANTEE_RATE                numeric(5,2)                  
,    CONTRACT_MUTUAL_AID_FEE_RATE                 numeric(5,2)                  
,    REVIVAL_TARGET_ROOM                          numeric(1,0)                  
,    SPECIAL_PREFERRED_RENT_CATEGORY              varchar(1)                    
,    NON_STANDARD_CATEGORY                        numeric(1,0)                  
) TABLESPACE :TS_TBL;

COMMENT ON TABLE ROOM_MASTER IS '部屋マスタ 既存システム物理名: ECNE0P';
COMMENT ON COLUMN ROOM_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: ECN01D 100:アパート,110:店舗付アパート,200:マンション,210:店舗付マンション,300:倉庫,310:事務所付倉庫,320:トランクルーム,350:工場,360:事務所付工場,400:事務所,410:倉庫付事務所,420:工場付事務所,490:専用事務所,500:店舗,510:アパート付店舗,520:マンション付店舗,600:ビル,700:戸建住宅,900:駐車場,990:その他,999:不明';
COMMENT ON COLUMN ROOM_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: ECN02H 0: 1:ワンルーム';
COMMENT ON COLUMN ROOM_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: ECN03D 0: 1:有';
COMMENT ON COLUMN ROOM_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: ECN04H';
COMMENT ON COLUMN ROOM_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: ECN05N';
COMMENT ON COLUMN ROOM_MASTER.UPDATER IS '更新者 既存システム物理名: ECN06C';
COMMENT ON COLUMN ROOM_MASTER.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: ECN07S';
COMMENT ON COLUMN ROOM_MASTER.BUILDING_CODE IS '建物コード 既存システム物理名: ECNABC';
COMMENT ON COLUMN ROOM_MASTER.ROOM_CODE IS '部屋コード 既存システム物理名: ECNACC';
COMMENT ON COLUMN ROOM_MASTER.ROOM_NUMBER IS '部屋番号 既存システム物理名: ECN31N';
COMMENT ON COLUMN ROOM_MASTER.ROOM_PARENT_CHILD_CODE IS '部屋親子関係コード 既存システム物理名: ECN49C';
COMMENT ON COLUMN ROOM_MASTER.OWNER_CODE_10 IS '家主コード(10) 既存システム物理名: ECNCKC';
COMMENT ON COLUMN ROOM_MASTER.BULK_LEASE_SIGN IS '一括借上サイン 既存システム物理名: ECN40S';
COMMENT ON COLUMN ROOM_MASTER.SET_PROPERTY_SIGN IS 'セット物件サイン 既存システム物理名: ECN46S';
COMMENT ON COLUMN ROOM_MASTER.DECLARATION_REQUEST_COLLECTION_SIGN IS '届出依頼書回収 サイン 既存システム物理名: ECN41S';
COMMENT ON COLUMN ROOM_MASTER.LAYOUT_CATEGORY IS '間取区分 既存システム物理名: ECNBPB';
COMMENT ON COLUMN ROOM_MASTER.LAYOUT_DETAILS IS '間取内容 既存システム物理名: ECN08M';
COMMENT ON COLUMN ROOM_MASTER.FLOOR_NUMBER IS '階数 既存システム物理名: ECN09Q';
COMMENT ON COLUMN ROOM_MASTER.OCCUPANCY_MEDIATION_SIGN IS '入居斡旋サイン 既存システム物理名: ECN12S';
COMMENT ON COLUMN ROOM_MASTER.OCCUPANT_CATEGORY IS '入居者区分 既存システム物理名: ECNCSB';
COMMENT ON COLUMN ROOM_MASTER.MUTUAL_AID_ASSOCIATION_SIGN IS '共済会加入サイン 既存システム物理名: ECN13S';
COMMENT ON COLUMN ROOM_MASTER.MUTUAL_AID_ASSOCIATION_REFERRAL_COUNT IS '共済会客付回数 既存システム物理名: ECN36S';
COMMENT ON COLUMN ROOM_MASTER.MUTUAL_AID_ASSOC_CASH_RECEIPT_AMOUNT IS '共済会現金受入額 既存システム物理名: ECN39A';
COMMENT ON COLUMN ROOM_MASTER.MANAGEMENT_SIGN IS '管理サイン 既存システム物理名: ECN14S';
COMMENT ON COLUMN ROOM_MASTER.MANAGEMENT_UNIT_CATEGORY IS '管理単位区分 既存システム物理名: ECNEUB';
COMMENT ON COLUMN ROOM_MASTER.REFERRAL_COUNT IS '客付回数 既存システム物理名: ECN37S';
COMMENT ON COLUMN ROOM_MASTER.MANAGEMENT_CONTRACT_CASH_RECEIPT_AMOUNT IS '管理契約現金受入額 既存システム物理名: ECN38A';
COMMENT ON COLUMN ROOM_MASTER.INITIAL_ASSESSMENT_REVIEW_NUMBER IS '初回査定審査書番号 既存システム物理名: ECN14N';
COMMENT ON COLUMN ROOM_MASTER.OFFICE_FLOOR_AREA_SQUARE_METERS IS '事務所床面積(平米) 既存システム物理名: ECN15Q';
COMMENT ON COLUMN ROOM_MASTER.RESIDENTIAL_AREA_SQUARE_METERS IS '居住用面積(平米) 既存システム物理名: ECN16Q';
COMMENT ON COLUMN ROOM_MASTER.PARKING_AVAILABILITY_SIGN IS '駐車場有無サイン 既存システム物理名: ECN17S';
COMMENT ON COLUMN ROOM_MASTER.INITIAL_OCCUPANCY_DATE IS '初回入居日 既存システム物理名: ECN18D';
COMMENT ON COLUMN ROOM_MASTER.OCCUPANCY_STATUS_SIGN IS '入居状況サイン 既存システム物理名: ECN19S';
COMMENT ON COLUMN ROOM_MASTER.EXPECTED_AVAILABLE_DATE IS '入居可能予定日 既存システム物理名: ECN20D';
COMMENT ON COLUMN ROOM_MASTER.STORE_EXCLUSIVE_AREA_SQUARE_METERS IS '店舗専用面積(平米 既存システム物理名: ECN21Q';
COMMENT ON COLUMN ROOM_MASTER.BALCONY_AREA IS 'ベランダ面積 既存システム物理名: ECN23Q';
COMMENT ON COLUMN ROOM_MASTER.ROOM_POSITION_CATEGORY IS '部屋位置区分 既存システム物理名: ECNBSB';
COMMENT ON COLUMN ROOM_MASTER.ORIENTATION_CATEGORY IS '方位区分 既存システム物理名: ECNBFB';
COMMENT ON COLUMN ROOM_MASTER.ROOM_TYPE IS '部屋種別 既存システム物理名: ECNBRB';
COMMENT ON COLUMN ROOM_MASTER.STUDIO_ROOM_SIGN IS 'ワンルームサイン 既存システム物理名: ECN24S';
COMMENT ON COLUMN ROOM_MASTER.SERVICE_ROOM_SIGN IS 'サービスルームサイン 既存システム物理名: ECN25S';
COMMENT ON COLUMN ROOM_MASTER.PRIVATE_GARDEN_AVAILABILITY_SIGN IS '専用庭有無サイン 既存システム物理名: ECN26S';
COMMENT ON COLUMN ROOM_MASTER.UNDERFLOOR_STORAGE_AVAILABILITY_SIGN IS '床下収納有無サイン 既存システム物理名: ECN27S';
COMMENT ON COLUMN ROOM_MASTER.ROOM_EQUIPMENT_CODE IS '部屋設備コード 既存システム物理名: ECNDBB';
COMMENT ON COLUMN ROOM_MASTER.ROOM_FEATURE_CODE IS '部屋特徴コード 既存システム物理名: ECNDDB';
COMMENT ON COLUMN ROOM_MASTER.NEW_OR_USED_CATEGORY IS '新築・中古区分 既存システム物理名: ECNBOB';
COMMENT ON COLUMN ROOM_MASTER.CURRENTLY_AVAILABLE_SIGN IS '募集中サイン 既存システム物理名: ECN22S';
COMMENT ON COLUMN ROOM_MASTER.REMAINING_COLLECTION_DATE IS '残集日 既存システム物理名: ECN54D';
COMMENT ON COLUMN ROOM_MASTER.FRONT_FREE_RENT_SIGN IS 'フロントフリーレントサイン 既存システム物理名: ECN28S';
COMMENT ON COLUMN ROOM_MASTER.FRONT_FREE_RENT_MONTHS IS 'フロントフリーレント 月数 既存システム物理名: ECN47Q';
COMMENT ON COLUMN ROOM_MASTER.FRONT_FREE_RENT_AMOUNT IS 'フロントフリーレント 金額 既存システム物理名: ECN48A';
COMMENT ON COLUMN ROOM_MASTER.ADDITIONAL_ADVERTISING_MONTHS IS '広告料上乗月数 既存システム物理名: ECN30Q';
COMMENT ON COLUMN ROOM_MASTER.RENTAL_ASSESSMENT_STATUS_CATEGORY IS '家賃査定状況区分 既存システム物理名: ECNCTB';
COMMENT ON COLUMN ROOM_MASTER.PRIORITY_INFORMATION_CODE IS '得情報コード 既存システム物理名: ECNBXB';
COMMENT ON COLUMN ROOM_MASTER.ASSESSMENT_REVIEW_NUMBER IS '査定審査書番号 既存システム物理名: ECNBHN';
COMMENT ON COLUMN ROOM_MASTER.FIRST_FLOOR_AREA_SQUARE_METERS IS '1階床面積(平米) 既存システム物理名: ECN43Q';
COMMENT ON COLUMN ROOM_MASTER.SECOND_FLOOR_AREA_SQUARE_METERS IS '2階床面積(平米) 既存システム物理名: ECN44Q';
COMMENT ON COLUMN ROOM_MASTER.THIRD_FLOOR_AREA_SQUARE_METERS IS '3階床面積(平米) 既存システム物理名: ECN45Q';
COMMENT ON COLUMN ROOM_MASTER.INTERFACE_SIGN IS 'インターフェース サイン 既存システム物理名: ECN32S';
COMMENT ON COLUMN ROOM_MASTER.REFERRAL_EXTRACTION_REQUIRED_SIGN IS '客付要抽出サイン 既存システム物理名: ECN42S';
COMMENT ON COLUMN ROOM_MASTER.DATA_MIGRATION_KEY_1 IS 'データ移行元キー1 既存システム物理名: ECN10N';
COMMENT ON COLUMN ROOM_MASTER.DATA_MIGRATION_KEY_2 IS 'データ移行元キー2 既存システム物理名: ECN11N';
COMMENT ON COLUMN ROOM_MASTER.ROOM_CHANGED_SIGN IS '部屋変更済サイン 既存システム物理名: ECN55S';
COMMENT ON COLUMN ROOM_MASTER.ROCKY IS 'ロッキー 既存システム物理名: ECNROK';
COMMENT ON COLUMN ROOM_MASTER.CATEGORY_A IS '区分A 既存システム物理名: ECNKBA';
COMMENT ON COLUMN ROOM_MASTER.CATEGORY_B IS '区分B 既存システム物理名: ECNKBB';
COMMENT ON COLUMN ROOM_MASTER.EQUIPMENT_CODE IS '設備コード 既存システム物理名: ECNSTB';
COMMENT ON COLUMN ROOM_MASTER.NEW_GUARANTEE_RATE IS '新保証率 既存システム物理名: ECN10A';
COMMENT ON COLUMN ROOM_MASTER.NEW_MANAGEMENT_GUARANTEE_RATE IS '新保証管理率 既存システム物理名: ECN10B';
COMMENT ON COLUMN ROOM_MASTER.CONTRACT_MUTUAL_AID_FEE_RATE IS '請負契約共済会費率 既存システム物理名: ECN10C';
COMMENT ON COLUMN ROOM_MASTER.REVIVAL_TARGET_ROOM IS 'よみがえり対象居室 既存システム物理名: ECN50S';
COMMENT ON COLUMN ROOM_MASTER.SPECIAL_PREFERRED_RENT_CATEGORY IS '特優賃区分 既存システム物理名: ECNTKY';
COMMENT ON COLUMN ROOM_MASTER.NON_STANDARD_CATEGORY IS '定型外区分 既存システム物理名: ECNTGS';
