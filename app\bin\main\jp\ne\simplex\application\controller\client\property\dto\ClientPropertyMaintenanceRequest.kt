package jp.ne.simplex.application.controller.client.property.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.*
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException

data class ClientPropertyMaintenanceRequest(
    @JsonProperty("propertyMaintenanceList")
    @field:Schema(description = "物件メンテナンス")
    val list: List<ClientPropertyMaintenanceUpdate>
) {
    fun toServiceInterface(): List<UpdatePropertyMaintenance> {
        return list.map { it.toServiceInterface() }
    }
}

data class ClientPropertyMaintenanceUpdate(
    @JsonProperty("buildingCd")
    @field:Schema(description = "建物コード", example = "000130305")
    val buildingCd: String,

    @JsonProperty("roomCd")
    @field:Schema(description = "部屋コード", example = "99020")
    val roomCd: String,

    @JsonProperty("publicInstruction")
    @field:Schema(description = "公開指示(true:公開、false:非公開)", example = "true")
    val publicInstruction: Boolean,

    @JsonProperty("adAmount")
    @field:Schema(description = "AD金額", example = "5000")
    val adAmount: Int?,

    @JsonProperty("ffPeriod")
    @field:Schema(description = "FF期間", example = "0.5")
    val ffPeriod: Float?,
) {

    // Service層の Interface に変換する
    fun toServiceInterface(): UpdatePropertyMaintenance {
        try {
            return UpdatePropertyMaintenance(
                id = Property.Id(
                    buildingCode = Building.Code.of(buildingCd),
                    roomCode = Room.Code.of(roomCd)
                ),
                publishStatus =
                    if (publicInstruction) PropertyMaintenance.PublishStatus.PUBLIC
                    else PropertyMaintenance.PublishStatus.PRIVATE,

                adFf = PropertyMaintenance.AdFf(
                    advertisementFee = adAmount,
                    frontFreerentPeriod = ffPeriod
                )
            )

        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }
}

