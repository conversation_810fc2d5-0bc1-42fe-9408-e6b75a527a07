-- TABLE: HR_CATEGORY_TABLE_B(人事区分テーブル(Ｂ))

CREATE TABLE HR_CATEGORY_TABLE_B(
     TYPE_CATEGORY                                varchar(2)                    
,    CODE                                         varchar(10)                   
,    KANJI_NAME                                   varchar(42)                   
,    KANJI_ABBREVIATION_1                         varchar(22)                   
,    KANJI_ABBREVIATION_2                         varchar(12)                   
,    CATEGORY_1                                   varchar(10)                   
,    CATEGORY_2                                   varchar(10)                   
,    CATEGORY_3                                   varchar(10)                   
,    CATEGORY_4                                   varchar(10)                   
,    CATEGORY_5                                   varchar(10)                   
,    CATEGORY_NAME_1                              varchar(22)                   
,    CATEGORY_NAME_2                              varchar(22)                   
,    CATEGORY_NAME_3                              varchar(22)                   
,    CATEGORY_NAME_4                              varchar(22)                   
,    CATEGORY_NAME_5                              varchar(22)                   
,    DELETE_CATEGORY                              varchar(1)                    
,    CREATOR                                      varchar(6)                    
,    CREATION_PROGRAM                             varchar(10)                   
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    UPDATE_PROGRAM                               varchar(10)                   
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    TERMINAL_ID                                  varchar(10)                   
) TABLESPACE :TS_TBL;

COMMENT ON TABLE HR_CATEGORY_TABLE_B IS '人事区分テーブル(Ｂ) 既存システム物理名: JXE1MP';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.TYPE_CATEGORY IS '種類区分 既存システム物理名: SHURUI_KBN';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CODE IS 'コード 既存システム物理名: CODE';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.KANJI_NAME IS '漢字名称 既存システム物理名: KANJI_MEISHO';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.KANJI_ABBREVIATION_1 IS '漢字略称1 既存システム物理名: KANJI_RYAKUSHO_1';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.KANJI_ABBREVIATION_2 IS '漢字略称2 既存システム物理名: KANJI_RYAKUSHO_2';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CATEGORY_1 IS '区分1 既存システム物理名: KBN_1';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CATEGORY_2 IS '区分2 既存システム物理名: KBN_2';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CATEGORY_3 IS '区分3 既存システム物理名: KBN_3';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CATEGORY_4 IS '区分4 既存システム物理名: KBN_4';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CATEGORY_5 IS '区分5 既存システム物理名: KBN_5';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CATEGORY_NAME_1 IS '区分名称1 既存システム物理名: KBN_MEISHO_1';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CATEGORY_NAME_2 IS '区分名称2 既存システム物理名: KBN_MEISHO_2';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CATEGORY_NAME_3 IS '区分名称3 既存システム物理名: KBN_MEISHO_3';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CATEGORY_NAME_4 IS '区分名称4 既存システム物理名: KBN_MEISHO_4';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CATEGORY_NAME_5 IS '区分名称5 既存システム物理名: KBN_MEISHO_5';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.DELETE_CATEGORY IS '削除区分 既存システム物理名: SAKUJO_KBN';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CREATOR IS '作成者 既存システム物理名: SAKUSEISHA';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CREATION_PROGRAM IS '作成プログラム 既存システム物理名: SAKUSEI_PGM';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CREATION_DATE IS '作成年月日 既存システム物理名: SAKUSEI_DT';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.CREATION_TIME IS '作成時間 既存システム物理名: SAKUSEI_TM';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.UPDATER IS '更新者 既存システム物理名: KOSHINSHA';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.UPDATE_PROGRAM IS '更新プログラム 既存システム物理名: KOSHIN_PGM';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.UPDATE_DATE IS '更新年月日 既存システム物理名: KOSHIN_DT';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.UPDATE_TIME IS '更新時刻 既存システム物理名: KOSHIN_TM';
COMMENT ON COLUMN HR_CATEGORY_TABLE_B.TERMINAL_ID IS '端末ID 既存システム物理名: TANMATSU_ID';
