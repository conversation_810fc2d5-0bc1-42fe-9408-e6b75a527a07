/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.BulkLeaseParkingTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BulkLeaseParkingPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 一括借上駐車場 既存システム物理名: EDD30P
 */
@Suppress("UNCHECKED_CAST")
open class BulkLeaseParkingRecord private constructor() : UpdatableRecordImpl<BulkLeaseParkingRecord>(BulkLeaseParkingTable.BULK_LEASE_PARKING) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var buildingCode: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var parkingCode: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    open var assessmentDivision: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised BulkLeaseParkingRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteSign: Byte? = null, buildingCode: String, parkingCode: String, assessmentDivision: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteSign = logicalDeleteSign
        this.buildingCode = buildingCode
        this.parkingCode = parkingCode
        this.assessmentDivision = assessmentDivision
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BulkLeaseParkingRecord
     */
    constructor(value: BulkLeaseParkingPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteSign = value.logicalDeleteSign
            this.buildingCode = value.buildingCode
            this.parkingCode = value.parkingCode
            this.assessmentDivision = value.assessmentDivision
            resetChangedOnNotNull()
        }
    }
}
