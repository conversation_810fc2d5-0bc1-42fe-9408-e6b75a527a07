-- TABLE: REG<PERSON>_MASTER(地域マスタ)

CREATE TABLE REGION_MASTER(
     GROUP_CODE                                   varchar(2)        NOT NULL    
,    DEPARTMENT_CODE                              varchar(6)        NOT NULL    
,    USE_START_DATE                               numeric(8,0)      NOT NULL    
,    USE_FINISH_DATE                              numeric(8,0)                  
,    REGION_CODE_1                                varchar(2)                    
,    REGION_NAME_1                                varchar(22)                   
,    REGION_ORDER_1                               varchar(6)                    
,    REGION_CODE_2                                varchar(2)                    
,    REGION_NAME_2                                varchar(22)                   
,    REGION_ORDER_2                               varchar(6)                    
,    REGION_CODE_3                                varchar(2)                    
,    REGION_NAME_3                                varchar(22)                   
,    REGION_ORDER_3                               varchar(6)                    
,    REGION_CODE_4                                varchar(2)                    
,    REGION_NAME_4                                varchar(22)                   
,    REGION_ORDER_4                               varchar(6)                    
,    REGION_CODE_5                                varchar(2)                    
,    REGION_NAME_5                                varchar(22)                   
,    REGION_ORDER_5                               varchar(6)                    
,    SALES_DEPARTMENT_SUPERIOR                    varchar(6)                    
,    IS_DELETED                                   varchar(1)                    
,    CREATE_USER                                  varchar(6)                    
,    CREATE_PROGRAM_ID                            varchar(10)                   
,    CREATE_DATE                                  numeric(8,0)                  
,    CREATE_TIME                                  numeric(6,0)                  
,    UPDATE_USER                                  varchar(6)                    
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    DEVICE_ID                                    varchar(10)                   
,    CONSTRAINT PK_REGION_MASTER PRIMARY KEY (GROUP_CODE, DEPARTMENT_CODE, USE_START_DATE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE REGION_MASTER IS '地域マスタ 既存システム物理名: JXH1MP';
COMMENT ON COLUMN REGION_MASTER.GROUP_CODE IS 'グループコード 既存システム物理名: JXH01C';
COMMENT ON COLUMN REGION_MASTER.DEPARTMENT_CODE IS '所属コード 既存システム物理名: JXH02C';
COMMENT ON COLUMN REGION_MASTER.USE_START_DATE IS '使用開始年月日 既存システム物理名: JXH03D';
COMMENT ON COLUMN REGION_MASTER.USE_FINISH_DATE IS '使用終了年月日 既存システム物理名: JXH04D';
COMMENT ON COLUMN REGION_MASTER.REGION_CODE_1 IS '地域1コード 既存システム物理名: JXH05C';
COMMENT ON COLUMN REGION_MASTER.REGION_NAME_1 IS '地域1名称 既存システム物理名: JXH06M';
COMMENT ON COLUMN REGION_MASTER.REGION_ORDER_1 IS '地域順1 既存システム物理名: JXH07C';
COMMENT ON COLUMN REGION_MASTER.REGION_CODE_2 IS '地域2コード 既存システム物理名: JXH08C';
COMMENT ON COLUMN REGION_MASTER.REGION_NAME_2 IS '地域2名称 既存システム物理名: JXH09M';
COMMENT ON COLUMN REGION_MASTER.REGION_ORDER_2 IS '地域順2 既存システム物理名: JXH10C';
COMMENT ON COLUMN REGION_MASTER.REGION_CODE_3 IS '地域3コード 既存システム物理名: JXH11C';
COMMENT ON COLUMN REGION_MASTER.REGION_NAME_3 IS '地域3名称 既存システム物理名: JXH12M';
COMMENT ON COLUMN REGION_MASTER.REGION_ORDER_3 IS '地域順3 既存システム物理名: JXH13C';
COMMENT ON COLUMN REGION_MASTER.REGION_CODE_4 IS '地域4コード 既存システム物理名: JXH14C';
COMMENT ON COLUMN REGION_MASTER.REGION_NAME_4 IS '地域4名称 既存システム物理名: JXH15M';
COMMENT ON COLUMN REGION_MASTER.REGION_ORDER_4 IS '地域順4 既存システム物理名: JXH16C';
COMMENT ON COLUMN REGION_MASTER.REGION_CODE_5 IS '地域5コード 既存システム物理名: JXH17C';
COMMENT ON COLUMN REGION_MASTER.REGION_NAME_5 IS '地域5名称 既存システム物理名: JXH18M';
COMMENT ON COLUMN REGION_MASTER.REGION_ORDER_5 IS '地域順5 既存システム物理名: JXH19C';
COMMENT ON COLUMN REGION_MASTER.SALES_DEPARTMENT_SUPERIOR IS '営業部上位所属 既存システム物理名: JXH20C';
COMMENT ON COLUMN REGION_MASTER.IS_DELETED IS '削除区分 既存システム物理名: JXHZ0B';
COMMENT ON COLUMN REGION_MASTER.CREATE_USER IS '作成者 既存システム物理名: JXHZ1C';
COMMENT ON COLUMN REGION_MASTER.CREATE_PROGRAM_ID IS '作成プログラム 既存システム物理名: JXHZ2C';
COMMENT ON COLUMN REGION_MASTER.CREATE_DATE IS '作成年月日 既存システム物理名: JXHZ3D';
COMMENT ON COLUMN REGION_MASTER.CREATE_TIME IS '作成時間 既存システム物理名: JXHZ4H';
COMMENT ON COLUMN REGION_MASTER.UPDATE_USER IS '更新者 既存システム物理名: JXHZ5C';
COMMENT ON COLUMN REGION_MASTER.UPDATE_PROGRAM_ID IS '更新プログラム 既存システム物理名: JXHZ6C';
COMMENT ON COLUMN REGION_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: JXHZ7D';
COMMENT ON COLUMN REGION_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: JXHZ8H';
COMMENT ON COLUMN REGION_MASTER.DEVICE_ID IS '端末ID 既存システム物理名: JXHZ9C';
