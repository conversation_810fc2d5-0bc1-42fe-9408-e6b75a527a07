package jp.ne.simplex.application.repository.db.extension

import jp.ne.simplex.application.model.*
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingReservationPojo
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import org.slf4j.LoggerFactory
import java.text.SimpleDateFormat

class ParkingReservationEx {

    companion object {
        private val log = LoggerFactory.getLogger(ParkingReservationEx::class.java)

        fun ParkingReservationPojo.toParkingReservationInfo(): ParkingReservationInfo? {
            return try {
                ParkingReservationInfo(
                    id = ParkingReservation.Id.of(this.parkingReservationId),
                    buildingCode = Building.Code.of(this.buildingCode),
                    parkingLotCode = this.parkingLotCode?.let { ParkingLot.Code.of(it) },
                    status = ParkingReservation.Status.fromValue(this.reserveStatus)!!,
                    reservationType = ParkingReservation.Type.fromValue(this.reserveType)!!,
                    reserveStartDatetime = this.reserveStartDatetime,
                    reserveEndDatetime = this.reserveEndDatetime,
                    remarks = this.remarks?.let { ParkingReservation.Remarks.of(it) },
                    requestSource = ParkingReservation.RequestSource.fromValue(this.reserverSystem),
                    receptionDate = this.receptionDate.toString().yyyyMMdd(),
                    eBoardParkingReservationId = ParkingReservation.EBoardId.of(this.eboardParkingReservationId),
                    receptionStaff = this.receptionStaff,
                    reserverName = this.reserverName,
                    reserverTel = this.reserverTel?.let { TelephoneNumber.of(it) },
                    updateDateTime = toUpdateDate(
                        this.creationDate,
                        this.creationTime,
                        this.updateDate,
                        this.updateTime
                    )
                )
            } catch (e: Exception) {
                // データ不備がなければ発生しないはず
                log.warn("Invalid parking_reservation. ParkingReservationPojo=${this}", e)
                null
            }
        }

        // Converts creation and update date/time to a formatted string
        private fun toUpdateDate(
            creationDate: Int?,
            creationTime: Int?,
            updateDate: Int?,
            updateTime: Int?
        ): String? {
            // yyyymmddhhmmss -> yyyy/mm/dd HH:mm:ss形式に変換
            return toLocalDateString(updateDate, updateTime) ?: toLocalDateString(
                creationDate,
                creationTime
            )
        }

        private fun toLocalDateString(date: Int?, time: Int?): String? {
            if (date == null || date.toString().length != 8 || time == null) return null
            val dateString = date.toString() + time.toString().padStart(6, '0')
            val sdf = SimpleDateFormat("yyyyMMddHHmmss")
            val convertedDate = sdf.parse(dateString)
            val sdf2 = SimpleDateFormat("yyyy/MM/dd HH:mm:ss")
            return sdf2.format(convertedDate)
        }

    }
}
