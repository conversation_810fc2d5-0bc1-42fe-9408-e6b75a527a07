package jp.ne.simplex.application.service

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.db.PropertyMaintenanceRepository
import jp.ne.simplex.application.repository.db.TemporaryReservationRepository
import jp.ne.simplex.db.jooq.gen.tables.pojos.PropertyMaintenanceInfoPojo
import jp.ne.simplex.db.jooq.gen.tables.records.PropertyMaintenanceInfoRecord
import jp.ne.simplex.db.jooq.gen.tables.references.PROPERTY_MAINTENANCE_INFO
import jp.ne.simplex.db.jooq.gen.tables.references.TEMPORARY_RESERVATION_FILE
import jp.ne.simplex.db.jooq.gen.tables.references.VACANT_HOUSE_HP
import jp.ne.simplex.exception.*
import jp.ne.simplex.mock.*
import jp.ne.simplex.shared.DSLContextEx.Companion.selectTemporaryReservationBy
import jp.ne.simplex.stub.*
import org.jooq.Configuration
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.*
import kotlin.test.assertEquals

class TemporaryReservationServiceTest : AbstractTestContainerTest() {

    private lateinit var temporaryReservationRepository: TemporaryReservationRepository

    override fun beforeEach() {
        temporaryReservationRepository =
            TemporaryReservationRepository(
                dslContext,
                MockBranchRepository(),
                MockEmployeeRepository()
            )
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(TEMPORARY_RESERVATION_FILE, PROPERTY_MAINTENANCE_INFO, VACANT_HOUSE_HP)
    }

    @Nested
    @DisplayName("仮押さえ登録時の、DB/外部API呼び出しのトランザクション管理が適切に行えていることの検証")
    inner class Scenario1 {
        // setup
        private val registerReq = stubOwnCompanyRegisterTemporaryReservation()
        private var eboardApiRegisterCallCount = 0
        private var eboardApiCancelCallCount = 0

        private val propertyRepository = MockPropertyRepository(
            listFunc = { _ ->
                listOf(
                    stubProperty(
                        buildingCode = registerReq.getId().buildingCode.value,
                        roomCode = registerReq.getId().roomCode.value,
                    )
                )
            }
        )

        @AfterEach
        fun tearDown() {
            eboardApiRegisterCallCount = 0
            eboardApiCancelCallCount = 0
        }

        @Test
        @DisplayName("外部APIの実行が成功した場合、DBへの更新処理が反映されること")
        fun case1() {
            // execute
            TemporaryReservationService(
                dslContext,
                temporaryReservationRepository,
                MockEboardRepository(
                    registerTemporaryReservationFunc = { _ -> eboardApiRegisterCallCount++ },
                    cancelTemporaryReservationFunc = { _ -> eboardApiCancelCallCount++ },
                ),
                propertyRepository,
                MockPropertyMaintenanceRepository(dslContext),
            ).update(registerReq, stubJwtRequestUser())

            // verify
            assertEquals(1, dslContext.selectTemporaryReservationBy(registerReq.getId()).size)
            assertEquals(1, eboardApiRegisterCallCount)
            assertEquals(0, eboardApiCancelCallCount)
        }

        @Test
        @DisplayName("DBへの更新処理で失敗した場合、外部APIをコールしないこと")
        fun case2() {
            // execute
            assertThrows<DBValidationException> {
                TemporaryReservationService(
                    dslContext,
                    MockTemporaryReservationRepository(
                        registerFunc = { _, _ -> throw DBValidationException(ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format()) }
                    ),
                    MockEboardRepository(
                        registerTemporaryReservationFunc = { _ -> eboardApiRegisterCallCount++ },
                        cancelTemporaryReservationFunc = { _ -> eboardApiCancelCallCount++ },
                    ),
                    propertyRepository,
                    MockPropertyMaintenanceRepository(dslContext),
                ).update(registerReq, stubJwtRequestUser())
            }

            // verify
            assertEquals(0, eboardApiRegisterCallCount)
            assertEquals(0, eboardApiCancelCallCount)
        }

        @Test
        @DisplayName("外部APIの実行が失敗した場合、DBへの更新処理はロールバックされること")
        fun case3() {
            // execute
            assertThrows<ExternalApiConnectionException> {
                TemporaryReservationService(
                    dslContext,
                    temporaryReservationRepository,
                    MockEboardRepository(
                        registerTemporaryReservationFunc = { _ ->
                            throw ExternalApiConnectionException(ErrorType.UNEXPECTED_ERROR)
                        },
                    ),
                    propertyRepository,
                    MockPropertyMaintenanceRepository(dslContext),
                ).update(registerReq, stubJwtRequestUser())
            }

            // verify
            assertEquals(0, dslContext.selectTemporaryReservationBy(registerReq.getId()).size)
        }
    }

    @Nested
    @DisplayName("仮押さえ解除時の、DB/外部API呼び出しのトランザクション管理が適切に行えていることの検証")
    inner class Scenario2 {

        // setup
        private val cancelReq = stubCancelTemporaryReservation()
        private var eboardApiRegisterCallCount = 0
        private var eboardApiCancelCallCount = 0
        private val propertyRepository = MockPropertyRepository(
            listFunc = { _ ->
                listOf(
                    stubProperty(
                        buildingCode = cancelReq.getId().buildingCode.value,
                        roomCode = cancelReq.getId().roomCode.value,
                    )
                )
            }
        )

        @BeforeEach
        fun setup() {
            temporaryReservationRepository = object : TemporaryReservationRepository(
                dslContext,
                MockBranchRepository(),
                MockEmployeeRepository()
            ) {
                override fun cancel(
                    config: Configuration, ctr: CancelTemporaryReservation
                ): TemporaryReservationInfo {
                    return super.cancel(config, ctr) ?: stubOwnCompanyTemporaryReservationInfo()
                }
            }
        }

        @AfterEach
        fun tearDown() {
            eboardApiRegisterCallCount = 0
            eboardApiCancelCallCount = 0
        }

        @Test
        @DisplayName("外部APIの実行が成功した場合、DBへの更新処理が反映されること")
        fun case1() {
            // execute
            TemporaryReservationService(
                dslContext,
                temporaryReservationRepository,
                MockEboardRepository(
                    registerTemporaryReservationFunc = { _ -> eboardApiRegisterCallCount++ },
                    cancelTemporaryReservationFunc = { _ -> eboardApiCancelCallCount++ },
                ),
                propertyRepository,
                MockPropertyMaintenanceRepository(dslContext),
            ).update(cancelReq, stubJwtRequestUser())

            // verify
            assertEquals(1, dslContext.selectTemporaryReservationBy(cancelReq.getId()).size)
            assertEquals(0, eboardApiRegisterCallCount)
            assertEquals(1, eboardApiCancelCallCount)
        }

        @Test
        @DisplayName("DBへの更新処理で失敗した場合、外部APIをコールしないこと")
        fun case2() {
            // execute
            assertThrows<DBValidationException> {
                TemporaryReservationService(
                    dslContext,
                    MockTemporaryReservationRepository(
                        cancelFunc = { _, _ -> throw DBValidationException(ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format()) }
                    ),
                    MockEboardRepository(
                        registerTemporaryReservationFunc = { _ -> eboardApiRegisterCallCount++ },
                        cancelTemporaryReservationFunc = { _ -> eboardApiCancelCallCount++ },
                    ),
                    propertyRepository,
                    MockPropertyMaintenanceRepository(dslContext),
                ).update(cancelReq, stubJwtRequestUser())
            }

            // verify
            assertEquals(0, eboardApiRegisterCallCount)
            assertEquals(0, eboardApiCancelCallCount)
        }

        @Test
        @DisplayName("外部APIの実行が失敗した場合、DBへの更新処理はロールバックされること")
        fun case3() {
            // execute
            assertThrows<ExternalApiConnectionException> {
                TemporaryReservationService(
                    dslContext,
                    temporaryReservationRepository,
                    MockEboardRepository(
                        cancelTemporaryReservationFunc = { _ ->
                            throw ExternalApiConnectionException(ErrorType.UNEXPECTED_ERROR)
                        },
                    ),
                    propertyRepository,
                    MockPropertyMaintenanceRepository(dslContext),
                ).update(cancelReq, stubJwtRequestUser())
            }

            // verify
            assertEquals(0, dslContext.selectTemporaryReservationBy(cancelReq.getId()).size)
        }
    }

    @Nested
    @DisplayName("仮押さえコメント更新時の、DB/外部API呼び出しのトランザクション管理が適切に行えていることの検証")
    inner class Scenario3 {

        // setup
        private val request = stubUpdateTemporaryReservationComment()
        private var eboardApiCallCount = 0
        private val propertyRepository = MockPropertyRepository(
            listFunc = { _ ->
                listOf(
                    stubProperty(
                        buildingCode = request.getId().buildingCode.value,
                        roomCode = request.getId().roomCode.value,
                    )
                )
            }
        )

        @AfterEach
        fun tearDown() {
            eboardApiCallCount = 0
        }

        @Test
        @DisplayName("外部APIの実行が成功した場合、DBへの更新処理が反映されること")
        fun case1() {
            // execute
            TemporaryReservationService(
                dslContext,
                temporaryReservationRepository,
                MockEboardRepository(
                    updateTemporaryReservationCommentFunc = { _ -> eboardApiCallCount++ }
                ),
                propertyRepository,
                MockPropertyMaintenanceRepository(dslContext),
            ).update(request, stubJwtRequestUser())

            // verify
            assertEquals(1, dslContext.selectTemporaryReservationBy(request.getId()).size)
            assertEquals(1, eboardApiCallCount)
        }

        @Test
        @DisplayName("DBへの更新処理で失敗した場合、外部APIをコールしないこと")
        fun case2() {
            // execute
            assertThrows<DBValidationException> {
                TemporaryReservationService(
                    dslContext,
                    MockTemporaryReservationRepository(
                        updateCommentFunc = { _, _ -> throw DBValidationException(ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format()) }
                    ),
                    MockEboardRepository(
                        updateTemporaryReservationCommentFunc = { _ -> eboardApiCallCount++ }
                    ),
                    propertyRepository,
                    MockPropertyMaintenanceRepository(dslContext),
                ).update(request, stubJwtRequestUser())
            }

            // verify
            assertEquals(0, eboardApiCallCount)
        }

        @Test
        @DisplayName("外部APIの実行が失敗した場合、DBへの更新処理はロールバックされること")
        fun case3() {
            // execute
            assertThrows<ExternalApiConnectionException> {
                TemporaryReservationService(
                    dslContext,
                    temporaryReservationRepository,
                    MockEboardRepository(
                        updateTemporaryReservationCommentFunc = { _ ->
                            throw ExternalApiConnectionException(ErrorType.UNEXPECTED_ERROR)
                        },
                    ),
                    propertyRepository,
                    MockPropertyMaintenanceRepository(dslContext),
                ).update(request, stubJwtRequestUser())
            }

            // verify
            assertEquals(0, dslContext.selectTemporaryReservationBy(request.getId()).size)
        }
    }

    @Nested
    @DisplayName("仮押さえ対象の物件が仮押さえ操作できない状態の時、Exceptionがスローされること")
    inner class Scenario4 {

        @Nested
        @DisplayName("対象の物件が仮押さえ操作できない状態の時、仮押さえ解除時にExceptionがスローされること")
        inner class CancelTemporaryReservationScenario {

            private val request = stubCancelTemporaryReservation()
            private val propertyMaintenanceRepository = PropertyMaintenanceRepository(dslContext)

            @BeforeEach
            fun setup() {
                // DBにデータをInsertしておく
                dslContext.save(
                    table = PROPERTY_MAINTENANCE_INFO,
                    recordConstructor = { p: PropertyMaintenanceInfoPojo ->
                        PropertyMaintenanceInfoRecord(p)
                    },
                    pojos = listOf(
                        stubPropertyMaintenanceInfoPojo(
                            buildingCode = request.getId().buildingCode.value,
                            roomCode = request.getId().roomCode.value,
                            publishStatus = PropertyMaintenance.PublishStatus.PUBLIC,
                        )
                    )
                )
            }

            @Test
            @DisplayName("リクエストされた対象の物件が存在しない場合、Exceptionがスローされること")
            fun case1() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ -> emptyList() }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「申込登録済」の場合、Exceptionがスローされること")
            fun case2() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.FORM_REGISTERED,
                            )
                        )
                    }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「手付変更済」の場合、Exceptionがスローされること")
            fun case3() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.DEPOSIT_CHANGED,
                            )
                        )
                    }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「残集確定入力済」の場合、Exceptionがスローされること")
            fun case4() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.REMAINING_COLLECTION_CONFIRMATION_INPUT,
                            )
                        )
                    }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「残集入力済」の場合、Exceptionがスローされること")
            fun case5() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.REMAINING_COLLECTION_INPUT,
                            )
                        )
                    }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「入居中」の場合、Exceptionがスローされること")
            fun case6() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.OCCUPIED,
                            )
                        )
                    }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「削除」の場合、Exceptionがスローされること")
            fun case7() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.DELETED,
                            )
                        )
                    }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }
        }

        @Nested
        @DisplayName("対象の物件が仮押さえ操作できない状態の時、仮押さえ解除時にExceptionがスローされること")
        inner class RegisterTemporaryReservationScenario {

            private val request = stubOwnCompanyRegisterTemporaryReservation()
            private val propertyMaintenanceRepository = PropertyMaintenanceRepository(dslContext)

            @BeforeEach
            fun setup() {
                // DBにデータをInsertしておく
                dslContext.save(
                    table = PROPERTY_MAINTENANCE_INFO,
                    recordConstructor = { p: PropertyMaintenanceInfoPojo ->
                        PropertyMaintenanceInfoRecord(p)
                    },
                    pojos = listOf(
                        stubPropertyMaintenanceInfoPojo(
                            buildingCode = request.getId().buildingCode.value,
                            roomCode = request.getId().roomCode.value,
                            publishStatus = PropertyMaintenance.PublishStatus.PUBLIC,
                        )
                    )
                )
            }

            @Test
            @DisplayName("リクエストされた対象の物件が存在しない場合、Exceptionがスローされること")
            fun case1() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ -> emptyList() }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「申込登録済」の場合、Exceptionがスローされること")
            fun case2() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.FORM_REGISTERED,
                            )
                        )
                    }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「手付変更済」の場合、Exceptionがスローされること")
            fun case3() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.DEPOSIT_CHANGED,
                            )
                        )
                    }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「残集確定入力済」の場合、Exceptionがスローされること")
            fun case4() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.REMAINING_COLLECTION_CONFIRMATION_INPUT,
                            )
                        )
                    }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「残集入力済」の場合、Exceptionがスローされること")
            fun case5() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.REMAINING_COLLECTION_INPUT,
                            )
                        )
                    }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「入居中」の場合、Exceptionがスローされること")
            fun case6() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.OCCUPIED,
                            )
                        )
                    }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「削除」の場合、Exceptionがスローされること")
            fun case7() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.DELETED,
                            )
                        )
                    }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }
        }

        @Nested
        @DisplayName("対象の物件が仮押さえ操作できない状態の時、仮押さえ解除時にExceptionがスローされること")
        inner class UpdateTemporaryReservationCommentScenario {

            private val request = stubUpdateTemporaryReservationComment()
            private val propertyMaintenanceRepository = PropertyMaintenanceRepository(dslContext)

            @BeforeEach
            fun setup() {
                // DBにデータをInsertしておく
                dslContext.save(
                    table = PROPERTY_MAINTENANCE_INFO,
                    recordConstructor = { p: PropertyMaintenanceInfoPojo ->
                        PropertyMaintenanceInfoRecord(p)
                    },
                    pojos = listOf(
                        stubPropertyMaintenanceInfoPojo(
                            buildingCode = request.getId().buildingCode.value,
                            roomCode = request.getId().roomCode.value,
                            publishStatus = PropertyMaintenance.PublishStatus.PUBLIC,
                        )
                    )
                )
            }

            @Test
            @DisplayName("リクエストされた対象の物件が存在しない場合、Exceptionがスローされること")
            fun case1() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ -> emptyList() }
                )

                // execute & verify
                assertThrows<ServerValidationException> {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }

            @Test
            @DisplayName("リクエストされた対象の物件ステータスが「申込登録済」の場合でも、Exceptionがスローされないこと")
            fun case2() {
                // setup
                val propertyRepository = MockPropertyRepository(
                    listFunc = { _ ->
                        listOf(
                            stubProperty(
                                buildingCode = request.getId().buildingCode.value,
                                roomCode = request.getId().roomCode.value,
                                recordStatusType = Property.RecordStatusType.FORM_REGISTERED,
                            )
                        )
                    }
                )

                // execute & verify
                assertDoesNotThrow {
                    TemporaryReservationService(
                        dslContext,
                        temporaryReservationRepository,
                        MockEboardRepository(),
                        propertyRepository,
                        propertyMaintenanceRepository
                    ).update(request, stubJwtRequestUser())
                }
            }
        }
    }

    @Nested
    @DisplayName("仮押さえ時に対象の物件の掲載ステータスを更新する")
    inner class Scenario5 {

        private val request = stubOwnCompanyForceRegisterTemporaryReservation()
        private val cancelRequest = stubForceCancelTemporaryReservation()
        val buildingCode = request.getId().buildingCode.value
        val roomCode = request.getId().roomCode.value
        private val propertyRepository = setupPropertyRepository(buildingCode, roomCode)

        @Test
        @DisplayName("仮押さえ時に対象の物件を非公開にする")
        fun case1() {
            // setup
            val propertyMaintenanceRepository = setupPropertyMaintenanceExist()

            // execute
            TemporaryReservationService(
                dslContext,
                temporaryReservationRepository,
                MockEboardRepository(),
                propertyRepository,
                propertyMaintenanceRepository
            ).update(request, stubJwtRequestUser())

            // verify
            val result =
                propertyMaintenanceRepository.listBy(
                    listOf(
                        Property.Id(
                            Building.Code.of(buildingCode),
                            Room.Code.of(roomCode)
                        )
                    )
                )
            assertEquals(PropertyMaintenance.PublishStatus.PRIVATE, result[0].publishStatus)
        }

        @Test
        @DisplayName("公開指示が一度も行われていない物件に対して、仮押さえ時に対象の物件を非公開にする")
        fun case2() {
            // setup
            val propertyMaintenanceRepository = PropertyMaintenanceRepository(dslContext)

            // execute
            TemporaryReservationService(
                dslContext,
                temporaryReservationRepository,
                MockEboardRepository(),
                propertyRepository,
                propertyMaintenanceRepository
            ).update(request, stubJwtRequestUser())

            // verify
            val result =
                propertyMaintenanceRepository.listBy(
                    listOf(
                        Property.Id(
                            Building.Code.of(buildingCode),
                            Room.Code.of(roomCode)
                        )
                    )
                )
            assertEquals(PropertyMaintenance.PublishStatus.PRIVATE, result[0].publishStatus)
        }

        @Test
        @DisplayName("仮押さえ時に対象の物件の掲載ステータスを退避できる")
        fun case3() {
            // setup
            val propertyMaintenanceRepository = setupPropertyMaintenanceExist()

            // execute
            TemporaryReservationService(
                dslContext,
                temporaryReservationRepository,
                MockEboardRepository(),
                propertyRepository,
                propertyMaintenanceRepository
            ).update(request, stubJwtRequestUser())

            // verify
            val result =
                propertyMaintenanceRepository.listBy(
                    listOf(
                        Property.Id(
                            Building.Code.of(buildingCode),
                            Room.Code.of(roomCode)
                        )
                    )
                )
            assertEquals(
                PropertyMaintenance.PublishStatus.PUBLIC,
                result[0].publishStatusBeforeTemporaryReserved
            )
        }

        @Test
        @DisplayName("公開指示が一度も行われていない物件に対して、仮押さえ時に対象の物件の掲載ステータスを退避できる")
        fun case4() {
            // setup
            val propertyMaintenanceRepository = PropertyMaintenanceRepository(dslContext)

            // execute
            TemporaryReservationService(
                dslContext,
                temporaryReservationRepository,
                MockEboardRepository(),
                propertyRepository,
                propertyMaintenanceRepository
            ).update(request, stubJwtRequestUser())

            // verify
            val result =
                propertyMaintenanceRepository.listBy(
                    listOf(
                        Property.Id(
                            Building.Code.of(buildingCode),
                            Room.Code.of(roomCode)
                        )
                    )
                )
            assertEquals(
                PropertyMaintenance.PublishStatus.PRIVATE,
                result[0].publishStatusBeforeTemporaryReserved
            )
        }

        @Test
        @DisplayName("仮押さえ解除時に対象の物件を仮押さえ前の状態にする")
        fun case5() {
            // setup
            val propertyMaintenanceRepository = setupPropertyMaintenanceExist()
            dslContext.saveVacantHousePojo(
                stubVacantHouseHpPojo(
                    buildingCode = request.getId().buildingCode.value,
                    roomCode = request.getId().roomCode.value,
                    customerCompletionFlag = "0"
                )
            )

            TemporaryReservationService(
                dslContext,
                temporaryReservationRepository,
                MockEboardRepository(),
                propertyRepository,
                propertyMaintenanceRepository
            ).update(request, stubJwtRequestUser())

            // execute
            TemporaryReservationService(
                dslContext,
                temporaryReservationRepository,
                MockEboardRepository(),
                propertyRepository,
                propertyMaintenanceRepository
            ).update(cancelRequest, stubJwtRequestUser())

            // verify
            val result =
                propertyMaintenanceRepository.listBy(
                    listOf(
                        Property.Id(
                            Building.Code.of(buildingCode),
                            Room.Code.of(roomCode)
                        )
                    )
                )
            assertEquals(PropertyMaintenance.PublishStatus.PUBLIC, result[0].publishStatus)
        }

        @Test
        @DisplayName("公開指示が一度も行われていない物件に対して、仮押さえ解除時に対象の物件を非公開にする")
        fun case6() {
            // setup
            val propertyMaintenanceRepository = PropertyMaintenanceRepository(dslContext)

            TemporaryReservationService(
                dslContext,
                temporaryReservationRepository,
                MockEboardRepository(),
                propertyRepository,
                propertyMaintenanceRepository
            ).update(request, stubJwtRequestUser())

            // execute
            TemporaryReservationService(
                dslContext,
                temporaryReservationRepository,
                MockEboardRepository(),
                propertyRepository,
                propertyMaintenanceRepository
            ).update(cancelRequest, stubJwtRequestUser())

            // verify
            val result =
                propertyMaintenanceRepository.listBy(
                    listOf(
                        Property.Id(
                            Building.Code.of(buildingCode),
                            Room.Code.of(roomCode)
                        )
                    )
                )
            assertEquals(PropertyMaintenance.PublishStatus.PRIVATE, result[0].publishStatus)
        }

        private fun setupPropertyMaintenanceExist(): PropertyMaintenanceRepository {
            // DBにデータをInsertしておく
            dslContext.save(
                table = PROPERTY_MAINTENANCE_INFO,
                recordConstructor = { p: PropertyMaintenanceInfoPojo ->
                    PropertyMaintenanceInfoRecord(p)
                },
                pojos = listOf(
                    stubPropertyMaintenanceInfoPojo(
                        buildingCode = request.getId().buildingCode.value,
                        roomCode = request.getId().roomCode.value,
                        publishStatus = PropertyMaintenance.PublishStatus.PUBLIC,
                        listingCategory = 1
                    )
                )
            )
            return PropertyMaintenanceRepository(dslContext)
        }

        private fun setupPropertyRepository(
            buildingCode: String,
            roomCode: String
        ): MockPropertyRepository {
            return MockPropertyRepository(
                listFunc = { _ ->
                    listOf(
                        stubProperty(
                            buildingCode,
                            roomCode
                        )
                    )
                }
            )
        }
    }
}
