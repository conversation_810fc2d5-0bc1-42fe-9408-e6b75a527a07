package jp.ne.simplex.application.controller.client.exclusive.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.ExclusiveProperty
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException

class ClientExclusivePropertiesDeleteRequest(
    @JsonProperty("idList")
    @field:Schema(description = "先行公開IDリスト", type = "Array", example = "[\"174539560948543005\",\"174539560948543006\"]")
    val idList: List<String>,
) {
    // Service層の Interface に変換する
    fun toServiceInterface(): List<ExclusiveProperty.Id> {
        if (idList.isEmpty()) {
            throw ClientValidationException(ErrorMessage.MISSING_REQUIRED_FIELDS.format("先行公開IDリスト"))
        }

        val deleteActions = try {
            idList.map { id -> ExclusiveProperty.Id.of(id.toLong()) }
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        } catch (_: Exception) {
            throw ClientValidationException(ErrorMessage.INVALID_REQUEST_FORMAT.format())
        }

        if (deleteActions.isEmpty()) {
            throw ClientValidationException(ErrorMessage.INVALID_REQUEST_FORMAT.format())
        }
        return deleteActions
    }
}
