/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 部屋画像 既存システム物理名: ERAHMP
 */
@Suppress("UNCHECKED_CAST")
data class RoomImagePojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateUser: String? = null,
    var propertyBuildingCd: String,
    var propertyRoomCd: String,
    var imageRegistrationCount: Byte? = null,
    var latestImageRegistrationDate: Int? = null,
    var floorPlanImageFileName: String? = null,
    var floorPlanImageRegistrationDate: Int? = null,
    var roomImageFileName_1: String? = null,
    var roomImageType_1: String? = null,
    var imageRegistrationDate_1: Int? = null,
    var roomImageFileName_2: String? = null,
    var roomImageType_2: String? = null,
    var imageRegistrationDate_2: Int? = null,
    var roomImageFileName_3: String? = null,
    var roomImageType_3: String? = null,
    var imageRegistrationDate_3: Int? = null,
    var roomImageFileName_4: String? = null,
    var roomImageType_4: String? = null,
    var imageRegistrationDate_4: Int? = null,
    var roomImageFileName_5: String? = null,
    var roomImageType_5: String? = null,
    var imageRegistrationDate_5: Int? = null,
    var roomImageFileName_6: String? = null,
    var roomImageType_6: String? = null,
    var imageRegistrationDate_6: Int? = null,
    var roomImageFileName_7: String? = null,
    var roomImageType_7: String? = null,
    var imageRegistrationDate_7: Int? = null,
    var roomImageFileName_8: String? = null,
    var roomImageType_8: String? = null,
    var imageRegistrationDate_8: Int? = null,
    var roomImageFileName_9: String? = null,
    var roomImageType_9: String? = null,
    var imageRegistrationDate_9: Int? = null,
    var searchBranchCd: Int? = null,
    var roomImageFileName_10: String? = null,
    var roomImageType_10: String? = null,
    var imageRegistrationDate_10: Int? = null,
    var roomImageFileName_11: String? = null,
    var roomImageType_11: String? = null,
    var imageRegistrationDate_11: Int? = null,
    var roomImageFileName_12: String? = null,
    var roomImageType_12: String? = null,
    var imageRegistrationDate_12: Int? = null,
    var floorPlanImageRegistrationTime: Int? = null,
    var imageRegistrationTime_1: Int? = null,
    var imageRegistrationTime_2: Int? = null,
    var imageRegistrationTime_3: Int? = null,
    var imageRegistrationTime_4: Int? = null,
    var imageRegistrationTime_5: Int? = null,
    var imageRegistrationTime_6: Int? = null,
    var imageRegistrationTime_7: Int? = null,
    var imageRegistrationTime_8: Int? = null,
    var imageRegistrationTime_9: Int? = null,
    var imageRegistrationTime_10: Int? = null,
    var imageRegistrationTime_11: Int? = null,
    var imageRegistrationTime_12: Int? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: RoomImagePojo = other as RoomImagePojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateUser == null) {
            if (o.updateUser != null)
                return false
        }
        else if (this.updateUser != o.updateUser)
            return false
        if (this.propertyBuildingCd != o.propertyBuildingCd)
            return false
        if (this.propertyRoomCd != o.propertyRoomCd)
            return false
        if (this.imageRegistrationCount == null) {
            if (o.imageRegistrationCount != null)
                return false
        }
        else if (this.imageRegistrationCount != o.imageRegistrationCount)
            return false
        if (this.latestImageRegistrationDate == null) {
            if (o.latestImageRegistrationDate != null)
                return false
        }
        else if (this.latestImageRegistrationDate != o.latestImageRegistrationDate)
            return false
        if (this.floorPlanImageFileName == null) {
            if (o.floorPlanImageFileName != null)
                return false
        }
        else if (this.floorPlanImageFileName != o.floorPlanImageFileName)
            return false
        if (this.floorPlanImageRegistrationDate == null) {
            if (o.floorPlanImageRegistrationDate != null)
                return false
        }
        else if (this.floorPlanImageRegistrationDate != o.floorPlanImageRegistrationDate)
            return false
        if (this.roomImageFileName_1 == null) {
            if (o.roomImageFileName_1 != null)
                return false
        }
        else if (this.roomImageFileName_1 != o.roomImageFileName_1)
            return false
        if (this.roomImageType_1 == null) {
            if (o.roomImageType_1 != null)
                return false
        }
        else if (this.roomImageType_1 != o.roomImageType_1)
            return false
        if (this.imageRegistrationDate_1 == null) {
            if (o.imageRegistrationDate_1 != null)
                return false
        }
        else if (this.imageRegistrationDate_1 != o.imageRegistrationDate_1)
            return false
        if (this.roomImageFileName_2 == null) {
            if (o.roomImageFileName_2 != null)
                return false
        }
        else if (this.roomImageFileName_2 != o.roomImageFileName_2)
            return false
        if (this.roomImageType_2 == null) {
            if (o.roomImageType_2 != null)
                return false
        }
        else if (this.roomImageType_2 != o.roomImageType_2)
            return false
        if (this.imageRegistrationDate_2 == null) {
            if (o.imageRegistrationDate_2 != null)
                return false
        }
        else if (this.imageRegistrationDate_2 != o.imageRegistrationDate_2)
            return false
        if (this.roomImageFileName_3 == null) {
            if (o.roomImageFileName_3 != null)
                return false
        }
        else if (this.roomImageFileName_3 != o.roomImageFileName_3)
            return false
        if (this.roomImageType_3 == null) {
            if (o.roomImageType_3 != null)
                return false
        }
        else if (this.roomImageType_3 != o.roomImageType_3)
            return false
        if (this.imageRegistrationDate_3 == null) {
            if (o.imageRegistrationDate_3 != null)
                return false
        }
        else if (this.imageRegistrationDate_3 != o.imageRegistrationDate_3)
            return false
        if (this.roomImageFileName_4 == null) {
            if (o.roomImageFileName_4 != null)
                return false
        }
        else if (this.roomImageFileName_4 != o.roomImageFileName_4)
            return false
        if (this.roomImageType_4 == null) {
            if (o.roomImageType_4 != null)
                return false
        }
        else if (this.roomImageType_4 != o.roomImageType_4)
            return false
        if (this.imageRegistrationDate_4 == null) {
            if (o.imageRegistrationDate_4 != null)
                return false
        }
        else if (this.imageRegistrationDate_4 != o.imageRegistrationDate_4)
            return false
        if (this.roomImageFileName_5 == null) {
            if (o.roomImageFileName_5 != null)
                return false
        }
        else if (this.roomImageFileName_5 != o.roomImageFileName_5)
            return false
        if (this.roomImageType_5 == null) {
            if (o.roomImageType_5 != null)
                return false
        }
        else if (this.roomImageType_5 != o.roomImageType_5)
            return false
        if (this.imageRegistrationDate_5 == null) {
            if (o.imageRegistrationDate_5 != null)
                return false
        }
        else if (this.imageRegistrationDate_5 != o.imageRegistrationDate_5)
            return false
        if (this.roomImageFileName_6 == null) {
            if (o.roomImageFileName_6 != null)
                return false
        }
        else if (this.roomImageFileName_6 != o.roomImageFileName_6)
            return false
        if (this.roomImageType_6 == null) {
            if (o.roomImageType_6 != null)
                return false
        }
        else if (this.roomImageType_6 != o.roomImageType_6)
            return false
        if (this.imageRegistrationDate_6 == null) {
            if (o.imageRegistrationDate_6 != null)
                return false
        }
        else if (this.imageRegistrationDate_6 != o.imageRegistrationDate_6)
            return false
        if (this.roomImageFileName_7 == null) {
            if (o.roomImageFileName_7 != null)
                return false
        }
        else if (this.roomImageFileName_7 != o.roomImageFileName_7)
            return false
        if (this.roomImageType_7 == null) {
            if (o.roomImageType_7 != null)
                return false
        }
        else if (this.roomImageType_7 != o.roomImageType_7)
            return false
        if (this.imageRegistrationDate_7 == null) {
            if (o.imageRegistrationDate_7 != null)
                return false
        }
        else if (this.imageRegistrationDate_7 != o.imageRegistrationDate_7)
            return false
        if (this.roomImageFileName_8 == null) {
            if (o.roomImageFileName_8 != null)
                return false
        }
        else if (this.roomImageFileName_8 != o.roomImageFileName_8)
            return false
        if (this.roomImageType_8 == null) {
            if (o.roomImageType_8 != null)
                return false
        }
        else if (this.roomImageType_8 != o.roomImageType_8)
            return false
        if (this.imageRegistrationDate_8 == null) {
            if (o.imageRegistrationDate_8 != null)
                return false
        }
        else if (this.imageRegistrationDate_8 != o.imageRegistrationDate_8)
            return false
        if (this.roomImageFileName_9 == null) {
            if (o.roomImageFileName_9 != null)
                return false
        }
        else if (this.roomImageFileName_9 != o.roomImageFileName_9)
            return false
        if (this.roomImageType_9 == null) {
            if (o.roomImageType_9 != null)
                return false
        }
        else if (this.roomImageType_9 != o.roomImageType_9)
            return false
        if (this.imageRegistrationDate_9 == null) {
            if (o.imageRegistrationDate_9 != null)
                return false
        }
        else if (this.imageRegistrationDate_9 != o.imageRegistrationDate_9)
            return false
        if (this.searchBranchCd == null) {
            if (o.searchBranchCd != null)
                return false
        }
        else if (this.searchBranchCd != o.searchBranchCd)
            return false
        if (this.roomImageFileName_10 == null) {
            if (o.roomImageFileName_10 != null)
                return false
        }
        else if (this.roomImageFileName_10 != o.roomImageFileName_10)
            return false
        if (this.roomImageType_10 == null) {
            if (o.roomImageType_10 != null)
                return false
        }
        else if (this.roomImageType_10 != o.roomImageType_10)
            return false
        if (this.imageRegistrationDate_10 == null) {
            if (o.imageRegistrationDate_10 != null)
                return false
        }
        else if (this.imageRegistrationDate_10 != o.imageRegistrationDate_10)
            return false
        if (this.roomImageFileName_11 == null) {
            if (o.roomImageFileName_11 != null)
                return false
        }
        else if (this.roomImageFileName_11 != o.roomImageFileName_11)
            return false
        if (this.roomImageType_11 == null) {
            if (o.roomImageType_11 != null)
                return false
        }
        else if (this.roomImageType_11 != o.roomImageType_11)
            return false
        if (this.imageRegistrationDate_11 == null) {
            if (o.imageRegistrationDate_11 != null)
                return false
        }
        else if (this.imageRegistrationDate_11 != o.imageRegistrationDate_11)
            return false
        if (this.roomImageFileName_12 == null) {
            if (o.roomImageFileName_12 != null)
                return false
        }
        else if (this.roomImageFileName_12 != o.roomImageFileName_12)
            return false
        if (this.roomImageType_12 == null) {
            if (o.roomImageType_12 != null)
                return false
        }
        else if (this.roomImageType_12 != o.roomImageType_12)
            return false
        if (this.imageRegistrationDate_12 == null) {
            if (o.imageRegistrationDate_12 != null)
                return false
        }
        else if (this.imageRegistrationDate_12 != o.imageRegistrationDate_12)
            return false
        if (this.floorPlanImageRegistrationTime == null) {
            if (o.floorPlanImageRegistrationTime != null)
                return false
        }
        else if (this.floorPlanImageRegistrationTime != o.floorPlanImageRegistrationTime)
            return false
        if (this.imageRegistrationTime_1 == null) {
            if (o.imageRegistrationTime_1 != null)
                return false
        }
        else if (this.imageRegistrationTime_1 != o.imageRegistrationTime_1)
            return false
        if (this.imageRegistrationTime_2 == null) {
            if (o.imageRegistrationTime_2 != null)
                return false
        }
        else if (this.imageRegistrationTime_2 != o.imageRegistrationTime_2)
            return false
        if (this.imageRegistrationTime_3 == null) {
            if (o.imageRegistrationTime_3 != null)
                return false
        }
        else if (this.imageRegistrationTime_3 != o.imageRegistrationTime_3)
            return false
        if (this.imageRegistrationTime_4 == null) {
            if (o.imageRegistrationTime_4 != null)
                return false
        }
        else if (this.imageRegistrationTime_4 != o.imageRegistrationTime_4)
            return false
        if (this.imageRegistrationTime_5 == null) {
            if (o.imageRegistrationTime_5 != null)
                return false
        }
        else if (this.imageRegistrationTime_5 != o.imageRegistrationTime_5)
            return false
        if (this.imageRegistrationTime_6 == null) {
            if (o.imageRegistrationTime_6 != null)
                return false
        }
        else if (this.imageRegistrationTime_6 != o.imageRegistrationTime_6)
            return false
        if (this.imageRegistrationTime_7 == null) {
            if (o.imageRegistrationTime_7 != null)
                return false
        }
        else if (this.imageRegistrationTime_7 != o.imageRegistrationTime_7)
            return false
        if (this.imageRegistrationTime_8 == null) {
            if (o.imageRegistrationTime_8 != null)
                return false
        }
        else if (this.imageRegistrationTime_8 != o.imageRegistrationTime_8)
            return false
        if (this.imageRegistrationTime_9 == null) {
            if (o.imageRegistrationTime_9 != null)
                return false
        }
        else if (this.imageRegistrationTime_9 != o.imageRegistrationTime_9)
            return false
        if (this.imageRegistrationTime_10 == null) {
            if (o.imageRegistrationTime_10 != null)
                return false
        }
        else if (this.imageRegistrationTime_10 != o.imageRegistrationTime_10)
            return false
        if (this.imageRegistrationTime_11 == null) {
            if (o.imageRegistrationTime_11 != null)
                return false
        }
        else if (this.imageRegistrationTime_11 != o.imageRegistrationTime_11)
            return false
        if (this.imageRegistrationTime_12 == null) {
            if (o.imageRegistrationTime_12 != null)
                return false
        }
        else if (this.imageRegistrationTime_12 != o.imageRegistrationTime_12)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateUser == null) 0 else this.updateUser.hashCode())
        result = prime * result + this.propertyBuildingCd.hashCode()
        result = prime * result + this.propertyRoomCd.hashCode()
        result = prime * result + (if (this.imageRegistrationCount == null) 0 else this.imageRegistrationCount.hashCode())
        result = prime * result + (if (this.latestImageRegistrationDate == null) 0 else this.latestImageRegistrationDate.hashCode())
        result = prime * result + (if (this.floorPlanImageFileName == null) 0 else this.floorPlanImageFileName.hashCode())
        result = prime * result + (if (this.floorPlanImageRegistrationDate == null) 0 else this.floorPlanImageRegistrationDate.hashCode())
        result = prime * result + (if (this.roomImageFileName_1 == null) 0 else this.roomImageFileName_1.hashCode())
        result = prime * result + (if (this.roomImageType_1 == null) 0 else this.roomImageType_1.hashCode())
        result = prime * result + (if (this.imageRegistrationDate_1 == null) 0 else this.imageRegistrationDate_1.hashCode())
        result = prime * result + (if (this.roomImageFileName_2 == null) 0 else this.roomImageFileName_2.hashCode())
        result = prime * result + (if (this.roomImageType_2 == null) 0 else this.roomImageType_2.hashCode())
        result = prime * result + (if (this.imageRegistrationDate_2 == null) 0 else this.imageRegistrationDate_2.hashCode())
        result = prime * result + (if (this.roomImageFileName_3 == null) 0 else this.roomImageFileName_3.hashCode())
        result = prime * result + (if (this.roomImageType_3 == null) 0 else this.roomImageType_3.hashCode())
        result = prime * result + (if (this.imageRegistrationDate_3 == null) 0 else this.imageRegistrationDate_3.hashCode())
        result = prime * result + (if (this.roomImageFileName_4 == null) 0 else this.roomImageFileName_4.hashCode())
        result = prime * result + (if (this.roomImageType_4 == null) 0 else this.roomImageType_4.hashCode())
        result = prime * result + (if (this.imageRegistrationDate_4 == null) 0 else this.imageRegistrationDate_4.hashCode())
        result = prime * result + (if (this.roomImageFileName_5 == null) 0 else this.roomImageFileName_5.hashCode())
        result = prime * result + (if (this.roomImageType_5 == null) 0 else this.roomImageType_5.hashCode())
        result = prime * result + (if (this.imageRegistrationDate_5 == null) 0 else this.imageRegistrationDate_5.hashCode())
        result = prime * result + (if (this.roomImageFileName_6 == null) 0 else this.roomImageFileName_6.hashCode())
        result = prime * result + (if (this.roomImageType_6 == null) 0 else this.roomImageType_6.hashCode())
        result = prime * result + (if (this.imageRegistrationDate_6 == null) 0 else this.imageRegistrationDate_6.hashCode())
        result = prime * result + (if (this.roomImageFileName_7 == null) 0 else this.roomImageFileName_7.hashCode())
        result = prime * result + (if (this.roomImageType_7 == null) 0 else this.roomImageType_7.hashCode())
        result = prime * result + (if (this.imageRegistrationDate_7 == null) 0 else this.imageRegistrationDate_7.hashCode())
        result = prime * result + (if (this.roomImageFileName_8 == null) 0 else this.roomImageFileName_8.hashCode())
        result = prime * result + (if (this.roomImageType_8 == null) 0 else this.roomImageType_8.hashCode())
        result = prime * result + (if (this.imageRegistrationDate_8 == null) 0 else this.imageRegistrationDate_8.hashCode())
        result = prime * result + (if (this.roomImageFileName_9 == null) 0 else this.roomImageFileName_9.hashCode())
        result = prime * result + (if (this.roomImageType_9 == null) 0 else this.roomImageType_9.hashCode())
        result = prime * result + (if (this.imageRegistrationDate_9 == null) 0 else this.imageRegistrationDate_9.hashCode())
        result = prime * result + (if (this.searchBranchCd == null) 0 else this.searchBranchCd.hashCode())
        result = prime * result + (if (this.roomImageFileName_10 == null) 0 else this.roomImageFileName_10.hashCode())
        result = prime * result + (if (this.roomImageType_10 == null) 0 else this.roomImageType_10.hashCode())
        result = prime * result + (if (this.imageRegistrationDate_10 == null) 0 else this.imageRegistrationDate_10.hashCode())
        result = prime * result + (if (this.roomImageFileName_11 == null) 0 else this.roomImageFileName_11.hashCode())
        result = prime * result + (if (this.roomImageType_11 == null) 0 else this.roomImageType_11.hashCode())
        result = prime * result + (if (this.imageRegistrationDate_11 == null) 0 else this.imageRegistrationDate_11.hashCode())
        result = prime * result + (if (this.roomImageFileName_12 == null) 0 else this.roomImageFileName_12.hashCode())
        result = prime * result + (if (this.roomImageType_12 == null) 0 else this.roomImageType_12.hashCode())
        result = prime * result + (if (this.imageRegistrationDate_12 == null) 0 else this.imageRegistrationDate_12.hashCode())
        result = prime * result + (if (this.floorPlanImageRegistrationTime == null) 0 else this.floorPlanImageRegistrationTime.hashCode())
        result = prime * result + (if (this.imageRegistrationTime_1 == null) 0 else this.imageRegistrationTime_1.hashCode())
        result = prime * result + (if (this.imageRegistrationTime_2 == null) 0 else this.imageRegistrationTime_2.hashCode())
        result = prime * result + (if (this.imageRegistrationTime_3 == null) 0 else this.imageRegistrationTime_3.hashCode())
        result = prime * result + (if (this.imageRegistrationTime_4 == null) 0 else this.imageRegistrationTime_4.hashCode())
        result = prime * result + (if (this.imageRegistrationTime_5 == null) 0 else this.imageRegistrationTime_5.hashCode())
        result = prime * result + (if (this.imageRegistrationTime_6 == null) 0 else this.imageRegistrationTime_6.hashCode())
        result = prime * result + (if (this.imageRegistrationTime_7 == null) 0 else this.imageRegistrationTime_7.hashCode())
        result = prime * result + (if (this.imageRegistrationTime_8 == null) 0 else this.imageRegistrationTime_8.hashCode())
        result = prime * result + (if (this.imageRegistrationTime_9 == null) 0 else this.imageRegistrationTime_9.hashCode())
        result = prime * result + (if (this.imageRegistrationTime_10 == null) 0 else this.imageRegistrationTime_10.hashCode())
        result = prime * result + (if (this.imageRegistrationTime_11 == null) 0 else this.imageRegistrationTime_11.hashCode())
        result = prime * result + (if (this.imageRegistrationTime_12 == null) 0 else this.imageRegistrationTime_12.hashCode())
        return result
    }
}
