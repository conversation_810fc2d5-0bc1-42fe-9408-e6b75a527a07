package jp.ne.simplex.application.repository.external.dkportal.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.UpdatePropertyMaintenance
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalOperationName
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalRequest
import jp.ne.simplex.exception.ErrorType

class DKPortalUpdateRoomStatusRequest private constructor(
    @field:JsonProperty("property_type")
    val propertyType: Int,

    @field:JsonProperty("kentaku_building_code")
    val kentakuBuildingCode: String,

    @field:JsonProperty("kentaku_room_code")
    val kentakuRoomCode: String,

    @field:JsonProperty("is_publish")
    val isPublish: Int,

    @field:JsonProperty("up_state")
    val upState: Int,
) : DKPortalRequest {
    override fun getDKPortalOperationName(): DKPortalOperationName {
        return DKPortalOperationName.UPDATE_ROOM_STATUS
    }
    
    companion object {

        fun of(
            updatePublishStatus: UpdatePropertyMaintenance.PublishStatusWithUpState,
        ): DKPortalUpdateRoomStatusRequest {
            return DKPortalUpdateRoomStatusRequest(
                propertyType = when (updatePublishStatus.propertyType) {
                    Property.Type.RESIDENTIAL -> 1
                    Property.Type.COMMERCIAL -> 2
                },
                kentakuBuildingCode = updatePublishStatus.id.buildingCode.value,
                kentakuRoomCode = updatePublishStatus.id.roomCode.value,
                isPublish = updatePublishStatus.publishStatus.value,
                upState = when (updatePublishStatus.propertyUpState) {
                    Property.UpState.PREPARING -> 1
                    Property.UpState.RECRUITING -> 2
                    Property.UpState.TEMPORARY_RESERVED -> 3
                    Property.UpState.ALREADY_MOVED_IN -> 4
                },
            )
        }
    }
}
