package jp.ne.simplex.application.controller.client.property.dto

import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.RegisterGarbageImage
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException
import org.springframework.web.bind.annotation.RequestPart
import org.springframework.web.multipart.MultipartFile

class ClientGarbageImageRegisterRequest(
    @RequestPart("buildingCd")
    @field:Schema(description = "建物コード", example = "000130301")
    val buildingCd: String,

    @RequestPart("image")
    @field:Schema(description = "ゴミ置場画像登録")
    val image: MultipartFile,
) {
    fun toServiceInterface(): RegisterGarbageImage {
        try {
            val buildingCode = Building.Code.of(buildingCd)
            return RegisterGarbageImage.of(
                buildingCode = buildingCode,
                image = image,
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }
}
