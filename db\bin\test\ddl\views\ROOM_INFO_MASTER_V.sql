-- VIEW: ROOM_INFO_MASTER_V(部屋情報マスタビュー)

CREATE VIEW ROOM_INFO_MASTER_V AS
SELECT
     RIM.PROPERTY_CD_PART1
,    RIM.PROPERTY_BUILDING_CD
,    LEFT(RIM.PROPERTY_BUILDING_CD, 7) AS ORDER_CD
,    RIM.PROPERTY_CD_PART2
,    RIM.PROPERTY_ROOM_CD
,    RIM.DELETE_FLAG
,    RIM.CUSTOMER_COMPANY_CD
,    RIM.CUSTOMER_BRANCH_CD
,    RIM.CUSTOMER_DEPARTMENT_CD
,    RIM.CUSTOMER_COMPLETION_FLAG
,    RIM.PREFECTURE_CD
,    RIM.CITY_CD
,    RIM.LINE_CD
,    RIM.STATION_CD
,    RIM.RENT
,    RIM.LAYOUT_ROOM_COUNT
,    RIM.EXCLUSIVE_AREA
,    RIM.PROPERTY_TYPE
,    RIM.PREFERENCE_1
,    <PERSON><PERSON>.PREFERENCE_2
,    R<PERSON>.PREFERENCE_3
,    R<PERSON>.PREFERENCE_4
,    R<PERSON>.PREFERENCE_5
,    <PERSON><PERSON>.PREFERENCE_6
,    R<PERSON>.PREFERENCE_7
,    R<PERSON>.PREFERENCE_8
,    R<PERSON>.PREFERENCE_9
,    RIM.PREFERENCE_10
,    RIM.PREFERENCE_11
,    RIM.PREFERENCE_12
,    RIM.PREFERENCE_13
,    RIM.PREFERENCE_14
,    RIM.PREFERENCE_15
,    RIM.PREFERENCE_16
,    RIM.PREFERENCE_17
,    RIM.PREFERENCE_18
,    RIM.PREFERENCE_19
,    RIM.PREFERENCE_20
,    RIM.PREFERENCE_21
,    RIM.PREFERENCE_22
,    RIM.PREFERENCE_23
,    RIM.PREFERENCE_24
,    RIM.PREFERENCE_25
,    RIM.PREFERENCE_26
,    RIM.PREFERENCE_27
,    RIM.PREFERENCE_28
,    RIM.PREFERENCE_29
,    RIM.PREFERENCE_30
,    RIM.PREFERENCE_31
,    RIM.PREFERENCE_32
,    RIM.PREFERENCE_33
,    RIM.PREFERENCE_34
,    RIM.PREFERENCE_35
,    RIM.PREFERENCE_36
,    RIM.PREFERENCE_37
,    RIM.PREFERENCE_38
,    RIM.PREFERENCE_39
,    RIM.PREFERENCE_40
,    RIM.PREFERENCE_41
,    RIM.PREFERENCE_42
,    RIM.PREFERENCE_43
,    RIM.PREFERENCE_44
,    RIM.PREFERENCE_45
,    RIM.PREFERENCE_46
,    RIM.PREFERENCE_47
,    RIM.PREFERENCE_48
,    RIM.PREFERENCE_49
,    RIM.PREFERENCE_50
,    RIM.PREFERENCE_51
,    RIM.PREFERENCE_52
,    RIM.PREFERENCE_53
,    RIM.PREFERENCE_54
,    RIM.PREFERENCE_55
,    RIM.PREFERENCE_56
,    RIM.PREFERENCE_57
,    RIM.PREFERENCE_58
,    RIM.PREFERENCE_59
,    RIM.PREFERENCE_60
,    RIM.PREFERENCE_99
,    RIM.PREFERENCE_NEW_BUILD
,    RIM.PREFERENCE_CORNER_ROOM
,    RIM.PREFERENCE_ABOVE_2ND_FLOOR
,    RIM.LINE_NAME
,    RIM.STATION_NAME
,    RIM.BUS_STOP_NAME
,    RIM.BUS_TIME
,    RIM.WALKING_TIME
,    RIM.DISTANCE
,    RIM.KEY_MONEY
,    RIM.DEPOSIT
,    NULLIF(TRIM(trailing TRIM(trailing RIM.DEPOSIT, '円'),'ヶ月'), '-')::numeric AS DEPOSIT_COMPUTED
,    RIM.NEIGHBORHOOD_ASSOCIATION_FEE
,    NULLIF(LEFT(RIM.NEIGHBORHOOD_ASSOCIATION_FEE, -1), '')::numeric AS NEIGHBORHOOD_ASSOCIATION_FEE_COMPUTED
,    RIM.COMMON_SERVICE_FEE
,    NULLIF(LEFT(RIM.COMMON_SERVICE_FEE, -1), '')::numeric AS COMMON_SERVICE_FEE_COMPUTED
,    RIM.ROOM_TYPE_NAME
,    RIM.LAYOUT_TYPE
,    RIM.LAYOUT
,    RIM.LAYOUT_DETAILS
,    RIM.PARKING_TYPE
,    RIM.PARKING_FEE
,    RIM.CONSTRUCTION_YEAR_MONTH
,    RIM.HANDLING_STORE_COMPANY
,    RIM.LOCATION_LISTING_AREA
,    RIM.FLOOR_NUMBER
,    RIM.DIRECTION
,    RIM.ROOM_POSITION
,    RIM.AVAILABLE_MOVE_IN_YEAR_MONTH
,    RIM.TRANSPORTATION
,    RIM.EQUIPMENT
,    RIM.NOTES
,    RIM.INQUIRY_BRANCH_NAME
,    RIM.BRANCH_PHONE_NUMBER
,    RIM.BRANCH_FAX_NUMBER
,    RIM.TRANSACTION_TYPE
,    RIM.BUILDING_NAME
,    RIM.STRUCTURE_NAME
,    RIM.AGENT_ASSIGNABLE_TYPE
,    RIM.SUBLEASE_TYPE
,    RIM.CREATION_DATE
,    RIM.CREATION_TIME
,    RIM.CREATOR
,    RIM.UPDATE_DATE
,    RIM.UPDATE_TIME
,    RIM.UPDATER
,    RIM.BRANCH_ADDRESS
,    RIM.RECOMMENDATION_COMMENT
,    RIM.COMPLETION_YEAR_MONTH
,    RIM.PROPERTY_POSTAL_CODE
,    NULLIF(RIM.VACATE_NOTICE_DATE, 0) AS VACATE_NOTICE_DATE
,    NULLIF(RIM.EXPECTED_MOVE_OUT_DATE, 0) AS EXPECTED_MOVE_OUT_DATE
,    NULLIF(RIM.MOVE_OUT_DATE, 0) AS MOVE_OUT_DATE
,    RIM.EXPECTED_COMPLETION_DATE
,    NULLIF(RIM.AVAILABLE_MOVE_IN_DATE, 0) AS AVAILABLE_MOVE_IN_DATE
,    NULLIF(RIM.MOVE_IN_APPLICATION_DATE, 0) AS MOVE_IN_APPLICATION_DATE
,    RIM.DEPOSIT_DATE
,    RIM.BALANCE_COLLECTION_DATE
,    NULLIF(RIM.MOVE_IN_DATE, 0) AS MOVE_IN_DATE
,    RIM.COMPLETION_DATE
,    RIM.TENANT_RECRUITMENT_COLLECTION_DATE
,    RIM.TENANT
,    RIM.OWNER
,    RIM.CUSTOMER_AGENT_BRANCH_CD
,    RIM.CUSTOMER_AGENT_DEPARTMENT_CD
,    RIM.CUSTOMER_AGENT_EMPLOYEE_CD
,    RIM.RENT_TAX
,    RIM.KEY_MONEY_TAX
,    RIM.KEY_MONEY_TOTAL
,    RIM.COMMON_SERVICE_FEE_TAX
,    RIM.PARKING_FEE_TAX
,    LPAD(RIM.ROOM_NUMBER, 4, '0') AS ROOM_NUMBER
,    RIM.NEW_EXISTING_FLAG
,    RIM.ROOM_STATUS_TYPE
,    RIM.RECORD_STATUS_TYPE
,    RIM.FF_USAGE_PERIOD
,    RIM.AD_PAYABLE_AMOUNT
,    RIM.LOCATION_CITY
,    RIM.TENANT_CONTRACT_NUMBER
,    RIM.OWNER_CONTACT
,    RIM.VACANT_PERIOD
,    RIM.FLOOR_AREA_1F
,    RIM.FLOOR_AREA_2F
,    RIM.FLOOR_AREA_3F
,    RIM.RECRUITMENT_CREATION_DATE
,    RIM.RECRUITMENT_APPROVAL_DATE
,    RIM.MOVE_OUT_INSPECTION_DATE
,    RIM.RESTORATION_DATE
,    RIM.RESTORATION_COMPLETION_DATE
,    RIM.VACANT_BOOKING_DATE
,    NULLIF(RIM.VACANT_BOOKING_COMPLETION_DATE, 0) AS VACANT_BOOKING_COMPLETION_DATE
,    RIM.ADDITIONAL_KEY_MONEY
,    RIM.MUTUAL_AID_JOIN_SIGN
,    RIM.RENTAL_TYPE
,    RIM.SPECIAL_RENTAL_TYPE
,    RIM.DISTANCE2
,    RIM.APPROVAL_TYPE
,    RIM.RECORD_SEPARATOR
,    RIM.CHANGE_TYPE
,    RIM.NO_DEPOSIT_FLAG
,    RIM.CAMPAIGN_TARGET_FLAG
,    RIM.PREFERENCE_61
,    RIM.PREFERENCE_62
,    RIM.PREFERENCE_63
,    RIM.PREFERENCE_64
,    RIM.PREFERENCE_65
,    RIM.PREFERENCE_66
,    RIM.PREFERENCE_67
,    RIM.PREFERENCE_68
,    RIM.PREFERENCE_69
,    RIM.PREFERENCE_70
,    RIM.PREFERENCE_71
,    RIM.PREFERENCE_72
,    RIM.PREFERENCE_73
,    RIM.PREFERENCE_74
,    RIM.PREFERENCE_75
,    RIM.PREFERENCE_76
,    RIM.PREFERENCE_77
,    RIM.PREFERENCE_78
,    RIM.PREFERENCE_79
,    RIM.PREFERENCE_80
,    RIM.PREFERENCE_81
,    RIM.PREFERENCE_82
,    RIM.PREFERENCE_83
,    RIM.PREFERENCE_84
,    RIM.PREFERENCE_85
,    RIM.PREFERENCE_86
,    RIM.PREFERENCE_87
,    RIM.PREFERENCE_88
,    RIM.PREFERENCE_89
,    RIM.PREFERENCE_90
,    RIM.PREFERENCE_91
,    RIM.PREFERENCE_92
,    RIM.PREFERENCE_93
,    RIM.PREFERENCE_94
,    RIM.PREFERENCE_95
,    RIM.PREFERENCE_96
,    RIM.PREFERENCE_97
,    RIM.PREFERENCE_98
,    RIM.PROPERTY_ADDRESS
,    RIM.PROPERTY_ADDRESS_DETAIL
,    RIM.SERVICE_ROOM_SIGN
,    RIM.HIGH_VOLTAGE_BULK_RECEIPT
,    RIM.HIGH_RENTAL_SIGN
,    RIM.SOLAR_DISCOUNT_TARGET
,    RIM.CLEANING_COST_FIXED
,    RIM.PREVIOUS_RENT
,    RIM.EXISTING_REVIEW_UPDATE_DATE
,    RIM.MOVE_OUT_INSPECTION_TIME
,    RIM.RECRUITMENT_START_DATE
,    RIM.CLEANING_COST_TOTAL
,    RIM.DISCOUNT_INITIAL_VALUE_SIGN
,    RIM.FLAG_RESERVE_7
,    RIM.PET_FLAG
,    RIM.FLAG_RESERVE_9
,    RIM.FLAG_RESERVE_10
,    NULLIF(RIM.CHALLENGE_START_DATE, 0) AS CHALLENGE_START_DATE
,    NULLIF(RIM.CHALLENGE_END_DATE, 0) AS CHALLENGE_END_DATE
,    RIM.APPLICATION_END_DATE
,    RIM.MOVE_IN_END_DATE
,    RIM.ADDITIONAL_RELEASE_DATE
,    RIM.RECRUITMENT_RENT
,    RIM.CHALLENGE_ADDITIONAL_AMOUNT
,    RIM.REVIEW_RENT
,    RIM.DATE_RESERVE_11
,    RIM.DATE_RESERVE_12
,    RIM.DATE_RESERVE_13
,    RIM.AMOUNT_RESERVE_1
,    RIM.AMOUNT_RESERVE_2
,    RIM.AMOUNT_RESERVE_3
,    RIM.COMMON_SERVICE_FEE_BASE
,    RIM.GENERAL_CABLE_TV_BASE
,    RIM.GENERAL_CABLE_TV_TAX
,    RIM.GENERAL_INTERNET_BASE
,    RIM.GENERAL_INTERNET_TAX
,    RIM.GENERAL_WATER_QUALITY_BASE
,    RIM.GENERAL_WATER_QUALITY_TAX
,    RIM.GENERAL_TENANT_WATER_BASE
,    RIM.GENERAL_TENANT_WATER_TAX
,    RIM.GENERAL_DRAIN_USE_BASE
,    RIM.GENERAL_DRAIN_USE_TAX
,    RIM.GENERAL_GARBAGE_COLLECTION_BASE
,    RIM.GENERAL_GARBAGE_COLLECTION_TAX
,    RIM.GENERAL_SHARED_ANTENNA_BASE
,    RIM.GENERAL_SHARED_ANTENNA_TAX
,    RIM.GENERAL_OWNER_CLEANING_BASE
,    RIM.GENERAL_OWNER_CLEANING_TAX
,    RIM.GENERAL_BUILDING_MAINTENANCE_BASE
,    RIM.GENERAL_BUILDING_MAINTENANCE_TAX
,    RIM.GENERAL_BUILDING_MANAGEMENT_BASE
,    RIM.GENERAL_BUILDING_MANAGEMENT_TAX
,    RIM.GENERAL_NEIGHBORHOOD_ASSOC_BASE
,    RIM.GENERAL_NEIGHBORHOOD_ASSOC_TAX
,    RIM.GENERAL_NEIGHBORHOOD_OTHER_BASE
,    RIM.GENERAL_NEIGHBORHOOD_OTHER_TAX
,    RIM.GENERAL_REPAYMENT_AGENT_BASE
,    RIM.GENERAL_REPAYMENT_AGENT_TAX
,    RIM.GENERAL_HL_COMMISSION_BASE
,    RIM.GENERAL_HL_COMMISSION_TAX
,    RIM.GENERAL_FURNISHED_BASE
,    RIM.GENERAL_FURNISHED_TAX
,    RIM.GENERAL_TENANT_DEPOSIT_BASE
,    RIM.GENERAL_TENANT_DEPOSIT_TAX
,    RIM.GENERAL_RENTAL_BASE
,    RIM.GENERAL_RENTAL_TAX
,    RIM.RESERVE_AMOUNT_1_BASE
,    RIM.RESERVE_AMOUNT_1_TAX
,    RIM.RESERVE_AMOUNT_2_BASE
,    RIM.RESERVE_AMOUNT_2_TAX
,    RIM.RESERVE_AMOUNT_3_BASE
,    RIM.RESERVE_AMOUNT_3_TAX
,    RIM.FLAG_RESERVE_11
,    RIM.FLAG_RESERVE_12
,    RIM.BUNDLE_WATER
,    RIM.BUNDLE_ELECTRICITY
,    RIM.BUNDLE_GAS
,    RIM.CATEGORY_2DIGIT_RESERVE_1
,    RIM.CATEGORY_2DIGIT_RESERVE_2
,    RIM.CATEGORY_2DIGIT_RESERVE_3
,    RIM.CATEGORY_2DIGIT_RESERVE_4
,    RIM.CATEGORY_2DIGIT_RESERVE_5
,    RIM.AMOUNT_RESERVE_4
,    RIM.AMOUNT_RESERVE_5
,    RIM.AMOUNT_RESERVE_6
,    RIM.AMOUNT_RESERVE_7
,    RIM.AMOUNT_RESERVE_8
,    RIM.DATE_RESERVE_14
,    RIM.DATE_RESERVE_15
,    RIM.DATE_RESERVE_16
,    RIM.DATE_RESERVE_17
,    RIM.DATE_RESERVE_18
,    RIM.CATEGORY_1DIGIT_RESERVE_1
,    RIM.CATEGORY_1DIGIT_RESERVE_2
,    RIM.CATEGORY_1DIGIT_RESERVE_3
,    RIM.CATEGORY_1DIGIT_RESERVE_4
,    RIM.CATEGORY_1DIGIT_RESERVE_5
,    RIM.LEASING_STORE_CD
,    RIM.MANAGEMENT_BRANCH_CD
,    RIM.SALES_OFFICE_CD
,    RIM.SCREENING_BRANCH_CD
,    RIM.PREFERENCE_100
,    RIM.PREFERENCE_101
,    RIM.PREFERENCE_102
,    RIM.PREFERENCE_103
,    RIM.PREFERENCE_104
,    RIM.PREFERENCE_105
,    RIM.PREFERENCE_106
,    RIM.PREFERENCE_107
,    RIM.PREFERENCE_108
,    RIM.PREFERENCE_109
,    RIM.PREFERENCE_110
,    RIM.PREFERENCE_111
,    RIM.PREFERENCE_112
,    RIM.PREFERENCE_113
,    RIM.PREFERENCE_114
,    RIM.PREFERENCE_115
,    RIM.PREFERENCE_116
,    RIM.PREFERENCE_117
,    RIM.PREFERENCE_118
,    RIM.PREFERENCE_119
,    RIM.PREFERENCE_120
,    RIM.PREFERENCE_121
,    RIM.PREFERENCE_122
,    RIM.PREFERENCE_123
,    RIM.PREFERENCE_124
,    RIM.PREFERENCE_125
,    RIM.PREFERENCE_126
,    RIM.PREFERENCE_127
,    RIM.PREFERENCE_128
,    RIM.PREFERENCE_129
,    RIM.PREFERENCE_130
,    RIM.PREFERENCE_131
,    RIM.PREFERENCE_132
,    RIM.PREFERENCE_133
,    RIM.PREFERENCE_134
,    RIM.PREFERENCE_135
,    RIM.PREFERENCE_136
,    RIM.PREFERENCE_137
,    RIM.PREFERENCE_138
,    RIM.PREFERENCE_139
,    RIM.PREFERENCE_140
,    RIM.PREFERENCE_141
,    RIM.PREFERENCE_142
,    RIM.PREFERENCE_143
,    RIM.PREFERENCE_144
,    RIM.PREFERENCE_145
,    RIM.PREFERENCE_146
,    RIM.PREFERENCE_147
,    RIM.PREFERENCE_148
,    RIM.PREFERENCE_149
,    RIM.PREFERENCE_150
,    RIM.PREFERENCE_151
,    RIM.PREFERENCE_152
,    RIM.PREFERENCE_153
,    RIM.PREFERENCE_154
,    RIM.PREFERENCE_155
,    RIM.PREFERENCE_156
,    RIM.PREFERENCE_157
,    RIM.PREFERENCE_158
,    RIM.PREFERENCE_159
,    RIM.PREFERENCE_160
,    RIM.PREFERENCE_161
,    RIM.PREFERENCE_162
,    RIM.PREFERENCE_163
,    RIM.PREFERENCE_164
,    RIM.PREFERENCE_165
,    RIM.PREFERENCE_166
,    RIM.PREFERENCE_167
,    RIM.PREFERENCE_168
,    RIM.PREFERENCE_169
,    RIM.PREFERENCE_170
,    RIM.PREFERENCE_171
,    RIM.PREFERENCE_172
,    RIM.PREFERENCE_173
,    RIM.PREFERENCE_174
,    RIM.PREFERENCE_175
,    RIM.PREFERENCE_176
,    RIM.PREFERENCE_177
,    RIM.PREFERENCE_178
,    RIM.PREFERENCE_179
,    RIM.PREFERENCE_180
,    RIM.PREFERENCE_181
,    RIM.PREFERENCE_182
,    RIM.PREFERENCE_183
,    RIM.PREFERENCE_184
,    RIM.PREFERENCE_185
,    RIM.PREFERENCE_186
,    RIM.PREFERENCE_187
,    RIM.PREFERENCE_188
,    RIM.PREFERENCE_189
,    RIM.PREFERENCE_190
,    RIM.PREFERENCE_191
,    RIM.PREFERENCE_192
,    RIM.PREFERENCE_193
,    RIM.PREFERENCE_194
,    RIM.PREFERENCE_195
,    RIM.PREFERENCE_196
,    RIM.PREFERENCE_197
,    RIM.PREFERENCE_198
,    RIM.PREFERENCE_199
,    RIM.MARKETING_BRANCH_OFFICE_CD
,    (CASE
       WHEN RIM.RECORD_STATUS_TYPE IN ('20', '30', '40', '50') THEN '140' /* 申込済み */
       WHEN NULLIF(TRF.APPLICATION_SCHEDULED_DATE, '') IS NOT NULL THEN
         CASE WHEN NULLIF(RIM.MOVE_IN_APPLICATION_DATE, 0) IS NULL
           THEN '130' /* 仮押さえ中 */
           ELSE '140' /* 申込済み */
         END
       WHEN RIM.TENANT_RECRUITMENT_COLLECTION_DATE IS NOT NULL AND RIM.TENANT_RECRUITMENT_COLLECTION_DATE <> 0 THEN '120' /* 募集中 */
       WHEN RIM.EXISTING_REVIEW_UPDATE_DATE IS NOT NULL AND RIM.VACATE_NOTICE_DATE IS NOT NULL AND RIM.EXISTING_REVIEW_UPDATE_DATE < RIM.VACATE_NOTICE_DATE THEN
         CASE
           WHEN RIM.RECRUITMENT_START_DATE IS NOT NULL AND RIM.RECRUITMENT_START_DATE <> 0 AND RIM.RECRUITMENT_RENT IS NOT NULL AND RIM.RECRUITMENT_RENT <> 0 THEN '100' /* 既存審査前 */
           WHEN RIM.RECRUITMENT_START_DATE IS NOT NULL AND RIM.RECRUITMENT_START_DATE = 0 AND RIM.RECRUITMENT_RENT IS NOT NULL AND RIM.RECRUITMENT_RENT = 0 THEN '100' /* 既存審査前 */
           ELSE '120' /* 募集中 */
         END
       WHEN RIM.EXISTING_REVIEW_UPDATE_DATE IS NOT NULL THEN
         CASE
           WHEN RIM.RECRUITMENT_START_DATE IS NOT NULL AND RIM.RECRUITMENT_START_DATE <> 0 AND RIM.RECRUITMENT_RENT IS NOT NULL AND RIM.RECRUITMENT_RENT <> 0 THEN '110' /* 斡旋回収前 */
           WHEN RIM.RECRUITMENT_START_DATE IS NOT NULL AND RIM.RECRUITMENT_START_DATE = 0 AND RIM.RECRUITMENT_RENT IS NOT NULL AND RIM.RECRUITMENT_RENT = 0 THEN '110' /* 斡旋回収前 */
           ELSE '120' /* 募集中 */
         END
       ELSE '120' /* 募集中 */
     END)::varchar(9) AS PROPERTY_STATUS
,    RIM.PROPERTY_SITUATION
,    (CASE
       WHEN RIM.PROPERTY_TYPE = '30' THEN BM.BUILDING_TYPE_CODE::NUMERIC /* 事業用 */
       ELSE RIM.PROPERTY_TYPE::NUMERIC /* 居住用 */
     END)::NUMERIC AS PROPERTY_BUILDING_TYPE
,    (CASE
       WHEN BM.HOUSING_LOAN_CORPORATION_LOAN_FLAG = 1 THEN NULL /* 住宅金融公庫融資サインが '1' の場合 */
       WHEN RIM.SUBLEASE_TYPE in ('0', '5') THEN
         CASE
           WHEN MOUCFAB.STATUS = 1 THEN
             CASE
               WHEN MOUCFAB.RENEWAL_FEE_CATEGORY = 1 THEN '1'       /* 管理のみ更新契約手数料承諾建物.更新手数料区分が'1' の場合は'1.1万円' */
               WHEN MOUCFAB.RENEWAL_FEE_CATEGORY in (2, 3) THEN '2' /* 管理のみ更新契約手数料承諾建物.更新手数料区分が'2', '3' の場合は'2.2万円' */
               ELSE NULL /* それ以外の場合 */
             END
           ELSE NULL /* それ以外の場合 */
         END
       WHEN RCFAB.STATUS = 1 THEN
         CASE
           WHEN RCFAB.RENEWAL_FEE_CATEGORY = 1 THEN '1'       /* 更新契約手数料承諾建物.更新手数料区分が'1' の場合は'1.1万円' */
           WHEN RCFAB.RENEWAL_FEE_CATEGORY in (2, 3) THEN '2' /* 更新契約手数料承諾建物.更新手数料区分が'2', '3' の場合は'2.2万円' */
           ELSE NULL /* それ以外の場合 */
         END
       ELSE NULL /* それ以外の場合 */
     END)::VARCHAR(3) AS RENEWAL_FEE_CATEGORY
,    RIM.PREFECTURE_CITY_CD
,    NULLIF(TRF.REGISTRATION_DATE, '')::NUMERIC AS REGISTRATION_DATE
,    CASE WHEN OTHER_COMPANY_FLAG = '1' THEN TRF.CONTRACT_FORM_E_CODE ELSE EM.NAME_KANJI END AS APPLICATION_SCHEDULED_PERSON
,    PMI.RENTAL_PRICE AS PRIORITY_RENT
,    PMI.KEY_MONEY AS PRIORITY_KEY_MONEY
,    PMI.SECURITY_DEPOSIT AS PRIORITY_DEPOSIT
,    (CASE
        WHEN RIM.CAMPAIGN_TARGET_FLAG in ('1', '3') THEN '09'
        WHEN RIM.CAMPAIGN_TARGET_FLAG in ('6', '7') THEN
            CASE RIM.DISCOUNT_INITIAL_VALUE_SIGN
                WHEN NULL THEN '00'
                WHEN 1 THEN '02'
                WHEN 2 THEN '01'
                WHEN 9 THEN '00'
                ELSE ('0' || RIM.DISCOUNT_INITIAL_VALUE_SIGN)
            END
        WHEN RIM.CAMPAIGN_TARGET_FLAG = '8' THEN
         CASE
            WHEN RIM.CHALLENGE_START_DATE = 0 AND CHALLENGE_END_DATE = 0 THEN '10'
            WHEN RIM.CHALLENGE_START_DATE != 0 AND CHALLENGE_END_DATE != 0 AND TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD')::NUMERIC <= CHALLENGE_END_DATE THEN '10'
         ELSE NULL
         END
        ELSE NULL
      END)::varchar(2) AS CAMPAIGN_TARGET_FLAG_COMPUTED
,    CASE RIM.CAMPAIGN_TARGET_FLAG WHEN 8 THEN '回収前' ELSE NULL END AS CAMPAIGN_TARGET_FLAG_EIGHT
,    NULLIF(CASE RIM.NEW_EXISTING_FLAG
            WHEN '1' THEN RIM.CHALLENGE_END_DATE
            ELSE RIM.ADDITIONAL_RELEASE_DATE
            END, 0) AS CHALLENGE_END_OR_RELEASE_DATE
,    CASE RIM.MOVE_IN_APPLICATION_DATE WHEN 0 THEN NULLIF(TRF.APPLICATION_SCHEDULED_DATE, '')::NUMERIC ELSE NULLIF(RIM.MOVE_IN_APPLICATION_DATE, 0) END AS MOVE_IN_APPLICATION_OR_SCHEDULED_DATE
,    BEL.CERTIFICATION_DATE
,    CASE
        WHEN BEL.CERTIFICATION_DATE IS NULL THEN '-1'
        WHEN BEL.CERTIFICATION_DATE <= TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD')::NUMERIC THEN '1'
        ELSE '0'
     END AS CERTIFICATION_FLAG
,    SBM.IDENTIFICATION_CATEGORY
,    EP.EXCLUSIVE_IN_PROGRESS
FROM ROOM_INFO_MASTER AS RIM
LEFT JOIN TEMPORARY_RESERVATION_FILE AS TRF ON RIM.PROPERTY_BUILDING_CD = TRF.BUILDING_CD AND RIM.PROPERTY_ROOM_CD = TRF.ROOM_CD
LEFT JOIN EMPLOYEE_MASTER AS EM ON TRF.APPLICATION_SCHEDULED_PERSON_CD = EM.EMPLOYEE_NUMBER
LEFT JOIN PROPERTY_MAINTENANCE_INFO AS PMI ON RIM.PROPERTY_BUILDING_CD = PMI.BUILDING_CD AND RIM.PROPERTY_ROOM_CD = PMI.ROOM_CD
LEFT JOIN (
    SELECT PROPERTY_BUILDING_CD, MAX(CERTIFICATION_DATE) AS CERTIFICATION_DATE
    FROM BELS_APPLICATION_RESULT_PROGRESS_FILE
    WHERE (DELETE_FLAG <> '1' OR DELETE_FLAG IS NULL)
    GROUP BY PROPERTY_BUILDING_CD
) AS BEL
    ON RIM.PREFERENCE_16 = 1
    AND BEL.PROPERTY_BUILDING_CD = RIM.PROPERTY_BUILDING_CD
LEFT JOIN (
    SELECT BUILDING_CODE, BUILDING_TYPE_CODE, HOUSING_LOAN_CORPORATION_LOAN_FLAG
    FROM BUILDING_MASTER
) AS BM
    ON BM.BUILDING_CODE = RIM.PROPERTY_BUILDING_CD
LEFT JOIN (
    SELECT BUILDING_CD, STATUS, RENEWAL_FEE_CATEGORY
    FROM MNG_ONLY_UPD_CONT_FEE_APPROVAL_BUILDING
) AS MOUCFAB
    ON MOUCFAB.BUILDING_CD = RIM.PROPERTY_BUILDING_CD
LEFT JOIN (
    SELECT BUILDING_CD, STATUS, RENEWAL_FEE_CATEGORY
    FROM RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING
) AS RCFAB
    ON RCFAB.BUILDING_CD = RIM.PROPERTY_BUILDING_CD
LEFT JOIN special_building_master_v AS SBM ON RIM.PROPERTY_BUILDING_CD = SBM.BUILDING_CD
LEFT JOIN (
    SELECT *
    FROM (
        SELECT
            BUILDING_CODE,
            ROOM_CODE,
            CASE
                WHEN EXCLUSIVE_FROM <= TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD')::NUMERIC
                     AND EXCLUSIVE_TO >= TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD')::NUMERIC THEN '1'
                WHEN EXCLUSIVE_FROM > TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD')::NUMERIC THEN '2'
                ELSE '3'
            END AS EXCLUSIVE_IN_PROGRESS,
            ROW_NUMBER() OVER (
                PARTITION BY BUILDING_CODE, ROOM_CODE
                ORDER BY 
                    CASE 
                        WHEN EXCLUSIVE_FROM <= TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD')::NUMERIC
                             AND EXCLUSIVE_TO >= TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD')::NUMERIC THEN '1'
                        WHEN EXCLUSIVE_FROM > TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD')::NUMERIC THEN '2'
                        ELSE '3'
                    END
            ) AS rn
        FROM EXCLUSIVE_PROPERTY
        WHERE DELETE_FLAG <> '1'
        AND EARLY_CLOSURE_FLAG <> '1'
    ) sub
    WHERE rn = 1  -- FLAG=1を優先し、1件だけ残す
) AS EP
    ON RIM.PROPERTY_BUILDING_CD = EP.BUILDING_CODE AND RIM.PROPERTY_ROOM_CD = EP.ROOM_CODE;
