/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ManagedPropertyDataIbmLayoutTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ManagedPropertyDataIbmLayoutPojo

import org.jooq.impl.TableRecordImpl


/**
 * 管理物件データ(IBMレイアウト) 既存システム物理名: FBWD0P
 */
@Suppress("UNCHECKED_CAST")
open class ManagedPropertyDataIbmLayoutRecord private constructor() : TableRecordImpl<ManagedPropertyDataIbmLayoutRecord>(ManagedPropertyDataIbmLayoutTable.MANAGED_PROPERTY_DATA_IBM_LAYOUT) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var managementBranchCd: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var buildingCd: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var managerCd: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var customerCd: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var bulkLeaseCategory: Byte?
        set(value): Unit = set(10, value)
        get(): Byte? = get(10) as Byte?

    open var completionDate: Int?
        set(value): Unit = set(11, value)
        get(): Int? = get(11) as Int?

    open var managementStartDate: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    /**
     * Create a detached, initialised ManagedPropertyDataIbmLayoutRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, managementBranchCd: String? = null, buildingCd: String? = null, managerCd: String? = null, customerCd: String? = null, bulkLeaseCategory: Byte? = null, completionDate: Int? = null, managementStartDate: Int? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.managementBranchCd = managementBranchCd
        this.buildingCd = buildingCd
        this.managerCd = managerCd
        this.customerCd = customerCd
        this.bulkLeaseCategory = bulkLeaseCategory
        this.completionDate = completionDate
        this.managementStartDate = managementStartDate
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ManagedPropertyDataIbmLayoutRecord
     */
    constructor(value: ManagedPropertyDataIbmLayoutPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.managementBranchCd = value.managementBranchCd
            this.buildingCd = value.buildingCd
            this.managerCd = value.managerCd
            this.customerCd = value.customerCd
            this.bulkLeaseCategory = value.bulkLeaseCategory
            this.completionDate = value.completionDate
            this.managementStartDate = value.managementStartDate
            resetChangedOnNotNull()
        }
    }
}
