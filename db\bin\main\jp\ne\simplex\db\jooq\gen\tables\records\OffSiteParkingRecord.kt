/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.OffSiteParkingTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.OffSiteParkingPojo

import org.jooq.impl.TableRecordImpl


/**
 * 敷地外駐車場 既存システム物理名: ECC40P
 */
@Suppress("UNCHECKED_CAST")
open class OffSiteParkingRecord private constructor() : TableRecordImpl<OffSiteParkingRecord>(OffSiteParkingTable.OFF_SITE_PARKING) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var orderCd: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var parkingCd: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var offSiteLocationPrefCd: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var offSiteLocationCityCd: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var offSiteLocationTownCd: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var offSiteLocationDetail: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var residenceDisplayType: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var residenceLocationPrefCd: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var residenceLocationCityCd: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var residenceLocationTownCd: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var residenceLocationDetail: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var latitudeWgs: Int?
        set(value): Unit = set(18, value)
        get(): Int? = get(18) as Int?

    open var longitudeWgs: Int?
        set(value): Unit = set(19, value)
        get(): Int? = get(19) as Int?

    open var distance: Short?
        set(value): Unit = set(20, value)
        get(): Short? = get(20) as Short?

    open var mapScale: Short?
        set(value): Unit = set(21, value)
        get(): Short? = get(21) as Short?

    /**
     * Create a detached, initialised OffSiteParkingRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteSign: Byte? = null, orderCd: String? = null, parkingCd: String? = null, offSiteLocationPrefCd: String? = null, offSiteLocationCityCd: String? = null, offSiteLocationTownCd: String? = null, offSiteLocationDetail: String? = null, residenceDisplayType: String? = null, residenceLocationPrefCd: String? = null, residenceLocationCityCd: String? = null, residenceLocationTownCd: String? = null, residenceLocationDetail: String? = null, latitudeWgs: Int? = null, longitudeWgs: Int? = null, distance: Short? = null, mapScale: Short? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteSign = logicalDeleteSign
        this.orderCd = orderCd
        this.parkingCd = parkingCd
        this.offSiteLocationPrefCd = offSiteLocationPrefCd
        this.offSiteLocationCityCd = offSiteLocationCityCd
        this.offSiteLocationTownCd = offSiteLocationTownCd
        this.offSiteLocationDetail = offSiteLocationDetail
        this.residenceDisplayType = residenceDisplayType
        this.residenceLocationPrefCd = residenceLocationPrefCd
        this.residenceLocationCityCd = residenceLocationCityCd
        this.residenceLocationTownCd = residenceLocationTownCd
        this.residenceLocationDetail = residenceLocationDetail
        this.latitudeWgs = latitudeWgs
        this.longitudeWgs = longitudeWgs
        this.distance = distance
        this.mapScale = mapScale
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised OffSiteParkingRecord
     */
    constructor(value: OffSiteParkingPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteSign = value.logicalDeleteSign
            this.orderCd = value.orderCd
            this.parkingCd = value.parkingCd
            this.offSiteLocationPrefCd = value.offSiteLocationPrefCd
            this.offSiteLocationCityCd = value.offSiteLocationCityCd
            this.offSiteLocationTownCd = value.offSiteLocationTownCd
            this.offSiteLocationDetail = value.offSiteLocationDetail
            this.residenceDisplayType = value.residenceDisplayType
            this.residenceLocationPrefCd = value.residenceLocationPrefCd
            this.residenceLocationCityCd = value.residenceLocationCityCd
            this.residenceLocationTownCd = value.residenceLocationTownCd
            this.residenceLocationDetail = value.residenceLocationDetail
            this.latitudeWgs = value.latitudeWgs
            this.longitudeWgs = value.longitudeWgs
            this.distance = value.distance
            this.mapScale = value.mapScale
            resetChangedOnNotNull()
        }
    }
}
