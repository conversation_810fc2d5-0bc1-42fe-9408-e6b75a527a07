package jp.ne.simplex.application.repository.external.dkportal.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalResponse
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.exception.ExternalApiServerException

class DKPortalUpdateRoomStatusResponse(
    @field:JsonProperty("status")
    val status: Boolean,
) : DKPortalResponse {

    fun throwIfReceivedError() {
        if (status) {
            return
        }
        throw ExternalApiServerException(
            ErrorType.DK_PORTAL_API_ERROR,
            ErrorMessage.DK_PORTAL_RECEIVED_ERROR_RESPONSE.format()
        )
    }
}
