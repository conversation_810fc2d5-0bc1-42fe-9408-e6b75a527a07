package jp.ne.simplex.application.repository.external.eboard.config

import com.fasterxml.jackson.annotation.JsonIgnore
import jp.ne.simplex.application.repository.external.ExternalApiLoggingInterceptor
import jp.ne.simplex.application.repository.external.ExternalApiRequest
import jp.ne.simplex.application.repository.external.ExternalApiRestClient
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.log.LogType
import org.apache.hc.client5.http.classic.HttpClient
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestClient

@Configuration
class EboardRestConfig(
    @Value("\${external.eboard.endpoint}")
    private val endpoint: String,
) {

    @Bean("eboardRestClient")
    fun restClient(
        builder: RestClient.Builder = RestClient.builder(),
        httpClient: HttpClient? = null,
    ): ExternalApiRestClient<EboardRequest, EboardResponse> {
        return ExternalApiRestClient
            .builder<EboardRequest, EboardResponse>(builder)
            .endpoint(endpoint)
            .requestFactory(httpClient)
            .requestInterceptor(
                EboardAuthInterceptor(),
                ExternalApiLoggingInterceptor(LogType.EBOARD)
            )
            .errorHandler(
                EboardAuthErrorHandler(),
                EboardDefaultErrorHandler(),
            )
            .build()
    }
}

/** EboardRestClientを使用してHTTP通信する際の Request Interface */
interface EboardRequest : ExternalApiRequest {

    override fun getErrorType(): ErrorType {
        return ErrorType.EBOARD_API_ERROR
    }

    @JsonIgnore
    fun getApiPath(): EboardApiPath

}

/** EboardRestClientを使用してHTTP通信する際の Response body Interface */
interface EboardResponse

/** EboardRestClientを使用してHTTP通信する際の API パス一覧  */
enum class EboardApiPath(val value: String) {

    /** 認証 */
    TOKEN_DP_ISSUE("/token_dp_issue"),

    /** 仮押さえ情報更新 */
    UPDATE_KARIOSAE("/newEboardApi/update_kariosae"),

    /** 公開指示 */
    INSTRUCT_PUBLIC("/newEboardApi/instruct_public"),
    
    /** 駐車場予約 */
    RESERVE_PARKING("/newEboardApi/reserve_parking"),

    /** 駐車場配置図画像登録 */
    UPDATE_PARKING_IMAGE("/newEboardApi/update_parking_image"),

    /** ゴミ置場画像登録 */
    UPDATE_GARBAGE_IMAGE("/newEboardApi/update_garbage_image"),
}
