/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ProductNameMasterVTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ProductNameMasterVPojo

import org.jooq.impl.TableRecordImpl


/**
 * This class is generated by jOOQ.
 */
@Suppress("UNCHECKED_CAST")
open class ProductNameMasterVRecord private constructor() : TableRecordImpl<ProductNameMasterVRecord>(ProductNameMasterVTable.PRODUCT_NAME_MASTER_V) {

    open var productNameCode: Short?
        set(value): Unit = set(0, value)
        get(): Short? = get(0) as Short?

    open var productName: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    /**
     * Create a detached, initialised ProductNameMasterVRecord
     */
    constructor(productNameCode: Short? = null, productName: String? = null): this() {
        this.productNameCode = productNameCode
        this.productName = productName
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ProductNameMasterVRecord
     */
    constructor(value: ProductNameMasterVPojo?): this() {
        if (value != null) {
            this.productNameCode = value.productNameCode
            this.productName = value.productName
            resetChangedOnNotNull()
        }
    }
}
