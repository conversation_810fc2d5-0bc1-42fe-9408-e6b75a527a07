-- TABLE: BUILDING_MAINTENANCE_INFO(建物メンテナンス情報)

CREATE TABLE BUILDING_MAINTENANCE_INFO(
     CREATION_DATE                                numeric(10,0)                 
,    CREATION_TIME                                numeric(8,0)                  
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(10,0)                 
,    UPDATE_TIME                                  numeric(8,0)                  
,    UPDATER                                      varchar(10)                   
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    LOGICAL_DELETE_FLAG                          numeric(1,0)                  
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    GEOSPATIAL_AUTHORITY_11DIGIT_CD              varchar(11)                   
,    ADDRESS_DETAIL                               varchar(62)                   
,    ADDRESS_DETAIL_ALPHABET                      varchar(200)                  
,    BUILDING_NAME_ALPHABET                       varchar(200)                  
,    RESERVE_01                                   numeric(1,0)                  
,    RESERVE_02                                   numeric(1,0)                  
,    RESERVE_03                                   numeric(1,0)                  
,    RESERVE_04                                   numeric(1,0)                  
,    RESERVE_05                                   numeric(1,0)                  
,    RESERVE_06                                   numeric(1,0)                  
,    RESERVE_07                                   numeric(1,0)                  
,    RESERVE_08                                   numeric(1,0)                  
,    RESERVE_09                                   numeric(1,0)                  
,    RESERVE_10                                   numeric(1,0)                  
,    CONSTRAINT PK_BUILDING_MAINTENANCE_INFO PRIMARY KEY (BUILDING_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BUILDING_MAINTENANCE_INFO IS '建物メンテナンス情報 既存システム物理名: EBLDMP';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.CREATION_DATE IS '作成年月日 既存システム物理名: EBL01D';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.CREATION_TIME IS '作成時刻 既存システム物理名: EBL02H';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.CREATOR IS '作成者 既存システム物理名: EBL03C';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.UPDATE_DATE IS '更新年月日 既存システム物理名: EBL04D';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.UPDATE_TIME IS '更新時刻 既存システム物理名: EBL05H';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.UPDATER IS '更新者 既存システム物理名: EBL06C';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EBL07N';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.LOGICAL_DELETE_FLAG IS '論理削除サイン 既存システム物理名: EBL08S';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.BUILDING_CODE IS '建物コード 既存システム物理名: EBL09C';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.GEOSPATIAL_AUTHORITY_11DIGIT_CD IS '国土地理院11桁CD 既存システム物理名: EBL10C';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.ADDRESS_DETAIL IS '住所詳細 既存システム物理名: EBL11X';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.ADDRESS_DETAIL_ALPHABET IS '住所詳細アルファベット表記 既存システム物理名: EBL12X';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.BUILDING_NAME_ALPHABET IS '建物名アルファベット表記 既存システム物理名: EBL13X';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.RESERVE_01 IS '予備01 既存システム物理名: EBLF01';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.RESERVE_02 IS '予備02 既存システム物理名: EBLF02';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.RESERVE_03 IS '予備03 既存システム物理名: EBLF03';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.RESERVE_04 IS '予備04 既存システム物理名: EBLF04';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.RESERVE_05 IS '予備05 既存システム物理名: EBLF05';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.RESERVE_06 IS '予備06 既存システム物理名: EBLF06';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.RESERVE_07 IS '予備07 既存システム物理名: EBLF07';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.RESERVE_08 IS '予備08 既存システム物理名: EBLF08';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.RESERVE_09 IS '予備09 既存システム物理名: EBLF09';
COMMENT ON COLUMN BUILDING_MAINTENANCE_INFO.RESERVE_10 IS '予備10 既存システム物理名: EBLF10';
