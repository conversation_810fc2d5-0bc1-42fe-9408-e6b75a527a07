-- ************************************************************************** --
-- ファンクション名 : ROUND_UP
-- 処理概要         : 切り上げ処理を行う
-- 引数             : target(対象数値), unit(桁数)
-- 戻り値           : date
-- 備考             :
-- ************************************************************************** --
CREATE OR REPLACE FUNCTION ROUND_UP(numeric, integer)
RETURNS numeric AS $$
  DECLARE
    target ALIAS FOR $1;
    digits ALIAS FOR $2;
    c_add character varying;
  BEGIN
    c_add = '0.';
    FOR i IN 1..digits LOOP
      c_add = c_add || '0';
    END LOOP;
    c_add = c_add || '9';
    RETURN TRUNC(abs(target) + cast(c_add as numeric), digits) * sign(target);
  END;
$$ LANGUAGE plpgsql;
