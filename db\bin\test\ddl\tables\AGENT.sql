-- TABLE: AGENT(仲介業者)

CREATE TABLE AGENT(
     SAKUSEI_DT                                   numeric(8,0)                  
,    SAKUSEI_TM                                   numeric(6,0)                  
,    KOSHIN_DT                                    numeric(8,0)                  
,    KOSHIN_TM                                    numeric(6,0)                  
,    KOSHIN_PGM_ID                                varchar(10)                   
,    KOSHINS<PERSON>                                    varchar(10)                   
,    CHUKAI_GYOSHA_CD                             varchar(9)        NOT NULL    
,    TOGO_TORIHIKISAKI_CD                         varchar(10)                   
,    CHUKAI_GYOSHAMEI_KANJI                       varchar(42)                   
,    CHUKAI_GYOSHAMEI_KANJI_2                     varchar(28)                   
,    CHUKAI_GYOSHAMEI_KANA                        varchar(40)                   
,    <PERSON><PERSON><PERSON>HITENMEI_KANJI                           varchar(42)                   
,    KENSAKUYO_KANA                               varchar(25)                   
,    K<PERSON><PERSON>IN_HOJIN_KBN                              varchar(1)                    
,    HOJINKAKU_KBN                                varchar(2)                    
,    HO<PERSON>INKAKU_ZENGO_KBN                          varchar(1)                    
,    <PERSON>ANREN_KAISHA_SIGN                           numeric(1,0)                  
,    DAIHYOSHAMEI_KANJI                           varchar(42)                   
,    DAIHYOSHAMEI_KANA                            varchar(10)                   
,    CHUKAI_GYOSHA_TANTO_BUSHO                    varchar(12)                   
,    CHUKAI_GYOSHA_TANTOSHAMEI                    varchar(42)                   
,    YUBIN_BANGO                                  varchar(8)                    
,    TODOFUKEN_CD                                 varchar(2)                    
,    SHIKUGUN_CD                                  varchar(2)                    
,    CHOSONAZA_TSUSHO_CD                          varchar(6)                    
,    JUSHO_SHOSAI                                 varchar(62)                   
,    BIRU_MEISHO                                  varchar(32)                   
,    DENWA_BANGO                                  varchar(15)                   
,    FAX_BANGO                                    varchar(15)                   
,    SETSURITSU_DT                                numeric(8,0)                  
,    TAKKENNGYO_MENKYO_BANGO_1                    varchar(2)                    
,    TAKKENNGYO_MENKYO_BANGO_2                    varchar(2)                    
,    TAKKENNGYO_MENKYO_BANGO_3                    varchar(6)                    
,    GINKO_CD                                     varchar(4)                    
,    GINKO_SHITEN_CD                              varchar(3)                    
,    KOZA_SBT                                     varchar(1)                    
,    KOZA_BANGO                                   varchar(10)                   
,    KOZA_MEIGININMEI_KANA                        varchar(40)                   
,    KOZA_MEIGININMEI_KANJI                       varchar(42)                   
,    SHOKAI_TOROKU_SHITEN_CD                      varchar(6)                    
,    TORIHIKI_TEISHI_SIGN                         numeric(1,0)                  
,    INTERFACE_SIGN                               numeric(1,0)                  
,    DATA_IKOMOTO_KEY_1                           varchar(15)                   
,    DATA_IKOMOTO_KEY_2                           varchar(15)                   
,    JICHITAI_CD                                  numeric(5,0)                  
,    SAKUJO_DT                                    numeric(8,0)                  
,    TOKUTEI_FUDOSAN_KBN                          varchar(1)                    
,    SYUYOU_CHUKAI_GYOSHA_KBN                     varchar(2)                    
,    TOKUTEI_FC_KBN                               varchar(1)                    
,    YOBI_KBN_1                                   varchar(1)                    
,    YOBI_DT_1                                    numeric(8,0)                  
,    YOBI_DT_2                                    numeric(8,0)                  
,    CONSTRAINT PK_AGENT PRIMARY KEY (CHUKAI_GYOSHA_CD)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE AGENT IS '仲介業者 既存システム物理名: ELA10P';
COMMENT ON COLUMN AGENT.SAKUSEI_DT IS '作成年月日 既存システム物理名: SAKUSEI_DT';
COMMENT ON COLUMN AGENT.SAKUSEI_TM IS '作成時刻 既存システム物理名: SAKUSEI_TM';
COMMENT ON COLUMN AGENT.KOSHIN_DT IS '更新年月日 既存システム物理名: KOSHIN_DT';
COMMENT ON COLUMN AGENT.KOSHIN_TM IS '更新時刻 既存システム物理名: KOSHIN_TM';
COMMENT ON COLUMN AGENT.KOSHIN_PGM_ID IS '更新プログラムＩＤ 既存システム物理名: KOSHIN_PGM_ID';
COMMENT ON COLUMN AGENT.KOSHINSHA IS '更新者 既存システム物理名: KOSHINSHA';
COMMENT ON COLUMN AGENT.CHUKAI_GYOSHA_CD IS '仲介業者コード 既存システム物理名: CHUKAI_GYOSHA_CD';
COMMENT ON COLUMN AGENT.TOGO_TORIHIKISAKI_CD IS '統合取引先コード 既存システム物理名: TOGO_TORIHIKISAKI_CD';
COMMENT ON COLUMN AGENT.CHUKAI_GYOSHAMEI_KANJI IS '仲介業者名（漢字） 既存システム物理名: CHUKAI_GYOSHAMEI_KANJI';
COMMENT ON COLUMN AGENT.CHUKAI_GYOSHAMEI_KANJI_2 IS '仲介業者名（漢字） 既存システム物理名: CHUKAI_GYOSHAMEI_KANJI_2';
COMMENT ON COLUMN AGENT.CHUKAI_GYOSHAMEI_KANA IS '仲介業者名（仮名） 既存システム物理名: CHUKAI_GYOSHAMEI_KANA';
COMMENT ON COLUMN AGENT.HONSHITENMEI_KANJI IS '本支社名（漢字） 既存システム物理名: HONSHITENMEI_KANJI';
COMMENT ON COLUMN AGENT.KENSAKUYO_KANA IS '検索用仮名 既存システム物理名: KENSAKUYO_KANA';
COMMENT ON COLUMN AGENT.KOJIN_HOJIN_KBN IS '個人法人区分 既存システム物理名: KOJIN_HOJIN_KBN';
COMMENT ON COLUMN AGENT.HOJINKAKU_KBN IS '法人格区分 既存システム物理名: HOJINKAKU_KBN';
COMMENT ON COLUMN AGENT.HOJINKAKU_ZENGO_KBN IS '法人格前後区分 既存システム物理名: HOJINKAKU_ZENGO_KBN';
COMMENT ON COLUMN AGENT.KANREN_KAISHA_SIGN IS '関連会社サイン 既存システム物理名: KANREN_KAISHA_SIGN';
COMMENT ON COLUMN AGENT.DAIHYOSHAMEI_KANJI IS '代表者名（漢字） 既存システム物理名: DAIHYOSHAMEI_KANJI';
COMMENT ON COLUMN AGENT.DAIHYOSHAMEI_KANA IS '代表者名（仮名） 既存システム物理名: DAIHYOSHAMEI_KANA';
COMMENT ON COLUMN AGENT.CHUKAI_GYOSHA_TANTO_BUSHO IS '仲介業者担当部署 既存システム物理名: CHUKAI_GYOSHA_TANTO_BUSHO';
COMMENT ON COLUMN AGENT.CHUKAI_GYOSHA_TANTOSHAMEI IS '仲介業者担当者名 既存システム物理名: CHUKAI_GYOSHA_TANTOSHAMEI';
COMMENT ON COLUMN AGENT.YUBIN_BANGO IS '郵便番号 既存システム物理名: YUBIN_BANGO';
COMMENT ON COLUMN AGENT.TODOFUKEN_CD IS '都道府県コード 既存システム物理名: TODOFUKEN_CD';
COMMENT ON COLUMN AGENT.SHIKUGUN_CD IS '市区郡コード 既存システム物理名: SHIKUGUN_CD';
COMMENT ON COLUMN AGENT.CHOSONAZA_TSUSHO_CD IS '町村字通称コード 既存システム物理名: CHOSONAZA_TSUSHO_CD';
COMMENT ON COLUMN AGENT.JUSHO_SHOSAI IS '住所詳細 既存システム物理名: JUSHO_SHOSAI';
COMMENT ON COLUMN AGENT.BIRU_MEISHO IS 'ビル名称 既存システム物理名: BIRU_MEISHO';
COMMENT ON COLUMN AGENT.DENWA_BANGO IS '電話番号 既存システム物理名: DENWA_BANGO';
COMMENT ON COLUMN AGENT.FAX_BANGO IS 'ＦＡＸ番号 既存システム物理名: FAX_BANGO';
COMMENT ON COLUMN AGENT.SETSURITSU_DT IS '設立年月日 既存システム物理名: SETSURITSU_DT';
COMMENT ON COLUMN AGENT.TAKKENNGYO_MENKYO_BANGO_1 IS '宅建業免許番号１ 既存システム物理名: TAKKENNGYO_MENKYO_BANGO_1';
COMMENT ON COLUMN AGENT.TAKKENNGYO_MENKYO_BANGO_2 IS '宅建業免許番号２ 既存システム物理名: TAKKENNGYO_MENKYO_BANGO_2';
COMMENT ON COLUMN AGENT.TAKKENNGYO_MENKYO_BANGO_3 IS '宅建業免許番号３ 既存システム物理名: TAKKENNGYO_MENKYO_BANGO_3';
COMMENT ON COLUMN AGENT.GINKO_CD IS '銀行コード 既存システム物理名: GINKO_CD';
COMMENT ON COLUMN AGENT.GINKO_SHITEN_CD IS '銀行支店コード 既存システム物理名: GINKO_SHITEN_CD';
COMMENT ON COLUMN AGENT.KOZA_SBT IS '口座種別 既存システム物理名: KOZA_SBT';
COMMENT ON COLUMN AGENT.KOZA_BANGO IS '口座番号 既存システム物理名: KOZA_BANGO';
COMMENT ON COLUMN AGENT.KOZA_MEIGININMEI_KANA IS '口座名義人名（仮名 既存システム物理名: KOZA_MEIGININMEI_KANA';
COMMENT ON COLUMN AGENT.KOZA_MEIGININMEI_KANJI IS '口座名義人名（漢字 既存システム物理名: KOZA_MEIGININMEI_KANJI';
COMMENT ON COLUMN AGENT.SHOKAI_TOROKU_SHITEN_CD IS '初回登録支店コード 既存システム物理名: SHOKAI_TOROKU_SHITEN_CD';
COMMENT ON COLUMN AGENT.TORIHIKI_TEISHI_SIGN IS '取引停止サイン 既存システム物理名: TORIHIKI_TEISHI_SIGN';
COMMENT ON COLUMN AGENT.INTERFACE_SIGN IS 'インターフェースサイン 既存システム物理名: INTERFACE_SIGN';
COMMENT ON COLUMN AGENT.DATA_IKOMOTO_KEY_1 IS 'データ移行元キー１ 既存システム物理名: DATA_IKOMOTO_KEY_1';
COMMENT ON COLUMN AGENT.DATA_IKOMOTO_KEY_2 IS 'データ移行元キー２ 既存システム物理名: DATA_IKOMOTO_KEY_2';
COMMENT ON COLUMN AGENT.JICHITAI_CD IS '自治体コード 既存システム物理名: JICHITAI_CD';
COMMENT ON COLUMN AGENT.SAKUJO_DT IS '削除年月日 既存システム物理名: SAKUJO_DT';
COMMENT ON COLUMN AGENT.TOKUTEI_FUDOSAN_KBN IS '特定不動産区分 既存システム物理名: TOKUTEI_FUDOSAN_KBN';
COMMENT ON COLUMN AGENT.SYUYOU_CHUKAI_GYOSHA_KBN IS '主要仲介業者ＣＤ 既存システム物理名: SYUYOU_CHUKAI_GYOSHA_KBN';
COMMENT ON COLUMN AGENT.TOKUTEI_FC_KBN IS '直営ＦＣ区分 既存システム物理名: TOKUTEI_FC_KBN';
COMMENT ON COLUMN AGENT.YOBI_KBN_1 IS '予備区分１ 既存システム物理名: YOBI_KBN_1';
COMMENT ON COLUMN AGENT.YOBI_DT_1 IS '予備日付１ 既存システム物理名: YOBI_DT_1';
COMMENT ON COLUMN AGENT.YOBI_DT_2 IS '予備日付２ 既存システム物理名: YOBI_DT_2';
