/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.DaitoPartnersOfficeCdTableTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.DaitoPartnersOfficeCdTablePojo

import org.jooq.impl.TableRecordImpl


/**
 * 大東パートナーズ営業所CD表 既存システム物理名: EDP10P
 */
@Suppress("UNCHECKED_CAST")
open class DaitoPartnersOfficeCdTableRecord private constructor() : TableRecordImpl<DaitoPartnersOfficeCdTableRecord>(DaitoPartnersOfficeCdTableTable.DAITO_PARTNERS_OFFICE_CD_TABLE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var rentalReviewCenterCd: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var centerCategory: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var rentalReviewCenterName: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var rentalReviewCenterAbbreviation: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var jurisdictionBranchCd: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var deletionDate: Int?
        set(value): Unit = set(11, value)
        get(): Int? = get(11) as Int?

    /**
     * Create a detached, initialised DaitoPartnersOfficeCdTableRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, rentalReviewCenterCd: String? = null, centerCategory: String? = null, rentalReviewCenterName: String? = null, rentalReviewCenterAbbreviation: String? = null, jurisdictionBranchCd: String? = null, deletionDate: Int? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.rentalReviewCenterCd = rentalReviewCenterCd
        this.centerCategory = centerCategory
        this.rentalReviewCenterName = rentalReviewCenterName
        this.rentalReviewCenterAbbreviation = rentalReviewCenterAbbreviation
        this.jurisdictionBranchCd = jurisdictionBranchCd
        this.deletionDate = deletionDate
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised DaitoPartnersOfficeCdTableRecord
     */
    constructor(value: DaitoPartnersOfficeCdTablePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.rentalReviewCenterCd = value.rentalReviewCenterCd
            this.centerCategory = value.centerCategory
            this.rentalReviewCenterName = value.rentalReviewCenterName
            this.rentalReviewCenterAbbreviation = value.rentalReviewCenterAbbreviation
            this.jurisdictionBranchCd = value.jurisdictionBranchCd
            this.deletionDate = value.deletionDate
            resetChangedOnNotNull()
        }
    }
}
