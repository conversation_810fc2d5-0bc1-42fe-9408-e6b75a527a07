truncate table CONSUMPTION_TAX_RATE_MASTER;
insert into CONSUMPTION_TAX_RATE_MASTER (CREATION_DATE, CREATION_TIME, UPDATE_DATE, UPDATE_TIME, UPDATE_PROGRAM_ID, UPDATER, CONSUMPTION_TAX_MANAGEMENT_CODE, EFFECTIVE_START_DATE, EFFECTIVE_END_DATE, NATIONAL_TAX_CONSUMPTION_PERCENT, LOCAL_TAX_CONSUMPTION_PERCENT) values
 (19960412, 170000, 19960412, 170000, 'DFU', 'HAYAKAWA', '01', 19890401, 19970331, 3, 0)
,(19960412, 170000, 19960412, 170000, 'DFU', 'HAYAKAWA', '01', 19970401, 20140323, 5, 0)
,(19960520, 181500, 19960520, 180500, 'DFU', 'HAY<PERSON><PERSON><PERSON>', '01', 0, 19890331, 0, 0)
,(0, 0, 0, 0, 'DFU', null, '01', 20140324, 20190909, 8, 0)
,(20190912, 100000, 20190912, 100000, 'DFU', '104974', '01', 20190911, 99999999, 10, 0)
;
