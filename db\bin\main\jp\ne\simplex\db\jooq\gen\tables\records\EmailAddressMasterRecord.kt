/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.EmailAddressMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.EmailAddressMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * メールアドレスマスタ 既存システム物理名: XXMADP
 */
@Suppress("UNCHECKED_CAST")
open class EmailAddressMasterRecord private constructor() : TableRecordImpl<EmailAddressMasterRecord>(EmailAddressMasterTable.EMAIL_ADDRESS_MASTER) {

    open var employeeNumber: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var notesId: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var address: String
        set(value): Unit = set(2, value)
        get(): String = get(2) as String

    /**
     * Create a detached, initialised EmailAddressMasterRecord
     */
    constructor(employeeNumber: String, notesId: String, address: String): this() {
        this.employeeNumber = employeeNumber
        this.notesId = notesId
        this.address = address
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised EmailAddressMasterRecord
     */
    constructor(value: EmailAddressMasterPojo?): this() {
        if (value != null) {
            this.employeeNumber = value.employeeNumber
            this.notesId = value.notesId
            this.address = value.address
            resetChangedOnNotNull()
        }
    }
}
