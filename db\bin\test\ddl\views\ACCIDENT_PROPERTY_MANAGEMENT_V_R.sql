-- VIEW: ACCIDENT_PROPERTY_MANAGEMENT_V_R(事故物件管理ビュー部屋コード付)

CREATE VIEW ACCIDENT_PROPERTY_MANAGEMENT_V_R AS
SELECT
     1 as NOTICE_FLAG
,    MIN(TARGET_CATEGORY) as TARGET_CATEGORY
,    BUILDING_CODE
,    ROOM_CODE
 FROM ACCIDENT_PROPERTY_MANAGEMENT AS APM
 WHERE (APM.LOGICAL_DELETE_SIGN != '1' OR APM.LOGICAL_DELETE_SIGN IS NULL)
 AND (APM.ROOM_CODE IS NOT NULL OR APM.ROOM_CODE != '')
 AND APM.NOTICE_PERIOD_EXPIRATION_DATE >= TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD')::NUMERIC
 GROUP BY APM.BUILDING_CODE, APM.ROOM_CODE;
