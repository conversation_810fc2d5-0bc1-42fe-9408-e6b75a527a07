package jp.ne.simplex.mock

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.Parking
import jp.ne.simplex.application.repository.db.ParkingDetailsRepositoryInterface
import jp.ne.simplex.application.repository.db.pojos.AggregateTenantContractPojo
import jp.ne.simplex.application.repository.db.pojos.ParkingDetailPojo
import jp.ne.simplex.application.repository.db.pojos.PreviousParkingTenantContractPojo
import jp.ne.simplex.application.repository.db.pojos.PropertyTenantContractPojo
import jp.ne.simplex.authentication.AuthInfo

class MockParkingDetailsRepository(
    val findParkingDetailByOrderCodeFunc: (
        orderCode: Building.OrderCode,
        forClient: Boolean
    ) -> List<Parking> = { _, _ -> emptyList() },
    val findParkingDetailForWelcomeParkBatchFunc: (orderCode: Building.OrderCode) -> List<Parking> = { _ -> emptyList() },
    val findParkingDetailPojoByOrderCodeFunc: (orderCode: Building.OrderCode) -> List<ParkingDetailPojo> = { _ -> emptyList() },
    val getAggregateTenantContractFunc: (tenantContractNumber: String) -> AggregateTenantContractPojo? = { _ -> null },
    val getPreviousTenantContractFunc: (orderCode: Building.OrderCode, parkingCode: String) -> PreviousParkingTenantContractPojo? = { _, _ -> null },
    val getBulkTenantContract: (parkingTenantContractNumber: String) -> PropertyTenantContractPojo? = { _ -> null },
) : ParkingDetailsRepositoryInterface {

    override fun findParkingDetailByOrderCode(
        orderCode: Building.OrderCode,
        forClient: Boolean,
        authInfo: AuthInfo?
    ): List<Parking> {
        return findParkingDetailByOrderCodeFunc(orderCode, forClient)
    }


    override fun findParkingDetailPojoByOrderCode(orderCode: Building.OrderCode): List<ParkingDetailPojo> {
        return findParkingDetailPojoByOrderCodeFunc(orderCode)
    }

    override fun findParkingDetailForWelcomeParkBatch(orderCode: Building.OrderCode): List<Parking> {
        return findParkingDetailForWelcomeParkBatchFunc(orderCode)
    }

    override fun findAggregateTenantContract(tenantContractNumber: String): AggregateTenantContractPojo? {
        return getAggregateTenantContractFunc(tenantContractNumber)
    }

    override fun findPreviousTenantContract(
        orderCode: Building.OrderCode,
        parkingCode: String,
        tenantContractNumber: String
    ): PreviousParkingTenantContractPojo? {
        return getPreviousTenantContractFunc(orderCode, parkingCode)
    }

    override fun findBulkTenantContract(
        parkingTenantContractNumber: String
    ): PropertyTenantContractPojo? {
        return getBulkTenantContract(parkingTenantContractNumber)
    }

}
