package jp.ne.simplex.application.service

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.model.LoginAccessKeyInfo
import jp.ne.simplex.application.repository.aws.SecretManagerRepository
import jp.ne.simplex.application.repository.db.AuthRepository
import jp.ne.simplex.authentication.AccessKeyVerifier
import jp.ne.simplex.authentication.JwtAuthProcessor
import jp.ne.simplex.db.jooq.gen.tables.references.EMPLOYEE_MASTER
import jp.ne.simplex.db.jooq.gen.tables.references.PASSWORD_MASTER
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ServerValidationException
import jp.ne.simplex.mock.MockEmployeeRepository
import jp.ne.simplex.mock.MockLocalDateTime
import jp.ne.simplex.mock.MockSecretManagerClient
import jp.ne.simplex.stub.stubAuthConfig
import jp.ne.simplex.stub.stubEmployee
import jp.ne.simplex.stub.stubSsoConfig
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.fail

class AuthServiceTest : AbstractTestContainerTest() {

    companion object {
        private const val EMPLOYEE_CODE = "123456"
        private const val ACCESS_KEY = "536c385ef56"
        private val currentDateTime = LocalDateTime.of(2025, 3, 24, 13, 45, 30)
    }

    private val jwtSmClient = MockSecretManagerClient.of(
        config = stubAuthConfig().jwt,
        accessTokenSecretKey = """
                            {
                                "jwt_key":"access_token_secret_key"
                            }
                        """.trimIndent(),
        refreshTokenSecretKey = """
                            {
                                "jwt_key":"refresh_token_secret_key"
                            }
                        """.trimIndent()
    )

    private val accessKeySmClient = MockSecretManagerClient.of(
        config = stubSsoConfig().accessKey,
        hash = "****************************************************************"
    )

    private val jwtAuthProcessor = JwtAuthProcessor(stubAuthConfig(), SecretManagerRepository(jwtSmClient))

    private val accessKeyVerifier = AccessKeyVerifier(stubSsoConfig(), SecretManagerRepository(accessKeySmClient))

    lateinit var authService: AuthService

    override fun beforeEach() {
        MockLocalDateTime.setNow(currentDateTime)
        val authRepository =
            object : AuthRepository(dslContext, MockEmployeeRepository()) {
                override fun
                    getEmployee(employeeCode: Employee.Code): Employee? {
                    return if (employeeCode.value == EMPLOYEE_CODE) stubEmployee(EMPLOYEE_CODE) else null
                }
            }
        authService = AuthService(authRepository, jwtAuthProcessor, accessKeyVerifier)
    }

    @AfterEach
    override fun tearDownEach() {
        super.tearDownEach()
        MockLocalDateTime.close()
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PASSWORD_MASTER, EMPLOYEE_MASTER)
    }

    @Nested
    @DisplayName("駐車場詳細遷移API")
    inner class Scenario1 {
        @Test
        @DisplayName("駐車場遷移用のアクセスキーが一致している場合トークンを生成すること")
        fun case1() {
            try {
                // execute
                authService.loginWithAccessKey(
                    LoginAccessKeyInfo(
                        Employee.Code.of(EMPLOYEE_CODE),
                        LoginAccessKeyInfo.AccessKey.of(ACCESS_KEY)
                    )
                )
            } catch (e: Exception) {
                fail()
            }
        }

        @Test
        @DisplayName("駐車場遷移用のアクセスキーが一致していない場合例外を投げること")
        fun case2() {
            try {
                // execute
                authService.loginWithAccessKey(LoginAccessKeyInfo(Employee.Code.of(EMPLOYEE_CODE), LoginAccessKeyInfo.AccessKey.of("hoge")))
                fail()
            } catch (e: ServerValidationException) {
                assertEquals(ErrorMessage.AUTHENTICATION_FAILURE.format().message, e.detail.message)
            }
        }

        @Test
        @DisplayName("社員が存在しない場合例外を投げること")
        fun case3() {
            try {
                // execute
                authService.loginWithAccessKey(LoginAccessKeyInfo(Employee.Code.of("000000"), LoginAccessKeyInfo.AccessKey.of(ACCESS_KEY)))
                fail()
            } catch (e: ServerValidationException) {
                assertEquals(
                    ErrorMessage.AUTHENTICATION_FAILURE.format().message,
                    e.detail.message
                )
            }
        }
    }
}
