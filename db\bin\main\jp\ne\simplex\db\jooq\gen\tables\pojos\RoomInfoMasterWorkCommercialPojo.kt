/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable
import java.math.BigDecimal


/**
 * 物件ボード用空き事業用物件ワーク 既存システム物理名: EMU33P
 */
@Suppress("UNCHECKED_CAST")
data class RoomInfoMasterWorkCommercialPojo(
    var branchCode: String? = null,
    var status: String? = null,
    var date: Int? = null,
    var negotiation: String? = null,
    var buildingCode: String? = null,
    var roomCode: String? = null,
    var landlordName: String? = null,
    var address_1: String? = null,
    var address_2: String? = null,
    var rent: Int? = null,
    var commonFee: Short? = null,
    var keyMoney: Byte? = null,
    var securityDeposit: Byte? = null,
    var moveOutDate: Int? = null,
    var remarks_1: String? = null,
    var remarks_2: String? = null,
    var rockyCategory: String? = null,
    var categoryA: String? = null,
    var categoryB: String? = null,
    var ffCategory: Byte? = null,
    var adCategory: Byte? = null,
    var newExistingType: String? = null,
    var category: String? = null,
    var area: BigDecimal? = null,
    var prefectureCode: String? = null,
    var cityCode: String? = null,
    var townCode: String? = null,
    var propertyAddressKana: String? = null,
    var townNameKana: String? = null,
    var roomNumber: String? = null,
    var tax: String? = null,
    var constructionDate: Int? = null,
    var cityKana: String? = null,
    var usageSort: String? = null,
    var time: Short? = null,
    var extractionBranchCode: String? = null,
    var address_3: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: RoomInfoMasterWorkCommercialPojo = other as RoomInfoMasterWorkCommercialPojo
        if (this.branchCode == null) {
            if (o.branchCode != null)
                return false
        }
        else if (this.branchCode != o.branchCode)
            return false
        if (this.status == null) {
            if (o.status != null)
                return false
        }
        else if (this.status != o.status)
            return false
        if (this.date == null) {
            if (o.date != null)
                return false
        }
        else if (this.date != o.date)
            return false
        if (this.negotiation == null) {
            if (o.negotiation != null)
                return false
        }
        else if (this.negotiation != o.negotiation)
            return false
        if (this.buildingCode == null) {
            if (o.buildingCode != null)
                return false
        }
        else if (this.buildingCode != o.buildingCode)
            return false
        if (this.roomCode == null) {
            if (o.roomCode != null)
                return false
        }
        else if (this.roomCode != o.roomCode)
            return false
        if (this.landlordName == null) {
            if (o.landlordName != null)
                return false
        }
        else if (this.landlordName != o.landlordName)
            return false
        if (this.address_1 == null) {
            if (o.address_1 != null)
                return false
        }
        else if (this.address_1 != o.address_1)
            return false
        if (this.address_2 == null) {
            if (o.address_2 != null)
                return false
        }
        else if (this.address_2 != o.address_2)
            return false
        if (this.rent == null) {
            if (o.rent != null)
                return false
        }
        else if (this.rent != o.rent)
            return false
        if (this.commonFee == null) {
            if (o.commonFee != null)
                return false
        }
        else if (this.commonFee != o.commonFee)
            return false
        if (this.keyMoney == null) {
            if (o.keyMoney != null)
                return false
        }
        else if (this.keyMoney != o.keyMoney)
            return false
        if (this.securityDeposit == null) {
            if (o.securityDeposit != null)
                return false
        }
        else if (this.securityDeposit != o.securityDeposit)
            return false
        if (this.moveOutDate == null) {
            if (o.moveOutDate != null)
                return false
        }
        else if (this.moveOutDate != o.moveOutDate)
            return false
        if (this.remarks_1 == null) {
            if (o.remarks_1 != null)
                return false
        }
        else if (this.remarks_1 != o.remarks_1)
            return false
        if (this.remarks_2 == null) {
            if (o.remarks_2 != null)
                return false
        }
        else if (this.remarks_2 != o.remarks_2)
            return false
        if (this.rockyCategory == null) {
            if (o.rockyCategory != null)
                return false
        }
        else if (this.rockyCategory != o.rockyCategory)
            return false
        if (this.categoryA == null) {
            if (o.categoryA != null)
                return false
        }
        else if (this.categoryA != o.categoryA)
            return false
        if (this.categoryB == null) {
            if (o.categoryB != null)
                return false
        }
        else if (this.categoryB != o.categoryB)
            return false
        if (this.ffCategory == null) {
            if (o.ffCategory != null)
                return false
        }
        else if (this.ffCategory != o.ffCategory)
            return false
        if (this.adCategory == null) {
            if (o.adCategory != null)
                return false
        }
        else if (this.adCategory != o.adCategory)
            return false
        if (this.newExistingType == null) {
            if (o.newExistingType != null)
                return false
        }
        else if (this.newExistingType != o.newExistingType)
            return false
        if (this.category == null) {
            if (o.category != null)
                return false
        }
        else if (this.category != o.category)
            return false
        if (this.area == null) {
            if (o.area != null)
                return false
        }
        else if (this.area != o.area)
            return false
        if (this.prefectureCode == null) {
            if (o.prefectureCode != null)
                return false
        }
        else if (this.prefectureCode != o.prefectureCode)
            return false
        if (this.cityCode == null) {
            if (o.cityCode != null)
                return false
        }
        else if (this.cityCode != o.cityCode)
            return false
        if (this.townCode == null) {
            if (o.townCode != null)
                return false
        }
        else if (this.townCode != o.townCode)
            return false
        if (this.propertyAddressKana == null) {
            if (o.propertyAddressKana != null)
                return false
        }
        else if (this.propertyAddressKana != o.propertyAddressKana)
            return false
        if (this.townNameKana == null) {
            if (o.townNameKana != null)
                return false
        }
        else if (this.townNameKana != o.townNameKana)
            return false
        if (this.roomNumber == null) {
            if (o.roomNumber != null)
                return false
        }
        else if (this.roomNumber != o.roomNumber)
            return false
        if (this.tax == null) {
            if (o.tax != null)
                return false
        }
        else if (this.tax != o.tax)
            return false
        if (this.constructionDate == null) {
            if (o.constructionDate != null)
                return false
        }
        else if (this.constructionDate != o.constructionDate)
            return false
        if (this.cityKana == null) {
            if (o.cityKana != null)
                return false
        }
        else if (this.cityKana != o.cityKana)
            return false
        if (this.usageSort == null) {
            if (o.usageSort != null)
                return false
        }
        else if (this.usageSort != o.usageSort)
            return false
        if (this.time == null) {
            if (o.time != null)
                return false
        }
        else if (this.time != o.time)
            return false
        if (this.extractionBranchCode == null) {
            if (o.extractionBranchCode != null)
                return false
        }
        else if (this.extractionBranchCode != o.extractionBranchCode)
            return false
        if (this.address_3 == null) {
            if (o.address_3 != null)
                return false
        }
        else if (this.address_3 != o.address_3)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.branchCode == null) 0 else this.branchCode.hashCode())
        result = prime * result + (if (this.status == null) 0 else this.status.hashCode())
        result = prime * result + (if (this.date == null) 0 else this.date.hashCode())
        result = prime * result + (if (this.negotiation == null) 0 else this.negotiation.hashCode())
        result = prime * result + (if (this.buildingCode == null) 0 else this.buildingCode.hashCode())
        result = prime * result + (if (this.roomCode == null) 0 else this.roomCode.hashCode())
        result = prime * result + (if (this.landlordName == null) 0 else this.landlordName.hashCode())
        result = prime * result + (if (this.address_1 == null) 0 else this.address_1.hashCode())
        result = prime * result + (if (this.address_2 == null) 0 else this.address_2.hashCode())
        result = prime * result + (if (this.rent == null) 0 else this.rent.hashCode())
        result = prime * result + (if (this.commonFee == null) 0 else this.commonFee.hashCode())
        result = prime * result + (if (this.keyMoney == null) 0 else this.keyMoney.hashCode())
        result = prime * result + (if (this.securityDeposit == null) 0 else this.securityDeposit.hashCode())
        result = prime * result + (if (this.moveOutDate == null) 0 else this.moveOutDate.hashCode())
        result = prime * result + (if (this.remarks_1 == null) 0 else this.remarks_1.hashCode())
        result = prime * result + (if (this.remarks_2 == null) 0 else this.remarks_2.hashCode())
        result = prime * result + (if (this.rockyCategory == null) 0 else this.rockyCategory.hashCode())
        result = prime * result + (if (this.categoryA == null) 0 else this.categoryA.hashCode())
        result = prime * result + (if (this.categoryB == null) 0 else this.categoryB.hashCode())
        result = prime * result + (if (this.ffCategory == null) 0 else this.ffCategory.hashCode())
        result = prime * result + (if (this.adCategory == null) 0 else this.adCategory.hashCode())
        result = prime * result + (if (this.newExistingType == null) 0 else this.newExistingType.hashCode())
        result = prime * result + (if (this.category == null) 0 else this.category.hashCode())
        result = prime * result + (if (this.area == null) 0 else this.area.hashCode())
        result = prime * result + (if (this.prefectureCode == null) 0 else this.prefectureCode.hashCode())
        result = prime * result + (if (this.cityCode == null) 0 else this.cityCode.hashCode())
        result = prime * result + (if (this.townCode == null) 0 else this.townCode.hashCode())
        result = prime * result + (if (this.propertyAddressKana == null) 0 else this.propertyAddressKana.hashCode())
        result = prime * result + (if (this.townNameKana == null) 0 else this.townNameKana.hashCode())
        result = prime * result + (if (this.roomNumber == null) 0 else this.roomNumber.hashCode())
        result = prime * result + (if (this.tax == null) 0 else this.tax.hashCode())
        result = prime * result + (if (this.constructionDate == null) 0 else this.constructionDate.hashCode())
        result = prime * result + (if (this.cityKana == null) 0 else this.cityKana.hashCode())
        result = prime * result + (if (this.usageSort == null) 0 else this.usageSort.hashCode())
        result = prime * result + (if (this.time == null) 0 else this.time.hashCode())
        result = prime * result + (if (this.extractionBranchCode == null) 0 else this.extractionBranchCode.hashCode())
        result = prime * result + (if (this.address_3 == null) 0 else this.address_3.hashCode())
        return result
    }
}
