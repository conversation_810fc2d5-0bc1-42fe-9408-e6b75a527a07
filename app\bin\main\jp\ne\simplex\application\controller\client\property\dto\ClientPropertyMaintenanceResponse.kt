package jp.ne.simplex.application.controller.client.property.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.UpdatePropertyMaintenance

data class ClientPropertyMaintenanceResponse(
    @JsonProperty("failedRecords")
    @field:Schema(description = "更新に失敗した物件IDリスト")
    val failedRecords: List<ClientFailedPropertyIdDto>,
) {
    companion object {
        fun of(failedRecords: List<UpdatePropertyMaintenance.FailedDetail>): ClientPropertyMaintenanceResponse {
            return ClientPropertyMaintenanceResponse(
                failedRecords = failedRecords.map {
                    ClientFailedPropertyIdDto.of(
                        propertyId = it.id,
                        adFfUpdateResult = it.adFfUpdateResult,
                        publicInstructionResult = it.publicInstructionResult
                    )
                }
            )
        }
    }

    data class ClientFailedPropertyIdDto(
        @JsonProperty("buildingCode")
        @field:Schema(description = "建物コード")
        val buildingCode: String,

        @JsonProperty("roomCode")
        @field:Schema(description = "部屋コード")
        val roomCode: String,

        @JsonProperty("adFfUpdateResult")
        @field:Schema(description = "AD金額・FF金額更新ステータス")
        val adFfUpdateResult: UpdatePropertyMaintenance.FailedDetail.Status,

        @JsonProperty("publicInstructionResult")
        @field:Schema(description = "公開指示更新更新ステータス")
        val publicInstructionResult: UpdatePropertyMaintenance.FailedDetail.Status,

        ) {
        companion object {
            fun of(
                propertyId: Property.Id,
                adFfUpdateResult: UpdatePropertyMaintenance.FailedDetail.Status,
                publicInstructionResult: UpdatePropertyMaintenance.FailedDetail.Status
            ): ClientFailedPropertyIdDto {
                return ClientFailedPropertyIdDto(
                    buildingCode = propertyId.buildingCode.value,
                    roomCode = propertyId.roomCode.value,
                    adFfUpdateResult = adFfUpdateResult,
                    publicInstructionResult = publicInstructionResult
                )
            }
        }
    }
}
