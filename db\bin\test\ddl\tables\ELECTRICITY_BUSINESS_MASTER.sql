-- TABLE: ELECTRICITY_BUSINESS_MASTER(電力事業マスタ)

CREATE TABLE ELECTRICITY_BUSINESS_MASTER(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER_ID                                   varchar(10)                   
,    LOGICAL_DELETE_FLAG                          varchar(1)                    
,    ELECTRICITY_BUSINESS_TYPE                    varchar(1)                    
,    ELECTRICITY_BUSINESS_TYPE_NAME               varchar(62)                   
,    DISPLAY_ORDER                                varchar(2)                    
,    REPORT_CATEGORY                              varchar(2)                    
,    SMILE_INFO_DISPLAY_FLAG                      varchar(1)                    
,    EXPLANATION_SPECIAL_CONTRACT_DISPLAY_FLAG    varchar(1)                    
,    ZEH_FLAG                                     varchar(1)                    
,    RESERVE1_FLAG                                varchar(1)                    
,    RESERVE1_VALUE                               varchar(42)                   
,    RESERVE2_FLAG                                varchar(1)                    
,    RESERVE2_VALUE                               varchar(42)                   
,    RESERVE3_FLAG                                varchar(1)                    
,    RESERVE3_VALUE                               varchar(42)                   
) TABLESPACE :TS_TBL;

COMMENT ON TABLE ELECTRICITY_BUSINESS_MASTER IS '電力事業マスタ 既存システム物理名: BGDENP';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: BGD01D @290';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: BGD02H @290';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: BGD03D @290';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: BGD04H @290';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: BGD05P @5026';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.UPDATER_ID IS '更新者ID 既存システム物理名: BGD06P @290';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.LOGICAL_DELETE_FLAG IS '論理削除フラグ 既存システム物理名: BGD07S @290';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.ELECTRICITY_BUSINESS_TYPE IS '電力事業区分 既存システム物理名: BGD08K @290 すまいるインフォ 表示フラグ';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.ELECTRICITY_BUSINESS_TYPE_NAME IS '電力事業区分名称 既存システム物理名: BGD09N @290 重説・特約表示 フラグ';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.DISPLAY_ORDER IS '表示順 既存システム物理名: BGD10N @290';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.REPORT_CATEGORY IS '帳票区分 既存システム物理名: BGD11P @290';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.SMILE_INFO_DISPLAY_FLAG IS 'すまいるインフォ表示フラグ 既存システム物理名: BGD12F @5026';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.EXPLANATION_SPECIAL_CONTRACT_DISPLAY_FLAG IS '重説・特約表示フラグ 既存システム物理名: BGD13F @290';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.ZEH_FLAG IS 'ZEHフラグ 既存システム物理名: BGD14F @5026';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.RESERVE1_FLAG IS '予備1フラグ 既存システム物理名: BGD15F @290';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.RESERVE1_VALUE IS '予備1値 既存システム物理名: BGD16V @5026';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.RESERVE2_FLAG IS '予備2フラグ 既存システム物理名: BGD17F';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.RESERVE2_VALUE IS '予備2値 既存システム物理名: BGD18V';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.RESERVE3_FLAG IS '予備3フラグ 既存システム物理名: BGD19F';
COMMENT ON COLUMN ELECTRICITY_BUSINESS_MASTER.RESERVE3_VALUE IS '予備3値 既存システム物理名: BGD20V';
