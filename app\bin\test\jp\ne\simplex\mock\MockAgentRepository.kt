package jp.ne.simplex.mock

import jp.ne.simplex.application.model.Agent
import jp.ne.simplex.application.repository.db.AgentRepositoryInterface

class MockAgentRepository(
    val listByFunc: (eCodeList: List<Agent.ECode>) -> List<Agent> = { _ -> emptyList<Agent>() }
) : AgentRepositoryInterface {
    override fun listBy(eCodeList: List<Agent.ECode>): List<Agent> {
        return listByFunc(eCodeList)
    }
}
