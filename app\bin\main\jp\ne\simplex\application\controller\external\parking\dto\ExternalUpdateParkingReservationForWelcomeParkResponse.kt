package jp.ne.simplex.application.controller.external.parking.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.controller.external.shared.ErrorResponseForWelcomePark
import jp.ne.simplex.exception.ServerValidationException

data class ExternalUpdateParkingReservationForWelcomeParkResponse(
    @JsonProperty("result")
    @field:Schema(description = "WelcomeParkが予約できるかの結果(0:予約不可, 1:予約可能)")
    val result: Int,
    @JsonProperty("errorCode")
    @field:Schema(description = "エラーコード(10: 建物コードまたは駐車場コード不正, 20: 使用中, 99: 予期せぬエラー)")
    val errorCode: String? = null,
    @JsonProperty("errorMessage")
    @field:Schema(description = "エラーメッセージ")
    val errorMessage: String? = null,
) {
    companion object {
        fun toSuccessResponse(): ExternalUpdateParkingReservationForWelcomeParkResponse {
            return ExternalUpdateParkingReservationForWelcomeParkResponse(
                result = 1,
            )
        }

        fun toFailureResponse(e: ServerValidationException): ExternalUpdateParkingReservationForWelcomeParkResponse {
            val errorMessage = ErrorResponseForWelcomePark.of(e.detail.errorMessage)

            return ExternalUpdateParkingReservationForWelcomeParkResponse(
                result = 0,
                errorCode = errorMessage.code,
                errorMessage = errorMessage.message
            )
        }
    }
}
