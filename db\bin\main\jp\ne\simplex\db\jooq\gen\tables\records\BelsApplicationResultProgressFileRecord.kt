/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.BelsApplicationResultProgressFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BelsApplicationResultProgressFilePojo

import org.jooq.impl.TableRecordImpl


/**
 * BELS申請結果進捗ファイル 既存システム物理名: BELSMP
 */
@Suppress("UNCHECKED_CAST")
open class BelsApplicationResultProgressFileRecord private constructor() : TableRecordImpl<BelsApplicationResultProgressFileRecord>(BelsApplicationResultProgressFileTable.BELS_APPLICATION_RESULT_PROGRESS_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgram: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var deleteFlag: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var orderCode: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var additionalCode: Short?
        set(value): Unit = set(8, value)
        get(): Short? = get(8) as Short?

    open var buildingNumber: Byte?
        set(value): Unit = set(9, value)
        get(): Byte? = get(9) as Byte?

    open var energySavingPerformance: Short?
        set(value): Unit = set(10, value)
        get(): Short? = get(10) as Short?

    open var certificationDate: Int?
        set(value): Unit = set(11, value)
        get(): Int? = get(11) as Int?

    open var attachedFileName: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var propertyBuildingCd: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    /**
     * Create a detached, initialised BelsApplicationResultProgressFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgram: String? = null, updater: String? = null, deleteFlag: String? = null, orderCode: Int? = null, additionalCode: Short? = null, buildingNumber: Byte? = null, energySavingPerformance: Short? = null, certificationDate: Int? = null, attachedFileName: String? = null, propertyBuildingCd: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgram = updateProgram
        this.updater = updater
        this.deleteFlag = deleteFlag
        this.orderCode = orderCode
        this.additionalCode = additionalCode
        this.buildingNumber = buildingNumber
        this.energySavingPerformance = energySavingPerformance
        this.certificationDate = certificationDate
        this.attachedFileName = attachedFileName
        this.propertyBuildingCd = propertyBuildingCd
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BelsApplicationResultProgressFileRecord
     */
    constructor(value: BelsApplicationResultProgressFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgram = value.updateProgram
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            this.orderCode = value.orderCode
            this.additionalCode = value.additionalCode
            this.buildingNumber = value.buildingNumber
            this.energySavingPerformance = value.energySavingPerformance
            this.certificationDate = value.certificationDate
            this.attachedFileName = value.attachedFileName
            this.propertyBuildingCd = value.propertyBuildingCd
            resetChangedOnNotNull()
        }
    }
}
