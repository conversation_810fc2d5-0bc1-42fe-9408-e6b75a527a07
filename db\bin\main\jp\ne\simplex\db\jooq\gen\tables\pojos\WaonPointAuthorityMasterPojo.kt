/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * WAONポイント権限マスタ 既存システム物理名: EMWAMP
 */
@Suppress("UNCHECKED_CAST")
data class WaonPointAuthorityMasterPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var creator: String? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updater: String? = null,
    var employeeNumber: String,
    var effectiveStartDate: Int? = null,
    var effectiveEndDate: Int? = null,
    var applicationScope: String? = null
): Serializable {


    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: WaonPointAuthorityMasterPojo = other as WaonPointAuthorityMasterPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.creator == null) {
            if (o.creator != null)
                return false
        }
        else if (this.creator != o.creator)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.employeeNumber != o.employeeNumber)
            return false
        if (this.effectiveStartDate == null) {
            if (o.effectiveStartDate != null)
                return false
        }
        else if (this.effectiveStartDate != o.effectiveStartDate)
            return false
        if (this.effectiveEndDate == null) {
            if (o.effectiveEndDate != null)
                return false
        }
        else if (this.effectiveEndDate != o.effectiveEndDate)
            return false
        if (this.applicationScope == null) {
            if (o.applicationScope != null)
                return false
        }
        else if (this.applicationScope != o.applicationScope)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.creator == null) 0 else this.creator.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + this.employeeNumber.hashCode()
        result = prime * result + (if (this.effectiveStartDate == null) 0 else this.effectiveStartDate.hashCode())
        result = prime * result + (if (this.effectiveEndDate == null) 0 else this.effectiveEndDate.hashCode())
        result = prime * result + (if (this.applicationScope == null) 0 else this.applicationScope.hashCode())
        return result
    }
}
