/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable
import java.math.BigDecimal
import java.math.BigInteger


/**
 * DK-PORTAL用物件データファイル 既存システム物理名: SITE_PROPERTY_KENTAKU_FOR_P
 */
@Suppress("UNCHECKED_CAST")
data class SitePropertyKentakuForPPojo(
    var propertyFullId: BigInteger? = null,
    var buildingId: Int? = null,
    var renewDate: String? = null,
    var distanceFromStation_1: BigDecimal? = null,
    var walkFromStation_1: Short? = null,
    var busFromStation_1: Short? = null,
    var busStopName_1: String? = null,
    var fromBusStop_1: Short? = null,
    var distanceFromBusstop_1: BigDecimal? = null,
    var nearestRoute_1: String? = null,
    var nearestStation_1: String? = null,
    var kindaikaCodeText_1: String? = null,
    var wayToCode_1: Byte? = null,
    var distanceFromStation_2: BigDecimal? = null,
    var walkFromStation_2: Short? = null,
    var busFromStation_2: Short? = null,
    var busStopName_2: String? = null,
    var fromBusStop_2: Short? = null,
    var distanceFromBusstop_2: BigDecimal? = null,
    var nearestRoute_2: String? = null,
    var nearestStation_2: String? = null,
    var kindaikaCodeText_2: String? = null,
    var wayToCode_2: Byte? = null,
    var distanceFromStation_3: BigDecimal? = null,
    var walkFromStation_3: Short? = null,
    var busFromStation_3: Short? = null,
    var busStopName_3: String? = null,
    var fromBusStop_3: Short? = null,
    var distanceFromBusstop_3: BigDecimal? = null,
    var nearestRoute_3: String? = null,
    var nearestStation_3: String? = null,
    var kindaikaCodeText_3: String? = null,
    var wayToCode_3: Byte? = null,
    var zipCodeText: String? = null,
    var prefecture: String? = null,
    var city: String? = null,
    var town: String? = null,
    var tyoume: String? = null,
    var kokudoCodeText: String? = null,
    var jisCodeValue: Int? = null,
    var townCodeValue: String? = null,
    var tyoumeCodeValue: Short? = null,
    var restaddr1: String? = null,
    var latitude: BigInteger? = null,
    var longitude: BigInteger? = null,
    var buildingName: String? = null,
    var dispNameCode: Byte? = null,
    var buildingFurigana: String? = null,
    var kindCode: Byte? = null,
    var kindDispName: String? = null,
    var saleBlockNum: Int? = null,
    var emptyHousesNum: Int? = null,
    var sellingCompany: String? = null,
    var completionDate: Int? = null,
    var areaWays1Code: Byte? = null,
    var structureCode: Byte? = null,
    var structureDispName: String? = null,
    var buildingTypeCode: Byte? = null,
    var allFloorNum: Short? = null,
    var underFloorNum: Byte? = null,
    var newUsedCode: Byte? = null,
    var managerStyleCode: Byte? = null,
    var managerComment: String? = null,
    var quietCode: Byte? = null,
    var gasCode: Byte? = null,
    var waterSupplyCode: Byte? = null,
    var wasteWaterCode: Byte? = null,
    var elecPowerCode: Byte? = null,
    var twoByFourCode: Byte? = null,
    var sellTypeCode: Byte? = null,
    var avoidQuakeCode: Byte? = null,
    var barrierFreeCode: Byte? = null,
    var fulltimeManagementCode: Byte? = null,
    var liftCode: Byte? = null,
    var liftNumCode: Byte? = null,
    var wallTypeCode: Byte? = null,
    var deliveryMailboxCode: Byte? = null,
    var launderetteCode: Byte? = null,
    var roomNumberText: String? = null,
    var dispRoomNumberCode: Byte? = null,
    var salesPoint: String? = null,
    var remark1: String? = null,
    var remark2: String? = null,
    var specialRemark: String? = null,
    var note: String? = null,
    var price: Long? = null,
    var priceTaxCode: Byte? = null,
    var consumptionTax: BigDecimal? = null,
    var queryPerson: String? = null,
    var firmSideCode: String? = null,
    var intoCode: Byte? = null,
    var intoDate: Int? = null,
    var leaveDate: Int? = null,
    var otherCompanyCode: Byte? = null,
    var messageToOtherCompany: String? = null,
    var registDate: Int? = null,
    var registTime: String? = null,
    var rentExchangeStyleCode: Byte? = null,
    var housePlanCode: Byte? = null,
    var roomNum: Short? = null,
    var housePlanEquiv: Int? = null,
    var windowDirectionCode: Byte? = null,
    var floorNum: String? = null,
    var nonMoveintoCode: Byte? = null,
    var managedPropertyCode: Byte? = null,
    var petCode: Byte? = null,
    var officeCode: Byte? = null,
    var musicalCode: Byte? = null,
    var housePlanDispName: String? = null,
    var usePartArea: BigDecimal? = null,
    var keyMoney: BigDecimal? = null,
    var keyMoneyUnitCode: Byte? = null,
    var keyMoneyTaxCode: Byte? = null,
    var deposit: BigDecimal? = null,
    var depositUnitCode: Byte? = null,
    var repairCost: BigDecimal? = null,
    var repairCostUnitCode: Byte? = null,
    var guaranty: BigDecimal? = null,
    var guarantyUnitCode: Byte? = null,
    var syokyakuClassCode: Byte? = null,
    var syokyaku: BigDecimal? = null,
    var syokyakuUnitCode: Byte? = null,
    var premium: BigDecimal? = null,
    var premiumUnitCode: Byte? = null,
    var premiumTaxCode: Byte? = null,
    var manageCost: Int? = null,
    var manageCostTaxCode: Byte? = null,
    var serviceFee: Int? = null,
    var serviceFeeFreeCode: Byte? = null,
    var serviceFeeTaxCode: Byte? = null,
    var zappi: Int? = null,
    var zappiTaxCode: Byte? = null,
    var otherCostComment: String? = null,
    var otherCost_1: Int? = null,
    var otherCostItem_1: String? = null,
    var otherCostTaxCode_1: Byte? = null,
    var otherCost_2: Int? = null,
    var otherCostItem_2: String? = null,
    var otherCostTaxCode_2: Byte? = null,
    var otherCost_3: Int? = null,
    var otherCostItem_3: String? = null,
    var otherCostTaxCode_3: Byte? = null,
    var otherCost_4: Int? = null,
    var otherCostItem_4: String? = null,
    var otherCostTaxCode_4: Byte? = null,
    var otherCost_5: Int? = null,
    var otherCostItem_5: String? = null,
    var otherCostTaxCode_5: Byte? = null,
    var outerFacilityCode_1: Byte? = null,
    var outerFacilityCode_2: Byte? = null,
    var outerArea_2: BigDecimal? = null,
    var renewalFee: BigDecimal? = null,
    var renewalFeeUnitCode: Byte? = null,
    var renewalFeeClassCode: Byte? = null,
    var houseRentLimitDate: Int? = null,
    var insuranceCode: Byte? = null,
    var specialRentalLowerCost: Int? = null,
    var specialRentalUpperCost: Int? = null,
    var additionalDepositUnitCode: Byte? = null,
    var additionalDepositReasonCode: Byte? = null,
    var brokerage: BigDecimal? = null,
    var brokerageUnitCode: Byte? = null,
    var renewalCharge: BigDecimal? = null,
    var renewalChargeUnitCode: Byte? = null,
    var studentOnlyCode: Byte? = null,
    var sexConditionCode: Byte? = null,
    var kidsCode: Byte? = null,
    var aloneCode: Byte? = null,
    var twoPeopleCode: Byte? = null,
    var elderCode: Byte? = null,
    var corporationOnlyCode: Byte? = null,
    var residenceHouseRentCode: Byte? = null,
    var roomStyleCode_1: Byte? = null,
    var roomArea_1: BigDecimal? = null,
    var roomUnitCode_1: Byte? = null,
    var roomStyleCode_2: Byte? = null,
    var roomArea_2: BigDecimal? = null,
    var roomUnitCode_2: Byte? = null,
    var roomStyleCode_3: Byte? = null,
    var roomArea_3: BigDecimal? = null,
    var roomUnitCode_3: Byte? = null,
    var roomStyleCode_4: Byte? = null,
    var roomArea_4: BigDecimal? = null,
    var roomUnitCode_4: Byte? = null,
    var roomStyleCode_5: Byte? = null,
    var roomArea_5: BigDecimal? = null,
    var roomUnitCode_5: Byte? = null,
    var roomStyleCode_6: Byte? = null,
    var roomArea_6: BigDecimal? = null,
    var roomUnitCode_6: Byte? = null,
    var roomStyleCode_7: Byte? = null,
    var roomArea_7: BigDecimal? = null,
    var roomUnitCode_7: Byte? = null,
    var roomStyleCode_8: Byte? = null,
    var roomArea_8: BigDecimal? = null,
    var roomUnitCode_8: Byte? = null,
    var roomStyleCode_9: Byte? = null,
    var roomArea_9: BigDecimal? = null,
    var roomUnitCode_9: Byte? = null,
    var roomStyleCode_10: Byte? = null,
    var roomArea_10: BigDecimal? = null,
    var roomUnitCode_10: Byte? = null,
    var parkingCode: Byte? = null,
    var fromNearParking: BigDecimal? = null,
    var parkingNum: Short? = null,
    var parkingTypeCode: Byte? = null,
    var parkingShutterCode: Byte? = null,
    var parkingLowerCost: Int? = null,
    var parkingTaxCode: Byte? = null,
    var parkableNumCode: Byte? = null,
    var parkingFreeCode: Byte? = null,
    var bikeParkCode: Byte? = null,
    var bikeParkCost: Int? = null,
    var motorbikeParkCode: Byte? = null,
    var motorbikeCost: Int? = null,
    var airconCode: Byte? = null,
    var coolerCode: Byte? = null,
    var heatingCode: Byte? = null,
    var loadHeaterCode: Byte? = null,
    var stoveCode: Byte? = null,
    var floorHeatingCode: Byte? = null,
    var catvCode: Byte? = null,
    var communityBroadcastCode: Byte? = null,
    var bsCode: Byte? = null,
    var csCode: Byte? = null,
    var internetCode: Byte? = null,
    var closetCode: Byte? = null,
    var walkinWardrobeCode: Byte? = null,
    var closetUnderFloorCode: Byte? = null,
    var trunkRoomCode: Byte? = null,
    var oshiireCode: Byte? = null,
    var garretClosetCode: Byte? = null,
    var shoeCupboardCode: Byte? = null,
    var storeroomCode: Byte? = null,
    var bathToiletCode: Byte? = null,
    var bathCode: Byte? = null,
    var showerCode: Byte? = null,
    var autoBathCode: Byte? = null,
    var dressingRoomCode: Byte? = null,
    var reboilBathCode: Byte? = null,
    var toiletCode: Byte? = null,
    var bathDrierCode: Byte? = null,
    var shampooDresserCode: Byte? = null,
    var washletCode: Byte? = null,
    var bathOver_1tsuboCode: Byte? = null,
    var warmletCode: Byte? = null,
    var cookingStoveCode: Byte? = null,
    var kitchenCode: Byte? = null,
    var microwaveOvenCode: Byte? = null,
    var ihCookingHeaterCode: Byte? = null,
    var coldStorageCode: Byte? = null,
    var grillCode: Byte? = null,
    var disposerCode: Byte? = null,
    var dishWasherCode: Byte? = null,
    var waterCleanerCode: Byte? = null,
    var woodenFloorCode: Byte? = null,
    var loftCode: Byte? = null,
    var cushionFloorCode: Byte? = null,
    var highestFloorCode: Byte? = null,
    var maisonetteCode: Byte? = null,
    var overSecondFloorCode: Byte? = null,
    var caveCode: Byte? = null,
    var soundproofCode: Byte? = null,
    var cornerHouseCode: Byte? = null,
    var sunroomCode: Byte? = null,
    var basementCode: Byte? = null,
    var southRoomCode: Byte? = null,
    var patioCode: Byte? = null,
    var crimePrevShutterCode: Byte? = null,
    var crimePrevCameraCode: Byte? = null,
    var autolockCode: Byte? = null,
    var doubleLockCode: Byte? = null,
    var washingMachineCode: Byte? = null,
    var drierCode: Byte? = null,
    var washingMachinePlaceCode: Byte? = null,
    var cardKeyCode: Byte? = null,
    var bowWindowCode: Byte? = null,
    var lightCode: Byte? = null,
    var allElectricCode: Byte? = null,
    var hotWaterSupplyCode: Byte? = null,
    var interphoneCode: Byte? = null,
    var fulltimeFunCode: Byte? = null,
    var ecocuteCode: Byte? = null,
    var doubleSideBalconyCode: Byte? = null,
    var balconySideNumCode: Byte? = null,
    var bathTvCode: Byte? = null,
    var porchCode: Byte? = null,
    var upStartDate: Int? = null,
    var upEndDate: Int? = null,
    var dressingTableCode: Byte? = null,
    var privateDustBoxCode: Byte? = null,
    var pianoCode: Byte? = null,
    var largeShoesBoxCode: Byte? = null,
    var closetUnderTatamiCode: Byte? = null,
    var indoorsBicycleParkingCode: Byte? = null,
    var securityKeyCode: Byte? = null,
    var shutterCode: Byte? = null,
    var forSouthCode: Byte? = null,
    var closetUnderstairCode: Byte? = null,
    var nearbyConvenienceStoreCode: Byte? = null,
    var nearbyBankCode: Byte? = null,
    var nearbyRentalVideoCode: Byte? = null,
    var largeScaleRenewalCode: Byte? = null,
    var recoveryCostCode: Byte? = null,
    var guarantorCode: Byte? = null,
    var guarantorProxyCode: Byte? = null,
    var guarantorProxyComCode: Byte? = null,
    var guarantorProxyComment: String? = null,
    var dispMapCode: Byte? = null,
    var latitudeWorld: BigInteger? = null,
    var longitudeWorld: BigInteger? = null,
    var gardenCode: Byte? = null,
    var balconyCode: Byte? = null,
    var panoramaId: Int? = null,
    var largeScaleRenewalDate: Int? = null,
    var shatakuKanouCode: Byte? = null,
    var noDepositPlanCode: Byte? = null,
    var kentakuKindCode: String? = null,
    var priceSaleFlag: Byte? = null,
    var refomeFlag: Byte? = null,
    var productCode: Short? = null,
    var ownerShipBranchCode: String? = null,
    var kentakuBuildingCode: String? = null,
    var kentakuRoomCode: String? = null,
    var keyExchangeFreeCode: Byte? = null,
    var adPrice: Int? = null,
    var ffPrice: String? = null,
    var leaveDateTp: Int? = null,
    var leaveFinishDate: Int? = null,
    var lowParkingPrice: Int? = null,
    var highParkingPrice: Int? = null,
    var structureDispNameTp: String? = null,
    var displaceCode: Byte? = null,
    var financeCorporationCode: Byte? = null,
    var waterCompanyName: String? = null,
    var waterCompanyTel: String? = null,
    var electricCompanyName: String? = null,
    var electricCompanyTel: String? = null,
    var gasCompanyName: String? = null,
    var gasCompanyTel: String? = null,
    var collectDate: String? = null,
    var kouentinCode: Byte? = null,
    var intoDateTxt: String? = null,
    var roomSituationCode: String? = null,
    var recordSituationCode: String? = null,
    var electricDiscountFlag: Byte? = null,
    var fletsHikariCode: Byte? = null,
    var akiyaTerm: Short? = null,
    var cleaningFeeCode: Byte? = null,
    var cleaningFee: Int? = null,
    var powerCode: Byte? = null,
    var fireZoneCode: Byte? = null,
    var discountRate: Short? = null,
    var discountTerm: Int? = null,
    var petFlag: Byte? = null,
    var internetFreeCode: Byte? = null,
    var allRoomCloset: Byte? = null,
    var walkThroughCloset: Byte? = null,
    var freeWashRoom: Byte? = null,
    var autoBath: Byte? = null,
    var indoorClothesDrying: Byte? = null,
    var motionSensorLighting: Byte? = null,
    var openKitchen: Byte? = null,
    var islandKitchen: Byte? = null,
    var gasCookerAttached: Byte? = null,
    var threeOverGas: Byte? = null,
    var doubleGlazing: Byte? = null,
    var securityGlazing: Byte? = null,
    var vibrationControlFloor: Byte? = null,
    var snowVanishingFacility: Byte? = null,
    var keroseneHeater: Byte? = null,
    var bathWindow: Byte? = null,
    var japaneseStyleRoom: Byte? = null,
    var earthquakeResistConst: Byte? = null,
    var allinoneServiceWater: Byte? = null,
    var allinoneServiceElectricity: Byte? = null,
    var allinoneServiceGas: Byte? = null,
    var priceAndCost: Long? = null,
    var valCode_1: String? = null,
    var valCode_2: String? = null,
    var valCode_3: String? = null,
    var panoramaType: Byte? = null,
    var serviceFeeDetails: String? = null,
    var shinsaBranchCode: String? = null,
    var contractConfirmCode: Byte? = null,
    var prefectureEn: String? = null,
    var shikugunchousonEn: String? = null,
    var ooazaTsuusyouEn: String? = null,
    var azaChoumeEn: String? = null,
    var restaddrAlphabet: String? = null,
    var upState: Byte? = null,
    var adPriceUnitCode: Byte? = null,
    var deleteDate: Int? = null,
    var realtimeUpTime: String? = null,
    var realtimeUpType: Byte? = null,
    var productTypeCd: Byte? = null,
    var moneyUpdateTime: String? = null,
    var kodawari100_199: String? = null,
    var floorMaxRoom: Short? = null,
    var renewalFeeFlg: Byte? = null,
    var fulltimeSupportFlg: Byte? = null,
    var membershipFeeExemptionKbn: Byte? = null,
    var membershipFeeExemptionDays: Byte? = null,
    var eboardComment: String? = null,
    var managementParkingKbn: Byte? = null,
    var netServiceJcom: Byte? = null,
    var netServiceStarcat: Byte? = null,
    var zehOriented: Byte? = null,
    var zehDkSoleil: Byte? = null,
    var zehDkAlpha: Byte? = null,
    var keySetCostFlag: Byte? = null,
    var electricIntroduction: Byte? = null,
    var electricType: Byte? = null,
    var emergencyECompanyName: String? = null,
    var emergencyECompanyTel: String? = null,
    var gasIntroduction: Byte? = null,
    var emergencyGasCompanyName: String? = null,
    var emergencyGasCompanyTel: String? = null,
    var waterIntroduction: Byte? = null,
    var waterMeterType: Byte? = null,
    var internetType: Byte? = null,
    var internetName: String? = null,
    var internetTel: String? = null,
    var internetIntroduction: Byte? = null,
    var waterServer: Byte? = null,
    var lifelineGuidanceType: Byte? = null,
    var roomSaveEnergyCertDate: Int? = null,
    var roomThirdPartyEvalFlg: Byte? = null,
    var roomSaveEnergyLevel: String? = null,
    var roomEnergyCost: String? = null,
    var roomEnergyCostSun: String? = null,
    var roomRenewEnergyFlg: Byte? = null,
    var roomInsulationLevel: String? = null,
    var roomEasyUtilityCosts: Int? = null,
    var roomZehLevelFlg: Byte? = null,
    var roomNetZeroEnergyFlg: Byte? = null,
    var buildingSaveEnergyCertDate: Int? = null,
    var buildingThirdPartyEvalFlg: Byte? = null,
    var buildingSaveEnergyLevel: String? = null,
    var buildingEnergyCost: String? = null,
    var buildingEnergyCostSun: String? = null,
    var buildingRenewEnergyFlg: Byte? = null,
    var buildingInsulationLevel: String? = null,
    var buildingEasyUtilityCosts: Int? = null,
    var buildingZehLevelFlg: Byte? = null,
    var buildingNetZeroEnergyFlg: Byte? = null,
    var shinsaBusinessOfficeCode: String? = null,
    var challengeStart: Int? = null,
    var challengeEnd: Int? = null,
    var challengeDiscountPrice: Int? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: SitePropertyKentakuForPPojo = other as SitePropertyKentakuForPPojo
        if (this.propertyFullId == null) {
            if (o.propertyFullId != null)
                return false
        }
        else if (this.propertyFullId != o.propertyFullId)
            return false
        if (this.buildingId == null) {
            if (o.buildingId != null)
                return false
        }
        else if (this.buildingId != o.buildingId)
            return false
        if (this.renewDate == null) {
            if (o.renewDate != null)
                return false
        }
        else if (this.renewDate != o.renewDate)
            return false
        if (this.distanceFromStation_1 == null) {
            if (o.distanceFromStation_1 != null)
                return false
        }
        else if (this.distanceFromStation_1 != o.distanceFromStation_1)
            return false
        if (this.walkFromStation_1 == null) {
            if (o.walkFromStation_1 != null)
                return false
        }
        else if (this.walkFromStation_1 != o.walkFromStation_1)
            return false
        if (this.busFromStation_1 == null) {
            if (o.busFromStation_1 != null)
                return false
        }
        else if (this.busFromStation_1 != o.busFromStation_1)
            return false
        if (this.busStopName_1 == null) {
            if (o.busStopName_1 != null)
                return false
        }
        else if (this.busStopName_1 != o.busStopName_1)
            return false
        if (this.fromBusStop_1 == null) {
            if (o.fromBusStop_1 != null)
                return false
        }
        else if (this.fromBusStop_1 != o.fromBusStop_1)
            return false
        if (this.distanceFromBusstop_1 == null) {
            if (o.distanceFromBusstop_1 != null)
                return false
        }
        else if (this.distanceFromBusstop_1 != o.distanceFromBusstop_1)
            return false
        if (this.nearestRoute_1 == null) {
            if (o.nearestRoute_1 != null)
                return false
        }
        else if (this.nearestRoute_1 != o.nearestRoute_1)
            return false
        if (this.nearestStation_1 == null) {
            if (o.nearestStation_1 != null)
                return false
        }
        else if (this.nearestStation_1 != o.nearestStation_1)
            return false
        if (this.kindaikaCodeText_1 == null) {
            if (o.kindaikaCodeText_1 != null)
                return false
        }
        else if (this.kindaikaCodeText_1 != o.kindaikaCodeText_1)
            return false
        if (this.wayToCode_1 == null) {
            if (o.wayToCode_1 != null)
                return false
        }
        else if (this.wayToCode_1 != o.wayToCode_1)
            return false
        if (this.distanceFromStation_2 == null) {
            if (o.distanceFromStation_2 != null)
                return false
        }
        else if (this.distanceFromStation_2 != o.distanceFromStation_2)
            return false
        if (this.walkFromStation_2 == null) {
            if (o.walkFromStation_2 != null)
                return false
        }
        else if (this.walkFromStation_2 != o.walkFromStation_2)
            return false
        if (this.busFromStation_2 == null) {
            if (o.busFromStation_2 != null)
                return false
        }
        else if (this.busFromStation_2 != o.busFromStation_2)
            return false
        if (this.busStopName_2 == null) {
            if (o.busStopName_2 != null)
                return false
        }
        else if (this.busStopName_2 != o.busStopName_2)
            return false
        if (this.fromBusStop_2 == null) {
            if (o.fromBusStop_2 != null)
                return false
        }
        else if (this.fromBusStop_2 != o.fromBusStop_2)
            return false
        if (this.distanceFromBusstop_2 == null) {
            if (o.distanceFromBusstop_2 != null)
                return false
        }
        else if (this.distanceFromBusstop_2 != o.distanceFromBusstop_2)
            return false
        if (this.nearestRoute_2 == null) {
            if (o.nearestRoute_2 != null)
                return false
        }
        else if (this.nearestRoute_2 != o.nearestRoute_2)
            return false
        if (this.nearestStation_2 == null) {
            if (o.nearestStation_2 != null)
                return false
        }
        else if (this.nearestStation_2 != o.nearestStation_2)
            return false
        if (this.kindaikaCodeText_2 == null) {
            if (o.kindaikaCodeText_2 != null)
                return false
        }
        else if (this.kindaikaCodeText_2 != o.kindaikaCodeText_2)
            return false
        if (this.wayToCode_2 == null) {
            if (o.wayToCode_2 != null)
                return false
        }
        else if (this.wayToCode_2 != o.wayToCode_2)
            return false
        if (this.distanceFromStation_3 == null) {
            if (o.distanceFromStation_3 != null)
                return false
        }
        else if (this.distanceFromStation_3 != o.distanceFromStation_3)
            return false
        if (this.walkFromStation_3 == null) {
            if (o.walkFromStation_3 != null)
                return false
        }
        else if (this.walkFromStation_3 != o.walkFromStation_3)
            return false
        if (this.busFromStation_3 == null) {
            if (o.busFromStation_3 != null)
                return false
        }
        else if (this.busFromStation_3 != o.busFromStation_3)
            return false
        if (this.busStopName_3 == null) {
            if (o.busStopName_3 != null)
                return false
        }
        else if (this.busStopName_3 != o.busStopName_3)
            return false
        if (this.fromBusStop_3 == null) {
            if (o.fromBusStop_3 != null)
                return false
        }
        else if (this.fromBusStop_3 != o.fromBusStop_3)
            return false
        if (this.distanceFromBusstop_3 == null) {
            if (o.distanceFromBusstop_3 != null)
                return false
        }
        else if (this.distanceFromBusstop_3 != o.distanceFromBusstop_3)
            return false
        if (this.nearestRoute_3 == null) {
            if (o.nearestRoute_3 != null)
                return false
        }
        else if (this.nearestRoute_3 != o.nearestRoute_3)
            return false
        if (this.nearestStation_3 == null) {
            if (o.nearestStation_3 != null)
                return false
        }
        else if (this.nearestStation_3 != o.nearestStation_3)
            return false
        if (this.kindaikaCodeText_3 == null) {
            if (o.kindaikaCodeText_3 != null)
                return false
        }
        else if (this.kindaikaCodeText_3 != o.kindaikaCodeText_3)
            return false
        if (this.wayToCode_3 == null) {
            if (o.wayToCode_3 != null)
                return false
        }
        else if (this.wayToCode_3 != o.wayToCode_3)
            return false
        if (this.zipCodeText == null) {
            if (o.zipCodeText != null)
                return false
        }
        else if (this.zipCodeText != o.zipCodeText)
            return false
        if (this.prefecture == null) {
            if (o.prefecture != null)
                return false
        }
        else if (this.prefecture != o.prefecture)
            return false
        if (this.city == null) {
            if (o.city != null)
                return false
        }
        else if (this.city != o.city)
            return false
        if (this.town == null) {
            if (o.town != null)
                return false
        }
        else if (this.town != o.town)
            return false
        if (this.tyoume == null) {
            if (o.tyoume != null)
                return false
        }
        else if (this.tyoume != o.tyoume)
            return false
        if (this.kokudoCodeText == null) {
            if (o.kokudoCodeText != null)
                return false
        }
        else if (this.kokudoCodeText != o.kokudoCodeText)
            return false
        if (this.jisCodeValue == null) {
            if (o.jisCodeValue != null)
                return false
        }
        else if (this.jisCodeValue != o.jisCodeValue)
            return false
        if (this.townCodeValue == null) {
            if (o.townCodeValue != null)
                return false
        }
        else if (this.townCodeValue != o.townCodeValue)
            return false
        if (this.tyoumeCodeValue == null) {
            if (o.tyoumeCodeValue != null)
                return false
        }
        else if (this.tyoumeCodeValue != o.tyoumeCodeValue)
            return false
        if (this.restaddr1 == null) {
            if (o.restaddr1 != null)
                return false
        }
        else if (this.restaddr1 != o.restaddr1)
            return false
        if (this.latitude == null) {
            if (o.latitude != null)
                return false
        }
        else if (this.latitude != o.latitude)
            return false
        if (this.longitude == null) {
            if (o.longitude != null)
                return false
        }
        else if (this.longitude != o.longitude)
            return false
        if (this.buildingName == null) {
            if (o.buildingName != null)
                return false
        }
        else if (this.buildingName != o.buildingName)
            return false
        if (this.dispNameCode == null) {
            if (o.dispNameCode != null)
                return false
        }
        else if (this.dispNameCode != o.dispNameCode)
            return false
        if (this.buildingFurigana == null) {
            if (o.buildingFurigana != null)
                return false
        }
        else if (this.buildingFurigana != o.buildingFurigana)
            return false
        if (this.kindCode == null) {
            if (o.kindCode != null)
                return false
        }
        else if (this.kindCode != o.kindCode)
            return false
        if (this.kindDispName == null) {
            if (o.kindDispName != null)
                return false
        }
        else if (this.kindDispName != o.kindDispName)
            return false
        if (this.saleBlockNum == null) {
            if (o.saleBlockNum != null)
                return false
        }
        else if (this.saleBlockNum != o.saleBlockNum)
            return false
        if (this.emptyHousesNum == null) {
            if (o.emptyHousesNum != null)
                return false
        }
        else if (this.emptyHousesNum != o.emptyHousesNum)
            return false
        if (this.sellingCompany == null) {
            if (o.sellingCompany != null)
                return false
        }
        else if (this.sellingCompany != o.sellingCompany)
            return false
        if (this.completionDate == null) {
            if (o.completionDate != null)
                return false
        }
        else if (this.completionDate != o.completionDate)
            return false
        if (this.areaWays1Code == null) {
            if (o.areaWays1Code != null)
                return false
        }
        else if (this.areaWays1Code != o.areaWays1Code)
            return false
        if (this.structureCode == null) {
            if (o.structureCode != null)
                return false
        }
        else if (this.structureCode != o.structureCode)
            return false
        if (this.structureDispName == null) {
            if (o.structureDispName != null)
                return false
        }
        else if (this.structureDispName != o.structureDispName)
            return false
        if (this.buildingTypeCode == null) {
            if (o.buildingTypeCode != null)
                return false
        }
        else if (this.buildingTypeCode != o.buildingTypeCode)
            return false
        if (this.allFloorNum == null) {
            if (o.allFloorNum != null)
                return false
        }
        else if (this.allFloorNum != o.allFloorNum)
            return false
        if (this.underFloorNum == null) {
            if (o.underFloorNum != null)
                return false
        }
        else if (this.underFloorNum != o.underFloorNum)
            return false
        if (this.newUsedCode == null) {
            if (o.newUsedCode != null)
                return false
        }
        else if (this.newUsedCode != o.newUsedCode)
            return false
        if (this.managerStyleCode == null) {
            if (o.managerStyleCode != null)
                return false
        }
        else if (this.managerStyleCode != o.managerStyleCode)
            return false
        if (this.managerComment == null) {
            if (o.managerComment != null)
                return false
        }
        else if (this.managerComment != o.managerComment)
            return false
        if (this.quietCode == null) {
            if (o.quietCode != null)
                return false
        }
        else if (this.quietCode != o.quietCode)
            return false
        if (this.gasCode == null) {
            if (o.gasCode != null)
                return false
        }
        else if (this.gasCode != o.gasCode)
            return false
        if (this.waterSupplyCode == null) {
            if (o.waterSupplyCode != null)
                return false
        }
        else if (this.waterSupplyCode != o.waterSupplyCode)
            return false
        if (this.wasteWaterCode == null) {
            if (o.wasteWaterCode != null)
                return false
        }
        else if (this.wasteWaterCode != o.wasteWaterCode)
            return false
        if (this.elecPowerCode == null) {
            if (o.elecPowerCode != null)
                return false
        }
        else if (this.elecPowerCode != o.elecPowerCode)
            return false
        if (this.twoByFourCode == null) {
            if (o.twoByFourCode != null)
                return false
        }
        else if (this.twoByFourCode != o.twoByFourCode)
            return false
        if (this.sellTypeCode == null) {
            if (o.sellTypeCode != null)
                return false
        }
        else if (this.sellTypeCode != o.sellTypeCode)
            return false
        if (this.avoidQuakeCode == null) {
            if (o.avoidQuakeCode != null)
                return false
        }
        else if (this.avoidQuakeCode != o.avoidQuakeCode)
            return false
        if (this.barrierFreeCode == null) {
            if (o.barrierFreeCode != null)
                return false
        }
        else if (this.barrierFreeCode != o.barrierFreeCode)
            return false
        if (this.fulltimeManagementCode == null) {
            if (o.fulltimeManagementCode != null)
                return false
        }
        else if (this.fulltimeManagementCode != o.fulltimeManagementCode)
            return false
        if (this.liftCode == null) {
            if (o.liftCode != null)
                return false
        }
        else if (this.liftCode != o.liftCode)
            return false
        if (this.liftNumCode == null) {
            if (o.liftNumCode != null)
                return false
        }
        else if (this.liftNumCode != o.liftNumCode)
            return false
        if (this.wallTypeCode == null) {
            if (o.wallTypeCode != null)
                return false
        }
        else if (this.wallTypeCode != o.wallTypeCode)
            return false
        if (this.deliveryMailboxCode == null) {
            if (o.deliveryMailboxCode != null)
                return false
        }
        else if (this.deliveryMailboxCode != o.deliveryMailboxCode)
            return false
        if (this.launderetteCode == null) {
            if (o.launderetteCode != null)
                return false
        }
        else if (this.launderetteCode != o.launderetteCode)
            return false
        if (this.roomNumberText == null) {
            if (o.roomNumberText != null)
                return false
        }
        else if (this.roomNumberText != o.roomNumberText)
            return false
        if (this.dispRoomNumberCode == null) {
            if (o.dispRoomNumberCode != null)
                return false
        }
        else if (this.dispRoomNumberCode != o.dispRoomNumberCode)
            return false
        if (this.salesPoint == null) {
            if (o.salesPoint != null)
                return false
        }
        else if (this.salesPoint != o.salesPoint)
            return false
        if (this.remark1 == null) {
            if (o.remark1 != null)
                return false
        }
        else if (this.remark1 != o.remark1)
            return false
        if (this.remark2 == null) {
            if (o.remark2 != null)
                return false
        }
        else if (this.remark2 != o.remark2)
            return false
        if (this.specialRemark == null) {
            if (o.specialRemark != null)
                return false
        }
        else if (this.specialRemark != o.specialRemark)
            return false
        if (this.note == null) {
            if (o.note != null)
                return false
        }
        else if (this.note != o.note)
            return false
        if (this.price == null) {
            if (o.price != null)
                return false
        }
        else if (this.price != o.price)
            return false
        if (this.priceTaxCode == null) {
            if (o.priceTaxCode != null)
                return false
        }
        else if (this.priceTaxCode != o.priceTaxCode)
            return false
        if (this.consumptionTax == null) {
            if (o.consumptionTax != null)
                return false
        }
        else if (this.consumptionTax != o.consumptionTax)
            return false
        if (this.queryPerson == null) {
            if (o.queryPerson != null)
                return false
        }
        else if (this.queryPerson != o.queryPerson)
            return false
        if (this.firmSideCode == null) {
            if (o.firmSideCode != null)
                return false
        }
        else if (this.firmSideCode != o.firmSideCode)
            return false
        if (this.intoCode == null) {
            if (o.intoCode != null)
                return false
        }
        else if (this.intoCode != o.intoCode)
            return false
        if (this.intoDate == null) {
            if (o.intoDate != null)
                return false
        }
        else if (this.intoDate != o.intoDate)
            return false
        if (this.leaveDate == null) {
            if (o.leaveDate != null)
                return false
        }
        else if (this.leaveDate != o.leaveDate)
            return false
        if (this.otherCompanyCode == null) {
            if (o.otherCompanyCode != null)
                return false
        }
        else if (this.otherCompanyCode != o.otherCompanyCode)
            return false
        if (this.messageToOtherCompany == null) {
            if (o.messageToOtherCompany != null)
                return false
        }
        else if (this.messageToOtherCompany != o.messageToOtherCompany)
            return false
        if (this.registDate == null) {
            if (o.registDate != null)
                return false
        }
        else if (this.registDate != o.registDate)
            return false
        if (this.registTime == null) {
            if (o.registTime != null)
                return false
        }
        else if (this.registTime != o.registTime)
            return false
        if (this.rentExchangeStyleCode == null) {
            if (o.rentExchangeStyleCode != null)
                return false
        }
        else if (this.rentExchangeStyleCode != o.rentExchangeStyleCode)
            return false
        if (this.housePlanCode == null) {
            if (o.housePlanCode != null)
                return false
        }
        else if (this.housePlanCode != o.housePlanCode)
            return false
        if (this.roomNum == null) {
            if (o.roomNum != null)
                return false
        }
        else if (this.roomNum != o.roomNum)
            return false
        if (this.housePlanEquiv == null) {
            if (o.housePlanEquiv != null)
                return false
        }
        else if (this.housePlanEquiv != o.housePlanEquiv)
            return false
        if (this.windowDirectionCode == null) {
            if (o.windowDirectionCode != null)
                return false
        }
        else if (this.windowDirectionCode != o.windowDirectionCode)
            return false
        if (this.floorNum == null) {
            if (o.floorNum != null)
                return false
        }
        else if (this.floorNum != o.floorNum)
            return false
        if (this.nonMoveintoCode == null) {
            if (o.nonMoveintoCode != null)
                return false
        }
        else if (this.nonMoveintoCode != o.nonMoveintoCode)
            return false
        if (this.managedPropertyCode == null) {
            if (o.managedPropertyCode != null)
                return false
        }
        else if (this.managedPropertyCode != o.managedPropertyCode)
            return false
        if (this.petCode == null) {
            if (o.petCode != null)
                return false
        }
        else if (this.petCode != o.petCode)
            return false
        if (this.officeCode == null) {
            if (o.officeCode != null)
                return false
        }
        else if (this.officeCode != o.officeCode)
            return false
        if (this.musicalCode == null) {
            if (o.musicalCode != null)
                return false
        }
        else if (this.musicalCode != o.musicalCode)
            return false
        if (this.housePlanDispName == null) {
            if (o.housePlanDispName != null)
                return false
        }
        else if (this.housePlanDispName != o.housePlanDispName)
            return false
        if (this.usePartArea == null) {
            if (o.usePartArea != null)
                return false
        }
        else if (this.usePartArea != o.usePartArea)
            return false
        if (this.keyMoney == null) {
            if (o.keyMoney != null)
                return false
        }
        else if (this.keyMoney != o.keyMoney)
            return false
        if (this.keyMoneyUnitCode == null) {
            if (o.keyMoneyUnitCode != null)
                return false
        }
        else if (this.keyMoneyUnitCode != o.keyMoneyUnitCode)
            return false
        if (this.keyMoneyTaxCode == null) {
            if (o.keyMoneyTaxCode != null)
                return false
        }
        else if (this.keyMoneyTaxCode != o.keyMoneyTaxCode)
            return false
        if (this.deposit == null) {
            if (o.deposit != null)
                return false
        }
        else if (this.deposit != o.deposit)
            return false
        if (this.depositUnitCode == null) {
            if (o.depositUnitCode != null)
                return false
        }
        else if (this.depositUnitCode != o.depositUnitCode)
            return false
        if (this.repairCost == null) {
            if (o.repairCost != null)
                return false
        }
        else if (this.repairCost != o.repairCost)
            return false
        if (this.repairCostUnitCode == null) {
            if (o.repairCostUnitCode != null)
                return false
        }
        else if (this.repairCostUnitCode != o.repairCostUnitCode)
            return false
        if (this.guaranty == null) {
            if (o.guaranty != null)
                return false
        }
        else if (this.guaranty != o.guaranty)
            return false
        if (this.guarantyUnitCode == null) {
            if (o.guarantyUnitCode != null)
                return false
        }
        else if (this.guarantyUnitCode != o.guarantyUnitCode)
            return false
        if (this.syokyakuClassCode == null) {
            if (o.syokyakuClassCode != null)
                return false
        }
        else if (this.syokyakuClassCode != o.syokyakuClassCode)
            return false
        if (this.syokyaku == null) {
            if (o.syokyaku != null)
                return false
        }
        else if (this.syokyaku != o.syokyaku)
            return false
        if (this.syokyakuUnitCode == null) {
            if (o.syokyakuUnitCode != null)
                return false
        }
        else if (this.syokyakuUnitCode != o.syokyakuUnitCode)
            return false
        if (this.premium == null) {
            if (o.premium != null)
                return false
        }
        else if (this.premium != o.premium)
            return false
        if (this.premiumUnitCode == null) {
            if (o.premiumUnitCode != null)
                return false
        }
        else if (this.premiumUnitCode != o.premiumUnitCode)
            return false
        if (this.premiumTaxCode == null) {
            if (o.premiumTaxCode != null)
                return false
        }
        else if (this.premiumTaxCode != o.premiumTaxCode)
            return false
        if (this.manageCost == null) {
            if (o.manageCost != null)
                return false
        }
        else if (this.manageCost != o.manageCost)
            return false
        if (this.manageCostTaxCode == null) {
            if (o.manageCostTaxCode != null)
                return false
        }
        else if (this.manageCostTaxCode != o.manageCostTaxCode)
            return false
        if (this.serviceFee == null) {
            if (o.serviceFee != null)
                return false
        }
        else if (this.serviceFee != o.serviceFee)
            return false
        if (this.serviceFeeFreeCode == null) {
            if (o.serviceFeeFreeCode != null)
                return false
        }
        else if (this.serviceFeeFreeCode != o.serviceFeeFreeCode)
            return false
        if (this.serviceFeeTaxCode == null) {
            if (o.serviceFeeTaxCode != null)
                return false
        }
        else if (this.serviceFeeTaxCode != o.serviceFeeTaxCode)
            return false
        if (this.zappi == null) {
            if (o.zappi != null)
                return false
        }
        else if (this.zappi != o.zappi)
            return false
        if (this.zappiTaxCode == null) {
            if (o.zappiTaxCode != null)
                return false
        }
        else if (this.zappiTaxCode != o.zappiTaxCode)
            return false
        if (this.otherCostComment == null) {
            if (o.otherCostComment != null)
                return false
        }
        else if (this.otherCostComment != o.otherCostComment)
            return false
        if (this.otherCost_1 == null) {
            if (o.otherCost_1 != null)
                return false
        }
        else if (this.otherCost_1 != o.otherCost_1)
            return false
        if (this.otherCostItem_1 == null) {
            if (o.otherCostItem_1 != null)
                return false
        }
        else if (this.otherCostItem_1 != o.otherCostItem_1)
            return false
        if (this.otherCostTaxCode_1 == null) {
            if (o.otherCostTaxCode_1 != null)
                return false
        }
        else if (this.otherCostTaxCode_1 != o.otherCostTaxCode_1)
            return false
        if (this.otherCost_2 == null) {
            if (o.otherCost_2 != null)
                return false
        }
        else if (this.otherCost_2 != o.otherCost_2)
            return false
        if (this.otherCostItem_2 == null) {
            if (o.otherCostItem_2 != null)
                return false
        }
        else if (this.otherCostItem_2 != o.otherCostItem_2)
            return false
        if (this.otherCostTaxCode_2 == null) {
            if (o.otherCostTaxCode_2 != null)
                return false
        }
        else if (this.otherCostTaxCode_2 != o.otherCostTaxCode_2)
            return false
        if (this.otherCost_3 == null) {
            if (o.otherCost_3 != null)
                return false
        }
        else if (this.otherCost_3 != o.otherCost_3)
            return false
        if (this.otherCostItem_3 == null) {
            if (o.otherCostItem_3 != null)
                return false
        }
        else if (this.otherCostItem_3 != o.otherCostItem_3)
            return false
        if (this.otherCostTaxCode_3 == null) {
            if (o.otherCostTaxCode_3 != null)
                return false
        }
        else if (this.otherCostTaxCode_3 != o.otherCostTaxCode_3)
            return false
        if (this.otherCost_4 == null) {
            if (o.otherCost_4 != null)
                return false
        }
        else if (this.otherCost_4 != o.otherCost_4)
            return false
        if (this.otherCostItem_4 == null) {
            if (o.otherCostItem_4 != null)
                return false
        }
        else if (this.otherCostItem_4 != o.otherCostItem_4)
            return false
        if (this.otherCostTaxCode_4 == null) {
            if (o.otherCostTaxCode_4 != null)
                return false
        }
        else if (this.otherCostTaxCode_4 != o.otherCostTaxCode_4)
            return false
        if (this.otherCost_5 == null) {
            if (o.otherCost_5 != null)
                return false
        }
        else if (this.otherCost_5 != o.otherCost_5)
            return false
        if (this.otherCostItem_5 == null) {
            if (o.otherCostItem_5 != null)
                return false
        }
        else if (this.otherCostItem_5 != o.otherCostItem_5)
            return false
        if (this.otherCostTaxCode_5 == null) {
            if (o.otherCostTaxCode_5 != null)
                return false
        }
        else if (this.otherCostTaxCode_5 != o.otherCostTaxCode_5)
            return false
        if (this.outerFacilityCode_1 == null) {
            if (o.outerFacilityCode_1 != null)
                return false
        }
        else if (this.outerFacilityCode_1 != o.outerFacilityCode_1)
            return false
        if (this.outerFacilityCode_2 == null) {
            if (o.outerFacilityCode_2 != null)
                return false
        }
        else if (this.outerFacilityCode_2 != o.outerFacilityCode_2)
            return false
        if (this.outerArea_2 == null) {
            if (o.outerArea_2 != null)
                return false
        }
        else if (this.outerArea_2 != o.outerArea_2)
            return false
        if (this.renewalFee == null) {
            if (o.renewalFee != null)
                return false
        }
        else if (this.renewalFee != o.renewalFee)
            return false
        if (this.renewalFeeUnitCode == null) {
            if (o.renewalFeeUnitCode != null)
                return false
        }
        else if (this.renewalFeeUnitCode != o.renewalFeeUnitCode)
            return false
        if (this.renewalFeeClassCode == null) {
            if (o.renewalFeeClassCode != null)
                return false
        }
        else if (this.renewalFeeClassCode != o.renewalFeeClassCode)
            return false
        if (this.houseRentLimitDate == null) {
            if (o.houseRentLimitDate != null)
                return false
        }
        else if (this.houseRentLimitDate != o.houseRentLimitDate)
            return false
        if (this.insuranceCode == null) {
            if (o.insuranceCode != null)
                return false
        }
        else if (this.insuranceCode != o.insuranceCode)
            return false
        if (this.specialRentalLowerCost == null) {
            if (o.specialRentalLowerCost != null)
                return false
        }
        else if (this.specialRentalLowerCost != o.specialRentalLowerCost)
            return false
        if (this.specialRentalUpperCost == null) {
            if (o.specialRentalUpperCost != null)
                return false
        }
        else if (this.specialRentalUpperCost != o.specialRentalUpperCost)
            return false
        if (this.additionalDepositUnitCode == null) {
            if (o.additionalDepositUnitCode != null)
                return false
        }
        else if (this.additionalDepositUnitCode != o.additionalDepositUnitCode)
            return false
        if (this.additionalDepositReasonCode == null) {
            if (o.additionalDepositReasonCode != null)
                return false
        }
        else if (this.additionalDepositReasonCode != o.additionalDepositReasonCode)
            return false
        if (this.brokerage == null) {
            if (o.brokerage != null)
                return false
        }
        else if (this.brokerage != o.brokerage)
            return false
        if (this.brokerageUnitCode == null) {
            if (o.brokerageUnitCode != null)
                return false
        }
        else if (this.brokerageUnitCode != o.brokerageUnitCode)
            return false
        if (this.renewalCharge == null) {
            if (o.renewalCharge != null)
                return false
        }
        else if (this.renewalCharge != o.renewalCharge)
            return false
        if (this.renewalChargeUnitCode == null) {
            if (o.renewalChargeUnitCode != null)
                return false
        }
        else if (this.renewalChargeUnitCode != o.renewalChargeUnitCode)
            return false
        if (this.studentOnlyCode == null) {
            if (o.studentOnlyCode != null)
                return false
        }
        else if (this.studentOnlyCode != o.studentOnlyCode)
            return false
        if (this.sexConditionCode == null) {
            if (o.sexConditionCode != null)
                return false
        }
        else if (this.sexConditionCode != o.sexConditionCode)
            return false
        if (this.kidsCode == null) {
            if (o.kidsCode != null)
                return false
        }
        else if (this.kidsCode != o.kidsCode)
            return false
        if (this.aloneCode == null) {
            if (o.aloneCode != null)
                return false
        }
        else if (this.aloneCode != o.aloneCode)
            return false
        if (this.twoPeopleCode == null) {
            if (o.twoPeopleCode != null)
                return false
        }
        else if (this.twoPeopleCode != o.twoPeopleCode)
            return false
        if (this.elderCode == null) {
            if (o.elderCode != null)
                return false
        }
        else if (this.elderCode != o.elderCode)
            return false
        if (this.corporationOnlyCode == null) {
            if (o.corporationOnlyCode != null)
                return false
        }
        else if (this.corporationOnlyCode != o.corporationOnlyCode)
            return false
        if (this.residenceHouseRentCode == null) {
            if (o.residenceHouseRentCode != null)
                return false
        }
        else if (this.residenceHouseRentCode != o.residenceHouseRentCode)
            return false
        if (this.roomStyleCode_1 == null) {
            if (o.roomStyleCode_1 != null)
                return false
        }
        else if (this.roomStyleCode_1 != o.roomStyleCode_1)
            return false
        if (this.roomArea_1 == null) {
            if (o.roomArea_1 != null)
                return false
        }
        else if (this.roomArea_1 != o.roomArea_1)
            return false
        if (this.roomUnitCode_1 == null) {
            if (o.roomUnitCode_1 != null)
                return false
        }
        else if (this.roomUnitCode_1 != o.roomUnitCode_1)
            return false
        if (this.roomStyleCode_2 == null) {
            if (o.roomStyleCode_2 != null)
                return false
        }
        else if (this.roomStyleCode_2 != o.roomStyleCode_2)
            return false
        if (this.roomArea_2 == null) {
            if (o.roomArea_2 != null)
                return false
        }
        else if (this.roomArea_2 != o.roomArea_2)
            return false
        if (this.roomUnitCode_2 == null) {
            if (o.roomUnitCode_2 != null)
                return false
        }
        else if (this.roomUnitCode_2 != o.roomUnitCode_2)
            return false
        if (this.roomStyleCode_3 == null) {
            if (o.roomStyleCode_3 != null)
                return false
        }
        else if (this.roomStyleCode_3 != o.roomStyleCode_3)
            return false
        if (this.roomArea_3 == null) {
            if (o.roomArea_3 != null)
                return false
        }
        else if (this.roomArea_3 != o.roomArea_3)
            return false
        if (this.roomUnitCode_3 == null) {
            if (o.roomUnitCode_3 != null)
                return false
        }
        else if (this.roomUnitCode_3 != o.roomUnitCode_3)
            return false
        if (this.roomStyleCode_4 == null) {
            if (o.roomStyleCode_4 != null)
                return false
        }
        else if (this.roomStyleCode_4 != o.roomStyleCode_4)
            return false
        if (this.roomArea_4 == null) {
            if (o.roomArea_4 != null)
                return false
        }
        else if (this.roomArea_4 != o.roomArea_4)
            return false
        if (this.roomUnitCode_4 == null) {
            if (o.roomUnitCode_4 != null)
                return false
        }
        else if (this.roomUnitCode_4 != o.roomUnitCode_4)
            return false
        if (this.roomStyleCode_5 == null) {
            if (o.roomStyleCode_5 != null)
                return false
        }
        else if (this.roomStyleCode_5 != o.roomStyleCode_5)
            return false
        if (this.roomArea_5 == null) {
            if (o.roomArea_5 != null)
                return false
        }
        else if (this.roomArea_5 != o.roomArea_5)
            return false
        if (this.roomUnitCode_5 == null) {
            if (o.roomUnitCode_5 != null)
                return false
        }
        else if (this.roomUnitCode_5 != o.roomUnitCode_5)
            return false
        if (this.roomStyleCode_6 == null) {
            if (o.roomStyleCode_6 != null)
                return false
        }
        else if (this.roomStyleCode_6 != o.roomStyleCode_6)
            return false
        if (this.roomArea_6 == null) {
            if (o.roomArea_6 != null)
                return false
        }
        else if (this.roomArea_6 != o.roomArea_6)
            return false
        if (this.roomUnitCode_6 == null) {
            if (o.roomUnitCode_6 != null)
                return false
        }
        else if (this.roomUnitCode_6 != o.roomUnitCode_6)
            return false
        if (this.roomStyleCode_7 == null) {
            if (o.roomStyleCode_7 != null)
                return false
        }
        else if (this.roomStyleCode_7 != o.roomStyleCode_7)
            return false
        if (this.roomArea_7 == null) {
            if (o.roomArea_7 != null)
                return false
        }
        else if (this.roomArea_7 != o.roomArea_7)
            return false
        if (this.roomUnitCode_7 == null) {
            if (o.roomUnitCode_7 != null)
                return false
        }
        else if (this.roomUnitCode_7 != o.roomUnitCode_7)
            return false
        if (this.roomStyleCode_8 == null) {
            if (o.roomStyleCode_8 != null)
                return false
        }
        else if (this.roomStyleCode_8 != o.roomStyleCode_8)
            return false
        if (this.roomArea_8 == null) {
            if (o.roomArea_8 != null)
                return false
        }
        else if (this.roomArea_8 != o.roomArea_8)
            return false
        if (this.roomUnitCode_8 == null) {
            if (o.roomUnitCode_8 != null)
                return false
        }
        else if (this.roomUnitCode_8 != o.roomUnitCode_8)
            return false
        if (this.roomStyleCode_9 == null) {
            if (o.roomStyleCode_9 != null)
                return false
        }
        else if (this.roomStyleCode_9 != o.roomStyleCode_9)
            return false
        if (this.roomArea_9 == null) {
            if (o.roomArea_9 != null)
                return false
        }
        else if (this.roomArea_9 != o.roomArea_9)
            return false
        if (this.roomUnitCode_9 == null) {
            if (o.roomUnitCode_9 != null)
                return false
        }
        else if (this.roomUnitCode_9 != o.roomUnitCode_9)
            return false
        if (this.roomStyleCode_10 == null) {
            if (o.roomStyleCode_10 != null)
                return false
        }
        else if (this.roomStyleCode_10 != o.roomStyleCode_10)
            return false
        if (this.roomArea_10 == null) {
            if (o.roomArea_10 != null)
                return false
        }
        else if (this.roomArea_10 != o.roomArea_10)
            return false
        if (this.roomUnitCode_10 == null) {
            if (o.roomUnitCode_10 != null)
                return false
        }
        else if (this.roomUnitCode_10 != o.roomUnitCode_10)
            return false
        if (this.parkingCode == null) {
            if (o.parkingCode != null)
                return false
        }
        else if (this.parkingCode != o.parkingCode)
            return false
        if (this.fromNearParking == null) {
            if (o.fromNearParking != null)
                return false
        }
        else if (this.fromNearParking != o.fromNearParking)
            return false
        if (this.parkingNum == null) {
            if (o.parkingNum != null)
                return false
        }
        else if (this.parkingNum != o.parkingNum)
            return false
        if (this.parkingTypeCode == null) {
            if (o.parkingTypeCode != null)
                return false
        }
        else if (this.parkingTypeCode != o.parkingTypeCode)
            return false
        if (this.parkingShutterCode == null) {
            if (o.parkingShutterCode != null)
                return false
        }
        else if (this.parkingShutterCode != o.parkingShutterCode)
            return false
        if (this.parkingLowerCost == null) {
            if (o.parkingLowerCost != null)
                return false
        }
        else if (this.parkingLowerCost != o.parkingLowerCost)
            return false
        if (this.parkingTaxCode == null) {
            if (o.parkingTaxCode != null)
                return false
        }
        else if (this.parkingTaxCode != o.parkingTaxCode)
            return false
        if (this.parkableNumCode == null) {
            if (o.parkableNumCode != null)
                return false
        }
        else if (this.parkableNumCode != o.parkableNumCode)
            return false
        if (this.parkingFreeCode == null) {
            if (o.parkingFreeCode != null)
                return false
        }
        else if (this.parkingFreeCode != o.parkingFreeCode)
            return false
        if (this.bikeParkCode == null) {
            if (o.bikeParkCode != null)
                return false
        }
        else if (this.bikeParkCode != o.bikeParkCode)
            return false
        if (this.bikeParkCost == null) {
            if (o.bikeParkCost != null)
                return false
        }
        else if (this.bikeParkCost != o.bikeParkCost)
            return false
        if (this.motorbikeParkCode == null) {
            if (o.motorbikeParkCode != null)
                return false
        }
        else if (this.motorbikeParkCode != o.motorbikeParkCode)
            return false
        if (this.motorbikeCost == null) {
            if (o.motorbikeCost != null)
                return false
        }
        else if (this.motorbikeCost != o.motorbikeCost)
            return false
        if (this.airconCode == null) {
            if (o.airconCode != null)
                return false
        }
        else if (this.airconCode != o.airconCode)
            return false
        if (this.coolerCode == null) {
            if (o.coolerCode != null)
                return false
        }
        else if (this.coolerCode != o.coolerCode)
            return false
        if (this.heatingCode == null) {
            if (o.heatingCode != null)
                return false
        }
        else if (this.heatingCode != o.heatingCode)
            return false
        if (this.loadHeaterCode == null) {
            if (o.loadHeaterCode != null)
                return false
        }
        else if (this.loadHeaterCode != o.loadHeaterCode)
            return false
        if (this.stoveCode == null) {
            if (o.stoveCode != null)
                return false
        }
        else if (this.stoveCode != o.stoveCode)
            return false
        if (this.floorHeatingCode == null) {
            if (o.floorHeatingCode != null)
                return false
        }
        else if (this.floorHeatingCode != o.floorHeatingCode)
            return false
        if (this.catvCode == null) {
            if (o.catvCode != null)
                return false
        }
        else if (this.catvCode != o.catvCode)
            return false
        if (this.communityBroadcastCode == null) {
            if (o.communityBroadcastCode != null)
                return false
        }
        else if (this.communityBroadcastCode != o.communityBroadcastCode)
            return false
        if (this.bsCode == null) {
            if (o.bsCode != null)
                return false
        }
        else if (this.bsCode != o.bsCode)
            return false
        if (this.csCode == null) {
            if (o.csCode != null)
                return false
        }
        else if (this.csCode != o.csCode)
            return false
        if (this.internetCode == null) {
            if (o.internetCode != null)
                return false
        }
        else if (this.internetCode != o.internetCode)
            return false
        if (this.closetCode == null) {
            if (o.closetCode != null)
                return false
        }
        else if (this.closetCode != o.closetCode)
            return false
        if (this.walkinWardrobeCode == null) {
            if (o.walkinWardrobeCode != null)
                return false
        }
        else if (this.walkinWardrobeCode != o.walkinWardrobeCode)
            return false
        if (this.closetUnderFloorCode == null) {
            if (o.closetUnderFloorCode != null)
                return false
        }
        else if (this.closetUnderFloorCode != o.closetUnderFloorCode)
            return false
        if (this.trunkRoomCode == null) {
            if (o.trunkRoomCode != null)
                return false
        }
        else if (this.trunkRoomCode != o.trunkRoomCode)
            return false
        if (this.oshiireCode == null) {
            if (o.oshiireCode != null)
                return false
        }
        else if (this.oshiireCode != o.oshiireCode)
            return false
        if (this.garretClosetCode == null) {
            if (o.garretClosetCode != null)
                return false
        }
        else if (this.garretClosetCode != o.garretClosetCode)
            return false
        if (this.shoeCupboardCode == null) {
            if (o.shoeCupboardCode != null)
                return false
        }
        else if (this.shoeCupboardCode != o.shoeCupboardCode)
            return false
        if (this.storeroomCode == null) {
            if (o.storeroomCode != null)
                return false
        }
        else if (this.storeroomCode != o.storeroomCode)
            return false
        if (this.bathToiletCode == null) {
            if (o.bathToiletCode != null)
                return false
        }
        else if (this.bathToiletCode != o.bathToiletCode)
            return false
        if (this.bathCode == null) {
            if (o.bathCode != null)
                return false
        }
        else if (this.bathCode != o.bathCode)
            return false
        if (this.showerCode == null) {
            if (o.showerCode != null)
                return false
        }
        else if (this.showerCode != o.showerCode)
            return false
        if (this.autoBathCode == null) {
            if (o.autoBathCode != null)
                return false
        }
        else if (this.autoBathCode != o.autoBathCode)
            return false
        if (this.dressingRoomCode == null) {
            if (o.dressingRoomCode != null)
                return false
        }
        else if (this.dressingRoomCode != o.dressingRoomCode)
            return false
        if (this.reboilBathCode == null) {
            if (o.reboilBathCode != null)
                return false
        }
        else if (this.reboilBathCode != o.reboilBathCode)
            return false
        if (this.toiletCode == null) {
            if (o.toiletCode != null)
                return false
        }
        else if (this.toiletCode != o.toiletCode)
            return false
        if (this.bathDrierCode == null) {
            if (o.bathDrierCode != null)
                return false
        }
        else if (this.bathDrierCode != o.bathDrierCode)
            return false
        if (this.shampooDresserCode == null) {
            if (o.shampooDresserCode != null)
                return false
        }
        else if (this.shampooDresserCode != o.shampooDresserCode)
            return false
        if (this.washletCode == null) {
            if (o.washletCode != null)
                return false
        }
        else if (this.washletCode != o.washletCode)
            return false
        if (this.bathOver_1tsuboCode == null) {
            if (o.bathOver_1tsuboCode != null)
                return false
        }
        else if (this.bathOver_1tsuboCode != o.bathOver_1tsuboCode)
            return false
        if (this.warmletCode == null) {
            if (o.warmletCode != null)
                return false
        }
        else if (this.warmletCode != o.warmletCode)
            return false
        if (this.cookingStoveCode == null) {
            if (o.cookingStoveCode != null)
                return false
        }
        else if (this.cookingStoveCode != o.cookingStoveCode)
            return false
        if (this.kitchenCode == null) {
            if (o.kitchenCode != null)
                return false
        }
        else if (this.kitchenCode != o.kitchenCode)
            return false
        if (this.microwaveOvenCode == null) {
            if (o.microwaveOvenCode != null)
                return false
        }
        else if (this.microwaveOvenCode != o.microwaveOvenCode)
            return false
        if (this.ihCookingHeaterCode == null) {
            if (o.ihCookingHeaterCode != null)
                return false
        }
        else if (this.ihCookingHeaterCode != o.ihCookingHeaterCode)
            return false
        if (this.coldStorageCode == null) {
            if (o.coldStorageCode != null)
                return false
        }
        else if (this.coldStorageCode != o.coldStorageCode)
            return false
        if (this.grillCode == null) {
            if (o.grillCode != null)
                return false
        }
        else if (this.grillCode != o.grillCode)
            return false
        if (this.disposerCode == null) {
            if (o.disposerCode != null)
                return false
        }
        else if (this.disposerCode != o.disposerCode)
            return false
        if (this.dishWasherCode == null) {
            if (o.dishWasherCode != null)
                return false
        }
        else if (this.dishWasherCode != o.dishWasherCode)
            return false
        if (this.waterCleanerCode == null) {
            if (o.waterCleanerCode != null)
                return false
        }
        else if (this.waterCleanerCode != o.waterCleanerCode)
            return false
        if (this.woodenFloorCode == null) {
            if (o.woodenFloorCode != null)
                return false
        }
        else if (this.woodenFloorCode != o.woodenFloorCode)
            return false
        if (this.loftCode == null) {
            if (o.loftCode != null)
                return false
        }
        else if (this.loftCode != o.loftCode)
            return false
        if (this.cushionFloorCode == null) {
            if (o.cushionFloorCode != null)
                return false
        }
        else if (this.cushionFloorCode != o.cushionFloorCode)
            return false
        if (this.highestFloorCode == null) {
            if (o.highestFloorCode != null)
                return false
        }
        else if (this.highestFloorCode != o.highestFloorCode)
            return false
        if (this.maisonetteCode == null) {
            if (o.maisonetteCode != null)
                return false
        }
        else if (this.maisonetteCode != o.maisonetteCode)
            return false
        if (this.overSecondFloorCode == null) {
            if (o.overSecondFloorCode != null)
                return false
        }
        else if (this.overSecondFloorCode != o.overSecondFloorCode)
            return false
        if (this.caveCode == null) {
            if (o.caveCode != null)
                return false
        }
        else if (this.caveCode != o.caveCode)
            return false
        if (this.soundproofCode == null) {
            if (o.soundproofCode != null)
                return false
        }
        else if (this.soundproofCode != o.soundproofCode)
            return false
        if (this.cornerHouseCode == null) {
            if (o.cornerHouseCode != null)
                return false
        }
        else if (this.cornerHouseCode != o.cornerHouseCode)
            return false
        if (this.sunroomCode == null) {
            if (o.sunroomCode != null)
                return false
        }
        else if (this.sunroomCode != o.sunroomCode)
            return false
        if (this.basementCode == null) {
            if (o.basementCode != null)
                return false
        }
        else if (this.basementCode != o.basementCode)
            return false
        if (this.southRoomCode == null) {
            if (o.southRoomCode != null)
                return false
        }
        else if (this.southRoomCode != o.southRoomCode)
            return false
        if (this.patioCode == null) {
            if (o.patioCode != null)
                return false
        }
        else if (this.patioCode != o.patioCode)
            return false
        if (this.crimePrevShutterCode == null) {
            if (o.crimePrevShutterCode != null)
                return false
        }
        else if (this.crimePrevShutterCode != o.crimePrevShutterCode)
            return false
        if (this.crimePrevCameraCode == null) {
            if (o.crimePrevCameraCode != null)
                return false
        }
        else if (this.crimePrevCameraCode != o.crimePrevCameraCode)
            return false
        if (this.autolockCode == null) {
            if (o.autolockCode != null)
                return false
        }
        else if (this.autolockCode != o.autolockCode)
            return false
        if (this.doubleLockCode == null) {
            if (o.doubleLockCode != null)
                return false
        }
        else if (this.doubleLockCode != o.doubleLockCode)
            return false
        if (this.washingMachineCode == null) {
            if (o.washingMachineCode != null)
                return false
        }
        else if (this.washingMachineCode != o.washingMachineCode)
            return false
        if (this.drierCode == null) {
            if (o.drierCode != null)
                return false
        }
        else if (this.drierCode != o.drierCode)
            return false
        if (this.washingMachinePlaceCode == null) {
            if (o.washingMachinePlaceCode != null)
                return false
        }
        else if (this.washingMachinePlaceCode != o.washingMachinePlaceCode)
            return false
        if (this.cardKeyCode == null) {
            if (o.cardKeyCode != null)
                return false
        }
        else if (this.cardKeyCode != o.cardKeyCode)
            return false
        if (this.bowWindowCode == null) {
            if (o.bowWindowCode != null)
                return false
        }
        else if (this.bowWindowCode != o.bowWindowCode)
            return false
        if (this.lightCode == null) {
            if (o.lightCode != null)
                return false
        }
        else if (this.lightCode != o.lightCode)
            return false
        if (this.allElectricCode == null) {
            if (o.allElectricCode != null)
                return false
        }
        else if (this.allElectricCode != o.allElectricCode)
            return false
        if (this.hotWaterSupplyCode == null) {
            if (o.hotWaterSupplyCode != null)
                return false
        }
        else if (this.hotWaterSupplyCode != o.hotWaterSupplyCode)
            return false
        if (this.interphoneCode == null) {
            if (o.interphoneCode != null)
                return false
        }
        else if (this.interphoneCode != o.interphoneCode)
            return false
        if (this.fulltimeFunCode == null) {
            if (o.fulltimeFunCode != null)
                return false
        }
        else if (this.fulltimeFunCode != o.fulltimeFunCode)
            return false
        if (this.ecocuteCode == null) {
            if (o.ecocuteCode != null)
                return false
        }
        else if (this.ecocuteCode != o.ecocuteCode)
            return false
        if (this.doubleSideBalconyCode == null) {
            if (o.doubleSideBalconyCode != null)
                return false
        }
        else if (this.doubleSideBalconyCode != o.doubleSideBalconyCode)
            return false
        if (this.balconySideNumCode == null) {
            if (o.balconySideNumCode != null)
                return false
        }
        else if (this.balconySideNumCode != o.balconySideNumCode)
            return false
        if (this.bathTvCode == null) {
            if (o.bathTvCode != null)
                return false
        }
        else if (this.bathTvCode != o.bathTvCode)
            return false
        if (this.porchCode == null) {
            if (o.porchCode != null)
                return false
        }
        else if (this.porchCode != o.porchCode)
            return false
        if (this.upStartDate == null) {
            if (o.upStartDate != null)
                return false
        }
        else if (this.upStartDate != o.upStartDate)
            return false
        if (this.upEndDate == null) {
            if (o.upEndDate != null)
                return false
        }
        else if (this.upEndDate != o.upEndDate)
            return false
        if (this.dressingTableCode == null) {
            if (o.dressingTableCode != null)
                return false
        }
        else if (this.dressingTableCode != o.dressingTableCode)
            return false
        if (this.privateDustBoxCode == null) {
            if (o.privateDustBoxCode != null)
                return false
        }
        else if (this.privateDustBoxCode != o.privateDustBoxCode)
            return false
        if (this.pianoCode == null) {
            if (o.pianoCode != null)
                return false
        }
        else if (this.pianoCode != o.pianoCode)
            return false
        if (this.largeShoesBoxCode == null) {
            if (o.largeShoesBoxCode != null)
                return false
        }
        else if (this.largeShoesBoxCode != o.largeShoesBoxCode)
            return false
        if (this.closetUnderTatamiCode == null) {
            if (o.closetUnderTatamiCode != null)
                return false
        }
        else if (this.closetUnderTatamiCode != o.closetUnderTatamiCode)
            return false
        if (this.indoorsBicycleParkingCode == null) {
            if (o.indoorsBicycleParkingCode != null)
                return false
        }
        else if (this.indoorsBicycleParkingCode != o.indoorsBicycleParkingCode)
            return false
        if (this.securityKeyCode == null) {
            if (o.securityKeyCode != null)
                return false
        }
        else if (this.securityKeyCode != o.securityKeyCode)
            return false
        if (this.shutterCode == null) {
            if (o.shutterCode != null)
                return false
        }
        else if (this.shutterCode != o.shutterCode)
            return false
        if (this.forSouthCode == null) {
            if (o.forSouthCode != null)
                return false
        }
        else if (this.forSouthCode != o.forSouthCode)
            return false
        if (this.closetUnderstairCode == null) {
            if (o.closetUnderstairCode != null)
                return false
        }
        else if (this.closetUnderstairCode != o.closetUnderstairCode)
            return false
        if (this.nearbyConvenienceStoreCode == null) {
            if (o.nearbyConvenienceStoreCode != null)
                return false
        }
        else if (this.nearbyConvenienceStoreCode != o.nearbyConvenienceStoreCode)
            return false
        if (this.nearbyBankCode == null) {
            if (o.nearbyBankCode != null)
                return false
        }
        else if (this.nearbyBankCode != o.nearbyBankCode)
            return false
        if (this.nearbyRentalVideoCode == null) {
            if (o.nearbyRentalVideoCode != null)
                return false
        }
        else if (this.nearbyRentalVideoCode != o.nearbyRentalVideoCode)
            return false
        if (this.largeScaleRenewalCode == null) {
            if (o.largeScaleRenewalCode != null)
                return false
        }
        else if (this.largeScaleRenewalCode != o.largeScaleRenewalCode)
            return false
        if (this.recoveryCostCode == null) {
            if (o.recoveryCostCode != null)
                return false
        }
        else if (this.recoveryCostCode != o.recoveryCostCode)
            return false
        if (this.guarantorCode == null) {
            if (o.guarantorCode != null)
                return false
        }
        else if (this.guarantorCode != o.guarantorCode)
            return false
        if (this.guarantorProxyCode == null) {
            if (o.guarantorProxyCode != null)
                return false
        }
        else if (this.guarantorProxyCode != o.guarantorProxyCode)
            return false
        if (this.guarantorProxyComCode == null) {
            if (o.guarantorProxyComCode != null)
                return false
        }
        else if (this.guarantorProxyComCode != o.guarantorProxyComCode)
            return false
        if (this.guarantorProxyComment == null) {
            if (o.guarantorProxyComment != null)
                return false
        }
        else if (this.guarantorProxyComment != o.guarantorProxyComment)
            return false
        if (this.dispMapCode == null) {
            if (o.dispMapCode != null)
                return false
        }
        else if (this.dispMapCode != o.dispMapCode)
            return false
        if (this.latitudeWorld == null) {
            if (o.latitudeWorld != null)
                return false
        }
        else if (this.latitudeWorld != o.latitudeWorld)
            return false
        if (this.longitudeWorld == null) {
            if (o.longitudeWorld != null)
                return false
        }
        else if (this.longitudeWorld != o.longitudeWorld)
            return false
        if (this.gardenCode == null) {
            if (o.gardenCode != null)
                return false
        }
        else if (this.gardenCode != o.gardenCode)
            return false
        if (this.balconyCode == null) {
            if (o.balconyCode != null)
                return false
        }
        else if (this.balconyCode != o.balconyCode)
            return false
        if (this.panoramaId == null) {
            if (o.panoramaId != null)
                return false
        }
        else if (this.panoramaId != o.panoramaId)
            return false
        if (this.largeScaleRenewalDate == null) {
            if (o.largeScaleRenewalDate != null)
                return false
        }
        else if (this.largeScaleRenewalDate != o.largeScaleRenewalDate)
            return false
        if (this.shatakuKanouCode == null) {
            if (o.shatakuKanouCode != null)
                return false
        }
        else if (this.shatakuKanouCode != o.shatakuKanouCode)
            return false
        if (this.noDepositPlanCode == null) {
            if (o.noDepositPlanCode != null)
                return false
        }
        else if (this.noDepositPlanCode != o.noDepositPlanCode)
            return false
        if (this.kentakuKindCode == null) {
            if (o.kentakuKindCode != null)
                return false
        }
        else if (this.kentakuKindCode != o.kentakuKindCode)
            return false
        if (this.priceSaleFlag == null) {
            if (o.priceSaleFlag != null)
                return false
        }
        else if (this.priceSaleFlag != o.priceSaleFlag)
            return false
        if (this.refomeFlag == null) {
            if (o.refomeFlag != null)
                return false
        }
        else if (this.refomeFlag != o.refomeFlag)
            return false
        if (this.productCode == null) {
            if (o.productCode != null)
                return false
        }
        else if (this.productCode != o.productCode)
            return false
        if (this.ownerShipBranchCode == null) {
            if (o.ownerShipBranchCode != null)
                return false
        }
        else if (this.ownerShipBranchCode != o.ownerShipBranchCode)
            return false
        if (this.kentakuBuildingCode == null) {
            if (o.kentakuBuildingCode != null)
                return false
        }
        else if (this.kentakuBuildingCode != o.kentakuBuildingCode)
            return false
        if (this.kentakuRoomCode == null) {
            if (o.kentakuRoomCode != null)
                return false
        }
        else if (this.kentakuRoomCode != o.kentakuRoomCode)
            return false
        if (this.keyExchangeFreeCode == null) {
            if (o.keyExchangeFreeCode != null)
                return false
        }
        else if (this.keyExchangeFreeCode != o.keyExchangeFreeCode)
            return false
        if (this.adPrice == null) {
            if (o.adPrice != null)
                return false
        }
        else if (this.adPrice != o.adPrice)
            return false
        if (this.ffPrice == null) {
            if (o.ffPrice != null)
                return false
        }
        else if (this.ffPrice != o.ffPrice)
            return false
        if (this.leaveDateTp == null) {
            if (o.leaveDateTp != null)
                return false
        }
        else if (this.leaveDateTp != o.leaveDateTp)
            return false
        if (this.leaveFinishDate == null) {
            if (o.leaveFinishDate != null)
                return false
        }
        else if (this.leaveFinishDate != o.leaveFinishDate)
            return false
        if (this.lowParkingPrice == null) {
            if (o.lowParkingPrice != null)
                return false
        }
        else if (this.lowParkingPrice != o.lowParkingPrice)
            return false
        if (this.highParkingPrice == null) {
            if (o.highParkingPrice != null)
                return false
        }
        else if (this.highParkingPrice != o.highParkingPrice)
            return false
        if (this.structureDispNameTp == null) {
            if (o.structureDispNameTp != null)
                return false
        }
        else if (this.structureDispNameTp != o.structureDispNameTp)
            return false
        if (this.displaceCode == null) {
            if (o.displaceCode != null)
                return false
        }
        else if (this.displaceCode != o.displaceCode)
            return false
        if (this.financeCorporationCode == null) {
            if (o.financeCorporationCode != null)
                return false
        }
        else if (this.financeCorporationCode != o.financeCorporationCode)
            return false
        if (this.waterCompanyName == null) {
            if (o.waterCompanyName != null)
                return false
        }
        else if (this.waterCompanyName != o.waterCompanyName)
            return false
        if (this.waterCompanyTel == null) {
            if (o.waterCompanyTel != null)
                return false
        }
        else if (this.waterCompanyTel != o.waterCompanyTel)
            return false
        if (this.electricCompanyName == null) {
            if (o.electricCompanyName != null)
                return false
        }
        else if (this.electricCompanyName != o.electricCompanyName)
            return false
        if (this.electricCompanyTel == null) {
            if (o.electricCompanyTel != null)
                return false
        }
        else if (this.electricCompanyTel != o.electricCompanyTel)
            return false
        if (this.gasCompanyName == null) {
            if (o.gasCompanyName != null)
                return false
        }
        else if (this.gasCompanyName != o.gasCompanyName)
            return false
        if (this.gasCompanyTel == null) {
            if (o.gasCompanyTel != null)
                return false
        }
        else if (this.gasCompanyTel != o.gasCompanyTel)
            return false
        if (this.collectDate == null) {
            if (o.collectDate != null)
                return false
        }
        else if (this.collectDate != o.collectDate)
            return false
        if (this.kouentinCode == null) {
            if (o.kouentinCode != null)
                return false
        }
        else if (this.kouentinCode != o.kouentinCode)
            return false
        if (this.intoDateTxt == null) {
            if (o.intoDateTxt != null)
                return false
        }
        else if (this.intoDateTxt != o.intoDateTxt)
            return false
        if (this.roomSituationCode == null) {
            if (o.roomSituationCode != null)
                return false
        }
        else if (this.roomSituationCode != o.roomSituationCode)
            return false
        if (this.recordSituationCode == null) {
            if (o.recordSituationCode != null)
                return false
        }
        else if (this.recordSituationCode != o.recordSituationCode)
            return false
        if (this.electricDiscountFlag == null) {
            if (o.electricDiscountFlag != null)
                return false
        }
        else if (this.electricDiscountFlag != o.electricDiscountFlag)
            return false
        if (this.fletsHikariCode == null) {
            if (o.fletsHikariCode != null)
                return false
        }
        else if (this.fletsHikariCode != o.fletsHikariCode)
            return false
        if (this.akiyaTerm == null) {
            if (o.akiyaTerm != null)
                return false
        }
        else if (this.akiyaTerm != o.akiyaTerm)
            return false
        if (this.cleaningFeeCode == null) {
            if (o.cleaningFeeCode != null)
                return false
        }
        else if (this.cleaningFeeCode != o.cleaningFeeCode)
            return false
        if (this.cleaningFee == null) {
            if (o.cleaningFee != null)
                return false
        }
        else if (this.cleaningFee != o.cleaningFee)
            return false
        if (this.powerCode == null) {
            if (o.powerCode != null)
                return false
        }
        else if (this.powerCode != o.powerCode)
            return false
        if (this.fireZoneCode == null) {
            if (o.fireZoneCode != null)
                return false
        }
        else if (this.fireZoneCode != o.fireZoneCode)
            return false
        if (this.discountRate == null) {
            if (o.discountRate != null)
                return false
        }
        else if (this.discountRate != o.discountRate)
            return false
        if (this.discountTerm == null) {
            if (o.discountTerm != null)
                return false
        }
        else if (this.discountTerm != o.discountTerm)
            return false
        if (this.petFlag == null) {
            if (o.petFlag != null)
                return false
        }
        else if (this.petFlag != o.petFlag)
            return false
        if (this.internetFreeCode == null) {
            if (o.internetFreeCode != null)
                return false
        }
        else if (this.internetFreeCode != o.internetFreeCode)
            return false
        if (this.allRoomCloset == null) {
            if (o.allRoomCloset != null)
                return false
        }
        else if (this.allRoomCloset != o.allRoomCloset)
            return false
        if (this.walkThroughCloset == null) {
            if (o.walkThroughCloset != null)
                return false
        }
        else if (this.walkThroughCloset != o.walkThroughCloset)
            return false
        if (this.freeWashRoom == null) {
            if (o.freeWashRoom != null)
                return false
        }
        else if (this.freeWashRoom != o.freeWashRoom)
            return false
        if (this.autoBath == null) {
            if (o.autoBath != null)
                return false
        }
        else if (this.autoBath != o.autoBath)
            return false
        if (this.indoorClothesDrying == null) {
            if (o.indoorClothesDrying != null)
                return false
        }
        else if (this.indoorClothesDrying != o.indoorClothesDrying)
            return false
        if (this.motionSensorLighting == null) {
            if (o.motionSensorLighting != null)
                return false
        }
        else if (this.motionSensorLighting != o.motionSensorLighting)
            return false
        if (this.openKitchen == null) {
            if (o.openKitchen != null)
                return false
        }
        else if (this.openKitchen != o.openKitchen)
            return false
        if (this.islandKitchen == null) {
            if (o.islandKitchen != null)
                return false
        }
        else if (this.islandKitchen != o.islandKitchen)
            return false
        if (this.gasCookerAttached == null) {
            if (o.gasCookerAttached != null)
                return false
        }
        else if (this.gasCookerAttached != o.gasCookerAttached)
            return false
        if (this.threeOverGas == null) {
            if (o.threeOverGas != null)
                return false
        }
        else if (this.threeOverGas != o.threeOverGas)
            return false
        if (this.doubleGlazing == null) {
            if (o.doubleGlazing != null)
                return false
        }
        else if (this.doubleGlazing != o.doubleGlazing)
            return false
        if (this.securityGlazing == null) {
            if (o.securityGlazing != null)
                return false
        }
        else if (this.securityGlazing != o.securityGlazing)
            return false
        if (this.vibrationControlFloor == null) {
            if (o.vibrationControlFloor != null)
                return false
        }
        else if (this.vibrationControlFloor != o.vibrationControlFloor)
            return false
        if (this.snowVanishingFacility == null) {
            if (o.snowVanishingFacility != null)
                return false
        }
        else if (this.snowVanishingFacility != o.snowVanishingFacility)
            return false
        if (this.keroseneHeater == null) {
            if (o.keroseneHeater != null)
                return false
        }
        else if (this.keroseneHeater != o.keroseneHeater)
            return false
        if (this.bathWindow == null) {
            if (o.bathWindow != null)
                return false
        }
        else if (this.bathWindow != o.bathWindow)
            return false
        if (this.japaneseStyleRoom == null) {
            if (o.japaneseStyleRoom != null)
                return false
        }
        else if (this.japaneseStyleRoom != o.japaneseStyleRoom)
            return false
        if (this.earthquakeResistConst == null) {
            if (o.earthquakeResistConst != null)
                return false
        }
        else if (this.earthquakeResistConst != o.earthquakeResistConst)
            return false
        if (this.allinoneServiceWater == null) {
            if (o.allinoneServiceWater != null)
                return false
        }
        else if (this.allinoneServiceWater != o.allinoneServiceWater)
            return false
        if (this.allinoneServiceElectricity == null) {
            if (o.allinoneServiceElectricity != null)
                return false
        }
        else if (this.allinoneServiceElectricity != o.allinoneServiceElectricity)
            return false
        if (this.allinoneServiceGas == null) {
            if (o.allinoneServiceGas != null)
                return false
        }
        else if (this.allinoneServiceGas != o.allinoneServiceGas)
            return false
        if (this.priceAndCost == null) {
            if (o.priceAndCost != null)
                return false
        }
        else if (this.priceAndCost != o.priceAndCost)
            return false
        if (this.valCode_1 == null) {
            if (o.valCode_1 != null)
                return false
        }
        else if (this.valCode_1 != o.valCode_1)
            return false
        if (this.valCode_2 == null) {
            if (o.valCode_2 != null)
                return false
        }
        else if (this.valCode_2 != o.valCode_2)
            return false
        if (this.valCode_3 == null) {
            if (o.valCode_3 != null)
                return false
        }
        else if (this.valCode_3 != o.valCode_3)
            return false
        if (this.panoramaType == null) {
            if (o.panoramaType != null)
                return false
        }
        else if (this.panoramaType != o.panoramaType)
            return false
        if (this.serviceFeeDetails == null) {
            if (o.serviceFeeDetails != null)
                return false
        }
        else if (this.serviceFeeDetails != o.serviceFeeDetails)
            return false
        if (this.shinsaBranchCode == null) {
            if (o.shinsaBranchCode != null)
                return false
        }
        else if (this.shinsaBranchCode != o.shinsaBranchCode)
            return false
        if (this.contractConfirmCode == null) {
            if (o.contractConfirmCode != null)
                return false
        }
        else if (this.contractConfirmCode != o.contractConfirmCode)
            return false
        if (this.prefectureEn == null) {
            if (o.prefectureEn != null)
                return false
        }
        else if (this.prefectureEn != o.prefectureEn)
            return false
        if (this.shikugunchousonEn == null) {
            if (o.shikugunchousonEn != null)
                return false
        }
        else if (this.shikugunchousonEn != o.shikugunchousonEn)
            return false
        if (this.ooazaTsuusyouEn == null) {
            if (o.ooazaTsuusyouEn != null)
                return false
        }
        else if (this.ooazaTsuusyouEn != o.ooazaTsuusyouEn)
            return false
        if (this.azaChoumeEn == null) {
            if (o.azaChoumeEn != null)
                return false
        }
        else if (this.azaChoumeEn != o.azaChoumeEn)
            return false
        if (this.restaddrAlphabet == null) {
            if (o.restaddrAlphabet != null)
                return false
        }
        else if (this.restaddrAlphabet != o.restaddrAlphabet)
            return false
        if (this.upState == null) {
            if (o.upState != null)
                return false
        }
        else if (this.upState != o.upState)
            return false
        if (this.adPriceUnitCode == null) {
            if (o.adPriceUnitCode != null)
                return false
        }
        else if (this.adPriceUnitCode != o.adPriceUnitCode)
            return false
        if (this.deleteDate == null) {
            if (o.deleteDate != null)
                return false
        }
        else if (this.deleteDate != o.deleteDate)
            return false
        if (this.realtimeUpTime == null) {
            if (o.realtimeUpTime != null)
                return false
        }
        else if (this.realtimeUpTime != o.realtimeUpTime)
            return false
        if (this.realtimeUpType == null) {
            if (o.realtimeUpType != null)
                return false
        }
        else if (this.realtimeUpType != o.realtimeUpType)
            return false
        if (this.productTypeCd == null) {
            if (o.productTypeCd != null)
                return false
        }
        else if (this.productTypeCd != o.productTypeCd)
            return false
        if (this.moneyUpdateTime == null) {
            if (o.moneyUpdateTime != null)
                return false
        }
        else if (this.moneyUpdateTime != o.moneyUpdateTime)
            return false
        if (this.kodawari100_199 == null) {
            if (o.kodawari100_199 != null)
                return false
        }
        else if (this.kodawari100_199 != o.kodawari100_199)
            return false
        if (this.floorMaxRoom == null) {
            if (o.floorMaxRoom != null)
                return false
        }
        else if (this.floorMaxRoom != o.floorMaxRoom)
            return false
        if (this.renewalFeeFlg == null) {
            if (o.renewalFeeFlg != null)
                return false
        }
        else if (this.renewalFeeFlg != o.renewalFeeFlg)
            return false
        if (this.fulltimeSupportFlg == null) {
            if (o.fulltimeSupportFlg != null)
                return false
        }
        else if (this.fulltimeSupportFlg != o.fulltimeSupportFlg)
            return false
        if (this.membershipFeeExemptionKbn == null) {
            if (o.membershipFeeExemptionKbn != null)
                return false
        }
        else if (this.membershipFeeExemptionKbn != o.membershipFeeExemptionKbn)
            return false
        if (this.membershipFeeExemptionDays == null) {
            if (o.membershipFeeExemptionDays != null)
                return false
        }
        else if (this.membershipFeeExemptionDays != o.membershipFeeExemptionDays)
            return false
        if (this.eboardComment == null) {
            if (o.eboardComment != null)
                return false
        }
        else if (this.eboardComment != o.eboardComment)
            return false
        if (this.managementParkingKbn == null) {
            if (o.managementParkingKbn != null)
                return false
        }
        else if (this.managementParkingKbn != o.managementParkingKbn)
            return false
        if (this.netServiceJcom == null) {
            if (o.netServiceJcom != null)
                return false
        }
        else if (this.netServiceJcom != o.netServiceJcom)
            return false
        if (this.netServiceStarcat == null) {
            if (o.netServiceStarcat != null)
                return false
        }
        else if (this.netServiceStarcat != o.netServiceStarcat)
            return false
        if (this.zehOriented == null) {
            if (o.zehOriented != null)
                return false
        }
        else if (this.zehOriented != o.zehOriented)
            return false
        if (this.zehDkSoleil == null) {
            if (o.zehDkSoleil != null)
                return false
        }
        else if (this.zehDkSoleil != o.zehDkSoleil)
            return false
        if (this.zehDkAlpha == null) {
            if (o.zehDkAlpha != null)
                return false
        }
        else if (this.zehDkAlpha != o.zehDkAlpha)
            return false
        if (this.keySetCostFlag == null) {
            if (o.keySetCostFlag != null)
                return false
        }
        else if (this.keySetCostFlag != o.keySetCostFlag)
            return false
        if (this.electricIntroduction == null) {
            if (o.electricIntroduction != null)
                return false
        }
        else if (this.electricIntroduction != o.electricIntroduction)
            return false
        if (this.electricType == null) {
            if (o.electricType != null)
                return false
        }
        else if (this.electricType != o.electricType)
            return false
        if (this.emergencyECompanyName == null) {
            if (o.emergencyECompanyName != null)
                return false
        }
        else if (this.emergencyECompanyName != o.emergencyECompanyName)
            return false
        if (this.emergencyECompanyTel == null) {
            if (o.emergencyECompanyTel != null)
                return false
        }
        else if (this.emergencyECompanyTel != o.emergencyECompanyTel)
            return false
        if (this.gasIntroduction == null) {
            if (o.gasIntroduction != null)
                return false
        }
        else if (this.gasIntroduction != o.gasIntroduction)
            return false
        if (this.emergencyGasCompanyName == null) {
            if (o.emergencyGasCompanyName != null)
                return false
        }
        else if (this.emergencyGasCompanyName != o.emergencyGasCompanyName)
            return false
        if (this.emergencyGasCompanyTel == null) {
            if (o.emergencyGasCompanyTel != null)
                return false
        }
        else if (this.emergencyGasCompanyTel != o.emergencyGasCompanyTel)
            return false
        if (this.waterIntroduction == null) {
            if (o.waterIntroduction != null)
                return false
        }
        else if (this.waterIntroduction != o.waterIntroduction)
            return false
        if (this.waterMeterType == null) {
            if (o.waterMeterType != null)
                return false
        }
        else if (this.waterMeterType != o.waterMeterType)
            return false
        if (this.internetType == null) {
            if (o.internetType != null)
                return false
        }
        else if (this.internetType != o.internetType)
            return false
        if (this.internetName == null) {
            if (o.internetName != null)
                return false
        }
        else if (this.internetName != o.internetName)
            return false
        if (this.internetTel == null) {
            if (o.internetTel != null)
                return false
        }
        else if (this.internetTel != o.internetTel)
            return false
        if (this.internetIntroduction == null) {
            if (o.internetIntroduction != null)
                return false
        }
        else if (this.internetIntroduction != o.internetIntroduction)
            return false
        if (this.waterServer == null) {
            if (o.waterServer != null)
                return false
        }
        else if (this.waterServer != o.waterServer)
            return false
        if (this.lifelineGuidanceType == null) {
            if (o.lifelineGuidanceType != null)
                return false
        }
        else if (this.lifelineGuidanceType != o.lifelineGuidanceType)
            return false
        if (this.roomSaveEnergyCertDate == null) {
            if (o.roomSaveEnergyCertDate != null)
                return false
        }
        else if (this.roomSaveEnergyCertDate != o.roomSaveEnergyCertDate)
            return false
        if (this.roomThirdPartyEvalFlg == null) {
            if (o.roomThirdPartyEvalFlg != null)
                return false
        }
        else if (this.roomThirdPartyEvalFlg != o.roomThirdPartyEvalFlg)
            return false
        if (this.roomSaveEnergyLevel == null) {
            if (o.roomSaveEnergyLevel != null)
                return false
        }
        else if (this.roomSaveEnergyLevel != o.roomSaveEnergyLevel)
            return false
        if (this.roomEnergyCost == null) {
            if (o.roomEnergyCost != null)
                return false
        }
        else if (this.roomEnergyCost != o.roomEnergyCost)
            return false
        if (this.roomEnergyCostSun == null) {
            if (o.roomEnergyCostSun != null)
                return false
        }
        else if (this.roomEnergyCostSun != o.roomEnergyCostSun)
            return false
        if (this.roomRenewEnergyFlg == null) {
            if (o.roomRenewEnergyFlg != null)
                return false
        }
        else if (this.roomRenewEnergyFlg != o.roomRenewEnergyFlg)
            return false
        if (this.roomInsulationLevel == null) {
            if (o.roomInsulationLevel != null)
                return false
        }
        else if (this.roomInsulationLevel != o.roomInsulationLevel)
            return false
        if (this.roomEasyUtilityCosts == null) {
            if (o.roomEasyUtilityCosts != null)
                return false
        }
        else if (this.roomEasyUtilityCosts != o.roomEasyUtilityCosts)
            return false
        if (this.roomZehLevelFlg == null) {
            if (o.roomZehLevelFlg != null)
                return false
        }
        else if (this.roomZehLevelFlg != o.roomZehLevelFlg)
            return false
        if (this.roomNetZeroEnergyFlg == null) {
            if (o.roomNetZeroEnergyFlg != null)
                return false
        }
        else if (this.roomNetZeroEnergyFlg != o.roomNetZeroEnergyFlg)
            return false
        if (this.buildingSaveEnergyCertDate == null) {
            if (o.buildingSaveEnergyCertDate != null)
                return false
        }
        else if (this.buildingSaveEnergyCertDate != o.buildingSaveEnergyCertDate)
            return false
        if (this.buildingThirdPartyEvalFlg == null) {
            if (o.buildingThirdPartyEvalFlg != null)
                return false
        }
        else if (this.buildingThirdPartyEvalFlg != o.buildingThirdPartyEvalFlg)
            return false
        if (this.buildingSaveEnergyLevel == null) {
            if (o.buildingSaveEnergyLevel != null)
                return false
        }
        else if (this.buildingSaveEnergyLevel != o.buildingSaveEnergyLevel)
            return false
        if (this.buildingEnergyCost == null) {
            if (o.buildingEnergyCost != null)
                return false
        }
        else if (this.buildingEnergyCost != o.buildingEnergyCost)
            return false
        if (this.buildingEnergyCostSun == null) {
            if (o.buildingEnergyCostSun != null)
                return false
        }
        else if (this.buildingEnergyCostSun != o.buildingEnergyCostSun)
            return false
        if (this.buildingRenewEnergyFlg == null) {
            if (o.buildingRenewEnergyFlg != null)
                return false
        }
        else if (this.buildingRenewEnergyFlg != o.buildingRenewEnergyFlg)
            return false
        if (this.buildingInsulationLevel == null) {
            if (o.buildingInsulationLevel != null)
                return false
        }
        else if (this.buildingInsulationLevel != o.buildingInsulationLevel)
            return false
        if (this.buildingEasyUtilityCosts == null) {
            if (o.buildingEasyUtilityCosts != null)
                return false
        }
        else if (this.buildingEasyUtilityCosts != o.buildingEasyUtilityCosts)
            return false
        if (this.buildingZehLevelFlg == null) {
            if (o.buildingZehLevelFlg != null)
                return false
        }
        else if (this.buildingZehLevelFlg != o.buildingZehLevelFlg)
            return false
        if (this.buildingNetZeroEnergyFlg == null) {
            if (o.buildingNetZeroEnergyFlg != null)
                return false
        }
        else if (this.buildingNetZeroEnergyFlg != o.buildingNetZeroEnergyFlg)
            return false
        if (this.shinsaBusinessOfficeCode == null) {
            if (o.shinsaBusinessOfficeCode != null)
                return false
        }
        else if (this.shinsaBusinessOfficeCode != o.shinsaBusinessOfficeCode)
            return false
        if (this.challengeStart == null) {
            if (o.challengeStart != null)
                return false
        }
        else if (this.challengeStart != o.challengeStart)
            return false
        if (this.challengeEnd == null) {
            if (o.challengeEnd != null)
                return false
        }
        else if (this.challengeEnd != o.challengeEnd)
            return false
        if (this.challengeDiscountPrice == null) {
            if (o.challengeDiscountPrice != null)
                return false
        }
        else if (this.challengeDiscountPrice != o.challengeDiscountPrice)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.propertyFullId == null) 0 else this.propertyFullId.hashCode())
        result = prime * result + (if (this.buildingId == null) 0 else this.buildingId.hashCode())
        result = prime * result + (if (this.renewDate == null) 0 else this.renewDate.hashCode())
        result = prime * result + (if (this.distanceFromStation_1 == null) 0 else this.distanceFromStation_1.hashCode())
        result = prime * result + (if (this.walkFromStation_1 == null) 0 else this.walkFromStation_1.hashCode())
        result = prime * result + (if (this.busFromStation_1 == null) 0 else this.busFromStation_1.hashCode())
        result = prime * result + (if (this.busStopName_1 == null) 0 else this.busStopName_1.hashCode())
        result = prime * result + (if (this.fromBusStop_1 == null) 0 else this.fromBusStop_1.hashCode())
        result = prime * result + (if (this.distanceFromBusstop_1 == null) 0 else this.distanceFromBusstop_1.hashCode())
        result = prime * result + (if (this.nearestRoute_1 == null) 0 else this.nearestRoute_1.hashCode())
        result = prime * result + (if (this.nearestStation_1 == null) 0 else this.nearestStation_1.hashCode())
        result = prime * result + (if (this.kindaikaCodeText_1 == null) 0 else this.kindaikaCodeText_1.hashCode())
        result = prime * result + (if (this.wayToCode_1 == null) 0 else this.wayToCode_1.hashCode())
        result = prime * result + (if (this.distanceFromStation_2 == null) 0 else this.distanceFromStation_2.hashCode())
        result = prime * result + (if (this.walkFromStation_2 == null) 0 else this.walkFromStation_2.hashCode())
        result = prime * result + (if (this.busFromStation_2 == null) 0 else this.busFromStation_2.hashCode())
        result = prime * result + (if (this.busStopName_2 == null) 0 else this.busStopName_2.hashCode())
        result = prime * result + (if (this.fromBusStop_2 == null) 0 else this.fromBusStop_2.hashCode())
        result = prime * result + (if (this.distanceFromBusstop_2 == null) 0 else this.distanceFromBusstop_2.hashCode())
        result = prime * result + (if (this.nearestRoute_2 == null) 0 else this.nearestRoute_2.hashCode())
        result = prime * result + (if (this.nearestStation_2 == null) 0 else this.nearestStation_2.hashCode())
        result = prime * result + (if (this.kindaikaCodeText_2 == null) 0 else this.kindaikaCodeText_2.hashCode())
        result = prime * result + (if (this.wayToCode_2 == null) 0 else this.wayToCode_2.hashCode())
        result = prime * result + (if (this.distanceFromStation_3 == null) 0 else this.distanceFromStation_3.hashCode())
        result = prime * result + (if (this.walkFromStation_3 == null) 0 else this.walkFromStation_3.hashCode())
        result = prime * result + (if (this.busFromStation_3 == null) 0 else this.busFromStation_3.hashCode())
        result = prime * result + (if (this.busStopName_3 == null) 0 else this.busStopName_3.hashCode())
        result = prime * result + (if (this.fromBusStop_3 == null) 0 else this.fromBusStop_3.hashCode())
        result = prime * result + (if (this.distanceFromBusstop_3 == null) 0 else this.distanceFromBusstop_3.hashCode())
        result = prime * result + (if (this.nearestRoute_3 == null) 0 else this.nearestRoute_3.hashCode())
        result = prime * result + (if (this.nearestStation_3 == null) 0 else this.nearestStation_3.hashCode())
        result = prime * result + (if (this.kindaikaCodeText_3 == null) 0 else this.kindaikaCodeText_3.hashCode())
        result = prime * result + (if (this.wayToCode_3 == null) 0 else this.wayToCode_3.hashCode())
        result = prime * result + (if (this.zipCodeText == null) 0 else this.zipCodeText.hashCode())
        result = prime * result + (if (this.prefecture == null) 0 else this.prefecture.hashCode())
        result = prime * result + (if (this.city == null) 0 else this.city.hashCode())
        result = prime * result + (if (this.town == null) 0 else this.town.hashCode())
        result = prime * result + (if (this.tyoume == null) 0 else this.tyoume.hashCode())
        result = prime * result + (if (this.kokudoCodeText == null) 0 else this.kokudoCodeText.hashCode())
        result = prime * result + (if (this.jisCodeValue == null) 0 else this.jisCodeValue.hashCode())
        result = prime * result + (if (this.townCodeValue == null) 0 else this.townCodeValue.hashCode())
        result = prime * result + (if (this.tyoumeCodeValue == null) 0 else this.tyoumeCodeValue.hashCode())
        result = prime * result + (if (this.restaddr1 == null) 0 else this.restaddr1.hashCode())
        result = prime * result + (if (this.latitude == null) 0 else this.latitude.hashCode())
        result = prime * result + (if (this.longitude == null) 0 else this.longitude.hashCode())
        result = prime * result + (if (this.buildingName == null) 0 else this.buildingName.hashCode())
        result = prime * result + (if (this.dispNameCode == null) 0 else this.dispNameCode.hashCode())
        result = prime * result + (if (this.buildingFurigana == null) 0 else this.buildingFurigana.hashCode())
        result = prime * result + (if (this.kindCode == null) 0 else this.kindCode.hashCode())
        result = prime * result + (if (this.kindDispName == null) 0 else this.kindDispName.hashCode())
        result = prime * result + (if (this.saleBlockNum == null) 0 else this.saleBlockNum.hashCode())
        result = prime * result + (if (this.emptyHousesNum == null) 0 else this.emptyHousesNum.hashCode())
        result = prime * result + (if (this.sellingCompany == null) 0 else this.sellingCompany.hashCode())
        result = prime * result + (if (this.completionDate == null) 0 else this.completionDate.hashCode())
        result = prime * result + (if (this.areaWays1Code == null) 0 else this.areaWays1Code.hashCode())
        result = prime * result + (if (this.structureCode == null) 0 else this.structureCode.hashCode())
        result = prime * result + (if (this.structureDispName == null) 0 else this.structureDispName.hashCode())
        result = prime * result + (if (this.buildingTypeCode == null) 0 else this.buildingTypeCode.hashCode())
        result = prime * result + (if (this.allFloorNum == null) 0 else this.allFloorNum.hashCode())
        result = prime * result + (if (this.underFloorNum == null) 0 else this.underFloorNum.hashCode())
        result = prime * result + (if (this.newUsedCode == null) 0 else this.newUsedCode.hashCode())
        result = prime * result + (if (this.managerStyleCode == null) 0 else this.managerStyleCode.hashCode())
        result = prime * result + (if (this.managerComment == null) 0 else this.managerComment.hashCode())
        result = prime * result + (if (this.quietCode == null) 0 else this.quietCode.hashCode())
        result = prime * result + (if (this.gasCode == null) 0 else this.gasCode.hashCode())
        result = prime * result + (if (this.waterSupplyCode == null) 0 else this.waterSupplyCode.hashCode())
        result = prime * result + (if (this.wasteWaterCode == null) 0 else this.wasteWaterCode.hashCode())
        result = prime * result + (if (this.elecPowerCode == null) 0 else this.elecPowerCode.hashCode())
        result = prime * result + (if (this.twoByFourCode == null) 0 else this.twoByFourCode.hashCode())
        result = prime * result + (if (this.sellTypeCode == null) 0 else this.sellTypeCode.hashCode())
        result = prime * result + (if (this.avoidQuakeCode == null) 0 else this.avoidQuakeCode.hashCode())
        result = prime * result + (if (this.barrierFreeCode == null) 0 else this.barrierFreeCode.hashCode())
        result = prime * result + (if (this.fulltimeManagementCode == null) 0 else this.fulltimeManagementCode.hashCode())
        result = prime * result + (if (this.liftCode == null) 0 else this.liftCode.hashCode())
        result = prime * result + (if (this.liftNumCode == null) 0 else this.liftNumCode.hashCode())
        result = prime * result + (if (this.wallTypeCode == null) 0 else this.wallTypeCode.hashCode())
        result = prime * result + (if (this.deliveryMailboxCode == null) 0 else this.deliveryMailboxCode.hashCode())
        result = prime * result + (if (this.launderetteCode == null) 0 else this.launderetteCode.hashCode())
        result = prime * result + (if (this.roomNumberText == null) 0 else this.roomNumberText.hashCode())
        result = prime * result + (if (this.dispRoomNumberCode == null) 0 else this.dispRoomNumberCode.hashCode())
        result = prime * result + (if (this.salesPoint == null) 0 else this.salesPoint.hashCode())
        result = prime * result + (if (this.remark1 == null) 0 else this.remark1.hashCode())
        result = prime * result + (if (this.remark2 == null) 0 else this.remark2.hashCode())
        result = prime * result + (if (this.specialRemark == null) 0 else this.specialRemark.hashCode())
        result = prime * result + (if (this.note == null) 0 else this.note.hashCode())
        result = prime * result + (if (this.price == null) 0 else this.price.hashCode())
        result = prime * result + (if (this.priceTaxCode == null) 0 else this.priceTaxCode.hashCode())
        result = prime * result + (if (this.consumptionTax == null) 0 else this.consumptionTax.hashCode())
        result = prime * result + (if (this.queryPerson == null) 0 else this.queryPerson.hashCode())
        result = prime * result + (if (this.firmSideCode == null) 0 else this.firmSideCode.hashCode())
        result = prime * result + (if (this.intoCode == null) 0 else this.intoCode.hashCode())
        result = prime * result + (if (this.intoDate == null) 0 else this.intoDate.hashCode())
        result = prime * result + (if (this.leaveDate == null) 0 else this.leaveDate.hashCode())
        result = prime * result + (if (this.otherCompanyCode == null) 0 else this.otherCompanyCode.hashCode())
        result = prime * result + (if (this.messageToOtherCompany == null) 0 else this.messageToOtherCompany.hashCode())
        result = prime * result + (if (this.registDate == null) 0 else this.registDate.hashCode())
        result = prime * result + (if (this.registTime == null) 0 else this.registTime.hashCode())
        result = prime * result + (if (this.rentExchangeStyleCode == null) 0 else this.rentExchangeStyleCode.hashCode())
        result = prime * result + (if (this.housePlanCode == null) 0 else this.housePlanCode.hashCode())
        result = prime * result + (if (this.roomNum == null) 0 else this.roomNum.hashCode())
        result = prime * result + (if (this.housePlanEquiv == null) 0 else this.housePlanEquiv.hashCode())
        result = prime * result + (if (this.windowDirectionCode == null) 0 else this.windowDirectionCode.hashCode())
        result = prime * result + (if (this.floorNum == null) 0 else this.floorNum.hashCode())
        result = prime * result + (if (this.nonMoveintoCode == null) 0 else this.nonMoveintoCode.hashCode())
        result = prime * result + (if (this.managedPropertyCode == null) 0 else this.managedPropertyCode.hashCode())
        result = prime * result + (if (this.petCode == null) 0 else this.petCode.hashCode())
        result = prime * result + (if (this.officeCode == null) 0 else this.officeCode.hashCode())
        result = prime * result + (if (this.musicalCode == null) 0 else this.musicalCode.hashCode())
        result = prime * result + (if (this.housePlanDispName == null) 0 else this.housePlanDispName.hashCode())
        result = prime * result + (if (this.usePartArea == null) 0 else this.usePartArea.hashCode())
        result = prime * result + (if (this.keyMoney == null) 0 else this.keyMoney.hashCode())
        result = prime * result + (if (this.keyMoneyUnitCode == null) 0 else this.keyMoneyUnitCode.hashCode())
        result = prime * result + (if (this.keyMoneyTaxCode == null) 0 else this.keyMoneyTaxCode.hashCode())
        result = prime * result + (if (this.deposit == null) 0 else this.deposit.hashCode())
        result = prime * result + (if (this.depositUnitCode == null) 0 else this.depositUnitCode.hashCode())
        result = prime * result + (if (this.repairCost == null) 0 else this.repairCost.hashCode())
        result = prime * result + (if (this.repairCostUnitCode == null) 0 else this.repairCostUnitCode.hashCode())
        result = prime * result + (if (this.guaranty == null) 0 else this.guaranty.hashCode())
        result = prime * result + (if (this.guarantyUnitCode == null) 0 else this.guarantyUnitCode.hashCode())
        result = prime * result + (if (this.syokyakuClassCode == null) 0 else this.syokyakuClassCode.hashCode())
        result = prime * result + (if (this.syokyaku == null) 0 else this.syokyaku.hashCode())
        result = prime * result + (if (this.syokyakuUnitCode == null) 0 else this.syokyakuUnitCode.hashCode())
        result = prime * result + (if (this.premium == null) 0 else this.premium.hashCode())
        result = prime * result + (if (this.premiumUnitCode == null) 0 else this.premiumUnitCode.hashCode())
        result = prime * result + (if (this.premiumTaxCode == null) 0 else this.premiumTaxCode.hashCode())
        result = prime * result + (if (this.manageCost == null) 0 else this.manageCost.hashCode())
        result = prime * result + (if (this.manageCostTaxCode == null) 0 else this.manageCostTaxCode.hashCode())
        result = prime * result + (if (this.serviceFee == null) 0 else this.serviceFee.hashCode())
        result = prime * result + (if (this.serviceFeeFreeCode == null) 0 else this.serviceFeeFreeCode.hashCode())
        result = prime * result + (if (this.serviceFeeTaxCode == null) 0 else this.serviceFeeTaxCode.hashCode())
        result = prime * result + (if (this.zappi == null) 0 else this.zappi.hashCode())
        result = prime * result + (if (this.zappiTaxCode == null) 0 else this.zappiTaxCode.hashCode())
        result = prime * result + (if (this.otherCostComment == null) 0 else this.otherCostComment.hashCode())
        result = prime * result + (if (this.otherCost_1 == null) 0 else this.otherCost_1.hashCode())
        result = prime * result + (if (this.otherCostItem_1 == null) 0 else this.otherCostItem_1.hashCode())
        result = prime * result + (if (this.otherCostTaxCode_1 == null) 0 else this.otherCostTaxCode_1.hashCode())
        result = prime * result + (if (this.otherCost_2 == null) 0 else this.otherCost_2.hashCode())
        result = prime * result + (if (this.otherCostItem_2 == null) 0 else this.otherCostItem_2.hashCode())
        result = prime * result + (if (this.otherCostTaxCode_2 == null) 0 else this.otherCostTaxCode_2.hashCode())
        result = prime * result + (if (this.otherCost_3 == null) 0 else this.otherCost_3.hashCode())
        result = prime * result + (if (this.otherCostItem_3 == null) 0 else this.otherCostItem_3.hashCode())
        result = prime * result + (if (this.otherCostTaxCode_3 == null) 0 else this.otherCostTaxCode_3.hashCode())
        result = prime * result + (if (this.otherCost_4 == null) 0 else this.otherCost_4.hashCode())
        result = prime * result + (if (this.otherCostItem_4 == null) 0 else this.otherCostItem_4.hashCode())
        result = prime * result + (if (this.otherCostTaxCode_4 == null) 0 else this.otherCostTaxCode_4.hashCode())
        result = prime * result + (if (this.otherCost_5 == null) 0 else this.otherCost_5.hashCode())
        result = prime * result + (if (this.otherCostItem_5 == null) 0 else this.otherCostItem_5.hashCode())
        result = prime * result + (if (this.otherCostTaxCode_5 == null) 0 else this.otherCostTaxCode_5.hashCode())
        result = prime * result + (if (this.outerFacilityCode_1 == null) 0 else this.outerFacilityCode_1.hashCode())
        result = prime * result + (if (this.outerFacilityCode_2 == null) 0 else this.outerFacilityCode_2.hashCode())
        result = prime * result + (if (this.outerArea_2 == null) 0 else this.outerArea_2.hashCode())
        result = prime * result + (if (this.renewalFee == null) 0 else this.renewalFee.hashCode())
        result = prime * result + (if (this.renewalFeeUnitCode == null) 0 else this.renewalFeeUnitCode.hashCode())
        result = prime * result + (if (this.renewalFeeClassCode == null) 0 else this.renewalFeeClassCode.hashCode())
        result = prime * result + (if (this.houseRentLimitDate == null) 0 else this.houseRentLimitDate.hashCode())
        result = prime * result + (if (this.insuranceCode == null) 0 else this.insuranceCode.hashCode())
        result = prime * result + (if (this.specialRentalLowerCost == null) 0 else this.specialRentalLowerCost.hashCode())
        result = prime * result + (if (this.specialRentalUpperCost == null) 0 else this.specialRentalUpperCost.hashCode())
        result = prime * result + (if (this.additionalDepositUnitCode == null) 0 else this.additionalDepositUnitCode.hashCode())
        result = prime * result + (if (this.additionalDepositReasonCode == null) 0 else this.additionalDepositReasonCode.hashCode())
        result = prime * result + (if (this.brokerage == null) 0 else this.brokerage.hashCode())
        result = prime * result + (if (this.brokerageUnitCode == null) 0 else this.brokerageUnitCode.hashCode())
        result = prime * result + (if (this.renewalCharge == null) 0 else this.renewalCharge.hashCode())
        result = prime * result + (if (this.renewalChargeUnitCode == null) 0 else this.renewalChargeUnitCode.hashCode())
        result = prime * result + (if (this.studentOnlyCode == null) 0 else this.studentOnlyCode.hashCode())
        result = prime * result + (if (this.sexConditionCode == null) 0 else this.sexConditionCode.hashCode())
        result = prime * result + (if (this.kidsCode == null) 0 else this.kidsCode.hashCode())
        result = prime * result + (if (this.aloneCode == null) 0 else this.aloneCode.hashCode())
        result = prime * result + (if (this.twoPeopleCode == null) 0 else this.twoPeopleCode.hashCode())
        result = prime * result + (if (this.elderCode == null) 0 else this.elderCode.hashCode())
        result = prime * result + (if (this.corporationOnlyCode == null) 0 else this.corporationOnlyCode.hashCode())
        result = prime * result + (if (this.residenceHouseRentCode == null) 0 else this.residenceHouseRentCode.hashCode())
        result = prime * result + (if (this.roomStyleCode_1 == null) 0 else this.roomStyleCode_1.hashCode())
        result = prime * result + (if (this.roomArea_1 == null) 0 else this.roomArea_1.hashCode())
        result = prime * result + (if (this.roomUnitCode_1 == null) 0 else this.roomUnitCode_1.hashCode())
        result = prime * result + (if (this.roomStyleCode_2 == null) 0 else this.roomStyleCode_2.hashCode())
        result = prime * result + (if (this.roomArea_2 == null) 0 else this.roomArea_2.hashCode())
        result = prime * result + (if (this.roomUnitCode_2 == null) 0 else this.roomUnitCode_2.hashCode())
        result = prime * result + (if (this.roomStyleCode_3 == null) 0 else this.roomStyleCode_3.hashCode())
        result = prime * result + (if (this.roomArea_3 == null) 0 else this.roomArea_3.hashCode())
        result = prime * result + (if (this.roomUnitCode_3 == null) 0 else this.roomUnitCode_3.hashCode())
        result = prime * result + (if (this.roomStyleCode_4 == null) 0 else this.roomStyleCode_4.hashCode())
        result = prime * result + (if (this.roomArea_4 == null) 0 else this.roomArea_4.hashCode())
        result = prime * result + (if (this.roomUnitCode_4 == null) 0 else this.roomUnitCode_4.hashCode())
        result = prime * result + (if (this.roomStyleCode_5 == null) 0 else this.roomStyleCode_5.hashCode())
        result = prime * result + (if (this.roomArea_5 == null) 0 else this.roomArea_5.hashCode())
        result = prime * result + (if (this.roomUnitCode_5 == null) 0 else this.roomUnitCode_5.hashCode())
        result = prime * result + (if (this.roomStyleCode_6 == null) 0 else this.roomStyleCode_6.hashCode())
        result = prime * result + (if (this.roomArea_6 == null) 0 else this.roomArea_6.hashCode())
        result = prime * result + (if (this.roomUnitCode_6 == null) 0 else this.roomUnitCode_6.hashCode())
        result = prime * result + (if (this.roomStyleCode_7 == null) 0 else this.roomStyleCode_7.hashCode())
        result = prime * result + (if (this.roomArea_7 == null) 0 else this.roomArea_7.hashCode())
        result = prime * result + (if (this.roomUnitCode_7 == null) 0 else this.roomUnitCode_7.hashCode())
        result = prime * result + (if (this.roomStyleCode_8 == null) 0 else this.roomStyleCode_8.hashCode())
        result = prime * result + (if (this.roomArea_8 == null) 0 else this.roomArea_8.hashCode())
        result = prime * result + (if (this.roomUnitCode_8 == null) 0 else this.roomUnitCode_8.hashCode())
        result = prime * result + (if (this.roomStyleCode_9 == null) 0 else this.roomStyleCode_9.hashCode())
        result = prime * result + (if (this.roomArea_9 == null) 0 else this.roomArea_9.hashCode())
        result = prime * result + (if (this.roomUnitCode_9 == null) 0 else this.roomUnitCode_9.hashCode())
        result = prime * result + (if (this.roomStyleCode_10 == null) 0 else this.roomStyleCode_10.hashCode())
        result = prime * result + (if (this.roomArea_10 == null) 0 else this.roomArea_10.hashCode())
        result = prime * result + (if (this.roomUnitCode_10 == null) 0 else this.roomUnitCode_10.hashCode())
        result = prime * result + (if (this.parkingCode == null) 0 else this.parkingCode.hashCode())
        result = prime * result + (if (this.fromNearParking == null) 0 else this.fromNearParking.hashCode())
        result = prime * result + (if (this.parkingNum == null) 0 else this.parkingNum.hashCode())
        result = prime * result + (if (this.parkingTypeCode == null) 0 else this.parkingTypeCode.hashCode())
        result = prime * result + (if (this.parkingShutterCode == null) 0 else this.parkingShutterCode.hashCode())
        result = prime * result + (if (this.parkingLowerCost == null) 0 else this.parkingLowerCost.hashCode())
        result = prime * result + (if (this.parkingTaxCode == null) 0 else this.parkingTaxCode.hashCode())
        result = prime * result + (if (this.parkableNumCode == null) 0 else this.parkableNumCode.hashCode())
        result = prime * result + (if (this.parkingFreeCode == null) 0 else this.parkingFreeCode.hashCode())
        result = prime * result + (if (this.bikeParkCode == null) 0 else this.bikeParkCode.hashCode())
        result = prime * result + (if (this.bikeParkCost == null) 0 else this.bikeParkCost.hashCode())
        result = prime * result + (if (this.motorbikeParkCode == null) 0 else this.motorbikeParkCode.hashCode())
        result = prime * result + (if (this.motorbikeCost == null) 0 else this.motorbikeCost.hashCode())
        result = prime * result + (if (this.airconCode == null) 0 else this.airconCode.hashCode())
        result = prime * result + (if (this.coolerCode == null) 0 else this.coolerCode.hashCode())
        result = prime * result + (if (this.heatingCode == null) 0 else this.heatingCode.hashCode())
        result = prime * result + (if (this.loadHeaterCode == null) 0 else this.loadHeaterCode.hashCode())
        result = prime * result + (if (this.stoveCode == null) 0 else this.stoveCode.hashCode())
        result = prime * result + (if (this.floorHeatingCode == null) 0 else this.floorHeatingCode.hashCode())
        result = prime * result + (if (this.catvCode == null) 0 else this.catvCode.hashCode())
        result = prime * result + (if (this.communityBroadcastCode == null) 0 else this.communityBroadcastCode.hashCode())
        result = prime * result + (if (this.bsCode == null) 0 else this.bsCode.hashCode())
        result = prime * result + (if (this.csCode == null) 0 else this.csCode.hashCode())
        result = prime * result + (if (this.internetCode == null) 0 else this.internetCode.hashCode())
        result = prime * result + (if (this.closetCode == null) 0 else this.closetCode.hashCode())
        result = prime * result + (if (this.walkinWardrobeCode == null) 0 else this.walkinWardrobeCode.hashCode())
        result = prime * result + (if (this.closetUnderFloorCode == null) 0 else this.closetUnderFloorCode.hashCode())
        result = prime * result + (if (this.trunkRoomCode == null) 0 else this.trunkRoomCode.hashCode())
        result = prime * result + (if (this.oshiireCode == null) 0 else this.oshiireCode.hashCode())
        result = prime * result + (if (this.garretClosetCode == null) 0 else this.garretClosetCode.hashCode())
        result = prime * result + (if (this.shoeCupboardCode == null) 0 else this.shoeCupboardCode.hashCode())
        result = prime * result + (if (this.storeroomCode == null) 0 else this.storeroomCode.hashCode())
        result = prime * result + (if (this.bathToiletCode == null) 0 else this.bathToiletCode.hashCode())
        result = prime * result + (if (this.bathCode == null) 0 else this.bathCode.hashCode())
        result = prime * result + (if (this.showerCode == null) 0 else this.showerCode.hashCode())
        result = prime * result + (if (this.autoBathCode == null) 0 else this.autoBathCode.hashCode())
        result = prime * result + (if (this.dressingRoomCode == null) 0 else this.dressingRoomCode.hashCode())
        result = prime * result + (if (this.reboilBathCode == null) 0 else this.reboilBathCode.hashCode())
        result = prime * result + (if (this.toiletCode == null) 0 else this.toiletCode.hashCode())
        result = prime * result + (if (this.bathDrierCode == null) 0 else this.bathDrierCode.hashCode())
        result = prime * result + (if (this.shampooDresserCode == null) 0 else this.shampooDresserCode.hashCode())
        result = prime * result + (if (this.washletCode == null) 0 else this.washletCode.hashCode())
        result = prime * result + (if (this.bathOver_1tsuboCode == null) 0 else this.bathOver_1tsuboCode.hashCode())
        result = prime * result + (if (this.warmletCode == null) 0 else this.warmletCode.hashCode())
        result = prime * result + (if (this.cookingStoveCode == null) 0 else this.cookingStoveCode.hashCode())
        result = prime * result + (if (this.kitchenCode == null) 0 else this.kitchenCode.hashCode())
        result = prime * result + (if (this.microwaveOvenCode == null) 0 else this.microwaveOvenCode.hashCode())
        result = prime * result + (if (this.ihCookingHeaterCode == null) 0 else this.ihCookingHeaterCode.hashCode())
        result = prime * result + (if (this.coldStorageCode == null) 0 else this.coldStorageCode.hashCode())
        result = prime * result + (if (this.grillCode == null) 0 else this.grillCode.hashCode())
        result = prime * result + (if (this.disposerCode == null) 0 else this.disposerCode.hashCode())
        result = prime * result + (if (this.dishWasherCode == null) 0 else this.dishWasherCode.hashCode())
        result = prime * result + (if (this.waterCleanerCode == null) 0 else this.waterCleanerCode.hashCode())
        result = prime * result + (if (this.woodenFloorCode == null) 0 else this.woodenFloorCode.hashCode())
        result = prime * result + (if (this.loftCode == null) 0 else this.loftCode.hashCode())
        result = prime * result + (if (this.cushionFloorCode == null) 0 else this.cushionFloorCode.hashCode())
        result = prime * result + (if (this.highestFloorCode == null) 0 else this.highestFloorCode.hashCode())
        result = prime * result + (if (this.maisonetteCode == null) 0 else this.maisonetteCode.hashCode())
        result = prime * result + (if (this.overSecondFloorCode == null) 0 else this.overSecondFloorCode.hashCode())
        result = prime * result + (if (this.caveCode == null) 0 else this.caveCode.hashCode())
        result = prime * result + (if (this.soundproofCode == null) 0 else this.soundproofCode.hashCode())
        result = prime * result + (if (this.cornerHouseCode == null) 0 else this.cornerHouseCode.hashCode())
        result = prime * result + (if (this.sunroomCode == null) 0 else this.sunroomCode.hashCode())
        result = prime * result + (if (this.basementCode == null) 0 else this.basementCode.hashCode())
        result = prime * result + (if (this.southRoomCode == null) 0 else this.southRoomCode.hashCode())
        result = prime * result + (if (this.patioCode == null) 0 else this.patioCode.hashCode())
        result = prime * result + (if (this.crimePrevShutterCode == null) 0 else this.crimePrevShutterCode.hashCode())
        result = prime * result + (if (this.crimePrevCameraCode == null) 0 else this.crimePrevCameraCode.hashCode())
        result = prime * result + (if (this.autolockCode == null) 0 else this.autolockCode.hashCode())
        result = prime * result + (if (this.doubleLockCode == null) 0 else this.doubleLockCode.hashCode())
        result = prime * result + (if (this.washingMachineCode == null) 0 else this.washingMachineCode.hashCode())
        result = prime * result + (if (this.drierCode == null) 0 else this.drierCode.hashCode())
        result = prime * result + (if (this.washingMachinePlaceCode == null) 0 else this.washingMachinePlaceCode.hashCode())
        result = prime * result + (if (this.cardKeyCode == null) 0 else this.cardKeyCode.hashCode())
        result = prime * result + (if (this.bowWindowCode == null) 0 else this.bowWindowCode.hashCode())
        result = prime * result + (if (this.lightCode == null) 0 else this.lightCode.hashCode())
        result = prime * result + (if (this.allElectricCode == null) 0 else this.allElectricCode.hashCode())
        result = prime * result + (if (this.hotWaterSupplyCode == null) 0 else this.hotWaterSupplyCode.hashCode())
        result = prime * result + (if (this.interphoneCode == null) 0 else this.interphoneCode.hashCode())
        result = prime * result + (if (this.fulltimeFunCode == null) 0 else this.fulltimeFunCode.hashCode())
        result = prime * result + (if (this.ecocuteCode == null) 0 else this.ecocuteCode.hashCode())
        result = prime * result + (if (this.doubleSideBalconyCode == null) 0 else this.doubleSideBalconyCode.hashCode())
        result = prime * result + (if (this.balconySideNumCode == null) 0 else this.balconySideNumCode.hashCode())
        result = prime * result + (if (this.bathTvCode == null) 0 else this.bathTvCode.hashCode())
        result = prime * result + (if (this.porchCode == null) 0 else this.porchCode.hashCode())
        result = prime * result + (if (this.upStartDate == null) 0 else this.upStartDate.hashCode())
        result = prime * result + (if (this.upEndDate == null) 0 else this.upEndDate.hashCode())
        result = prime * result + (if (this.dressingTableCode == null) 0 else this.dressingTableCode.hashCode())
        result = prime * result + (if (this.privateDustBoxCode == null) 0 else this.privateDustBoxCode.hashCode())
        result = prime * result + (if (this.pianoCode == null) 0 else this.pianoCode.hashCode())
        result = prime * result + (if (this.largeShoesBoxCode == null) 0 else this.largeShoesBoxCode.hashCode())
        result = prime * result + (if (this.closetUnderTatamiCode == null) 0 else this.closetUnderTatamiCode.hashCode())
        result = prime * result + (if (this.indoorsBicycleParkingCode == null) 0 else this.indoorsBicycleParkingCode.hashCode())
        result = prime * result + (if (this.securityKeyCode == null) 0 else this.securityKeyCode.hashCode())
        result = prime * result + (if (this.shutterCode == null) 0 else this.shutterCode.hashCode())
        result = prime * result + (if (this.forSouthCode == null) 0 else this.forSouthCode.hashCode())
        result = prime * result + (if (this.closetUnderstairCode == null) 0 else this.closetUnderstairCode.hashCode())
        result = prime * result + (if (this.nearbyConvenienceStoreCode == null) 0 else this.nearbyConvenienceStoreCode.hashCode())
        result = prime * result + (if (this.nearbyBankCode == null) 0 else this.nearbyBankCode.hashCode())
        result = prime * result + (if (this.nearbyRentalVideoCode == null) 0 else this.nearbyRentalVideoCode.hashCode())
        result = prime * result + (if (this.largeScaleRenewalCode == null) 0 else this.largeScaleRenewalCode.hashCode())
        result = prime * result + (if (this.recoveryCostCode == null) 0 else this.recoveryCostCode.hashCode())
        result = prime * result + (if (this.guarantorCode == null) 0 else this.guarantorCode.hashCode())
        result = prime * result + (if (this.guarantorProxyCode == null) 0 else this.guarantorProxyCode.hashCode())
        result = prime * result + (if (this.guarantorProxyComCode == null) 0 else this.guarantorProxyComCode.hashCode())
        result = prime * result + (if (this.guarantorProxyComment == null) 0 else this.guarantorProxyComment.hashCode())
        result = prime * result + (if (this.dispMapCode == null) 0 else this.dispMapCode.hashCode())
        result = prime * result + (if (this.latitudeWorld == null) 0 else this.latitudeWorld.hashCode())
        result = prime * result + (if (this.longitudeWorld == null) 0 else this.longitudeWorld.hashCode())
        result = prime * result + (if (this.gardenCode == null) 0 else this.gardenCode.hashCode())
        result = prime * result + (if (this.balconyCode == null) 0 else this.balconyCode.hashCode())
        result = prime * result + (if (this.panoramaId == null) 0 else this.panoramaId.hashCode())
        result = prime * result + (if (this.largeScaleRenewalDate == null) 0 else this.largeScaleRenewalDate.hashCode())
        result = prime * result + (if (this.shatakuKanouCode == null) 0 else this.shatakuKanouCode.hashCode())
        result = prime * result + (if (this.noDepositPlanCode == null) 0 else this.noDepositPlanCode.hashCode())
        result = prime * result + (if (this.kentakuKindCode == null) 0 else this.kentakuKindCode.hashCode())
        result = prime * result + (if (this.priceSaleFlag == null) 0 else this.priceSaleFlag.hashCode())
        result = prime * result + (if (this.refomeFlag == null) 0 else this.refomeFlag.hashCode())
        result = prime * result + (if (this.productCode == null) 0 else this.productCode.hashCode())
        result = prime * result + (if (this.ownerShipBranchCode == null) 0 else this.ownerShipBranchCode.hashCode())
        result = prime * result + (if (this.kentakuBuildingCode == null) 0 else this.kentakuBuildingCode.hashCode())
        result = prime * result + (if (this.kentakuRoomCode == null) 0 else this.kentakuRoomCode.hashCode())
        result = prime * result + (if (this.keyExchangeFreeCode == null) 0 else this.keyExchangeFreeCode.hashCode())
        result = prime * result + (if (this.adPrice == null) 0 else this.adPrice.hashCode())
        result = prime * result + (if (this.ffPrice == null) 0 else this.ffPrice.hashCode())
        result = prime * result + (if (this.leaveDateTp == null) 0 else this.leaveDateTp.hashCode())
        result = prime * result + (if (this.leaveFinishDate == null) 0 else this.leaveFinishDate.hashCode())
        result = prime * result + (if (this.lowParkingPrice == null) 0 else this.lowParkingPrice.hashCode())
        result = prime * result + (if (this.highParkingPrice == null) 0 else this.highParkingPrice.hashCode())
        result = prime * result + (if (this.structureDispNameTp == null) 0 else this.structureDispNameTp.hashCode())
        result = prime * result + (if (this.displaceCode == null) 0 else this.displaceCode.hashCode())
        result = prime * result + (if (this.financeCorporationCode == null) 0 else this.financeCorporationCode.hashCode())
        result = prime * result + (if (this.waterCompanyName == null) 0 else this.waterCompanyName.hashCode())
        result = prime * result + (if (this.waterCompanyTel == null) 0 else this.waterCompanyTel.hashCode())
        result = prime * result + (if (this.electricCompanyName == null) 0 else this.electricCompanyName.hashCode())
        result = prime * result + (if (this.electricCompanyTel == null) 0 else this.electricCompanyTel.hashCode())
        result = prime * result + (if (this.gasCompanyName == null) 0 else this.gasCompanyName.hashCode())
        result = prime * result + (if (this.gasCompanyTel == null) 0 else this.gasCompanyTel.hashCode())
        result = prime * result + (if (this.collectDate == null) 0 else this.collectDate.hashCode())
        result = prime * result + (if (this.kouentinCode == null) 0 else this.kouentinCode.hashCode())
        result = prime * result + (if (this.intoDateTxt == null) 0 else this.intoDateTxt.hashCode())
        result = prime * result + (if (this.roomSituationCode == null) 0 else this.roomSituationCode.hashCode())
        result = prime * result + (if (this.recordSituationCode == null) 0 else this.recordSituationCode.hashCode())
        result = prime * result + (if (this.electricDiscountFlag == null) 0 else this.electricDiscountFlag.hashCode())
        result = prime * result + (if (this.fletsHikariCode == null) 0 else this.fletsHikariCode.hashCode())
        result = prime * result + (if (this.akiyaTerm == null) 0 else this.akiyaTerm.hashCode())
        result = prime * result + (if (this.cleaningFeeCode == null) 0 else this.cleaningFeeCode.hashCode())
        result = prime * result + (if (this.cleaningFee == null) 0 else this.cleaningFee.hashCode())
        result = prime * result + (if (this.powerCode == null) 0 else this.powerCode.hashCode())
        result = prime * result + (if (this.fireZoneCode == null) 0 else this.fireZoneCode.hashCode())
        result = prime * result + (if (this.discountRate == null) 0 else this.discountRate.hashCode())
        result = prime * result + (if (this.discountTerm == null) 0 else this.discountTerm.hashCode())
        result = prime * result + (if (this.petFlag == null) 0 else this.petFlag.hashCode())
        result = prime * result + (if (this.internetFreeCode == null) 0 else this.internetFreeCode.hashCode())
        result = prime * result + (if (this.allRoomCloset == null) 0 else this.allRoomCloset.hashCode())
        result = prime * result + (if (this.walkThroughCloset == null) 0 else this.walkThroughCloset.hashCode())
        result = prime * result + (if (this.freeWashRoom == null) 0 else this.freeWashRoom.hashCode())
        result = prime * result + (if (this.autoBath == null) 0 else this.autoBath.hashCode())
        result = prime * result + (if (this.indoorClothesDrying == null) 0 else this.indoorClothesDrying.hashCode())
        result = prime * result + (if (this.motionSensorLighting == null) 0 else this.motionSensorLighting.hashCode())
        result = prime * result + (if (this.openKitchen == null) 0 else this.openKitchen.hashCode())
        result = prime * result + (if (this.islandKitchen == null) 0 else this.islandKitchen.hashCode())
        result = prime * result + (if (this.gasCookerAttached == null) 0 else this.gasCookerAttached.hashCode())
        result = prime * result + (if (this.threeOverGas == null) 0 else this.threeOverGas.hashCode())
        result = prime * result + (if (this.doubleGlazing == null) 0 else this.doubleGlazing.hashCode())
        result = prime * result + (if (this.securityGlazing == null) 0 else this.securityGlazing.hashCode())
        result = prime * result + (if (this.vibrationControlFloor == null) 0 else this.vibrationControlFloor.hashCode())
        result = prime * result + (if (this.snowVanishingFacility == null) 0 else this.snowVanishingFacility.hashCode())
        result = prime * result + (if (this.keroseneHeater == null) 0 else this.keroseneHeater.hashCode())
        result = prime * result + (if (this.bathWindow == null) 0 else this.bathWindow.hashCode())
        result = prime * result + (if (this.japaneseStyleRoom == null) 0 else this.japaneseStyleRoom.hashCode())
        result = prime * result + (if (this.earthquakeResistConst == null) 0 else this.earthquakeResistConst.hashCode())
        result = prime * result + (if (this.allinoneServiceWater == null) 0 else this.allinoneServiceWater.hashCode())
        result = prime * result + (if (this.allinoneServiceElectricity == null) 0 else this.allinoneServiceElectricity.hashCode())
        result = prime * result + (if (this.allinoneServiceGas == null) 0 else this.allinoneServiceGas.hashCode())
        result = prime * result + (if (this.priceAndCost == null) 0 else this.priceAndCost.hashCode())
        result = prime * result + (if (this.valCode_1 == null) 0 else this.valCode_1.hashCode())
        result = prime * result + (if (this.valCode_2 == null) 0 else this.valCode_2.hashCode())
        result = prime * result + (if (this.valCode_3 == null) 0 else this.valCode_3.hashCode())
        result = prime * result + (if (this.panoramaType == null) 0 else this.panoramaType.hashCode())
        result = prime * result + (if (this.serviceFeeDetails == null) 0 else this.serviceFeeDetails.hashCode())
        result = prime * result + (if (this.shinsaBranchCode == null) 0 else this.shinsaBranchCode.hashCode())
        result = prime * result + (if (this.contractConfirmCode == null) 0 else this.contractConfirmCode.hashCode())
        result = prime * result + (if (this.prefectureEn == null) 0 else this.prefectureEn.hashCode())
        result = prime * result + (if (this.shikugunchousonEn == null) 0 else this.shikugunchousonEn.hashCode())
        result = prime * result + (if (this.ooazaTsuusyouEn == null) 0 else this.ooazaTsuusyouEn.hashCode())
        result = prime * result + (if (this.azaChoumeEn == null) 0 else this.azaChoumeEn.hashCode())
        result = prime * result + (if (this.restaddrAlphabet == null) 0 else this.restaddrAlphabet.hashCode())
        result = prime * result + (if (this.upState == null) 0 else this.upState.hashCode())
        result = prime * result + (if (this.adPriceUnitCode == null) 0 else this.adPriceUnitCode.hashCode())
        result = prime * result + (if (this.deleteDate == null) 0 else this.deleteDate.hashCode())
        result = prime * result + (if (this.realtimeUpTime == null) 0 else this.realtimeUpTime.hashCode())
        result = prime * result + (if (this.realtimeUpType == null) 0 else this.realtimeUpType.hashCode())
        result = prime * result + (if (this.productTypeCd == null) 0 else this.productTypeCd.hashCode())
        result = prime * result + (if (this.moneyUpdateTime == null) 0 else this.moneyUpdateTime.hashCode())
        result = prime * result + (if (this.kodawari100_199 == null) 0 else this.kodawari100_199.hashCode())
        result = prime * result + (if (this.floorMaxRoom == null) 0 else this.floorMaxRoom.hashCode())
        result = prime * result + (if (this.renewalFeeFlg == null) 0 else this.renewalFeeFlg.hashCode())
        result = prime * result + (if (this.fulltimeSupportFlg == null) 0 else this.fulltimeSupportFlg.hashCode())
        result = prime * result + (if (this.membershipFeeExemptionKbn == null) 0 else this.membershipFeeExemptionKbn.hashCode())
        result = prime * result + (if (this.membershipFeeExemptionDays == null) 0 else this.membershipFeeExemptionDays.hashCode())
        result = prime * result + (if (this.eboardComment == null) 0 else this.eboardComment.hashCode())
        result = prime * result + (if (this.managementParkingKbn == null) 0 else this.managementParkingKbn.hashCode())
        result = prime * result + (if (this.netServiceJcom == null) 0 else this.netServiceJcom.hashCode())
        result = prime * result + (if (this.netServiceStarcat == null) 0 else this.netServiceStarcat.hashCode())
        result = prime * result + (if (this.zehOriented == null) 0 else this.zehOriented.hashCode())
        result = prime * result + (if (this.zehDkSoleil == null) 0 else this.zehDkSoleil.hashCode())
        result = prime * result + (if (this.zehDkAlpha == null) 0 else this.zehDkAlpha.hashCode())
        result = prime * result + (if (this.keySetCostFlag == null) 0 else this.keySetCostFlag.hashCode())
        result = prime * result + (if (this.electricIntroduction == null) 0 else this.electricIntroduction.hashCode())
        result = prime * result + (if (this.electricType == null) 0 else this.electricType.hashCode())
        result = prime * result + (if (this.emergencyECompanyName == null) 0 else this.emergencyECompanyName.hashCode())
        result = prime * result + (if (this.emergencyECompanyTel == null) 0 else this.emergencyECompanyTel.hashCode())
        result = prime * result + (if (this.gasIntroduction == null) 0 else this.gasIntroduction.hashCode())
        result = prime * result + (if (this.emergencyGasCompanyName == null) 0 else this.emergencyGasCompanyName.hashCode())
        result = prime * result + (if (this.emergencyGasCompanyTel == null) 0 else this.emergencyGasCompanyTel.hashCode())
        result = prime * result + (if (this.waterIntroduction == null) 0 else this.waterIntroduction.hashCode())
        result = prime * result + (if (this.waterMeterType == null) 0 else this.waterMeterType.hashCode())
        result = prime * result + (if (this.internetType == null) 0 else this.internetType.hashCode())
        result = prime * result + (if (this.internetName == null) 0 else this.internetName.hashCode())
        result = prime * result + (if (this.internetTel == null) 0 else this.internetTel.hashCode())
        result = prime * result + (if (this.internetIntroduction == null) 0 else this.internetIntroduction.hashCode())
        result = prime * result + (if (this.waterServer == null) 0 else this.waterServer.hashCode())
        result = prime * result + (if (this.lifelineGuidanceType == null) 0 else this.lifelineGuidanceType.hashCode())
        result = prime * result + (if (this.roomSaveEnergyCertDate == null) 0 else this.roomSaveEnergyCertDate.hashCode())
        result = prime * result + (if (this.roomThirdPartyEvalFlg == null) 0 else this.roomThirdPartyEvalFlg.hashCode())
        result = prime * result + (if (this.roomSaveEnergyLevel == null) 0 else this.roomSaveEnergyLevel.hashCode())
        result = prime * result + (if (this.roomEnergyCost == null) 0 else this.roomEnergyCost.hashCode())
        result = prime * result + (if (this.roomEnergyCostSun == null) 0 else this.roomEnergyCostSun.hashCode())
        result = prime * result + (if (this.roomRenewEnergyFlg == null) 0 else this.roomRenewEnergyFlg.hashCode())
        result = prime * result + (if (this.roomInsulationLevel == null) 0 else this.roomInsulationLevel.hashCode())
        result = prime * result + (if (this.roomEasyUtilityCosts == null) 0 else this.roomEasyUtilityCosts.hashCode())
        result = prime * result + (if (this.roomZehLevelFlg == null) 0 else this.roomZehLevelFlg.hashCode())
        result = prime * result + (if (this.roomNetZeroEnergyFlg == null) 0 else this.roomNetZeroEnergyFlg.hashCode())
        result = prime * result + (if (this.buildingSaveEnergyCertDate == null) 0 else this.buildingSaveEnergyCertDate.hashCode())
        result = prime * result + (if (this.buildingThirdPartyEvalFlg == null) 0 else this.buildingThirdPartyEvalFlg.hashCode())
        result = prime * result + (if (this.buildingSaveEnergyLevel == null) 0 else this.buildingSaveEnergyLevel.hashCode())
        result = prime * result + (if (this.buildingEnergyCost == null) 0 else this.buildingEnergyCost.hashCode())
        result = prime * result + (if (this.buildingEnergyCostSun == null) 0 else this.buildingEnergyCostSun.hashCode())
        result = prime * result + (if (this.buildingRenewEnergyFlg == null) 0 else this.buildingRenewEnergyFlg.hashCode())
        result = prime * result + (if (this.buildingInsulationLevel == null) 0 else this.buildingInsulationLevel.hashCode())
        result = prime * result + (if (this.buildingEasyUtilityCosts == null) 0 else this.buildingEasyUtilityCosts.hashCode())
        result = prime * result + (if (this.buildingZehLevelFlg == null) 0 else this.buildingZehLevelFlg.hashCode())
        result = prime * result + (if (this.buildingNetZeroEnergyFlg == null) 0 else this.buildingNetZeroEnergyFlg.hashCode())
        result = prime * result + (if (this.shinsaBusinessOfficeCode == null) 0 else this.shinsaBusinessOfficeCode.hashCode())
        result = prime * result + (if (this.challengeStart == null) 0 else this.challengeStart.hashCode())
        result = prime * result + (if (this.challengeEnd == null) 0 else this.challengeEnd.hashCode())
        result = prime * result + (if (this.challengeDiscountPrice == null) 0 else this.challengeDiscountPrice.hashCode())
        return result
    }
}
