-- TABLE: TENANT_SALES_MAINTENANCE_INFO(テナント営業メンテナンス情報)

CREATE TABLE TENANT_SALES_MAINTENANCE_INFO(
     BRANCH_CD                                    varchar(4)        NOT NULL    
,    RESIDENTIAL_BUSINESS_USE_DIVISION            numeric(1,0)      NOT NULL    
,    SORT_ORDER1                                  numeric(2,0)                  
,    SORT_ORDER2                                  numeric(2,0)                  
,    SORT_ORDER3                                  numeric(2,0)                  
,    SORT_ORDER4                                  numeric(2,0)                  
,    SORT_ORDER5                                  numeric(2,0)                  
,    ASC_DESC_KEY1                                numeric(1,0)                  
,    ASC_DESC_KEY2                                numeric(1,0)                  
,    ASC_DESC_KEY3                                numeric(1,0)                  
,    ASC_DESC_KEY4                                numeric(1,0)                  
,    ASC_DESC_KEY5                                numeric(1,0)                  
,    DISPLAY_DIVISION1                            numeric(1,0)                  
,    DISPLAY_DIVISION2                            numeric(1,0)                  
,    DISPLAY_DIVISION3                            numeric(1,0)                  
,    DISPLAY_DIVISION4                            numeric(1,0)                  
,    DISPLAY_DIVISION5                            numeric(1,0)                  
,    DISPLAY_DIVISION6                            numeric(1,0)                  
,    DISPLAY_DIVISION7                            numeric(1,0)                  
,    DISPLAY_DIVISION8                            numeric(1,0)                  
,    DISPLAY_DIVISION9                            numeric(1,0)                  
,    DISPLAY_DIVISION10                           numeric(1,0)                  
,    DISPLAY_DIVISION11                           numeric(1,0)                  
,    DISPLAY_DIVISION12                           numeric(1,0)                  
,    DISPLAY_DIVISION13                           numeric(1,0)                  
,    DISPLAY_DIVISION14                           numeric(1,0)                  
,    DISPLAY_DIVISION15                           numeric(1,0)                  
,    DISPLAY_DIVISION16                           numeric(1,0)                  
,    DISPLAY_DIVISION17                           numeric(1,0)                  
,    DISPLAY_DIVISION18                           numeric(1,0)                  
,    DISPLAY_DIVISION19                           numeric(1,0)                  
,    DISPLAY_DIVISION20                           numeric(1,0)                  
,    DISPLAY_DIVISION21                           numeric(1,0)                  
,    DISPLAY_DIVISION22                           numeric(1,0)                  
,    DISPLAY_DIVISION23                           numeric(1,0)                  
,    DISPLAY_DIVISION24                           numeric(1,0)                  
,    DISPLAY_DIVISION25                           numeric(1,0)                  
,    DISPLAY_DIVISION26                           numeric(1,0)                  
,    CONSTRAINT PK_TENANT_SALES_MAINTENANCE_IN PRIMARY KEY (BRANCH_CD, RESIDENTIAL_BUSINESS_USE_DIVISION)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE TENANT_SALES_MAINTENANCE_INFO IS 'テナント営業メンテナンス情報 既存システム物理名: EMETMP';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.BRANCH_CD IS '支店CD 既存システム物理名: EMESCD';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.RESIDENTIAL_BUSINESS_USE_DIVISION IS '居住事業用区分 既存システム物理名: EMEKJK';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.SORT_ORDER1 IS 'ソート順1 既存システム物理名: EMESJ1';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.SORT_ORDER2 IS 'ソート順2 既存システム物理名: EMESJ2';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.SORT_ORDER3 IS 'ソート順3 既存システム物理名: EMESJ3';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.SORT_ORDER4 IS 'ソート順4 既存システム物理名: EMESJ4';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.SORT_ORDER5 IS 'ソート順5 既存システム物理名: EMESJ5';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.ASC_DESC_KEY1 IS '昇順降順キー1 既存システム物理名: EMESK1';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.ASC_DESC_KEY2 IS '昇順降順キー2 既存システム物理名: EMESK2';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.ASC_DESC_KEY3 IS '昇順降順キー3 既存システム物理名: EMESK3';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.ASC_DESC_KEY4 IS '昇順降順キー4 既存システム物理名: EMESK4';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.ASC_DESC_KEY5 IS '昇順降順キー5 既存システム物理名: EMESK5';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION1 IS '表示区分1 既存システム物理名: EMEH01';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION2 IS '表示区分2 既存システム物理名: EMEH02';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION3 IS '表示区分3 既存システム物理名: EMEH03';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION4 IS '表示区分4 既存システム物理名: EMEH04';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION5 IS '表示区分5 既存システム物理名: EMEH05';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION6 IS '表示区分6 既存システム物理名: EMEH06';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION7 IS '表示区分7 既存システム物理名: EMEH07';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION8 IS '表示区分8 既存システム物理名: EMEH08';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION9 IS '表示区分9 既存システム物理名: EMEH09';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION10 IS '表示区分10 既存システム物理名: EMEH10';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION11 IS '表示区分11 既存システム物理名: EMEH11';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION12 IS '表示区分12 既存システム物理名: EMEH12';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION13 IS '表示区分13 既存システム物理名: EMEH13';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION14 IS '表示区分14 既存システム物理名: EMEH14';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION15 IS '表示区分15 既存システム物理名: EMEH15';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION16 IS '表示区分16 既存システム物理名: EMEH16';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION17 IS '表示区分17 既存システム物理名: EMEH17';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION18 IS '表示区分18 既存システム物理名: EMEH18';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION19 IS '表示区分19 既存システム物理名: EMEH19';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION20 IS '表示区分20 既存システム物理名: EMEH20';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION21 IS '表示区分21 既存システム物理名: EMEH21';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION22 IS '表示区分22 既存システム物理名: EMEH22';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION23 IS '表示区分23 既存システム物理名: EMEH23';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION24 IS '表示区分24 既存システム物理名: EMEH24';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION25 IS '表示区分25 既存システム物理名: EMEH25';
COMMENT ON COLUMN TENANT_SALES_MAINTENANCE_INFO.DISPLAY_DIVISION26 IS '表示区分26 既存システム物理名: EMEH26';
