/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.BuildingSpecificUtilityInfoTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingSpecificUtilityInfoPojo

import org.jooq.impl.TableRecordImpl


/**
 * 建物別 ライフライン情報 既存システム物理名: YITLLP
 */
@Suppress("UNCHECKED_CAST")
open class BuildingSpecificUtilityInfoRecord private constructor() : TableRecordImpl<BuildingSpecificUtilityInfoRecord>(BuildingSpecificUtilityInfoTable.BUILDING_SPECIFIC_UTILITY_INFO) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updaterId: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var deleteFlag: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var buildingCd: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var gas1Type: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var gas1CompanyCd: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var gas1DepartmentCd: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var gas2Type: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var gas2CompanyCd: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var gas2DepartmentCd: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var electricType: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var electricCompanyCd: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var electricDepartmentCd: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var waterType: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var waterCompanyCd: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var waterDepartmentCd: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var toiletType: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var toiletCompanyCd: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var toiletDepartmentCd: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var keroseneType: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var keroseneCompanyCd: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var keroseneDepartmentCd: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var terrestrialReception: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var bsReception: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var csReception: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var cableTv: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var cableTvCompanyCd: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var cableTvDepartmentCd: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var communalFacility: String?
        set(value): Unit = set(32, value)
        get(): String? = get(32) as String?

    open var communalFacilityCompanyCd: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var communalFacilityDepartmentCd: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var internet1Remarks: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var internet2WiringMethodCd: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var internet2CompanyCd: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var internet2DepartmentCd: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var internet2Remarks: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var internet3WiringMethodCd: String?
        set(value): Unit = set(40, value)
        get(): String? = get(40) as String?

    open var internet3CompanyCd: String?
        set(value): Unit = set(41, value)
        get(): String? = get(41) as String?

    open var internet3DepartmentCd: String?
        set(value): Unit = set(42, value)
        get(): String? = get(42) as String?

    open var internet3Remarks: String?
        set(value): Unit = set(43, value)
        get(): String? = get(43) as String?

    open var internet4WiringMethodCd: String?
        set(value): Unit = set(44, value)
        get(): String? = get(44) as String?

    open var internet4CompanyCd: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var internet4DepartmentCd: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var internet4Remarks: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var internet5WiringMethodCd: String?
        set(value): Unit = set(48, value)
        get(): String? = get(48) as String?

    open var internet5CompanyCd: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    open var internet5DepartmentCd: String?
        set(value): Unit = set(50, value)
        get(): String? = get(50) as String?

    open var internet5Remarks: String?
        set(value): Unit = set(51, value)
        get(): String? = get(51) as String?

    open var internet6WiringMethodCd: String?
        set(value): Unit = set(52, value)
        get(): String? = get(52) as String?

    open var internet6CompanyCd: String?
        set(value): Unit = set(53, value)
        get(): String? = get(53) as String?

    open var internet6DepartmentCd: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var internet6Remarks: String?
        set(value): Unit = set(55, value)
        get(): String? = get(55) as String?

    open var internet7WiringMethodCd: String?
        set(value): Unit = set(56, value)
        get(): String? = get(56) as String?

    open var internet7CompanyCd: String?
        set(value): Unit = set(57, value)
        get(): String? = get(57) as String?

    open var internet7DepartmentCd: String?
        set(value): Unit = set(58, value)
        get(): String? = get(58) as String?

    open var internet7Remarks: String?
        set(value): Unit = set(59, value)
        get(): String? = get(59) as String?

    open var internet8WiringMethodCd: String?
        set(value): Unit = set(60, value)
        get(): String? = get(60) as String?

    open var internet8CompanyCd: String?
        set(value): Unit = set(61, value)
        get(): String? = get(61) as String?

    open var internet8DepartmentCd: String?
        set(value): Unit = set(62, value)
        get(): String? = get(62) as String?

    open var internet8Remarks: String?
        set(value): Unit = set(63, value)
        get(): String? = get(63) as String?

    open var internet9WiringMethodCd: String?
        set(value): Unit = set(64, value)
        get(): String? = get(64) as String?

    open var internet9CompanyCd: String?
        set(value): Unit = set(65, value)
        get(): String? = get(65) as String?

    open var internet9DepartmentCd: String?
        set(value): Unit = set(66, value)
        get(): String? = get(66) as String?

    open var internet9Remarks: String?
        set(value): Unit = set(67, value)
        get(): String? = get(67) as String?

    open var internet10WiringMethodCd: String?
        set(value): Unit = set(68, value)
        get(): String? = get(68) as String?

    open var internet10CompanyCd: String?
        set(value): Unit = set(69, value)
        get(): String? = get(69) as String?

    open var internet10DepartmentCd: String?
        set(value): Unit = set(70, value)
        get(): String? = get(70) as String?

    open var internet10Remarks: String?
        set(value): Unit = set(71, value)
        get(): String? = get(71) as String?

    open var migrationIncompleteFlag: String?
        set(value): Unit = set(72, value)
        get(): String? = get(72) as String?

    /**
     * Create a detached, initialised BuildingSpecificUtilityInfoRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updaterId: String? = null, deleteFlag: Byte? = null, buildingCd: String? = null, gas1Type: String? = null, gas1CompanyCd: String? = null, gas1DepartmentCd: String? = null, gas2Type: String? = null, gas2CompanyCd: String? = null, gas2DepartmentCd: String? = null, electricType: String? = null, electricCompanyCd: String? = null, electricDepartmentCd: String? = null, waterType: String? = null, waterCompanyCd: String? = null, waterDepartmentCd: String? = null, toiletType: String? = null, toiletCompanyCd: String? = null, toiletDepartmentCd: String? = null, keroseneType: String? = null, keroseneCompanyCd: String? = null, keroseneDepartmentCd: String? = null, terrestrialReception: String? = null, bsReception: String? = null, csReception: String? = null, cableTv: String? = null, cableTvCompanyCd: String? = null, cableTvDepartmentCd: String? = null, communalFacility: String? = null, communalFacilityCompanyCd: String? = null, communalFacilityDepartmentCd: String? = null, internet1Remarks: String? = null, internet2WiringMethodCd: String? = null, internet2CompanyCd: String? = null, internet2DepartmentCd: String? = null, internet2Remarks: String? = null, internet3WiringMethodCd: String? = null, internet3CompanyCd: String? = null, internet3DepartmentCd: String? = null, internet3Remarks: String? = null, internet4WiringMethodCd: String? = null, internet4CompanyCd: String? = null, internet4DepartmentCd: String? = null, internet4Remarks: String? = null, internet5WiringMethodCd: String? = null, internet5CompanyCd: String? = null, internet5DepartmentCd: String? = null, internet5Remarks: String? = null, internet6WiringMethodCd: String? = null, internet6CompanyCd: String? = null, internet6DepartmentCd: String? = null, internet6Remarks: String? = null, internet7WiringMethodCd: String? = null, internet7CompanyCd: String? = null, internet7DepartmentCd: String? = null, internet7Remarks: String? = null, internet8WiringMethodCd: String? = null, internet8CompanyCd: String? = null, internet8DepartmentCd: String? = null, internet8Remarks: String? = null, internet9WiringMethodCd: String? = null, internet9CompanyCd: String? = null, internet9DepartmentCd: String? = null, internet9Remarks: String? = null, internet10WiringMethodCd: String? = null, internet10CompanyCd: String? = null, internet10DepartmentCd: String? = null, internet10Remarks: String? = null, migrationIncompleteFlag: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updaterId = updaterId
        this.deleteFlag = deleteFlag
        this.buildingCd = buildingCd
        this.gas1Type = gas1Type
        this.gas1CompanyCd = gas1CompanyCd
        this.gas1DepartmentCd = gas1DepartmentCd
        this.gas2Type = gas2Type
        this.gas2CompanyCd = gas2CompanyCd
        this.gas2DepartmentCd = gas2DepartmentCd
        this.electricType = electricType
        this.electricCompanyCd = electricCompanyCd
        this.electricDepartmentCd = electricDepartmentCd
        this.waterType = waterType
        this.waterCompanyCd = waterCompanyCd
        this.waterDepartmentCd = waterDepartmentCd
        this.toiletType = toiletType
        this.toiletCompanyCd = toiletCompanyCd
        this.toiletDepartmentCd = toiletDepartmentCd
        this.keroseneType = keroseneType
        this.keroseneCompanyCd = keroseneCompanyCd
        this.keroseneDepartmentCd = keroseneDepartmentCd
        this.terrestrialReception = terrestrialReception
        this.bsReception = bsReception
        this.csReception = csReception
        this.cableTv = cableTv
        this.cableTvCompanyCd = cableTvCompanyCd
        this.cableTvDepartmentCd = cableTvDepartmentCd
        this.communalFacility = communalFacility
        this.communalFacilityCompanyCd = communalFacilityCompanyCd
        this.communalFacilityDepartmentCd = communalFacilityDepartmentCd
        this.internet1Remarks = internet1Remarks
        this.internet2WiringMethodCd = internet2WiringMethodCd
        this.internet2CompanyCd = internet2CompanyCd
        this.internet2DepartmentCd = internet2DepartmentCd
        this.internet2Remarks = internet2Remarks
        this.internet3WiringMethodCd = internet3WiringMethodCd
        this.internet3CompanyCd = internet3CompanyCd
        this.internet3DepartmentCd = internet3DepartmentCd
        this.internet3Remarks = internet3Remarks
        this.internet4WiringMethodCd = internet4WiringMethodCd
        this.internet4CompanyCd = internet4CompanyCd
        this.internet4DepartmentCd = internet4DepartmentCd
        this.internet4Remarks = internet4Remarks
        this.internet5WiringMethodCd = internet5WiringMethodCd
        this.internet5CompanyCd = internet5CompanyCd
        this.internet5DepartmentCd = internet5DepartmentCd
        this.internet5Remarks = internet5Remarks
        this.internet6WiringMethodCd = internet6WiringMethodCd
        this.internet6CompanyCd = internet6CompanyCd
        this.internet6DepartmentCd = internet6DepartmentCd
        this.internet6Remarks = internet6Remarks
        this.internet7WiringMethodCd = internet7WiringMethodCd
        this.internet7CompanyCd = internet7CompanyCd
        this.internet7DepartmentCd = internet7DepartmentCd
        this.internet7Remarks = internet7Remarks
        this.internet8WiringMethodCd = internet8WiringMethodCd
        this.internet8CompanyCd = internet8CompanyCd
        this.internet8DepartmentCd = internet8DepartmentCd
        this.internet8Remarks = internet8Remarks
        this.internet9WiringMethodCd = internet9WiringMethodCd
        this.internet9CompanyCd = internet9CompanyCd
        this.internet9DepartmentCd = internet9DepartmentCd
        this.internet9Remarks = internet9Remarks
        this.internet10WiringMethodCd = internet10WiringMethodCd
        this.internet10CompanyCd = internet10CompanyCd
        this.internet10DepartmentCd = internet10DepartmentCd
        this.internet10Remarks = internet10Remarks
        this.migrationIncompleteFlag = migrationIncompleteFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BuildingSpecificUtilityInfoRecord
     */
    constructor(value: BuildingSpecificUtilityInfoPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updaterId = value.updaterId
            this.deleteFlag = value.deleteFlag
            this.buildingCd = value.buildingCd
            this.gas1Type = value.gas1Type
            this.gas1CompanyCd = value.gas1CompanyCd
            this.gas1DepartmentCd = value.gas1DepartmentCd
            this.gas2Type = value.gas2Type
            this.gas2CompanyCd = value.gas2CompanyCd
            this.gas2DepartmentCd = value.gas2DepartmentCd
            this.electricType = value.electricType
            this.electricCompanyCd = value.electricCompanyCd
            this.electricDepartmentCd = value.electricDepartmentCd
            this.waterType = value.waterType
            this.waterCompanyCd = value.waterCompanyCd
            this.waterDepartmentCd = value.waterDepartmentCd
            this.toiletType = value.toiletType
            this.toiletCompanyCd = value.toiletCompanyCd
            this.toiletDepartmentCd = value.toiletDepartmentCd
            this.keroseneType = value.keroseneType
            this.keroseneCompanyCd = value.keroseneCompanyCd
            this.keroseneDepartmentCd = value.keroseneDepartmentCd
            this.terrestrialReception = value.terrestrialReception
            this.bsReception = value.bsReception
            this.csReception = value.csReception
            this.cableTv = value.cableTv
            this.cableTvCompanyCd = value.cableTvCompanyCd
            this.cableTvDepartmentCd = value.cableTvDepartmentCd
            this.communalFacility = value.communalFacility
            this.communalFacilityCompanyCd = value.communalFacilityCompanyCd
            this.communalFacilityDepartmentCd = value.communalFacilityDepartmentCd
            this.internet1Remarks = value.internet1Remarks
            this.internet2WiringMethodCd = value.internet2WiringMethodCd
            this.internet2CompanyCd = value.internet2CompanyCd
            this.internet2DepartmentCd = value.internet2DepartmentCd
            this.internet2Remarks = value.internet2Remarks
            this.internet3WiringMethodCd = value.internet3WiringMethodCd
            this.internet3CompanyCd = value.internet3CompanyCd
            this.internet3DepartmentCd = value.internet3DepartmentCd
            this.internet3Remarks = value.internet3Remarks
            this.internet4WiringMethodCd = value.internet4WiringMethodCd
            this.internet4CompanyCd = value.internet4CompanyCd
            this.internet4DepartmentCd = value.internet4DepartmentCd
            this.internet4Remarks = value.internet4Remarks
            this.internet5WiringMethodCd = value.internet5WiringMethodCd
            this.internet5CompanyCd = value.internet5CompanyCd
            this.internet5DepartmentCd = value.internet5DepartmentCd
            this.internet5Remarks = value.internet5Remarks
            this.internet6WiringMethodCd = value.internet6WiringMethodCd
            this.internet6CompanyCd = value.internet6CompanyCd
            this.internet6DepartmentCd = value.internet6DepartmentCd
            this.internet6Remarks = value.internet6Remarks
            this.internet7WiringMethodCd = value.internet7WiringMethodCd
            this.internet7CompanyCd = value.internet7CompanyCd
            this.internet7DepartmentCd = value.internet7DepartmentCd
            this.internet7Remarks = value.internet7Remarks
            this.internet8WiringMethodCd = value.internet8WiringMethodCd
            this.internet8CompanyCd = value.internet8CompanyCd
            this.internet8DepartmentCd = value.internet8DepartmentCd
            this.internet8Remarks = value.internet8Remarks
            this.internet9WiringMethodCd = value.internet9WiringMethodCd
            this.internet9CompanyCd = value.internet9CompanyCd
            this.internet9DepartmentCd = value.internet9DepartmentCd
            this.internet9Remarks = value.internet9Remarks
            this.internet10WiringMethodCd = value.internet10WiringMethodCd
            this.internet10CompanyCd = value.internet10CompanyCd
            this.internet10DepartmentCd = value.internet10DepartmentCd
            this.internet10Remarks = value.internet10Remarks
            this.migrationIncompleteFlag = value.migrationIncompleteFlag
            resetChangedOnNotNull()
        }
    }
}
