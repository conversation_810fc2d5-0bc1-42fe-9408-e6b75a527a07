package jp.ne.simplex.application.repository.external

import jp.ne.simplex.log.LogType
import jp.ne.simplex.log.MdcType
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpRequest
import org.springframework.http.HttpStatusCode
import org.springframework.http.client.ClientHttpRequestExecution
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse
import org.springframework.util.StreamUtils
import java.io.ByteArrayInputStream
import java.io.InputStream
import java.nio.charset.StandardCharsets

/**
 * RestTemplateに適用するログ出力用のInterceptor
 * MulesoftやOpenSearchとのアクセスログ出力として利用する
 */
class ExternalApiLoggingInterceptor(
    private val logType: LogType
) : ClientHttpRequestInterceptor {

    companion object {
        private val log = LoggerFactory.getLogger(ExternalApiLoggingInterceptor::class.java)
    }

    override fun intercept(
        request: HttpRequest,
        body: ByteArray,
        execution: ClientHttpRequestExecution
    ): ClientHttpResponse {
        logRequestDetails(request, body)

        val response = BufferingClientHttpResponseWrapper(execution.execute(request, body))
        logResponseDetails(response)

        return response
    }

    private fun logRequestDetails(request: HttpRequest, body: ByteArray) {
        MDC.put(MdcType.LOG_TYPE.key, logType.request)
        MDC.put(MdcType.METHOD.key, request.method.name())
        MDC.put(MdcType.HEADER.key, request.headers.toString())
        MDC.put(MdcType.URI.key, request.uri.toString())

        val content = String(body, StandardCharsets.UTF_8)
        log.info(content)

        MDC.remove(MdcType.LOG_TYPE.key)
        MDC.remove(MdcType.METHOD.key)
        MDC.remove(MdcType.HEADER.key)
        MDC.remove(MdcType.URI.key)
    }

    private fun logResponseDetails(response: BufferingClientHttpResponseWrapper) {
        MDC.put(MdcType.LOG_TYPE.key, logType.response)
        MDC.put(MdcType.STATUS.key, response.statusCode.toString())

        val content = StreamUtils.copyToString(response.body, StandardCharsets.UTF_8)
        log.info(content)

        MDC.remove(MdcType.LOG_TYPE.key)
        MDC.remove(MdcType.STATUS.key)
    }
}

class BufferingClientHttpResponseWrapper(
    private val response: ClientHttpResponse,
) : ClientHttpResponse {

    private var body: ByteArray? = null

    override fun getStatusCode(): HttpStatusCode {
        return response.statusCode
    }

    override fun getStatusText(): String {
        return response.statusText
    }

    override fun getHeaders(): HttpHeaders {
        return response.headers
    }

    override fun getBody(): InputStream {
        if (body == null) {
            body = StreamUtils.copyToByteArray(response.body)
        }

        return ByteArrayInputStream(body)
    }

    override fun close() {
        response.close()
    }
}
