package jp.ne.simplex.application.controller.client.parking.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.*
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.shared.StringExtension.Companion.isAllCharsFullWidthConvertible
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeParseException
import java.time.temporal.ChronoUnit

data class ClientParkingReservationRegisterRequest(

    @JsonProperty("buildingCode")
    @field:Schema(description = "建物コード", example = "000106801")
    val buildingCode: String,

    @JsonProperty("parkingCode")
    @field:Schema(description = "駐車場コード", example = "001")
    val parkingCode: String,

    @JsonProperty("reservationStatus")
    @field:Schema(description = "予約状態", example = "RESERVATION")
    val reservationStatus: ParkingReservation.Status,

    @JsonProperty("reservationType")
    @field:Schema(description = "予約種別", example = "WORK")
    val reservationType: ParkingReservation.Type,

    @JsonProperty("reserveStartDate")
    @field:Schema(description = "予約開始日", example = "20250101")
    val reserveStartDate: String?,

    @JsonProperty("reserveEndDate")
    @field:Schema(description = "予約終了日", example = "20250102")
    val reserveEndDate: String?,

    @JsonProperty("receptionStaff")
    @field:Schema(description = "受付担当者", example = "シンプレクス受付")
    val receptionStaff: String?,

    @JsonProperty("reserverName")
    @field:Schema(description = "利用者氏名", example = "シンプレクス予約")
    val reserverName: String?,

    @JsonProperty("reserverTel")
    @field:Schema(description = "利用者電話番号", example = "090-1234-5678")
    val reserverTel: String?,

    @JsonProperty("remarks")
    @field:Schema(description = "備考", example = "備考")
    val remarks: String?,

    ) {

    companion object {
        private const val WORK_MAX_PERIOD = 31
        private const val NAME_MAX_LENGTH = 42
    }

    fun toServiceInterface(): RegisterParkingReservation {

        // 氏名の長さチェック
        if ((receptionStaff != null && receptionStaff.length > NAME_MAX_LENGTH)
            || (reserverName != null && reserverName.length > NAME_MAX_LENGTH)
        ) {
            throw ClientValidationException(
                ErrorMessage.STRING_MAX_LENGTH.format("氏名", NAME_MAX_LENGTH)
            )
        }

        // 氏名の文字列チェック(全角or全角に変換できるもののみ受け付ける)
        if ((receptionStaff != null && !receptionStaff.isAllCharsFullWidthConvertible())
            || (reserverName != null && !reserverName.isAllCharsFullWidthConvertible())
        ) {
            throw ClientValidationException(ErrorMessage.INVALID_CHAR.format("氏名"))
        }

        // 予約メモの文字列チェック(全角or全角に変換できるもののみ受け付ける)
        if (remarks != null && !remarks.isAllCharsFullWidthConvertible()) {
            throw ClientValidationException(ErrorMessage.INVALID_CHAR.format("予約メモ"))
        }

        try {
            return when (reservationType) {
                ParkingReservation.Type.MANUAL_APPLICATION -> this.toManualApplicationReservation()
                ParkingReservation.Type.WORK -> this.toWorkReservation()
                ParkingReservation.Type.REPLACE -> this.toReplaceReservation()
                else -> throw ClientValidationException(ErrorMessage.PARKING_RESERVATION_INVALID_TYPE.format())
            }
        } catch (e: ClientValidationException) {
            throw e
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }

    private fun toManualApplicationReservation(): RegisterParkingReservation {
        when (reservationStatus) {
            ParkingReservation.Status.TENTATIVE -> {
                if (reserveEndDate != null) {
                    throw ClientValidationException(
                        ErrorMessage.PARKING_RESERVATION_START_AND_END_DATE_NOT_ALLOWED.format("仮申込")
                    )
                }
                return RegisterParkingReservation.of(
                    parkingLotId = ParkingLot.Id(
                        Building.Code.of(buildingCode),
                        ParkingLot.Code.of(parkingCode)
                    ),
                    parkingReservationStatus = reservationStatus,
                    reservationType = reservationType,
                    reserveStartDatetime = (reserveStartDate?.yyyyMMdd() ?: LocalDate.now())
                        .atTime(LocalTime.MIN),
                    reserveEndDatetime = null,
                    receptionStaff = receptionStaff,
                    reserverName = reserverName,
                    reserverTel = reserverTel?.let { TelephoneNumber.of(it) },
                    requestSource = ParkingReservation.RequestSource.DK_LINK,
                    remarks = remarks?.let { ParkingReservation.Remarks.of(it) },
                )
            }

            ParkingReservation.Status.RESERVATION -> {
                if (reserveStartDate == null) {
                    throw ClientValidationException(
                        ErrorMessage.PARKING_RESERVATION_START_DATE_REQUIRED.format("申込")
                    )
                }
                if (reserveEndDate != null) {
                    throw ClientValidationException(
                        ErrorMessage.PARKING_RESERVATION_END_DATE_NOT_ALLOWED.format("申込")
                    )
                }
                val startDate: LocalDate
                try {
                    startDate = reserveStartDate.yyyyMMdd()
                } catch (_: DateTimeParseException) {
                    throw ClientValidationException(
                        ErrorMessage.INVALID_DATE_FORMAT.format("yyyyMMdd")
                    )
                }
                return RegisterParkingReservation.of(
                    parkingLotId = ParkingLot.Id(
                        Building.Code.of(buildingCode),
                        ParkingLot.Code.of(parkingCode)
                    ),
                    parkingReservationStatus = reservationStatus,
                    reservationType = reservationType,
                    reserveStartDatetime = startDate.atTime(LocalTime.MIN),
                    reserveEndDatetime = null,
                    receptionStaff = receptionStaff,
                    reserverName = reserverName,
                    reserverTel = reserverTel?.let { TelephoneNumber.of(it) },
                    requestSource = ParkingReservation.RequestSource.DK_LINK,
                    remarks = remarks?.let { ParkingReservation.Remarks.of(it) },
                )
            }

            else -> throw ClientValidationException(ErrorMessage.PARKING_RESERVATION_INVALID_STATUS.format())
        }
    }

    private fun toWorkReservation(): RegisterParkingReservation {
        if (reservationStatus != ParkingReservation.Status.RESERVATION) {
            throw ClientValidationException(ErrorMessage.PARKING_RESERVATION_INVALID_STATUS.format())
        }
        if (reserveStartDate == null || reserveEndDate == null) {
            throw ClientValidationException(
                ErrorMessage.PARKING_RESERVATION_START_AND_END_DATE_REQUIRED.format("作業")
            )
        }
        val startDate: LocalDate
        val endDate: LocalDate
        try {
            startDate = reserveStartDate.yyyyMMdd()
            endDate = reserveEndDate.yyyyMMdd()
        } catch (_: DateTimeParseException) {
            throw ClientValidationException(ErrorMessage.INVALID_DATE_FORMAT.format("yyyyMMdd"))
        }
        if (startDate.isAfter(endDate)) {
            throw ClientValidationException(ErrorMessage.PARKING_RESERVATION_INVALID_START_AND_END_DATE.format())
        }
        if (ChronoUnit.DAYS.between(startDate, endDate) > WORK_MAX_PERIOD) {
            throw ClientValidationException(
                ErrorMessage.PARKING_RESERVATION_START_AND_END_DATE_PERIOD.format(
                    "作業",
                    WORK_MAX_PERIOD
                )
            )
        }
        return RegisterParkingReservation.of(
            parkingLotId = ParkingLot.Id(
                Building.Code.of(buildingCode),
                ParkingLot.Code.of(parkingCode)
            ),
            parkingReservationStatus = reservationStatus,
            reservationType = reservationType,
            reserveStartDatetime = startDate.atTime(LocalTime.MIN),
            reserveEndDatetime = endDate.atTime(LocalTime.MAX),
            receptionStaff = receptionStaff,
            reserverName = reserverName,
            reserverTel = reserverTel?.let { TelephoneNumber.of(it) },
            requestSource = ParkingReservation.RequestSource.DK_LINK,
            remarks = remarks?.let { ParkingReservation.Remarks.of(it) },
        )
    }

    private fun toReplaceReservation(): RegisterParkingReservation {
        if (reservationStatus != ParkingReservation.Status.RESERVATION) {
            throw ClientValidationException(ErrorMessage.PARKING_RESERVATION_INVALID_STATUS.format())
        }
        if (reserveStartDate != null || reserveEndDate != null) {
            throw ClientValidationException(
                ErrorMessage.PARKING_RESERVATION_START_AND_END_DATE_NOT_ALLOWED.format("場所変更")
            )
        }
        return RegisterParkingReservation.of(
            parkingLotId = ParkingLot.Id(
                Building.Code.of(buildingCode),
                ParkingLot.Code.of(parkingCode)
            ),
            parkingReservationStatus = reservationStatus,
            reservationType = reservationType,
            reserveStartDatetime = null,
            reserveEndDatetime = null,
            receptionStaff = receptionStaff,
            reserverName = reserverName,
            reserverTel = reserverTel?.let { TelephoneNumber.of(it) },
            requestSource = ParkingReservation.RequestSource.DK_LINK,
            remarks = remarks?.let { ParkingReservation.Remarks.of(it) },
        )
    }
}
