package jp.ne.simplex.stub

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.VacancyParkingLotTarget.BulkLeaseFlag
import jp.ne.simplex.application.repository.db.extension.AgentEx.Companion.getAgent
import jp.ne.simplex.application.repository.db.extension.EmployeeMasterEx.Companion.getEmployee
import jp.ne.simplex.application.repository.db.pojos.*
import jp.ne.simplex.application.repository.db.pojos.ContractPojo
import jp.ne.simplex.application.repository.db.pojos.TemporaryContractPojo
import jp.ne.simplex.application.repository.db.pojos.TenantContractPojo
import jp.ne.simplex.authentication.AuthConfig
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.authentication.AuthInfo.Jwt
import jp.ne.simplex.authentication.saml.SingleSignOnConfig
import jp.ne.simplex.db.jooq.gen.tables.pojos.*
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingMasterPojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.LatestRentEvaluationPojo
import jp.ne.simplex.shared.BooleanExtension.Companion.toInt
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import org.springframework.boot.web.server.Cookie
import org.springframework.mock.web.MockMultipartFile
import org.springframework.web.multipart.MultipartFile
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDate
import java.time.LocalDateTime
import jp.ne.simplex.application.repository.db.pojos.RoomInfoMasterPojo as CustomRoomInfoMasterPojo

private const val DEFAULT_EMPLOYEE_CODE = "000011"
private const val DEFAULT_EMPLOYEE_PASSWORD = "000011"
private const val DEFAULT_BUSINESS_OFFICE_CODE = "917"
private const val DEFAULT_BUSINESS_OFFICE_NAME = "渋谷"
private const val DEFAULT_BRANCH_CODE = "642"
private const val DEFAULT_BRANCH_NAME = "渋谷支店"
private const val DEFAULT_AFFILIATION_CODE = "627001"
private const val DEFAULT_BUILDING_CODE = "000130305"
private const val DEFAULT_ORDER_CODE = "0001303"
private const val DEFAULT_ROOM_CODE = "01010"
private const val DEFAULT_TENANT_CONTRACT_NUMBER = "00000000"
private const val DEFAULT_EXCLUSIVE_ID: Long = 123456789012345678
private const val DEFAULT_PARKING_LOT_CODE = "001"
private const val DEFAULT_E_CODE = "E00001000"
private const val DEFAULT_REGION_GROUP_CODE = "01"
private const val DEFAULT_REGION_DEPARTMENT_CODE = "642800"
private const val DEFAULT_UUID = "ff0e398d-95cb-46de-aa9f-1474049f4c8c"
private const val DEFAULT_DIRECTION = "東"
private val DEFAULT_COOKIE_CONFIG = AuthConfig.Jwt.CookieConfig(
    httpOnly = true,
    secure = true,
    sameSite = Cookie.SameSite.STRICT,
    maxAge = 86400
)
private val DEFAULT_JWT_CONFIG: AuthConfig.Jwt = AuthConfig.Jwt(
    AuthConfig.Jwt.TokenConfig("access_token_key", 300),
    AuthConfig.Jwt.TokenConfig("refresh_token_key", 10800),
    DEFAULT_COOKIE_CONFIG
)
private val DEFAULT_API_KEY_CONFIG: AuthConfig.ApiKey = AuthConfig.ApiKey(
    secretId = "external_api_key_list",
    externalSystemName = AuthConfig.ApiKey.ExternalSystemName(
        eboard = "eboard",
        kimaroomSign = "kimaroomSign",
        dkPortal = "dkPortal",
        welcomePark = "welcomePark"
    )
)
private val DEFAULT_SAML_CONFIG: SingleSignOnConfig.SamlConfig = SingleSignOnConfig.SamlConfig(
    entityId = "http://localhost:18080/realms/propetech-dev",
    dkLinkUrl = "http://localhost:4200",
    secretId = "saml_verification_cert"
)
private val DEFAULT_ACCESS_KEY_CONFIG: SingleSignOnConfig.AccessKeyConfig =
    SingleSignOnConfig.AccessKeyConfig(
        secretId = "access_key_hash"
    )

fun stubLoginInfo(
    employeeCode: String = DEFAULT_EMPLOYEE_CODE,
    password: String = DEFAULT_EMPLOYEE_PASSWORD,
): LoginInfo {
    return LoginInfo(
        employeeCode = Employee.Code(employeeCode),
        password = password,
    )
}

fun stubOffice(
    code: String = DEFAULT_BUSINESS_OFFICE_CODE,
    name: String = DEFAULT_BUSINESS_OFFICE_NAME,
): Office {
    return Office(Office.Code.of(code), Office.Name.of(name))
}

fun stubBranch(
    code: String = DEFAULT_BRANCH_CODE,
    name: String = DEFAULT_BRANCH_NAME,
    company: Company = Company.DaitouKentaku,
): Branch {
    return Branch(
        Branch.Code.of(code),
        Branch.Name.of(name),
        company
    )
}

fun stubEmployee(
    code: String = DEFAULT_EMPLOYEE_CODE,
    name: String? = null,
    affiliationCode: String = DEFAULT_AFFILIATION_CODE,
): Employee {
    return stubEmployeeMasterPojo(
        employeeNumber = code,
        nameKanji = name,
        affiliationCode = affiliationCode,
    ).getEmployee()!!
}

fun stubEmployeeMasterPojo(
    employeeNumber: String = DEFAULT_EMPLOYEE_CODE,
    nameKanji: String? = null,
    affiliationCode: String = DEFAULT_AFFILIATION_CODE,
    positionCode: String? = null,
    jobTypeCode: String? = null,
    resignationDate: Long? = null,
    companyCode: String? = null,
): EmployeeMasterPojo {
    return EmployeeMasterPojo(
        employeeNumber = employeeNumber,
        nameKanji = nameKanji,
        affiliationCode = affiliationCode,
        positionCode = positionCode,
        jobTypeCode = jobTypeCode,
        resignationDate = resignationDate,
        companyCode = companyCode
    )
}

fun stubPasswordMasterPojo(
    employeeId: String,
    currentPassword: String? = null,
): PasswordMasterPojo {
    return PasswordMasterPojo(
        employeeId = employeeId,
        currentPassword = currentPassword,
    )
}

fun stubPropertyId(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
): Property.Id {
    return Property.Id(
        buildingCode = Building.Code.of(buildingCode),
        roomCode = Room.Code.of(roomCode)
    )
}

fun stubTemporaryReservationFilePojo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    applicationScheduledDate: String? = null,
    applicationScheduledPersonCd: String? = null,
    customerRepBranchCd: String? = null,
    comment: String? = null,
    contractFormECode: String? = null,
    registrationDate: String? = null,
    linkCdRegistrationTime: String? = null,
    otherCompanyFlag: String? = null,
    otherCompanyMemberId: String? = null,
    otherCompanyName: String? = null,
    otherCompanyStoreName: String? = null,
    otherCompanyRepName: String? = null
): TemporaryReservationFilePojo {

    return TemporaryReservationFilePojo(
        buildingCd = buildingCode,
        roomCd = roomCode,
        status = "1", // どのパターンでも「1」を固定で設定する
        applicationScheduledDate = applicationScheduledDate,
        state = null, // どのパターンでも何も設定していない
        applicationScheduledPersonCd = applicationScheduledPersonCd,
        customerRepCd = "999999", // Legacyで未使用なので適当な値を設定
        customerRepBranchCd = customerRepBranchCd,
        customerRepShozokuCd = "999", // Legacyで未使用なので適当な値を設定
        comment = comment,
        contractFormECode = contractFormECode,
        // Legacyでは、一意となるシーケンスを保存していたが、これを使わずとも排他制御できるのでDKリンクでは不要（適当な値を設定しておく）
        listComment = "543631631690",
        registrationDate = registrationDate,
        linkCdRegistrationTime = linkCdRegistrationTime,
        otherCompanyFlag = otherCompanyFlag,
        otherCompanyMemberId = otherCompanyMemberId,
        otherCompanyName = otherCompanyName,
        otherCompanyStoreName = otherCompanyStoreName,
        otherCompanyRepName = otherCompanyRepName,
    )
}

fun stubOwnCompanyTemporaryReservationInfo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    scheduledMoveInDate: LocalDate = LocalDate.of(2024, 12, 19),
    comment: String? = null,
    assignedBranchCode: String = DEFAULT_BRANCH_CODE,
    assignedBranchName: String = DEFAULT_BRANCH_NAME,
    assignedBranchCompany: Company = Company.DaitouKentaku,
    assignedEmployeeCode: String = DEFAULT_EMPLOYEE_CODE,
    assignedEmployeeName: String? = null,
    updateDate: String = "20251219",
    updateTime: String = "183045",
): TemporaryReservationInfo.OwnCompanyTemporaryReservationInfo {
    return TemporaryReservationInfo.OwnCompanyTemporaryReservationInfo(
        id = stubPropertyId(buildingCode, roomCode),
        assignedBranch = stubBranch(
            assignedBranchCode,
            assignedBranchName,
            assignedBranchCompany
        ),
        assignedEmployee = stubEmployee(assignedEmployeeCode, assignedEmployeeName),
        scheduledMoveInDate = scheduledMoveInDate,
        comment = TemporaryReservation.Comment.of(comment),
        version = TemporaryReservation.Version.of(updateDate, updateTime),
    )
}

fun stubOtherCompanyTemporaryReservationInfo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    scheduledMoveInDate: LocalDate = LocalDate.of(2024, 12, 19),
    comment: String? = null,
    otherCompanyCode: String = "",
    otherCompanyName: String = "",
    otherCompanyStoreName: String = "",
    otherCompanyStaffName: String = "",
    updateDate: String = "20251219",
    updateTime: String = "183045",
): TemporaryReservationInfo.OtherCompanyTemporaryReservationInfo {
    return TemporaryReservationInfo.OtherCompanyTemporaryReservationInfo(
        id = stubPropertyId(buildingCode, roomCode),
        otherCompanyInfo = TemporaryReservation.OtherCompanyInfo(
            companyCode = otherCompanyCode,
            companyName = otherCompanyName,
            storeName = otherCompanyStoreName,
            staffName = otherCompanyStaffName
        ),
        scheduledMoveInDate = scheduledMoveInDate,
        comment = TemporaryReservation.Comment.of(comment),
        version = TemporaryReservation.Version.of(updateDate, updateTime),
    )
}

fun stubOwnCompanyRegisterTemporaryReservation(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    assignedBranchCode: String = DEFAULT_BRANCH_CODE,
    assignedBranchName: String = DEFAULT_BRANCH_NAME,
    assignedBranchCompany: Company = Company.DaitouKentaku,
    assignedEmployeeCode: String = DEFAULT_EMPLOYEE_CODE,
    assignedEmployeeName: String? = null,
    scheduledMoveInDate: LocalDate = LocalDate.of(2024, 12, 19),
    comment: String? = null,
    updateDate: String? = null,
    updateTime: String? = null,
): RegisterTemporaryReservation.OwnCompanyRegisterTemporaryReservation {
    return RegisterTemporaryReservation.OwnCompanyRegisterTemporaryReservation(
        id = stubPropertyId(buildingCode, roomCode),
        assignedBranch = stubBranch(
            assignedBranchCode,
            assignedBranchName,
            assignedBranchCompany
        ),
        assignedEmployee = stubEmployee(assignedEmployeeCode, assignedEmployeeName),
        scheduledMoveInDate = scheduledMoveInDate,
        comment = TemporaryReservation.Comment.of(comment),
        version = stubTemporaryReservationVersion(updateDate, updateTime),
    )
}

fun stubOtherCompanyRegisterTemporaryReservation(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    scheduledMoveInDate: LocalDate = LocalDate.of(2024, 12, 19),
    otherCompanyCode: String = "",
    otherCompanyName: String = "",
    otherCompanyStoreName: String = "",
    otherCompanyStaffName: String = "",
    comment: String? = null,
    updateDate: String = "20251219",
    updateTime: String = "123000",
): RegisterTemporaryReservation.OtherCompanyRegisterTemporaryReservation {
    return RegisterTemporaryReservation.OtherCompanyRegisterTemporaryReservation(
        id = stubPropertyId(buildingCode, roomCode),
        scheduledMoveInDate = scheduledMoveInDate,
        otherCompanyInfo = TemporaryReservation.OtherCompanyInfo(
            companyCode = otherCompanyCode,
            companyName = otherCompanyName,
            storeName = otherCompanyStoreName,
            staffName = otherCompanyStaffName,
        ),
        comment = TemporaryReservation.Comment.of(comment),
        version = stubTemporaryReservationVersion(updateDate, updateTime)!!,
    )
}

fun stubUpdateTemporaryReservationComment(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    comment: String? = null,
): UpdateTemporaryReservationComment {
    return UpdateTemporaryReservationComment(
        id = stubPropertyId(buildingCode, roomCode),
        comment = TemporaryReservation.Comment.of(comment),
    )
}

fun stubCancelTemporaryReservation(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    comment: String? = null,
    updateDate: String? = null,
    updateTime: String? = null,
): CancelTemporaryReservation {
    return CancelTemporaryReservation(
        id = stubPropertyId(buildingCode, roomCode),
        comment = TemporaryReservation.Comment.of(comment),
        version = stubTemporaryReservationVersion(updateDate, updateTime)
    )
}

private fun stubTemporaryReservationVersion(
    updateDate: String? = null,
    updateTime: String? = null,
): TemporaryReservation.Version? {
    if (updateDate != null && updateTime != null) {
        return TemporaryReservation.Version.of(updateDate, updateTime)
    }
    return null
}

fun stubOwnCompanyForceRegisterTemporaryReservation(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    scheduledMoveInDate: LocalDate = LocalDate.of(2024, 12, 19),
    assignedBranchCode: String = DEFAULT_BRANCH_CODE,
    assignedEmployeeCode: String = DEFAULT_EMPLOYEE_CODE,
    comment: String? = null,
): ForceUpdateTemporaryReservation.ForceRegisterTemporaryReservation {
    return ForceUpdateTemporaryReservation.ForceRegisterTemporaryReservation(
        id = stubPropertyId(buildingCode, roomCode),
        scheduledMoveInDate = scheduledMoveInDate,
        assignedBranchCode = Branch.Code.of(assignedBranchCode),
        assignedEmployeeCode = Employee.Code.of(assignedEmployeeCode),
        otherCompanyInfo = null,
        comment = TemporaryReservation.Comment.of(comment),
    )
}

fun stubOtherCompanyForceRegisterTemporaryReservation(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    scheduledMoveInDate: LocalDate = LocalDate.of(2024, 12, 19),
    otherCompanyCode: String = "",
    otherCompanyName: String = "",
    otherCompanyStoreName: String = "",
    otherCompanyStaffName: String = "",
    comment: String? = null,
): ForceUpdateTemporaryReservation.ForceRegisterTemporaryReservation {
    return ForceUpdateTemporaryReservation.ForceRegisterTemporaryReservation(
        id = stubPropertyId(buildingCode, roomCode),
        scheduledMoveInDate = scheduledMoveInDate,
        assignedBranchCode = null,
        assignedEmployeeCode = null,
        otherCompanyInfo = TemporaryReservation.OtherCompanyInfo(
            companyCode = otherCompanyCode,
            companyName = otherCompanyName,
            storeName = otherCompanyStoreName,
            staffName = otherCompanyStaffName,
        ),
        comment = TemporaryReservation.Comment.of(comment),
    )
}

fun stubForceCancelTemporaryReservation(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    comment: String? = null,
): ForceUpdateTemporaryReservation.ForceCancelTemporaryReservation {
    return ForceUpdateTemporaryReservation.ForceCancelTemporaryReservation(
        id = stubPropertyId(buildingCode, roomCode),
        comment = TemporaryReservation.Comment.of(comment),
    )
}

fun stubDaitoBuildingManagementTable(
    creationDate: String? = null,
    updateDate: String? = null,
    branchCode: String? = null, // 支店コード
    officeCode: String? = null, // 営業所コード
    usageStartDate: String? = null,
    usageEndDate: String? = null,
    installationCategory: String? = null,
    satelliteCategory: String? = null,
): DaitoBuildingManagementTablePojo {
    return DaitoBuildingManagementTablePojo(
        creationDate = if (creationDate == null) null else Integer.parseInt(creationDate),
        updateDate = if (updateDate == null) null else Integer.parseInt(updateDate),
        constructionTerminalInstallation = branchCode,
        daikenTerminalInstallation = officeCode,
        usageStartDate = if (usageStartDate == null) null else Integer.parseInt(usageStartDate),
        usageEndDate = if (usageEndDate == null) null else Integer.parseInt(usageEndDate),
        installationCategory = installationCategory,
        satelliteCategory = satelliteCategory,
    )
}

fun stubJwtRequestUser(
    employeeCode: String = DEFAULT_EMPLOYEE_CODE,
    businessOfficeCode: String = DEFAULT_BUSINESS_OFFICE_CODE
): AuthInfo.RequestUser {
    return stubJwtAuthInfo(employeeCode, businessOfficeCode).getRequestUser()
}

fun stubExternalRequestUser(
    externalSystem: ExternalSystem = ExternalSystem.KIMAROOM_SIGN
): AuthInfo.RequestUser {
    return stubApiKeyAuthInfo(externalSystem).getRequestUser()
}

fun stubJwtAuthInfo(
    employeeCode: String = DEFAULT_EMPLOYEE_CODE,
    businessOfficeCode: String? = DEFAULT_BUSINESS_OFFICE_CODE,
    companyCode: String? = Company.DaitouKentakuPartners.code,
): Jwt {
    return Jwt(
        employeeCode = Employee.Code(employeeCode),
        businessOfficeCode = businessOfficeCode?.let { Office.Code.of(it) },
        companyCode = companyCode
    )
}

fun stubApiKeyAuthInfo(
    externalSystem: ExternalSystem = ExternalSystem.KIMAROOM_SIGN
): AuthInfo.ApiKey {
    return AuthInfo.ApiKey(externalSystem)
}

fun stubAuthConfig(
    jwtConfig: AuthConfig.Jwt = DEFAULT_JWT_CONFIG,
    apiKeyConfig: AuthConfig.ApiKey = DEFAULT_API_KEY_CONFIG,
): AuthConfig {
    return AuthConfig(jwtConfig, apiKeyConfig)
}

fun stubSsoConfig(
    samlConfig: SingleSignOnConfig.SamlConfig = DEFAULT_SAML_CONFIG,
    accessKeyConfig: SingleSignOnConfig.AccessKeyConfig = DEFAULT_ACCESS_KEY_CONFIG,
): SingleSignOnConfig {
    return SingleSignOnConfig(false, samlConfig, accessKeyConfig)
}

fun stubParkingVehicleInfoFilePojo(
    tenantContractNumber: String = DEFAULT_TENANT_CONTRACT_NUMBER,
    roomCd: String? = null,
    tandemSign: String? = null,
    landTransportName: String? = null,
    type: String? = null,
    businessCategory: String? = null,
    leftNumber: String? = null,
    rightNumber: String? = null,
    manufacturerDivision: ParkingVehicleInfo.Category.ManufacturerDivision? = null,
    carModelName: String? = null,
    lightVehicleSign: String? = null,
    parkingCertIssueSign: String? = null,
    parkingCertComment: String? = null,
): ParkingVehicleInfoFilePojo {
    return ParkingVehicleInfoFilePojo(
        tenantContractNumber = tenantContractNumber,
        roomCode = roomCd,
        tandemSign = tandemSign,
        landTransportName_1 = landTransportName,
        type_1 = type,
        businessCategory_1 = businessCategory,
        leftNumber_1 = leftNumber,
        rightNumber_1 = rightNumber,
        manufacturerDivision_1 = manufacturerDivision?.byte,
        carModelName_1 = carModelName,
        lightVehicleSign_1 = lightVehicleSign?.toByte(),
        parkingCertIssueSign_1 = parkingCertIssueSign?.toByte(),
        parkingCertComment_1 = parkingCertComment,
    )
}

fun stubParkingVehicleInfo(
    tenantContractNumber: String = DEFAULT_TENANT_CONTRACT_NUMBER,
    roomCd: String? = null,
    parkingLotCategory: ParkingLot.Category? = null,
    landTransportName: String? = null,
    type: String? = null,
    businessCategory: String? = null,
    leftNumber: String? = null,
    rightNumber: String? = null,
    manufacturerDivision: ParkingVehicleInfo.Category.ManufacturerDivision? = null,
    carModelName: String? = null,
    parkingCertIssueSign: ParkingVehicleInfo.ParkingCertIssueSign? = null,
    parkingCertComment: String? = null,
): ParkingVehicleInfo {
    return ParkingVehicleInfo(
        tenantContractNumber = TenantContract.Number.of(tenantContractNumber),
        roomCd = roomCd?.let { Room.Code.of(it) },
        parkingLotCategory = parkingLotCategory,
        vehicleNumber = ParkingVehicleInfo.Number(
            landTransportName,
            type,
            businessCategory,
            leftNumber,
            rightNumber
        ),
        vehicleCategory = ParkingVehicleInfo.Category(manufacturerDivision, carModelName),
        parkingCertIssueSign = parkingCertIssueSign,
        parkingCertComment = parkingCertComment,
    )
}

fun stubParkingReservationPojo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    parkingLotCode: String? = null,
    parkingReservationId: String = DEFAULT_UUID,
    reserveType: ParkingReservation.Type = ParkingReservation.Type.REPLACE,
    reserveStatus: ParkingReservation.Status = ParkingReservation.Status.TENTATIVE,
    receptionDate: Int = LocalDate.now().yyyyMMdd().toInt(),
    receptionStaff: String? = null,
    reserverName: String? = null,
    reserverTel: String? = null,
    remarks: String? = null,
    requestSource: ParkingReservation.RequestSource? = null,
    reserveStartDatetime: LocalDateTime? = null,
    reserveEndDatetime: LocalDateTime? = null,
    eboardParkingReservationId: String = "001",
    creationDate: Int? = null,
    creationTime: Int? = null,
    creator: String? = null,
    updateDate: Int? = null,
    updateTime: Int? = null,
    updater: String? = null,
    deleteFlag: String = "0"
): ParkingReservationPojo {
    return ParkingReservationPojo(
        buildingCode = buildingCode,
        parkingLotCode = parkingLotCode,
        parkingReservationId = parkingReservationId,
        reserveType = reserveType.value,
        reserveStatus = reserveStatus.value,
        receptionDate = receptionDate,
        receptionStaff = receptionStaff,
        reserverName = reserverName,
        reserverTel = reserverTel,
        remarks = remarks,
        reserverSystem = requestSource?.value,
        reserveStartDatetime = reserveStartDatetime,
        reserveEndDatetime = reserveEndDatetime,
        eboardParkingReservationId = eboardParkingReservationId,
        creationDate = creationDate,
        creationTime = creationTime,
        creator = creator,
        updateDate = updateDate,
        updateTime = updateTime,
        updater = updater,
        deleteFlag = deleteFlag,
    )
}

fun stubParkingReservationId(
    uniqueId: String = DEFAULT_UUID,
): ParkingReservation.Id {
    return ParkingReservation.Id.of(uniqueId)
}

fun stubRegisterParkingReservation(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    parkingLotCode: String = DEFAULT_PARKING_LOT_CODE,
    parkingReservationStatus: ParkingReservation.Status = ParkingReservation.Status.TENTATIVE,
    reservationType: ParkingReservation.Type = ParkingReservation.Type.REPLACE,
    reserveStartDatetime: LocalDateTime? = null,
    reserveEndDatetime: LocalDateTime? = null,
    receptionStaff: String? = null,
    reserverName: String? = null,
    reserverTel: String? = null,
    requestSource: ParkingReservation.RequestSource = ParkingReservation.RequestSource.DK_LINK,
    remarks: String? = null,
): RegisterParkingReservation {
    return RegisterParkingReservation.of(
        parkingLotId = ParkingLot.Id(
            Building.Code.of(buildingCode),
            ParkingLot.Code.of(parkingLotCode)
        ),
        parkingReservationStatus = parkingReservationStatus,
        reservationType = reservationType,
        reserveStartDatetime = reserveStartDatetime,
        reserveEndDatetime = reserveEndDatetime,
        receptionStaff = receptionStaff,
        reserverName = reserverName,
        reserverTel = reserverTel?.let { TelephoneNumber.of(it) },
        requestSource = requestSource,
        remarks = remarks?.let { ParkingReservation.Remarks.of(it) },
    )
}

fun stubUpdateParkingReservation(
    id: ParkingReservation.Id = stubParkingReservationId(),
    parkingReservationStatus: ParkingReservation.Status = ParkingReservation.Status.TENTATIVE,
    reservationType: ParkingReservation.Type = ParkingReservation.Type.MANUAL_APPLICATION,
    reserveStartDatetime: LocalDateTime? = null,
    reserveEndDatetime: LocalDateTime? = null,
    receptionStaff: String? = null,
    reserverName: String? = null,
    reserverTel: String? = null,
    requestSource: ParkingReservation.RequestSource = ParkingReservation.RequestSource.DK_LINK,
    remarks: String? = null,
): UpdateParkingReservation {
    return UpdateParkingReservation(
        id = id,
        status = parkingReservationStatus,
        reservationType = reservationType,
        reserveStartDatetime = reserveStartDatetime,
        reserveEndDatetime = reserveEndDatetime,
        receptionStaff = receptionStaff,
        reserverName = reserverName,
        reserverTel = reserverTel?.let { TelephoneNumber.of(it) },
        requestSource = requestSource,
        remarks = remarks?.let { ParkingReservation.Remarks.of(it) },
    )
}

fun stubCancelParkingReservation(
    id: ParkingReservation.Id = stubParkingReservationId(),
    remarks: String? = null,
): CancelParkingReservation {
    return CancelParkingReservation.of(
        id = id,
        remarks = remarks?.let { ParkingReservation.Remarks.of(it) },
    )
}

fun stubCancelParkingApplicationReservation(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    parkingLotCode: String = DEFAULT_PARKING_LOT_CODE,
    parkingReservationStatus: ParkingReservation.Status = ParkingReservation.Status.CANCEL,
    reservationType: ParkingReservation.Type = ParkingReservation.Type.AUTO_APPLICATION,
    remarks: String? = null,
): CancelApplicationParkingReservation {
    return CancelApplicationParkingReservation(
        parkingLotId = ParkingLot.Id(
            Building.Code.of(buildingCode),
            ParkingLot.Code.of(parkingLotCode)
        ),
        status = parkingReservationStatus,
        reservationType = reservationType,
        remarks = remarks?.let { ParkingReservation.Remarks.of(it) }
    )
}

fun stubParkingPojo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    parkingLotCode: String = DEFAULT_PARKING_LOT_CODE,
    parkingLotNumber: String? = null,
    parkingCategory: String? = null,
    bulkLeaseFlag: Byte? = null,
    tenantCategory: String? = "1",
    recruitmentFlag: Byte? = 1,
    offSiteParkingCategory: String? = null,
    logicalDeleteFlag: Byte? = null,
    consolidatedBuildingCode: String? = null,
    consolidatedParkingCode: String? = null,
    transferredBuildingCode: String? = null,
): ParkingPojo {
    return ParkingPojo(
        buildingCode = buildingCode,
        parkingLotCode = parkingLotCode,
        parkingLotNumber = parkingLotNumber,
        parkingCategory = parkingCategory,
        bulkLeaseFlag = bulkLeaseFlag,
        tenantCategory = tenantCategory,
        recruitmentFlag = recruitmentFlag,
        offSiteParkingCategory = offSiteParkingCategory,
        logicalDeleteFlag = logicalDeleteFlag,
        consolidatedBuildingCode = consolidatedBuildingCode,
        consolidatedParkingCode = consolidatedParkingCode,
        transferredBuildingCode = transferredBuildingCode,
    )
}

fun stubParkingInfoMasterPojo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    parkingCode: String = DEFAULT_PARKING_LOT_CODE,
    localDisplayNumber: String? = null,
    currentParkingStatus: String? = null,
    fixedParkingStatus: String? = null,
    specialContractFlag: Byte? = null,
    brokerApplicationPossibility: String? = null,
    logicalDeleteFlag: Byte? = null,
): ParkingInfoMasterPojo {
    return ParkingInfoMasterPojo(
        buildingCode = buildingCode,
        parkingCode = parkingCode,
        localDisplayNumber = localDisplayNumber,
        currentParkingStatus = currentParkingStatus,
        fixedParkingStatus = fixedParkingStatus,
        specialContractFlag = specialContractFlag,
        brokerApplicationPossibility = brokerApplicationPossibility,
        logicalDeleteFlag = logicalDeleteFlag,
    )
}

fun stubParkingReservationInfo(
    id: ParkingReservation.Id = stubParkingReservationId(),
    buildingCode: String = DEFAULT_BUILDING_CODE,
    parkingLotCode: String? = DEFAULT_PARKING_LOT_CODE,
    parkingReservationStatus: ParkingReservation.Status = ParkingReservation.Status.RESERVATION,
    reservationType: ParkingReservation.Type = ParkingReservation.Type.REPLACE,
    reserveStartDatetime: LocalDateTime? = null,
    reserveEndDatetime: LocalDateTime? = null,
    requestSource: ParkingReservation.RequestSource? = null,
    remarks: String? = null,
    receptionDate: String = LocalDate.now().yyyyMMdd(),
    eBoardParkingReservationId: String = "001",
    receptionStaff: String? = null,
    reserverName: String? = null,
    reserverTel: String? = null,
): ParkingReservationInfo {
    return ParkingReservationInfo(
        id = id,
        buildingCode = Building.Code.of(buildingCode),
        parkingLotCode = parkingLotCode?.let { ParkingLot.Code.of(it) },
        status = parkingReservationStatus,
        reservationType = reservationType,
        reserveStartDatetime = reserveStartDatetime,
        reserveEndDatetime = reserveEndDatetime,
        requestSource = requestSource,
        remarks = remarks?.let { ParkingReservation.Remarks.of(it) },
        receptionDate = receptionDate.yyyyMMdd(),
        eBoardParkingReservationId = ParkingReservation.EBoardId.of(eBoardParkingReservationId),
        receptionStaff = receptionStaff,
        reserverName = reserverName,
        reserverTel = reserverTel?.let { TelephoneNumber.of(it) }
    )
}

fun stubParkingContractPossibilityPojo(
    orderCode: String,
    isFirstParkingContractPossible: String? = null,
    isSecondParkingContractPossible: String? = null,
    isAutoJudge: String? = null
): ParkingContractPossibilityPojo {
    return ParkingContractPossibilityPojo(
        orderCode = orderCode,
        isFirstParkingContractPossible = isFirstParkingContractPossible,
        isSecondParkingContractPossible = isSecondParkingContractPossible,
        isAutoJudge = isAutoJudge,
        deleteFlag = "0",
    )
}

fun stubUpdateParkingContractIsAutoJudge(
    orderCode: String = DEFAULT_ORDER_CODE,
    isAutoJudge: ParkingContractPossibility.ContractPossibilityAutoJudge
    = ParkingContractPossibility.ContractPossibilityAutoJudge.AUTO
): UpdateParkingContractIsAutoJudge {
    return UpdateParkingContractIsAutoJudge(
        orderCode = Building.OrderCode.of(orderCode),
        isAutoJudge = isAutoJudge,
    )
}

fun stubParkingContractPossibility(
    orderCode: String = DEFAULT_ORDER_CODE,
    firstParkingContractPossibility: ParkingContractPossibility.ContractPossibility
    = ParkingContractPossibility.ContractPossibility.IMPOSSIBLE,
    secondParkingContractPossibility: ParkingContractPossibility.ContractPossibility
    = ParkingContractPossibility.ContractPossibility.IMPOSSIBLE,
    isAutoJudge: ParkingContractPossibility.ContractPossibilityAutoJudge
    = ParkingContractPossibility.ContractPossibilityAutoJudge.AUTO
): ParkingContractPossibility {
    return ParkingContractPossibility(
        orderCode = Building.OrderCode.of(orderCode),
        firstParkingContractPossibility = firstParkingContractPossibility,
        secondParkingContractPossibility = secondParkingContractPossibility,
        isAutoJudge = isAutoJudge
    )
}

fun stubPropertyMaintenanceInfoPojo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    publishStatus: PropertyMaintenance.PublishStatus = PropertyMaintenance.PublishStatus.PRIVATE,
    oldPublishStatus: PropertyMaintenance.PublishStatus = PropertyMaintenance.PublishStatus.PRIVATE,
    listingCategory: Byte? = null,
    adAmount: Int? = null,
    ffPeriod: Float? = null,
    applicationRent: Int? = null,
    applicationKeyMoney: Int? = null,
    applicationDeposit: Int? = null,
    creationDate: Int? = null,
    creationTime: Int? = null,
    updater: String? = null,
    updateDate: Int? = null,
    updateTime: Int? = null,
): PropertyMaintenanceInfoPojo {
    return PropertyMaintenanceInfoPojo(
        buildingCd = buildingCode,
        roomCd = roomCode,
        listingCategoryGoodRoomNet = publishStatus.value.toByte(),
        homesPanoramaSendFlag = oldPublishStatus.value.toByte(),
        listingCategory = listingCategory,
        adAmount = adAmount,
        ffAmount = ffPeriod?.toBigDecimal(),
        rentalPrice = applicationRent,
        keyMoney = applicationKeyMoney,
        securityDeposit = applicationDeposit,
        creationDate = creationDate,
        creationTime = creationTime,
        updater = updater,
        updateDate = updateDate,
        updateTime = updateTime,
    )
}

fun stubUpdatePropertyMaintenance(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    publishStatus: PropertyMaintenance.PublishStatus = PropertyMaintenance.PublishStatus.PRIVATE,
    advertisementFee: Int? = null,
    frontFreerentPeriod: Float? = null
): UpdatePropertyMaintenance {
    return UpdatePropertyMaintenance(
        id = Property.Id(
            buildingCode = Building.Code.of(buildingCode),
            roomCode = Room.Code.of(roomCode)
        ),
        publishStatus = publishStatus,
        adFf = PropertyMaintenance.AdFf(
            advertisementFee = advertisementFee,
            frontFreerentPeriod = frontFreerentPeriod
        )
    )
}

fun stubPropertyMaintenanceInfo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    publishStatus: PropertyMaintenance.PublishStatus = PropertyMaintenance.PublishStatus.PUBLIC,
    publishStatusBeforeTemporaryReserved: PropertyMaintenance.PublishStatus = PropertyMaintenance.PublishStatus.PRIVATE,
): PropertyMaintenanceInfo {
    return PropertyMaintenanceInfo(
        propertyId = Property.Id(
            buildingCode = Building.Code.of(buildingCode),
            roomCode = Room.Code.of(roomCode)
        ),
        publishStatus = publishStatus,
        publishStatusBeforeTemporaryReserved = publishStatusBeforeTemporaryReserved
    )
}

fun stubParking(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    buildingName: String? = null,
    postalCode: String? = null,
    location: String? = null,
    businessOfficeCode: String? = null,
    parkingLotList: List<ParkingLot> = emptyList(),
    allowAllParkingLotAvailabilityEdit: Boolean = false,
): Parking {
    return Parking(
        building = Building(
            code = Building.Code.of(buildingCode),
            name = Building.Name.of(buildingName ?: ""),
            postalCode = postalCode,
            location = location,
            businessOfficeCode = businessOfficeCode?.let { Office.Code.of(it) },
        ),
        parkingLotList = parkingLotList,
        allowAllParkingLotAvailabilityEdit = allowAllParkingLotAvailabilityEdit
    )
}

fun stubParkingLot(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    parkingLotCode: String? = DEFAULT_PARKING_LOT_CODE,
    localDisplayNumber: String? = null,
    parkingLotCategory: ParkingLot.Category = ParkingLot.Category.SINGLE,
    parkingStatusDivision: ParkingLot.StatusDivision = ParkingLot.StatusDivision.VACANT,
    vacancyParkingStatus: ParkingLot.VacancyStatus = ParkingLot.VacancyStatus.POSSIBLE,
    isAvailable: Boolean = true,
    parkingFee: Int? = null,
    parkingFeeInTax: Int? = null,
    assessmentDivision: ParkingLot.AssessmentDivision? = ParkingLot.AssessmentDivision.ASSESSMENT,
    specialContractFlag: ParkingLot.SpecialContractFlag? = ParkingLot.SpecialContractFlag.YES,
    expectedMoveOutDate: String? = null,
    brokerApplicationPossibility: ParkingLot.BrokerApplicationPossibility? = ParkingLot.BrokerApplicationPossibility.POSSIBLE,
    offSiteParkingLotCategory: ParkingLot.OffSiteCategory = ParkingLot.OffSiteCategory.OUTSIDE,
    offSiteParkingDistance: Int? = null,
    linkedBuildingCode: String? = null,
    linkedRoomCode: String? = null,
    linkedRoomNumber: String? = null,
    reservationList: List<ParkingReservationInfo> = emptyList(),
    bulkLeaseFlag: BulkLeaseFlag? = BulkLeaseFlag.THIRTY_Y,
): ParkingLot {
    return ParkingLot(
        id = ParkingLot.Id(Building.Code.of(buildingCode), ParkingLot.Code.of(parkingLotCode!!)),
        localDisplayNumber = localDisplayNumber,
        parkingLotCategory = parkingLotCategory,
        parkingStatusDivision = parkingStatusDivision,
        vacancyParkingStatus = vacancyParkingStatus,
        isAvailable = isAvailable,
        parkingFee = parkingFee,
        parkingFeeInTax = parkingFeeInTax,
        assessmentDivision = assessmentDivision,
        specialContractFlag = specialContractFlag,
        expectedMoveOutDate = expectedMoveOutDate?.yyyyMMdd(),
        brokerApplicationPossibility = brokerApplicationPossibility,
        offSiteParkingLotCategory = offSiteParkingLotCategory,
        offSiteParkingDistance = offSiteParkingDistance,
        linkedPropertyId = if (linkedBuildingCode != null && linkedRoomCode != null) Property.Id(
            Building.Code.of(linkedBuildingCode),
            Room.Code.of(linkedRoomCode)
        ) else null,
        linkedRoomNumber = linkedRoomNumber?.let(Room.Number::of),
        reservationList = reservationList,
        bulkLeaseFlag = bulkLeaseFlag
    )
}

fun stubVacancyParkingLotTarget(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    parkingLotCode: String? = DEFAULT_PARKING_LOT_CODE,
    parkingLotNumber: String = DEFAULT_PARKING_LOT_CODE,
    parkingFee: Int = 30000,
    bulkLeaseFlag: BulkLeaseFlag = BulkLeaseFlag.THIRTY_Y,
    assessmentDivision: ParkingLot.AssessmentDivision? = ParkingLot.AssessmentDivision.ASSESSMENT,
    parkingLotCategory: ParkingLot.Category = ParkingLot.Category.SINGLE,
): VacancyParkingLotTarget {
    return VacancyParkingLotTarget(
        id = ParkingLot.Id(Building.Code.of(buildingCode), ParkingLot.Code.of(parkingLotCode!!)),
        parkingLotNumber = parkingLotNumber,
        parkingFee = parkingFee,
        bulkLeaseFlag = bulkLeaseFlag,
        assessmentDivision = assessmentDivision,
        parkingLotCategory = parkingLotCategory,
    )
}

fun stubConsumptionTaxRate(
    nationalTaxConsumptionPercent: BigDecimal = BigDecimal("0.10")
): ConsumptionTaxRate {
    return ConsumptionTaxRate(
        nationalTaxConsumptionPercent = nationalTaxConsumptionPercent,
    )
}

fun stubBuilding(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    buildingName: String = "",
    leasingStoreCode: String? = null,
    reviewBranchCode: String? = null
): Building {
    return Building(
        code = Building.Code.of(buildingCode),
        name = Building.Name.of(buildingName),
        leasingStoreCode = leasingStoreCode?.let { Branch.Code.of(it) },
        reviewBranchCode = reviewBranchCode?.let { Branch.Code.of(it) }
    )
}

fun stubBuildingMasterPojo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    buildingName: String? = null,
    bulkLeaseFlag: Int? = null,
    prefectureCode: String? = null,
    cityCode: String? = null,
    townCode: String? = null,
    addressDetail: String? = null,
    landlordCode: String? = null,
    completionDeliveryDate: Int? = null,
    daikenBranchCode: String? = null,
): BuildingMasterPojo {
    return BuildingMasterPojo(
        buildingCode = buildingCode,
        buildingName = buildingName,
        bulkLeaseFlag = bulkLeaseFlag?.toByte(),
        prefectureCode = prefectureCode,
        cityCode = cityCode,
        townCode = townCode,
        addressDetail = addressDetail,
        landlordCode = landlordCode,
        completionDeliveryDate = completionDeliveryDate,
        daikenBranchCode = daikenBranchCode
    )
}

fun stubAddressMasterPojo(
    addressCode: String,
    postalCode: String? = null,
    prefectureKanjiName: String? = null,
    cityKanjiName: String? = null,
    townKanjiName: String? = null,
): AddressMasterPojo {
    return AddressMasterPojo(
        addressCode = addressCode,
        postalCode = postalCode,
        prefectureKanjiName = prefectureKanjiName,
        cityKanjiName = cityKanjiName,
        townKanjiName = townKanjiName,
    )
}

fun stubCustomerPojo(
    integratedClientCode: String? = null,
    clientNameKanji: String? = null,
): CustomerPojo {
    return CustomerPojo(
        clientBranchCode = 1, //dummy
        clientCategory = "1", //dummy
        clientCode = "1", //dummy
        integratedClientCode = integratedClientCode,
        clientNameKanji = clientNameKanji,
    )
}

fun stubBuildingInfoMasterPojo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    propertyName: String? = null,
    postalCode: String? = null,
    location: String? = null,
    prefectureCode: String? = null,
    cityCode: String? = null,
    vacantRooms: Short? = null,
    marketingBranchOfficeCd: String? = null,
): BuildingInfoMasterPojo {
    return BuildingInfoMasterPojo(
        buildingCode = buildingCode,
        propertyName = propertyName,
        postalCode = postalCode,
        location = location,
        prefectureCode = prefectureCode,
        cityCode = cityCode,
        vacantRooms = vacantRooms,
        marketingBranchOfficeCd = marketingBranchOfficeCd
    )
}

fun stubNewOfficeMasterPojo(
    id: Int,
    area: String,
    businessDepartment: String,
    businessOfficeCode: String,
    businessOfficeName: String,
    prefectureCode: String,
    prefectureName: String,
    cityCode: String,
    cityName: String,
    deleteFlag: String = "0",
): NewOfficeMasterPojo {
    return NewOfficeMasterPojo(
        id = id,
        area = area,
        businessDepartment = businessDepartment,
        businessOfficeCode = businessOfficeCode,
        businessOfficeName = businessOfficeName,
        prefectureCode = prefectureCode,
        prefectureName = prefectureName,
        cityCode = cityCode,
        cityName = cityName,
        deleteFlag = deleteFlag,
    )
}

fun stubParkingEnablePojo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    parkingLotCode: String = DEFAULT_PARKING_LOT_CODE,
    parkingLotEnable: String = "1",
    deleteFlag: String = "0",
): ParkingEnablePojo {
    return ParkingEnablePojo(
        buildingCode = buildingCode,
        parkingLotCode = parkingLotCode,
        parkingLotEnable = parkingLotEnable,
        deleteFlag = deleteFlag,
    )
}

fun stubLatestRentEvaluationPojo(
    assessmentReviewNumber: String,
    latestRentAssessmentHistory: String,
    buildingCode: String = DEFAULT_BUILDING_CODE,
    propertyCode: String = DEFAULT_PARKING_LOT_CODE,
    roomParkingDivision: String? = null,
    keyMoneyAmount: Int? = null,
    depositAmount: Int? = null,
    parkingFee: Int? = null,
    taxDivision: String? = null,
    keyMoneyInoutDivision: String? = null,
    parkingFeeInoutDivision: String? = null,
    brokerApplicationCollectionDivision: String? = null,
    standardRentForCoop: Int? = null,
    brokerApplicationCollectionDate: Int? = null,
): LatestRentEvaluationPojo {
    return LatestRentEvaluationPojo(
        assessmentReviewNumber = assessmentReviewNumber,
        latestRentAssessmentHistory = latestRentAssessmentHistory,
        buildingCode = buildingCode,
        propertyCode = propertyCode,
        roomParkingDivision = roomParkingDivision,
        keyMoneyAmount = keyMoneyAmount,
        depositAmount = depositAmount,
        parkingFee = parkingFee,
        taxDivision = taxDivision,
        keyMoneyInoutDivision = keyMoneyInoutDivision,
        parkingFeeInoutDivision = parkingFeeInoutDivision,
        brokerApplicationCollectionDivision = brokerApplicationCollectionDivision,
        standardRentForCoop = standardRentForCoop,
        brokerApplicationCollectionDate = brokerApplicationCollectionDate
    )
}

fun stubParkingHourlyRentalApprovalPojo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    parkingTimeRentalConsentType: String? = null
): ParkingHourlyRentalApprovalPojo {
    return ParkingHourlyRentalApprovalPojo(
        buildingCode = buildingCode,
        parkingTimeRentalConsentType = parkingTimeRentalConsentType
    )
}

fun stubBulkLeaseParkingPojo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    parkingCode: String = DEFAULT_PARKING_LOT_CODE,
    assessmentDivision: String? = null,
    logicalDeleteSign: Byte? = null,
): BulkLeaseParkingPojo {
    return BulkLeaseParkingPojo(
        buildingCode = buildingCode,
        parkingCode = parkingCode,
        assessmentDivision = assessmentDivision,
        logicalDeleteSign = logicalDeleteSign,
    )
}

fun stubOffSiteParkingPojo(
    orderCd: String = DEFAULT_BUILDING_CODE.substring(0, 7),
    parkingCd: String = DEFAULT_PARKING_LOT_CODE,
    distance: Short? = null,
    logicalDeleteSign: Byte? = null,
): OffSiteParkingPojo {
    return OffSiteParkingPojo(
        orderCd = orderCd,
        parkingCd = parkingCd,
        distance = distance,
        logicalDeleteSign = logicalDeleteSign,
    )
}

fun stubRoomMasterPojo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String? = DEFAULT_ROOM_CODE,
    roomNumber: String? = null,
    currentlyAvailableSign: Byte? = null,
    logicalDeleteSign: Byte? = null,
): RoomMasterPojo {
    return RoomMasterPojo(
        buildingCode = buildingCode,
        roomCode = roomCode,
        roomNumber = roomNumber,
        currentlyAvailableSign = currentlyAvailableSign,
        logicalDeleteSign = logicalDeleteSign,
    )
}

fun stubProperty(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    roomNumber: String? = null,
    buildingType: Building.Type = Building.Type.APARTMENT,
    recordStatusType: Property.RecordStatusType? = null,
    customerCompletionFlag: Boolean = false,
    moveInApplicationDate: LocalDate? = null,
    direction: String? = DEFAULT_DIRECTION,
    changeDivision: String? = null,
    applicationScheduledDate: LocalDate? = null,
    marketingBranchOfficeCd: String = DEFAULT_BRANCH_CODE
): Property {
    return Property(
        id = Property.Id(
            buildingCode = Building.Code.of(buildingCode),
            roomCode = Room.Code.of(roomCode)
        ),
        roomNumber = roomNumber?.let { Room.Number.of(it) },
        buildingType = buildingType,
        recordStatusType = recordStatusType,
        customerCompletionFlag = customerCompletionFlag,
        moveInApplicationDate = moveInApplicationDate,
        direction = direction,
        changeDivision = changeDivision,
        applicationScheduledDate = applicationScheduledDate,
        marketingBranchOfficeCode = Office.Code.of(marketingBranchOfficeCd)
    )
}

fun stubRoomInfoMasterPojo(
    recordType: String = "1",
    propertyCdType: String = "1",
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    buildingType: Building.Type = Building.Type.APARTMENT,
    recordStatusType: Property.RecordStatusType? = null,
    roomNumber: String? = null,
    buildingName: String? = null,
    direction: String? = null,
    marketingBranchOfficeCd: String? = DEFAULT_BRANCH_CODE
): CustomRoomInfoMasterPojo {
    return CustomRoomInfoMasterPojo(
        recordType = recordType,
        propertyCdType = propertyCdType,
        propertyBuildingCd = buildingCode,
        propertyRoomCd = roomCode,
        propertyType = buildingType.code,
        recordStatusType = recordStatusType?.code,
        roomNumber = roomNumber,
        buildingName = buildingName,
        direction = direction,
        marketingBranchOfficeCd = marketingBranchOfficeCd
    )
}

fun stubVacantHouseHpPojo(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    changeDivision: String? = null,
    customerCompletionFlag: String? = null,

    ): VacantHousePojo {
    return VacantHousePojo(
        propertyBuildingCd = buildingCode,
        propertyRoomCd = roomCode,
        changeDivision = changeDivision,
        customerCompletionFlag = customerCompletionFlag,
    )
}

fun stubExclusivePropertyPojo(
    id: Long = DEFAULT_EXCLUSIVE_ID,
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    salesOfficeCode: String? = null,
    exclusiveFrom: Int = 20241219,
    exclusiveTo: Int = 20241225,
    companyType: Byte = 1,
    earlyClosureFlag: String = false.toInt().toString(),
    creationDate: Int = 20241219,
    creationTime: Int = 123045,
    creator: String = DEFAULT_EMPLOYEE_CODE,
    updateDate: Int = 20241219,
    updateTime: Int = 123045,
    updater: String = DEFAULT_EMPLOYEE_CODE,
    deleteFlag: String = "0",
): ExclusivePropertyPojo {
    return ExclusivePropertyPojo(
        id = id,
        buildingCode = buildingCode,
        roomCode = roomCode,
        salesOfficeCode = salesOfficeCode,
        exclusiveFrom = exclusiveFrom,
        exclusiveTo = exclusiveTo,
        companyType = companyType,
        earlyClosureFlag = earlyClosureFlag,
        creationDate = creationDate,
        creationTime = creationTime,
        creator = creator,
        updateDate = updateDate,
        updateTime = updateTime,
        updater = updater,
        deleteFlag = deleteFlag,
    )
}

fun stubExclusivePropertyECodePojo(
    id: Long = DEFAULT_EXCLUSIVE_ID,
    eCode: String = DEFAULT_E_CODE,
    creationDate: Int = 20241219,
    creationTime: Int = 123045,
    creator: String = DEFAULT_EMPLOYEE_CODE,
    updateDate: Int = 20241219,
    updateTime: Int = 123045,
    updater: String = DEFAULT_EMPLOYEE_CODE,
    deleteFlag: String = "0",
): ExclusivePropertyECodePojo {
    return ExclusivePropertyECodePojo(
        id = id,
        eCode = eCode,
        creationDate = creationDate,
        creationTime = creationTime,
        creator = creator,
        updateDate = updateDate,
        updateTime = updateTime,
        updater = updater,
        deleteFlag = deleteFlag,
    )
}

fun stubExclusivePropertiesSearchPojo(
    id: Long = DEFAULT_EXCLUSIVE_ID,
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    salesOfficeCode: String? = null,
    propertyName: String? = null,
    roomNumber: String? = null,
    exclusiveFrom: Int = 20241219,
    exclusiveTo: Int = 20241225,
    companyType: ExclusiveProperty.CompanyType = ExclusiveProperty.CompanyType.Leasing,
    earlyClosureFlag: String = false.toInt().toString(),
    creationDate: Int = 20241219,
    creator: String = DEFAULT_EMPLOYEE_CODE,
    updateDate: Int = 20241219,
    updater: String = DEFAULT_EMPLOYEE_CODE,
    deleteFlag: String = false.toInt().toString(),
): ExclusivePropertiesSearchPojo {
    return ExclusivePropertiesSearchPojo(
        id = id,
        buildingCode = buildingCode,
        roomCode = roomCode,
        salesOfficeCode = salesOfficeCode,
        propertyName = propertyName,
        roomNumber = roomNumber,
        exclusiveFrom = exclusiveFrom,
        exclusiveTo = exclusiveTo,
        companyType = companyType.value,
        earlyClosureFlag = earlyClosureFlag,
        creationDate = creationDate,
        creator = creator,
        updateDate = updateDate,
        updater = updater,
        totalCount = 1,
        deleteFlag = deleteFlag
    )
}

fun stubRoomInfoMasterPojo(
    recordType: String = "1",
    propertyCdType: String = "1",
    propertyBuildingCd: String = DEFAULT_BUILDING_CODE,
    propertyRoomCd: String = DEFAULT_ROOM_CODE,
    roomNumber: String? = null,
    marketingBranchOfficeCd: String? = DEFAULT_BRANCH_CODE
): CustomRoomInfoMasterPojo {
    return CustomRoomInfoMasterPojo(
        propertyBuildingCd = propertyBuildingCd,
        propertyRoomCd = propertyRoomCd,
        recordType = recordType,
        propertyCdType = propertyCdType,
        roomNumber = roomNumber,
        marketingBranchOfficeCd = marketingBranchOfficeCd
    )
}

fun stubAgentPojo(
    chukaiGyoshaCd: String = DEFAULT_E_CODE,
    chukaiGyoshameiKanji: String? = null,
    honshitenmeiKanji: String? = null,
): AgentPojo {
    return AgentPojo(
        chukaiGyoshaCd = chukaiGyoshaCd,
        chukaiGyoshameiKanji = chukaiGyoshameiKanji,
        honshitenmeiKanji = honshitenmeiKanji,
    )
}

fun stubKtAllBranchPojo(
    branchCode: String,
    branchName: String? = null,
    companyCode: BigInteger? = null,
    eboardCompany: Company? = null,
): KtAllBranchPojo {
    return KtAllBranchPojo(
        branchCode = branchCode,
        branchName = branchName,
        companyCode = companyCode,
        eboardCompanyCode = eboardCompany?.code
    )
}

fun stubAffiliationMasterPojo(
    shozokuCode: String? = null,
    shozokuAbbrev1: String? = null,
    usageStartDate: LocalDate? = null,
    usageEndDate: LocalDate? = null,
    hierarchyDivision: String? = null,
    company: Company? = null,
): AffiliationMasterPojo {
    return AffiliationMasterPojo(
        shozokuCode = shozokuCode,
        shozokuAbbrev1 = shozokuAbbrev1,
        usageStartDate = usageStartDate?.yyyyMMdd()?.toInt(),
        usageEndDate = usageEndDate?.yyyyMMdd()?.toInt(),
        hierarchyDivision = hierarchyDivision,
        companyCode = company?.code,
    )
}

fun stubBranchFilePojo(
    branchCode: String
): BranchFilePojo {
    return BranchFilePojo(
        branchCode = branchCode,
    )
}

fun stubRegionMasterPojo(
    groupCode: String = DEFAULT_REGION_GROUP_CODE,
    departmentCode: String = DEFAULT_REGION_DEPARTMENT_CODE,
    regionCode2: String? = null,
    useStartDate: LocalDate = LocalDate.now(),
    useFinishDate: LocalDate? = null,
): RegionMasterPojo {
    return RegionMasterPojo(
        groupCode = groupCode,
        departmentCode = departmentCode,
        regionCode_2 = regionCode2,
        useStartDate = useStartDate.yyyyMMdd().toInt(),
        useFinishDate = useFinishDate?.yyyyMMdd()?.toInt(),
    )
}

fun stubHrCategoryTableBPojo(
    typeCategory: String? = null,
    code: String? = null,
): HrCategoryTableBPojo {
    return HrCategoryTableBPojo(
        typeCategory = typeCategory,
        code = code,
    )
}

fun stubTenantContractPojo(
    contractNumber: String,
    buildingCode: String? = null,
    parkingCode: String? = null,
    roomCode: String? = null,
    tenantName: String? = null,
    tenantCode: String? = null,
    currentStateDivision: String? = null,
    modificationStateDivision: String? = null,
    cancellationSign: Int? = null,
    moveInStartProcessedSign: Int? = null,
    moveOutDate: Int? = null,
    moveInScheduledDate: Int? = null,
    contractEffectiveStartDate: Int? = 0,
    contractEffectiveEndDate: Int? = null,
    contractExpiryDate: Int? = null,
    vacateScheduledDate: Int? = null,
    vacateNoticeDate: Int? = null,
    aggregateContractNumber: String? = null,
    logicalDeleteSign: Int? = 0,
): TenantContractPojo {
    return TenantContractPojo(
        tenantContractChangeSeq = "00",
        tenantContractNumber = contractNumber,
        buildingCode = buildingCode,
        parkingCode = parkingCode,
        roomCode = roomCode,
        tenantName = tenantName,
        tenantCode = tenantCode,
        currentStateDivision = currentStateDivision,
        modificationStateDivision = modificationStateDivision,
        cancellationSign = cancellationSign,
        moveInStartProcessedSign = moveInStartProcessedSign,
        moveOutDate = moveOutDate,
        moveInScheduledDate = moveInScheduledDate,
        contractEffectiveStartDate = contractEffectiveStartDate,
        contractEffectiveEndDate = contractEffectiveEndDate,
        contractExpiryDate = contractExpiryDate,
        vacateScheduledDate = vacateScheduledDate,
        vacateNoticeDate = vacateNoticeDate,
        aggregateContractNumber = aggregateContractNumber,
        logicalDeleteSign = logicalDeleteSign,
    )
}

fun stubTenantPojo(
    tenantCode: String,
    tenantNameKanji: String? = null,
): TenantPojo {
    return TenantPojo(
        tenantCode = tenantCode,
        tenantNameKanji = tenantNameKanji
    )
}

fun stubContractPojo(
    buildingCd: String = DEFAULT_BUILDING_CODE,
    effectiveStartDate: Int? = null,
    effectiveEndDate: Int? = null,
    initialSetupSign: String? = null,
    contractType: String? = null,
    logicalDeleteSign: String? = null,
): ContractPojo {
    return ContractPojo(
        buildingCd = buildingCd,
        effectiveStartDate = effectiveStartDate,
        effectiveEndDate = effectiveEndDate,
        initialSetupSign = initialSetupSign,
        contractType = contractType,
        logicalDeleteSign = logicalDeleteSign,
    )
}

fun stubTemporaryContractPojo(
    buildingCd: String = DEFAULT_BUILDING_CODE,
    effectiveStartDate: Int? = null,
    effectiveEndDate: Int? = null,
    initialSetupFlag: String? = null,
    contractType: String? = null,
    logicalDeleteFlag: String? = null,
): TemporaryContractPojo {
    return TemporaryContractPojo(
        buildingCd = buildingCd,
        effectiveStartDate = effectiveStartDate,
        effectiveEndDate = effectiveEndDate,
        initialSetupFlag = initialSetupFlag,
        contractType = contractType,
        logicalDeleteFlag = logicalDeleteFlag,
    )
}

fun stubImageFile(
    fileName: String = "parking-layout.jpg",
    imagePath: String = "/parking-layout.jpg",
): ImageFile {
    return ImageFile.of(stubImage(fileName, imagePath), 500 * 1024)
}

fun stubImage(
    fileName: String = "parking-layout.jpg",
    imagePath: String = "/parking-layout.jpg",
): MultipartFile {
    ImageFile::class.java.getResourceAsStream(imagePath).use {
        return MockMultipartFile(
            fileName, fileName, "image/jpeg", it!!
        )
    }
}

fun stubRegisterParkingImage(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    image: MultipartFile = stubImage(),
): RegisterParkingImage {
    return RegisterParkingImage.of(Building.Code.of(buildingCode), image)
}

fun stubRegisterGarbageImage(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    image: MultipartFile = stubImage(fileName = "dumpster.jpg", imagePath = "/dumpster.jpg"),
): RegisterGarbageImage {
    return RegisterGarbageImage.of(Building.Code.of(buildingCode), image)
}

fun stubAgentPojo(
    eCode: String = DEFAULT_E_CODE,
): AgentPojo {
    return AgentPojo(
        chukaiGyoshaCd = eCode,
    )
}

fun stubAgent(
    eCode: String = DEFAULT_E_CODE,
): Agent {
    return stubAgentPojo(eCode).getAgent()!!
}

fun stubExclusivePropertyTargetWithId(
    id: Long = DEFAULT_EXCLUSIVE_ID,
    companyType: ExclusiveProperty.CompanyType = ExclusiveProperty.CompanyType.Leasing,
    eCode: String? = null,
): ExclusiveProperty.ExclusiveTargetWithId {
    return ExclusiveProperty.ExclusiveTargetWithId(
        id = ExclusiveProperty.Id.of(id),
        target = ExclusiveProperty.ExclusiveTarget(
            companyType = companyType,
            eCode = eCode?.let { Agent.ECode.of(it) },
        )
    )
}

fun stubExclusivePropertyInfo(
    id: Long = DEFAULT_EXCLUSIVE_ID,
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    exclusiveTarget: ExclusiveProperty.ExclusiveTarget,
    salesOfficeCode: String? = null,
    exclusiveFrom: Int = 20241219,
    exclusiveTo: Int = 20241225,
    creationDate: LocalDate = LocalDate.of(2024, 12, 19),
    creator: String = DEFAULT_EMPLOYEE_CODE,
    earlyClosureFlag: Boolean = false,
    updateDate: LocalDate = LocalDate.of(2024, 12, 19),
    updater: String = DEFAULT_EMPLOYEE_CODE,
): ExclusivePropertyInfo {
    return ExclusivePropertyInfo(
        id = ExclusiveProperty.Id.of(id),
        propertyId = stubPropertyId(buildingCode, roomCode),
        buildingName = null,
        roomNumber = null,
        exclusiveRange = DateRange.of(
            from = exclusiveFrom.toString().yyyyMMdd(),
            to = exclusiveTo.toString().yyyyMMdd(),
        ),
        exclusiveTarget = exclusiveTarget,
        exclusiveTargetName = null,
        createDate = creationDate,
        creator = Employee.Name(creator),
        creatorAffiliationOfficeCode = salesOfficeCode?.let { Office.Code.of(it) },
        earlyClosureFlag = earlyClosureFlag,
        updateDate = updateDate,
        updater = Employee.Name(updater),
    )
}

fun stubRegisterExclusiveProperty(
    buildingCode: String = DEFAULT_BUILDING_CODE,
    roomCode: String = DEFAULT_ROOM_CODE,
    exclusiveFrom: LocalDate = LocalDate.of(2025, 4, 18),
    exclusiveTo: LocalDate = LocalDate.of(2025, 4, 29),
    exclusiveTargetWithIds: List<ExclusiveProperty.ExclusiveTargetWithId>,
): RegisterExclusiveProperty {
    return RegisterExclusiveProperty(
        propertyId = stubPropertyId(buildingCode, roomCode),
        exclusiveRange = DateRange.of(exclusiveFrom, exclusiveTo),
        exclusiveTargetWithIds = exclusiveTargetWithIds,
    )
}

fun stubUpdateExclusiveProperty(
    id: Long = DEFAULT_EXCLUSIVE_ID,
    exclusiveFrom: LocalDate = LocalDate.of(2025, 4, 18),
    exclusiveTo: LocalDate = LocalDate.of(2025, 4, 29),
    exclusiveTargetWithIds: List<ExclusiveProperty.ExclusiveTargetWithId>,
): UpdateExclusiveProperty {
    return UpdateExclusiveProperty(
        id = ExclusiveProperty.Id.of(id),
        exclusiveRange = DateRange.of(exclusiveFrom, exclusiveTo),
        exclusiveTargetWithIds = exclusiveTargetWithIds,
    )
}
