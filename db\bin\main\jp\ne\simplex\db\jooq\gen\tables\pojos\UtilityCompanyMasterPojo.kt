/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * ライフライン会社マスタ 既存システム物理名: YCALKP
 */
@Suppress("UNCHECKED_CAST")
data class UtilityCompanyMasterPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updaterId: String? = null,
    var deleteFlag: Byte? = null,
    var utilityCategory: String? = null,
    var utilityCompanyCd: String? = null,
    var utilityCompanyFullName: String? = null,
    var utilityCompanyNameKana: String? = null,
    var utilityCompanyShortName: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: UtilityCompanyMasterPojo = other as UtilityCompanyMasterPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updaterId == null) {
            if (o.updaterId != null)
                return false
        }
        else if (this.updaterId != o.updaterId)
            return false
        if (this.deleteFlag == null) {
            if (o.deleteFlag != null)
                return false
        }
        else if (this.deleteFlag != o.deleteFlag)
            return false
        if (this.utilityCategory == null) {
            if (o.utilityCategory != null)
                return false
        }
        else if (this.utilityCategory != o.utilityCategory)
            return false
        if (this.utilityCompanyCd == null) {
            if (o.utilityCompanyCd != null)
                return false
        }
        else if (this.utilityCompanyCd != o.utilityCompanyCd)
            return false
        if (this.utilityCompanyFullName == null) {
            if (o.utilityCompanyFullName != null)
                return false
        }
        else if (this.utilityCompanyFullName != o.utilityCompanyFullName)
            return false
        if (this.utilityCompanyNameKana == null) {
            if (o.utilityCompanyNameKana != null)
                return false
        }
        else if (this.utilityCompanyNameKana != o.utilityCompanyNameKana)
            return false
        if (this.utilityCompanyShortName == null) {
            if (o.utilityCompanyShortName != null)
                return false
        }
        else if (this.utilityCompanyShortName != o.utilityCompanyShortName)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updaterId == null) 0 else this.updaterId.hashCode())
        result = prime * result + (if (this.deleteFlag == null) 0 else this.deleteFlag.hashCode())
        result = prime * result + (if (this.utilityCategory == null) 0 else this.utilityCategory.hashCode())
        result = prime * result + (if (this.utilityCompanyCd == null) 0 else this.utilityCompanyCd.hashCode())
        result = prime * result + (if (this.utilityCompanyFullName == null) 0 else this.utilityCompanyFullName.hashCode())
        result = prime * result + (if (this.utilityCompanyNameKana == null) 0 else this.utilityCompanyNameKana.hashCode())
        result = prime * result + (if (this.utilityCompanyShortName == null) 0 else this.utilityCompanyShortName.hashCode())
        return result
    }
}
