package jp.ne.simplex.application.controller.external.parking

import jp.ne.simplex.application.controller.external.ExternalRootController
import jp.ne.simplex.application.controller.external.parking.dto.ExternalParkingDetailRequest
import jp.ne.simplex.application.controller.external.parking.dto.ExternalParkingDetailResponse
import jp.ne.simplex.application.service.ConsumptionTaxRateService
import jp.ne.simplex.application.service.ParkingContractService
import jp.ne.simplex.application.service.ParkingDetailsService
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.openapi.ApiDefinition
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.ModelAttribute
import org.springframework.web.bind.annotation.RestController

@RestController
class ExternalParkingDetailController(
    private val parkingDetailsService: ParkingDetailsService,
    private val parkingContractService: ParkingContractService,
    private val consumptionTaxRateService: ConsumptionTaxRateService,
) : ExternalRootController() {
    @Value("\${app.parking.detail.layout-image-base-path}")
    val layoutImageBasePath = ""

    @GetMapping("/parking-detail")
    @ApiDefinition(
        summary = "駐車場詳細取得",
        description = "駐車場詳細情報を取得するAPI",
    )
    fun getParkingDetail(
        @AuthenticationPrincipal authInfo: AuthInfo.ApiKey,
        @ModelAttribute request: ExternalParkingDetailRequest,
    ): ExternalParkingDetailResponse {
        val orderCode = request.toServiceInterface()
        val parkingList =
            parkingDetailsService.getParkingList(orderCode = orderCode, authInfo = authInfo)
        val consumptionTaxRate = consumptionTaxRateService.getConsumptionTaxRate()

        val contractPossibility =
            parkingContractService.getParkingContractPossibility(orderCode, parkingList)
        return ExternalParkingDetailResponse.from(
            contractPossibility,
            parkingList,
            layoutImageBasePath,
            consumptionTaxRate
        )
    }
}
