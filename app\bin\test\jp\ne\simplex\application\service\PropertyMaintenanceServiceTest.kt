package jp.ne.simplex.application.service

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.PropertyMaintenance
import jp.ne.simplex.application.model.UpdatePropertyMaintenance
import jp.ne.simplex.application.repository.db.PropertyRepositoryInterface
import jp.ne.simplex.application.repository.db.ExclusivePropertyRepository
import jp.ne.simplex.db.jooq.gen.tables.references.PROPERTY_MAINTENANCE_INFO
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.exception.ExternalApiServerException
import jp.ne.simplex.exception.ServerValidationException
import jp.ne.simplex.mock.*
import jp.ne.simplex.shared.DSLContextEx.Companion.selectPropertyMaintenanceBy
import jp.ne.simplex.stub.*
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class PropertyMaintenanceServiceTest : AbstractTestContainerTest() {

    override fun beforeEach() {}

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PROPERTY_MAINTENANCE_INFO)
    }

    private lateinit var service: ExclusivePropertyService

    private val allPassAgentRepository = MockAgentRepository(
        // Eコードの存在チェックにパスするように関数をモック
        listByFunc = { eCodeList -> eCodeList.map { stubAgent(it.value) } }
    )

    private val allPassPropertyRepository = MockPropertyRepository(
        listFunc = { ids ->
            ids.map { stubProperty(it.buildingCode.value, it.roomCode.value) }
        }
    )

    @Nested
    @DisplayName("物件メンテナンス処理のバリデーション検証")
    inner class Scenario1 {

        @Nested
        @DisplayName("通常の公開中物件の検証")
        inner class Scenario1x1 {

            @Test
            @DisplayName("公開指示・AD金額・FF金額を変更してもバリデーションに抵触しないこと")
            fun case1() {

                val request = stubUpdatePropertyMaintenance(
                    publishStatus = PropertyMaintenance.PublishStatus.PRIVATE,
                    advertisementFee = 1000,
                    frontFreerentPeriod = 1000F
                )

                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )

                assertDoesNotThrow {
                    PropertyMaintenanceService(
                        propertyRepository = mockPropertyRepository(request),
                        repository = MockPropertyMaintenanceRepository(
                            dslContext,
                            listByFunc = { _ ->
                                listOf(
                                    stubPropertyMaintenanceInfo(
                                        // 掲載区分(いい部屋ネット)
                                        publishStatus = PropertyMaintenance.PublishStatus.PUBLIC,
                                    ),
                                )
                            }
                        ),
                        dkportalRepository = MockDKPortalRepository(),
                        eboardRepository = MockEboardRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(
                        ),
                        exclusivePropertyService = service,
                    ).update(stubJwtRequestUser(), listOf(request))
                }
            }
        }

        @Nested
        @DisplayName("非公開物件の検証")
        inner class Scenario1x2 {

            @Test
            @DisplayName("公開指示・AD金額・FF金額を変更してもバリデーションに抵触しないこと_既存データあり")
            fun case1() {

                val request = stubUpdatePropertyMaintenance(
                    publishStatus = PropertyMaintenance.PublishStatus.PUBLIC,
                    advertisementFee = 1100,
                    frontFreerentPeriod = 1100F
                )

                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )

                assertDoesNotThrow {
                    PropertyMaintenanceService(
                        propertyRepository = mockPropertyRepository(request),
                        repository = MockPropertyMaintenanceRepository(
                            dslContext,
                            listByFunc = { _ ->
                                listOf(
                                    stubPropertyMaintenanceInfo(
                                        // 掲載区分(いい部屋ネット)
                                        publishStatus = PropertyMaintenance.PublishStatus.PRIVATE
                                    ),
                                )
                            }
                        ),
                        dkportalRepository = MockDKPortalRepository(),
                        eboardRepository = MockEboardRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        exclusivePropertyService = service,
                    ).update(stubJwtRequestUser(), listOf(request))
                }
            }

            @Test
            @DisplayName("公開指示・AD金額・FF金額を変更してもバリデーションに抵触しないこと_既存データなし")
            fun case2() {

                val request = stubUpdatePropertyMaintenance(
                    publishStatus = PropertyMaintenance.PublishStatus.PUBLIC,
                    advertisementFee = 1100,
                    frontFreerentPeriod = 1100F
                )

                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )

                assertDoesNotThrow {
                    PropertyMaintenanceService(
                        propertyRepository = mockPropertyRepository(request),
                        repository = MockPropertyMaintenanceRepository(dslContext),
                        dkportalRepository = MockDKPortalRepository(),
                        eboardRepository = MockEboardRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        exclusivePropertyService = service,
                    ).update(stubJwtRequestUser(), listOf(request))
                }
            }
        }

        @Nested
        @DisplayName("公開不可物件の検証")
        inner class Scenario1x3 {

            @Test
            @DisplayName("公開指示が公開に変更されようとしている場合バリデーションに抵触すること")
            fun case1() {

                val request = stubUpdatePropertyMaintenance(
                    publishStatus = PropertyMaintenance.PublishStatus.PUBLIC,
                    advertisementFee = 1000,
                    frontFreerentPeriod = 1000F
                )

                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )

                val err = assertThrows<ServerValidationException> {
                    PropertyMaintenanceService(
                        propertyRepository = MockPropertyRepository(
                            listFunc = { _ ->
                                listOf(
                                    stubProperty(customerCompletionFlag = true)
                                )
                            }),
                        repository = MockPropertyMaintenanceRepository(
                            dslContext,
                            listByFunc = { _ ->
                                listOf(
                                    stubPropertyMaintenanceInfo(
                                        // 掲載区分(いい部屋ネット)
                                        publishStatus = PropertyMaintenance.PublishStatus.PRIVATE
                                    ),
                                )
                            }
                        ),
                        dkportalRepository = MockDKPortalRepository(),
                        eboardRepository = MockEboardRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        exclusivePropertyService = service,
                    ).update(stubJwtRequestUser(), listOf(request))
                }

                assertEquals(
                    err.detail.message,
                    ErrorMessage.PUBLISH_UNAVAILABLE_PROPERTY_NOT_ALLOWED.format().message
                )
            }

            @Test
            @DisplayName("AD金額を変更してもバリデーションに抵触しないこと")
            fun case2() {

                val request = stubUpdatePropertyMaintenance(
                    publishStatus = PropertyMaintenance.PublishStatus.PRIVATE,
                    advertisementFee = 1100,
                    frontFreerentPeriod = 1000F
                )

                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )

                assertDoesNotThrow {
                    PropertyMaintenanceService(
                        propertyRepository = MockPropertyRepository(
                            listFunc = { _ ->
                                listOf(
                                    stubProperty(customerCompletionFlag = true)
                                )
                            }),
                        repository = MockPropertyMaintenanceRepository(
                            dslContext,
                            listByFunc = { _ ->
                                listOf(
                                    stubPropertyMaintenanceInfo(
                                        // 掲載区分(いい部屋ネット)
                                        publishStatus = PropertyMaintenance.PublishStatus.PRIVATE
                                    ),
                                )
                            }
                        ),
                        dkportalRepository = MockDKPortalRepository(),
                        eboardRepository = MockEboardRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        exclusivePropertyService = service,
                    ).update(stubJwtRequestUser(), listOf(request))
                }
            }

            @Test
            @DisplayName("FF金額を変更してもバリデーションに抵触しないこと")
            fun case3() {

                val request = stubUpdatePropertyMaintenance(
                    publishStatus = PropertyMaintenance.PublishStatus.PRIVATE,
                    advertisementFee = 1000,
                    frontFreerentPeriod = 1100F
                )

                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )

                assertDoesNotThrow {
                    PropertyMaintenanceService(
                        propertyRepository = MockPropertyRepository(
                            listFunc = { _ ->
                                listOf(
                                    stubProperty(customerCompletionFlag = true)
                                )
                            }),
                        repository = MockPropertyMaintenanceRepository(
                            dslContext,
                            listByFunc = { _ ->
                                listOf(
                                    stubPropertyMaintenanceInfo(
                                        // 掲載区分(いい部屋ネット)
                                        publishStatus = PropertyMaintenance.PublishStatus.PRIVATE,
                                    ),
                                )
                            }
                        ),
                        dkportalRepository = MockDKPortalRepository(),
                        eboardRepository = MockEboardRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        exclusivePropertyService = service,
                    ).update(stubJwtRequestUser(), listOf(request))
                }
            }

            @Test
            @DisplayName("仮押さえ中物件に対して公開指示をするとバリデーションに抵触すること")
            fun case4() {
                val request = stubUpdatePropertyMaintenance(
                    publishStatus = PropertyMaintenance.PublishStatus.PUBLIC,
                    advertisementFee = 1000,
                    frontFreerentPeriod = 1000F
                )

                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )

                val err = assertThrows<ServerValidationException> {
                    PropertyMaintenanceService(
                        propertyRepository = MockPropertyRepository(
                            listFunc = { _ ->
                                listOf(
                                    // 申込前の物件
                                    stubProperty(
                                        changeDivision = "1",
                                        customerCompletionFlag = false,
                                        moveInApplicationDate = null
                                    )
                                )
                            }),
                        repository = MockPropertyMaintenanceRepository(
                            dslContext,
                            listByFunc = { _ ->
                                listOf(
                                    // 掲載区分(いい部屋ネット)
                                    stubPropertyMaintenanceInfo(
                                        publishStatus = PropertyMaintenance.PublishStatus.PRIVATE
                                    ),
                                )
                            }
                        ),
                        dkportalRepository = MockDKPortalRepository(),
                        eboardRepository = MockEboardRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(
                            findByFunc = { _ ->
                                // 仮押さえ中状態を返す
                                stubOwnCompanyTemporaryReservationInfo()
                            }
                        ),
                        exclusivePropertyService = service,
                    ).update(stubJwtRequestUser(), listOf(request))
                }

                assertEquals(
                    err.detail.message,
                    ErrorMessage.PUBLISH_UNAVAILABLE_STATUS_TEMPORARY_RESERVATION.format().message
                )
            }

            @Test
            @DisplayName("方位が未設定の場合、公開指示を行うとバリデーションに抵触すること")
            fun case5() {
                val request = stubUpdatePropertyMaintenance(
                    publishStatus = PropertyMaintenance.PublishStatus.PUBLIC
                )

                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )

                val err = assertThrows<ServerValidationException> {
                    PropertyMaintenanceService(
                        propertyRepository = MockPropertyRepository(
                            listFunc = { _ ->
                                listOf(
                                    stubProperty(
                                        direction = null,
                                        changeDivision = "1",
                                    ) // 方位未設定
                                )
                            }),
                        repository = MockPropertyMaintenanceRepository(
                            dslContext,
                            listByFunc = { _ ->
                                listOf(
                                    stubPropertyMaintenanceInfo(
                                        publishStatus = PropertyMaintenance.PublishStatus.PRIVATE
                                    ),
                                )
                            }
                        ),
                        dkportalRepository = MockDKPortalRepository(),
                        eboardRepository = MockEboardRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        exclusivePropertyService = service,
                    ).update(stubJwtRequestUser(), listOf(request))
                }

                assertEquals(
                    err.detail.message,
                    ErrorMessage.DIRECTION_NOT_SET.format().message
                )
            }
        }
    }

    @Nested
    @DisplayName("「公開指示」の更新処理では、「ADFF」の更新処理で成功した物件かついい物件ボードのAPIに成功した物件のみがDBに永続化されること")
    inner class Scenario2 {

        @Test
        @DisplayName("前処理である「ADFF」の更新処理に成功した物件のみが、いい物件ボードのAPIのインプットになること")
        fun case1() {
            // condition
            val request1 = stubUpdatePropertyMaintenance(buildingCode = "215452000")
            val request2 = stubUpdatePropertyMaintenance(buildingCode = "876543000")
            val request3 = stubUpdatePropertyMaintenance(buildingCode = "159043000")

            service = ExclusivePropertyService(
                dslContext = dslContext,
                repository = ExclusivePropertyRepository(dslContext),
                agentRepository = allPassAgentRepository,
                propertyRepository = allPassPropertyRepository,
                dkPortalRepository = MockDKPortalRepository(),
                temporaryReservationRepository = MockTemporaryReservationRepository(),
                propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                    dslContext
                ),
                employeeRepository = MockEmployeeRepository()
            )

            // setup
            val service = PropertyMaintenanceService(
                propertyRepository = mockPropertyRepository(request1, request2, request3),
                // 「公開指示」の更新処理以外の永続化関数で何も実行しないようにする
                repository = MockPropertyMaintenanceRepository(
                    dslContext,
                    updateAdFfFunc = { _ -> },
                    listByFunc = { _ ->
                        listOf(
                            stubPropertyMaintenanceInfo(buildingCode = "215452000"),
                            stubPropertyMaintenanceInfo(buildingCode = "876543000"),
                            stubPropertyMaintenanceInfo(buildingCode = "159043000")
                        )
                    }
                ),
                dkportalRepository = MockDKPortalRepository(
                    updateAdFfFunc = { _ ->
                        UpdatePropertyMaintenance.Result(
                            success = listOf(request1.id),
                            failed = listOf(request2.id, request3.id)
                        )
                    }
                ),
                eboardRepository = MockEboardRepository(
                    instructPublicFunc = { _, updatePublishStatusList ->
                        // verify
                        // 前処理のDK PORTALへのADFF更新のAPIでリクエスト①のみ成功しているので
                        // ここでは、request1 しか入ってこないことを確認する
                        assertEquals(1, updatePublishStatusList.size)
                        assertEquals(request1.id, updatePublishStatusList.first().id)

                        UpdatePropertyMaintenance.Result(emptyMap(), emptyList())
                    },
                ),
                temporaryReservationRepository = MockTemporaryReservationRepository(),
                exclusivePropertyService = service,
            )

            // execute
            service.update(stubJwtRequestUser(), listOf(request1, request2, request3))
        }

        @Test
        @DisplayName("DKポータルのAPI結果によらず、いい物件ボードのAPIに成功した物件のみがDBに永続化されること")
        fun case2() {
            // condition
            val request1 = stubUpdatePropertyMaintenance(buildingCode = "215452000")
            val request2 = stubUpdatePropertyMaintenance(buildingCode = "876543000")
            val request3 = stubUpdatePropertyMaintenance(buildingCode = "159043000")

            service = ExclusivePropertyService(
                dslContext = dslContext,
                repository = ExclusivePropertyRepository(dslContext),
                agentRepository = allPassAgentRepository,
                propertyRepository = allPassPropertyRepository,
                dkPortalRepository = MockDKPortalRepository(),
                temporaryReservationRepository = MockTemporaryReservationRepository(),
                propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                    dslContext
                ),
                employeeRepository = MockEmployeeRepository()
            )
            // setup
            val service = PropertyMaintenanceService(
                propertyRepository = mockPropertyRepository(request1, request2, request3),
                // 「公開指示」の更新処理以外の永続化関数で何も実行しないようにする
                repository = MockPropertyMaintenanceRepository(
                    dslContext,
                    updateAdFfFunc = { _ -> },
                    listByFunc = { _ ->
                        listOf(
                            stubPropertyMaintenanceInfo(buildingCode = "215452000"),
                            stubPropertyMaintenanceInfo(buildingCode = "876543000"),
                            stubPropertyMaintenanceInfo(buildingCode = "159043000")
                        )
                    }
                ),
                dkportalRepository = MockDKPortalRepository(
                    updateRoomStatusFunc = { _ ->
                        throw ExternalApiServerException(
                            ErrorType.DK_PORTAL_API_ERROR,
                            ErrorMessage.DK_PORTAL_RECEIVED_ERROR_RESPONSE.format()
                        )
                    }
                ),
                eboardRepository = MockEboardRepository(
                    instructPublicFunc = { _, _ ->
                        UpdatePropertyMaintenance.Result(
                            success = mapOf(
                                request1.id to Property.UpState.PREPARING,
                                request3.id to Property.UpState.ALREADY_MOVED_IN
                            ),
                            failed = listOf(request2.id)
                        )
                    },
                ),
                temporaryReservationRepository = MockTemporaryReservationRepository(),
                exclusivePropertyService = service,
            )

            // execute
            service.update(stubJwtRequestUser(), listOf(request1, request2, request3))

            // verify
            // リクエスト②は、setup記載の通り、いい物件のAPIで失敗するので後続のDB永続化は実施されない
            assertEquals(1, dslContext.selectPropertyMaintenanceBy(request1.id).size)
            assertEquals(0, dslContext.selectPropertyMaintenanceBy(request2.id).size)
            assertEquals(1, dslContext.selectPropertyMaintenanceBy(request3.id).size)
        }
    }

    @Nested
    @DisplayName("「公開指示」の更新処理では、いい物件ボードのAPIに成功した物件のみがDBに永続化されること")
    inner class Scenairo3 {

        @Test
        @DisplayName("いい物件から、200で、upState= 空でレスポンスが返ってきた場合、DBに永続化されること")
        fun case1() {
            // condition
            val request1 = stubUpdatePropertyMaintenance(buildingCode = "215452000")
            val request2 = stubUpdatePropertyMaintenance(buildingCode = "876543000")
            val request3 = stubUpdatePropertyMaintenance(buildingCode = "159043000")

            service = ExclusivePropertyService(
                dslContext = dslContext,
                repository = ExclusivePropertyRepository(dslContext),
                agentRepository = allPassAgentRepository,
                propertyRepository = allPassPropertyRepository,
                dkPortalRepository = MockDKPortalRepository(),
                temporaryReservationRepository = MockTemporaryReservationRepository(),
                propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                    dslContext
                ),
                employeeRepository = MockEmployeeRepository()
            )
            // setup
            val service = PropertyMaintenanceService(
                propertyRepository = mockPropertyRepository(request1, request2, request3),
                // 「公開指示」の更新処理以外の永続化関数で何も実行しないようにする
                repository = MockPropertyMaintenanceRepository(
                    dslContext,
                    updateAdFfFunc = { _ -> },
                    listByFunc = { _ ->
                        listOf(
                            stubPropertyMaintenanceInfo(buildingCode = "215452000"),
                            stubPropertyMaintenanceInfo(buildingCode = "876543000"),
                            stubPropertyMaintenanceInfo(buildingCode = "159043000")
                        )
                    }
                ),
                dkportalRepository = MockDKPortalRepository(
                    updateRoomStatusFunc = { list ->
                        val requestIds = list.map { it.id }
                        assertTrue(requestIds.contains(request1.id))
                        assertFalse(requestIds.contains(request2.id))
                        assertFalse(requestIds.contains(request3.id))

                        UpdatePropertyMaintenance.Result(requestIds, emptyList())
                    }
                ),
                eboardRepository = MockEboardRepository(
                    instructPublicFunc = { _, _ ->
                        UpdatePropertyMaintenance.Result(
                            success = mapOf(
                                request1.id to Property.UpState.PREPARING,
                                request3.id to null
                            ),
                            failed = listOf(request2.id)
                        )
                    },
                ),
                temporaryReservationRepository = MockTemporaryReservationRepository(),
                exclusivePropertyService = service,
            )

            // execute
            service.update(stubJwtRequestUser(), listOf(request1, request2, request3))

            // verify
            // リクエスト②は、setup記載の通り、いい物件のAPIで失敗するので後続のDB永続化は実施されない
            assertEquals(1, dslContext.selectPropertyMaintenanceBy(request1.id).size)
            assertEquals(0, dslContext.selectPropertyMaintenanceBy(request2.id).size)
            assertEquals(1, dslContext.selectPropertyMaintenanceBy(request3.id).size)
        }
    }

    private fun mockPropertyRepository(vararg request: UpdatePropertyMaintenance): PropertyRepositoryInterface {
        return MockPropertyRepository(listFunc = { _ ->
            request.map {
                stubProperty(
                    buildingCode = it.id.buildingCode.value,
                    roomCode = it.id.roomCode.value,
                    changeDivision = "1"
                )
            }
        })
    }
}
