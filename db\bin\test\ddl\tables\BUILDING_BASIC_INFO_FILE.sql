-- TABLE: BUILDING_BASIC_INFO_FILE(建物基本情報ファイル)

CREATE TABLE BUILDING_BASIC_INFO_FILE(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PGM                                   varchar(10)                   
,    UPDATE_RESPONSIBLE_CD                        varchar(10)                   
,    BUILDING_CD                                  varchar(9)                    
,    EQUIPMENT_INFO_TENTATIVE_CONFIRM_DATE        numeric(8)                    
,    EQUIPMENT_INFO_CONFIRM_DATE                  numeric(8)                    
,    EQUIPMENT_DETAIL_COMPLETION_DATE             numeric(8)                    
,    BUILDING_STRUCTURE                           varchar(2)                    
,    ROOF_FINISH                                  varchar(2)                    
,    EXTERIOR_WALL                                varchar(2)                    
,    TOILET_TYPE                                  varchar(1)                    
,    DRAINAGE_TYPE                                varchar(1)                    
,    LAND_AREA                                    numeric(7,2)                  
,    PLANTING_AREA                                numeric(7,2)                  
,    UNPAVED_PARKING_AREA                         numeric(7,2)                  
,    SICK_HOUSE_MEASURES                          varchar(1)                    
,    PAINTING_WITH_WARRANTY                       varchar(1)                    
,    PAINTING_WARRANTY_DATE                       numeric(8)                    
,    WATER_SUPPLY_TYPE                            varchar(1)                    
,    WATER_METER_TYPE                             varchar(1)                    
,    WATER_METER_SHARED                           varchar(1)                    
,    WATER_METER_DIRECT                           numeric(1)                    
,    WATER_PIPE_DIAMETER_MAIN                     numeric(3)                    
,    WATER_PIPE_DIAMETER_UNIT                     numeric(3)                    
,    WATER_PIPE_DIAMETER_BUSINESS                 numeric(3)                    
,    WATER_BUREAU_COMPANY_CD                      varchar(6)                    
,    WATER_BUREAU_BRANCH_CD                       varchar(3)                    
,    ELECTRIC_METER_TYPE                          varchar(1)                    
,    ELECTRIC_METER_SHARED                        varchar(1)                    
,    ELECTRIC_METER_DIRECT                        numeric(1)                    
,    ELECTRIC_COMPANY_CD                          varchar(6)                    
,    ELECTRIC_BRANCH_CD                           varchar(3)                    
,    GAS_TYPE                                     varchar(1)                    
,    GAS_METER_TYPE                               varchar(1)                    
,    GAS_COMPANY_CD                               varchar(6)                    
,    GAS_BRANCH_CD                                varchar(3)                    
,    DELETE_DATE                                  numeric(8)                    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BUILDING_BASIC_INFO_FILE IS '建物基本情報ファイル 既存システム物理名: HAD40P';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: HAD01D @290';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.CREATION_TIME IS '作成時間 既存システム物理名: HAD02H @290';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: HAD03D @290';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.UPDATE_TIME IS '更新時間 既存システム物理名: HAD04H';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.UPDATE_PGM IS '更新PGM 既存システム物理名: HAD05M';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.UPDATE_RESPONSIBLE_CD IS '更新担当者CD 既存システム物理名: HAD06M';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.BUILDING_CD IS '建物CD 既存システム物理名: HAD07C';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.EQUIPMENT_INFO_TENTATIVE_CONFIRM_DATE IS '設備情報仮確定日 既存システム物理名: HAD08D';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.EQUIPMENT_INFO_CONFIRM_DATE IS '設備情報確定日 既存システム物理名: HAD09D';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.EQUIPMENT_DETAIL_COMPLETION_DATE IS '設備詳細完了日 既存システム物理名: HAD52D';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.BUILDING_STRUCTURE IS '建物構造 既存システム物理名: HAD10B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.ROOF_FINISH IS '屋根仕上 既存システム物理名: HAD14B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.EXTERIOR_WALL IS '外壁 既存システム物理名: HAD15B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.TOILET_TYPE IS 'トイレ区分 既存システム物理名: HAD16B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.DRAINAGE_TYPE IS '排水区分 既存システム物理名: HAD17B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.LAND_AREA IS '敷地面積 既存システム物理名: HAD30A';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.PLANTING_AREA IS '植裁面積 既存システム物理名: HAD31A';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.UNPAVED_PARKING_AREA IS '駐車場未舗装面積 既存システム物理名: HAD49A';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.SICK_HOUSE_MEASURES IS 'シックハウス対策有無 既存システム物理名: HAD61B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.PAINTING_WITH_WARRANTY IS '保証付塗装 既存システム物理名: HAD50B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.PAINTING_WARRANTY_DATE IS '保証付塗装施工日 既存システム物理名: HAD51D';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.WATER_SUPPLY_TYPE IS '水道区分 既存システム物理名: HAD24B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.WATER_METER_TYPE IS '水道メータ区分 既存システム物理名: HAD27B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.WATER_METER_SHARED IS '水道メータ・共用 既存システム物理名: HAD53B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.WATER_METER_DIRECT IS '水道メータ・直圧 既存システム物理名: HAD45B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.WATER_PIPE_DIAMETER_MAIN IS '水道口径・本管 既存システム物理名: HAD33A';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.WATER_PIPE_DIAMETER_UNIT IS '水道口径・各戸 既存システム物理名: HAD34A';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.WATER_PIPE_DIAMETER_BUSINESS IS '水道口径・事業 既存システム物理名: HAD35A';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.WATER_BUREAU_COMPANY_CD IS '水道局会社CD 既存システム物理名: HAD55C';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.WATER_BUREAU_BRANCH_CD IS '水道局支社CD 既存システム物理名: HAD56C';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.ELECTRIC_METER_TYPE IS '電気メータ区分 既存システム物理名: HAD28B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.ELECTRIC_METER_SHARED IS '電気メータ・共用 既存システム物理名: HAD54B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.ELECTRIC_METER_DIRECT IS '電気メータ・直圧 既存システム物理名: HAD46B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.ELECTRIC_COMPANY_CD IS '電力会社CD 既存システム物理名: HAD57C';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.ELECTRIC_BRANCH_CD IS '電力支社CD 既存システム物理名: HAD58C';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.GAS_TYPE IS 'ガス区分 既存システム物理名: HAD26B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.GAS_METER_TYPE IS 'ガスメータ区分 既存システム物理名: HAD29B';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.GAS_COMPANY_CD IS 'ガス会社CD 既存システム物理名: HAD59C';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.GAS_BRANCH_CD IS 'ガス支社CD 既存システム物理名: HAD60C';
COMMENT ON COLUMN BUILDING_BASIC_INFO_FILE.DELETE_DATE IS '削除日 既存システム物理名: HAD99D';
