-- TABLE: PARKING_ENABLE(駐車場利用停止)

CREATE TABLE PARKING_ENABLE(
     BUILDING_CODE                                varchar(9)        NOT NULL    
,    PARKING_LOT_CODE                             varchar(3)        NOT NULL    
,    PARKING_LOT_ENABLE                           varchar(1)        NOT NULL    
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    DELETE_FLAG                                  varchar(1)        NOT NULL    
,    CONSTRAINT PK_PARKING_ENABLE PRIMARY KEY (BUILDING_CODE, PARKING_LOT_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PARKING_ENABLE IS '駐車場利用停止 既存システム物理名: -';
COMMENT ON COLUMN PARKING_ENABLE.BUILDING_CODE IS '建物コード 既存システム物理名: -';
COMMENT ON COLUMN PARKING_ENABLE.PARKING_LOT_CODE IS '駐車場コード 既存システム物理名: -';
COMMENT ON COLUMN PARKING_ENABLE.PARKING_LOT_ENABLE IS '区画利用可否 既存システム物理名: -';
COMMENT ON COLUMN PARKING_ENABLE.CREATION_DATE IS '作成年月日 既存システム物理名: -';
COMMENT ON COLUMN PARKING_ENABLE.CREATION_TIME IS '作成時刻 既存システム物理名: -';
COMMENT ON COLUMN PARKING_ENABLE.CREATOR IS '作成者 既存システム物理名: -';
COMMENT ON COLUMN PARKING_ENABLE.UPDATE_DATE IS '更新年月日 既存システム物理名: -';
COMMENT ON COLUMN PARKING_ENABLE.UPDATE_TIME IS '更新時刻 既存システム物理名: -';
COMMENT ON COLUMN PARKING_ENABLE.UPDATER IS '更新者 既存システム物理名: -';
COMMENT ON COLUMN PARKING_ENABLE.DELETE_FLAG IS '削除フラグ 既存システム物理名: -';
