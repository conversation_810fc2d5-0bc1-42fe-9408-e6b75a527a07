-- TABLE: MNG_ONLY_24H_SUPPORT_BUILDING(管理のみ24時間サポート建物)

CREATE TABLE MNG_ONLY_24H_SUPPORT_BUILDING(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    CREATION_PROGRAM_ID                          varchar(10)                   
,    CREATION_TERMINAL_ID                         varchar(10)                   
,    CREATION_RESPONSIBLE_ID                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATE_TERMINAL_ID                           varchar(10)                   
,    UPDATE_RESPONSIBLE_ID                        varchar(6)                    
,    DELETE_FLAG                                  numeric(1)                    
,    BUILDING_CD                                  varchar(9)                    
,    APPROVAL_DATE                                numeric(8)                    
,    STATUS                                       numeric(1)                    
,    APPROVAL_REJECTION_INPUT_PERSON              varchar(6)                    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE MNG_ONLY_24H_SUPPORT_BUILDING IS '管理のみ24時間サポート建物 既存システム物理名: HLB31P';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.CREATION_DATE IS '作成年月日 既存システム物理名: HLB01D @290';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.CREATION_TIME IS '作成時刻 既存システム物理名: HLB02H @290';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.CREATION_PROGRAM_ID IS '作成プログラムID 既存システム物理名: HLB03M @290';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.CREATION_TERMINAL_ID IS '作成端末ID 既存システム物理名: HLB04M';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.CREATION_RESPONSIBLE_ID IS '作成担当者ID 既存システム物理名: HLB05C';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.UPDATE_DATE IS '更新年月日 既存システム物理名: HLB06D';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.UPDATE_TIME IS '更新時刻 既存システム物理名: HLB07H';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: HLB08M';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.UPDATE_TERMINAL_ID IS '更新端末ID 既存システム物理名: HLB09M';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.UPDATE_RESPONSIBLE_ID IS '更新担当者ID 既存システム物理名: HLB10C';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.DELETE_FLAG IS '削除フラグ 既存システム物理名: HLB11B';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.BUILDING_CD IS '建物CD 既存システム物理名: HLB12C';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.APPROVAL_DATE IS '承諾日 既存システム物理名: HLB13D';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.STATUS IS 'ステータス 既存システム物理名: HLB14B';
COMMENT ON COLUMN MNG_ONLY_24H_SUPPORT_BUILDING.APPROVAL_REJECTION_INPUT_PERSON IS '承諾・拒否等入力者 既存システム物理名: HLB15C';
