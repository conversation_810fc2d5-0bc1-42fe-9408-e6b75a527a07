/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal
import java.math.BigInteger

import jp.ne.simplex.db.jooq.gen.tables.SitePropertyKentakuForPTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.SitePropertyKentakuForPPojo

import org.jooq.impl.TableRecordImpl


/**
 * DK-PORTAL用物件データファイル 既存システム物理名: SITE_PROPERTY_KENTAKU_FOR_P
 */
@Suppress("UNCHECKED_CAST")
open class SitePropertyKentakuForPRecord private constructor() : TableRecordImpl<SitePropertyKentakuForPRecord>(SitePropertyKentakuForPTable.SITE_PROPERTY_KENTAKU_FOR_P) {

    open var propertyFullId: BigInteger?
        set(value): Unit = set(0, value)
        get(): BigInteger? = get(0) as BigInteger?

    open var buildingId: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var renewDate: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var distanceFromStation_1: BigDecimal?
        set(value): Unit = set(3, value)
        get(): BigDecimal? = get(3) as BigDecimal?

    open var walkFromStation_1: Short?
        set(value): Unit = set(4, value)
        get(): Short? = get(4) as Short?

    open var busFromStation_1: Short?
        set(value): Unit = set(5, value)
        get(): Short? = get(5) as Short?

    open var busStopName_1: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var fromBusStop_1: Short?
        set(value): Unit = set(7, value)
        get(): Short? = get(7) as Short?

    open var distanceFromBusstop_1: BigDecimal?
        set(value): Unit = set(8, value)
        get(): BigDecimal? = get(8) as BigDecimal?

    open var nearestRoute_1: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var nearestStation_1: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var kindaikaCodeText_1: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var wayToCode_1: Byte?
        set(value): Unit = set(12, value)
        get(): Byte? = get(12) as Byte?

    open var distanceFromStation_2: BigDecimal?
        set(value): Unit = set(13, value)
        get(): BigDecimal? = get(13) as BigDecimal?

    open var walkFromStation_2: Short?
        set(value): Unit = set(14, value)
        get(): Short? = get(14) as Short?

    open var busFromStation_2: Short?
        set(value): Unit = set(15, value)
        get(): Short? = get(15) as Short?

    open var busStopName_2: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var fromBusStop_2: Short?
        set(value): Unit = set(17, value)
        get(): Short? = get(17) as Short?

    open var distanceFromBusstop_2: BigDecimal?
        set(value): Unit = set(18, value)
        get(): BigDecimal? = get(18) as BigDecimal?

    open var nearestRoute_2: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var nearestStation_2: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var kindaikaCodeText_2: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var wayToCode_2: Byte?
        set(value): Unit = set(22, value)
        get(): Byte? = get(22) as Byte?

    open var distanceFromStation_3: BigDecimal?
        set(value): Unit = set(23, value)
        get(): BigDecimal? = get(23) as BigDecimal?

    open var walkFromStation_3: Short?
        set(value): Unit = set(24, value)
        get(): Short? = get(24) as Short?

    open var busFromStation_3: Short?
        set(value): Unit = set(25, value)
        get(): Short? = get(25) as Short?

    open var busStopName_3: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var fromBusStop_3: Short?
        set(value): Unit = set(27, value)
        get(): Short? = get(27) as Short?

    open var distanceFromBusstop_3: BigDecimal?
        set(value): Unit = set(28, value)
        get(): BigDecimal? = get(28) as BigDecimal?

    open var nearestRoute_3: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var nearestStation_3: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var kindaikaCodeText_3: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var wayToCode_3: Byte?
        set(value): Unit = set(32, value)
        get(): Byte? = get(32) as Byte?

    open var zipCodeText: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var prefecture: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var city: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var town: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var tyoume: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var kokudoCodeText: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var jisCodeValue: Int?
        set(value): Unit = set(39, value)
        get(): Int? = get(39) as Int?

    open var townCodeValue: String?
        set(value): Unit = set(40, value)
        get(): String? = get(40) as String?

    open var tyoumeCodeValue: Short?
        set(value): Unit = set(41, value)
        get(): Short? = get(41) as Short?

    open var restaddr1: String?
        set(value): Unit = set(42, value)
        get(): String? = get(42) as String?

    open var latitude: BigInteger?
        set(value): Unit = set(43, value)
        get(): BigInteger? = get(43) as BigInteger?

    open var longitude: BigInteger?
        set(value): Unit = set(44, value)
        get(): BigInteger? = get(44) as BigInteger?

    open var buildingName: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var dispNameCode: Byte?
        set(value): Unit = set(46, value)
        get(): Byte? = get(46) as Byte?

    open var buildingFurigana: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var kindCode: Byte?
        set(value): Unit = set(48, value)
        get(): Byte? = get(48) as Byte?

    open var kindDispName: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    open var saleBlockNum: Int?
        set(value): Unit = set(50, value)
        get(): Int? = get(50) as Int?

    open var emptyHousesNum: Int?
        set(value): Unit = set(51, value)
        get(): Int? = get(51) as Int?

    open var sellingCompany: String?
        set(value): Unit = set(52, value)
        get(): String? = get(52) as String?

    open var completionDate: Int?
        set(value): Unit = set(53, value)
        get(): Int? = get(53) as Int?

    open var areaWays1Code: Byte?
        set(value): Unit = set(54, value)
        get(): Byte? = get(54) as Byte?

    open var structureCode: Byte?
        set(value): Unit = set(55, value)
        get(): Byte? = get(55) as Byte?

    open var structureDispName: String?
        set(value): Unit = set(56, value)
        get(): String? = get(56) as String?

    open var buildingTypeCode: Byte?
        set(value): Unit = set(57, value)
        get(): Byte? = get(57) as Byte?

    open var allFloorNum: Short?
        set(value): Unit = set(58, value)
        get(): Short? = get(58) as Short?

    open var underFloorNum: Byte?
        set(value): Unit = set(59, value)
        get(): Byte? = get(59) as Byte?

    open var newUsedCode: Byte?
        set(value): Unit = set(60, value)
        get(): Byte? = get(60) as Byte?

    open var managerStyleCode: Byte?
        set(value): Unit = set(61, value)
        get(): Byte? = get(61) as Byte?

    open var managerComment: String?
        set(value): Unit = set(62, value)
        get(): String? = get(62) as String?

    open var quietCode: Byte?
        set(value): Unit = set(63, value)
        get(): Byte? = get(63) as Byte?

    open var gasCode: Byte?
        set(value): Unit = set(64, value)
        get(): Byte? = get(64) as Byte?

    open var waterSupplyCode: Byte?
        set(value): Unit = set(65, value)
        get(): Byte? = get(65) as Byte?

    open var wasteWaterCode: Byte?
        set(value): Unit = set(66, value)
        get(): Byte? = get(66) as Byte?

    open var elecPowerCode: Byte?
        set(value): Unit = set(67, value)
        get(): Byte? = get(67) as Byte?

    open var twoByFourCode: Byte?
        set(value): Unit = set(68, value)
        get(): Byte? = get(68) as Byte?

    open var sellTypeCode: Byte?
        set(value): Unit = set(69, value)
        get(): Byte? = get(69) as Byte?

    open var avoidQuakeCode: Byte?
        set(value): Unit = set(70, value)
        get(): Byte? = get(70) as Byte?

    open var barrierFreeCode: Byte?
        set(value): Unit = set(71, value)
        get(): Byte? = get(71) as Byte?

    open var fulltimeManagementCode: Byte?
        set(value): Unit = set(72, value)
        get(): Byte? = get(72) as Byte?

    open var liftCode: Byte?
        set(value): Unit = set(73, value)
        get(): Byte? = get(73) as Byte?

    open var liftNumCode: Byte?
        set(value): Unit = set(74, value)
        get(): Byte? = get(74) as Byte?

    open var wallTypeCode: Byte?
        set(value): Unit = set(75, value)
        get(): Byte? = get(75) as Byte?

    open var deliveryMailboxCode: Byte?
        set(value): Unit = set(76, value)
        get(): Byte? = get(76) as Byte?

    open var launderetteCode: Byte?
        set(value): Unit = set(77, value)
        get(): Byte? = get(77) as Byte?

    open var roomNumberText: String?
        set(value): Unit = set(78, value)
        get(): String? = get(78) as String?

    open var dispRoomNumberCode: Byte?
        set(value): Unit = set(79, value)
        get(): Byte? = get(79) as Byte?

    open var salesPoint: String?
        set(value): Unit = set(80, value)
        get(): String? = get(80) as String?

    open var remark1: String?
        set(value): Unit = set(81, value)
        get(): String? = get(81) as String?

    open var remark2: String?
        set(value): Unit = set(82, value)
        get(): String? = get(82) as String?

    open var specialRemark: String?
        set(value): Unit = set(83, value)
        get(): String? = get(83) as String?

    open var note: String?
        set(value): Unit = set(84, value)
        get(): String? = get(84) as String?

    open var price: Long?
        set(value): Unit = set(85, value)
        get(): Long? = get(85) as Long?

    open var priceTaxCode: Byte?
        set(value): Unit = set(86, value)
        get(): Byte? = get(86) as Byte?

    open var consumptionTax: BigDecimal?
        set(value): Unit = set(87, value)
        get(): BigDecimal? = get(87) as BigDecimal?

    open var queryPerson: String?
        set(value): Unit = set(88, value)
        get(): String? = get(88) as String?

    open var firmSideCode: String?
        set(value): Unit = set(89, value)
        get(): String? = get(89) as String?

    open var intoCode: Byte?
        set(value): Unit = set(90, value)
        get(): Byte? = get(90) as Byte?

    open var intoDate: Int?
        set(value): Unit = set(91, value)
        get(): Int? = get(91) as Int?

    open var leaveDate: Int?
        set(value): Unit = set(92, value)
        get(): Int? = get(92) as Int?

    open var otherCompanyCode: Byte?
        set(value): Unit = set(93, value)
        get(): Byte? = get(93) as Byte?

    open var messageToOtherCompany: String?
        set(value): Unit = set(94, value)
        get(): String? = get(94) as String?

    open var registDate: Int?
        set(value): Unit = set(95, value)
        get(): Int? = get(95) as Int?

    open var registTime: String?
        set(value): Unit = set(96, value)
        get(): String? = get(96) as String?

    open var rentExchangeStyleCode: Byte?
        set(value): Unit = set(97, value)
        get(): Byte? = get(97) as Byte?

    open var housePlanCode: Byte?
        set(value): Unit = set(98, value)
        get(): Byte? = get(98) as Byte?

    open var roomNum: Short?
        set(value): Unit = set(99, value)
        get(): Short? = get(99) as Short?

    open var housePlanEquiv: Int?
        set(value): Unit = set(100, value)
        get(): Int? = get(100) as Int?

    open var windowDirectionCode: Byte?
        set(value): Unit = set(101, value)
        get(): Byte? = get(101) as Byte?

    open var floorNum: String?
        set(value): Unit = set(102, value)
        get(): String? = get(102) as String?

    open var nonMoveintoCode: Byte?
        set(value): Unit = set(103, value)
        get(): Byte? = get(103) as Byte?

    open var managedPropertyCode: Byte?
        set(value): Unit = set(104, value)
        get(): Byte? = get(104) as Byte?

    open var petCode: Byte?
        set(value): Unit = set(105, value)
        get(): Byte? = get(105) as Byte?

    open var officeCode: Byte?
        set(value): Unit = set(106, value)
        get(): Byte? = get(106) as Byte?

    open var musicalCode: Byte?
        set(value): Unit = set(107, value)
        get(): Byte? = get(107) as Byte?

    open var housePlanDispName: String?
        set(value): Unit = set(108, value)
        get(): String? = get(108) as String?

    open var usePartArea: BigDecimal?
        set(value): Unit = set(109, value)
        get(): BigDecimal? = get(109) as BigDecimal?

    open var keyMoney: BigDecimal?
        set(value): Unit = set(110, value)
        get(): BigDecimal? = get(110) as BigDecimal?

    open var keyMoneyUnitCode: Byte?
        set(value): Unit = set(111, value)
        get(): Byte? = get(111) as Byte?

    open var keyMoneyTaxCode: Byte?
        set(value): Unit = set(112, value)
        get(): Byte? = get(112) as Byte?

    open var deposit: BigDecimal?
        set(value): Unit = set(113, value)
        get(): BigDecimal? = get(113) as BigDecimal?

    open var depositUnitCode: Byte?
        set(value): Unit = set(114, value)
        get(): Byte? = get(114) as Byte?

    open var repairCost: BigDecimal?
        set(value): Unit = set(115, value)
        get(): BigDecimal? = get(115) as BigDecimal?

    open var repairCostUnitCode: Byte?
        set(value): Unit = set(116, value)
        get(): Byte? = get(116) as Byte?

    open var guaranty: BigDecimal?
        set(value): Unit = set(117, value)
        get(): BigDecimal? = get(117) as BigDecimal?

    open var guarantyUnitCode: Byte?
        set(value): Unit = set(118, value)
        get(): Byte? = get(118) as Byte?

    open var syokyakuClassCode: Byte?
        set(value): Unit = set(119, value)
        get(): Byte? = get(119) as Byte?

    open var syokyaku: BigDecimal?
        set(value): Unit = set(120, value)
        get(): BigDecimal? = get(120) as BigDecimal?

    open var syokyakuUnitCode: Byte?
        set(value): Unit = set(121, value)
        get(): Byte? = get(121) as Byte?

    open var premium: BigDecimal?
        set(value): Unit = set(122, value)
        get(): BigDecimal? = get(122) as BigDecimal?

    open var premiumUnitCode: Byte?
        set(value): Unit = set(123, value)
        get(): Byte? = get(123) as Byte?

    open var premiumTaxCode: Byte?
        set(value): Unit = set(124, value)
        get(): Byte? = get(124) as Byte?

    open var manageCost: Int?
        set(value): Unit = set(125, value)
        get(): Int? = get(125) as Int?

    open var manageCostTaxCode: Byte?
        set(value): Unit = set(126, value)
        get(): Byte? = get(126) as Byte?

    open var serviceFee: Int?
        set(value): Unit = set(127, value)
        get(): Int? = get(127) as Int?

    open var serviceFeeFreeCode: Byte?
        set(value): Unit = set(128, value)
        get(): Byte? = get(128) as Byte?

    open var serviceFeeTaxCode: Byte?
        set(value): Unit = set(129, value)
        get(): Byte? = get(129) as Byte?

    open var zappi: Int?
        set(value): Unit = set(130, value)
        get(): Int? = get(130) as Int?

    open var zappiTaxCode: Byte?
        set(value): Unit = set(131, value)
        get(): Byte? = get(131) as Byte?

    open var otherCostComment: String?
        set(value): Unit = set(132, value)
        get(): String? = get(132) as String?

    open var otherCost_1: Int?
        set(value): Unit = set(133, value)
        get(): Int? = get(133) as Int?

    open var otherCostItem_1: String?
        set(value): Unit = set(134, value)
        get(): String? = get(134) as String?

    open var otherCostTaxCode_1: Byte?
        set(value): Unit = set(135, value)
        get(): Byte? = get(135) as Byte?

    open var otherCost_2: Int?
        set(value): Unit = set(136, value)
        get(): Int? = get(136) as Int?

    open var otherCostItem_2: String?
        set(value): Unit = set(137, value)
        get(): String? = get(137) as String?

    open var otherCostTaxCode_2: Byte?
        set(value): Unit = set(138, value)
        get(): Byte? = get(138) as Byte?

    open var otherCost_3: Int?
        set(value): Unit = set(139, value)
        get(): Int? = get(139) as Int?

    open var otherCostItem_3: String?
        set(value): Unit = set(140, value)
        get(): String? = get(140) as String?

    open var otherCostTaxCode_3: Byte?
        set(value): Unit = set(141, value)
        get(): Byte? = get(141) as Byte?

    open var otherCost_4: Int?
        set(value): Unit = set(142, value)
        get(): Int? = get(142) as Int?

    open var otherCostItem_4: String?
        set(value): Unit = set(143, value)
        get(): String? = get(143) as String?

    open var otherCostTaxCode_4: Byte?
        set(value): Unit = set(144, value)
        get(): Byte? = get(144) as Byte?

    open var otherCost_5: Int?
        set(value): Unit = set(145, value)
        get(): Int? = get(145) as Int?

    open var otherCostItem_5: String?
        set(value): Unit = set(146, value)
        get(): String? = get(146) as String?

    open var otherCostTaxCode_5: Byte?
        set(value): Unit = set(147, value)
        get(): Byte? = get(147) as Byte?

    open var outerFacilityCode_1: Byte?
        set(value): Unit = set(148, value)
        get(): Byte? = get(148) as Byte?

    open var outerFacilityCode_2: Byte?
        set(value): Unit = set(149, value)
        get(): Byte? = get(149) as Byte?

    open var outerArea_2: BigDecimal?
        set(value): Unit = set(150, value)
        get(): BigDecimal? = get(150) as BigDecimal?

    open var renewalFee: BigDecimal?
        set(value): Unit = set(151, value)
        get(): BigDecimal? = get(151) as BigDecimal?

    open var renewalFeeUnitCode: Byte?
        set(value): Unit = set(152, value)
        get(): Byte? = get(152) as Byte?

    open var renewalFeeClassCode: Byte?
        set(value): Unit = set(153, value)
        get(): Byte? = get(153) as Byte?

    open var houseRentLimitDate: Int?
        set(value): Unit = set(154, value)
        get(): Int? = get(154) as Int?

    open var insuranceCode: Byte?
        set(value): Unit = set(155, value)
        get(): Byte? = get(155) as Byte?

    open var specialRentalLowerCost: Int?
        set(value): Unit = set(156, value)
        get(): Int? = get(156) as Int?

    open var specialRentalUpperCost: Int?
        set(value): Unit = set(157, value)
        get(): Int? = get(157) as Int?

    open var additionalDepositUnitCode: Byte?
        set(value): Unit = set(158, value)
        get(): Byte? = get(158) as Byte?

    open var additionalDepositReasonCode: Byte?
        set(value): Unit = set(159, value)
        get(): Byte? = get(159) as Byte?

    open var brokerage: BigDecimal?
        set(value): Unit = set(160, value)
        get(): BigDecimal? = get(160) as BigDecimal?

    open var brokerageUnitCode: Byte?
        set(value): Unit = set(161, value)
        get(): Byte? = get(161) as Byte?

    open var renewalCharge: BigDecimal?
        set(value): Unit = set(162, value)
        get(): BigDecimal? = get(162) as BigDecimal?

    open var renewalChargeUnitCode: Byte?
        set(value): Unit = set(163, value)
        get(): Byte? = get(163) as Byte?

    open var studentOnlyCode: Byte?
        set(value): Unit = set(164, value)
        get(): Byte? = get(164) as Byte?

    open var sexConditionCode: Byte?
        set(value): Unit = set(165, value)
        get(): Byte? = get(165) as Byte?

    open var kidsCode: Byte?
        set(value): Unit = set(166, value)
        get(): Byte? = get(166) as Byte?

    open var aloneCode: Byte?
        set(value): Unit = set(167, value)
        get(): Byte? = get(167) as Byte?

    open var twoPeopleCode: Byte?
        set(value): Unit = set(168, value)
        get(): Byte? = get(168) as Byte?

    open var elderCode: Byte?
        set(value): Unit = set(169, value)
        get(): Byte? = get(169) as Byte?

    open var corporationOnlyCode: Byte?
        set(value): Unit = set(170, value)
        get(): Byte? = get(170) as Byte?

    open var residenceHouseRentCode: Byte?
        set(value): Unit = set(171, value)
        get(): Byte? = get(171) as Byte?

    open var roomStyleCode_1: Byte?
        set(value): Unit = set(172, value)
        get(): Byte? = get(172) as Byte?

    open var roomArea_1: BigDecimal?
        set(value): Unit = set(173, value)
        get(): BigDecimal? = get(173) as BigDecimal?

    open var roomUnitCode_1: Byte?
        set(value): Unit = set(174, value)
        get(): Byte? = get(174) as Byte?

    open var roomStyleCode_2: Byte?
        set(value): Unit = set(175, value)
        get(): Byte? = get(175) as Byte?

    open var roomArea_2: BigDecimal?
        set(value): Unit = set(176, value)
        get(): BigDecimal? = get(176) as BigDecimal?

    open var roomUnitCode_2: Byte?
        set(value): Unit = set(177, value)
        get(): Byte? = get(177) as Byte?

    open var roomStyleCode_3: Byte?
        set(value): Unit = set(178, value)
        get(): Byte? = get(178) as Byte?

    open var roomArea_3: BigDecimal?
        set(value): Unit = set(179, value)
        get(): BigDecimal? = get(179) as BigDecimal?

    open var roomUnitCode_3: Byte?
        set(value): Unit = set(180, value)
        get(): Byte? = get(180) as Byte?

    open var roomStyleCode_4: Byte?
        set(value): Unit = set(181, value)
        get(): Byte? = get(181) as Byte?

    open var roomArea_4: BigDecimal?
        set(value): Unit = set(182, value)
        get(): BigDecimal? = get(182) as BigDecimal?

    open var roomUnitCode_4: Byte?
        set(value): Unit = set(183, value)
        get(): Byte? = get(183) as Byte?

    open var roomStyleCode_5: Byte?
        set(value): Unit = set(184, value)
        get(): Byte? = get(184) as Byte?

    open var roomArea_5: BigDecimal?
        set(value): Unit = set(185, value)
        get(): BigDecimal? = get(185) as BigDecimal?

    open var roomUnitCode_5: Byte?
        set(value): Unit = set(186, value)
        get(): Byte? = get(186) as Byte?

    open var roomStyleCode_6: Byte?
        set(value): Unit = set(187, value)
        get(): Byte? = get(187) as Byte?

    open var roomArea_6: BigDecimal?
        set(value): Unit = set(188, value)
        get(): BigDecimal? = get(188) as BigDecimal?

    open var roomUnitCode_6: Byte?
        set(value): Unit = set(189, value)
        get(): Byte? = get(189) as Byte?

    open var roomStyleCode_7: Byte?
        set(value): Unit = set(190, value)
        get(): Byte? = get(190) as Byte?

    open var roomArea_7: BigDecimal?
        set(value): Unit = set(191, value)
        get(): BigDecimal? = get(191) as BigDecimal?

    open var roomUnitCode_7: Byte?
        set(value): Unit = set(192, value)
        get(): Byte? = get(192) as Byte?

    open var roomStyleCode_8: Byte?
        set(value): Unit = set(193, value)
        get(): Byte? = get(193) as Byte?

    open var roomArea_8: BigDecimal?
        set(value): Unit = set(194, value)
        get(): BigDecimal? = get(194) as BigDecimal?

    open var roomUnitCode_8: Byte?
        set(value): Unit = set(195, value)
        get(): Byte? = get(195) as Byte?

    open var roomStyleCode_9: Byte?
        set(value): Unit = set(196, value)
        get(): Byte? = get(196) as Byte?

    open var roomArea_9: BigDecimal?
        set(value): Unit = set(197, value)
        get(): BigDecimal? = get(197) as BigDecimal?

    open var roomUnitCode_9: Byte?
        set(value): Unit = set(198, value)
        get(): Byte? = get(198) as Byte?

    open var roomStyleCode_10: Byte?
        set(value): Unit = set(199, value)
        get(): Byte? = get(199) as Byte?

    open var roomArea_10: BigDecimal?
        set(value): Unit = set(200, value)
        get(): BigDecimal? = get(200) as BigDecimal?

    open var roomUnitCode_10: Byte?
        set(value): Unit = set(201, value)
        get(): Byte? = get(201) as Byte?

    open var parkingCode: Byte?
        set(value): Unit = set(202, value)
        get(): Byte? = get(202) as Byte?

    open var fromNearParking: BigDecimal?
        set(value): Unit = set(203, value)
        get(): BigDecimal? = get(203) as BigDecimal?

    open var parkingNum: Short?
        set(value): Unit = set(204, value)
        get(): Short? = get(204) as Short?

    open var parkingTypeCode: Byte?
        set(value): Unit = set(205, value)
        get(): Byte? = get(205) as Byte?

    open var parkingShutterCode: Byte?
        set(value): Unit = set(206, value)
        get(): Byte? = get(206) as Byte?

    open var parkingLowerCost: Int?
        set(value): Unit = set(207, value)
        get(): Int? = get(207) as Int?

    open var parkingTaxCode: Byte?
        set(value): Unit = set(208, value)
        get(): Byte? = get(208) as Byte?

    open var parkableNumCode: Byte?
        set(value): Unit = set(209, value)
        get(): Byte? = get(209) as Byte?

    open var parkingFreeCode: Byte?
        set(value): Unit = set(210, value)
        get(): Byte? = get(210) as Byte?

    open var bikeParkCode: Byte?
        set(value): Unit = set(211, value)
        get(): Byte? = get(211) as Byte?

    open var bikeParkCost: Int?
        set(value): Unit = set(212, value)
        get(): Int? = get(212) as Int?

    open var motorbikeParkCode: Byte?
        set(value): Unit = set(213, value)
        get(): Byte? = get(213) as Byte?

    open var motorbikeCost: Int?
        set(value): Unit = set(214, value)
        get(): Int? = get(214) as Int?

    open var airconCode: Byte?
        set(value): Unit = set(215, value)
        get(): Byte? = get(215) as Byte?

    open var coolerCode: Byte?
        set(value): Unit = set(216, value)
        get(): Byte? = get(216) as Byte?

    open var heatingCode: Byte?
        set(value): Unit = set(217, value)
        get(): Byte? = get(217) as Byte?

    open var loadHeaterCode: Byte?
        set(value): Unit = set(218, value)
        get(): Byte? = get(218) as Byte?

    open var stoveCode: Byte?
        set(value): Unit = set(219, value)
        get(): Byte? = get(219) as Byte?

    open var floorHeatingCode: Byte?
        set(value): Unit = set(220, value)
        get(): Byte? = get(220) as Byte?

    open var catvCode: Byte?
        set(value): Unit = set(221, value)
        get(): Byte? = get(221) as Byte?

    open var communityBroadcastCode: Byte?
        set(value): Unit = set(222, value)
        get(): Byte? = get(222) as Byte?

    open var bsCode: Byte?
        set(value): Unit = set(223, value)
        get(): Byte? = get(223) as Byte?

    open var csCode: Byte?
        set(value): Unit = set(224, value)
        get(): Byte? = get(224) as Byte?

    open var internetCode: Byte?
        set(value): Unit = set(225, value)
        get(): Byte? = get(225) as Byte?

    open var closetCode: Byte?
        set(value): Unit = set(226, value)
        get(): Byte? = get(226) as Byte?

    open var walkinWardrobeCode: Byte?
        set(value): Unit = set(227, value)
        get(): Byte? = get(227) as Byte?

    open var closetUnderFloorCode: Byte?
        set(value): Unit = set(228, value)
        get(): Byte? = get(228) as Byte?

    open var trunkRoomCode: Byte?
        set(value): Unit = set(229, value)
        get(): Byte? = get(229) as Byte?

    open var oshiireCode: Byte?
        set(value): Unit = set(230, value)
        get(): Byte? = get(230) as Byte?

    open var garretClosetCode: Byte?
        set(value): Unit = set(231, value)
        get(): Byte? = get(231) as Byte?

    open var shoeCupboardCode: Byte?
        set(value): Unit = set(232, value)
        get(): Byte? = get(232) as Byte?

    open var storeroomCode: Byte?
        set(value): Unit = set(233, value)
        get(): Byte? = get(233) as Byte?

    open var bathToiletCode: Byte?
        set(value): Unit = set(234, value)
        get(): Byte? = get(234) as Byte?

    open var bathCode: Byte?
        set(value): Unit = set(235, value)
        get(): Byte? = get(235) as Byte?

    open var showerCode: Byte?
        set(value): Unit = set(236, value)
        get(): Byte? = get(236) as Byte?

    open var autoBathCode: Byte?
        set(value): Unit = set(237, value)
        get(): Byte? = get(237) as Byte?

    open var dressingRoomCode: Byte?
        set(value): Unit = set(238, value)
        get(): Byte? = get(238) as Byte?

    open var reboilBathCode: Byte?
        set(value): Unit = set(239, value)
        get(): Byte? = get(239) as Byte?

    open var toiletCode: Byte?
        set(value): Unit = set(240, value)
        get(): Byte? = get(240) as Byte?

    open var bathDrierCode: Byte?
        set(value): Unit = set(241, value)
        get(): Byte? = get(241) as Byte?

    open var shampooDresserCode: Byte?
        set(value): Unit = set(242, value)
        get(): Byte? = get(242) as Byte?

    open var washletCode: Byte?
        set(value): Unit = set(243, value)
        get(): Byte? = get(243) as Byte?

    open var bathOver_1tsuboCode: Byte?
        set(value): Unit = set(244, value)
        get(): Byte? = get(244) as Byte?

    open var warmletCode: Byte?
        set(value): Unit = set(245, value)
        get(): Byte? = get(245) as Byte?

    open var cookingStoveCode: Byte?
        set(value): Unit = set(246, value)
        get(): Byte? = get(246) as Byte?

    open var kitchenCode: Byte?
        set(value): Unit = set(247, value)
        get(): Byte? = get(247) as Byte?

    open var microwaveOvenCode: Byte?
        set(value): Unit = set(248, value)
        get(): Byte? = get(248) as Byte?

    open var ihCookingHeaterCode: Byte?
        set(value): Unit = set(249, value)
        get(): Byte? = get(249) as Byte?

    open var coldStorageCode: Byte?
        set(value): Unit = set(250, value)
        get(): Byte? = get(250) as Byte?

    open var grillCode: Byte?
        set(value): Unit = set(251, value)
        get(): Byte? = get(251) as Byte?

    open var disposerCode: Byte?
        set(value): Unit = set(252, value)
        get(): Byte? = get(252) as Byte?

    open var dishWasherCode: Byte?
        set(value): Unit = set(253, value)
        get(): Byte? = get(253) as Byte?

    open var waterCleanerCode: Byte?
        set(value): Unit = set(254, value)
        get(): Byte? = get(254) as Byte?

    open var woodenFloorCode: Byte?
        set(value): Unit = set(255, value)
        get(): Byte? = get(255) as Byte?

    open var loftCode: Byte?
        set(value): Unit = set(256, value)
        get(): Byte? = get(256) as Byte?

    open var cushionFloorCode: Byte?
        set(value): Unit = set(257, value)
        get(): Byte? = get(257) as Byte?

    open var highestFloorCode: Byte?
        set(value): Unit = set(258, value)
        get(): Byte? = get(258) as Byte?

    open var maisonetteCode: Byte?
        set(value): Unit = set(259, value)
        get(): Byte? = get(259) as Byte?

    open var overSecondFloorCode: Byte?
        set(value): Unit = set(260, value)
        get(): Byte? = get(260) as Byte?

    open var caveCode: Byte?
        set(value): Unit = set(261, value)
        get(): Byte? = get(261) as Byte?

    open var soundproofCode: Byte?
        set(value): Unit = set(262, value)
        get(): Byte? = get(262) as Byte?

    open var cornerHouseCode: Byte?
        set(value): Unit = set(263, value)
        get(): Byte? = get(263) as Byte?

    open var sunroomCode: Byte?
        set(value): Unit = set(264, value)
        get(): Byte? = get(264) as Byte?

    open var basementCode: Byte?
        set(value): Unit = set(265, value)
        get(): Byte? = get(265) as Byte?

    open var southRoomCode: Byte?
        set(value): Unit = set(266, value)
        get(): Byte? = get(266) as Byte?

    open var patioCode: Byte?
        set(value): Unit = set(267, value)
        get(): Byte? = get(267) as Byte?

    open var crimePrevShutterCode: Byte?
        set(value): Unit = set(268, value)
        get(): Byte? = get(268) as Byte?

    open var crimePrevCameraCode: Byte?
        set(value): Unit = set(269, value)
        get(): Byte? = get(269) as Byte?

    open var autolockCode: Byte?
        set(value): Unit = set(270, value)
        get(): Byte? = get(270) as Byte?

    open var doubleLockCode: Byte?
        set(value): Unit = set(271, value)
        get(): Byte? = get(271) as Byte?

    open var washingMachineCode: Byte?
        set(value): Unit = set(272, value)
        get(): Byte? = get(272) as Byte?

    open var drierCode: Byte?
        set(value): Unit = set(273, value)
        get(): Byte? = get(273) as Byte?

    open var washingMachinePlaceCode: Byte?
        set(value): Unit = set(274, value)
        get(): Byte? = get(274) as Byte?

    open var cardKeyCode: Byte?
        set(value): Unit = set(275, value)
        get(): Byte? = get(275) as Byte?

    open var bowWindowCode: Byte?
        set(value): Unit = set(276, value)
        get(): Byte? = get(276) as Byte?

    open var lightCode: Byte?
        set(value): Unit = set(277, value)
        get(): Byte? = get(277) as Byte?

    open var allElectricCode: Byte?
        set(value): Unit = set(278, value)
        get(): Byte? = get(278) as Byte?

    open var hotWaterSupplyCode: Byte?
        set(value): Unit = set(279, value)
        get(): Byte? = get(279) as Byte?

    open var interphoneCode: Byte?
        set(value): Unit = set(280, value)
        get(): Byte? = get(280) as Byte?

    open var fulltimeFunCode: Byte?
        set(value): Unit = set(281, value)
        get(): Byte? = get(281) as Byte?

    open var ecocuteCode: Byte?
        set(value): Unit = set(282, value)
        get(): Byte? = get(282) as Byte?

    open var doubleSideBalconyCode: Byte?
        set(value): Unit = set(283, value)
        get(): Byte? = get(283) as Byte?

    open var balconySideNumCode: Byte?
        set(value): Unit = set(284, value)
        get(): Byte? = get(284) as Byte?

    open var bathTvCode: Byte?
        set(value): Unit = set(285, value)
        get(): Byte? = get(285) as Byte?

    open var porchCode: Byte?
        set(value): Unit = set(286, value)
        get(): Byte? = get(286) as Byte?

    open var upStartDate: Int?
        set(value): Unit = set(287, value)
        get(): Int? = get(287) as Int?

    open var upEndDate: Int?
        set(value): Unit = set(288, value)
        get(): Int? = get(288) as Int?

    open var dressingTableCode: Byte?
        set(value): Unit = set(289, value)
        get(): Byte? = get(289) as Byte?

    open var privateDustBoxCode: Byte?
        set(value): Unit = set(290, value)
        get(): Byte? = get(290) as Byte?

    open var pianoCode: Byte?
        set(value): Unit = set(291, value)
        get(): Byte? = get(291) as Byte?

    open var largeShoesBoxCode: Byte?
        set(value): Unit = set(292, value)
        get(): Byte? = get(292) as Byte?

    open var closetUnderTatamiCode: Byte?
        set(value): Unit = set(293, value)
        get(): Byte? = get(293) as Byte?

    open var indoorsBicycleParkingCode: Byte?
        set(value): Unit = set(294, value)
        get(): Byte? = get(294) as Byte?

    open var securityKeyCode: Byte?
        set(value): Unit = set(295, value)
        get(): Byte? = get(295) as Byte?

    open var shutterCode: Byte?
        set(value): Unit = set(296, value)
        get(): Byte? = get(296) as Byte?

    open var forSouthCode: Byte?
        set(value): Unit = set(297, value)
        get(): Byte? = get(297) as Byte?

    open var closetUnderstairCode: Byte?
        set(value): Unit = set(298, value)
        get(): Byte? = get(298) as Byte?

    open var nearbyConvenienceStoreCode: Byte?
        set(value): Unit = set(299, value)
        get(): Byte? = get(299) as Byte?

    open var nearbyBankCode: Byte?
        set(value): Unit = set(300, value)
        get(): Byte? = get(300) as Byte?

    open var nearbyRentalVideoCode: Byte?
        set(value): Unit = set(301, value)
        get(): Byte? = get(301) as Byte?

    open var largeScaleRenewalCode: Byte?
        set(value): Unit = set(302, value)
        get(): Byte? = get(302) as Byte?

    open var recoveryCostCode: Byte?
        set(value): Unit = set(303, value)
        get(): Byte? = get(303) as Byte?

    open var guarantorCode: Byte?
        set(value): Unit = set(304, value)
        get(): Byte? = get(304) as Byte?

    open var guarantorProxyCode: Byte?
        set(value): Unit = set(305, value)
        get(): Byte? = get(305) as Byte?

    open var guarantorProxyComCode: Byte?
        set(value): Unit = set(306, value)
        get(): Byte? = get(306) as Byte?

    open var guarantorProxyComment: String?
        set(value): Unit = set(307, value)
        get(): String? = get(307) as String?

    open var dispMapCode: Byte?
        set(value): Unit = set(308, value)
        get(): Byte? = get(308) as Byte?

    open var latitudeWorld: BigInteger?
        set(value): Unit = set(309, value)
        get(): BigInteger? = get(309) as BigInteger?

    open var longitudeWorld: BigInteger?
        set(value): Unit = set(310, value)
        get(): BigInteger? = get(310) as BigInteger?

    open var gardenCode: Byte?
        set(value): Unit = set(311, value)
        get(): Byte? = get(311) as Byte?

    open var balconyCode: Byte?
        set(value): Unit = set(312, value)
        get(): Byte? = get(312) as Byte?

    open var panoramaId: Int?
        set(value): Unit = set(313, value)
        get(): Int? = get(313) as Int?

    open var largeScaleRenewalDate: Int?
        set(value): Unit = set(314, value)
        get(): Int? = get(314) as Int?

    open var shatakuKanouCode: Byte?
        set(value): Unit = set(315, value)
        get(): Byte? = get(315) as Byte?

    open var noDepositPlanCode: Byte?
        set(value): Unit = set(316, value)
        get(): Byte? = get(316) as Byte?

    open var kentakuKindCode: String?
        set(value): Unit = set(317, value)
        get(): String? = get(317) as String?

    open var priceSaleFlag: Byte?
        set(value): Unit = set(318, value)
        get(): Byte? = get(318) as Byte?

    open var refomeFlag: Byte?
        set(value): Unit = set(319, value)
        get(): Byte? = get(319) as Byte?

    open var productCode: Short?
        set(value): Unit = set(320, value)
        get(): Short? = get(320) as Short?

    open var ownerShipBranchCode: String?
        set(value): Unit = set(321, value)
        get(): String? = get(321) as String?

    open var kentakuBuildingCode: String?
        set(value): Unit = set(322, value)
        get(): String? = get(322) as String?

    open var kentakuRoomCode: String?
        set(value): Unit = set(323, value)
        get(): String? = get(323) as String?

    open var keyExchangeFreeCode: Byte?
        set(value): Unit = set(324, value)
        get(): Byte? = get(324) as Byte?

    open var adPrice: Int?
        set(value): Unit = set(325, value)
        get(): Int? = get(325) as Int?

    open var ffPrice: String?
        set(value): Unit = set(326, value)
        get(): String? = get(326) as String?

    open var leaveDateTp: Int?
        set(value): Unit = set(327, value)
        get(): Int? = get(327) as Int?

    open var leaveFinishDate: Int?
        set(value): Unit = set(328, value)
        get(): Int? = get(328) as Int?

    open var lowParkingPrice: Int?
        set(value): Unit = set(329, value)
        get(): Int? = get(329) as Int?

    open var highParkingPrice: Int?
        set(value): Unit = set(330, value)
        get(): Int? = get(330) as Int?

    open var structureDispNameTp: String?
        set(value): Unit = set(331, value)
        get(): String? = get(331) as String?

    open var displaceCode: Byte?
        set(value): Unit = set(332, value)
        get(): Byte? = get(332) as Byte?

    open var financeCorporationCode: Byte?
        set(value): Unit = set(333, value)
        get(): Byte? = get(333) as Byte?

    open var waterCompanyName: String?
        set(value): Unit = set(334, value)
        get(): String? = get(334) as String?

    open var waterCompanyTel: String?
        set(value): Unit = set(335, value)
        get(): String? = get(335) as String?

    open var electricCompanyName: String?
        set(value): Unit = set(336, value)
        get(): String? = get(336) as String?

    open var electricCompanyTel: String?
        set(value): Unit = set(337, value)
        get(): String? = get(337) as String?

    open var gasCompanyName: String?
        set(value): Unit = set(338, value)
        get(): String? = get(338) as String?

    open var gasCompanyTel: String?
        set(value): Unit = set(339, value)
        get(): String? = get(339) as String?

    open var collectDate: String?
        set(value): Unit = set(340, value)
        get(): String? = get(340) as String?

    open var kouentinCode: Byte?
        set(value): Unit = set(341, value)
        get(): Byte? = get(341) as Byte?

    open var intoDateTxt: String?
        set(value): Unit = set(342, value)
        get(): String? = get(342) as String?

    open var roomSituationCode: String?
        set(value): Unit = set(343, value)
        get(): String? = get(343) as String?

    open var recordSituationCode: String?
        set(value): Unit = set(344, value)
        get(): String? = get(344) as String?

    open var electricDiscountFlag: Byte?
        set(value): Unit = set(345, value)
        get(): Byte? = get(345) as Byte?

    open var fletsHikariCode: Byte?
        set(value): Unit = set(346, value)
        get(): Byte? = get(346) as Byte?

    open var akiyaTerm: Short?
        set(value): Unit = set(347, value)
        get(): Short? = get(347) as Short?

    open var cleaningFeeCode: Byte?
        set(value): Unit = set(348, value)
        get(): Byte? = get(348) as Byte?

    open var cleaningFee: Int?
        set(value): Unit = set(349, value)
        get(): Int? = get(349) as Int?

    open var powerCode: Byte?
        set(value): Unit = set(350, value)
        get(): Byte? = get(350) as Byte?

    open var fireZoneCode: Byte?
        set(value): Unit = set(351, value)
        get(): Byte? = get(351) as Byte?

    open var discountRate: Short?
        set(value): Unit = set(352, value)
        get(): Short? = get(352) as Short?

    open var discountTerm: Int?
        set(value): Unit = set(353, value)
        get(): Int? = get(353) as Int?

    open var petFlag: Byte?
        set(value): Unit = set(354, value)
        get(): Byte? = get(354) as Byte?

    open var internetFreeCode: Byte?
        set(value): Unit = set(355, value)
        get(): Byte? = get(355) as Byte?

    open var allRoomCloset: Byte?
        set(value): Unit = set(356, value)
        get(): Byte? = get(356) as Byte?

    open var walkThroughCloset: Byte?
        set(value): Unit = set(357, value)
        get(): Byte? = get(357) as Byte?

    open var freeWashRoom: Byte?
        set(value): Unit = set(358, value)
        get(): Byte? = get(358) as Byte?

    open var autoBath: Byte?
        set(value): Unit = set(359, value)
        get(): Byte? = get(359) as Byte?

    open var indoorClothesDrying: Byte?
        set(value): Unit = set(360, value)
        get(): Byte? = get(360) as Byte?

    open var motionSensorLighting: Byte?
        set(value): Unit = set(361, value)
        get(): Byte? = get(361) as Byte?

    open var openKitchen: Byte?
        set(value): Unit = set(362, value)
        get(): Byte? = get(362) as Byte?

    open var islandKitchen: Byte?
        set(value): Unit = set(363, value)
        get(): Byte? = get(363) as Byte?

    open var gasCookerAttached: Byte?
        set(value): Unit = set(364, value)
        get(): Byte? = get(364) as Byte?

    open var threeOverGas: Byte?
        set(value): Unit = set(365, value)
        get(): Byte? = get(365) as Byte?

    open var doubleGlazing: Byte?
        set(value): Unit = set(366, value)
        get(): Byte? = get(366) as Byte?

    open var securityGlazing: Byte?
        set(value): Unit = set(367, value)
        get(): Byte? = get(367) as Byte?

    open var vibrationControlFloor: Byte?
        set(value): Unit = set(368, value)
        get(): Byte? = get(368) as Byte?

    open var snowVanishingFacility: Byte?
        set(value): Unit = set(369, value)
        get(): Byte? = get(369) as Byte?

    open var keroseneHeater: Byte?
        set(value): Unit = set(370, value)
        get(): Byte? = get(370) as Byte?

    open var bathWindow: Byte?
        set(value): Unit = set(371, value)
        get(): Byte? = get(371) as Byte?

    open var japaneseStyleRoom: Byte?
        set(value): Unit = set(372, value)
        get(): Byte? = get(372) as Byte?

    open var earthquakeResistConst: Byte?
        set(value): Unit = set(373, value)
        get(): Byte? = get(373) as Byte?

    open var allinoneServiceWater: Byte?
        set(value): Unit = set(374, value)
        get(): Byte? = get(374) as Byte?

    open var allinoneServiceElectricity: Byte?
        set(value): Unit = set(375, value)
        get(): Byte? = get(375) as Byte?

    open var allinoneServiceGas: Byte?
        set(value): Unit = set(376, value)
        get(): Byte? = get(376) as Byte?

    open var priceAndCost: Long?
        set(value): Unit = set(377, value)
        get(): Long? = get(377) as Long?

    open var valCode_1: String?
        set(value): Unit = set(378, value)
        get(): String? = get(378) as String?

    open var valCode_2: String?
        set(value): Unit = set(379, value)
        get(): String? = get(379) as String?

    open var valCode_3: String?
        set(value): Unit = set(380, value)
        get(): String? = get(380) as String?

    open var panoramaType: Byte?
        set(value): Unit = set(381, value)
        get(): Byte? = get(381) as Byte?

    open var serviceFeeDetails: String?
        set(value): Unit = set(382, value)
        get(): String? = get(382) as String?

    open var shinsaBranchCode: String?
        set(value): Unit = set(383, value)
        get(): String? = get(383) as String?

    open var contractConfirmCode: Byte?
        set(value): Unit = set(384, value)
        get(): Byte? = get(384) as Byte?

    open var prefectureEn: String?
        set(value): Unit = set(385, value)
        get(): String? = get(385) as String?

    open var shikugunchousonEn: String?
        set(value): Unit = set(386, value)
        get(): String? = get(386) as String?

    open var ooazaTsuusyouEn: String?
        set(value): Unit = set(387, value)
        get(): String? = get(387) as String?

    open var azaChoumeEn: String?
        set(value): Unit = set(388, value)
        get(): String? = get(388) as String?

    open var restaddrAlphabet: String?
        set(value): Unit = set(389, value)
        get(): String? = get(389) as String?

    open var upState: Byte?
        set(value): Unit = set(390, value)
        get(): Byte? = get(390) as Byte?

    open var adPriceUnitCode: Byte?
        set(value): Unit = set(391, value)
        get(): Byte? = get(391) as Byte?

    open var deleteDate: Int?
        set(value): Unit = set(392, value)
        get(): Int? = get(392) as Int?

    open var realtimeUpTime: String?
        set(value): Unit = set(393, value)
        get(): String? = get(393) as String?

    open var realtimeUpType: Byte?
        set(value): Unit = set(394, value)
        get(): Byte? = get(394) as Byte?

    open var productTypeCd: Byte?
        set(value): Unit = set(395, value)
        get(): Byte? = get(395) as Byte?

    open var moneyUpdateTime: String?
        set(value): Unit = set(396, value)
        get(): String? = get(396) as String?

    open var kodawari100_199: String?
        set(value): Unit = set(397, value)
        get(): String? = get(397) as String?

    open var floorMaxRoom: Short?
        set(value): Unit = set(398, value)
        get(): Short? = get(398) as Short?

    open var renewalFeeFlg: Byte?
        set(value): Unit = set(399, value)
        get(): Byte? = get(399) as Byte?

    open var fulltimeSupportFlg: Byte?
        set(value): Unit = set(400, value)
        get(): Byte? = get(400) as Byte?

    open var membershipFeeExemptionKbn: Byte?
        set(value): Unit = set(401, value)
        get(): Byte? = get(401) as Byte?

    open var membershipFeeExemptionDays: Byte?
        set(value): Unit = set(402, value)
        get(): Byte? = get(402) as Byte?

    open var eboardComment: String?
        set(value): Unit = set(403, value)
        get(): String? = get(403) as String?

    open var managementParkingKbn: Byte?
        set(value): Unit = set(404, value)
        get(): Byte? = get(404) as Byte?

    open var netServiceJcom: Byte?
        set(value): Unit = set(405, value)
        get(): Byte? = get(405) as Byte?

    open var netServiceStarcat: Byte?
        set(value): Unit = set(406, value)
        get(): Byte? = get(406) as Byte?

    open var zehOriented: Byte?
        set(value): Unit = set(407, value)
        get(): Byte? = get(407) as Byte?

    open var zehDkSoleil: Byte?
        set(value): Unit = set(408, value)
        get(): Byte? = get(408) as Byte?

    open var zehDkAlpha: Byte?
        set(value): Unit = set(409, value)
        get(): Byte? = get(409) as Byte?

    open var keySetCostFlag: Byte?
        set(value): Unit = set(410, value)
        get(): Byte? = get(410) as Byte?

    open var electricIntroduction: Byte?
        set(value): Unit = set(411, value)
        get(): Byte? = get(411) as Byte?

    open var electricType: Byte?
        set(value): Unit = set(412, value)
        get(): Byte? = get(412) as Byte?

    open var emergencyECompanyName: String?
        set(value): Unit = set(413, value)
        get(): String? = get(413) as String?

    open var emergencyECompanyTel: String?
        set(value): Unit = set(414, value)
        get(): String? = get(414) as String?

    open var gasIntroduction: Byte?
        set(value): Unit = set(415, value)
        get(): Byte? = get(415) as Byte?

    open var emergencyGasCompanyName: String?
        set(value): Unit = set(416, value)
        get(): String? = get(416) as String?

    open var emergencyGasCompanyTel: String?
        set(value): Unit = set(417, value)
        get(): String? = get(417) as String?

    open var waterIntroduction: Byte?
        set(value): Unit = set(418, value)
        get(): Byte? = get(418) as Byte?

    open var waterMeterType: Byte?
        set(value): Unit = set(419, value)
        get(): Byte? = get(419) as Byte?

    open var internetType: Byte?
        set(value): Unit = set(420, value)
        get(): Byte? = get(420) as Byte?

    open var internetName: String?
        set(value): Unit = set(421, value)
        get(): String? = get(421) as String?

    open var internetTel: String?
        set(value): Unit = set(422, value)
        get(): String? = get(422) as String?

    open var internetIntroduction: Byte?
        set(value): Unit = set(423, value)
        get(): Byte? = get(423) as Byte?

    open var waterServer: Byte?
        set(value): Unit = set(424, value)
        get(): Byte? = get(424) as Byte?

    open var lifelineGuidanceType: Byte?
        set(value): Unit = set(425, value)
        get(): Byte? = get(425) as Byte?

    open var roomSaveEnergyCertDate: Int?
        set(value): Unit = set(426, value)
        get(): Int? = get(426) as Int?

    open var roomThirdPartyEvalFlg: Byte?
        set(value): Unit = set(427, value)
        get(): Byte? = get(427) as Byte?

    open var roomSaveEnergyLevel: String?
        set(value): Unit = set(428, value)
        get(): String? = get(428) as String?

    open var roomEnergyCost: String?
        set(value): Unit = set(429, value)
        get(): String? = get(429) as String?

    open var roomEnergyCostSun: String?
        set(value): Unit = set(430, value)
        get(): String? = get(430) as String?

    open var roomRenewEnergyFlg: Byte?
        set(value): Unit = set(431, value)
        get(): Byte? = get(431) as Byte?

    open var roomInsulationLevel: String?
        set(value): Unit = set(432, value)
        get(): String? = get(432) as String?

    open var roomEasyUtilityCosts: Int?
        set(value): Unit = set(433, value)
        get(): Int? = get(433) as Int?

    open var roomZehLevelFlg: Byte?
        set(value): Unit = set(434, value)
        get(): Byte? = get(434) as Byte?

    open var roomNetZeroEnergyFlg: Byte?
        set(value): Unit = set(435, value)
        get(): Byte? = get(435) as Byte?

    open var buildingSaveEnergyCertDate: Int?
        set(value): Unit = set(436, value)
        get(): Int? = get(436) as Int?

    open var buildingThirdPartyEvalFlg: Byte?
        set(value): Unit = set(437, value)
        get(): Byte? = get(437) as Byte?

    open var buildingSaveEnergyLevel: String?
        set(value): Unit = set(438, value)
        get(): String? = get(438) as String?

    open var buildingEnergyCost: String?
        set(value): Unit = set(439, value)
        get(): String? = get(439) as String?

    open var buildingEnergyCostSun: String?
        set(value): Unit = set(440, value)
        get(): String? = get(440) as String?

    open var buildingRenewEnergyFlg: Byte?
        set(value): Unit = set(441, value)
        get(): Byte? = get(441) as Byte?

    open var buildingInsulationLevel: String?
        set(value): Unit = set(442, value)
        get(): String? = get(442) as String?

    open var buildingEasyUtilityCosts: Int?
        set(value): Unit = set(443, value)
        get(): Int? = get(443) as Int?

    open var buildingZehLevelFlg: Byte?
        set(value): Unit = set(444, value)
        get(): Byte? = get(444) as Byte?

    open var buildingNetZeroEnergyFlg: Byte?
        set(value): Unit = set(445, value)
        get(): Byte? = get(445) as Byte?

    open var shinsaBusinessOfficeCode: String?
        set(value): Unit = set(446, value)
        get(): String? = get(446) as String?

    open var challengeStart: Int?
        set(value): Unit = set(447, value)
        get(): Int? = get(447) as Int?

    open var challengeEnd: Int?
        set(value): Unit = set(448, value)
        get(): Int? = get(448) as Int?

    open var challengeDiscountPrice: Int?
        set(value): Unit = set(449, value)
        get(): Int? = get(449) as Int?

    /**
     * Create a detached, initialised SitePropertyKentakuForPRecord
     */
    constructor(value: SitePropertyKentakuForPPojo?): this() {
        if (value != null) {
            this.propertyFullId = value.propertyFullId
            this.buildingId = value.buildingId
            this.renewDate = value.renewDate
            this.distanceFromStation_1 = value.distanceFromStation_1
            this.walkFromStation_1 = value.walkFromStation_1
            this.busFromStation_1 = value.busFromStation_1
            this.busStopName_1 = value.busStopName_1
            this.fromBusStop_1 = value.fromBusStop_1
            this.distanceFromBusstop_1 = value.distanceFromBusstop_1
            this.nearestRoute_1 = value.nearestRoute_1
            this.nearestStation_1 = value.nearestStation_1
            this.kindaikaCodeText_1 = value.kindaikaCodeText_1
            this.wayToCode_1 = value.wayToCode_1
            this.distanceFromStation_2 = value.distanceFromStation_2
            this.walkFromStation_2 = value.walkFromStation_2
            this.busFromStation_2 = value.busFromStation_2
            this.busStopName_2 = value.busStopName_2
            this.fromBusStop_2 = value.fromBusStop_2
            this.distanceFromBusstop_2 = value.distanceFromBusstop_2
            this.nearestRoute_2 = value.nearestRoute_2
            this.nearestStation_2 = value.nearestStation_2
            this.kindaikaCodeText_2 = value.kindaikaCodeText_2
            this.wayToCode_2 = value.wayToCode_2
            this.distanceFromStation_3 = value.distanceFromStation_3
            this.walkFromStation_3 = value.walkFromStation_3
            this.busFromStation_3 = value.busFromStation_3
            this.busStopName_3 = value.busStopName_3
            this.fromBusStop_3 = value.fromBusStop_3
            this.distanceFromBusstop_3 = value.distanceFromBusstop_3
            this.nearestRoute_3 = value.nearestRoute_3
            this.nearestStation_3 = value.nearestStation_3
            this.kindaikaCodeText_3 = value.kindaikaCodeText_3
            this.wayToCode_3 = value.wayToCode_3
            this.zipCodeText = value.zipCodeText
            this.prefecture = value.prefecture
            this.city = value.city
            this.town = value.town
            this.tyoume = value.tyoume
            this.kokudoCodeText = value.kokudoCodeText
            this.jisCodeValue = value.jisCodeValue
            this.townCodeValue = value.townCodeValue
            this.tyoumeCodeValue = value.tyoumeCodeValue
            this.restaddr1 = value.restaddr1
            this.latitude = value.latitude
            this.longitude = value.longitude
            this.buildingName = value.buildingName
            this.dispNameCode = value.dispNameCode
            this.buildingFurigana = value.buildingFurigana
            this.kindCode = value.kindCode
            this.kindDispName = value.kindDispName
            this.saleBlockNum = value.saleBlockNum
            this.emptyHousesNum = value.emptyHousesNum
            this.sellingCompany = value.sellingCompany
            this.completionDate = value.completionDate
            this.areaWays1Code = value.areaWays1Code
            this.structureCode = value.structureCode
            this.structureDispName = value.structureDispName
            this.buildingTypeCode = value.buildingTypeCode
            this.allFloorNum = value.allFloorNum
            this.underFloorNum = value.underFloorNum
            this.newUsedCode = value.newUsedCode
            this.managerStyleCode = value.managerStyleCode
            this.managerComment = value.managerComment
            this.quietCode = value.quietCode
            this.gasCode = value.gasCode
            this.waterSupplyCode = value.waterSupplyCode
            this.wasteWaterCode = value.wasteWaterCode
            this.elecPowerCode = value.elecPowerCode
            this.twoByFourCode = value.twoByFourCode
            this.sellTypeCode = value.sellTypeCode
            this.avoidQuakeCode = value.avoidQuakeCode
            this.barrierFreeCode = value.barrierFreeCode
            this.fulltimeManagementCode = value.fulltimeManagementCode
            this.liftCode = value.liftCode
            this.liftNumCode = value.liftNumCode
            this.wallTypeCode = value.wallTypeCode
            this.deliveryMailboxCode = value.deliveryMailboxCode
            this.launderetteCode = value.launderetteCode
            this.roomNumberText = value.roomNumberText
            this.dispRoomNumberCode = value.dispRoomNumberCode
            this.salesPoint = value.salesPoint
            this.remark1 = value.remark1
            this.remark2 = value.remark2
            this.specialRemark = value.specialRemark
            this.note = value.note
            this.price = value.price
            this.priceTaxCode = value.priceTaxCode
            this.consumptionTax = value.consumptionTax
            this.queryPerson = value.queryPerson
            this.firmSideCode = value.firmSideCode
            this.intoCode = value.intoCode
            this.intoDate = value.intoDate
            this.leaveDate = value.leaveDate
            this.otherCompanyCode = value.otherCompanyCode
            this.messageToOtherCompany = value.messageToOtherCompany
            this.registDate = value.registDate
            this.registTime = value.registTime
            this.rentExchangeStyleCode = value.rentExchangeStyleCode
            this.housePlanCode = value.housePlanCode
            this.roomNum = value.roomNum
            this.housePlanEquiv = value.housePlanEquiv
            this.windowDirectionCode = value.windowDirectionCode
            this.floorNum = value.floorNum
            this.nonMoveintoCode = value.nonMoveintoCode
            this.managedPropertyCode = value.managedPropertyCode
            this.petCode = value.petCode
            this.officeCode = value.officeCode
            this.musicalCode = value.musicalCode
            this.housePlanDispName = value.housePlanDispName
            this.usePartArea = value.usePartArea
            this.keyMoney = value.keyMoney
            this.keyMoneyUnitCode = value.keyMoneyUnitCode
            this.keyMoneyTaxCode = value.keyMoneyTaxCode
            this.deposit = value.deposit
            this.depositUnitCode = value.depositUnitCode
            this.repairCost = value.repairCost
            this.repairCostUnitCode = value.repairCostUnitCode
            this.guaranty = value.guaranty
            this.guarantyUnitCode = value.guarantyUnitCode
            this.syokyakuClassCode = value.syokyakuClassCode
            this.syokyaku = value.syokyaku
            this.syokyakuUnitCode = value.syokyakuUnitCode
            this.premium = value.premium
            this.premiumUnitCode = value.premiumUnitCode
            this.premiumTaxCode = value.premiumTaxCode
            this.manageCost = value.manageCost
            this.manageCostTaxCode = value.manageCostTaxCode
            this.serviceFee = value.serviceFee
            this.serviceFeeFreeCode = value.serviceFeeFreeCode
            this.serviceFeeTaxCode = value.serviceFeeTaxCode
            this.zappi = value.zappi
            this.zappiTaxCode = value.zappiTaxCode
            this.otherCostComment = value.otherCostComment
            this.otherCost_1 = value.otherCost_1
            this.otherCostItem_1 = value.otherCostItem_1
            this.otherCostTaxCode_1 = value.otherCostTaxCode_1
            this.otherCost_2 = value.otherCost_2
            this.otherCostItem_2 = value.otherCostItem_2
            this.otherCostTaxCode_2 = value.otherCostTaxCode_2
            this.otherCost_3 = value.otherCost_3
            this.otherCostItem_3 = value.otherCostItem_3
            this.otherCostTaxCode_3 = value.otherCostTaxCode_3
            this.otherCost_4 = value.otherCost_4
            this.otherCostItem_4 = value.otherCostItem_4
            this.otherCostTaxCode_4 = value.otherCostTaxCode_4
            this.otherCost_5 = value.otherCost_5
            this.otherCostItem_5 = value.otherCostItem_5
            this.otherCostTaxCode_5 = value.otherCostTaxCode_5
            this.outerFacilityCode_1 = value.outerFacilityCode_1
            this.outerFacilityCode_2 = value.outerFacilityCode_2
            this.outerArea_2 = value.outerArea_2
            this.renewalFee = value.renewalFee
            this.renewalFeeUnitCode = value.renewalFeeUnitCode
            this.renewalFeeClassCode = value.renewalFeeClassCode
            this.houseRentLimitDate = value.houseRentLimitDate
            this.insuranceCode = value.insuranceCode
            this.specialRentalLowerCost = value.specialRentalLowerCost
            this.specialRentalUpperCost = value.specialRentalUpperCost
            this.additionalDepositUnitCode = value.additionalDepositUnitCode
            this.additionalDepositReasonCode = value.additionalDepositReasonCode
            this.brokerage = value.brokerage
            this.brokerageUnitCode = value.brokerageUnitCode
            this.renewalCharge = value.renewalCharge
            this.renewalChargeUnitCode = value.renewalChargeUnitCode
            this.studentOnlyCode = value.studentOnlyCode
            this.sexConditionCode = value.sexConditionCode
            this.kidsCode = value.kidsCode
            this.aloneCode = value.aloneCode
            this.twoPeopleCode = value.twoPeopleCode
            this.elderCode = value.elderCode
            this.corporationOnlyCode = value.corporationOnlyCode
            this.residenceHouseRentCode = value.residenceHouseRentCode
            this.roomStyleCode_1 = value.roomStyleCode_1
            this.roomArea_1 = value.roomArea_1
            this.roomUnitCode_1 = value.roomUnitCode_1
            this.roomStyleCode_2 = value.roomStyleCode_2
            this.roomArea_2 = value.roomArea_2
            this.roomUnitCode_2 = value.roomUnitCode_2
            this.roomStyleCode_3 = value.roomStyleCode_3
            this.roomArea_3 = value.roomArea_3
            this.roomUnitCode_3 = value.roomUnitCode_3
            this.roomStyleCode_4 = value.roomStyleCode_4
            this.roomArea_4 = value.roomArea_4
            this.roomUnitCode_4 = value.roomUnitCode_4
            this.roomStyleCode_5 = value.roomStyleCode_5
            this.roomArea_5 = value.roomArea_5
            this.roomUnitCode_5 = value.roomUnitCode_5
            this.roomStyleCode_6 = value.roomStyleCode_6
            this.roomArea_6 = value.roomArea_6
            this.roomUnitCode_6 = value.roomUnitCode_6
            this.roomStyleCode_7 = value.roomStyleCode_7
            this.roomArea_7 = value.roomArea_7
            this.roomUnitCode_7 = value.roomUnitCode_7
            this.roomStyleCode_8 = value.roomStyleCode_8
            this.roomArea_8 = value.roomArea_8
            this.roomUnitCode_8 = value.roomUnitCode_8
            this.roomStyleCode_9 = value.roomStyleCode_9
            this.roomArea_9 = value.roomArea_9
            this.roomUnitCode_9 = value.roomUnitCode_9
            this.roomStyleCode_10 = value.roomStyleCode_10
            this.roomArea_10 = value.roomArea_10
            this.roomUnitCode_10 = value.roomUnitCode_10
            this.parkingCode = value.parkingCode
            this.fromNearParking = value.fromNearParking
            this.parkingNum = value.parkingNum
            this.parkingTypeCode = value.parkingTypeCode
            this.parkingShutterCode = value.parkingShutterCode
            this.parkingLowerCost = value.parkingLowerCost
            this.parkingTaxCode = value.parkingTaxCode
            this.parkableNumCode = value.parkableNumCode
            this.parkingFreeCode = value.parkingFreeCode
            this.bikeParkCode = value.bikeParkCode
            this.bikeParkCost = value.bikeParkCost
            this.motorbikeParkCode = value.motorbikeParkCode
            this.motorbikeCost = value.motorbikeCost
            this.airconCode = value.airconCode
            this.coolerCode = value.coolerCode
            this.heatingCode = value.heatingCode
            this.loadHeaterCode = value.loadHeaterCode
            this.stoveCode = value.stoveCode
            this.floorHeatingCode = value.floorHeatingCode
            this.catvCode = value.catvCode
            this.communityBroadcastCode = value.communityBroadcastCode
            this.bsCode = value.bsCode
            this.csCode = value.csCode
            this.internetCode = value.internetCode
            this.closetCode = value.closetCode
            this.walkinWardrobeCode = value.walkinWardrobeCode
            this.closetUnderFloorCode = value.closetUnderFloorCode
            this.trunkRoomCode = value.trunkRoomCode
            this.oshiireCode = value.oshiireCode
            this.garretClosetCode = value.garretClosetCode
            this.shoeCupboardCode = value.shoeCupboardCode
            this.storeroomCode = value.storeroomCode
            this.bathToiletCode = value.bathToiletCode
            this.bathCode = value.bathCode
            this.showerCode = value.showerCode
            this.autoBathCode = value.autoBathCode
            this.dressingRoomCode = value.dressingRoomCode
            this.reboilBathCode = value.reboilBathCode
            this.toiletCode = value.toiletCode
            this.bathDrierCode = value.bathDrierCode
            this.shampooDresserCode = value.shampooDresserCode
            this.washletCode = value.washletCode
            this.bathOver_1tsuboCode = value.bathOver_1tsuboCode
            this.warmletCode = value.warmletCode
            this.cookingStoveCode = value.cookingStoveCode
            this.kitchenCode = value.kitchenCode
            this.microwaveOvenCode = value.microwaveOvenCode
            this.ihCookingHeaterCode = value.ihCookingHeaterCode
            this.coldStorageCode = value.coldStorageCode
            this.grillCode = value.grillCode
            this.disposerCode = value.disposerCode
            this.dishWasherCode = value.dishWasherCode
            this.waterCleanerCode = value.waterCleanerCode
            this.woodenFloorCode = value.woodenFloorCode
            this.loftCode = value.loftCode
            this.cushionFloorCode = value.cushionFloorCode
            this.highestFloorCode = value.highestFloorCode
            this.maisonetteCode = value.maisonetteCode
            this.overSecondFloorCode = value.overSecondFloorCode
            this.caveCode = value.caveCode
            this.soundproofCode = value.soundproofCode
            this.cornerHouseCode = value.cornerHouseCode
            this.sunroomCode = value.sunroomCode
            this.basementCode = value.basementCode
            this.southRoomCode = value.southRoomCode
            this.patioCode = value.patioCode
            this.crimePrevShutterCode = value.crimePrevShutterCode
            this.crimePrevCameraCode = value.crimePrevCameraCode
            this.autolockCode = value.autolockCode
            this.doubleLockCode = value.doubleLockCode
            this.washingMachineCode = value.washingMachineCode
            this.drierCode = value.drierCode
            this.washingMachinePlaceCode = value.washingMachinePlaceCode
            this.cardKeyCode = value.cardKeyCode
            this.bowWindowCode = value.bowWindowCode
            this.lightCode = value.lightCode
            this.allElectricCode = value.allElectricCode
            this.hotWaterSupplyCode = value.hotWaterSupplyCode
            this.interphoneCode = value.interphoneCode
            this.fulltimeFunCode = value.fulltimeFunCode
            this.ecocuteCode = value.ecocuteCode
            this.doubleSideBalconyCode = value.doubleSideBalconyCode
            this.balconySideNumCode = value.balconySideNumCode
            this.bathTvCode = value.bathTvCode
            this.porchCode = value.porchCode
            this.upStartDate = value.upStartDate
            this.upEndDate = value.upEndDate
            this.dressingTableCode = value.dressingTableCode
            this.privateDustBoxCode = value.privateDustBoxCode
            this.pianoCode = value.pianoCode
            this.largeShoesBoxCode = value.largeShoesBoxCode
            this.closetUnderTatamiCode = value.closetUnderTatamiCode
            this.indoorsBicycleParkingCode = value.indoorsBicycleParkingCode
            this.securityKeyCode = value.securityKeyCode
            this.shutterCode = value.shutterCode
            this.forSouthCode = value.forSouthCode
            this.closetUnderstairCode = value.closetUnderstairCode
            this.nearbyConvenienceStoreCode = value.nearbyConvenienceStoreCode
            this.nearbyBankCode = value.nearbyBankCode
            this.nearbyRentalVideoCode = value.nearbyRentalVideoCode
            this.largeScaleRenewalCode = value.largeScaleRenewalCode
            this.recoveryCostCode = value.recoveryCostCode
            this.guarantorCode = value.guarantorCode
            this.guarantorProxyCode = value.guarantorProxyCode
            this.guarantorProxyComCode = value.guarantorProxyComCode
            this.guarantorProxyComment = value.guarantorProxyComment
            this.dispMapCode = value.dispMapCode
            this.latitudeWorld = value.latitudeWorld
            this.longitudeWorld = value.longitudeWorld
            this.gardenCode = value.gardenCode
            this.balconyCode = value.balconyCode
            this.panoramaId = value.panoramaId
            this.largeScaleRenewalDate = value.largeScaleRenewalDate
            this.shatakuKanouCode = value.shatakuKanouCode
            this.noDepositPlanCode = value.noDepositPlanCode
            this.kentakuKindCode = value.kentakuKindCode
            this.priceSaleFlag = value.priceSaleFlag
            this.refomeFlag = value.refomeFlag
            this.productCode = value.productCode
            this.ownerShipBranchCode = value.ownerShipBranchCode
            this.kentakuBuildingCode = value.kentakuBuildingCode
            this.kentakuRoomCode = value.kentakuRoomCode
            this.keyExchangeFreeCode = value.keyExchangeFreeCode
            this.adPrice = value.adPrice
            this.ffPrice = value.ffPrice
            this.leaveDateTp = value.leaveDateTp
            this.leaveFinishDate = value.leaveFinishDate
            this.lowParkingPrice = value.lowParkingPrice
            this.highParkingPrice = value.highParkingPrice
            this.structureDispNameTp = value.structureDispNameTp
            this.displaceCode = value.displaceCode
            this.financeCorporationCode = value.financeCorporationCode
            this.waterCompanyName = value.waterCompanyName
            this.waterCompanyTel = value.waterCompanyTel
            this.electricCompanyName = value.electricCompanyName
            this.electricCompanyTel = value.electricCompanyTel
            this.gasCompanyName = value.gasCompanyName
            this.gasCompanyTel = value.gasCompanyTel
            this.collectDate = value.collectDate
            this.kouentinCode = value.kouentinCode
            this.intoDateTxt = value.intoDateTxt
            this.roomSituationCode = value.roomSituationCode
            this.recordSituationCode = value.recordSituationCode
            this.electricDiscountFlag = value.electricDiscountFlag
            this.fletsHikariCode = value.fletsHikariCode
            this.akiyaTerm = value.akiyaTerm
            this.cleaningFeeCode = value.cleaningFeeCode
            this.cleaningFee = value.cleaningFee
            this.powerCode = value.powerCode
            this.fireZoneCode = value.fireZoneCode
            this.discountRate = value.discountRate
            this.discountTerm = value.discountTerm
            this.petFlag = value.petFlag
            this.internetFreeCode = value.internetFreeCode
            this.allRoomCloset = value.allRoomCloset
            this.walkThroughCloset = value.walkThroughCloset
            this.freeWashRoom = value.freeWashRoom
            this.autoBath = value.autoBath
            this.indoorClothesDrying = value.indoorClothesDrying
            this.motionSensorLighting = value.motionSensorLighting
            this.openKitchen = value.openKitchen
            this.islandKitchen = value.islandKitchen
            this.gasCookerAttached = value.gasCookerAttached
            this.threeOverGas = value.threeOverGas
            this.doubleGlazing = value.doubleGlazing
            this.securityGlazing = value.securityGlazing
            this.vibrationControlFloor = value.vibrationControlFloor
            this.snowVanishingFacility = value.snowVanishingFacility
            this.keroseneHeater = value.keroseneHeater
            this.bathWindow = value.bathWindow
            this.japaneseStyleRoom = value.japaneseStyleRoom
            this.earthquakeResistConst = value.earthquakeResistConst
            this.allinoneServiceWater = value.allinoneServiceWater
            this.allinoneServiceElectricity = value.allinoneServiceElectricity
            this.allinoneServiceGas = value.allinoneServiceGas
            this.priceAndCost = value.priceAndCost
            this.valCode_1 = value.valCode_1
            this.valCode_2 = value.valCode_2
            this.valCode_3 = value.valCode_3
            this.panoramaType = value.panoramaType
            this.serviceFeeDetails = value.serviceFeeDetails
            this.shinsaBranchCode = value.shinsaBranchCode
            this.contractConfirmCode = value.contractConfirmCode
            this.prefectureEn = value.prefectureEn
            this.shikugunchousonEn = value.shikugunchousonEn
            this.ooazaTsuusyouEn = value.ooazaTsuusyouEn
            this.azaChoumeEn = value.azaChoumeEn
            this.restaddrAlphabet = value.restaddrAlphabet
            this.upState = value.upState
            this.adPriceUnitCode = value.adPriceUnitCode
            this.deleteDate = value.deleteDate
            this.realtimeUpTime = value.realtimeUpTime
            this.realtimeUpType = value.realtimeUpType
            this.productTypeCd = value.productTypeCd
            this.moneyUpdateTime = value.moneyUpdateTime
            this.kodawari100_199 = value.kodawari100_199
            this.floorMaxRoom = value.floorMaxRoom
            this.renewalFeeFlg = value.renewalFeeFlg
            this.fulltimeSupportFlg = value.fulltimeSupportFlg
            this.membershipFeeExemptionKbn = value.membershipFeeExemptionKbn
            this.membershipFeeExemptionDays = value.membershipFeeExemptionDays
            this.eboardComment = value.eboardComment
            this.managementParkingKbn = value.managementParkingKbn
            this.netServiceJcom = value.netServiceJcom
            this.netServiceStarcat = value.netServiceStarcat
            this.zehOriented = value.zehOriented
            this.zehDkSoleil = value.zehDkSoleil
            this.zehDkAlpha = value.zehDkAlpha
            this.keySetCostFlag = value.keySetCostFlag
            this.electricIntroduction = value.electricIntroduction
            this.electricType = value.electricType
            this.emergencyECompanyName = value.emergencyECompanyName
            this.emergencyECompanyTel = value.emergencyECompanyTel
            this.gasIntroduction = value.gasIntroduction
            this.emergencyGasCompanyName = value.emergencyGasCompanyName
            this.emergencyGasCompanyTel = value.emergencyGasCompanyTel
            this.waterIntroduction = value.waterIntroduction
            this.waterMeterType = value.waterMeterType
            this.internetType = value.internetType
            this.internetName = value.internetName
            this.internetTel = value.internetTel
            this.internetIntroduction = value.internetIntroduction
            this.waterServer = value.waterServer
            this.lifelineGuidanceType = value.lifelineGuidanceType
            this.roomSaveEnergyCertDate = value.roomSaveEnergyCertDate
            this.roomThirdPartyEvalFlg = value.roomThirdPartyEvalFlg
            this.roomSaveEnergyLevel = value.roomSaveEnergyLevel
            this.roomEnergyCost = value.roomEnergyCost
            this.roomEnergyCostSun = value.roomEnergyCostSun
            this.roomRenewEnergyFlg = value.roomRenewEnergyFlg
            this.roomInsulationLevel = value.roomInsulationLevel
            this.roomEasyUtilityCosts = value.roomEasyUtilityCosts
            this.roomZehLevelFlg = value.roomZehLevelFlg
            this.roomNetZeroEnergyFlg = value.roomNetZeroEnergyFlg
            this.buildingSaveEnergyCertDate = value.buildingSaveEnergyCertDate
            this.buildingThirdPartyEvalFlg = value.buildingThirdPartyEvalFlg
            this.buildingSaveEnergyLevel = value.buildingSaveEnergyLevel
            this.buildingEnergyCost = value.buildingEnergyCost
            this.buildingEnergyCostSun = value.buildingEnergyCostSun
            this.buildingRenewEnergyFlg = value.buildingRenewEnergyFlg
            this.buildingInsulationLevel = value.buildingInsulationLevel
            this.buildingEasyUtilityCosts = value.buildingEasyUtilityCosts
            this.buildingZehLevelFlg = value.buildingZehLevelFlg
            this.buildingNetZeroEnergyFlg = value.buildingNetZeroEnergyFlg
            this.shinsaBusinessOfficeCode = value.shinsaBusinessOfficeCode
            this.challengeStart = value.challengeStart
            this.challengeEnd = value.challengeEnd
            this.challengeDiscountPrice = value.challengeDiscountPrice
            resetChangedOnNotNull()
        }
    }
}
