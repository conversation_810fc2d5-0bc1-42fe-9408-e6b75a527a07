-- ************************************************************************** --
-- ファンクション名 : UPDATE_PREFECTURE_CITY_CD
-- 処理概要         : 県CD-市区群町村CDカラムを更新します
-- 引数             :
-- 戻り値           :
-- 備考             :
-- ************************************************************************** --
CREATE OR REPLACE FUNCTION update_prefecture_city_cd()
RETURNS TRIGGER AS $$
BEGIN
    NEW.prefecture_city_cd := NEW.prefecture_cd || '-' || NEW.city_cd;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_prefecture_city_cd
BEFORE INSERT OR UPDATE ON room_info_master
FOR EACH ROW
EXECUTE FUNCTION update_prefecture_city_cd();
