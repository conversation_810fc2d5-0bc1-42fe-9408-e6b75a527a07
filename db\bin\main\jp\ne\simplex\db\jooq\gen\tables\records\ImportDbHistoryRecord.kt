/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.time.LocalDateTime

import jp.ne.simplex.db.jooq.gen.tables.ImportDbHistoryTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ImportDbHistoryPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * DB取込実行履歴 既存システム物理名: -
 */
@Suppress("UNCHECKED_CAST")
open class ImportDbHistoryRecord private constructor() : UpdatableRecordImpl<ImportDbHistoryRecord>(ImportDbHistoryTable.IMPORT_DB_HISTORY) {

    open var fileKey: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var importTimestamp: LocalDateTime
        set(value): Unit = set(1, value)
        get(): LocalDateTime = get(1) as LocalDateTime

    open var deleteFlag: Boolean
        set(value): Unit = set(2, value)
        get(): Boolean = get(2) as Boolean

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, Boolean?> = super.key() as Record2<String?, Boolean?>

    /**
     * Create a detached, initialised ImportDbHistoryRecord
     */
    constructor(fileKey: String, importTimestamp: LocalDateTime, deleteFlag: Boolean): this() {
        this.fileKey = fileKey
        this.importTimestamp = importTimestamp
        this.deleteFlag = deleteFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ImportDbHistoryRecord
     */
    constructor(value: ImportDbHistoryPojo?): this() {
        if (value != null) {
            this.fileKey = value.fileKey
            this.importTimestamp = value.importTimestamp
            this.deleteFlag = value.deleteFlag
            resetChangedOnNotNull()
        }
    }
}
