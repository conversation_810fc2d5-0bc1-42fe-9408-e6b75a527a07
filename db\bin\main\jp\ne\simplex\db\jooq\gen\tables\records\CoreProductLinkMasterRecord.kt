/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.CoreProductLinkMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.CoreProductLinkMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 基幹商品紐付けマスタ 既存システム物理名: ERA20P
 */
@Suppress("UNCHECKED_CAST")
open class CoreProductLinkMasterRecord private constructor() : TableRecordImpl<CoreProductLinkMasterRecord>(CoreProductLinkMasterTable.CORE_PRODUCT_LINK_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var updateProgramId: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(7, value)
        get(): Byte? = get(7) as Byte?

    open var buildingCd: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var roomCd: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var coreProductId: Int?
        set(value): Unit = set(10, value)
        get(): Int? = get(10) as Int?

    open var layer1TypeId: Int?
        set(value): Unit = set(11, value)
        get(): Int? = get(11) as Int?

    open var layer2TypeId: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    open var layer3TypeId: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var layer4TypeId: Int?
        set(value): Unit = set(14, value)
        get(): Int? = get(14) as Int?

    open var layer5TypeId: Int?
        set(value): Unit = set(15, value)
        get(): Int? = get(15) as Int?

    open var layer6TypeId: Int?
        set(value): Unit = set(16, value)
        get(): Int? = get(16) as Int?

    open var layer7TypeId: Int?
        set(value): Unit = set(17, value)
        get(): Int? = get(17) as Int?

    open var layer8TypeId: Int?
        set(value): Unit = set(18, value)
        get(): Int? = get(18) as Int?

    open var layer9TypeId: Int?
        set(value): Unit = set(19, value)
        get(): Int? = get(19) as Int?

    open var exteriorImageId: Int?
        set(value): Unit = set(20, value)
        get(): Int? = get(20) as Int?

    open var floorPlanImageId: Int?
        set(value): Unit = set(21, value)
        get(): Int? = get(21) as Int?

    open var interiorImageId1: Int?
        set(value): Unit = set(22, value)
        get(): Int? = get(22) as Int?

    open var interiorImageId2: Int?
        set(value): Unit = set(23, value)
        get(): Int? = get(23) as Int?

    open var interiorImageId3: Int?
        set(value): Unit = set(24, value)
        get(): Int? = get(24) as Int?

    open var interiorImageId4: Int?
        set(value): Unit = set(25, value)
        get(): Int? = get(25) as Int?

    open var interiorImageId5: Int?
        set(value): Unit = set(26, value)
        get(): Int? = get(26) as Int?

    open var interiorImageId6: Int?
        set(value): Unit = set(27, value)
        get(): Int? = get(27) as Int?

    open var interiorImageId7: Int?
        set(value): Unit = set(28, value)
        get(): Int? = get(28) as Int?

    open var interiorImageId8: Int?
        set(value): Unit = set(29, value)
        get(): Int? = get(29) as Int?

    open var interiorImageId9: Int?
        set(value): Unit = set(30, value)
        get(): Int? = get(30) as Int?

    open var interiorImageId10: Int?
        set(value): Unit = set(31, value)
        get(): Int? = get(31) as Int?

    open var interiorImageId11: Int?
        set(value): Unit = set(32, value)
        get(): Int? = get(32) as Int?

    open var interiorImageId12: Int?
        set(value): Unit = set(33, value)
        get(): Int? = get(33) as Int?

    open var coreProductName: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    /**
     * Create a detached, initialised CoreProductLinkMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, updateProgramId: String? = null, logicalDeleteSign: Byte? = null, buildingCd: String? = null, roomCd: String? = null, coreProductId: Int? = null, layer1TypeId: Int? = null, layer2TypeId: Int? = null, layer3TypeId: Int? = null, layer4TypeId: Int? = null, layer5TypeId: Int? = null, layer6TypeId: Int? = null, layer7TypeId: Int? = null, layer8TypeId: Int? = null, layer9TypeId: Int? = null, exteriorImageId: Int? = null, floorPlanImageId: Int? = null, interiorImageId1: Int? = null, interiorImageId2: Int? = null, interiorImageId3: Int? = null, interiorImageId4: Int? = null, interiorImageId5: Int? = null, interiorImageId6: Int? = null, interiorImageId7: Int? = null, interiorImageId8: Int? = null, interiorImageId9: Int? = null, interiorImageId10: Int? = null, interiorImageId11: Int? = null, interiorImageId12: Int? = null, coreProductName: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.updateProgramId = updateProgramId
        this.logicalDeleteSign = logicalDeleteSign
        this.buildingCd = buildingCd
        this.roomCd = roomCd
        this.coreProductId = coreProductId
        this.layer1TypeId = layer1TypeId
        this.layer2TypeId = layer2TypeId
        this.layer3TypeId = layer3TypeId
        this.layer4TypeId = layer4TypeId
        this.layer5TypeId = layer5TypeId
        this.layer6TypeId = layer6TypeId
        this.layer7TypeId = layer7TypeId
        this.layer8TypeId = layer8TypeId
        this.layer9TypeId = layer9TypeId
        this.exteriorImageId = exteriorImageId
        this.floorPlanImageId = floorPlanImageId
        this.interiorImageId1 = interiorImageId1
        this.interiorImageId2 = interiorImageId2
        this.interiorImageId3 = interiorImageId3
        this.interiorImageId4 = interiorImageId4
        this.interiorImageId5 = interiorImageId5
        this.interiorImageId6 = interiorImageId6
        this.interiorImageId7 = interiorImageId7
        this.interiorImageId8 = interiorImageId8
        this.interiorImageId9 = interiorImageId9
        this.interiorImageId10 = interiorImageId10
        this.interiorImageId11 = interiorImageId11
        this.interiorImageId12 = interiorImageId12
        this.coreProductName = coreProductName
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised CoreProductLinkMasterRecord
     */
    constructor(value: CoreProductLinkMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.updateProgramId = value.updateProgramId
            this.logicalDeleteSign = value.logicalDeleteSign
            this.buildingCd = value.buildingCd
            this.roomCd = value.roomCd
            this.coreProductId = value.coreProductId
            this.layer1TypeId = value.layer1TypeId
            this.layer2TypeId = value.layer2TypeId
            this.layer3TypeId = value.layer3TypeId
            this.layer4TypeId = value.layer4TypeId
            this.layer5TypeId = value.layer5TypeId
            this.layer6TypeId = value.layer6TypeId
            this.layer7TypeId = value.layer7TypeId
            this.layer8TypeId = value.layer8TypeId
            this.layer9TypeId = value.layer9TypeId
            this.exteriorImageId = value.exteriorImageId
            this.floorPlanImageId = value.floorPlanImageId
            this.interiorImageId1 = value.interiorImageId1
            this.interiorImageId2 = value.interiorImageId2
            this.interiorImageId3 = value.interiorImageId3
            this.interiorImageId4 = value.interiorImageId4
            this.interiorImageId5 = value.interiorImageId5
            this.interiorImageId6 = value.interiorImageId6
            this.interiorImageId7 = value.interiorImageId7
            this.interiorImageId8 = value.interiorImageId8
            this.interiorImageId9 = value.interiorImageId9
            this.interiorImageId10 = value.interiorImageId10
            this.interiorImageId11 = value.interiorImageId11
            this.interiorImageId12 = value.interiorImageId12
            this.coreProductName = value.coreProductName
            resetChangedOnNotNull()
        }
    }
}
