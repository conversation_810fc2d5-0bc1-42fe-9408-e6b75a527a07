/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 家賃審査センター管轄支店 既存システム物理名: EJD10P
 */
@Suppress("UNCHECKED_CAST")
data class RentReviewCenterBranchPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updater: String? = null,
    var rentReviewCenterCode: String? = null,
    var centerDivision: String? = null,
    var rentReviewCenterName: String? = null,
    var rentReviewCenterAbbrev: String? = null,
    var jurisdictionBranchCd: String? = null,
    var deletionDate: Int? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: RentReviewCenterBranchPojo = other as RentReviewCenterBranchPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.rentReviewCenterCode == null) {
            if (o.rentReviewCenterCode != null)
                return false
        }
        else if (this.rentReviewCenterCode != o.rentReviewCenterCode)
            return false
        if (this.centerDivision == null) {
            if (o.centerDivision != null)
                return false
        }
        else if (this.centerDivision != o.centerDivision)
            return false
        if (this.rentReviewCenterName == null) {
            if (o.rentReviewCenterName != null)
                return false
        }
        else if (this.rentReviewCenterName != o.rentReviewCenterName)
            return false
        if (this.rentReviewCenterAbbrev == null) {
            if (o.rentReviewCenterAbbrev != null)
                return false
        }
        else if (this.rentReviewCenterAbbrev != o.rentReviewCenterAbbrev)
            return false
        if (this.jurisdictionBranchCd == null) {
            if (o.jurisdictionBranchCd != null)
                return false
        }
        else if (this.jurisdictionBranchCd != o.jurisdictionBranchCd)
            return false
        if (this.deletionDate == null) {
            if (o.deletionDate != null)
                return false
        }
        else if (this.deletionDate != o.deletionDate)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.rentReviewCenterCode == null) 0 else this.rentReviewCenterCode.hashCode())
        result = prime * result + (if (this.centerDivision == null) 0 else this.centerDivision.hashCode())
        result = prime * result + (if (this.rentReviewCenterName == null) 0 else this.rentReviewCenterName.hashCode())
        result = prime * result + (if (this.rentReviewCenterAbbrev == null) 0 else this.rentReviewCenterAbbrev.hashCode())
        result = prime * result + (if (this.jurisdictionBranchCd == null) 0 else this.jurisdictionBranchCd.hashCode())
        result = prime * result + (if (this.deletionDate == null) 0 else this.deletionDate.hashCode())
        return result
    }
}
