package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.db.ParkingEnableRepositoryInterface
import jp.ne.simplex.application.repository.db.ParkingRepositoryInterface
import jp.ne.simplex.application.repository.db.ParkingReservationRepositoryInterface
import jp.ne.simplex.application.repository.external.eboard.EboardRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ServerValidationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDateTime

@Service
class ParkingReservationService(
    private val parkingContractService: ParkingContractService,
    private val mailService: ParkingReservationMailService,
    private val reservationRepository: ParkingReservationRepositoryInterface,
    private val parkingDetailsService: ParkingDetailsService,
    private val parkingRepository: ParkingRepositoryInterface,
    private val parkingEnableRepository: ParkingEnableRepositoryInterface,
    private val eBoardRepository: EboardRepositoryInterface,
) {

    companion object {
        private val log = LoggerFactory.getLogger(ParkingReservationService::class.java)

        private val validReservationStatusList: List<ParkingReservation.Status> = listOf(
            ParkingReservation.Status.TENTATIVE,
            ParkingReservation.Status.RESERVATION
        )

        private val applicationTypeList = listOf(
            ParkingReservation.Type.AUTO_APPLICATION,
            ParkingReservation.Type.MANUAL_APPLICATION,
        )

        private val doneStatusList = listOf(
            ParkingLot.StatusDivision.CONFIRMED,
            ParkingLot.StatusDivision.OCCUPIED,
        )

        private val signedStatusList = listOf(
            ParkingLot.StatusDivision.APPLIED,
            ParkingLot.StatusDivision.PLANNED_MOVE_OUT,
            ParkingLot.StatusDivision.COLLECTING,
        ) + doneStatusList
    }

    fun updateReservations(
        authInfo: AuthInfo,
        params: List<UpdateOrCancelParkingReservation>
    ): List<ParkingReservation.Id> {
        // 駐車場予約の更新処理を随時実行しエラーが発生した場合はIDを返却
        return params.mapNotNull {
            runCatching {
                updateReservation(authInfo, it)
                null
            }.getOrElse { e ->
                when (it) {
                    is UpdateParkingReservation -> {
                        log.warn("駐車場予約の更新に失敗しました {}", it.id, e)
                        it.id
                    }

                    is CancelParkingReservation -> {
                        log.warn("駐車場予約の取消に失敗しました {}", it.id, e)
                        it.id
                    }

                    is FinishParkingReservation -> {
                        log.warn("駐車場予約の完了に失敗しました {}", it.id, e)
                        it.id
                    }
                }
            }
        }
    }

    fun updateReservation(authInfo: AuthInfo, param: ParkingReservationAction) {
        when (param) {
            is RegisterParkingReservation -> {
                // キマルームサインからの駐車場予約登録の場合
                // 仮予約がある場合、ない場合で通常の処理と異なるので明示的に処理を分ける
                if (authInfo is AuthInfo.ApiKey && authInfo.externalSystem == ExternalSystem.KIMAROOM_SIGN) {
                    return when (val tentative =
                        reservationRepository.findTentative(param.parkingLotId)) {
                        // 仮予約がない場合、通常と同様Insert
                        // @formatter:off
                        null -> {
                            validateRegister(param)
                                .let { reservationRepository.register(authInfo.getRequestUser(), param) }
                                .let { notifyResultAsync(authInfo, param, param.parkingReservationId, null) }
                            // @formatter:on
                        }
                        // 仮予約が存在する場合は、更新
                        // @formatter:off
                        else -> {
                            UpdateParkingReservation(
                                id = tentative.id,
                                status = param.parkingReservationStatus,
                                reservationType = param.reservationType,
                                reserveStartDatetime = param.reserveStartDatetime,
                                reserveEndDatetime = param.reserveEndDatetime,
                                receptionStaff = param.receptionStaff,
                                reserverName = param.reserverName,
                                reserverTel = param.reserverTel,
                                requestSource = param.requestSource,
                                remarks = param.remarks,
                            ).let { updateParam ->
                                reservationRepository.update(authInfo.getRequestUser(), updateParam, param.reservationType)
                                    .let { notifyResultAsync(authInfo, updateParam, updateParam.id, tentative) }
                            }
                            // @formatter:on
                        }
                    }
                }

                validateRegister(param)
                    .let { reservationRepository.register(authInfo.getRequestUser(), param) }
                    .let { notifyResultAsync(authInfo, param, param.parkingReservationId, null) }
            }

            is UpdateParkingReservation -> {
                validateUpdate(param)
                    .let { targetRecord ->
                        reservationRepository.update(authInfo.getRequestUser(), param)
                            .let { notifyResultAsync(authInfo, param, param.id, targetRecord) }
                    }
            }

            is CancelParkingReservation -> {
                validateCancel(param)
                    .let { targetRecord ->
                        reservationRepository.cancel(authInfo.getRequestUser(), param)
                            .let { notifyResultAsync(authInfo, param, param.id, targetRecord) }
                    }
            }

            is CancelApplicationParkingReservation -> {
                validateCancelApplication(param)
                    .let { targetRecord ->
                        reservationRepository.cancelApplication(
                            authInfo.getRequestUser(),
                            targetRecord.id,
                            param
                        )
                            .let {
                                notifyResultAsync(
                                    authInfo,
                                    param,
                                    targetRecord.id,
                                    targetRecord
                                )
                            }
                    }
            }

            is FinishParkingReservation -> {
                // 駐車場予約を完了へ更新
                reservationRepository.finish(authInfo.getRequestUser(), param)
                    .let { notifyResultSync(authInfo, param, param.id, null) } //バッチ呼び出しのため同期処理
            }
        }
    }

    private fun notifyResultAsync(
        authInfo: AuthInfo,
        reservationAction: ParkingReservationAction,
        parkingReservationId: ParkingReservation.Id,
        targetRecord: ParkingReservationInfo?,
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            notifyResultSync(
                authInfo,
                reservationAction,
                parkingReservationId,
                targetRecord
            )
        }
    }

    private fun notifyResultSync(
        authInfo: AuthInfo,
        reservationAction: ParkingReservationAction,
        parkingReservationId: ParkingReservation.Id,
        targetRecord: ParkingReservationInfo?,
    ) {
        val parkingReservationInfo = reservationRepository.findById(parkingReservationId)

        // 駐車場申込判定結果の更新及びDKポータルへの連携
        runCatching {
            parkingContractService.updateParkingContractPossibilityAndInformDKPortal(
                authInfo.getRequestUser(), parkingReservationInfo!!.buildingCode.getOrderCode(),
                reservationAction is FinishParkingReservation // 消込バッチ時には同期処理とする
            )
        }.getOrElse {
            log.warn("駐車場申込判定結果の更新及びDKポータルへの連携に失敗しました。", it)
        }

        // いい物件ボードへの連携
        runCatching {
            eBoardRepository.reserveParking(reservationAction, parkingReservationInfo!!)
        }.getOrElse {
            log.warn("いい物件ボードへの駐車場予約の連携に失敗しました。", it)
        }

        // メールの送信
        runCatching {
            when (reservationAction) {
                is RegisterParkingReservation -> {
                    mailService.notifyRegister(authInfo, reservationAction)
                }

                is UpdateParkingReservation -> {
                    mailService.notifyUpdate(
                        authInfo,
                        targetRecord!!,
                        reservationAction.status,
                        reservationAction.remarks
                    )
                }

                is CancelParkingReservation -> {
                    mailService.notifyUpdate(
                        authInfo,
                        targetRecord!!,
                        reservationAction.status,
                        reservationAction.remarks
                    )
                }

                is CancelApplicationParkingReservation -> {
                    mailService.notifyUpdate(
                        authInfo,
                        targetRecord!!,
                        reservationAction.status,
                        reservationAction.remarks
                    )
                }

                is FinishParkingReservation -> {
                    // メール通知不要
                }
            }
        }.getOrElse {
            log.warn("駐車場予約完了メールの送信に失敗しました。", it)
        }
    }

    fun validateRegister(param: RegisterParkingReservation) {

        // 駐車場存在チェック
        if (!parkingRepository.isParkingExist(param.parkingLotId)) {
            throw ServerValidationException(ErrorMessage.PARKING_DOES_NOT_EXIST.format())
        }

        // 予約可能チェック
        if (!parkingEnableRepository.isAvailable(param.parkingLotId)) {
            throw ServerValidationException(ErrorMessage.PARKING_IS_NOT_AVAILABLE.format())
        }

        // 同一駐車場予約リストを取得
        val existReservations =
            reservationRepository.findActiveReservations(
                param.parkingLotId.buildingCode,
                param.parkingLotId.parkingLotCode
            )

        // 予約がない場合
        if (existReservations.isEmpty()) {

            // 駐車場情報を取得
            val parkingLot = parkingDetailsService.getParkingLot(param.parkingLotId)
            val status = parkingLot?.parkingStatusDivision
            if (status in signedStatusList) {
                if (param.reservationType != ParkingReservation.Type.REPLACE) {
                    if (status == ParkingLot.StatusDivision.PLANNED_MOVE_OUT) {
                        // 退去予定日がある場合は、予約開始日時が退去予定日より未来の場合のみ予約可能
                        if (parkingLot.expectedMoveOutDate?.isBefore(param.reserveStartDatetime?.toLocalDate()!!) == false) {
                            throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_INVALID_START_DATE_PLANNED_MOVE_OUT.format())
                        }
                    } else {
                        throw ServerValidationException(ErrorMessage.PARKING_IS_SIGNED.format())
                    }
                }
            } else {
                if (param.reservationType == ParkingReservation.Type.REPLACE) {
                    throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_REPLACE_UNAVAILABLE.format())
                }
            }
            return
        }

        // 複数予約になる場合は既存予約でバリデーションをかける
        for (reserved in existReservations) {
            when (reserved.reservationType) {
                ParkingReservation.Type.AUTO_APPLICATION,
                ParkingReservation.Type.MANUAL_APPLICATION -> {
                    when (param.reservationType) {
                        ParkingReservation.Type.AUTO_APPLICATION,
                        ParkingReservation.Type.MANUAL_APPLICATION -> {
                            throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_ALREADY_RESERVED.format())
                        }

                        ParkingReservation.Type.WORK,
                        ParkingReservation.Type.ONE_DAY ->
                            validatePeriod(
                                param.reserveStartDatetime!!,
                                param.reserveEndDatetime,
                                reserved
                            )

                        ParkingReservation.Type.REPLACE -> {}
                    }
                }

                ParkingReservation.Type.REPLACE -> {
                    throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_ALREADY_RESERVED.format())
                }

                ParkingReservation.Type.WORK,
                ParkingReservation.Type.ONE_DAY -> {
                    if (param.reservationType == ParkingReservation.Type.REPLACE) {
                        throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_REPLACE_UNAVAILABLE.format())
                    }
                    validatePeriod(param.reserveStartDatetime!!, param.reserveEndDatetime, reserved)
                }
            }
        }
    }

    private fun validateUpdate(param: UpdateParkingReservation): ParkingReservationInfo {
        // 更新対象レコードを取得
        val targetRecord = reservationRepository.findById(param.id)
            ?: throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_NOT_FOUND.format())

        if (targetRecord.reservationType != param.reservationType) {
            throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_TYPE_UNCHANGEABLE.format())
        }

        if (targetRecord.status !in validReservationStatusList) {
            // 対象が完了orキャンセルは更新不可
            throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_CHANGE_INVALID_STATUS.format())
        }

        // 予約期間の変更があった場合(場所変更はすり抜ける)
        if (param.reserveStartDatetime != targetRecord.reserveStartDatetime || param.reserveEndDatetime != targetRecord.reserveEndDatetime) {
            // 同一駐車場予約リストを取得
            val existReservations =
                reservationRepository.findActiveReservations(
                    targetRecord.buildingCode,
                    targetRecord.parkingLotCode
                )

            // 予約期間変更の場合は他の予約期間と重複していないかをチェック(追加予約がない場合はそのまま変更可能)
            for (reserved in existReservations) {
                if (param.id.value == reserved.id.value) {
                    // 今更新しようと思ってる予約は重複チェック対象外
                    continue
                }
                validatePeriod(param.reserveStartDatetime!!, param.reserveEndDatetime, reserved)
            }
        }
        return targetRecord
    }

    private fun validateCancel(param: CancelParkingReservation): ParkingReservationInfo {
        // 削除対象レコードを取得
        val targetRecord = reservationRepository.findById(param.id)
            ?: throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_NOT_FOUND.format())

        if (targetRecord.status !in validReservationStatusList) {
            // 対象が完了orキャンセルは取消不可
            throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_CANCEL_INVALID_STATUS.format())
        }
        return targetRecord
    }

    private fun validateCancelApplication(param: CancelApplicationParkingReservation): ParkingReservationInfo {

        // 予約種別が「申込」以外は取消不可
        if (!applicationTypeList.contains(param.reservationType)) {
            throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_INVALID_TYPE.format())
        }

        // 削除対象レコードを取得
        val targetRecord =
            reservationRepository.findActiveApplication(param.parkingLotId)
                ?: throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_NOT_FOUND.format())

        if (targetRecord.status !in validReservationStatusList) {
            // 対象が完了orキャンセルは取消不可
            throw ServerValidationException(ErrorMessage.PARKING_RESERVATION_CANCEL_INVALID_STATUS.format())
        }

        return targetRecord
    }

    private fun validatePeriod(
        start: LocalDateTime,
        end: LocalDateTime?,
        parkingReservationInfo: ParkingReservationInfo
    ) {
        val existStart = parkingReservationInfo.reserveStartDatetime
        val existEnd = parkingReservationInfo.reserveEndDatetime
        val e = ServerValidationException(ErrorMessage.PARKING_RESERVATION_DATE_CONFLICTED.format())

        // 更新時のみユースケースあり
        // 申込み予約後に場所変更予約をし、申請予約の時間変更をするケース等は制約なし
        if (existStart == null && existEnd == null) {
            return
        }

        // 開始日時のみが存在しない予約は、業務上発生し得ないので、この分岐に入ることはない
        // そのため、ここでは、単純な期間の重複チェックのみを行う
        if (existStart == null) {
            if (!start.isAfter(existEnd)) {
                throw e
            }
            return
        }

        // 終了日時のみが存在しない予約（=「申込み」）が存在する場合
        if (existEnd == null) {
            // 追加で「申込み」はできないため、この分岐入ることはない
            // 上記記載のチェックは、当該処理の前段でチェックされている想定だが、挙動としては想定外なので、Exceptionをスローする
            if (end == null) {
                throw e
            }
            if (!end.isBefore(existStart)) {
                // 他の予約開始期間後を予約終了日時としようとした場合
                throw e
            }
            return
        }
        // 開始日時/終了日時が設定されている予約（=「作業」）が存在する場合
        if (existStart.isBefore(start)) {
            // 既存予約の開始日時が新規・更新対象の開始日時以前の場合、既存予約の終了日時と新規・更新対象の開始日時を比較
            if (!existEnd.isBefore(start)) {
                throw e
            }
        } else if (existStart.isAfter(start)) {
            if (end == null) {
                throw e
            }
            // 既存予約の開始日時が新規・更新対象の開始日時以降の場合、既存予約の開始日時と新規・更新対象の終了日時を比較
            if (!existStart.isAfter(end)) {
                throw e
            }
        } else {
            // 開始日時がそもそも重なっている
            throw e
        }
    }

    /**
     * 駐車場予約のうち、完了へ更新対象となる駐車場予約を取得する。
     * 完了へ更新対象となるのは、駐車場の状態が確定または入居中の場合。
     */
    fun getActiveReservationsShouldFinish(): List<FinishParkingReservation> {
        return reservationRepository.findActiveReservationsForBatch().mapNotNull { reservation ->
            reservation.getParkingLotId()?.let { parkingLotId ->
                // 駐車場情報を取得
                parkingDetailsService.getParkingLot(parkingLotId)?.let { parkingLot ->
                    // 駐車場の状態が確定または入居中の場合、完了へ更新対象とする
                    when (parkingLot.parkingStatusDivision) {
                        in doneStatusList -> FinishParkingReservation.of(reservation.id)
                        else -> null
                    }
                }
            }
        }
    }

    fun finishReservations(targetReservations: List<FinishParkingReservation>): Int {
        if (targetReservations.isEmpty()) return 0
        // 駐車場予約を完了へ更新
        var updatedCount = 0
        targetReservations.forEach { param ->
            updateReservation(AuthInfo.Batch(), param).also { updatedCount++ }
        }
        return updatedCount
    }
}
