-- TABLE: TEMPORARY_CONTRACT(仮契約書)

CREATE TABLE TEMPORARY_CONTRACT(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATION_PROGRAM_ID                          varchar(10)                   
,    CREATION_TERMINAL_ID                         varchar(8)                    
,    CREATION_RESPONSIBLE_CD                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATE_TERMINAL_ID                           varchar(8)                    
,    UPDATE_RESPONSIBLE_CD                        varchar(6)                    
,    LOGICAL_DELETE_FLAG                          varchar(1)                    
,    BUILDING_CD                                  varchar(9)                    
,    EFFECTIVE_START_DATE                         numeric(8,0)                  
,    EFFECTIVE_END_DATE                           numeric(8,0)                  
,    CONTRACT_TYPE                                varchar(5)                    
,    DATA_MANAGEMENT_NO                           numeric(3,0)                  
,    CONCLUSION_CATEGORY                          varchar(2)                    
,    MANAGEMENT_CONTRACT_START_DATE               numeric(8,0)                  
,    MANAGEMENT_CONTRACT_END_DATE                 numeric(8,0)                  
,    CONTRACT_OUTPUT_MANAGEMENT_NO                numeric(4,0)                  
,    CONFIRM_PROOF_OUTPUT_DATE                    numeric(8,0)                  
,    CONTRACT_COLLECTION_INPUT_DATE               numeric(8,0)                  
,    CONTRACT_APPROVAL_CATEGORY                   varchar(1)                    
,    CONTRACT_APPROVER                            numeric(6,0)                  
,    CONTRACT_APPROVAL_DATE                       numeric(8,0)                  
,    CONTRACT_OUTPUT_DATE                         numeric(8,0)                  
,    CONTRACT_CONCLUSION_CATEGORY                 numeric(1,0)                  
,    CONCLUSION_INPUT_DATE                        numeric(8,0)                  
,    MANAGEMENT_CONTRACT_EXPECTED_DATE            numeric(8,0)                  
,    MANAGEMENT_CONTRACT_DATE                     numeric(8,0)                  
,    HEAD_OFFICE_APPLICATION_CATEGORY             varchar(1)                    
,    HEAD_OFFICE_RECEPTION_CATEGORY               varchar(1)                    
,    HEAD_OFFICE_RECEPTION_DATE                   numeric(8,0)                  
,    NON_STANDARD_APPROVAL_DATE                   numeric(8,0)                  
,    NON_STANDARD_APPROVER                        varchar(6)                    
,    NON_STANDARD_APPLICATION                     varchar(1)                    
,    AGREEMENT_TERMINATION_DATE                   numeric(8,0)                  
,    AGREEMENT_TERMINATION_DATE2                  numeric(8,0)                  
,    AGREEMENT_TERMINATION_REASON                 varchar(2)                    
,    NAME_CHANGE_PROGRESS_NO                      numeric(6,0)                  
,    NAME_CHANGE_REASON_CATEGORY                  varchar(2)                    
,    NOTIFICATION_NO                              numeric(6,0)                  
,    AGREEMENT_REGISTRATION_NO                    varchar(9)                    
,    OLD_AGREEMENT_NO                             varchar(9)                    
,    MANAGEMENT_DELEGATOR_CD                      varchar(9)                    
,    MANAGEMENT_DELEGATOR_NAME                    varchar(42)                   
,    DELEGATOR_POSTAL_CODE                        varchar(8)                    
,    DELEGATOR_ADDRESS_CD1                        varchar(2)                    
,    DELEGATOR_ADDRESS_CD2                        varchar(2)                    
,    DELEGATOR_ADDRESS_CD3                        varchar(6)                    
,    DELEGATOR_ADDRESS_DETAIL                     varchar(62)                   
,    DELEGATOR_BUILDING_NAME                      varchar(42)                   
,    DELEGATOR_PHONE_NO                           varchar(15)                   
,    OWNER_TAX_CATEGORY                           varchar(1)                    
,    CONSTRUCTION_CATEGORY                        varchar(1)                    
,    BUILDING_TYPE                                varchar(3)                    
,    ROOM_PURPOSE1_BUSINESS                       varchar(1)                    
,    ROOM_PURPOSE2_RESIDENTIAL                    varchar(1)                    
,    ROOM_PURPOSE3_PARKING                        varchar(1)                    
,    ROOM_PURPOSE4_TR                             varchar(1)                    
,    ROOM_PURPOSE5_OTHER                          varchar(1)                    
,    LOAN_CATEGORY                                varchar(1)                    
,    TOTAL_UNITS_BUSINESS                         numeric(3,0)                  
,    TOTAL_UNITS_RESIDENTIAL                      numeric(3,0)                  
,    TOTAL_PARKING_UNITS                          numeric(3,0)                  
,    MANAGED_UNITS_BUSINESS                       numeric(3,0)                  
,    MANAGED_UNITS_RESIDENTIAL                    numeric(3,0)                  
,    MANAGED_PARKING_UNITS                        numeric(3,0)                  
,    CONTRACT_FORM                                varchar(1)                    
,    MANAGEMENT_CATEGORY                          varchar(1)                    
,    MANAGEMENT_PARTNERSHIP                       varchar(2)                    
,    PARTNERSHIP_TYPE                             varchar(2)                    
,    MANAGEMENT_PARTNER                           varchar(10)                   
,    MAINTENANCE_PARTNER                          varchar(10)                   
,    DETAILS_ISSUE                                varchar(2)                    
,    MANAGEMENT_FORM_CATEGORY                     varchar(2)                    
,    SPECIAL_RENT                                 varchar(1)                    
,    COM_COMMUNITY_PARTNERSHIP                    varchar(1)                    
,    PRO_RATA_DAYS                                numeric(2,0)                  
,    EXCLUDED_PRO_RATA_DAYS                       numeric(2,0)                  
,    NON_STANDARD_FLAG                            varchar(1)                    
,    NEW_OWNER_NAME_AT_NAME_CHANGE                varchar(28)                   
,    OWNER_ADDRESS_AT_NAME_CHANGE                 varchar(62)                   
,    OWNER_TRANSFER_ACCOUNT_OWNER_CD              varchar(10)                   
,    DETAILS_CONSOLIDATION_UNIT                   varchar(2)                    
,    TRANSFER_ACCOUNT_DIVISION                    varchar(1)                    
,    RECIPIENT_CD                                 varchar(10)                   
,    PREVIOUS_RECIPIENT_CD                        varchar(10)                   
,    RENEWAL_FEE_ACQUISITION                      varchar(2)                    
,    RENEWAL_FEE_ACQUISITION_MONTHS               numeric(2,0)                  
,    RENEWAL_FEE_COMMISSION_RATE                  numeric(3,0)                  
,    GUARANTOR_NOT_REQUIRED_APPROVAL_FLAG         varchar(1)                    
,    FIXED_TERM_RENTAL_CONTRACT_CONCLUSION        varchar(2)                    
,    WATER_FEE_MANAGEMENT_FEE_COLLECTION          varchar(2)                    
,    WATER_METER_CATEGORY                         varchar(1)                    
,    GAS_CATEGORY                                 varchar(1)                    
,    SHARED_OWNERSHIP_CD1                         varchar(9)                    
,    DELEGATOR_SHARED_INTEREST1                   numeric(4,1)                  
,    SHARED_OWNERSHIP_CD2                         varchar(9)                    
,    DELEGATOR_SHARED_INTEREST2                   numeric(4,1)                  
,    SHARED_OWNERSHIP_CD3                         varchar(9)                    
,    DELEGATOR_SHARED_INTEREST3                   numeric(4,1)                  
,    WATER_READING_CATEGORY                       varchar(2)                    
,    WATER_FEE_COLLECTION                         varchar(2)                    
,    DEPOSIT_HANDLING_CATEGORY                    varchar(1)                    
,    COMMUNITY_FEE_MANAGEMENT_COLLECTION          varchar(2)                    
,    COMMUNITY_FEE_MANAGEMENT_PAYMENT             varchar(2)                    
,    GAS_READING_CATEGORY                         varchar(2)                    
,    PETS_ALLOWED_CATEGORY                        varchar(2)                    
,    RENT_TRANSFER_ACCOUNT_CATEGORY               varchar(2)                    
,    MONTHLY_RENT_TRANSFER_ACCOUNT                varchar(10)                   
,    COMMON_FEE_TRANSFER_ACCOUNT                  varchar(2)                    
,    COMMON_FEE_TRANSFER_ACCOUNT_CD               varchar(10)                   
,    EXCLUSIVE_BROKERAGE_PERIOD                   varchar(2)                    
,    MUTUAL_AID_ASSOCIATION_ENROLLMENT            varchar(1)                    
,    DEPOSIT_SETTLEMENT_METHOD                    varchar(2)                    
,    DEPOSIT_MANAGEMENT_DELEGATOR_RATE            numeric(3,0)                  
,    DEPARTURE_MANAGEMENT_SERVICE_TERMINATION     varchar(2)                    
,    BUILDING_INSPECTION_SERVICE_SITE_PATROL      varchar(2)                    
,    VACANT_ROOM_MANAGEMENT_SERVICE               varchar(2)                    
,    WATER_FEE_READING                            varchar(2)                    
,    COMMON_EQUIPMENT_MAINTENANCE                 varchar(2)                    
,    OTHER_MAINTENANCE                            varchar(2)                    
,    MAINTENANCE_FEE_COLLECTION_TARGET            varchar(2)                    
,    MAINTENANCE_FEE_UNIT                         varchar(2)                    
,    MAINTENANCE_FEE_TOTAL                        numeric(9,0)                  
,    MAINTENANCE_FEE_DAITO_SHARE                  numeric(9,0)                  
,    TENANT_SETTLEMENT_SERVICE                    varchar(2)                    
,    TENANT_SETTLEMENT_MNG_FEE_DEDUCTION          varchar(2)                    
,    DEPARTURE_SETTLEMENT_SERVICE                 varchar(2)                    
,    SALES_DEPART_ACHIEVE_ACCOUNTING_CATEGORY     varchar(2)                    
,    MOVE_IN_OUT_MANAGEMENT                       varchar(2)                    
,    MNG_DEPARTMENT_LEASE_CONTRACT_INPUT          varchar(2)                    
,    CONTRACT_CATEGORY                            varchar(2)                    
,    SITE_AREA                                    numeric(7,2)                  
,    OTHER_PURPOSES                               varchar(1)                    
,    OTHER_PURPOSES_CONTENT                       varchar(22)                   
,    SPECIAL_CONTRACT_NO                          varchar(8)                    
,    ADDITIONAL_TASK1                             varchar(22)                   
,    ADDITIONAL_TASK2                             varchar(22)                   
,    MANAGEMENT_BRANCH_PHONE_NO                   varchar(12)                   
,    MANAGEMENT_CONTRACT_TR_INITIAL               numeric(9,0)                  
,    MANAGEMENT_CONTRACT_TR_NEXT                  numeric(9,0)                  
,    SPECIAL_CLAUSES_INCLUDED                     varchar(1)                    
,    MAINTENANCE_ITEMS_INCLUDED                   varchar(1)                    
,    MANAGEMENT_DELEGATION_DATA_INCLUDED          varchar(1)                    
,    MNG_DELEGATION_CONTENT_DATA_INCLUDED         varchar(1)                    
,    AGREEMENT_NO_CHANGE_MANAGEMENT_NO            numeric(3,0)                  
,    TRANSFER_DATE                                numeric(6,0)                  
,    RECORD_NEW_OLD_CATEGORY                      varchar(1)                    
,    INITIAL_SETUP_FLAG                           varchar(1)                    
,    CONTRACT_END_DATE                            numeric(8,0)                  
,    OPERATION_START_DATE                         numeric(8,0)                  
,    MAINTENANCE_SERVICE_CATEGORY                 varchar(1)                    
,    SIMULTANEOUS_CONTRACT_OUTPUT                 varchar(2)                    
,    OUTPUT_CONTROL_CATEGORY                      varchar(1)                    
,    MANAGEMENT_START_EXPECTED_DATE               numeric(8,0)                  
,    AUTO_CREATION_CATEGORY                       varchar(1)                    
,    MAINTENANCE_DELEGATION_CREATION_CATEGORY     varchar(1)                    
,    PREVIOUS_CONCLUSION_DATE                     numeric(8,0)                  
,    MAINTE_DELEGATION_ONLY_UPDATE_CATEGORY       varchar(1)                    
,    MAINTE_DELEGATION_CONTRACT_OUTPUT_TARGET     varchar(1)                    
,    MAINTE_CONCLUSION_EXPECTED_DATE              numeric(8,0)                  
,    MAINTE_CONCLUSION_DATE                       numeric(8,0)                  
,    CONCLUSION_EXPECTED_DATE_REQUIRED            varchar(1)                    
,    HOUSE_COM_STORE_CD                           varchar(3)                    
,    MNG_START_DATE_CHANGE_CONTRACT_CHANGE        varchar(1)                    
,    BUSINESS_NEW_GUARANTEE_CATEGORY              varchar(1)                    
,    REPAIR_SPECIAL_CLAUSE                        varchar(1)                    
,    REPAIR_SPECIAL_CLAUSE_PERIOD                 numeric(2,0)                  
,    INPUT_MANAGEMENT_NO                          numeric(3,0)                  
,    NON_LEASE_USE_RESIDENTIAL                    varchar(1)                    
,    NON_LEASE_USE_BUSINESS                       varchar(1)                    
,    NON_LEASE_USE_PARKING                        varchar(1)                    
,    BUILDING_STRUCTURE                           varchar(2)                    
,    BUILDING_FLOORS                              numeric(2,0)                  
,    BUILDING_ADDRESS_CD1                         varchar(2)                    
,    BUILDING_ADDRESS_CD2                         varchar(2)                    
,    BUILDING_ADDRESS_CD3                         varchar(6)                    
,    BUILDING_ADDRESS_DETAIL                      varchar(62)                   
,    METER_COUNT                                  numeric(3,0)                  
,    WATER_USAGE_INCLUDED                         varchar(1)                    
,    WATER_USAGE_MONTHLY                          numeric(7,0)                  
,    MAINTENANCE_ITEM_B_INCLUDED                  varchar(1)                    
,    MAINTENANCE_ITEM_B_MONTHLY                   numeric(7,0)                  
,    MAINTENANCE_ITEM_A_INCLUDED                  varchar(1)                    
,    MAINTENANCE_ITEM_A_MONTHLY                   numeric(7,0)                  
,    RENTAL_ADJUSTMENT_INCLUDED                   varchar(1)                    
,    RENTAL_ADJUSTMENT_MONTHLY                    numeric(7,0)                  
,    MAINTENANCE_FEE_ADJUSTMENT_INCLUDED          varchar(1)                    
,    MAINTENANCE_FEE_ADJUSTMENT_MONTHLY           numeric(7,0)                  
,    REPAIR_FEE_ADJUSTMENT_INCLUDED               varchar(1)                    
,    REPAIR_FEE_ADJUSTMENT_MONTHLY                numeric(7,0)                  
,    COMMUNITY_FEE_ADJUSTMENT_INCLUDED            varchar(1)                    
,    COMMUNITY_FEE_ADJUSTMENT_MONTHLY             numeric(7,0)                  
,    CATV_ADJUSTMENT_INCLUDED                     varchar(1)                    
,    CATV_ADJUSTMENT_MONTHLY                      numeric(7,0)                  
,    OTHER_ADJUSTMENT_DESCRIPTION                 varchar(12)                   
,    OTHER_ADJUSTMENT_INCLUDED                    varchar(1)                    
,    OTHER_ADJUSTMENT_MONTHLY                     numeric(7,0)                  
,    BUSINESS_NON_LEASE_ADJUSTMENT_INCLUDED       varchar(1)                    
,    BUSINESS_NON_LEASE_ADJUSTMENT_MONTHLY        numeric(7,0)                  
,    ABOVE_DESCRIPTION                            varchar(16)                   
,    ABOVE_INCLUDED                               varchar(1)                    
,    ABOVE_MONTHLY                                numeric(7,0)                  
,    ABOVE_ADJUSTMENT_CATEGORY                    varchar(1)                    
,    SUBLEASE_RENTAL_ASSESSMENT_TOTAL             numeric(9,0)                  
,    RENTAL_ADJUSTMENT_AMOUNT                     numeric(9,0)                  
,    LEASE_RATE                                   numeric(5,2)                  
,    LEASE_RENTAL                                 numeric(9,0)                  
,    ADJUSTMENT_AMOUNT                            numeric(9,0)                  
,    ADJUSTMENT_CATEGORY                          varchar(1)                    
,    LEASE_PAYMENT_RENTAL                         numeric(9,0)                  
,    CONSUMPTION_TAX                              numeric(9,0)                  
,    CONTRACT_BRANCH_CD                           varchar(6)                    
,    PARKING_ADDRESS_CD1                          varchar(2)                    
,    PARKING_ADDRESS_CD2                          varchar(2)                    
,    PARKING_ADDRESS_CD3                          varchar(6)                    
,    PARKING_ADDRESS_DETAIL                       varchar(62)                   
,    LEASE_ASSESSMENT_PARKING_SPACES              numeric(3,0)                  
,    LEASE_NON_ASSESSMENT_PARKING_SPACES          numeric(3,0)                  
,    LEASED_RESIDENTIAL_UNITS                     numeric(3,0)                  
,    LEASED_BUSINESS_UNITS                        numeric(3,0)                  
,    OTHER_ITEM1                                  varchar(3)                    
,    OTHER_ADJUSTMENT1                            numeric(7,0)                  
,    OTHER_ITEM2                                  varchar(3)                    
,    OTHER_ADJUSTMENT2                            numeric(7,0)                  
,    OTHER_ITEM3                                  varchar(3)                    
,    OTHER_ADJUSTMENT3                            numeric(7,0)                  
,    OTHER_ITEM4                                  varchar(3)                    
,    OTHER_ADJUSTMENT4                            numeric(7,0)                  
,    OTHER_ITEM5                                  varchar(3)                    
,    OTHER_ADJUSTMENT5                            numeric(7,0)                  
,    BULK_SWITCH_SIGN                             varchar(1)                    
,    SWITCH_PAYMENT_METHOD                        numeric(1,0)                  
,    DAITO_BULK_ROOM_PARKING_DB_UPDATE_DATE       numeric(8,0)                  
,    SWITCH_TYPE                                  varchar(1)                    
,    EXPIRATION_PAYMENT_METHOD                    numeric(1,0)                  
,    NON_JOINED_ROOMS_KYOSAI                      numeric(3,0)                  
,    JOINED_ROOMS_KYOSAI                          numeric(3,0)                  
,    MANAGEMENT_ONLY_CONTRACT_OUTPUT              numeric(1,0)                  
,    MAINTENANCE_SHORTFALL                        numeric(9,0)                  
,    MANAGEMENT_FEE_RATE                          numeric(5,2)                  
,    RESERVE                                      numeric(9,0)                  
,    EXPIRATION_TYPE                              varchar(1)                    
,    NON_LEASE_USE_TR                             varchar(1)                    
,    RENT_REVISION_PROCESSING_CATEGORY            varchar(2)                    
,    CHANGE_CONTRACT_TARGET_CATEGORY              varchar(1)                    
,    CONFIRMATION_OUTPUT_TARGET_CATEGORY          varchar(1)                    
,    RECEPTION_CATEGORY                           varchar(1)                    
,    METER_COUNT2                                 numeric(3,0)                  
,    WATER_USAGE_INCLUDED2                        varchar(1)                    
,    WATER_USAGE_MONTHLY2                         numeric(7,0)                  
,    MAINTENANCE_ITEM_B_INCLUDED2                 varchar(1)                    
,    MAINTENANCE_ITEM_B_MONTHLY2                  numeric(7,0)                  
,    MAINTENANCE_ITEM_A_INCLUDED2                 varchar(1)                    
,    MAINTENANCE_ITEM_A_MONTHLY2                  numeric(7,0)                  
,    RENT_ADJUSTMENT_INCLUDED                     varchar(1)                    
,    RENT_ADJUSTMENT_MONTHLY                      numeric(7,0)                  
,    MAINTENANCE_FEE_ADJUSTMENT_INCLUDED2         varchar(1)                    
,    MAINTENANCE_FEE_ADJUSTMENT_MONTHLY2          numeric(7,0)                  
,    REPAIR_FEE_ADJUSTMENT_INCLUDED2              varchar(1)                    
,    REPAIR_FEE_ADJUSTMENT_MONTHLY2               numeric(7,0)                  
,    COMMUNITY_FEE_ADJUSTMENT_INCLUDED2           varchar(1)                    
,    COMMUNITY_FEE_ADJUSTMENT_MONTHLY2            numeric(7,0)                  
,    CATV_ADJUSTMENT_INCLUDED2                    varchar(1)                    
,    CATV_ADJUSTMENT_MONTHLY2                     numeric(7,0)                  
,    OTHER_ADJUSTMENT_DESCRIPTION2                varchar(12)                   
,    OTHER_ADJUSTMENT_INCLUDED2                   varchar(1)                    
,    OTHER_ADJUSTMENT_MONTHLY2                    numeric(7,0)                  
,    BUSINESS_NON_LEASE_ADJUSTMENT_INCLUDED2      varchar(1)                    
,    BUSINESS_NON_LEASE_ADJUSTMENT_MONTHLY2       numeric(7,0)                  
,    ABOVE_DESCRIPTION2                           varchar(16)                   
,    ABOVE_INCLUDED2                              varchar(1)                    
,    ABOVE_MONTHLY2                               numeric(7,0)                  
,    ABOVE_ADJUSTMENT_CATEGORY2                   varchar(1)                    
,    SUBLEASE_RENT_ASSESSMENT_TOTAL               numeric(9,0)                  
,    RENT_ADJUSTMENT_AMOUNT                       numeric(9,0)                  
,    LEASE_RATE2                                  numeric(5,2)                  
,    LEASE_RENTAL2                                numeric(9,0)                  
,    ADJUSTMENT_AMOUNT2                           numeric(9,0)                  
,    ADJUSTMENT_CATEGORY2                         varchar(1)                    
,    LEASE_PAYMENT_RENTAL2                        numeric(9,0)                  
,    CONSUMPTION_TAX2                             numeric(9,0)                  
,    MAINTENANCE_SHORTFALL2                       numeric(9,0)                  
,    MANAGEMENT_FEE_RATE2                         numeric(5,2)                  
,    PRE_REVISION_MINIMUM_PARKING_FEE             numeric(7,0)                  
,    PRE_REVISION_PARKING_COUNT                   numeric(3,0)                  
,    POST_REVISION_MINIMUM_PARKING_FEE            numeric(7,0)                  
,    POST_REVISION_PARKING_COUNT                  numeric(3,0)                  
,    SUBLEASE_RATE_UNDER_10                       numeric(5,2)                  
,    SUBLEASE_RATE_UNDER_20                       numeric(5,2)                  
,    SUBLEASE_RATE_20_AND_ABOVE                   numeric(5,2)                  
,    VACANCY_RATE_UNDER_10                        numeric(5,2)                  
,    VACANCY_RATE_UNDER_20                        numeric(5,2)                  
,    VACANCY_RATE_20_AND_ABOVE                    numeric(5,2)                  
,    MAINTENANCE_REQUIRED_AMOUNT                  numeric(9,0)                  
,    LEASE_ROOM_MAINTENANCE_FEE                   numeric(9,0)                  
,    MANAGED_ROOM_MAINTENANCE_FEE                 numeric(9,0)                  
,    OWNER_DELAY_ROOM_MAINTENANCE_FEE             numeric(9,0)                  
,    MAINTENANCE_SHORTFALL3                       numeric(9,0)                  
,    GENERAL_APPLICATION_NO1                      varchar(6)                    
,    GENERAL_APPLICATION_NO2                      numeric(2,0)                  
,    GENERAL_APPLICATION_NO3                      numeric(4,0)                  
,    RENT_PREPAYMENT_CONFIRMATION_CATEGORY        varchar(1)                    
,    CHECK_SHEET_CONFIRMATION                     varchar(1)                    
,    CONTENT_CONFIRMATION_DATE                    numeric(8,0)                  
,    MAINTENANCE_CONTENT_CONFIRMATION_DATE        numeric(8,0)                  
,    CONSUMPTION_TAX_CALCULATION_BASE_DATE        numeric(8,0)                  
,    CONSUMPTION_TAX_RATE                         numeric(4,2)                  
,    CONSUMPTION_TAX_REVISION_FLAG                numeric(1,0)                  
,    PARKING_TOTAL_SPACES                         numeric(3,0)                  
,    LEASE_SPACES                                 numeric(3,0)                  
,    NON_LEASE_SPACES                             numeric(3,0)                  
,    SUBLEASE_PARKING_FEE_TOTAL                   numeric(9,0)                  
,    PARKING_FEE_ADJUSTMENT_AMOUNT                numeric(9,0)                  
,    STANDARD_PARKING_FEE                         numeric(9,0)                  
,    LEASE_ASSESSMENT_PARKING_FEE                 numeric(9,0)                  
,    LEASE_RATE3                                  numeric(5,2)                  
,    LEASE_PARKING_FEE                            numeric(9,0)                  
,    INCLUDED_CONSUMPTION_TAX                     numeric(9,0)                  
,    RENT_INCREASE_CATEGORY                       varchar(1)                    
,    NOTE_CODE                                    varchar(2)                    
,    CONSTRAINT UQ_TEMPORARY_CONTRACT UNIQUE (BUILDING_CD, EFFECTIVE_START_DATE, EFFECTIVE_END_DATE, CONTRACT_TYPE, CONTRACT_OUTPUT_MANAGEMENT_NO, LOGICAL_DELETE_FLAG)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE TEMPORARY_CONTRACT IS '仮契約書 既存システム物理名: HCC35P';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CREATION_DATE IS '作成年月日 既存システム物理名: HCC01D 管理委託契約締結 予定日';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CREATION_TIME IS '作成時間 既存システム物理名: HCC02H 管理委託契約書締 日';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CREATION_PROGRAM_ID IS '作成プログラムID 既存システム物理名: HCC03M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CREATION_TERMINAL_ID IS '作成端末ID 既存システム物理名: HCC04M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CREATION_RESPONSIBLE_CD IS '作成担当者CD 既存システム物理名: HCC05M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.UPDATE_DATE IS '更新年月日 既存システム物理名: HCC06D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.UPDATE_TIME IS '更新時間 既存システム物理名: HCC07H';
COMMENT ON COLUMN TEMPORARY_CONTRACT.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: HCC08M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.UPDATE_TERMINAL_ID IS '更新端末ID 既存システム物理名: HCC09M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.UPDATE_RESPONSIBLE_CD IS '更新担当者CD 既存システム物理名: HCC10M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LOGICAL_DELETE_FLAG IS '論理削除サイン 既存システム物理名: HCC11S';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUILDING_CD IS '建物CD 既存システム物理名: HCC12C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.EFFECTIVE_START_DATE IS '効力発生日 既存システム物理名: HCC13D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.EFFECTIVE_END_DATE IS '効力終了日 既存システム物理名: HCC14D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTRACT_TYPE IS '契約書タイプ 既存システム物理名: HCC15A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DATA_MANAGEMENT_NO IS 'データ管理No 既存システム物理名: HCC16N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONCLUSION_CATEGORY IS '締結区分 既存システム物理名: HCC17B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_CONTRACT_START_DATE IS '管理契約開始日 既存システム物理名: HCC18D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_CONTRACT_END_DATE IS '管理契約終了日 既存システム物理名: HCC19D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTRACT_OUTPUT_MANAGEMENT_NO IS '契約書出力管理No 既存システム物理名: HCC20N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONFIRM_PROOF_OUTPUT_DATE IS '確認プルーフ出力日 既存システム物理名: HCC22D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTRACT_COLLECTION_INPUT_DATE IS '契約書回収入力日 既存システム物理名: HCC23D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTRACT_APPROVAL_CATEGORY IS '契約書内容承認区分 既存システム物理名: HCC24B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTRACT_APPROVER IS '契約内容承認者 既存システム物理名: HCC25C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTRACT_APPROVAL_DATE IS '契約内容承認日 既存システム物理名: HCC26D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTRACT_OUTPUT_DATE IS '契約書出力日 既存システム物理名: HCC27D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTRACT_CONCLUSION_CATEGORY IS '契約書締結区分 既存システム物理名: HCC28B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONCLUSION_INPUT_DATE IS '締結入力日 既存システム物理名: HCC29D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_CONTRACT_EXPECTED_DATE IS '管理委託契約締結予定日 既存システム物理名: HCC30D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_CONTRACT_DATE IS '管理委託契約書締日 既存システム物理名: HCC31D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.HEAD_OFFICE_APPLICATION_CATEGORY IS '本社申請区分 既存システム物理名: HCC32B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.HEAD_OFFICE_RECEPTION_CATEGORY IS '本社受付区分 既存システム物理名: HCC33B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.HEAD_OFFICE_RECEPTION_DATE IS '本社受付日 既存システム物理名: HCC34D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NON_STANDARD_APPROVAL_DATE IS '定形外承認日 既存システム物理名: HCC38D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NON_STANDARD_APPROVER IS '定形外承認者 既存システム物理名: HCC39C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NON_STANDARD_APPLICATION IS '定型外申請 既存システム物理名: HCC47B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.AGREEMENT_TERMINATION_DATE IS '合意解約締結日 既存システム物理名: HCC40D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.AGREEMENT_TERMINATION_DATE2 IS '合意解約日 既存システム物理名: HCC41D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.AGREEMENT_TERMINATION_REASON IS '合意解約理由 既存システム物理名: HCC42B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NAME_CHANGE_PROGRESS_NO IS '名変進捗No 既存システム物理名: HCC43N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NAME_CHANGE_REASON_CATEGORY IS '名変理由区分 既存システム物理名: HCC44B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NOTIFICATION_NO IS '届出書No 既存システム物理名: HCC45N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.AGREEMENT_REGISTRATION_NO IS '協定書登録番号 既存システム物理名: HCC48N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OLD_AGREEMENT_NO IS '旧協定書番号 既存システム物理名: HCC49N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_DELEGATOR_CD IS '管理委託者CD 既存システム物理名: HCC50C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_DELEGATOR_NAME IS '管理委託者名 既存システム物理名: HCCE0M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DELEGATOR_POSTAL_CODE IS '委託者郵便番号 既存システム物理名: HCCE1N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DELEGATOR_ADDRESS_CD1 IS '委託者住所CD1 既存システム物理名: HCCE2C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DELEGATOR_ADDRESS_CD2 IS '委託者住所CD2 既存システム物理名: HCCE3C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DELEGATOR_ADDRESS_CD3 IS '委託者住所CD3 既存システム物理名: HCCE4C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DELEGATOR_ADDRESS_DETAIL IS '委託者住所詳細 既存システム物理名: HCCE5A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DELEGATOR_BUILDING_NAME IS '委託者ビル名称 既存システム物理名: HCCE6M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DELEGATOR_PHONE_NO IS '委託者電話番号 既存システム物理名: HCCE7C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OWNER_TAX_CATEGORY IS '家主課税区分 既存システム物理名: HCC51B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONSTRUCTION_CATEGORY IS '施工区分 既存システム物理名: HCC52B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUILDING_TYPE IS '建物種別 既存システム物理名: HCC53B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ROOM_PURPOSE1_BUSINESS IS '部屋用途1(事業用) 既存システム物理名: HCCE8C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ROOM_PURPOSE2_RESIDENTIAL IS '部屋用途2(居住用) 既存システム物理名: HCCE9C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ROOM_PURPOSE3_PARKING IS '部屋用途3(駐車場) 既存システム物理名: HCCF0C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ROOM_PURPOSE4_TR IS '部屋用途4(TR) 既存システム物理名: HCCF1C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ROOM_PURPOSE5_OTHER IS '部屋用途5(その他) 既存システム物理名: HCCF2C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LOAN_CATEGORY IS '融資区分 既存システム物理名: HCC54B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.TOTAL_UNITS_BUSINESS IS '総戸数(事業用) 既存システム物理名: HCC55A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.TOTAL_UNITS_RESIDENTIAL IS '総戸数(居住用) 既存システム物理名: HCC56A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.TOTAL_PARKING_UNITS IS '総駐車場数 既存システム物理名: HCCC0A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGED_UNITS_BUSINESS IS '管理部屋数(事業用) 既存システム物理名: HCC57A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGED_UNITS_RESIDENTIAL IS '管理部屋数(居住用) 既存システム物理名: HCC58A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGED_PARKING_UNITS IS '管理駐車場数 既存システム物理名: HCCC1A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTRACT_FORM IS '契約形態 既存システム物理名: HCC59B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_CATEGORY IS '管理区分 既存システム物理名: HCC60B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_PARTNERSHIP IS '管理提携有無 既存システム物理名: HCC61B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PARTNERSHIP_TYPE IS '提携タイプ 既存システム物理名: HCC62B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_PARTNER IS '管理提携業者 既存システム物理名: HCC63C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_PARTNER IS '保守管理業者 既存システム物理名: HCCF3C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DETAILS_ISSUE IS '明細書発行有無 既存システム物理名: HCC64B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_FORM_CATEGORY IS '管理形態区分 既存システム物理名: HCC65B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SPECIAL_RENT IS '特優賃 既存システム物理名: HCC66B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.COM_COMMUNITY_PARTNERSHIP IS 'コム提携 既存システム物理名: HCC67B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PRO_RATA_DAYS IS '日割日数 既存システム物理名: HCC68A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.EXCLUDED_PRO_RATA_DAYS IS '日割除外日数 既存システム物理名: HCC69A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NON_STANDARD_FLAG IS '基準外サイン 既存システム物理名: HCC70B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NEW_OWNER_NAME_AT_NAME_CHANGE IS '名義変更時新家主名 既存システム物理名: HCC71M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OWNER_ADDRESS_AT_NAME_CHANGE IS '名義変更時家主住所 既存システム物理名: HCCF6M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OWNER_TRANSFER_ACCOUNT_OWNER_CD IS '家主振込口座(家主CD) 既存システム物理名: HCC72C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DETAILS_CONSOLIDATION_UNIT IS '明細書名寄せ単位 既存システム物理名: HCC73B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.TRANSFER_ACCOUNT_DIVISION IS '振込口座細分化 既存システム物理名: HCC74B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RECIPIENT_CD IS '送付先CD 既存システム物理名: HCC75C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PREVIOUS_RECIPIENT_CD IS '変更前送付先CD 既存システム物理名: HCCC9C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENEWAL_FEE_ACQUISITION IS '更新料取得可否 既存システム物理名: HCC76B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENEWAL_FEE_ACQUISITION_MONTHS IS '更新料取得月数 既存システム物理名: HCC77A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENEWAL_FEE_COMMISSION_RATE IS '更新手数料取得割合 既存システム物理名: HCC78A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.GUARANTOR_NOT_REQUIRED_APPROVAL_FLAG IS '保証人不要制度承認サイン 既存システム物理名: HCC79B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.FIXED_TERM_RENTAL_CONTRACT_CONCLUSION IS '定期借家契約締結可否 既存システム物理名: HCC80B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.WATER_FEE_MANAGEMENT_FEE_COLLECTION IS '水道料管理料徴収 既存システム物理名: HCC81B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.WATER_METER_CATEGORY IS '水道メータ区分 既存システム物理名: HCC82B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.GAS_CATEGORY IS 'ガス区分 既存システム物理名: HCC83B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SHARED_OWNERSHIP_CD1 IS '共有名義CD1 既存システム物理名: HCC84C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DELEGATOR_SHARED_INTEREST1 IS '委託契約者共有持ち分1 既存システム物理名: HCC85A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SHARED_OWNERSHIP_CD2 IS '共有名義CD2 既存システム物理名: HCC86C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DELEGATOR_SHARED_INTEREST2 IS '委託契約者共有持ち分2 既存システム物理名: HCC87A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SHARED_OWNERSHIP_CD3 IS '共有名義CD3 既存システム物理名: HCC88C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DELEGATOR_SHARED_INTEREST3 IS '委託契約者共有持ち分3 既存システム物理名: HCC89A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.WATER_READING_CATEGORY IS '水道検針区分 既存システム物理名: HCC90B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.WATER_FEE_COLLECTION IS '水道料回収 既存システム物理名: HCC91B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DEPOSIT_HANDLING_CATEGORY IS '敷金預かり区分 既存システム物理名: HCC92B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.COMMUNITY_FEE_MANAGEMENT_COLLECTION IS '自治会費管理回収業務 既存システム物理名: HCC93B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.COMMUNITY_FEE_MANAGEMENT_PAYMENT IS '自治会費管理支払代行業務 既存システム物理名: HCC94B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.GAS_READING_CATEGORY IS 'ガス検針区分 既存システム物理名: HCCD0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PETS_ALLOWED_CATEGORY IS 'ペット可否区分 既存システム物理名: HCC95B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENT_TRANSFER_ACCOUNT_CATEGORY IS '家賃等振込先区分 既存システム物理名: HCC96B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MONTHLY_RENT_TRANSFER_ACCOUNT IS '例月家賃振込先 既存システム物理名: HCC97C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.COMMON_FEE_TRANSFER_ACCOUNT IS '共益費振込先 既存システム物理名: HCC98B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.COMMON_FEE_TRANSFER_ACCOUNT_CD IS '共益費振込先CD 既存システム物理名: HCC99C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.EXCLUSIVE_BROKERAGE_PERIOD IS '斡旋専任期間 既存システム物理名: HCCA0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MUTUAL_AID_ASSOCIATION_ENROLLMENT IS '共済会加入 既存システム物理名: HCCA1B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DEPOSIT_SETTLEMENT_METHOD IS '敷金精算方法 既存システム物理名: HCCA2B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DEPOSIT_MANAGEMENT_DELEGATOR_RATE IS '敷金管理委託者割合 既存システム物理名: HCCA3A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DEPARTURE_MANAGEMENT_SERVICE_TERMINATION IS '退居管理業務(解約) 既存システム物理名: HCCA4B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUILDING_INSPECTION_SERVICE_SITE_PATROL IS '建物点検業務(敷地巡回) 既存システム物理名: HCCA5B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.VACANT_ROOM_MANAGEMENT_SERVICE IS '空室管理業務 既存システム物理名: HCCA6B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.WATER_FEE_READING IS '水道料の検針 既存システム物理名: HCCA7B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.COMMON_EQUIPMENT_MAINTENANCE IS '共用設備の維持管理 既存システム物理名: HCCA8B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_MAINTENANCE IS 'その他維持 既存システム物理名: HCCA9B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_FEE_COLLECTION_TARGET IS '維持費回収対象 既存システム物理名: HCCB0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_FEE_UNIT IS '維持費単位 既存システム物理名: HCCB1B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_FEE_TOTAL IS '維持費(総額) 既存システム物理名: HCCB2A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_FEE_DAITO_SHARE IS '維持費(大東取得分) 既存システム物理名: HCCB3A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.TENANT_SETTLEMENT_SERVICE IS '入居精算業務有無 既存システム物理名: HCCC2B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.TENANT_SETTLEMENT_MNG_FEE_DEDUCTION IS '入居精算時管理費 控除有無 既存システム物理名: HCCC3B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DEPARTURE_SETTLEMENT_SERVICE IS '退居精算業務有無 既存システム物理名: HCCC4B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SALES_DEPART_ACHIEVE_ACCOUNTING_CATEGORY IS 'テ営課実績計上区分 既存システム物理名: HCCC5B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MOVE_IN_OUT_MANAGEMENT IS '入退居管理有無 既存システム物理名: HCCC6B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MNG_DEPARTMENT_LEASE_CONTRACT_INPUT IS '管理課賃貸借契約 入力可否 既存システム物理名: HCCC7B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTRACT_CATEGORY IS '契約区分 既存システム物理名: HCCD1B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SITE_AREA IS '敷地面積 既存システム物理名: HCCD2A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_PURPOSES IS 'その他用途有無 既存システム物理名: HCCD3B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_PURPOSES_CONTENT IS 'その他用途内容 既存システム物理名: HCCD4A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SPECIAL_CONTRACT_NO IS 'テ契No 既存システム物理名: HCCD5N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ADDITIONAL_TASK1 IS '追加業務1 既存システム物理名: HCCD6A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ADDITIONAL_TASK2 IS '追加業務2 既存システム物理名: HCCD7A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_BRANCH_PHONE_NO IS '管理支店電話番号 既存システム物理名: HCCD8N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_CONTRACT_TR_INITIAL IS '管理契約時金TR(初回のみ) 既存システム物理名: HCCF4N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_CONTRACT_TR_NEXT IS '管理契約時金TR(次回以降) 既存システム物理名: HCCF5N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SPECIAL_CLAUSES_INCLUDED IS '特約記載有無 既存システム物理名: HCCB4B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_ITEMS_INCLUDED IS '維持管理項目有無 既存システム物理名: HCCB5B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_DELEGATION_DATA_INCLUDED IS '管理委託分担データ有無 既存システム物理名: HCCB6B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MNG_DELEGATION_CONTENT_DATA_INCLUDED IS '管理委託内容データ有無 既存システム物理名: HCCB7B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.AGREEMENT_NO_CHANGE_MANAGEMENT_NO IS '協定書番号変更管理No 既存システム物理名: HCCC8N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.TRANSFER_DATE IS '譲受年月 既存システム物理名: HCCD9D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RECORD_NEW_OLD_CATEGORY IS 'レコード新旧区分 既存システム物理名: HCCB9B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.INITIAL_SETUP_FLAG IS '初回セットアップサイン 既存システム物理名: HCCB8B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTRACT_END_DATE IS '契約終了日 既存システム物理名: HCCF7D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OPERATION_START_DATE IS '運用開始日 既存システム物理名: HCCF8D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_SERVICE_CATEGORY IS '維持管理業務区分 既存システム物理名: HCCF9B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SIMULTANEOUS_CONTRACT_OUTPUT IS '同時出力契約書 既存システム物理名: HCCG0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OUTPUT_CONTROL_CATEGORY IS '出力制御区分 既存システム物理名: HCCG1B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_START_EXPECTED_DATE IS '管理開始予定日 既存システム物理名: HCCG2D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.AUTO_CREATION_CATEGORY IS '自動作成区分 既存システム物理名: HCCG3B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_DELEGATION_CREATION_CATEGORY IS '維持管理委託 作成区分 既存システム物理名: HCCG4B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PREVIOUS_CONCLUSION_DATE IS '前回締結日 既存システム物理名: HCCG5D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTE_DELEGATION_ONLY_UPDATE_CATEGORY IS '維持管理委託のみ 更新区分 既存システム物理名: HCCG6B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTE_DELEGATION_CONTRACT_OUTPUT_TARGET IS '維持管理委託契約 出力対象 既存システム物理名: HCCG7B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTE_CONCLUSION_EXPECTED_DATE IS '維持管理締結予定日 既存システム物理名: HCCG8D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTE_CONCLUSION_DATE IS '維持管理締結日 既存システム物理名: HCCG9D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONCLUSION_EXPECTED_DATE_REQUIRED IS '締結予定日必須入力 既存システム物理名: HCCH0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.HOUSE_COM_STORE_CD IS 'ハウスコム店舗CD 既存システム物理名: HCCH1C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MNG_START_DATE_CHANGE_CONTRACT_CHANGE IS '管理開始日変更＆契約内容変更 既存システム物理名: HCCH2B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUSINESS_NEW_GUARANTEE_CATEGORY IS '事業用新保証区分 既存システム物理名: HCCH3B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.REPAIR_SPECIAL_CLAUSE IS '修繕特約 既存システム物理名: HCCH4B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.REPAIR_SPECIAL_CLAUSE_PERIOD IS '修繕特約期間 既存システム物理名: HCCH5B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.INPUT_MANAGEMENT_NO IS '入力管理No 既存システム物理名: HCCH6N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NON_LEASE_USE_RESIDENTIAL IS '非借上用途 居住用 既存システム物理名: HCCH7B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NON_LEASE_USE_BUSINESS IS '非借上用途 事業用 既存システム物理名: HCCH8B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NON_LEASE_USE_PARKING IS '非借上用途 駐車場 既存システム物理名: HCCH9B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUILDING_STRUCTURE IS '建物構造 既存システム物理名: HCCI0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUILDING_FLOORS IS '建物階数 既存システム物理名: HCCI2A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUILDING_ADDRESS_CD1 IS '建物住所CD1 既存システム物理名: HCCI3C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUILDING_ADDRESS_CD2 IS '建物住所CD2 既存システム物理名: HCCI4C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUILDING_ADDRESS_CD3 IS '建物住所CD3 既存システム物理名: HCCI5C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUILDING_ADDRESS_DETAIL IS '建物住所詳細 既存システム物理名: HCCI6M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.METER_COUNT IS '検針メータ数 既存システム物理名: HCCI7A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.WATER_USAGE_INCLUDED IS '水道使用量 有無 既存システム物理名: HCCI8B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.WATER_USAGE_MONTHLY IS '水道使用量 月額 既存システム物理名: HCCI9A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_ITEM_B_INCLUDED IS '乙維持管理項目有無 既存システム物理名: HCCJ0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_ITEM_B_MONTHLY IS '乙維持管理項目月額 既存システム物理名: HCCJ1A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_ITEM_A_INCLUDED IS '甲維持管理項目有無 既存システム物理名: HCCJ2B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_ITEM_A_MONTHLY IS '甲維持管理項目月額 既存システム物理名: HCCJ3A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENTAL_ADJUSTMENT_INCLUDED IS '家賃調整金 有無 既存システム物理名: HCCJ4B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENTAL_ADJUSTMENT_MONTHLY IS '家賃調整金 月額 既存システム物理名: HCCJ5A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_FEE_ADJUSTMENT_INCLUDED IS '維持費調整金 有無 既存システム物理名: HCCJ6B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_FEE_ADJUSTMENT_MONTHLY IS '維持費調整金 月額 既存システム物理名: HCCJ7A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.REPAIR_FEE_ADJUSTMENT_INCLUDED IS '修繕費調整金 有無 既存システム物理名: HCCJ8B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.REPAIR_FEE_ADJUSTMENT_MONTHLY IS '修繕費調整金 月額 既存システム物理名: HCCJ9A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.COMMUNITY_FEE_ADJUSTMENT_INCLUDED IS '自治会費調整金有無 既存システム物理名: HCCK0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.COMMUNITY_FEE_ADJUSTMENT_MONTHLY IS '自治会費調整金月額 既存システム物理名: HCCK1A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CATV_ADJUSTMENT_INCLUDED IS 'CATV調整金有無 既存システム物理名: HCCK2B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CATV_ADJUSTMENT_MONTHLY IS 'CATV調整金月額 既存システム物理名: HCCK3A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ADJUSTMENT_DESCRIPTION IS 'その他調整金 文言 既存システム物理名: HCCK4M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ADJUSTMENT_INCLUDED IS 'その他調整金 有無 既存システム物理名: HCCK5B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ADJUSTMENT_MONTHLY IS 'その他調整金 月額 既存システム物理名: HCCK6A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUSINESS_NON_LEASE_ADJUSTMENT_INCLUDED IS '事 非借上調整有無 既存システム物理名: HCCK7B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUSINESS_NON_LEASE_ADJUSTMENT_MONTHLY IS '事 非借上調整月額 既存システム物理名: HCCK8A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ABOVE_DESCRIPTION IS '上記以外 文言 既存システム物理名: HCCK9M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ABOVE_INCLUDED IS '上記以外 有無 既存システム物理名: HCCL0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ABOVE_MONTHLY IS '上記以外 月額 既存システム物理名: HCCL1A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ABOVE_ADJUSTMENT_CATEGORY IS '上記以外 加減区分 既存システム物理名: HCCL2B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SUBLEASE_RENTAL_ASSESSMENT_TOTAL IS '転貸賃料査定総額 既存システム物理名: HCCL3A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENTAL_ADJUSTMENT_AMOUNT IS '家賃調整額 既存システム物理名: HCCL4A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_RATE IS '借上率 既存システム物理名: HCCL5A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_RENTAL IS '借上賃料 既存システム物理名: HCCL6A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ADJUSTMENT_AMOUNT IS '調整額 既存システム物理名: HCCL7A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ADJUSTMENT_CATEGORY IS '加減区分 既存システム物理名: HCCL8B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_PAYMENT_RENTAL IS '借上支払賃料 既存システム物理名: HCCL9A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONSUMPTION_TAX IS '消費税等 既存システム物理名: HCCI1B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTRACT_BRANCH_CD IS '契約支店CD 既存システム物理名: HCCN8C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PARKING_ADDRESS_CD1 IS '駐車場住所CD1 既存システム物理名: HCCM0C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PARKING_ADDRESS_CD2 IS '駐車場住所CD2 既存システム物理名: HCCM1C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PARKING_ADDRESS_CD3 IS '駐車場住所CD3 既存システム物理名: HCCM2C';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PARKING_ADDRESS_DETAIL IS '駐車場住所詳細 既存システム物理名: HCCM3M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_ASSESSMENT_PARKING_SPACES IS '借上査定駐車場台数 既存システム物理名: HCCM4A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_NON_ASSESSMENT_PARKING_SPACES IS '借上査定外駐車場 台数 既存システム物理名: HCCM5A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASED_RESIDENTIAL_UNITS IS '借上部屋戸数 居住用 既存システム物理名: HCCM6A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASED_BUSINESS_UNITS IS '借上部屋戸数 事業用 既存システム物理名: HCCM7A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ITEM1 IS 'その他項目1 既存システム物理名: HCCM8B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ADJUSTMENT1 IS 'その他調整金1 既存システム物理名: HCCM9A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ITEM2 IS 'その他項目2 既存システム物理名: HCCN0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ADJUSTMENT2 IS 'その他調整金2 既存システム物理名: HCCN1A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ITEM3 IS 'その他項目3 既存システム物理名: HCCN2B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ADJUSTMENT3 IS 'その他調整金3 既存システム物理名: HCCN3A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ITEM4 IS 'その他項目4 既存システム物理名: HCCN4B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ADJUSTMENT4 IS 'その他調整金4 既存システム物理名: HCCN5A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ITEM5 IS 'その他項目5 既存システム物理名: HCCN6B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ADJUSTMENT5 IS 'その他調整金5 既存システム物理名: HCCN7A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BULK_SWITCH_SIGN IS '一括切替サイン 既存システム物理名: HCCN9B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SWITCH_PAYMENT_METHOD IS '切替時支払方法 既存システム物理名: HCCO1A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.DAITO_BULK_ROOM_PARKING_DB_UPDATE_DATE IS '大東一括部屋・駐車場DB更新日付 既存システム物理名: HCCO2B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SWITCH_TYPE IS '切替時タイプ 既存システム物理名: HCCO3A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.EXPIRATION_PAYMENT_METHOD IS '満了時支払方法 既存システム物理名: HCCO4A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NON_JOINED_ROOMS_KYOSAI IS '共済会非加入部屋数 既存システム物理名: HCCO5A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.JOINED_ROOMS_KYOSAI IS '共済会加入部屋数 既存システム物理名: HCCO6A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_ONLY_CONTRACT_OUTPUT IS '管理のみ契約書出力 既存システム物理名: HCCO7A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_SHORTFALL IS '維持管理不足額 既存システム物理名: HCCO8B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_FEE_RATE IS '管理費相当料率 既存システム物理名: HCCO9A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RESERVE IS '予備 既存システム物理名: HCCP0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.EXPIRATION_TYPE IS '満了時タイプ 既存システム物理名: HCCP1B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NON_LEASE_USE_TR IS '非借上用途TR 既存システム物理名: HCCP2B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENT_REVISION_PROCESSING_CATEGORY IS '賃料改定時処理区分 既存システム物理名: HCCP3B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CHANGE_CONTRACT_TARGET_CATEGORY IS '変更契約対象区分 既存システム物理名: HCCP4B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONFIRMATION_OUTPUT_TARGET_CATEGORY IS 'ご確認書出力対象 区分 既存システム物理名: HCCP5B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RECEPTION_CATEGORY IS '受付区分 既存システム物理名: HCCP6B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.METER_COUNT2 IS '検針メータ数2 既存システム物理名: HCCP7B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.WATER_USAGE_INCLUDED2 IS '水道使用量 有無2 既存システム物理名: HCCP8B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.WATER_USAGE_MONTHLY2 IS '水道使用量 月額2 既存システム物理名: HCCP9A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_ITEM_B_INCLUDED2 IS '乙維持管理項目有無2 既存システム物理名: HCCQ0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_ITEM_B_MONTHLY2 IS '乙維持管理項目月額2 既存システム物理名: HCCQ1A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_ITEM_A_INCLUDED2 IS '甲維持管理項目有無2 既存システム物理名: HCCQ2B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_ITEM_A_MONTHLY2 IS '甲維持管理項目月額2 既存システム物理名: HCCQ3A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENT_ADJUSTMENT_INCLUDED IS '家賃調整金 有無 既存システム物理名: HCCQ4B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENT_ADJUSTMENT_MONTHLY IS '家賃調整金 月額 既存システム物理名: HCCQ5A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_FEE_ADJUSTMENT_INCLUDED2 IS '維持費調整金 有無2 既存システム物理名: HCCQ6B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_FEE_ADJUSTMENT_MONTHLY2 IS '維持費調整金 月額2 既存システム物理名: HCCQ7A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.REPAIR_FEE_ADJUSTMENT_INCLUDED2 IS '修繕費調整金 有無2 既存システム物理名: HCCQ8B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.REPAIR_FEE_ADJUSTMENT_MONTHLY2 IS '修繕費調整金 月額2 既存システム物理名: HCCQ9A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.COMMUNITY_FEE_ADJUSTMENT_INCLUDED2 IS '自治会費調整金有無2 既存システム物理名: HCCR0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.COMMUNITY_FEE_ADJUSTMENT_MONTHLY2 IS '自治会費調整金月額2 既存システム物理名: HCCR1A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CATV_ADJUSTMENT_INCLUDED2 IS 'CATV調整金有無2 既存システム物理名: HCCR2B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CATV_ADJUSTMENT_MONTHLY2 IS 'CATV調整金月額2 既存システム物理名: HCCR3A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ADJUSTMENT_DESCRIPTION2 IS 'その他調整金 文言2 既存システム物理名: HCCR4M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ADJUSTMENT_INCLUDED2 IS 'その他調整金 有無2 既存システム物理名: HCCR5B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OTHER_ADJUSTMENT_MONTHLY2 IS 'その他調整金 月額2 既存システム物理名: HCCR6A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUSINESS_NON_LEASE_ADJUSTMENT_INCLUDED2 IS '事 非借上調整有無2 既存システム物理名: HCCR7B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.BUSINESS_NON_LEASE_ADJUSTMENT_MONTHLY2 IS '事 非借上調整月額2 既存システム物理名: HCCR8A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ABOVE_DESCRIPTION2 IS '上記以外 文言2 既存システム物理名: HCCR9M';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ABOVE_INCLUDED2 IS '上記以外 有無2 既存システム物理名: HCCS0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ABOVE_MONTHLY2 IS '上記以外 月額2 既存システム物理名: HCCS1A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ABOVE_ADJUSTMENT_CATEGORY2 IS '上記以外 加減区分2 既存システム物理名: HCCS2B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SUBLEASE_RENT_ASSESSMENT_TOTAL IS '転貸賃料査定総額 既存システム物理名: HCCS3A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENT_ADJUSTMENT_AMOUNT IS '家賃調整額 既存システム物理名: HCCS4A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_RATE2 IS '借上率2 既存システム物理名: HCCS5A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_RENTAL2 IS '借上賃料2 既存システム物理名: HCCS6A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ADJUSTMENT_AMOUNT2 IS '調整額2 既存システム物理名: HCCS7A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.ADJUSTMENT_CATEGORY2 IS '加減区分2 既存システム物理名: HCCS8B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_PAYMENT_RENTAL2 IS '借上支払賃料2 既存システム物理名: HCCS9A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONSUMPTION_TAX2 IS '消費税等2 既存システム物理名: HCCT1B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_SHORTFALL2 IS '維持管理不足額2 既存システム物理名: HCCT8B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGEMENT_FEE_RATE2 IS '管理費相当料率2 既存システム物理名: HCCT9B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PRE_REVISION_MINIMUM_PARKING_FEE IS '改定前最低駐車料 既存システム物理名: HCCT2A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PRE_REVISION_PARKING_COUNT IS '改定前駐車台数 既存システム物理名: HCCT3A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.POST_REVISION_MINIMUM_PARKING_FEE IS '改定後最低駐車料 既存システム物理名: HCCT4A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.POST_REVISION_PARKING_COUNT IS '改定後駐車台数 既存システム物理名: HCCT5A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SUBLEASE_RATE_UNDER_10 IS '転貸中借上料率 10未満 既存システム物理名: HCCU0A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SUBLEASE_RATE_UNDER_20 IS '転貸中借上料率 20未満 既存システム物理名: HCCU1A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SUBLEASE_RATE_20_AND_ABOVE IS '転貸中借上料率 20以上 既存システム物理名: HCCU2A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.VACANCY_RATE_UNDER_10 IS '空室中借上料率 10未満 既存システム物理名: HCCU3A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.VACANCY_RATE_UNDER_20 IS '空室中借上料率 20未満 既存システム物理名: HCCU4A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.VACANCY_RATE_20_AND_ABOVE IS '空室中借上料率 20以上 既存システム物理名: HCCU5A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_REQUIRED_AMOUNT IS '維持費必要額 既存システム物理名: HCCU6A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_ROOM_MAINTENANCE_FEE IS '借上部屋維持費 既存システム物理名: HCCU7A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MANAGED_ROOM_MAINTENANCE_FEE IS '管理部屋維持費 既存システム物理名: HCCU8A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.OWNER_DELAY_ROOM_MAINTENANCE_FEE IS '施主遅れ部屋維持費 既存システム物理名: HCCU9A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_SHORTFALL3 IS '維持費不足額3 既存システム物理名: HCCV0A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.GENERAL_APPLICATION_NO1 IS '一般申請No1 既存システム物理名: HCCV1N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.GENERAL_APPLICATION_NO2 IS '一般申請No2 既存システム物理名: HCCV2N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.GENERAL_APPLICATION_NO3 IS '一般申請No3 既存システム物理名: HCCV3N';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENT_PREPAYMENT_CONFIRMATION_CATEGORY IS '賃料前払確認区分 既存システム物理名: HCCV4B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CHECK_SHEET_CONFIRMATION IS 'チェックシート確認 既存システム物理名: HCCV5B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONTENT_CONFIRMATION_DATE IS '内容確認日 既存システム物理名: HCCV6D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.MAINTENANCE_CONTENT_CONFIRMATION_DATE IS '維持管理内容確認日 既存システム物理名: HCCV7D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONSUMPTION_TAX_CALCULATION_BASE_DATE IS '消費税計算基準日 既存システム物理名: HCCV8D';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONSUMPTION_TAX_RATE IS '消費税率 既存システム物理名: HCCV9X';
COMMENT ON COLUMN TEMPORARY_CONTRACT.CONSUMPTION_TAX_REVISION_FLAG IS '消費税改正洗替区分 既存システム物理名: HCCW0B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PARKING_TOTAL_SPACES IS '駐車場総区画数 既存システム物理名: HCCW1S';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_SPACES IS '借上区画数 既存システム物理名: HCCW2S';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NON_LEASE_SPACES IS '非借上区画数 既存システム物理名: HCCW3S';
COMMENT ON COLUMN TEMPORARY_CONTRACT.SUBLEASE_PARKING_FEE_TOTAL IS '転貸駐車料総額 既存システム物理名: HCCW4A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.PARKING_FEE_ADJUSTMENT_AMOUNT IS '駐車料調整額 既存システム物理名: HCCW5A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.STANDARD_PARKING_FEE IS '基準駐車料 既存システム物理名: HCCW6A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_ASSESSMENT_PARKING_FEE IS '借上査定駐車料 既存システム物理名: HCCW7A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_RATE3 IS '借上料率3 既存システム物理名: HCCW8R';
COMMENT ON COLUMN TEMPORARY_CONTRACT.LEASE_PARKING_FEE IS '借上駐車料 既存システム物理名: HCCW9A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.INCLUDED_CONSUMPTION_TAX IS '内消費税 既存システム物理名: HCCX0A';
COMMENT ON COLUMN TEMPORARY_CONTRACT.RENT_INCREASE_CATEGORY IS '家賃上乗せ区分 既存システム物理名: HCCX1B';
COMMENT ON COLUMN TEMPORARY_CONTRACT.NOTE_CODE IS '注意コード 既存システム物理名: HCCX2C';
