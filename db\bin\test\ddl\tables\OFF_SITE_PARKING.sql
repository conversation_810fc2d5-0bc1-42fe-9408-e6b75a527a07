-- TABLE: OFF_SITE_PARKING(敷地外駐車場)

CREATE TABLE OFF_SITE_PARKING(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1)                    
,    ORDER_CD                                     varchar(7)                    
,    PARKING_CD                                   varchar(3)                    
,    OFF_SITE_LOCATION_PREF_CD                    varchar(2)                    
,    OFF_SITE_LOCATION_CITY_CD                    varchar(2)                    
,    OFF_SITE_LOCATION_TOWN_CD                    varchar(6)                    
,    OFF_SITE_LOCATION_DETAIL                     varchar(62)                   
,    RESIDENCE_DISPLAY_TYPE                       varchar(1)                    
,    RESIDENCE_LOCATION_PREF_CD                   varchar(2)                    
,    RESIDENCE_LOCATION_CITY_CD                   varchar(2)                    
,    RESIDENCE_LOCATION_TOWN_CD                   varchar(6)                    
,    RESIDENCE_LOCATION_DETAIL                    varchar(62)                   
,    LATITUDE_WGS                                 numeric(9)                    
,    LONGITUDE_WGS                                numeric(9)                    
,    DISTANCE                                     numeric(4)                    
,    MAP_SCALE                                    numeric(3)                    
,    CONSTRAINT UQ_OFF_SITE_PARKING UNIQUE (ORDER_CD, PARKING_CD, LOGICAL_DELETE_SIGN)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE OFF_SITE_PARKING IS '敷地外駐車場 既存システム物理名: ECC40P';
COMMENT ON COLUMN OFF_SITE_PARKING.CREATION_DATE IS '作成年月日 既存システム物理名: EC401D @290';
COMMENT ON COLUMN OFF_SITE_PARKING.CREATION_TIME IS '作成時刻 既存システム物理名: EC402H @290';
COMMENT ON COLUMN OFF_SITE_PARKING.UPDATE_DATE IS '更新年月日 既存システム物理名: EC403D';
COMMENT ON COLUMN OFF_SITE_PARKING.UPDATE_TIME IS '更新時刻 既存システム物理名: EC404H';
COMMENT ON COLUMN OFF_SITE_PARKING.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EC405N';
COMMENT ON COLUMN OFF_SITE_PARKING.UPDATER IS '更新者 既存システム物理名: EC406C';
COMMENT ON COLUMN OFF_SITE_PARKING.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: EC407S';
COMMENT ON COLUMN OFF_SITE_PARKING.ORDER_CD IS '受注CD 既存システム物理名: EC408C';
COMMENT ON COLUMN OFF_SITE_PARKING.PARKING_CD IS '駐車場CD 既存システム物理名: EC409C';
COMMENT ON COLUMN OFF_SITE_PARKING.OFF_SITE_LOCATION_PREF_CD IS '敷地外所在地CD 都道府県 既存システム物理名: EC410N';
COMMENT ON COLUMN OFF_SITE_PARKING.OFF_SITE_LOCATION_CITY_CD IS '敷地外所在地CD 市区郡 既存システム物理名: EC411N';
COMMENT ON COLUMN OFF_SITE_PARKING.OFF_SITE_LOCATION_TOWN_CD IS '敷地外所在地CD 町村 既存システム物理名: EC412N';
COMMENT ON COLUMN OFF_SITE_PARKING.OFF_SITE_LOCATION_DETAIL IS '敷地外所在地詳細 既存システム物理名: EC413X';
COMMENT ON COLUMN OFF_SITE_PARKING.RESIDENCE_DISPLAY_TYPE IS '住居表示区分 既存システム物理名: EC414B';
COMMENT ON COLUMN OFF_SITE_PARKING.RESIDENCE_LOCATION_PREF_CD IS '住居所在地CD 都道府県 既存システム物理名: EC415N';
COMMENT ON COLUMN OFF_SITE_PARKING.RESIDENCE_LOCATION_CITY_CD IS '住居所在地CD 市区郡 既存システム物理名: EC416N';
COMMENT ON COLUMN OFF_SITE_PARKING.RESIDENCE_LOCATION_TOWN_CD IS '住居所在地CD 町村 既存システム物理名: EC417N';
COMMENT ON COLUMN OFF_SITE_PARKING.RESIDENCE_LOCATION_DETAIL IS '住居所在地詳細 既存システム物理名: EC418X';
COMMENT ON COLUMN OFF_SITE_PARKING.LATITUDE_WGS IS '緯度(世界測地系) 既存システム物理名: EC419Q';
COMMENT ON COLUMN OFF_SITE_PARKING.LONGITUDE_WGS IS '経度(世界測地系) 既存システム物理名: EC420Q';
COMMENT ON COLUMN OFF_SITE_PARKING.DISTANCE IS '距離 既存システム物理名: EC421Q';
COMMENT ON COLUMN OFF_SITE_PARKING.MAP_SCALE IS 'MAP縮尺 既存システム物理名: EC422Q';
