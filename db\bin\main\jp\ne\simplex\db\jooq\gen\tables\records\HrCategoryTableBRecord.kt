/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.HrCategoryTableBTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.HrCategoryTableBPojo

import org.jooq.impl.TableRecordImpl


/**
 * 人事区分テーブル(Ｂ) 既存システム物理名: JXE1MP
 */
@Suppress("UNCHECKED_CAST")
open class HrCategoryTableBRecord private constructor() : TableRecordImpl<HrCategoryTableBRecord>(HrCategoryTableBTable.HR_CATEGORY_TABLE_B) {

    open var typeCategory: String?
        set(value): Unit = set(0, value)
        get(): String? = get(0) as String?

    open var code: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    open var kanjiName: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var kanjiAbbreviation_1: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var kanjiAbbreviation_2: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var category_1: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var category_2: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var category_3: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var category_4: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var category_5: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var categoryName_1: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var categoryName_2: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var categoryName_3: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var categoryName_4: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var categoryName_5: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var deleteCategory: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var creator: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var creationProgram: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var creationDate: Int?
        set(value): Unit = set(18, value)
        get(): Int? = get(18) as Int?

    open var creationTime: Int?
        set(value): Unit = set(19, value)
        get(): Int? = get(19) as Int?

    open var updater: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var updateProgram: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var updateDate: Int?
        set(value): Unit = set(22, value)
        get(): Int? = get(22) as Int?

    open var updateTime: Int?
        set(value): Unit = set(23, value)
        get(): Int? = get(23) as Int?

    open var terminalId: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    /**
     * Create a detached, initialised HrCategoryTableBRecord
     */
    constructor(typeCategory: String? = null, code: String? = null, kanjiName: String? = null, kanjiAbbreviation_1: String? = null, kanjiAbbreviation_2: String? = null, category_1: String? = null, category_2: String? = null, category_3: String? = null, category_4: String? = null, category_5: String? = null, categoryName_1: String? = null, categoryName_2: String? = null, categoryName_3: String? = null, categoryName_4: String? = null, categoryName_5: String? = null, deleteCategory: String? = null, creator: String? = null, creationProgram: String? = null, creationDate: Int? = null, creationTime: Int? = null, updater: String? = null, updateProgram: String? = null, updateDate: Int? = null, updateTime: Int? = null, terminalId: String? = null): this() {
        this.typeCategory = typeCategory
        this.code = code
        this.kanjiName = kanjiName
        this.kanjiAbbreviation_1 = kanjiAbbreviation_1
        this.kanjiAbbreviation_2 = kanjiAbbreviation_2
        this.category_1 = category_1
        this.category_2 = category_2
        this.category_3 = category_3
        this.category_4 = category_4
        this.category_5 = category_5
        this.categoryName_1 = categoryName_1
        this.categoryName_2 = categoryName_2
        this.categoryName_3 = categoryName_3
        this.categoryName_4 = categoryName_4
        this.categoryName_5 = categoryName_5
        this.deleteCategory = deleteCategory
        this.creator = creator
        this.creationProgram = creationProgram
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updater = updater
        this.updateProgram = updateProgram
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.terminalId = terminalId
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised HrCategoryTableBRecord
     */
    constructor(value: HrCategoryTableBPojo?): this() {
        if (value != null) {
            this.typeCategory = value.typeCategory
            this.code = value.code
            this.kanjiName = value.kanjiName
            this.kanjiAbbreviation_1 = value.kanjiAbbreviation_1
            this.kanjiAbbreviation_2 = value.kanjiAbbreviation_2
            this.category_1 = value.category_1
            this.category_2 = value.category_2
            this.category_3 = value.category_3
            this.category_4 = value.category_4
            this.category_5 = value.category_5
            this.categoryName_1 = value.categoryName_1
            this.categoryName_2 = value.categoryName_2
            this.categoryName_3 = value.categoryName_3
            this.categoryName_4 = value.categoryName_4
            this.categoryName_5 = value.categoryName_5
            this.deleteCategory = value.deleteCategory
            this.creator = value.creator
            this.creationProgram = value.creationProgram
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updater = value.updater
            this.updateProgram = value.updateProgram
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.terminalId = value.terminalId
            resetChangedOnNotNull()
        }
    }
}
