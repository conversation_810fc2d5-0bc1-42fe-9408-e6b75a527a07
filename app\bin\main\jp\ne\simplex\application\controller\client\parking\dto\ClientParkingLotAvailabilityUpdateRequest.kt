package jp.ne.simplex.application.controller.client.parking.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.UpdateParkingLotAvailability
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException

class ClientParkingLotAvailabilityUpdateRequest(
    @JsonProperty("buildingCode")
    @field:Schema(description = "建物コード", example = "000000001")
    val buildingCode: String,

    @JsonProperty("parkingLotCode")
    @field:Schema(description = "駐車場コード", example = "001")
    val parkingLotCode: String,

    // テーブルのカラム名に合わせるとparkingLotEnableだが、
    // API名と被るのでstatusにする
    @JsonProperty("status")
    @field:Schema(description = "区画利用可否", example = "1")
    val status: String,
) {
    fun toServiceInterface(): UpdateParkingLotAvailability {
        try {
            return UpdateParkingLotAvailability(
                parkingLotId = ParkingLot.Id(
                    Building.Code.of(buildingCode),
                    ParkingLot.Code.of(parkingLotCode)
                ),
                status = ParkingLot.Status.fromValue(status)!!
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }
}
