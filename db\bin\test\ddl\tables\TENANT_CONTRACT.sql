-- TABLE: TENANT_CONTRACT(テナント契約)

CREATE TABLE TENANT_CONTRACT(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1,0)                  
,    TENANT_CONTRACT_NUMBER                       varchar(8)        NOT NULL    
,    TENANT_CONTRACT_CHANGE_SEQ                   varchar(2)        NOT NULL    
,    CONTRACT_CONTENT_DIVISION                    varchar(1)                    
,    BUILDING_CODE                                varchar(9)                    
,    ROOM_CODE                                    varchar(5)                    
,    PARKING_CODE                                 varchar(3)                    
,    AGGREGATE_CONTRACT_NUMBER                    varchar(8)                    
,    AGGREGATE_CONTRACT_CHANGE_SEQ                varchar(2)                    
,    PARKING_SPACES                               numeric(3,0)                  
,    BULK_LEASE_SIGN                              numeric(1,0)                  
,    TENANT_TYPE                                  varchar(1)                    
,    TENANT_PROSPECT_NUMBER                       varchar(8)                    
,    TENANT_CODE                                  varchar(9)                    
,    SEARCH_KANA                                  varchar(25)                   
,    INFO_ACQUISITION_DIVISION                    varchar(2)                    
,    HOT_INFO_RECEIPT_NUMBER                      varchar(7)                    
,    LANDLORD_CODE_10                             varchar(10)                   
,    TAX_DIVISION                                 varchar(1)                    
,    SEARCH_KANA2                                 varchar(25)                   
,    NAME                                         varchar(42)                   
,    PREFECTURE_CODE                              varchar(2)                    
,    CITY_CODE                                    varchar(2)                    
,    TOWN_CODE                                    varchar(6)                    
,    ADDRESS_DETAIL                               varchar(62)                   
,    BUILDING_NAME                                varchar(32)                   
,    PHONE_NUMBER                                 varchar(15)                   
,    IMPORTANT_DISCLOSURE_OUTPUT_COUNT            numeric(2,0)                  
,    IMPORTANT_DISCLOSURE_OUTPUT_DATE             numeric(8,0)                  
,    IMPORTANT_DISCLOSURE_COLLECTION_DATE         numeric(8,0)                  
,    IMPORTANT_DISCLOSURE_DATE                    numeric(8,0)                  
,    IMPORTANT_DISCLOSURE_AGENT_CODE              varchar(6)                    
,    IMPORTANT_DISCLOSURE_DIVISION                varchar(1)                    
,    PAYMENT_DATE                                 numeric(8,0)                  
,    MOVE_IN_APPLICATION_DATE                     numeric(8,0)                  
,    MOVE_IN_SCHEDULED_DATE                       numeric(8,0)                  
,    USAGE_PURPOSE                                varchar(42)                   
,    MOVE_IN_APPLICATION_FEE                      numeric(9,0)                  
,    PAYMENT_METHOD_DIVISION                      varchar(1)                    
,    MOBILE_RECEIPT_NUMBER                        varchar(9)                    
,    RESERVATION_APPLICATION_FEE_APPLIED_AMOUNT   numeric(9,0)                  
,    TENANT_NAME                                  varchar(42)                   
,    CONTACT_DIVISION                             varchar(1)                    
,    CONTACT                                      varchar(62)                   
,    PHONE_NUMBER2                                varchar(15)                   
,    REMARKS                                      varchar(124)                  
,    NAME2                                        varchar(42)                   
,    PREFECTURE_CODE2                             varchar(2)                    
,    CITY_CODE2                                   varchar(2)                    
,    TOWN_CODE2                                   varchar(6)                    
,    ADDRESS_DETAIL2                              varchar(62)                   
,    BUILDING_NAME2                               varchar(32)                   
,    PHONE_NUMBER3                                varchar(15)                   
,    RELATIONSHIP                                 varchar(8)                    
,    NAME3                                        varchar(42)                   
,    PREFECTURE_CODE3                             varchar(2)                    
,    CITY_CODE3                                   varchar(2)                    
,    TOWN_CODE3                                   varchar(6)                    
,    ADDRESS_DETAIL3                              varchar(62)                   
,    BUILDING_NAME3                               varchar(32)                   
,    PHONE_NUMBER4                                varchar(15)                   
,    RELATIONSHIP2                                varchar(8)                    
,    COHABITANT_NAME1                             varchar(42)                   
,    COHABITANT_AGE1                              numeric(3,0)                  
,    COHABITANT_RELATIONSHIP1                     varchar(8)                    
,    COHABITANT_NAME2                             varchar(42)                   
,    COHABITANT_AGE2                              numeric(3,0)                  
,    COHABITANT_RELATIONSHIP2                     varchar(8)                    
,    COHABITANT_NAME3                             varchar(42)                   
,    AGENT_SHOZOKU_BRANCH                         numeric(3,0)                  
,    COHABITANT_RELATIONSHIP3                     varchar(8)                    
,    COHABITANT_NAME4                             varchar(42)                   
,    AGENT_SHOZOKU_STORE                          numeric(3,0)                  
,    COHABITANT_RELATIONSHIP4                     varchar(8)                    
,    COHABITANT_NAME5                             varchar(42)                   
,    AGENT_SHOZOKU_OFFICE                         numeric(3,0)                  
,    COHABITANT_RELATIONSHIP5                     varchar(8)                    
,    LANDLORD_APPROVAL_DATE                       numeric(8,0)                  
,    DEPOSIT_CHANGE_DATE                          numeric(8,0)                  
,    AGENCY_MEMBER_NUMBER                         varchar(12)                   
,    CONTRACT_PERIOD_DIVISION                     varchar(1)                    
,    RESERVATION_CONTRACT_SIGN                    numeric(1,0)                  
,    TENANT_RESERVATION_NUMBER                    varchar(6)                    
,    LEASE_CONTRACT_OUTPUT_CONTENT_DIVISION       varchar(1)                    
,    STANDARD_RENT                                numeric(9,0)                  
,    LEASE_CONTRACT_OUTPUT_COUNT                  numeric(2,0)                  
,    LEASE_CONTRACT_LATEST_OUTPUT_DATE            numeric(8,0)                  
,    LEASE_CONTRACT_COLLECTION_DATE               numeric(8,0)                  
,    LEASE_CONTRACT_DATE                          numeric(8,0)                  
,    CONTRACT_START_DATE                          numeric(8,0)                  
,    CONTRACT_EXPIRY_DATE                         numeric(8,0)                  
,    NEXT_RENT_REVISION_SCHEDULED_DATE            numeric(8,0)                  
,    RENT_REVISION_PERIOD                         numeric(3,0)                  
,    FRONT_FREE_RENT_DAYS                         numeric(3,1)                  
,    DEPOSIT_MONTHS                               numeric(3,1)                  
,    DEPRECIATION_MONTHS                          numeric(3,1)                  
,    KEY_MONEY_AMOUNT                             numeric(9,0)                  
,    KEY_MONEY_DIVISION                           varchar(1)                    
,    ADDITIONAL_KEY_MONEY_AMOUNT                  numeric(9,0)                  
,    DEPOSIT_AMOUNT                               numeric(9,0)                  
,    DEPRECIATION                                 numeric(9,0)                  
,    RENT                                         numeric(9,0)                  
,    RENT_DIVISION                                varchar(1)                    
,    PARKING_FEE_AGGREGATION_SIGN                 numeric(1,0)                  
,    PARKING_FEE                                  numeric(9,0)                  
,    PARKING_FEE_DIVISION                         varchar(1)                    
,    COMMON_SERVICE_FEE                           numeric(9,0)                  
,    COMMON_SERVICE_FEE_DIVISION                  varchar(1)                    
,    NEIGHBORHOOD_ASSOCIATION_FEE                 numeric(9,0)                  
,    ADDITIONAL_DEPOSIT_AMOUNT                    numeric(9,0)                  
,    PAYMENT_SCHEDULE_CREATION_SIGN               numeric(1,0)                  
,    PAYMENT_METHOD_DIVISION2                     varchar(1)                    
,    DIFFERENCE_PRORATED_DAYS                     numeric(3,0)                  
,    DIFFERENCE_PRORATED_RENT_AMOUNT              numeric(9,0)                  
,    DIFFERENCE_PRORATED_PARKING_FEE              numeric(9,0)                  
,    DIFFERENCE_PRORATED_MANAGEMENT_FEE           numeric(9,0)                  
,    DIFFERENCE_PRORATED_COMMON_SERVICE_FEE       numeric(9,0)                  
,    DIFFERENCE_PRORATED_ASSOCIATION_FEE          numeric(9,0)                  
,    DIFFERENCE_PRORATED_WATER_MANAGEMENT_FEE     numeric(9,0)                  
,    DIFFERENCE_MONTHS                            numeric(1,0)                  
,    MONTHLY_DIFFERENCE_RENT_AMOUNT               numeric(9,0)                  
,    MONTHLY_DIFFERENCE_PARKING_FEE               numeric(9,0)                  
,    MONTHLY_DIFFERENCE_COMMON_SERVICE_FEE        numeric(9,0)                  
,    MONTHLY_DIFFERENCE_MANAGEMENT_FEE            numeric(9,0)                  
,    MONTHLY_DIFFERENCE_ASSOCIATION_FEE           numeric(9,0)                  
,    MONTHLY_DIFFERENCE_WATER_MANAGEMENT_FEE      numeric(9,0)                  
,    PAYMENT_SCHEDULE_CREATION_SIGN2              numeric(1,0)                  
,    DIFFERENCE_RENT_COLLECTION_METHOD            varchar(1)                    
,    RENT_COLLECTION_METHOD_DIVISION              varchar(1)                    
,    AGENCY_DIVISION                              varchar(1)                    
,    BANK_CODE                                    varchar(4)                    
,    BANK_BRANCH_CODE                             varchar(3)                    
,    ACCOUNT_TYPE                                 varchar(1)                    
,    ACCOUNT_NUMBER                               varchar(10)                   
,    ACCOUNT_HOLDER_NAME_KANA                     varchar(40)                   
,    ACCOUNT_HOLDER_NAME_KANJI                    varchar(42)                   
,    INITIAL_CHANGE_EFFECTIVE_DATE                numeric(6,0)                  
,    BANK_CODE2                                   varchar(4)                    
,    BANK_BRANCH_CODE2                            varchar(3)                    
,    ACCOUNT_TYPE2                                varchar(1)                    
,    ACCOUNT_NUMBER2                              varchar(10)                   
,    ACCOUNT_HOLDER_NAME_KANA2                    varchar(40)                   
,    ACCOUNT_HOLDER_NAME_KANJI2                   varchar(42)                   
,    DIRECT_DEBIT_TARGET_DIVISION                 varchar(1)                    
,    DIRECT_DEBIT_AMOUNT                          numeric(9,0)                  
,    LEASE_CONTRACT_TRANSACTING_AGENT             varchar(6)                    
,    CONTRACT_EFFECTIVE_START_DATE                numeric(8,0)                  
,    CONTRACT_EFFECTIVE_END_DATE                  numeric(8,0)                  
,    FRONT_FREE_RENT_SIGN                         numeric(1,0)                  
,    FRONT_FREE_RENT_AMOUNT                       numeric(9,0)                  
,    FRONT_FREE_RENT_MONTHS                       numeric(3,0)                  
,    LANDLORD_ADVANCE_PAYMENT_ALLOCATION_AMOUNT   numeric(9,0)                  
,    LANDLORD_ADVANCE_PAYMENT                     numeric(9,0)                  
,    MNG_CONTRACT_INITIAL_PAYMENT_AMOUNT          numeric(9,0)                  
,    MNG_CONTRACT_INITIAL_PAYMENT_AMOUNT_TAX      numeric(9,0)                  
,    ASSOCIATION_ENTRY_FEE_PROCESS                numeric(9,0)                  
,    REGISTRATION_FEE_AMOUNT                      numeric(9,0)                  
,    REGISTRATION_FEE_AMOUNT_TAX                  numeric(9,0)                  
,    MNG_CONTRACT_INITIAL_PAYMENT_TRANSFERRED     numeric(1,0)                  
,    BROKERAGE_FEE_EXEMPTION_DIVISION             varchar(1)                    
,    RENT_MANAGEMENT_START_DATE                   numeric(6,0)                  
,    NOTARIZED_DOC_PAYMENT_SIGN                   numeric(1,0)                  
,    MOVE_IN_CALCULATION_OUTPUT_DATE              numeric(8,0)                  
,    REMAINING_RENT                               numeric(9,0)                  
,    REMAINING_RENT_TAX                           numeric(9,0)                  
,    REMAINING_PARKING_FEE                        numeric(9,0)                  
,    REMAINING_PARKING_FEE_TAX                    numeric(9,0)                  
,    REMAINING_COMMON_SERVICE_FEE                 numeric(9,0)                  
,    REMAINING_COMMON_SERVICE_FEE_TAX             numeric(9,0)                  
,    REMAINING_NEIGHBORHOOD_FEE                   numeric(9,0)                  
,    REMAINING_MONTHS                             numeric(3,0)                  
,    PRORATED_RENT                                numeric(9,0)                  
,    PRORATED_RENT_TAX                            numeric(9,0)                  
,    PRORATED_PARKING                             numeric(9,0)                  
,    PRORATED_PARKING_FEE_TAX                     numeric(9,0)                  
,    PRORATED_COMMON_SERVICE_FEE                  numeric(9,0)                  
,    PRORATED_COMMON_SERVICE_FEE_TAX              numeric(9,0)                  
,    PRORATED_NEIGHBORHOOD_FEE                    numeric(9,0)                  
,    KEY_MONEY_AMOUNT2                            numeric(9,0)                  
,    KEY_MONEY_TAX                                numeric(9,0)                  
,    DEPOSIT_AMOUNT2                              numeric(9,0)                  
,    MONTHLY_MANAGEMENT_FEE                       numeric(9,0)                  
,    NOTARIZED_DOC_CREATION_COST                  numeric(9,0)                  
,    STAMP_FEE                                    numeric(9,0)                  
,    INTERIOR_COOPERATION_FEE                     numeric(9,0)                  
,    INTERIOR_COOPERATION_FEE_TAX                 numeric(9,0)                  
,    INCOME_COMMISSION_FEE_TENANT                 numeric(9,0)                  
,    INCOME_COMMISSION_FEE_TAX                    numeric(9,0)                  
,    DEPOSIT_COMMISSION_FEE                       numeric(9,0)                  
,    DEPOSIT_COMMISSION_FEE_TAX                   numeric(9,0)                  
,    BROKERAGE_FEE                                numeric(9,0)                  
,    BROKERAGE_FEE_TAX                            numeric(9,0)                  
,    OUTSOURCED_ADVERTISING_FEE                   numeric(9,0)                  
,    OUTSOURCED_ADVERTISING_FEE_TAX               numeric(9,0)                  
,    PAYMENT_SIGN                                 numeric(1,0)                  
,    BROKER_CODE                                  varchar(9)                    
,    BROKERAGE_FEE_BREAKDOWN_DIVISION             varchar(1)                    
,    REMAINING_AMOUNT                             numeric(11,0)                 
,    REMAINING_SCHEDULED_DATE                     numeric(8,0)                  
,    REMAINING_DATE                               numeric(8,0)                  
,    REMAINING_APPROVAL_DATE                      numeric(8,0)                  
,    OFFSET_AMOUNT_MANAGEMENT_FEE                 numeric(9,0)                  
,    OFFSET_AMOUNT_MANAGEMENT_FEE_TAX             numeric(9,0)                  
,    OFFSET_AMOUNT_ASSOCIATION_FEE                numeric(9,0)                  
,    OFFSET_AMOUNT_MAINTENANCE_FEE                numeric(9,0)                  
,    OFFSET_AMOUNT_MAINTENANCE_FEE_TAX            numeric(9,0)                  
,    NEIGHBORHOOD_FEE_DAITO_PAYMENT               numeric(9,0)                  
,    OFFSET_AMOUNT_WATER_MANAGEMENT_FEE           numeric(9,0)                  
,    OFFSET_AMOUNT_TAX                            numeric(9,0)                  
,    OFFSET_AMOUNT_PRORATED_MANAGEMENT_FEE        numeric(9,0)                  
,    PRORATED_MANAGEMENT_FEE_TAX                  numeric(9,0)                  
,    OFFSET_AMOUNT_PRORATED_ASSOCIATION           numeric(9,0)                  
,    OFFSET_AMOUNT_PRORATED_MAINTENANCE           numeric(9,0)                  
,    MAINTENANCE_PRORATED_TAX                     numeric(9,0)                  
,    PRORATED_NEIGHBORHOOD_FEE_DAITO              numeric(9,0)                  
,    OFFSET_AMOUNT_PRORATED_WATER_MANAGEMENT      numeric(9,0)                  
,    OFFSET_AMOUNT_TAX2                           numeric(9,0)                  
,    MANAGEMENT_CONTRACT_INITIAL_PAYMENT          numeric(9,0)                  
,    MANAGEMENT_CONTRACT_INITIAL_PAYMENT_TAX      numeric(9,0)                  
,    TENANT_REGISTRATION_FEE                      numeric(9,0)                  
,    TENANT_REGISTRATION_FEE_TAX                  numeric(9,0)                  
,    ASSOCIATION_ENTRY_FEE                        numeric(9,0)                  
,    NOTARIZED_DOC_CREATION_COST2                 numeric(9,0)                  
,    STAMP_FEE2                                   numeric(9,0)                  
,    MOVE_IN_SETTLEMENT_AMOUNT                    numeric(9,0)                  
,    MOVE_IN_SETTLEMENT_DATE                      numeric(8,0)                  
,    TENANT_SETTLEMENT_PAYMENT_SCHEDULE_DIV       varchar(1)                    
,    TENANT_ADDRESS_OVERRIDE_DIVISION             varchar(1)                    
,    POST_MOVE_CONTACT_PHONE                      varchar(15)                   
,    FIRE_INSURANCE_FEE                           numeric(9,0)                  
,    KEY_HANDOVER_DATE                            numeric(8,0)                  
,    RENOVATION_APPLICATION_SIGN                  numeric(1,0)                  
,    MANAGEMENT_FEE_TAX                           numeric(9,0)                  
,    COOPERATION_FEE_OFFSET_MONTHS                numeric(3,0)                  
,    RESERVATION_CONTRACT_FEE_ALLOCATION_AMOUNT   numeric(9,0)                  
,    CANCELLATION_SIGN                            numeric(1,0)                  
,    MOVE_IN_START_PROCESSED_SIGN                 numeric(1,0)                  
,    VACATE_NOTICE_DATE                           numeric(8,0)                  
,    VACATE_SCHEDULED_DATE                        numeric(8,0)                  
,    BREACH_PERIOD_EXPIRY_DATE                    numeric(8,0)                  
,    MOVE_OUT_SETTLEMENT_DATE_LANDLORD            numeric(8,0)                  
,    MOVE_OUT_SETTLEMENT_DATE_TENANT              numeric(8,0)                  
,    ASSOCIATION_BENEFIT_START_DATE               numeric(8,0)                  
,    RESTORATION_COMPLETION_DATE                  numeric(8,0)                  
,    MOVE_OUT_DATE                                numeric(8,0)                  
,    BREACH_YEAR_MONTH                            numeric(6,0)                  
,    BREACH_PERIOD_DAYS                           numeric(3,0)                  
,    BREACH_PERIOD_START_DATE                     numeric(8,0)                  
,    BREACH_PERIOD_END_DATE                       numeric(8,0)                  
,    PREFECTURE_CODE4                             varchar(2)                    
,    CITY_CODE4                                   varchar(2)                    
,    TOWN_CODE4                                   varchar(6)                    
,    ADDRESS_DETAIL4                              varchar(62)                   
,    REMARKS2                                     varchar(62)                   
,    BANK_CODE3                                   varchar(4)                    
,    BANK_BRANCH_CODE3                            varchar(3)                    
,    ACCOUNT_TYPE3                                varchar(1)                    
,    ACCOUNT_NUMBER3                              varchar(10)                   
,    ACCOUNT_HOLDER_NAME_KANA3                    varchar(40)                   
,    ACCOUNT_HOLDER_NAME_KANJI3                   varchar(42)                   
,    ON_SITE_CONFIRMATION_DATE                    numeric(2,0)                  
,    RESTORATION_WORK_EXISTENCE_SIGN              numeric(1,0)                  
,    CONSTRUCTION_DIVISION                        varchar(1)                    
,    ACTUAL_PAYMENT_DIVISION                      varchar(1)                    
,    CONSTRUCTION_ORDER_NUMBER                    varchar(10)                   
,    MAINTENANCE_WORK_SIGN                        numeric(1,0)                  
,    CONSTRUCTION_ORDER_NUMBER2                   varchar(10)                   
,    CONTRACT_DOCUMENT_DIVISION                   varchar(1)                    
,    RENT_ARREARS_MONTHS                          numeric(3,0)                  
,    RENT_ARREARS_AMOUNT                          numeric(9,0)                  
,    DEPOSIT_RETAINED_AMOUNT                      numeric(9,0)                  
,    ADVANCE_RENT_PAYMENT_REQUEST_FINAL           numeric(6,0)                  
,    RENT_BILLING_FINAL_CREATION_YEAR             numeric(6,0)                  
,    COMPANY_CODE                                 varchar(3)                    
,    BRANCH_CODE                                  varchar(6)                    
,    DIRECT_SUPERVISOR_CODE                       varchar(6)                    
,    EMPLOYEE_CODE                                varchar(6)                    
,    CURRENT_RESPONSIBLE_SHOZOKU_CODE             varchar(6)                    
,    CURRENT_RESPONSIBLE_BRANCH_CODE              varchar(6)                    
,    SALES_PERFORMANCE_SHOZOKU_CODE               varchar(6)                    
,    SALES_PERFORMANCE_BRANCH_CODE                varchar(6)                    
,    COMPANY_CODE2                                varchar(3)                    
,    BASE_CODE                                    varchar(6)                    
,    DIRECT_SUPERVISOR_CODE2                      varchar(6)                    
,    EMPLOYEE_CODE2                               varchar(6)                    
,    CUSTOMER_RESPONSIBLE_BRANCH_CODE             varchar(6)                    
,    JOURNAL_ENTRY_SEQ                            numeric(3,0)                  
,    PREVIOUS_STATE_DIVISION                      varchar(2)                    
,    CURRENT_STATE_DIVISION                       varchar(2)                    
,    MODIFICATION_STATE_DIVISION                  varchar(2)                    
,    INTERFACE_SIGN                               numeric(1,0)                  
,    RESPONSE_RECEIPT                             varchar(1)                    
,    SATELLITE_CODE                               varchar(5)                    
,    RESPONSE_RECEIPT_DATE                        numeric(8,0)                  
,    SALES_OFFICE_STAFF                           varchar(6)                    
,    PARKING_AGGREGATION_DIVISION                 varchar(1)                    
,    LEDGER_NO                                    numeric(4,0)                  
,    GUARANTOR_NOT_REQUIRED_DIVISION              numeric(1,0)                  
,    COMMUNICATION_PARTNER_DIVISION               numeric(1,0)                  
,    NON_STANDARD_DIVISION                        numeric(1,0)                  
,    CONTRACT_RENEWAL_IMPLEMENTER                 numeric(1,0)                  
,    CORPORATE_HOUSING_AGENCY_SIGN                numeric(1,0)                  
,    FF_PAYMENT_SIGN                              numeric(1,0)                  
,    RENTAL_DIVISION                              numeric(1,0)                  
,    UNUSED6                                      numeric(1,0)                  
,    UNUSED7                                      numeric(1,0)                  
,    UNUSED8                                      numeric(1,0)                  
,    UNUSED9                                      numeric(1,0)                  
,    CONTRACT_RENT                                numeric(9,0)                  
,    SPECIAL_RENTAL_DIVISION                      varchar(1)                    
,    RESPONSE_RECEIVER                            varchar(6)                    
,    PARKING_CONTRACT_FEE                         numeric(9,0)                  
,    PARKING_CONTRACT_FEE_TAX                     numeric(9,0)                  
,    PARKING_CONTRACT_FEE_EXEMPTION_DIVISION      varchar(1)                    
,    INCOME_PARKING_FEE_TENANT                    numeric(9,0)                  
,    INCOME_PARKING_CONTRACT_FEE_TAX              numeric(9,0)                  
,    DEPOSIT_PARKING_CONTRACT_FEE                 numeric(9,0)                  
,    DEPOSIT_PARKING_CONTRACT_FEE_TAX             numeric(9,0)                  
,    CONSTRAINT PK_TENANT_CONTRACT PRIMARY KEY (TENANT_CONTRACT_NUMBER, TENANT_CONTRACT_CHANGE_SEQ)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE TENANT_CONTRACT IS 'テナント契約 既存システム物理名: ECB20P';
COMMENT ON COLUMN TENANT_CONTRACT.CREATION_DATE IS '作成年月日 既存システム物理名: ECB01D 建託所属の場合所属コードの上3桁、他は0';
COMMENT ON COLUMN TENANT_CONTRACT.CREATION_TIME IS '作成時刻 既存システム物理名: ECB02H';
COMMENT ON COLUMN TENANT_CONTRACT.UPDATE_DATE IS '更新年月日 既存システム物理名: ECB03D';
COMMENT ON COLUMN TENANT_CONTRACT.UPDATE_TIME IS '更新時刻 既存システム物理名: ECB04H';
COMMENT ON COLUMN TENANT_CONTRACT.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: ECB05N';
COMMENT ON COLUMN TENANT_CONTRACT.UPDATER IS '更新者 既存システム物理名: ECB06C';
COMMENT ON COLUMN TENANT_CONTRACT.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: ECBG9S';
COMMENT ON COLUMN TENANT_CONTRACT.TENANT_CONTRACT_NUMBER IS 'テナント契約番号 既存システム物理名: ECBAKN';
COMMENT ON COLUMN TENANT_CONTRACT.TENANT_CONTRACT_CHANGE_SEQ IS 'テナント契約変更連 既存システム物理名: ECB07N';
COMMENT ON COLUMN TENANT_CONTRACT.CONTRACT_CONTENT_DIVISION IS '契約内容区分 既存システム物理名: ECBFTB';
COMMENT ON COLUMN TENANT_CONTRACT.BUILDING_CODE IS '建物コード 既存システム物理名: ECBABC';
COMMENT ON COLUMN TENANT_CONTRACT.ROOM_CODE IS '部屋コード 既存システム物理名: ECBACC';
COMMENT ON COLUMN TENANT_CONTRACT.PARKING_CODE IS '駐車場コード 既存システム物理名: ECBBSC';
COMMENT ON COLUMN TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER IS '合算先契約番号 既存システム物理名: ECBO6N';
COMMENT ON COLUMN TENANT_CONTRACT.AGGREGATE_CONTRACT_CHANGE_SEQ IS '合算先契約変更連番 既存システム物理名: ECBO7N';
COMMENT ON COLUMN TENANT_CONTRACT.PARKING_SPACES IS '駐車場台数 既存システム物理名: ECBO8Q';
COMMENT ON COLUMN TENANT_CONTRACT.BULK_LEASE_SIGN IS '一括借上サイン 既存システム物理名: ECBK4S';
COMMENT ON COLUMN TENANT_CONTRACT.TENANT_TYPE IS '入居者区分 既存システム物理名: ECBCSB';
COMMENT ON COLUMN TENANT_CONTRACT.TENANT_PROSPECT_NUMBER IS 'テナント見込番号 既存システム物理名: ECBBIN';
COMMENT ON COLUMN TENANT_CONTRACT.TENANT_CODE IS 'テナントコード 既存システム物理名: ECBANC';
COMMENT ON COLUMN TENANT_CONTRACT.SEARCH_KANA IS '検索用仮名 既存システム物理名: ECB08M';
COMMENT ON COLUMN TENANT_CONTRACT.INFO_ACQUISITION_DIVISION IS '情報入手区分 既存システム物理名: ECBDVB';
COMMENT ON COLUMN TENANT_CONTRACT.HOT_INFO_RECEIPT_NUMBER IS '耳より情報受付番号 既存システム物理名: ECBBJN';
COMMENT ON COLUMN TENANT_CONTRACT.LANDLORD_CODE_10 IS '家主コード(10) 既存システム物理名: ECBCKC';
COMMENT ON COLUMN TENANT_CONTRACT.TAX_DIVISION IS '課税区分 既存システム物理名: ECBBKB';
COMMENT ON COLUMN TENANT_CONTRACT.SEARCH_KANA2 IS '検索用仮名2 既存システム物理名: ECB09M';
COMMENT ON COLUMN TENANT_CONTRACT.NAME IS '氏名 既存システム物理名: ECBJ5M';
COMMENT ON COLUMN TENANT_CONTRACT.PREFECTURE_CODE IS '都道府県コード 既存システム物理名: ECBJ6C';
COMMENT ON COLUMN TENANT_CONTRACT.CITY_CODE IS '市区郡コード 既存システム物理名: ECBJ7C';
COMMENT ON COLUMN TENANT_CONTRACT.TOWN_CODE IS '町村字通称コード 既存システム物理名: ECBJ8C';
COMMENT ON COLUMN TENANT_CONTRACT.ADDRESS_DETAIL IS '住所詳細 既存システム物理名: ECBJ9X';
COMMENT ON COLUMN TENANT_CONTRACT.BUILDING_NAME IS 'ビル名称 既存システム物理名: ECBK0M';
COMMENT ON COLUMN TENANT_CONTRACT.PHONE_NUMBER IS '電話番号 既存システム物理名: ECBK1N';
COMMENT ON COLUMN TENANT_CONTRACT.IMPORTANT_DISCLOSURE_OUTPUT_COUNT IS '重説出力回数 既存システム物理名: ECB10T';
COMMENT ON COLUMN TENANT_CONTRACT.IMPORTANT_DISCLOSURE_OUTPUT_DATE IS '重説出力日 既存システム物理名: ECB11D';
COMMENT ON COLUMN TENANT_CONTRACT.IMPORTANT_DISCLOSURE_COLLECTION_DATE IS '重説回収日 既存システム物理名: ECB12D';
COMMENT ON COLUMN TENANT_CONTRACT.IMPORTANT_DISCLOSURE_DATE IS '重要事項説明日 既存システム物理名: ECB13D';
COMMENT ON COLUMN TENANT_CONTRACT.IMPORTANT_DISCLOSURE_AGENT_CODE IS '重説取引主任者コード 既存システム物理名: ECB14C';
COMMENT ON COLUMN TENANT_CONTRACT.IMPORTANT_DISCLOSURE_DIVISION IS '重要事項説明区分 既存システム物理名: ECBK2B';
COMMENT ON COLUMN TENANT_CONTRACT.PAYMENT_DATE IS '入金日 既存システム物理名: ECBP2D';
COMMENT ON COLUMN TENANT_CONTRACT.MOVE_IN_APPLICATION_DATE IS '入居申込日 既存システム物理名: ECB15D';
COMMENT ON COLUMN TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE IS '入居予定日 既存システム物理名: ECB16D';
COMMENT ON COLUMN TENANT_CONTRACT.USAGE_PURPOSE IS '使用目的 既存システム物理名: ECB23X';
COMMENT ON COLUMN TENANT_CONTRACT.MOVE_IN_APPLICATION_FEE IS '入居申込金 既存システム物理名: ECB18A';
COMMENT ON COLUMN TENANT_CONTRACT.PAYMENT_METHOD_DIVISION IS '入金方法区分 既存システム物理名: ECBBCB';
COMMENT ON COLUMN TENANT_CONTRACT.MOBILE_RECEIPT_NUMBER IS '携帯領収書番号 既存システム物理名: ECB20N';
COMMENT ON COLUMN TENANT_CONTRACT.RESERVATION_APPLICATION_FEE_APPLIED_AMOUNT IS '予約申込金充当額 既存システム物理名: ECB19A';
COMMENT ON COLUMN TENANT_CONTRACT.TENANT_NAME IS '入居者名 既存システム物理名: ECBK5M';
COMMENT ON COLUMN TENANT_CONTRACT.CONTACT_DIVISION IS '連絡先区分 既存システム物理名: ECBECB';
COMMENT ON COLUMN TENANT_CONTRACT.CONTACT IS '連絡先 既存システム物理名: ECBL6M';
COMMENT ON COLUMN TENANT_CONTRACT.PHONE_NUMBER2 IS '電話番号2 既存システム物理名: ECBL7N';
COMMENT ON COLUMN TENANT_CONTRACT.REMARKS IS '備考 既存システム物理名: ECBL8X';
COMMENT ON COLUMN TENANT_CONTRACT.NAME2 IS '氏名2 既存システム物理名: ECBA7M';
COMMENT ON COLUMN TENANT_CONTRACT.PREFECTURE_CODE2 IS '都道府県コード2 既存システム物理名: ECBG0C';
COMMENT ON COLUMN TENANT_CONTRACT.CITY_CODE2 IS '市区郡コード2 既存システム物理名: ECBG1C';
COMMENT ON COLUMN TENANT_CONTRACT.TOWN_CODE2 IS '町村字通称コード2 既存システム物理名: ECBG2C';
COMMENT ON COLUMN TENANT_CONTRACT.ADDRESS_DETAIL2 IS '住所詳細2 既存システム物理名: ECBG3X';
COMMENT ON COLUMN TENANT_CONTRACT.BUILDING_NAME2 IS 'ビル名称2 既存システム物理名: ECBG4M';
COMMENT ON COLUMN TENANT_CONTRACT.PHONE_NUMBER3 IS '電話番号3 既存システム物理名: ECBG5N';
COMMENT ON COLUMN TENANT_CONTRACT.RELATIONSHIP IS '続柄 既存システム物理名: ECBH1M';
COMMENT ON COLUMN TENANT_CONTRACT.NAME3 IS '氏名3 既存システム物理名: ECBK6M';
COMMENT ON COLUMN TENANT_CONTRACT.PREFECTURE_CODE3 IS '都道府県コード3 既存システム物理名: ECBK7C';
COMMENT ON COLUMN TENANT_CONTRACT.CITY_CODE3 IS '市区郡コード3 既存システム物理名: ECBK8C';
COMMENT ON COLUMN TENANT_CONTRACT.TOWN_CODE3 IS '町村字通称コード3 既存システム物理名: ECBK9C';
COMMENT ON COLUMN TENANT_CONTRACT.ADDRESS_DETAIL3 IS '住所詳細3 既存システム物理名: ECBL0X';
COMMENT ON COLUMN TENANT_CONTRACT.BUILDING_NAME3 IS 'ビル名称3 既存システム物理名: ECBL1M';
COMMENT ON COLUMN TENANT_CONTRACT.PHONE_NUMBER4 IS '電話番号4 既存システム物理名: ECBL2N';
COMMENT ON COLUMN TENANT_CONTRACT.RELATIONSHIP2 IS '続柄2 既存システム物理名: ECBL3M';
COMMENT ON COLUMN TENANT_CONTRACT.COHABITANT_NAME1 IS '同居人氏名1 既存システム物理名: ECB72M';
COMMENT ON COLUMN TENANT_CONTRACT.COHABITANT_AGE1 IS '同居人年齢1 既存システム物理名: ECBI2Q';
COMMENT ON COLUMN TENANT_CONTRACT.COHABITANT_RELATIONSHIP1 IS '同居人続柄1 既存システム物理名: ECBI3M';
COMMENT ON COLUMN TENANT_CONTRACT.COHABITANT_NAME2 IS '同居人氏名2 既存システム物理名: ECBI4M';
COMMENT ON COLUMN TENANT_CONTRACT.COHABITANT_AGE2 IS '同居人年齢2 既存システム物理名: ECBI5Q';
COMMENT ON COLUMN TENANT_CONTRACT.COHABITANT_RELATIONSHIP2 IS '同居人続柄2 既存システム物理名: ECBI6M';
COMMENT ON COLUMN TENANT_CONTRACT.COHABITANT_NAME3 IS '同居人氏名3 既存システム物理名: ECBI7M';
COMMENT ON COLUMN TENANT_CONTRACT.AGENT_SHOZOKU_BRANCH IS '担当者所属(建託) 既存システム物理名: ECBI8Q';
COMMENT ON COLUMN TENANT_CONTRACT.COHABITANT_RELATIONSHIP3 IS '同居人続柄3 既存システム物理名: ECBI9M';
COMMENT ON COLUMN TENANT_CONTRACT.COHABITANT_NAME4 IS '同居人氏名4 既存システム物理名: ECBIAM';
COMMENT ON COLUMN TENANT_CONTRACT.AGENT_SHOZOKU_STORE IS '担当者所属(店舗) 既存システム物理名: ECBIBQ';
COMMENT ON COLUMN TENANT_CONTRACT.COHABITANT_RELATIONSHIP4 IS '同居人続柄4 既存システム物理名: ECBICM';
COMMENT ON COLUMN TENANT_CONTRACT.COHABITANT_NAME5 IS '同居人氏名5 既存システム物理名: ECBIDM';
COMMENT ON COLUMN TENANT_CONTRACT.AGENT_SHOZOKU_OFFICE IS '担当者所属(営業所) 既存システム物理名: ECBIEQ';
COMMENT ON COLUMN TENANT_CONTRACT.COHABITANT_RELATIONSHIP5 IS '同居人続柄5 既存システム物理名: ECBIFM';
COMMENT ON COLUMN TENANT_CONTRACT.LANDLORD_APPROVAL_DATE IS '家主承諾日 既存システム物理名: ECB21D';
COMMENT ON COLUMN TENANT_CONTRACT.DEPOSIT_CHANGE_DATE IS '手付変更日 既存システム物理名: ECB22D';
COMMENT ON COLUMN TENANT_CONTRACT.AGENCY_MEMBER_NUMBER IS '代行会社会員番号 既存システム物理名: ECBM0N';
COMMENT ON COLUMN TENANT_CONTRACT.CONTRACT_PERIOD_DIVISION IS '契約期間区分 既存システム物理名: ECBGGB';
COMMENT ON COLUMN TENANT_CONTRACT.RESERVATION_CONTRACT_SIGN IS '予約契約サイン 既存システム物理名: ECB24S';
COMMENT ON COLUMN TENANT_CONTRACT.TENANT_RESERVATION_NUMBER IS 'テナント予約番号 既存システム物理名: ECBAJN';
COMMENT ON COLUMN TENANT_CONTRACT.LEASE_CONTRACT_OUTPUT_CONTENT_DIVISION IS '賃貸借契約書出力内容区分 既存システム物理名: ECBDXB';
COMMENT ON COLUMN TENANT_CONTRACT.STANDARD_RENT IS '基準家賃 既存システム物理名: ECBG9A';
COMMENT ON COLUMN TENANT_CONTRACT.LEASE_CONTRACT_OUTPUT_COUNT IS '賃貸借契約書出力回数 既存システム物理名: ECB25T';
COMMENT ON COLUMN TENANT_CONTRACT.LEASE_CONTRACT_LATEST_OUTPUT_DATE IS '賃貸借契約書最新出力日 既存システム物理名: ECB26D';
COMMENT ON COLUMN TENANT_CONTRACT.LEASE_CONTRACT_COLLECTION_DATE IS '賃貸借契約書回収日 既存システム物理名: ECB27D';
COMMENT ON COLUMN TENANT_CONTRACT.LEASE_CONTRACT_DATE IS '賃貸借契約日 既存システム物理名: ECB28D';
COMMENT ON COLUMN TENANT_CONTRACT.CONTRACT_START_DATE IS '契約開始日 既存システム物理名: ECB29D';
COMMENT ON COLUMN TENANT_CONTRACT.CONTRACT_EXPIRY_DATE IS '契約満了日 既存システム物理名: ECB30D';
COMMENT ON COLUMN TENANT_CONTRACT.NEXT_RENT_REVISION_SCHEDULED_DATE IS '次回賃料改訂予定日 既存システム物理名: ECB31D';
COMMENT ON COLUMN TENANT_CONTRACT.RENT_REVISION_PERIOD IS '家賃改訂期間 既存システム物理名: ECB32L';
COMMENT ON COLUMN TENANT_CONTRACT.FRONT_FREE_RENT_DAYS IS 'フロントフリーレント日数 既存システム物理名: ECB33Q';
COMMENT ON COLUMN TENANT_CONTRACT.DEPOSIT_MONTHS IS '保証金(敷金)月数 既存システム物理名: ECB34Q';
COMMENT ON COLUMN TENANT_CONTRACT.DEPRECIATION_MONTHS IS '償却費月数 既存システム物理名: ECB35Q';
COMMENT ON COLUMN TENANT_CONTRACT.KEY_MONEY_AMOUNT IS '礼金(権利金)金額 既存システム物理名: ECB36A';
COMMENT ON COLUMN TENANT_CONTRACT.KEY_MONEY_DIVISION IS '礼金内外区分 既存システム物理名: ECBG8B';
COMMENT ON COLUMN TENANT_CONTRACT.ADDITIONAL_KEY_MONEY_AMOUNT IS '上乗礼金額(内額) 既存システム物理名: ECBI0A';
COMMENT ON COLUMN TENANT_CONTRACT.DEPOSIT_AMOUNT IS '保証金(敷金)金額 既存システム物理名: ECB37A';
COMMENT ON COLUMN TENANT_CONTRACT.DEPRECIATION IS '償却費 既存システム物理名: ECB38A';
COMMENT ON COLUMN TENANT_CONTRACT.RENT IS '家賃 既存システム物理名: ECB39A';
COMMENT ON COLUMN TENANT_CONTRACT.RENT_DIVISION IS '家賃内外区分 既存システム物理名: ECBJ0B';
COMMENT ON COLUMN TENANT_CONTRACT.PARKING_FEE_AGGREGATION_SIGN IS '駐車料合算サイン 既存システム物理名: ECB40S';
COMMENT ON COLUMN TENANT_CONTRACT.PARKING_FEE IS '駐車料 既存システム物理名: ECB41A';
COMMENT ON COLUMN TENANT_CONTRACT.PARKING_FEE_DIVISION IS '駐車料内外区分 既存システム物理名: ECBM1B';
COMMENT ON COLUMN TENANT_CONTRACT.COMMON_SERVICE_FEE IS '共益費 既存システム物理名: ECB42A';
COMMENT ON COLUMN TENANT_CONTRACT.COMMON_SERVICE_FEE_DIVISION IS '共益費内外区分 既存システム物理名: ECBM2B';
COMMENT ON COLUMN TENANT_CONTRACT.NEIGHBORHOOD_ASSOCIATION_FEE IS '町内会費(回収) 既存システム物理名: ECBM3A';
COMMENT ON COLUMN TENANT_CONTRACT.ADDITIONAL_DEPOSIT_AMOUNT IS '上乗保証金金額 既存システム物理名: ECB44A';
COMMENT ON COLUMN TENANT_CONTRACT.PAYMENT_SCHEDULE_CREATION_SIGN IS '支払予定要作成サイン 既存システム物理名: ECB45S';
COMMENT ON COLUMN TENANT_CONTRACT.PAYMENT_METHOD_DIVISION2 IS '入金方法区分2 既存システム物理名: ECBK4B';
COMMENT ON COLUMN TENANT_CONTRACT.DIFFERENCE_PRORATED_DAYS IS '差額日割日数 既存システム物理名: ECB46Q';
COMMENT ON COLUMN TENANT_CONTRACT.DIFFERENCE_PRORATED_RENT_AMOUNT IS '差額日割家賃額 既存システム物理名: ECB47A';
COMMENT ON COLUMN TENANT_CONTRACT.DIFFERENCE_PRORATED_PARKING_FEE IS '差額日割駐車料 既存システム物理名: ECBF3A';
COMMENT ON COLUMN TENANT_CONTRACT.DIFFERENCE_PRORATED_MANAGEMENT_FEE IS '差額日割管理料 既存システム物理名: ECBF4A';
COMMENT ON COLUMN TENANT_CONTRACT.DIFFERENCE_PRORATED_COMMON_SERVICE_FEE IS '差額日割共益費 既存システム物理名: ECBF5A';
COMMENT ON COLUMN TENANT_CONTRACT.DIFFERENCE_PRORATED_ASSOCIATION_FEE IS '差額日割共済会費 既存システム物理名: ECBH9A';
COMMENT ON COLUMN TENANT_CONTRACT.DIFFERENCE_PRORATED_WATER_MANAGEMENT_FEE IS '差額日割水道管理料 既存システム物理名: ECBQ1A';
COMMENT ON COLUMN TENANT_CONTRACT.DIFFERENCE_MONTHS IS '差額月数 既存システム物理名: ECB48Q';
COMMENT ON COLUMN TENANT_CONTRACT.MONTHLY_DIFFERENCE_RENT_AMOUNT IS '例月差額家賃額 既存システム物理名: ECB49A';
COMMENT ON COLUMN TENANT_CONTRACT.MONTHLY_DIFFERENCE_PARKING_FEE IS '例月差額駐車料 既存システム物理名: ECBF6A';
COMMENT ON COLUMN TENANT_CONTRACT.MONTHLY_DIFFERENCE_COMMON_SERVICE_FEE IS '例月差額共益費 既存システム物理名: ECBF7A';
COMMENT ON COLUMN TENANT_CONTRACT.MONTHLY_DIFFERENCE_MANAGEMENT_FEE IS '例月差額管理費 既存システム物理名: ECBF8A';
COMMENT ON COLUMN TENANT_CONTRACT.MONTHLY_DIFFERENCE_ASSOCIATION_FEE IS '例月差額共済会費 既存システム物理名: ECBF9A';
COMMENT ON COLUMN TENANT_CONTRACT.MONTHLY_DIFFERENCE_WATER_MANAGEMENT_FEE IS '例月差額水道管理料 既存システム物理名: ECBQ2A';
COMMENT ON COLUMN TENANT_CONTRACT.PAYMENT_SCHEDULE_CREATION_SIGN2 IS '支払予定要作成サイン2 既存システム物理名: ECB50S';
COMMENT ON COLUMN TENANT_CONTRACT.DIFFERENCE_RENT_COLLECTION_METHOD IS '差額家賃回収方法 既存システム物理名: ECB51B';
COMMENT ON COLUMN TENANT_CONTRACT.RENT_COLLECTION_METHOD_DIVISION IS '家賃回収方法区分 既存システム物理名: ECB52B';
COMMENT ON COLUMN TENANT_CONTRACT.AGENCY_DIVISION IS '代行会社区分 既存システム物理名: ECBFDB';
COMMENT ON COLUMN TENANT_CONTRACT.BANK_CODE IS '銀行コード 既存システム物理名: ECB53C';
COMMENT ON COLUMN TENANT_CONTRACT.BANK_BRANCH_CODE IS '銀行支店コード 既存システム物理名: ECB54C';
COMMENT ON COLUMN TENANT_CONTRACT.ACCOUNT_TYPE IS '口座種別 既存システム物理名: ECB55C';
COMMENT ON COLUMN TENANT_CONTRACT.ACCOUNT_NUMBER IS '口座番号 既存システム物理名: ECB56N';
COMMENT ON COLUMN TENANT_CONTRACT.ACCOUNT_HOLDER_NAME_KANA IS '口座名義人名(仮名) 既存システム物理名: ECB57M';
COMMENT ON COLUMN TENANT_CONTRACT.ACCOUNT_HOLDER_NAME_KANJI IS '口座名義人名(漢字) 既存システム物理名: ECB58M';
COMMENT ON COLUMN TENANT_CONTRACT.INITIAL_CHANGE_EFFECTIVE_DATE IS '初回／変更有効年月 既存システム物理名: ECB59D';
COMMENT ON COLUMN TENANT_CONTRACT.BANK_CODE2 IS '銀行コード2 既存システム物理名: ECB60C';
COMMENT ON COLUMN TENANT_CONTRACT.BANK_BRANCH_CODE2 IS '銀行支店コード2 既存システム物理名: ECB61C';
COMMENT ON COLUMN TENANT_CONTRACT.ACCOUNT_TYPE2 IS '口座種別2 既存システム物理名: ECB62B';
COMMENT ON COLUMN TENANT_CONTRACT.ACCOUNT_NUMBER2 IS '口座番号2 既存システム物理名: ECB63N';
COMMENT ON COLUMN TENANT_CONTRACT.ACCOUNT_HOLDER_NAME_KANA2 IS '口座名義人名(仮名)2 既存システム物理名: ECB64M';
COMMENT ON COLUMN TENANT_CONTRACT.ACCOUNT_HOLDER_NAME_KANJI2 IS '口座名義人名(漢字)2 既存システム物理名: ECB65M';
COMMENT ON COLUMN TENANT_CONTRACT.DIRECT_DEBIT_TARGET_DIVISION IS '自振対象区分 既存システム物理名: ECBDYB';
COMMENT ON COLUMN TENANT_CONTRACT.DIRECT_DEBIT_AMOUNT IS '自振金額 既存システム物理名: ECBL9A';
COMMENT ON COLUMN TENANT_CONTRACT.LEASE_CONTRACT_TRANSACTING_AGENT IS '賃貸借契約取引主任 既存システム物理名: ECB66C';
COMMENT ON COLUMN TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE IS '契約有効開始日 既存システム物理名: ECB67D';
COMMENT ON COLUMN TENANT_CONTRACT.CONTRACT_EFFECTIVE_END_DATE IS '契約有効終了日 既存システム物理名: ECB68D';
COMMENT ON COLUMN TENANT_CONTRACT.FRONT_FREE_RENT_SIGN IS 'フロントフリーレントサイン 既存システム物理名: ECB69S';
COMMENT ON COLUMN TENANT_CONTRACT.FRONT_FREE_RENT_AMOUNT IS 'フロントフリーレント金額 既存システム物理名: ECB70A';
COMMENT ON COLUMN TENANT_CONTRACT.FRONT_FREE_RENT_MONTHS IS 'フロントフリーレント月数 既存システム物理名: ECB71Q';
COMMENT ON COLUMN TENANT_CONTRACT.LANDLORD_ADVANCE_PAYMENT_ALLOCATION_AMOUNT IS '家主仮払金按分額 既存システム物理名: ECB17A';
COMMENT ON COLUMN TENANT_CONTRACT.LANDLORD_ADVANCE_PAYMENT IS '家主仮払金 既存システム物理名: ECB43A';
COMMENT ON COLUMN TENANT_CONTRACT.MNG_CONTRACT_INITIAL_PAYMENT_AMOUNT IS '管理契約時金処理額 既存システム物理名: ECBJ1A';
COMMENT ON COLUMN TENANT_CONTRACT.MNG_CONTRACT_INITIAL_PAYMENT_AMOUNT_TAX IS '管理契約時金処理額消費税 既存システム物理名: ECBM4A';
COMMENT ON COLUMN TENANT_CONTRACT.ASSOCIATION_ENTRY_FEE_PROCESS IS '共済会加入金処理額 既存システム物理名: ECBJ2A';
COMMENT ON COLUMN TENANT_CONTRACT.REGISTRATION_FEE_AMOUNT IS '登録料処理額 既存システム物理名: ECBK3A';
COMMENT ON COLUMN TENANT_CONTRACT.REGISTRATION_FEE_AMOUNT_TAX IS '登録料処理額消費税 既存システム物理名: ECBM5A';
COMMENT ON COLUMN TENANT_CONTRACT.MNG_CONTRACT_INITIAL_PAYMENT_TRANSFERRED IS '管理契約時金振替済 既存システム物理名: ECBF2S';
COMMENT ON COLUMN TENANT_CONTRACT.BROKERAGE_FEE_EXEMPTION_DIVISION IS '仲介手数料免除区分 既存システム物理名: ECBDFB';
COMMENT ON COLUMN TENANT_CONTRACT.RENT_MANAGEMENT_START_DATE IS '家賃管理開始年月 既存システム物理名: ECBI1D';
COMMENT ON COLUMN TENANT_CONTRACT.NOTARIZED_DOC_PAYMENT_SIGN IS '公正証書支払済サイン 既存システム物理名: ECB74S';
COMMENT ON COLUMN TENANT_CONTRACT.MOVE_IN_CALCULATION_OUTPUT_DATE IS '入居計算書出力日 既存システム物理名: ECBG6D';
COMMENT ON COLUMN TENANT_CONTRACT.REMAINING_RENT IS '残集家賃 既存システム物理名: ECB75A';
COMMENT ON COLUMN TENANT_CONTRACT.REMAINING_RENT_TAX IS '残集家賃消費税 既存システム物理名: ECBM6A';
COMMENT ON COLUMN TENANT_CONTRACT.REMAINING_PARKING_FEE IS '残集駐車料 既存システム物理名: ECB76A';
COMMENT ON COLUMN TENANT_CONTRACT.REMAINING_PARKING_FEE_TAX IS '残集駐車料消費税 既存システム物理名: ECBM7A';
COMMENT ON COLUMN TENANT_CONTRACT.REMAINING_COMMON_SERVICE_FEE IS '残集共益費 既存システム物理名: ECB77A';
COMMENT ON COLUMN TENANT_CONTRACT.REMAINING_COMMON_SERVICE_FEE_TAX IS '残集共益費消費税 既存システム物理名: ECBM8A';
COMMENT ON COLUMN TENANT_CONTRACT.REMAINING_NEIGHBORHOOD_FEE IS '残集町内会費 既存システム物理名: ECBJ3A';
COMMENT ON COLUMN TENANT_CONTRACT.REMAINING_MONTHS IS '残集月数 既存システム物理名: ECB78Q';
COMMENT ON COLUMN TENANT_CONTRACT.PRORATED_RENT IS '日割家賃 既存システム物理名: ECB79A';
COMMENT ON COLUMN TENANT_CONTRACT.PRORATED_RENT_TAX IS '日割家賃消費税 既存システム物理名: ECBM9A';
COMMENT ON COLUMN TENANT_CONTRACT.PRORATED_PARKING IS '日割駐車場 既存システム物理名: ECB80A';
COMMENT ON COLUMN TENANT_CONTRACT.PRORATED_PARKING_FEE_TAX IS '日割駐車料消費税 既存システム物理名: ECBN0A';
COMMENT ON COLUMN TENANT_CONTRACT.PRORATED_COMMON_SERVICE_FEE IS '日割共益費 既存システム物理名: ECB81A';
COMMENT ON COLUMN TENANT_CONTRACT.PRORATED_COMMON_SERVICE_FEE_TAX IS '日割共益費消費税 既存システム物理名: ECBN1A';
COMMENT ON COLUMN TENANT_CONTRACT.PRORATED_NEIGHBORHOOD_FEE IS '日割町内会費 既存システム物理名: ECBO9A';
COMMENT ON COLUMN TENANT_CONTRACT.KEY_MONEY_AMOUNT2 IS '礼金(権利金)金額2 既存システム物理名: ECB82A';
COMMENT ON COLUMN TENANT_CONTRACT.KEY_MONEY_TAX IS '礼金消費税 既存システム物理名: ECBN2A';
COMMENT ON COLUMN TENANT_CONTRACT.DEPOSIT_AMOUNT2 IS '保証金(敷金)金額2 既存システム物理名: ECB83A';
COMMENT ON COLUMN TENANT_CONTRACT.MONTHLY_MANAGEMENT_FEE IS '例月管理費 既存システム物理名: ECB84A';
COMMENT ON COLUMN TENANT_CONTRACT.NOTARIZED_DOC_CREATION_COST IS '公正証書作成費用 既存システム物理名: ECB85A';
COMMENT ON COLUMN TENANT_CONTRACT.STAMP_FEE IS '印紙代 既存システム物理名: ECB86A';
COMMENT ON COLUMN TENANT_CONTRACT.INTERIOR_COOPERATION_FEE IS '内装協力金 既存システム物理名: ECB87A';
COMMENT ON COLUMN TENANT_CONTRACT.INTERIOR_COOPERATION_FEE_TAX IS '内装協力金消費税 既存システム物理名: ECBN3A';
COMMENT ON COLUMN TENANT_CONTRACT.INCOME_COMMISSION_FEE_TENANT IS '収入手数料(借主) 既存システム物理名: ECBA1A';
COMMENT ON COLUMN TENANT_CONTRACT.INCOME_COMMISSION_FEE_TAX IS '収入手数料消費税 既存システム物理名: ECBN4A';
COMMENT ON COLUMN TENANT_CONTRACT.DEPOSIT_COMMISSION_FEE IS '預り手数料 既存システム物理名: ECBN5A';
COMMENT ON COLUMN TENANT_CONTRACT.DEPOSIT_COMMISSION_FEE_TAX IS '預り手数料消費税 既存システム物理名: ECBN6A';
COMMENT ON COLUMN TENANT_CONTRACT.BROKERAGE_FEE IS '仲介手数料 既存システム物理名: ECBA2A';
COMMENT ON COLUMN TENANT_CONTRACT.BROKERAGE_FEE_TAX IS '仲介手数料消費税 既存システム物理名: ECBN7A';
COMMENT ON COLUMN TENANT_CONTRACT.OUTSOURCED_ADVERTISING_FEE IS '外注広告費 既存システム物理名: ECBA3A';
COMMENT ON COLUMN TENANT_CONTRACT.OUTSOURCED_ADVERTISING_FEE_TAX IS '外注広告費消費税 既存システム物理名: ECBN8A';
COMMENT ON COLUMN TENANT_CONTRACT.PAYMENT_SIGN IS '支払サイン 既存システム物理名: ECBA4S';
COMMENT ON COLUMN TENANT_CONTRACT.BROKER_CODE IS '仲介業者コード 既存システム物理名: ECBA5C';
COMMENT ON COLUMN TENANT_CONTRACT.BROKERAGE_FEE_BREAKDOWN_DIVISION IS '仲介料内訳区分 既存システム物理名: ECBEWB';
COMMENT ON COLUMN TENANT_CONTRACT.REMAINING_AMOUNT IS '残集金 既存システム物理名: ECB88A';
COMMENT ON COLUMN TENANT_CONTRACT.REMAINING_SCHEDULED_DATE IS '残集予定日 既存システム物理名: ECBH1D';
COMMENT ON COLUMN TENANT_CONTRACT.REMAINING_DATE IS '残集日 既存システム物理名: ECB89D';
COMMENT ON COLUMN TENANT_CONTRACT.REMAINING_APPROVAL_DATE IS '残集承認日付 既存システム物理名: ECB90D';
COMMENT ON COLUMN TENANT_CONTRACT.OFFSET_AMOUNT_MANAGEMENT_FEE IS '相殺額(管理費) 既存システム物理名: ECB91A';
COMMENT ON COLUMN TENANT_CONTRACT.OFFSET_AMOUNT_MANAGEMENT_FEE_TAX IS '相殺額管理費消費税 既存システム物理名: ECBN9A';
COMMENT ON COLUMN TENANT_CONTRACT.OFFSET_AMOUNT_ASSOCIATION_FEE IS '相殺額(共済会費) 既存システム物理名: ECB92A';
COMMENT ON COLUMN TENANT_CONTRACT.OFFSET_AMOUNT_MAINTENANCE_FEE IS '相殺額(維持費) 既存システム物理名: ECB93A';
COMMENT ON COLUMN TENANT_CONTRACT.OFFSET_AMOUNT_MAINTENANCE_FEE_TAX IS '相殺額(維持費)消 既存システム物理名: ECBO0A';
COMMENT ON COLUMN TENANT_CONTRACT.NEIGHBORHOOD_FEE_DAITO_PAYMENT IS '町内会費(大東払) 既存システム物理名: ECBO5A';
COMMENT ON COLUMN TENANT_CONTRACT.OFFSET_AMOUNT_WATER_MANAGEMENT_FEE IS '相殺額(水道管理料) 既存システム物理名: ECBQ3A';
COMMENT ON COLUMN TENANT_CONTRACT.OFFSET_AMOUNT_TAX IS '相殺額(消費税) 既存システム物理名: ECBQ4A';
COMMENT ON COLUMN TENANT_CONTRACT.OFFSET_AMOUNT_PRORATED_MANAGEMENT_FEE IS '相殺額(日割管理費) 既存システム物理名: ECB94A';
COMMENT ON COLUMN TENANT_CONTRACT.PRORATED_MANAGEMENT_FEE_TAX IS '日割管理費消費税 既存システム物理名: ECBO1A';
COMMENT ON COLUMN TENANT_CONTRACT.OFFSET_AMOUNT_PRORATED_ASSOCIATION IS '相殺額(日割共済会) 既存システム物理名: ECB95A';
COMMENT ON COLUMN TENANT_CONTRACT.OFFSET_AMOUNT_PRORATED_MAINTENANCE IS '相殺額(日割維持費) 既存システム物理名: ECB96A';
COMMENT ON COLUMN TENANT_CONTRACT.MAINTENANCE_PRORATED_TAX IS '維持費日割消費税 既存システム物理名: ECBO2A';
COMMENT ON COLUMN TENANT_CONTRACT.PRORATED_NEIGHBORHOOD_FEE_DAITO IS '日割町内会費(大東) 既存システム物理名: ECBP0A';
COMMENT ON COLUMN TENANT_CONTRACT.OFFSET_AMOUNT_PRORATED_WATER_MANAGEMENT IS '相殺額(日割水管理) 既存システム物理名: ECBQ5A';
COMMENT ON COLUMN TENANT_CONTRACT.OFFSET_AMOUNT_TAX2 IS '相殺額(消費税)2 既存システム物理名: ECBQ6A';
COMMENT ON COLUMN TENANT_CONTRACT.MANAGEMENT_CONTRACT_INITIAL_PAYMENT IS '管理契約時金 既存システム物理名: ECB97A';
COMMENT ON COLUMN TENANT_CONTRACT.MANAGEMENT_CONTRACT_INITIAL_PAYMENT_TAX IS '管理契約時金消費税 既存システム物理名: ECB03A';
COMMENT ON COLUMN TENANT_CONTRACT.TENANT_REGISTRATION_FEE IS 'テナント登録料 既存システム物理名: ECBL5A';
COMMENT ON COLUMN TENANT_CONTRACT.TENANT_REGISTRATION_FEE_TAX IS 'テナント登録料消費 既存システム物理名: ECBO4A';
COMMENT ON COLUMN TENANT_CONTRACT.ASSOCIATION_ENTRY_FEE IS '共済会加入金 既存システム物理名: ECB98A';
COMMENT ON COLUMN TENANT_CONTRACT.NOTARIZED_DOC_CREATION_COST2 IS '公正証書作成費用2 既存システム物理名: ECB99A';
COMMENT ON COLUMN TENANT_CONTRACT.STAMP_FEE2 IS '印紙代2 既存システム物理名: ECBA0A';
COMMENT ON COLUMN TENANT_CONTRACT.MOVE_IN_SETTLEMENT_AMOUNT IS '入居精算額 既存システム物理名: ECBA8A';
COMMENT ON COLUMN TENANT_CONTRACT.MOVE_IN_SETTLEMENT_DATE IS '入居精算日 既存システム物理名: ECBA9D';
COMMENT ON COLUMN TENANT_CONTRACT.TENANT_SETTLEMENT_PAYMENT_SCHEDULE_DIV IS '入居者精算支払予定区分 既存システム物理名: ECBHPB';
COMMENT ON COLUMN TENANT_CONTRACT.TENANT_ADDRESS_OVERRIDE_DIVISION IS 'テナント住所上書区分 既存システム物理名: ECBDHB';
COMMENT ON COLUMN TENANT_CONTRACT.POST_MOVE_CONTACT_PHONE IS '入居後連絡先電話番 既存システム物理名: ECBP1N';
COMMENT ON COLUMN TENANT_CONTRACT.FIRE_INSURANCE_FEE IS '火災保険料 既存システム物理名: ECBB0A';
COMMENT ON COLUMN TENANT_CONTRACT.KEY_HANDOVER_DATE IS '鍵受渡日 既存システム物理名: ECBB1D';
COMMENT ON COLUMN TENANT_CONTRACT.RENOVATION_APPLICATION_SIGN IS '増改築申請書要否サイン 既存システム物理名: ECBB2S';
COMMENT ON COLUMN TENANT_CONTRACT.MANAGEMENT_FEE_TAX IS '管理費消費税 既存システム物理名: ECBB3A';
COMMENT ON COLUMN TENANT_CONTRACT.COOPERATION_FEE_OFFSET_MONTHS IS '協力金相殺月数 既存システム物理名: ECBH2Q';
COMMENT ON COLUMN TENANT_CONTRACT.RESERVATION_CONTRACT_FEE_ALLOCATION_AMOUNT IS '予約契約金按分額 既存システム物理名: ECBH3A';
COMMENT ON COLUMN TENANT_CONTRACT.CANCELLATION_SIGN IS 'キャンセルサイン 既存システム物理名: ECBB4S';
COMMENT ON COLUMN TENANT_CONTRACT.MOVE_IN_START_PROCESSED_SIGN IS '入居開始処理済サイン 既存システム物理名: ECBG7S';
COMMENT ON COLUMN TENANT_CONTRACT.VACATE_NOTICE_DATE IS '明渡し通知日 既存システム物理名: ECBB5D';
COMMENT ON COLUMN TENANT_CONTRACT.VACATE_SCHEDULED_DATE IS '明渡し予定日 既存システム物理名: ECBB6D';
COMMENT ON COLUMN TENANT_CONTRACT.BREACH_PERIOD_EXPIRY_DATE IS '違約期間満了日 既存システム物理名: ECBB7D';
COMMENT ON COLUMN TENANT_CONTRACT.MOVE_OUT_SETTLEMENT_DATE_LANDLORD IS '退居精算日(家主) 既存システム物理名: ECBB8D';
COMMENT ON COLUMN TENANT_CONTRACT.MOVE_OUT_SETTLEMENT_DATE_TENANT IS '退居精算日(入居者 既存システム物理名: ECBB9D';
COMMENT ON COLUMN TENANT_CONTRACT.ASSOCIATION_BENEFIT_START_DATE IS '共済給付開始日 既存システム物理名: ECBC0D';
COMMENT ON COLUMN TENANT_CONTRACT.RESTORATION_COMPLETION_DATE IS '原状復旧完了日 既存システム物理名: ECBC1D';
COMMENT ON COLUMN TENANT_CONTRACT.MOVE_OUT_DATE IS '退居日 既存システム物理名: ECBC2D';
COMMENT ON COLUMN TENANT_CONTRACT.BREACH_YEAR_MONTH IS '違約年月度 既存システム物理名: ECBC3D';
COMMENT ON COLUMN TENANT_CONTRACT.BREACH_PERIOD_DAYS IS '違約期間日数 既存システム物理名: ECBC4Q';
COMMENT ON COLUMN TENANT_CONTRACT.BREACH_PERIOD_START_DATE IS '違約期間開始日 既存システム物理名: ECBC5D';
COMMENT ON COLUMN TENANT_CONTRACT.BREACH_PERIOD_END_DATE IS '違約期間終了日 既存システム物理名: ECBC6D';
COMMENT ON COLUMN TENANT_CONTRACT.PREFECTURE_CODE4 IS '都道府県コード4 既存システム物理名: ECBAWC';
COMMENT ON COLUMN TENANT_CONTRACT.CITY_CODE4 IS '市区郡コード4 既存システム物理名: ECBAXC';
COMMENT ON COLUMN TENANT_CONTRACT.TOWN_CODE4 IS '町村字通称コード4 既存システム物理名: ECBAYC';
COMMENT ON COLUMN TENANT_CONTRACT.ADDRESS_DETAIL4 IS '住所詳細4 既存システム物理名: ECBC7M';
COMMENT ON COLUMN TENANT_CONTRACT.REMARKS2 IS '備考2 既存システム物理名: ECBC8X';
COMMENT ON COLUMN TENANT_CONTRACT.BANK_CODE3 IS '銀行コード3 既存システム物理名: ECBC9C';
COMMENT ON COLUMN TENANT_CONTRACT.BANK_BRANCH_CODE3 IS '銀行支店コード3 既存システム物理名: ECBD0C';
COMMENT ON COLUMN TENANT_CONTRACT.ACCOUNT_TYPE3 IS '口座種別3 既存システム物理名: ECBD1B';
COMMENT ON COLUMN TENANT_CONTRACT.ACCOUNT_NUMBER3 IS '口座番号3 既存システム物理名: ECBD2N';
COMMENT ON COLUMN TENANT_CONTRACT.ACCOUNT_HOLDER_NAME_KANA3 IS '口座名義人名(仮名)3 既存システム物理名: ECBD3M';
COMMENT ON COLUMN TENANT_CONTRACT.ACCOUNT_HOLDER_NAME_KANJI3 IS '口座名義人名(漢字)3 既存システム物理名: ECBD4M';
COMMENT ON COLUMN TENANT_CONTRACT.ON_SITE_CONFIRMATION_DATE IS '立会確認日 既存システム物理名: ECBD5D';
COMMENT ON COLUMN TENANT_CONTRACT.RESTORATION_WORK_EXISTENCE_SIGN IS '原状復旧工事有無サ 既存システム物理名: ECBD6S';
COMMENT ON COLUMN TENANT_CONTRACT.CONSTRUCTION_DIVISION IS '施工区分 既存システム物理名: ECBFHB';
COMMENT ON COLUMN TENANT_CONTRACT.ACTUAL_PAYMENT_DIVISION IS '実支払区分 既存システム物理名: ECBGFB';
COMMENT ON COLUMN TENANT_CONTRACT.CONSTRUCTION_ORDER_NUMBER IS '工事発注番号 既存システム物理名: ECBD7N';
COMMENT ON COLUMN TENANT_CONTRACT.MAINTENANCE_WORK_SIGN IS '営繕工事サイン 既存システム物理名: ECBD8S';
COMMENT ON COLUMN TENANT_CONTRACT.CONSTRUCTION_ORDER_NUMBER2 IS '工事発注番号2 既存システム物理名: ECBBKN';
COMMENT ON COLUMN TENANT_CONTRACT.CONTRACT_DOCUMENT_DIVISION IS '契約書区分 既存システム物理名: ECBEYB';
COMMENT ON COLUMN TENANT_CONTRACT.RENT_ARREARS_MONTHS IS '滞納家賃月数 既存システム物理名: ECBD9Q';
COMMENT ON COLUMN TENANT_CONTRACT.RENT_ARREARS_AMOUNT IS '滞納家賃額 既存システム物理名: ECBE0A';
COMMENT ON COLUMN TENANT_CONTRACT.DEPOSIT_RETAINED_AMOUNT IS '預り保証金額 既存システム物理名: ECBE1A';
COMMENT ON COLUMN TENANT_CONTRACT.ADVANCE_RENT_PAYMENT_REQUEST_FINAL IS '立替家賃支払依頼最 既存システム物理名: ECBE2D';
COMMENT ON COLUMN TENANT_CONTRACT.RENT_BILLING_FINAL_CREATION_YEAR IS '家賃請求最終作成年 既存システム物理名: ECBE3D';
COMMENT ON COLUMN TENANT_CONTRACT.COMPANY_CODE IS '会社コード 既存システム物理名: ECBE4C';
COMMENT ON COLUMN TENANT_CONTRACT.BRANCH_CODE IS '支店コード 既存システム物理名: ECBE5C';
COMMENT ON COLUMN TENANT_CONTRACT.DIRECT_SUPERVISOR_CODE IS '直属コード 既存システム物理名: ECBE6C';
COMMENT ON COLUMN TENANT_CONTRACT.EMPLOYEE_CODE IS '社員コード 既存システム物理名: ECBE7C';
COMMENT ON COLUMN TENANT_CONTRACT.CURRENT_RESPONSIBLE_SHOZOKU_CODE IS '現在担当所属コード 既存システム物理名: ECBH4C';
COMMENT ON COLUMN TENANT_CONTRACT.CURRENT_RESPONSIBLE_BRANCH_CODE IS '現在担当支店コード 既存システム物理名: ECBH5C';
COMMENT ON COLUMN TENANT_CONTRACT.SALES_PERFORMANCE_SHOZOKU_CODE IS '営業実績所属コード 既存システム物理名: ECBH6C';
COMMENT ON COLUMN TENANT_CONTRACT.SALES_PERFORMANCE_BRANCH_CODE IS '営業実績支店コード 既存システム物理名: ECBH7C';
COMMENT ON COLUMN TENANT_CONTRACT.COMPANY_CODE2 IS '会社コード2 既存システム物理名: ECBE8C';
COMMENT ON COLUMN TENANT_CONTRACT.BASE_CODE IS '拠点コード 既存システム物理名: ECBE9C';
COMMENT ON COLUMN TENANT_CONTRACT.DIRECT_SUPERVISOR_CODE2 IS '直属コード2 既存システム物理名: ECBF0C';
COMMENT ON COLUMN TENANT_CONTRACT.EMPLOYEE_CODE2 IS '社員コード2 既存システム物理名: ECBF1C';
COMMENT ON COLUMN TENANT_CONTRACT.CUSTOMER_RESPONSIBLE_BRANCH_CODE IS '客付責任支店コード 既存システム物理名: ECB10C';
COMMENT ON COLUMN TENANT_CONTRACT.JOURNAL_ENTRY_SEQ IS '仕訳計上連番 既存システム物理名: ECBH8N';
COMMENT ON COLUMN TENANT_CONTRACT.PREVIOUS_STATE_DIVISION IS '前回状態区分 既存システム物理名: ECB73B';
COMMENT ON COLUMN TENANT_CONTRACT.CURRENT_STATE_DIVISION IS '現在状態区分 既存システム物理名: ECBH0B';
COMMENT ON COLUMN TENANT_CONTRACT.MODIFICATION_STATE_DIVISION IS '修正状態区分 既存システム物理名: ECBJ4B';
COMMENT ON COLUMN TENANT_CONTRACT.INTERFACE_SIGN IS 'インターフェース サイン 既存システム物理名: ECBA6S';
COMMENT ON COLUMN TENANT_CONTRACT.RESPONSE_RECEIPT IS '反響受付 既存システム物理名: ECBP3C';
COMMENT ON COLUMN TENANT_CONTRACT.SATELLITE_CODE IS 'サテライトコード 既存システム物理名: ECBP4C';
COMMENT ON COLUMN TENANT_CONTRACT.RESPONSE_RECEIPT_DATE IS '反響受付日 既存システム物理名: ECBP5C';
COMMENT ON COLUMN TENANT_CONTRACT.SALES_OFFICE_STAFF IS '営業事務担当者 既存システム物理名: ECBP6C';
COMMENT ON COLUMN TENANT_CONTRACT.PARKING_AGGREGATION_DIVISION IS '駐車場合算区分 既存システム物理名: ECBPKB';
COMMENT ON COLUMN TENANT_CONTRACT.LEDGER_NO IS '台帳NO 既存システム物理名: ECBDCN';
COMMENT ON COLUMN TENANT_CONTRACT.GUARANTOR_NOT_REQUIRED_DIVISION IS '保証人不要区分 既存システム物理名: ECBHFS';
COMMENT ON COLUMN TENANT_CONTRACT.COMMUNICATION_PARTNER_DIVISION IS 'コム提携区分 既存システム物理名: ECBKB1';
COMMENT ON COLUMN TENANT_CONTRACT.NON_STANDARD_DIVISION IS '定型外区分 既存システム物理名: ECBKB2';
COMMENT ON COLUMN TENANT_CONTRACT.CONTRACT_RENEWAL_IMPLEMENTER IS '契約更新業務実施者 既存システム物理名: ECBKB3';
COMMENT ON COLUMN TENANT_CONTRACT.CORPORATE_HOUSING_AGENCY_SIGN IS '社宅代行サイン 既存システム物理名: ECBKB4';
COMMENT ON COLUMN TENANT_CONTRACT.FF_PAYMENT_SIGN IS 'FF支払サイン 既存システム物理名: ECBKB5';
COMMENT ON COLUMN TENANT_CONTRACT.RENTAL_DIVISION IS '賃貸区分 既存システム物理名: ECBKB6';
COMMENT ON COLUMN TENANT_CONTRACT.UNUSED6 IS '未使用6 既存システム物理名: ECBKB7';
COMMENT ON COLUMN TENANT_CONTRACT.UNUSED7 IS '未使用7 既存システム物理名: ECBKB8';
COMMENT ON COLUMN TENANT_CONTRACT.UNUSED8 IS '未使用8 既存システム物理名: ECBKB9';
COMMENT ON COLUMN TENANT_CONTRACT.UNUSED9 IS '未使用9 既存システム物理名: ECBKB0';
COMMENT ON COLUMN TENANT_CONTRACT.CONTRACT_RENT IS '契約家賃 既存システム物理名: ECBKKY';
COMMENT ON COLUMN TENANT_CONTRACT.SPECIAL_RENTAL_DIVISION IS '特優賃区分 既存システム物理名: ECBTKY';
COMMENT ON COLUMN TENANT_CONTRACT.RESPONSE_RECEIVER IS '反響受付者 既存システム物理名: ECBHKU';
COMMENT ON COLUMN TENANT_CONTRACT.PARKING_CONTRACT_FEE IS '駐車場契約手数料 既存システム物理名: ECBQ7A';
COMMENT ON COLUMN TENANT_CONTRACT.PARKING_CONTRACT_FEE_TAX IS '駐車場契約手数料税 既存システム物理名: ECBQ8A';
COMMENT ON COLUMN TENANT_CONTRACT.PARKING_CONTRACT_FEE_EXEMPTION_DIVISION IS 'P契約手数料免除区 既存システム物理名: ECBQ9B';
COMMENT ON COLUMN TENANT_CONTRACT.INCOME_PARKING_FEE_TENANT IS '収入P手数料(借) 既存システム物理名: ECBR1A';
COMMENT ON COLUMN TENANT_CONTRACT.INCOME_PARKING_CONTRACT_FEE_TAX IS '収入P契約手数料税 既存システム物理名: ECBR2A';
COMMENT ON COLUMN TENANT_CONTRACT.DEPOSIT_PARKING_CONTRACT_FEE IS '預りP契約手数料 既存システム物理名: ECBR3A';
COMMENT ON COLUMN TENANT_CONTRACT.DEPOSIT_PARKING_CONTRACT_FEE_TAX IS '預りP契約手数料税 既存システム物理名: ECBR4A';
