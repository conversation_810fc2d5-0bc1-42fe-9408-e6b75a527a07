package jp.ne.simplex.authentication

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder

class AuthSecurityContext {
    companion object {
        fun save(authInfo: AuthInfo) {
            SecurityContextHolder.getContext().authentication =
                UsernamePasswordAuthenticationToken(authInfo, null, emptyList())
        }

        fun clear() {
            SecurityContextHolder.clearContext()
        }
    }
}