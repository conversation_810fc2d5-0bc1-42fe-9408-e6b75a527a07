-- TABLE: COMMON_CODE(共用コード)

CREATE TABLE COMMON_CODE(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    CODE_CATEGORY                                varchar(5)                    
,    CODE                                         varchar(2)                    
,    OFFICIAL_NAME                                varchar(62)                   
,    ABBREVIATION                                 varchar(32)                   
,    GENERIC_FIELD                                varchar(30)                   
) TABLESPACE :TS_TBL;

COMMENT ON TABLE COMMON_CODE IS '共用コード 既存システム物理名: EZB20P';
COMMENT ON COLUMN COMMON_CODE.CREATION_DATE IS '作成年月日 既存システム物理名: SAKUSEI_DT';
COMMENT ON COLUMN COMMON_CODE.CREATION_TIME IS '作成時刻 既存システム物理名: SAKUSEI_TM';
COMMENT ON COLUMN COMMON_CODE.UPDATE_DATE IS '更新年月日 既存システム物理名: KOSHIN_DT';
COMMENT ON COLUMN COMMON_CODE.UPDATE_TIME IS '更新時刻 既存システム物理名: KOSHIN_TM';
COMMENT ON COLUMN COMMON_CODE.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: KOSHIN_PGM_ID';
COMMENT ON COLUMN COMMON_CODE.UPDATER IS '更新者 既存システム物理名: KOSHINSHA';
COMMENT ON COLUMN COMMON_CODE.CODE_CATEGORY IS 'コード区分 既存システム物理名: CODE_KBN';
COMMENT ON COLUMN COMMON_CODE.CODE IS 'コード 既存システム物理名: CODE';
COMMENT ON COLUMN COMMON_CODE.OFFICIAL_NAME IS '正式名称 既存システム物理名: SEISHIKI_MEISHO';
COMMENT ON COLUMN COMMON_CODE.ABBREVIATION IS '略称 既存システム物理名: RYAKUSHO';
COMMENT ON COLUMN COMMON_CODE.GENERIC_FIELD IS '汎用フィールド 既存システム物理名: HANYO_FIELD';
