package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.*
import jp.ne.simplex.db.jooq.gen.tables.pojos.ExclusivePropertyECodePojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.ExclusivePropertyPojo
import jp.ne.simplex.db.jooq.gen.tables.references.*
import jp.ne.simplex.mock.MockLocalDateTime
import jp.ne.simplex.shared.BooleanExtension.Companion.toInt
import jp.ne.simplex.shared.DSLContextEx.Companion.selectExclusivePropertyBy
import jp.ne.simplex.shared.DSLContextEx.Companion.selectExclusivePropertyECodeBy
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmmss
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.enums.PerPage
import jp.ne.simplex.shared.enums.SortOrder
import jp.ne.simplex.stub.*
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.*
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.assertEquals

// カラム数が多すぎてRoomInfoMasterPojoを使おうとするとエラーになるため、
// ROOM_INFO_MASTER関連のテストケースはなし
class ExclusivePropertyRepositoryTest : AbstractTestContainerTest() {
    private lateinit var repository: ExclusivePropertyRepository

    private val currentDateTime = LocalDateTime.of(2025, 11, 12, 13, 45, 30)

    override fun beforeEach() {
        MockLocalDateTime.setNow(currentDateTime)

        repository = ExclusivePropertyRepository(dslContext)
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(
            EXCLUSIVE_PROPERTY,
            EXCLUSIVE_PROPERTY_E_CODE,
            BUILDING_INFO_MASTER,
            ROOM_INFO_MASTER,
            AGENT,
            EMPLOYEE_MASTER
        )
    }

    @AfterEach
    fun tearDown() {
        MockLocalDateTime.close()
    }

    @Nested
    @DisplayName("先行公開検索APIの検証")
    inner class Scenario1 {
        @BeforeEach
        fun setup() {

        }

        @Test
        @DisplayName("任意の検索条件で対象のデータがない場合、結果が一件も取得されないこと")
        fun case1() {
            // 事前準備
            dslContext.saveExclusivePropertyPojo()
            dslContext.saveExclusivePropertyECodePojo()
            dslContext.saveBuildingInfoMasterPojo()
            dslContext.saveAgentPojo()
            // 実行
            val (result, totalCount) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.BUILDING_CODE),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )
            // 結果確認
            assertEquals(emptyList(), result)
            assertEquals(0, totalCount)
        }

        @Test
        @DisplayName("任意の検索条件で対象のデータが一件の場合、結果が一件取得されること")
        fun case2() {
            // 事前準備
            dslContext.saveExclusivePropertyPojo(
                stubExclusivePropertyPojo(
                    id = *********012345678,
                    buildingCode = "*********",
                    roomCode = "R0001",
                    salesOfficeCode = "SO1",
                    exclusiveFrom = 20230101,
                    exclusiveTo = 20231231,
                    companyType = 0,
                    creationDate = 20220101,
                    creationTime = 120000,
                    creator = "000011",
                    updateDate = 20221010,
                    updateTime = 230000,
                    updater = "100011",
                    deleteFlag = "0",
                )
            )
            dslContext.saveEmployeeMasterPojo(
                stubEmployeeMasterPojo(
                    "000011",
                    "TestEmployee",
                ),
                stubEmployeeMasterPojo(
                    "100011",
                    "UpdaterEmployee",
                )
            )
            dslContext.saveExclusivePropertyECodePojo(
                stubExclusivePropertyECodePojo(
                    id = *********012345678,
                    eCode = "E00001000",
                    creationDate = 20220101,
                    creationTime = 120000,
                    creator = "USER1",
                    updateDate = 20221010,
                    updateTime = 230000,
                    updater = "USER2",
                    deleteFlag = "0",
                )
            )
            dslContext.saveBuildingInfoMasterPojo(
                stubBuildingInfoMasterPojo(
                    buildingCode = "*********",
                    propertyName = "testPropertyName",
                )
            )
            dslContext.saveAgentPojo(
                stubAgentPojo(
                    chukaiGyoshaCd = "E00001000",
                    chukaiGyoshameiKanji = "test1",
                    honshitenmeiKanji = "test2",
                )
            )
            // 実行
            val (result, totalCount) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.BUILDING_CODE),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )
            // 結果確認
            assertEquals(*********012345678, result[0].id.value)
            assertEquals("*********", result[0].propertyId.buildingCode.value)
            assertEquals("R0001", result[0].propertyId.roomCode.value)
            assertEquals("testPropertyName", result[0].buildingName)
            assertEquals("20230101", result[0].exclusiveRange.from.yyyyMMdd())
            assertEquals("20231231", result[0].exclusiveRange.to.yyyyMMdd())
            assertEquals("test1test2", result[0].exclusiveTargetName)
            assertEquals("20220101", result[0].createDate.yyyyMMdd())
            assertEquals("TestEmployee", result[0].creator?.kanji)
            assertEquals("20221010", result[0].updateDate.yyyyMMdd())
            assertEquals("UpdaterEmployee", result[0].updater?.kanji)
            assertEquals(1, totalCount)
        }

        @Test
        @DisplayName("任意の検索条件で対象のデータが二件の場合、結果が二件取得されること")
        fun case3() {
            // 事前準備
            dslContext.saveExclusivePropertyPojo(
                stubExclusivePropertyPojo(
                    id = *********012345678,
                    buildingCode = "*********",
                    roomCode = "R0001",
                    salesOfficeCode = "SO1",
                    exclusiveFrom = 20230101,
                    exclusiveTo = 20231231,
                    companyType = 0,
                    creationDate = 20220101,
                    creationTime = 120000,
                    creator = "000011",
                    updateDate = 20221010,
                    updateTime = 230000,
                    updater = "100011",
                    deleteFlag = "0",
                ),
                stubExclusivePropertyPojo(
                    id = *********009876543,
                    buildingCode = "*********",
                    roomCode = "R0002",
                    salesOfficeCode = "SO2",
                    exclusiveFrom = 20230102,
                    exclusiveTo = 20241231,
                    companyType = 0,
                    creationDate = 20220102,
                    creationTime = 120000,
                    creator = "000012",
                    updateDate = 20221011,
                    updateTime = 230000,
                    updater = "100012",
                    deleteFlag = "0",
                ),
            )
            dslContext.saveExclusivePropertyECodePojo(
                stubExclusivePropertyECodePojo(
                    id = *********012345678,
                    eCode = "E00001000",
                    creationDate = 20220101,
                    creationTime = 120000,
                    creator = "USER1",
                    updateDate = 20221010,
                    updateTime = 230000,
                    updater = "USER2",
                    deleteFlag = "0",
                ),
                stubExclusivePropertyECodePojo(
                    id = *********009876543,
                    eCode = "E00001002",
                    creationDate = 20220102,
                    creationTime = 120000,
                    creator = "USER3",
                    updateDate = 20221011,
                    updateTime = 230000,
                    updater = "USER4",
                    deleteFlag = "0",
                ),
            )
            dslContext.saveBuildingInfoMasterPojo(
                stubBuildingInfoMasterPojo(
                    buildingCode = "*********",
                    propertyName = "testPropertyName",
                ),
                stubBuildingInfoMasterPojo(
                    buildingCode = "*********",
                    propertyName = "testPropertyName2",
                ),
            )
            dslContext.saveAgentPojo(
                stubAgentPojo(
                    chukaiGyoshaCd = "E00001000",
                    chukaiGyoshameiKanji = "test1",
                    honshitenmeiKanji = "test2",
                ),
                stubAgentPojo(
                    chukaiGyoshaCd = "E00001002",
                    chukaiGyoshameiKanji = "test3",
                    honshitenmeiKanji = "test4",
                ),
            )
            dslContext.saveEmployeeMasterPojo(
                stubEmployeeMasterPojo(
                    "000011",
                    "TestEmployee",
                ),
                stubEmployeeMasterPojo(
                    "000012",
                    "TestEmployee2",
                ),
                stubEmployeeMasterPojo(
                    "100011",
                    "UpdaterEmployee",
                ),
                stubEmployeeMasterPojo(
                    "100012",
                    "UpdaterEmployee2",
                )
            )
            // 実行
            val (result, totalCount) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.BUILDING_CODE),
                    sortOrder = SortOrder.ASC,
                    idList = null,
                    buildingCode = null,
                    roomCode = null,
                    exclusiveFrom = null,
                    exclusiveTo = null,
                    exclusiveTarget = null,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )
            // 結果確認
            assertEquals(*********012345678, result[0].id.value)
            assertEquals("*********", result[0].propertyId.buildingCode.value)
            assertEquals("R0001", result[0].propertyId.roomCode.value)
            assertEquals("testPropertyName", result[0].buildingName)
            assertEquals("20230101", result[0].exclusiveRange.from.yyyyMMdd())
            assertEquals("20231231", result[0].exclusiveRange.to.yyyyMMdd())
            assertEquals("test1test2", result[0].exclusiveTargetName)
            assertEquals("20220101", result[0].createDate.yyyyMMdd())
            assertEquals("TestEmployee", result[0].creator?.kanji)
            assertEquals("20221010", result[0].updateDate.yyyyMMdd())
            assertEquals("UpdaterEmployee", result[0].updater?.kanji)

            assertEquals(*********009876543, result[1].id.value)
            assertEquals("*********", result[1].propertyId.buildingCode.value)
            assertEquals("R0002", result[1].propertyId.roomCode.value)
            assertEquals("testPropertyName2", result[1].buildingName)
            assertEquals("20230102", result[1].exclusiveRange.from.yyyyMMdd())
            assertEquals("20241231", result[1].exclusiveRange.to.yyyyMMdd())
            assertEquals("test3test4", result[1].exclusiveTargetName)
            assertEquals("20220102", result[1].createDate.yyyyMMdd())
            assertEquals("TestEmployee2", result[1].creator?.kanji)
            assertEquals("20221011", result[1].updateDate.yyyyMMdd())
            assertEquals("UpdaterEmployee2", result[1].updater?.kanji)
            assertEquals(2, totalCount)
        }

        @Test
        @DisplayName("先行先の項目を指定した場合、先行先のカラムが部分一致したデータのみ取得できること")
        fun case4() {
            // 事前準備
            dslContext.saveExclusivePropertyPojo(
                stubExclusivePropertyPojo(
                    id = *********012345678,
                    companyType = ExclusiveProperty.CompanyType.RealEstate.value.toByte(),
                ),
                stubExclusivePropertyPojo(
                    id = *********009876543,
                    companyType = ExclusiveProperty.CompanyType.RealEstate.value.toByte(),
                ),
                stubExclusivePropertyPojo(
                    id = *********01239876,
                    companyType = ExclusiveProperty.CompanyType.RealEstate.value.toByte(),
                ),
            )
            dslContext.saveExclusivePropertyECodePojo(
                stubExclusivePropertyECodePojo(
                    id = *********012345678,
                    eCode = "E00001000",
                ),
                stubExclusivePropertyECodePojo(
                    id = *********009876543,
                    eCode = "E00001002",
                ),
                stubExclusivePropertyECodePojo(
                    id = *********01239876,
                    eCode = "*********",
                ),
            )
            dslContext.saveAgentPojo(
                stubAgentPojo(
                    chukaiGyoshaCd = "E00001000",
                    chukaiGyoshameiKanji = "test1",
                    honshitenmeiKanji = "test2",
                ),
                stubAgentPojo(
                    chukaiGyoshaCd = "E00001002",
                    chukaiGyoshameiKanji = "test3",
                    honshitenmeiKanji = "test4",
                ),
                stubAgentPojo(
                    chukaiGyoshaCd = "*********",
                    chukaiGyoshameiKanji = "test5",
                    honshitenmeiKanji = "test6",
                ),
            )
            // 実行
            val (result, totalCount) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.EXCLUSIVE_TARGET),
                    sortOrder = SortOrder.ASC,
                    idList = null,
                    buildingCode = null,
                    roomCode = null,
                    exclusiveFrom = null,
                    exclusiveTo = null,
                    exclusiveTarget = "1",
                    listingSituationTypeList = emptyList(),
                    //不動産会社タイプのみexclusiveTargetの検索が可能
                    companyTypeList = listOf(
                        ExclusiveProperty.CompanyType.RealEstate
                    ),
                )
            )
            // 結果確認
            assertEquals(1, totalCount)
            assertEquals("test1test2", result[0].exclusiveTargetName)
        }

        @Test
        @DisplayName("掲載状況が終了済みのデータのみ取得できること")
        fun case5() {
            // 事前準備
            dslContext.saveExclusivePropertyPojo(
                stubExclusivePropertyPojo(
                    id = *********012345678,
                    exclusiveFrom = 20270101,
                    exclusiveTo = 20291231,
                ),
                stubExclusivePropertyPojo(
                    id = *********009876543,
                    exclusiveFrom = 20230102,
                    exclusiveTo = 20261231,
                ),
                stubExclusivePropertyPojo(
                    id = *********01239876,
                    exclusiveFrom = 20210101,
                    exclusiveTo = 20231231,
                ),
            )
            // 実行
            val (result, totalCount) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.EXCLUSIVE_DATE),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = listOf(
                        PublishStatus.PreRelease,
                        PublishStatus.Completed
                    ),
                    companyTypeList = emptyList(),
                )
            )
            // 結果確認
            assertEquals(2, totalCount)
            assertEquals("20210101", result[0].exclusiveRange.from.yyyyMMdd())
            assertEquals("20231231", result[0].exclusiveRange.to.yyyyMMdd())
            assertEquals("20270101", result[1].exclusiveRange.from.yyyyMMdd())
            assertEquals("20291231", result[1].exclusiveRange.to.yyyyMMdd())
        }

        @Test
        @DisplayName("先行先種別を指定した場合、指定した先行先種別のデータのみ取得できること")
        fun case6() {
            // 事前準備
            dslContext.saveExclusivePropertyPojo(
                stubExclusivePropertyPojo(
                    id = *********012345678,
                    companyType = 0.toByte()
                ),
                stubExclusivePropertyPojo(
                    id = *********009876543,
                    companyType = 1.toByte()
                ),
                stubExclusivePropertyPojo(
                    id = *********01239876,
                    companyType = 2.toByte()
                ),
            )

            dslContext.saveExclusivePropertyECodePojo(
                stubExclusivePropertyECodePojo(
                    id = *********012345678,
                    eCode = "E12345678"
                )
            )
            // 実行
            val (result, totalCount) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.EXCLUSIVE_DATE),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = listOf(
                        ExclusiveProperty.CompanyType.RealEstate,
                        ExclusiveProperty.CompanyType.HouseCom,
                    ),
                )
            )
            // 結果確認
            assertEquals(2, totalCount)
            assertEquals(*********012345678, result[0].id.value)
            assertEquals(*********01239876, result[1].id.value)
        }

        @Test
        @DisplayName("掲載状況が公開中のデータには、掲載期間中でも早期終了した物件は含まれないこと")
        fun case7() {
            // 事前準備
            dslContext.saveExclusivePropertyPojo(
                stubExclusivePropertyPojo(
                    id = *********012345678,
                    exclusiveFrom = 20230102,
                    exclusiveTo = 20291231,
                ),
                stubExclusivePropertyPojo(
                    id = *********009876543,
                    exclusiveFrom = 20230102,
                    exclusiveTo = 20261231,
                    earlyClosureFlag = true.toInt().toString()
                ),
            )
            // 実行
            val (result, totalCount) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.EXCLUSIVE_DATE),
                    sortOrder = SortOrder.ASC,
                    idList = null,
                    buildingCode = null,
                    roomCode = null,
                    exclusiveFrom = null,
                    exclusiveTo = null,
                    exclusiveTarget = null,
                    listingSituationTypeList = listOf(
                        PublishStatus.InProgress
                    ),
                    companyTypeList = emptyList(),
                )
            )
            // 結果確認
            assertEquals(1, totalCount)
            assertEquals(*********012345678, result[0].id.value)
        }

        @Test
        @DisplayName("掲載状況が公開終了のデータには、掲載期間中でも早期終了した物件が含まれること")
        fun case8() {
            // 事前準備
            dslContext.saveExclusivePropertyPojo(
                stubExclusivePropertyPojo(
                    id = *********012345678,
                    exclusiveFrom = 20230102,
                    exclusiveTo = 20291231,
                ),
                stubExclusivePropertyPojo(
                    id = *********009876543,
                    exclusiveFrom = 20230102,
                    exclusiveTo = 20261231,
                    earlyClosureFlag = true.toInt().toString()
                ),
            )
            // 実行
            val (result, totalCount) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.EXCLUSIVE_DATE),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = listOf(
                        PublishStatus.Completed
                    ),
                    companyTypeList = emptyList(),
                )
            )
            // 結果確認
            assertEquals(1, totalCount)
            assertEquals(*********009876543, result[0].id.value)
        }
    }

    @Nested
    @DisplayName("先行公開検索APIのソート機能検証")
    inner class Scenario2 {

        @BeforeEach
        fun setup() {
            // テスト用のデータを準備
            dslContext.saveExclusivePropertyPojo(
                stubExclusivePropertyPojo(
                    id = *********012345678,
                    buildingCode = "*********",
                    roomCode = "R0001",
                    salesOfficeCode = "SO1",
                    exclusiveFrom = 20230101,
                    exclusiveTo = 20231231,
                    companyType = 0,
                    creationDate = 20220101,
                    creationTime = 120000,
                    creator = "000011",
                    updateDate = 20221010,
                    updateTime = 230000,
                    updater = "USERA",
                    deleteFlag = "0",
                ),
                stubExclusivePropertyPojo(
                    id = *********009876543,
                    buildingCode = "*********",
                    roomCode = "R0003",
                    salesOfficeCode = "SO2",
                    exclusiveFrom = 20230102,
                    exclusiveTo = 20241231,
                    companyType = 1,
                    creationDate = 20220102,
                    creationTime = 120000,
                    creator = "000012",
                    updateDate = 20221011,
                    updateTime = 230000,
                    updater = "USERC",
                    deleteFlag = "0",
                ),
                stubExclusivePropertyPojo(
                    id = *********01239876,
                    buildingCode = "*********",
                    roomCode = "R0002",
                    salesOfficeCode = "SO3",
                    exclusiveFrom = 20230103,
                    exclusiveTo = 20251231,
                    companyType = 2,
                    creationDate = 20220103,
                    creationTime = 120000,
                    creator = "000013",
                    updateDate = 20221012,
                    updateTime = 230000,
                    updater = "USERB",
                    deleteFlag = "0",
                ),
            )
            dslContext.saveExclusivePropertyECodePojo(
                stubExclusivePropertyECodePojo(
                    id = *********012345678,
                    eCode = "E00001000",
                ),
                stubExclusivePropertyECodePojo(
                    id = *********009876543,
                    eCode = "E00001002",
                ),
                stubExclusivePropertyECodePojo(
                    id = *********01239876,
                    eCode = "*********",
                ),
            )
            dslContext.saveBuildingInfoMasterPojo(
                stubBuildingInfoMasterPojo(
                    buildingCode = "*********",
                    propertyName = "BuildingC",
                ),
                stubBuildingInfoMasterPojo(
                    buildingCode = "*********",
                    propertyName = "BuildingA",
                ),
                stubBuildingInfoMasterPojo(
                    buildingCode = "*********",
                    propertyName = "BuildingB",
                ),
            )
            dslContext.saveAgentPojo(
                stubAgentPojo(
                    chukaiGyoshaCd = "E00001000",
                    chukaiGyoshameiKanji = "AgentC",
                    honshitenmeiKanji = "BranchC",
                ),
                stubAgentPojo(
                    chukaiGyoshaCd = "E00001002",
                    chukaiGyoshameiKanji = "AgentA",
                    honshitenmeiKanji = "BranchA",
                ),
                stubAgentPojo(
                    chukaiGyoshaCd = "*********",
                    chukaiGyoshameiKanji = "AgentB",
                    honshitenmeiKanji = "BranchB",
                ),
            )
            dslContext.saveEmployeeMasterPojo(
                stubEmployeeMasterPojo(
                    "000011",
                    "EmployeeC",
                ),
                stubEmployeeMasterPojo(
                    "000012",
                    "EmployeeA",
                ),
                stubEmployeeMasterPojo(
                    "000013",
                    "EmployeeB",
                ),
            )
        }

        @Test
        @DisplayName("建物CD順（昇順）で取得できること")
        fun sortByBuildingCodeAsc() {
            // 実行
            val (result, _) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.BUILDING_CODE),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )

            // 結果確認
            assertEquals(3, result.size)
            assertEquals("*********", result[0].propertyId.buildingCode.value) // 最小値が先頭
            assertEquals("*********", result[1].propertyId.buildingCode.value)
            assertEquals("*********", result[2].propertyId.buildingCode.value)
        }

        @Test
        @DisplayName("建物CD順（降順）で取得できること")
        fun sortByBuildingCodeDesc() {
            // 実行
            val (result, _) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.BUILDING_CODE),
                    sortOrder = SortOrder.DESC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )

            // 結果確認
            assertEquals(3, result.size)
            assertEquals("*********", result[0].propertyId.buildingCode.value) // 最大値が先頭
            assertEquals("*********", result[1].propertyId.buildingCode.value)
            assertEquals("*********", result[2].propertyId.buildingCode.value)
        }

        @Test
        @DisplayName("部屋CD順（昇順）で取得できること")
        fun sortByRoomCodeAsc() {
            // 実行
            val (result, _) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.ROOM_CODE),
                    sortOrder = SortOrder.ASC,
                    idList = null,
                    buildingCode = null,
                    roomCode = null,
                    exclusiveFrom = null,
                    exclusiveTo = null,
                    exclusiveTarget = null,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )

            // 結果確認
            assertEquals(3, result.size)
            assertEquals("R0001", result[0].propertyId.roomCode.value)
            assertEquals("R0002", result[1].propertyId.roomCode.value)
            assertEquals("R0003", result[2].propertyId.roomCode.value)
        }

        @Test
        @DisplayName("物件名順（昇順）で取得できること")
        fun sortByPropertyNameAsc() {
            // 実行
            val (result, _) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.PROPERTY_NAME),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )

            // 結果確認
            assertEquals(3, result.size)
            assertEquals("BuildingA", result[0].buildingName)
            assertEquals("BuildingB", result[1].buildingName)
            assertEquals("BuildingC", result[2].buildingName)
        }

        @Test
        @DisplayName("先行期間順（昇順）で取得できること")
        fun sortByExclusiveDateAsc() {
            // 実行
            val (result, _) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.EXCLUSIVE_DATE),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )

            // 結果確認
            assertEquals(3, result.size)
            assertEquals("20230101", result[0].exclusiveRange.from.yyyyMMdd())
            assertEquals("20230102", result[1].exclusiveRange.from.yyyyMMdd())
            assertEquals("20230103", result[2].exclusiveRange.from.yyyyMMdd())
        }

        @Test
        @DisplayName("先行期間終了順（降順）で取得できること")
        fun sortByExclusiveDateDesc() {
            // 実行
            val (result, _) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.EXCLUSIVE_END_DATE),
                    sortOrder = SortOrder.DESC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )

            // 結果確認
            assertEquals(3, result.size)
            assertEquals("20251231", result[0].exclusiveRange.to.yyyyMMdd())
            assertEquals("20241231", result[1].exclusiveRange.to.yyyyMMdd())
            assertEquals("20231231", result[2].exclusiveRange.to.yyyyMMdd())
        }

        @Test
        @DisplayName("先行先順（昇順）で取得できること")
        fun sortByExclusiveTargetAsc() {
            // 実行
            val (result, _) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.EXCLUSIVE_TARGET),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )

            // 結果確認
            assertEquals(3, result.size)
            // リーシングの場合、先行先名を"リーシング"に仕様変更
            assertEquals("リーシング", result[0].exclusiveTargetName)
            // ハウスコムの場合、先行先名を"ハウスコム"に仕様変更
            assertEquals("ハウスコム", result[1].exclusiveTargetName)
            assertEquals("AgentCBranchC", result[2].exclusiveTargetName)
        }

        @Test
        @DisplayName("先行先種別順（昇順）で取得できること")
        fun sortByCompanyTypeAsc() {
            // 実行
            val (result, _) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.COMPANY_TYPE),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )

            // 結果確認
            assertEquals(3, result.size)
            assertEquals(
                ExclusiveProperty.CompanyType.RealEstate,
                result[0].exclusiveTarget.companyType
            )
            assertEquals(
                ExclusiveProperty.CompanyType.Leasing,
                result[1].exclusiveTarget.companyType
            )
            assertEquals(
                ExclusiveProperty.CompanyType.HouseCom,
                result[2].exclusiveTarget.companyType
            )
        }

        @Test
        @DisplayName("作成日順（昇順）で取得できること")
        fun sortByCreationDateAsc() {
            // 実行
            val (result, _) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.CREATION_DATE),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )

            // 結果確認
            assertEquals(3, result.size)
            assertEquals("20220101", result[0].createDate.yyyyMMdd())
            assertEquals("20220102", result[1].createDate.yyyyMMdd())
            assertEquals("20220103", result[2].createDate.yyyyMMdd())
        }

        @Test
        @DisplayName("更新日順（昇順）で取得できること")
        fun sortByUpdateDateAsc() {
            // 実行
            val (result, _) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(ExclusivePropertiesSearch.SortBy.UPDATE_DATE),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )

            // 結果確認
            assertEquals(3, result.size)
            assertEquals("20221010", result[0].updateDate.yyyyMMdd())
            assertEquals("20221011", result[1].updateDate.yyyyMMdd())
            assertEquals("20221012", result[2].updateDate.yyyyMMdd())
        }

        @Test
        @DisplayName("複数条件でのソートが適用されること")
        fun sortByMultipleFields() {
            // 既存データを削除してから、テストデータを再作成
            dslContext.deleteFrom(EXCLUSIVE_PROPERTY).execute()
            dslContext.deleteFrom(EXCLUSIVE_PROPERTY_E_CODE).execute()
            dslContext.deleteFrom(BUILDING_INFO_MASTER).execute()
            dslContext.deleteFrom(AGENT).execute()
            dslContext.deleteFrom(EMPLOYEE_MASTER).execute()

            // 先行先種別が同じで異なる先行先を持つデータと、異なる先行先種別のデータを作成
            dslContext.saveExclusivePropertyPojo(
                stubExclusivePropertyPojo(
                    id = *********012345678,
                    companyType = 0.toByte() // RealEstate
                ),
                stubExclusivePropertyPojo(
                    id = *********009876543,
                    companyType = 0.toByte() // RealEstate
                ),
                stubExclusivePropertyPojo(
                    id = *********01239876,
                    companyType = 1.toByte() // Leasing
                ),
            )
            dslContext.saveExclusivePropertyECodePojo(
                stubExclusivePropertyECodePojo(
                    id = *********012345678,
                    eCode = "E00001001",
                ),
                stubExclusivePropertyECodePojo(
                    id = *********009876543,
                    eCode = "E00001002",
                ),
                stubExclusivePropertyECodePojo(
                    id = *********01239876,
                    eCode = "*********",
                ),
            )
            dslContext.saveAgentPojo(
                stubAgentPojo(
                    chukaiGyoshaCd = "E00001001",
                    chukaiGyoshameiKanji = "不動産C",
                    honshitenmeiKanji = "支店C",
                ),
                stubAgentPojo(
                    chukaiGyoshaCd = "E00001002",
                    chukaiGyoshameiKanji = "不動産A",
                    honshitenmeiKanji = "支店A",
                ),
                stubAgentPojo(
                    chukaiGyoshaCd = "*********",
                    chukaiGyoshameiKanji = "リーシングB",
                    honshitenmeiKanji = "支店B",
                ),
            )

            // 実行：先行先種別で昇順ソート後、先行先で昇順ソート
            val (result, _) = repository.search(
                ExclusivePropertiesSearch(
                    page = 1,
                    perPage = PerPage.TWENTY_FIVE,
                    sortBy = listOf(
                        ExclusivePropertiesSearch.SortBy.COMPANY_TYPE,
                        ExclusivePropertiesSearch.SortBy.EXCLUSIVE_TARGET
                    ),
                    sortOrder = SortOrder.ASC,
                    listingSituationTypeList = emptyList(),
                    companyTypeList = emptyList(),
                )
            )

            // 結果確認
            assertEquals(3, result.size)

            // 最初に先行先種別=0（不動産会社）のデータが来る
            assertEquals(
                ExclusiveProperty.CompanyType.RealEstate,
                result[0].exclusiveTarget.companyType
            )
            assertEquals(
                ExclusiveProperty.CompanyType.RealEstate,
                result[1].exclusiveTarget.companyType
            )
            // 先行先種別が同じ場合は先行先名でソート（不動産A → 不動産C）
            assertEquals("不動産A支店A", result[0].exclusiveTargetName)
            assertEquals("不動産C支店C", result[1].exclusiveTargetName)

            // 次に先行先種別=1（リーシング）のデータが来る
            assertEquals(
                ExclusiveProperty.CompanyType.Leasing,
                result[2].exclusiveTarget.companyType
            )
            assertEquals("リーシング", result[2].exclusiveTargetName)
        }
    }

    @Nested
    @DisplayName("先行公開新規登録APIの検証")
    inner class Scenario3 {

        @Test
        @DisplayName("Eコードが指定されている場合、EXCLUSIVE_PROPERTY_E_CODEテーブルにもレコードが永続化されること")
        fun case1() {
            // setup
            val data1 = stubExclusivePropertyTargetWithId(
                id = *********012345678,
                companyType = ExclusiveProperty.CompanyType.HouseCom,
                eCode = null
            )
            val data2 = stubExclusivePropertyTargetWithId(
                id = *********009876543,
                companyType = ExclusiveProperty.CompanyType.Leasing,
                eCode = null
            )
            val data3 = stubExclusivePropertyTargetWithId(
                id = *********009871234,
                companyType = ExclusiveProperty.CompanyType.RealEstate,
                eCode = "E12345678"
            )
            val property = stubProperty()

            val request =
                stubRegisterExclusiveProperty(exclusiveTargetWithIds = listOf(data1, data2, data3))

            // execute
            request.getRecords().forEach {
                dslContext.transaction { config ->
                    repository.register(config, stubJwtAuthInfo(), property, it)

                }
            }

            // verify
            assertEquals(1, dslContext.selectExclusivePropertyBy(data1.id).size)
            assertEquals(1, dslContext.selectExclusivePropertyBy(data2.id).size)
            assertEquals(1, dslContext.selectExclusivePropertyBy(data3.id).size)

            assertEquals(0, dslContext.selectExclusivePropertyECodeBy(data1.id).size)
            assertEquals(0, dslContext.selectExclusivePropertyECodeBy(data2.id).size)
            assertEquals(1, dslContext.selectExclusivePropertyECodeBy(data3.id).size)

        }
    }

    @Nested
    @DisplayName("先行公開更新APIの検証")
    inner class Scenario4 {

        val now = LocalDateTime.of(2025, 3, 15, 12, 0)!!

        val pojo = stubExclusivePropertyPojo(
            id = *********012345678,
            buildingCode = "*********",
            roomCode = "R0001",
            salesOfficeCode = "SO1",
            exclusiveFrom = 20250401,
            exclusiveTo = 20250407,
            companyType = 0,
            creationDate = 20250301,
            creationTime = 230000,
            creator = "USERA",
            updateDate = 20250301,
            updateTime = 230000,
            updater = "USERA",
            deleteFlag = "0",
        )

        val eCodePojo = stubExclusivePropertyECodePojo(
            id = pojo.id,
            eCode = "E12345678",
            creationDate = pojo.creationDate,
            creationTime = pojo.creationTime,
            creator = pojo.creator,
            updateDate = pojo.updateDate,
            updateTime = pojo.updateTime,
            updater = pojo.updater,
            deleteFlag = "0",
        )

        val updateTargetInfo = stubExclusivePropertyInfo(
            id = pojo.id,
            buildingCode = pojo.buildingCode,
            roomCode = pojo.roomCode,
            exclusiveFrom = pojo.exclusiveFrom,
            exclusiveTo = pojo.exclusiveTo,
            exclusiveTarget = ExclusiveProperty.ExclusiveTarget(
                companyType = ExclusiveProperty.CompanyType.RealEstate,
                eCode = Agent.ECode.of(eCodePojo.eCode),
            ),
            salesOfficeCode = pojo.salesOfficeCode,
            creationDate = pojo.creationDate.toString().yyyyMMdd(),
            creator = pojo.creator,
            updateDate = pojo.updateDate.toString().yyyyMMdd(),
            updater = pojo.updater,
        )

        @BeforeEach
        fun setup() {
            MockLocalDateTime.setNow(now)
            dslContext.saveExclusivePropertyPojo(pojo)
            dslContext.saveExclusivePropertyECodePojo(eCodePojo)
        }

        @AfterEach
        fun tearDown() {
            MockLocalDateTime.close()
        }

        @Test
        @DisplayName("Eコードが指定されている場合、EXCLUSIVE_PROPERTY_E_CODEテーブルにもレコードが更新されること")
        fun case1() {
            // setup
            val requestAuthInfo = stubJwtAuthInfo()
            val request = stubUpdateExclusiveProperty(
                id = updateTargetInfo.id.value,
                exclusiveFrom = updateTargetInfo.exclusiveRange.from.plusDays(7),
                exclusiveTo = updateTargetInfo.exclusiveRange.to.plusDays(14),
                exclusiveTargetWithIds = listOf(
                    stubExclusivePropertyTargetWithId(
                        id = 100,
                        companyType = updateTargetInfo.exclusiveTarget.companyType,
                        eCode = updateTargetInfo.exclusiveTarget.eCode?.value,
                    )
                )
            )

            // execute
            dslContext.transaction { config ->
                request.getRecords(stubProperty()).forEach {
                    repository.update(config, requestAuthInfo, updateTargetInfo.id, it)
                }
            }

            // verify
            assertEquals(1, dslContext.selectExclusivePropertyBy(updateTargetInfo.propertyId).size)

            // 1つ目の先行先のレコードの確認
            val record = dslContext.selectExclusivePropertyBy(request.id).first()
            val eCodeRecord = dslContext.selectExclusivePropertyECodeBy(request.id).first()
            // 期間と、メタデータは更新されている
            assertEquals(request.exclusiveRange.from.yyyyMMdd().toInt(), record.exclusiveFrom)
            assertEquals(request.exclusiveRange.to.yyyyMMdd().toInt(), record.exclusiveTo)
            assertEquals(requestAuthInfo.employeeCode.value, record.updater)
            assertEquals(now.yyyyMMdd().toInt(), record.updateDate)
            assertEquals(now.HHmmss().toInt(), record.updateTime)
            assertEquals("0", record.deleteFlag)

            assertEquals(
                request.exclusiveTargetWithIds.first().target.eCode?.value,
                eCodeRecord.eCode
            )
            assertEquals(requestAuthInfo.employeeCode.value, eCodeRecord.updater)
            assertEquals(now.yyyyMMdd().toInt(), eCodeRecord.updateDate)
            assertEquals(now.HHmmss().toInt(), eCodeRecord.updateTime)
            assertEquals("0", eCodeRecord.deleteFlag)

            // 上記以外は更新されていないこと
            pojo.assertCreatedDataIsNotUpdated(record)
            eCodePojo.assertCreatedDataIsNotUpdated(eCodeRecord)
        }

        // 新規作成時に設定された値が、更新されていないことをチェックする関数
        private fun ExclusivePropertyPojo.assertCreatedDataIsNotUpdated(other: ExclusivePropertyPojo) {
            assertEquals(this.buildingCode, other.buildingCode)
            assertEquals(this.roomCode, other.roomCode)
            assertEquals(this.salesOfficeCode, other.salesOfficeCode)
            assertEquals(this.creator, other.creator)
            assertEquals(this.creationDate, other.creationDate)
            assertEquals(this.creationTime, other.creationTime)
        }

        // 新規作成時に設定された値が、更新されていないことをチェックする関数
        private fun ExclusivePropertyECodePojo.assertCreatedDataIsNotUpdated(other: ExclusivePropertyECodePojo) {
            assertEquals(this.creator, other.creator)
            assertEquals(this.creationDate, other.creationDate)
            assertEquals(this.creationTime, other.creationTime)
        }
    }

    @Nested
    @DisplayName("先行公開削除APIの検証")
    inner class Scenario5 {

        @Test
        @DisplayName("削除処理後に更新対象項目の値が変化すること")
        fun case1() {
            val data1 = stubExclusivePropertiesSearchPojo(
                id = *********012345678,
                exclusiveFrom = 20260101,
                exclusiveTo = 20261231,
            ).toExclusivePropertyInfo()!!

            val data2 = stubExclusivePropertiesSearchPojo(
                id = *********012345679,
                exclusiveFrom = 20250101,
                exclusiveTo = 20251231,
            ).toExclusivePropertyInfo()!!

            // setup
            dslContext.saveExclusivePropertyPojo(
                stubExclusivePropertyPojo(
                    id = data1.id.value,
                    exclusiveFrom = 20260101,
                    exclusiveTo = 20261231,
                    earlyClosureFlag = false.toInt().toString(),
                    updater = "USER1",
                    updateDate = 20230101,
                    updateTime = 120000,
                    deleteFlag = false.toInt().toString(),
                ),
                stubExclusivePropertyPojo(
                    id = data2.id.value,
                    exclusiveFrom = 20250101,
                    exclusiveTo = 20251231,
                    earlyClosureFlag = false.toInt().toString(),
                    updater = "USER2",
                    updateDate = 20230101,
                    updateTime = 120000,
                    deleteFlag = false.toInt().toString(),
                )
            )
            dslContext.saveExclusivePropertyECodePojo(
                stubExclusivePropertyECodePojo(
                    id = data1.id.value,
                    eCode = "E00001000",
                    updater = "USER1",
                    updateDate = 20230101,
                    updateTime = 120000,
                    deleteFlag = false.toInt().toString(),
                ),
                stubExclusivePropertyECodePojo(
                    id = data2.id.value,
                    eCode = "E00001000",
                    updater = "USER2",
                    updateDate = 20230101,
                    updateTime = 120000,
                    deleteFlag = false.toInt().toString(),
                )
            )

            // execute
            listOf(data1, data2).forEach {
                dslContext.transaction { config ->
                    repository.cancel(config, stubJwtAuthInfo().getRequestUser(), it)
                }
            }

            // verify
            // 公開開始前
            var property = dslContext.selectExclusivePropertyBy(data1.id).first()
            assertEquals(data1.id.value, property.id)
            assertEquals(data1.exclusiveRange.to.yyyyMMdd(), property.exclusiveTo.toString())
            assertEquals(stubJwtAuthInfo().employeeCode.value, property.updater)
            assertEquals(LocalDate.now().yyyyMMdd(), property.updateDate.toString())
            assertEquals(LocalDateTime.now().HHmmss(), property.updateTime.toString())
            assertEquals(false.toInt().toString(), property.earlyClosureFlag)
            assertEquals(true.toInt().toString(), property.deleteFlag)

            var ecode = dslContext.selectExclusivePropertyECodeBy(data1.id).first()
            assertEquals(data1.id.value, ecode.id)
            assertEquals(stubJwtAuthInfo().employeeCode.value, ecode.updater)
            assertEquals(LocalDate.now().yyyyMMdd(), ecode.updateDate.toString())
            assertEquals(LocalDateTime.now().HHmmss(), ecode.updateTime.toString())
            assertEquals(true.toInt().toString(), ecode.deleteFlag)

            // 公開開始後
            property = dslContext.selectExclusivePropertyBy(data2.id).first()
            assertEquals(data2.id.value, property.id)
            assertEquals(LocalDate.now().yyyyMMdd(), property.exclusiveTo.toString())
            assertEquals(stubJwtAuthInfo().employeeCode.value, property.updater)
            assertEquals(LocalDate.now().yyyyMMdd(), property.updateDate.toString())
            assertEquals(LocalDateTime.now().HHmmss(), property.updateTime.toString())
            assertEquals(true.toInt().toString(), property.earlyClosureFlag)
            assertEquals(false.toInt().toString(), property.deleteFlag)

            ecode = dslContext.selectExclusivePropertyECodeBy(data2.id).first()
            assertEquals(data2.id.value, ecode.id)
            assertEquals(stubJwtAuthInfo().employeeCode.value, ecode.updater)
            assertEquals(LocalDate.now().yyyyMMdd(), ecode.updateDate.toString())
            assertEquals(LocalDateTime.now().HHmmss(), ecode.updateTime.toString())
            assertEquals(true.toInt().toString(), ecode.deleteFlag)
        }
    }

    // アノテーション
    @Nested
    @DisplayName("先行公開情報CSVの検証")
    inner class Scenario6 {
        val now = LocalDateTime.of(2025, 3, 15, 12, 0)!!

        fun setup() {
            dslContext.saveExclusivePropertyPojo(
                // 取得される
                stubExclusivePropertyPojo(
                    id = ******************,
                    buildingCode = "*********",
                    roomCode = "00010",
                    exclusiveFrom = now.minusDays(1).yyyyMMdd().toInt(),
                    exclusiveTo = now.plusDays(1).yyyyMMdd().toInt(),
                    companyType = 1,
                ),
                // 取得される
                stubExclusivePropertyPojo(
                    id = ******************,
                    buildingCode = "*********",
                    roomCode = "00020",
                    exclusiveFrom = now.minusDays(1).yyyyMMdd().toInt(),
                    exclusiveTo = now.plusDays(1).yyyyMMdd().toInt(),
                    companyType = 1,
                ),
                // 取得されない
                stubExclusivePropertyPojo(
                    id = ******************,
                    buildingCode = "*********",
                    roomCode = "00020",
                    exclusiveFrom = now.minusDays(1).yyyyMMdd().toInt(),
                    exclusiveTo = now.plusDays(1).yyyyMMdd().toInt(),
                    companyType = 0,
                ),
                // 取得されない
                stubExclusivePropertyPojo(
                    id = ******************,
                    buildingCode = "*********",
                    roomCode = "00030",
                    exclusiveFrom = now.plusDays(1).yyyyMMdd().toInt(),
                    exclusiveTo = now.plusDays(2).yyyyMMdd().toInt(),
                    companyType = 1,
                ),
                // 取得されない
                stubExclusivePropertyPojo(
                    id = *********000000000,
                    buildingCode = "*********",
                    roomCode = "00040",
                    exclusiveFrom = now.minusDays(2).yyyyMMdd().toInt(),
                    exclusiveTo = now.minusDays(1).yyyyMMdd().toInt(),
                    companyType = 1,
                ),
            )

            dslContext.saveRoomInfoMasterPojo(
                stubRoomInfoMasterPojo(
                    buildingCode = "*********",
                    roomCode = "00010",
                    roomNumber = "001",
                    buildingName = "テスト物件",
                ),
                stubRoomInfoMasterPojo(
                    buildingCode = "*********",
                    roomCode = "00020",
                    roomNumber = "002",
                    buildingName = "てすと物件",
                ),
                stubRoomInfoMasterPojo(
                    buildingCode = "*********",
                    roomCode = "00020",
                    roomNumber = "002",
                    buildingName = "test物件",
                ),
                stubRoomInfoMasterPojo(
                    buildingCode = "*********",
                    roomCode = "00030",
                    roomNumber = "003",
                    buildingName = "test物件",
                ),
                stubRoomInfoMasterPojo(
                    buildingCode = "*********",
                    roomCode = "00040",
                    roomNumber = "004",
                    buildingName = "TEST物件",
                ),
            )
        }

        @Test
        @DisplayName("先行公開情報CSVの対象データが0件取得されること")
        fun case01() {
            val param = ExclusivePropertiesSearchForBatch(
                listingSituationTypeList = listOf(PublishStatus.InProgress),
                companyTypeList = listOf(ExclusiveProperty.CompanyType.Leasing)
            )
            val results = repository.findExclusivePropertyForECloudBatch(param)

            assertEquals(0, results.size)
        }

        @Test
        @DisplayName("先行公開情報CSVの対象データが全て取得されること")
        fun case02() {
            MockLocalDateTime.setNow(now)
            setup()
            val param = ExclusivePropertiesSearchForBatch(
                listingSituationTypeList = listOf(PublishStatus.InProgress),
                companyTypeList = listOf(ExclusiveProperty.CompanyType.Leasing)
            )
            val results = repository.findExclusivePropertyForECloudBatch(param)

            assertEquals(2, results.size)
            assertEquals("******************", results[0].id.value.toString())
            assertEquals("*********", results[0].propertyId.buildingCode.value)
            assertEquals("00010", results[0].propertyId.roomCode.value)
            assertEquals("テスト物件", results[0].buildingName)
            assertEquals("001", results[0].roomNumber?.getValue())
            assertEquals("20250314", results[0].exclusiveRange.from.yyyyMMdd())
            assertEquals("20250316", results[0].exclusiveRange.to.yyyyMMdd())
            assertEquals(
                ExclusiveProperty.CompanyType.Leasing,
                results[0].exclusiveTarget.companyType
            )

            assertEquals("******************", results[1].id.value.toString())
            assertEquals("*********", results[1].propertyId.buildingCode.value)
            assertEquals("00020", results[1].propertyId.roomCode.value)
            assertEquals("てすと物件", results[1].buildingName)
            assertEquals("002", results[1].roomNumber?.getValue())
            assertEquals("20250314", results[1].exclusiveRange.from.yyyyMMdd())
            assertEquals("20250316", results[1].exclusiveRange.to.yyyyMMdd())
            assertEquals(
                ExclusiveProperty.CompanyType.Leasing,
                results[1].exclusiveTarget.companyType
            )

            MockLocalDateTime.close()
        }
    }
}
