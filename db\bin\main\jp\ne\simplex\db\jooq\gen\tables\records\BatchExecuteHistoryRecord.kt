/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.time.LocalDateTime

import jp.ne.simplex.db.jooq.gen.tables.BatchExecuteHistoryTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BatchExecuteHistoryPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 定期バッチ実行履歴 既存システム物理名: -
 */
@Suppress("UNCHECKED_CAST")
open class BatchExecuteHistoryRecord private constructor() : UpdatableRecordImpl<BatchExecuteHistoryRecord>(BatchExecuteHistoryTable.BATCH_EXECUTE_HISTORY) {

    open var executionDatetime: LocalDateTime
        set(value): Unit = set(0, value)
        get(): LocalDateTime = get(0) as LocalDateTime

    open var executionDate: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var batchType: String
        set(value): Unit = set(2, value)
        get(): String = get(2) as String

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised BatchExecuteHistoryRecord
     */
    constructor(executionDatetime: LocalDateTime, executionDate: String, batchType: String): this() {
        this.executionDatetime = executionDatetime
        this.executionDate = executionDate
        this.batchType = batchType
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BatchExecuteHistoryRecord
     */
    constructor(value: BatchExecuteHistoryPojo?): this() {
        if (value != null) {
            this.executionDatetime = value.executionDatetime
            this.executionDate = value.executionDate
            this.batchType = value.batchType
            resetChangedOnNotNull()
        }
    }
}
