package jp.ne.simplex.application.extension

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import jp.ne.simplex.shared.StringExtension.Companion.isOnlyFullWidth
import jp.ne.simplex.shared.StringExtension.Companion.toFullWidth

class StringExtensionTest : FunSpec({

    context("全角文字の判定の検証") {

        test("全角文字だけの文字列はtrueを返す") {
            "テスト".isOnlyFullWidth().shouldBeTrue() // 全角カタカナ
            "漢字".isOnlyFullWidth().shouldBeTrue() // 全角漢字
            "あいうえお".isOnlyFullWidth().shouldBeTrue() // 全角ひらがな
            "ＡＢＣＤＥ".isOnlyFullWidth().shouldBeTrue() // 全角英字
            "１２３４５".isOnlyFullWidth().shouldBeTrue() // 全角数字
            "！＠＃＄％".isOnlyFullWidth().shouldBeTrue() // 全角記号
            "　".isOnlyFullWidth().shouldBeTrue() // 全角スペース
        }

        test("半角文字を含む文字列はfalseを返す") {
            "テスト123".isOnlyFullWidth().shouldBeFalse() // 全角文字と半角数字の混在
            "Hello, テスト".isOnlyFullWidth().shouldBeFalse() // 半角英字と全角文字の混在
            "abc".isOnlyFullWidth().shouldBeFalse() // 全て半角英字
            "12345".isOnlyFullWidth().shouldBeFalse() // 全て半角数字
            "!@#$%".isOnlyFullWidth().shouldBeFalse() // 全て半角記号
            " ".isOnlyFullWidth().shouldBeFalse() // 半角スペース
        }

        test("特殊文字を含む場合") {
            "\u3000".isOnlyFullWidth().shouldBeTrue() // 全角スペース（Unicode）
            "\uFFFD".isOnlyFullWidth().shouldBeTrue() // 不明な文字
            "\u0008".isOnlyFullWidth().shouldBeFalse() // バックスペース
            "\u0009".isOnlyFullWidth().shouldBeFalse() // タブ
            "\u000A".isOnlyFullWidth().shouldBeFalse() // 改行
            "😀".isOnlyFullWidth().shouldBeFalse() // 絵文字
        }
    }

    context("半角文字から全角文字への変換の検証") {

        test("半角数字は全て全角数字に変換されること") {
            "1234567890".toFullWidth().shouldBe("１２３４５６７８９０")
        }

        test("半角アルファベットは全て全角アルファベットに変換されること") {
            "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".toFullWidth()
                .shouldBe("ａｂｃｄｅｆｇｈｉｊｋｌｍｎｏｐｑｒｓｔｕｖｗｘｙｚＡＢＣＤＥＦＧＨＩＪＫＬＭＮＯＰＱＲＳＴＵＶＷＸＹＺ")
        }

        test("半角カタカナは全角カタカナに変換されること") {
            "ｺﾝﾆﾁﾊ".toFullWidth().shouldBe("コンニチハ")
        }

        test("半角スペースが全角スペースに変換されること") {
            "A B C".toFullWidth().shouldBe("Ａ　Ｂ　Ｃ")
        }

        test("半角記号が全角記号に変換されること") {
            "!@#$%^&*()_+-=[]{};':\"\\|,.<>/?".toFullWidth()
                .shouldBe("！＠＃＄％＾＆＊（）＿＋－＝［］｛｝；＇：＂＼｜，．＜＞／？")
        }

        test("全角文字はそのまま保持されること") {
            "１２３４５ＡＢＣＤＥあいうえおアイウエオ一二三四五".toFullWidth()
                .shouldBe("１２３４５ＡＢＣＤＥあいうえおアイウエオ一二三四五")
        }
    }
})
