/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.TenantContractTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.TenantContractPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * テナント契約 既存システム物理名: ECB20P
 */
@Suppress("UNCHECKED_CAST")
open class TenantContractRecord private constructor() : UpdatableRecordImpl<TenantContractRecord>(TenantContractTable.TENANT_CONTRACT) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var tenantContractNumber: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var tenantContractChangeSeq: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    open var contractContentDivision: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var buildingCode: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var roomCode: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var parkingCode: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var aggregateContractNumber: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var aggregateContractChangeSeq: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var parkingSpaces: Short?
        set(value): Unit = set(15, value)
        get(): Short? = get(15) as Short?

    open var bulkLeaseSign: Byte?
        set(value): Unit = set(16, value)
        get(): Byte? = get(16) as Byte?

    open var tenantType: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var tenantProspectNumber: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var tenantCode: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var searchKana: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var infoAcquisitionDivision: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var hotInfoReceiptNumber: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var landlordCode_10: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var taxDivision: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var searchKana2: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var name: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var prefectureCode: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var cityCode: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var townCode: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var addressDetail: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var buildingName: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var phoneNumber: String?
        set(value): Unit = set(32, value)
        get(): String? = get(32) as String?

    open var importantDisclosureOutputCount: Byte?
        set(value): Unit = set(33, value)
        get(): Byte? = get(33) as Byte?

    open var importantDisclosureOutputDate: Int?
        set(value): Unit = set(34, value)
        get(): Int? = get(34) as Int?

    open var importantDisclosureCollectionDate: Int?
        set(value): Unit = set(35, value)
        get(): Int? = get(35) as Int?

    open var importantDisclosureDate: Int?
        set(value): Unit = set(36, value)
        get(): Int? = get(36) as Int?

    open var importantDisclosureAgentCode: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var importantDisclosureDivision: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var paymentDate: Int?
        set(value): Unit = set(39, value)
        get(): Int? = get(39) as Int?

    open var moveInApplicationDate: Int?
        set(value): Unit = set(40, value)
        get(): Int? = get(40) as Int?

    open var moveInScheduledDate: Int?
        set(value): Unit = set(41, value)
        get(): Int? = get(41) as Int?

    open var usagePurpose: String?
        set(value): Unit = set(42, value)
        get(): String? = get(42) as String?

    open var moveInApplicationFee: Int?
        set(value): Unit = set(43, value)
        get(): Int? = get(43) as Int?

    open var paymentMethodDivision: String?
        set(value): Unit = set(44, value)
        get(): String? = get(44) as String?

    open var mobileReceiptNumber: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var reservationApplicationFeeAppliedAmount: Int?
        set(value): Unit = set(46, value)
        get(): Int? = get(46) as Int?

    open var tenantName: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var contactDivision: String?
        set(value): Unit = set(48, value)
        get(): String? = get(48) as String?

    open var contact: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    open var phoneNumber2: String?
        set(value): Unit = set(50, value)
        get(): String? = get(50) as String?

    open var remarks: String?
        set(value): Unit = set(51, value)
        get(): String? = get(51) as String?

    open var name2: String?
        set(value): Unit = set(52, value)
        get(): String? = get(52) as String?

    open var prefectureCode2: String?
        set(value): Unit = set(53, value)
        get(): String? = get(53) as String?

    open var cityCode2: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var townCode2: String?
        set(value): Unit = set(55, value)
        get(): String? = get(55) as String?

    open var addressDetail2: String?
        set(value): Unit = set(56, value)
        get(): String? = get(56) as String?

    open var buildingName2: String?
        set(value): Unit = set(57, value)
        get(): String? = get(57) as String?

    open var phoneNumber3: String?
        set(value): Unit = set(58, value)
        get(): String? = get(58) as String?

    open var relationship: String?
        set(value): Unit = set(59, value)
        get(): String? = get(59) as String?

    open var name3: String?
        set(value): Unit = set(60, value)
        get(): String? = get(60) as String?

    open var prefectureCode3: String?
        set(value): Unit = set(61, value)
        get(): String? = get(61) as String?

    open var cityCode3: String?
        set(value): Unit = set(62, value)
        get(): String? = get(62) as String?

    open var townCode3: String?
        set(value): Unit = set(63, value)
        get(): String? = get(63) as String?

    open var addressDetail3: String?
        set(value): Unit = set(64, value)
        get(): String? = get(64) as String?

    open var buildingName3: String?
        set(value): Unit = set(65, value)
        get(): String? = get(65) as String?

    open var phoneNumber4: String?
        set(value): Unit = set(66, value)
        get(): String? = get(66) as String?

    open var relationship2: String?
        set(value): Unit = set(67, value)
        get(): String? = get(67) as String?

    open var cohabitantName1: String?
        set(value): Unit = set(68, value)
        get(): String? = get(68) as String?

    open var cohabitantAge1: Short?
        set(value): Unit = set(69, value)
        get(): Short? = get(69) as Short?

    open var cohabitantRelationship1: String?
        set(value): Unit = set(70, value)
        get(): String? = get(70) as String?

    open var cohabitantName2: String?
        set(value): Unit = set(71, value)
        get(): String? = get(71) as String?

    open var cohabitantAge2: Short?
        set(value): Unit = set(72, value)
        get(): Short? = get(72) as Short?

    open var cohabitantRelationship2: String?
        set(value): Unit = set(73, value)
        get(): String? = get(73) as String?

    open var cohabitantName3: String?
        set(value): Unit = set(74, value)
        get(): String? = get(74) as String?

    open var agentShozokuBranch: Short?
        set(value): Unit = set(75, value)
        get(): Short? = get(75) as Short?

    open var cohabitantRelationship3: String?
        set(value): Unit = set(76, value)
        get(): String? = get(76) as String?

    open var cohabitantName4: String?
        set(value): Unit = set(77, value)
        get(): String? = get(77) as String?

    open var agentShozokuStore: Short?
        set(value): Unit = set(78, value)
        get(): Short? = get(78) as Short?

    open var cohabitantRelationship4: String?
        set(value): Unit = set(79, value)
        get(): String? = get(79) as String?

    open var cohabitantName5: String?
        set(value): Unit = set(80, value)
        get(): String? = get(80) as String?

    open var agentShozokuOffice: Short?
        set(value): Unit = set(81, value)
        get(): Short? = get(81) as Short?

    open var cohabitantRelationship5: String?
        set(value): Unit = set(82, value)
        get(): String? = get(82) as String?

    open var landlordApprovalDate: Int?
        set(value): Unit = set(83, value)
        get(): Int? = get(83) as Int?

    open var depositChangeDate: Int?
        set(value): Unit = set(84, value)
        get(): Int? = get(84) as Int?

    open var agencyMemberNumber: String?
        set(value): Unit = set(85, value)
        get(): String? = get(85) as String?

    open var contractPeriodDivision: String?
        set(value): Unit = set(86, value)
        get(): String? = get(86) as String?

    open var reservationContractSign: Byte?
        set(value): Unit = set(87, value)
        get(): Byte? = get(87) as Byte?

    open var tenantReservationNumber: String?
        set(value): Unit = set(88, value)
        get(): String? = get(88) as String?

    open var leaseContractOutputContentDivision: String?
        set(value): Unit = set(89, value)
        get(): String? = get(89) as String?

    open var standardRent: Int?
        set(value): Unit = set(90, value)
        get(): Int? = get(90) as Int?

    open var leaseContractOutputCount: Byte?
        set(value): Unit = set(91, value)
        get(): Byte? = get(91) as Byte?

    open var leaseContractLatestOutputDate: Int?
        set(value): Unit = set(92, value)
        get(): Int? = get(92) as Int?

    open var leaseContractCollectionDate: Int?
        set(value): Unit = set(93, value)
        get(): Int? = get(93) as Int?

    open var leaseContractDate: Int?
        set(value): Unit = set(94, value)
        get(): Int? = get(94) as Int?

    open var contractStartDate: Int?
        set(value): Unit = set(95, value)
        get(): Int? = get(95) as Int?

    open var contractExpiryDate: Int?
        set(value): Unit = set(96, value)
        get(): Int? = get(96) as Int?

    open var nextRentRevisionScheduledDate: Int?
        set(value): Unit = set(97, value)
        get(): Int? = get(97) as Int?

    open var rentRevisionPeriod: Short?
        set(value): Unit = set(98, value)
        get(): Short? = get(98) as Short?

    open var frontFreeRentDays: BigDecimal?
        set(value): Unit = set(99, value)
        get(): BigDecimal? = get(99) as BigDecimal?

    open var depositMonths: BigDecimal?
        set(value): Unit = set(100, value)
        get(): BigDecimal? = get(100) as BigDecimal?

    open var depreciationMonths: BigDecimal?
        set(value): Unit = set(101, value)
        get(): BigDecimal? = get(101) as BigDecimal?

    open var keyMoneyAmount: Int?
        set(value): Unit = set(102, value)
        get(): Int? = get(102) as Int?

    open var keyMoneyDivision: String?
        set(value): Unit = set(103, value)
        get(): String? = get(103) as String?

    open var additionalKeyMoneyAmount: Int?
        set(value): Unit = set(104, value)
        get(): Int? = get(104) as Int?

    open var depositAmount: Int?
        set(value): Unit = set(105, value)
        get(): Int? = get(105) as Int?

    open var depreciation: Int?
        set(value): Unit = set(106, value)
        get(): Int? = get(106) as Int?

    open var rent: Int?
        set(value): Unit = set(107, value)
        get(): Int? = get(107) as Int?

    open var rentDivision: String?
        set(value): Unit = set(108, value)
        get(): String? = get(108) as String?

    open var parkingFeeAggregationSign: Byte?
        set(value): Unit = set(109, value)
        get(): Byte? = get(109) as Byte?

    open var parkingFee: Int?
        set(value): Unit = set(110, value)
        get(): Int? = get(110) as Int?

    open var parkingFeeDivision: String?
        set(value): Unit = set(111, value)
        get(): String? = get(111) as String?

    open var commonServiceFee: Int?
        set(value): Unit = set(112, value)
        get(): Int? = get(112) as Int?

    open var commonServiceFeeDivision: String?
        set(value): Unit = set(113, value)
        get(): String? = get(113) as String?

    open var neighborhoodAssociationFee: Int?
        set(value): Unit = set(114, value)
        get(): Int? = get(114) as Int?

    open var additionalDepositAmount: Int?
        set(value): Unit = set(115, value)
        get(): Int? = get(115) as Int?

    open var paymentScheduleCreationSign: Byte?
        set(value): Unit = set(116, value)
        get(): Byte? = get(116) as Byte?

    open var paymentMethodDivision2: String?
        set(value): Unit = set(117, value)
        get(): String? = get(117) as String?

    open var differenceProratedDays: Short?
        set(value): Unit = set(118, value)
        get(): Short? = get(118) as Short?

    open var differenceProratedRentAmount: Int?
        set(value): Unit = set(119, value)
        get(): Int? = get(119) as Int?

    open var differenceProratedParkingFee: Int?
        set(value): Unit = set(120, value)
        get(): Int? = get(120) as Int?

    open var differenceProratedManagementFee: Int?
        set(value): Unit = set(121, value)
        get(): Int? = get(121) as Int?

    open var differenceProratedCommonServiceFee: Int?
        set(value): Unit = set(122, value)
        get(): Int? = get(122) as Int?

    open var differenceProratedAssociationFee: Int?
        set(value): Unit = set(123, value)
        get(): Int? = get(123) as Int?

    open var differenceProratedWaterManagementFee: Int?
        set(value): Unit = set(124, value)
        get(): Int? = get(124) as Int?

    open var differenceMonths: Byte?
        set(value): Unit = set(125, value)
        get(): Byte? = get(125) as Byte?

    open var monthlyDifferenceRentAmount: Int?
        set(value): Unit = set(126, value)
        get(): Int? = get(126) as Int?

    open var monthlyDifferenceParkingFee: Int?
        set(value): Unit = set(127, value)
        get(): Int? = get(127) as Int?

    open var monthlyDifferenceCommonServiceFee: Int?
        set(value): Unit = set(128, value)
        get(): Int? = get(128) as Int?

    open var monthlyDifferenceManagementFee: Int?
        set(value): Unit = set(129, value)
        get(): Int? = get(129) as Int?

    open var monthlyDifferenceAssociationFee: Int?
        set(value): Unit = set(130, value)
        get(): Int? = get(130) as Int?

    open var monthlyDifferenceWaterManagementFee: Int?
        set(value): Unit = set(131, value)
        get(): Int? = get(131) as Int?

    open var paymentScheduleCreationSign2: Byte?
        set(value): Unit = set(132, value)
        get(): Byte? = get(132) as Byte?

    open var differenceRentCollectionMethod: String?
        set(value): Unit = set(133, value)
        get(): String? = get(133) as String?

    open var rentCollectionMethodDivision: String?
        set(value): Unit = set(134, value)
        get(): String? = get(134) as String?

    open var agencyDivision: String?
        set(value): Unit = set(135, value)
        get(): String? = get(135) as String?

    open var bankCode: String?
        set(value): Unit = set(136, value)
        get(): String? = get(136) as String?

    open var bankBranchCode: String?
        set(value): Unit = set(137, value)
        get(): String? = get(137) as String?

    open var accountType: String?
        set(value): Unit = set(138, value)
        get(): String? = get(138) as String?

    open var accountNumber: String?
        set(value): Unit = set(139, value)
        get(): String? = get(139) as String?

    open var accountHolderNameKana: String?
        set(value): Unit = set(140, value)
        get(): String? = get(140) as String?

    open var accountHolderNameKanji: String?
        set(value): Unit = set(141, value)
        get(): String? = get(141) as String?

    open var initialChangeEffectiveDate: Int?
        set(value): Unit = set(142, value)
        get(): Int? = get(142) as Int?

    open var bankCode2: String?
        set(value): Unit = set(143, value)
        get(): String? = get(143) as String?

    open var bankBranchCode2: String?
        set(value): Unit = set(144, value)
        get(): String? = get(144) as String?

    open var accountType2: String?
        set(value): Unit = set(145, value)
        get(): String? = get(145) as String?

    open var accountNumber2: String?
        set(value): Unit = set(146, value)
        get(): String? = get(146) as String?

    open var accountHolderNameKana2: String?
        set(value): Unit = set(147, value)
        get(): String? = get(147) as String?

    open var accountHolderNameKanji2: String?
        set(value): Unit = set(148, value)
        get(): String? = get(148) as String?

    open var directDebitTargetDivision: String?
        set(value): Unit = set(149, value)
        get(): String? = get(149) as String?

    open var directDebitAmount: Int?
        set(value): Unit = set(150, value)
        get(): Int? = get(150) as Int?

    open var leaseContractTransactingAgent: String?
        set(value): Unit = set(151, value)
        get(): String? = get(151) as String?

    open var contractEffectiveStartDate: Int?
        set(value): Unit = set(152, value)
        get(): Int? = get(152) as Int?

    open var contractEffectiveEndDate: Int?
        set(value): Unit = set(153, value)
        get(): Int? = get(153) as Int?

    open var frontFreeRentSign: Byte?
        set(value): Unit = set(154, value)
        get(): Byte? = get(154) as Byte?

    open var frontFreeRentAmount: Int?
        set(value): Unit = set(155, value)
        get(): Int? = get(155) as Int?

    open var frontFreeRentMonths: Short?
        set(value): Unit = set(156, value)
        get(): Short? = get(156) as Short?

    open var landlordAdvancePaymentAllocationAmount: Int?
        set(value): Unit = set(157, value)
        get(): Int? = get(157) as Int?

    open var landlordAdvancePayment: Int?
        set(value): Unit = set(158, value)
        get(): Int? = get(158) as Int?

    open var mngContractInitialPaymentAmount: Int?
        set(value): Unit = set(159, value)
        get(): Int? = get(159) as Int?

    open var mngContractInitialPaymentAmountTax: Int?
        set(value): Unit = set(160, value)
        get(): Int? = get(160) as Int?

    open var associationEntryFeeProcess: Int?
        set(value): Unit = set(161, value)
        get(): Int? = get(161) as Int?

    open var registrationFeeAmount: Int?
        set(value): Unit = set(162, value)
        get(): Int? = get(162) as Int?

    open var registrationFeeAmountTax: Int?
        set(value): Unit = set(163, value)
        get(): Int? = get(163) as Int?

    open var mngContractInitialPaymentTransferred: Byte?
        set(value): Unit = set(164, value)
        get(): Byte? = get(164) as Byte?

    open var brokerageFeeExemptionDivision: String?
        set(value): Unit = set(165, value)
        get(): String? = get(165) as String?

    open var rentManagementStartDate: Int?
        set(value): Unit = set(166, value)
        get(): Int? = get(166) as Int?

    open var notarizedDocPaymentSign: Byte?
        set(value): Unit = set(167, value)
        get(): Byte? = get(167) as Byte?

    open var moveInCalculationOutputDate: Int?
        set(value): Unit = set(168, value)
        get(): Int? = get(168) as Int?

    open var remainingRent: Int?
        set(value): Unit = set(169, value)
        get(): Int? = get(169) as Int?

    open var remainingRentTax: Int?
        set(value): Unit = set(170, value)
        get(): Int? = get(170) as Int?

    open var remainingParkingFee: Int?
        set(value): Unit = set(171, value)
        get(): Int? = get(171) as Int?

    open var remainingParkingFeeTax: Int?
        set(value): Unit = set(172, value)
        get(): Int? = get(172) as Int?

    open var remainingCommonServiceFee: Int?
        set(value): Unit = set(173, value)
        get(): Int? = get(173) as Int?

    open var remainingCommonServiceFeeTax: Int?
        set(value): Unit = set(174, value)
        get(): Int? = get(174) as Int?

    open var remainingNeighborhoodFee: Int?
        set(value): Unit = set(175, value)
        get(): Int? = get(175) as Int?

    open var remainingMonths: Short?
        set(value): Unit = set(176, value)
        get(): Short? = get(176) as Short?

    open var proratedRent: Int?
        set(value): Unit = set(177, value)
        get(): Int? = get(177) as Int?

    open var proratedRentTax: Int?
        set(value): Unit = set(178, value)
        get(): Int? = get(178) as Int?

    open var proratedParking: Int?
        set(value): Unit = set(179, value)
        get(): Int? = get(179) as Int?

    open var proratedParkingFeeTax: Int?
        set(value): Unit = set(180, value)
        get(): Int? = get(180) as Int?

    open var proratedCommonServiceFee: Int?
        set(value): Unit = set(181, value)
        get(): Int? = get(181) as Int?

    open var proratedCommonServiceFeeTax: Int?
        set(value): Unit = set(182, value)
        get(): Int? = get(182) as Int?

    open var proratedNeighborhoodFee: Int?
        set(value): Unit = set(183, value)
        get(): Int? = get(183) as Int?

    open var keyMoneyAmount2: Int?
        set(value): Unit = set(184, value)
        get(): Int? = get(184) as Int?

    open var keyMoneyTax: Int?
        set(value): Unit = set(185, value)
        get(): Int? = get(185) as Int?

    open var depositAmount2: Int?
        set(value): Unit = set(186, value)
        get(): Int? = get(186) as Int?

    open var monthlyManagementFee: Int?
        set(value): Unit = set(187, value)
        get(): Int? = get(187) as Int?

    open var notarizedDocCreationCost: Int?
        set(value): Unit = set(188, value)
        get(): Int? = get(188) as Int?

    open var stampFee: Int?
        set(value): Unit = set(189, value)
        get(): Int? = get(189) as Int?

    open var interiorCooperationFee: Int?
        set(value): Unit = set(190, value)
        get(): Int? = get(190) as Int?

    open var interiorCooperationFeeTax: Int?
        set(value): Unit = set(191, value)
        get(): Int? = get(191) as Int?

    open var incomeCommissionFeeTenant: Int?
        set(value): Unit = set(192, value)
        get(): Int? = get(192) as Int?

    open var incomeCommissionFeeTax: Int?
        set(value): Unit = set(193, value)
        get(): Int? = get(193) as Int?

    open var depositCommissionFee: Int?
        set(value): Unit = set(194, value)
        get(): Int? = get(194) as Int?

    open var depositCommissionFeeTax: Int?
        set(value): Unit = set(195, value)
        get(): Int? = get(195) as Int?

    open var brokerageFee: Int?
        set(value): Unit = set(196, value)
        get(): Int? = get(196) as Int?

    open var brokerageFeeTax: Int?
        set(value): Unit = set(197, value)
        get(): Int? = get(197) as Int?

    open var outsourcedAdvertisingFee: Int?
        set(value): Unit = set(198, value)
        get(): Int? = get(198) as Int?

    open var outsourcedAdvertisingFeeTax: Int?
        set(value): Unit = set(199, value)
        get(): Int? = get(199) as Int?

    open var paymentSign: Byte?
        set(value): Unit = set(200, value)
        get(): Byte? = get(200) as Byte?

    open var brokerCode: String?
        set(value): Unit = set(201, value)
        get(): String? = get(201) as String?

    open var brokerageFeeBreakdownDivision: String?
        set(value): Unit = set(202, value)
        get(): String? = get(202) as String?

    open var remainingAmount: Long?
        set(value): Unit = set(203, value)
        get(): Long? = get(203) as Long?

    open var remainingScheduledDate: Int?
        set(value): Unit = set(204, value)
        get(): Int? = get(204) as Int?

    open var remainingDate: Int?
        set(value): Unit = set(205, value)
        get(): Int? = get(205) as Int?

    open var remainingApprovalDate: Int?
        set(value): Unit = set(206, value)
        get(): Int? = get(206) as Int?

    open var offsetAmountManagementFee: Int?
        set(value): Unit = set(207, value)
        get(): Int? = get(207) as Int?

    open var offsetAmountManagementFeeTax: Int?
        set(value): Unit = set(208, value)
        get(): Int? = get(208) as Int?

    open var offsetAmountAssociationFee: Int?
        set(value): Unit = set(209, value)
        get(): Int? = get(209) as Int?

    open var offsetAmountMaintenanceFee: Int?
        set(value): Unit = set(210, value)
        get(): Int? = get(210) as Int?

    open var offsetAmountMaintenanceFeeTax: Int?
        set(value): Unit = set(211, value)
        get(): Int? = get(211) as Int?

    open var neighborhoodFeeDaitoPayment: Int?
        set(value): Unit = set(212, value)
        get(): Int? = get(212) as Int?

    open var offsetAmountWaterManagementFee: Int?
        set(value): Unit = set(213, value)
        get(): Int? = get(213) as Int?

    open var offsetAmountTax: Int?
        set(value): Unit = set(214, value)
        get(): Int? = get(214) as Int?

    open var offsetAmountProratedManagementFee: Int?
        set(value): Unit = set(215, value)
        get(): Int? = get(215) as Int?

    open var proratedManagementFeeTax: Int?
        set(value): Unit = set(216, value)
        get(): Int? = get(216) as Int?

    open var offsetAmountProratedAssociation: Int?
        set(value): Unit = set(217, value)
        get(): Int? = get(217) as Int?

    open var offsetAmountProratedMaintenance: Int?
        set(value): Unit = set(218, value)
        get(): Int? = get(218) as Int?

    open var maintenanceProratedTax: Int?
        set(value): Unit = set(219, value)
        get(): Int? = get(219) as Int?

    open var proratedNeighborhoodFeeDaito: Int?
        set(value): Unit = set(220, value)
        get(): Int? = get(220) as Int?

    open var offsetAmountProratedWaterManagement: Int?
        set(value): Unit = set(221, value)
        get(): Int? = get(221) as Int?

    open var offsetAmountTax2: Int?
        set(value): Unit = set(222, value)
        get(): Int? = get(222) as Int?

    open var managementContractInitialPayment: Int?
        set(value): Unit = set(223, value)
        get(): Int? = get(223) as Int?

    open var managementContractInitialPaymentTax: Int?
        set(value): Unit = set(224, value)
        get(): Int? = get(224) as Int?

    open var tenantRegistrationFee: Int?
        set(value): Unit = set(225, value)
        get(): Int? = get(225) as Int?

    open var tenantRegistrationFeeTax: Int?
        set(value): Unit = set(226, value)
        get(): Int? = get(226) as Int?

    open var associationEntryFee: Int?
        set(value): Unit = set(227, value)
        get(): Int? = get(227) as Int?

    open var notarizedDocCreationCost2: Int?
        set(value): Unit = set(228, value)
        get(): Int? = get(228) as Int?

    open var stampFee2: Int?
        set(value): Unit = set(229, value)
        get(): Int? = get(229) as Int?

    open var moveInSettlementAmount: Int?
        set(value): Unit = set(230, value)
        get(): Int? = get(230) as Int?

    open var moveInSettlementDate: Int?
        set(value): Unit = set(231, value)
        get(): Int? = get(231) as Int?

    open var tenantSettlementPaymentScheduleDiv: String?
        set(value): Unit = set(232, value)
        get(): String? = get(232) as String?

    open var tenantAddressOverrideDivision: String?
        set(value): Unit = set(233, value)
        get(): String? = get(233) as String?

    open var postMoveContactPhone: String?
        set(value): Unit = set(234, value)
        get(): String? = get(234) as String?

    open var fireInsuranceFee: Int?
        set(value): Unit = set(235, value)
        get(): Int? = get(235) as Int?

    open var keyHandoverDate: Int?
        set(value): Unit = set(236, value)
        get(): Int? = get(236) as Int?

    open var renovationApplicationSign: Byte?
        set(value): Unit = set(237, value)
        get(): Byte? = get(237) as Byte?

    open var managementFeeTax: Int?
        set(value): Unit = set(238, value)
        get(): Int? = get(238) as Int?

    open var cooperationFeeOffsetMonths: Short?
        set(value): Unit = set(239, value)
        get(): Short? = get(239) as Short?

    open var reservationContractFeeAllocationAmount: Int?
        set(value): Unit = set(240, value)
        get(): Int? = get(240) as Int?

    open var cancellationSign: Byte?
        set(value): Unit = set(241, value)
        get(): Byte? = get(241) as Byte?

    open var moveInStartProcessedSign: Byte?
        set(value): Unit = set(242, value)
        get(): Byte? = get(242) as Byte?

    open var vacateNoticeDate: Int?
        set(value): Unit = set(243, value)
        get(): Int? = get(243) as Int?

    open var vacateScheduledDate: Int?
        set(value): Unit = set(244, value)
        get(): Int? = get(244) as Int?

    open var breachPeriodExpiryDate: Int?
        set(value): Unit = set(245, value)
        get(): Int? = get(245) as Int?

    open var moveOutSettlementDateLandlord: Int?
        set(value): Unit = set(246, value)
        get(): Int? = get(246) as Int?

    open var moveOutSettlementDateTenant: Int?
        set(value): Unit = set(247, value)
        get(): Int? = get(247) as Int?

    open var associationBenefitStartDate: Int?
        set(value): Unit = set(248, value)
        get(): Int? = get(248) as Int?

    open var restorationCompletionDate: Int?
        set(value): Unit = set(249, value)
        get(): Int? = get(249) as Int?

    open var moveOutDate: Int?
        set(value): Unit = set(250, value)
        get(): Int? = get(250) as Int?

    open var breachYearMonth: Int?
        set(value): Unit = set(251, value)
        get(): Int? = get(251) as Int?

    open var breachPeriodDays: Short?
        set(value): Unit = set(252, value)
        get(): Short? = get(252) as Short?

    open var breachPeriodStartDate: Int?
        set(value): Unit = set(253, value)
        get(): Int? = get(253) as Int?

    open var breachPeriodEndDate: Int?
        set(value): Unit = set(254, value)
        get(): Int? = get(254) as Int?

    open var prefectureCode4: String?
        set(value): Unit = set(255, value)
        get(): String? = get(255) as String?

    open var cityCode4: String?
        set(value): Unit = set(256, value)
        get(): String? = get(256) as String?

    open var townCode4: String?
        set(value): Unit = set(257, value)
        get(): String? = get(257) as String?

    open var addressDetail4: String?
        set(value): Unit = set(258, value)
        get(): String? = get(258) as String?

    open var remarks2: String?
        set(value): Unit = set(259, value)
        get(): String? = get(259) as String?

    open var bankCode3: String?
        set(value): Unit = set(260, value)
        get(): String? = get(260) as String?

    open var bankBranchCode3: String?
        set(value): Unit = set(261, value)
        get(): String? = get(261) as String?

    open var accountType3: String?
        set(value): Unit = set(262, value)
        get(): String? = get(262) as String?

    open var accountNumber3: String?
        set(value): Unit = set(263, value)
        get(): String? = get(263) as String?

    open var accountHolderNameKana3: String?
        set(value): Unit = set(264, value)
        get(): String? = get(264) as String?

    open var accountHolderNameKanji3: String?
        set(value): Unit = set(265, value)
        get(): String? = get(265) as String?

    open var onSiteConfirmationDate: Byte?
        set(value): Unit = set(266, value)
        get(): Byte? = get(266) as Byte?

    open var restorationWorkExistenceSign: Byte?
        set(value): Unit = set(267, value)
        get(): Byte? = get(267) as Byte?

    open var constructionDivision: String?
        set(value): Unit = set(268, value)
        get(): String? = get(268) as String?

    open var actualPaymentDivision: String?
        set(value): Unit = set(269, value)
        get(): String? = get(269) as String?

    open var constructionOrderNumber: String?
        set(value): Unit = set(270, value)
        get(): String? = get(270) as String?

    open var maintenanceWorkSign: Byte?
        set(value): Unit = set(271, value)
        get(): Byte? = get(271) as Byte?

    open var constructionOrderNumber2: String?
        set(value): Unit = set(272, value)
        get(): String? = get(272) as String?

    open var contractDocumentDivision: String?
        set(value): Unit = set(273, value)
        get(): String? = get(273) as String?

    open var rentArrearsMonths: Short?
        set(value): Unit = set(274, value)
        get(): Short? = get(274) as Short?

    open var rentArrearsAmount: Int?
        set(value): Unit = set(275, value)
        get(): Int? = get(275) as Int?

    open var depositRetainedAmount: Int?
        set(value): Unit = set(276, value)
        get(): Int? = get(276) as Int?

    open var advanceRentPaymentRequestFinal: Int?
        set(value): Unit = set(277, value)
        get(): Int? = get(277) as Int?

    open var rentBillingFinalCreationYear: Int?
        set(value): Unit = set(278, value)
        get(): Int? = get(278) as Int?

    open var companyCode: String?
        set(value): Unit = set(279, value)
        get(): String? = get(279) as String?

    open var branchCode: String?
        set(value): Unit = set(280, value)
        get(): String? = get(280) as String?

    open var directSupervisorCode: String?
        set(value): Unit = set(281, value)
        get(): String? = get(281) as String?

    open var employeeCode: String?
        set(value): Unit = set(282, value)
        get(): String? = get(282) as String?

    open var currentResponsibleShozokuCode: String?
        set(value): Unit = set(283, value)
        get(): String? = get(283) as String?

    open var currentResponsibleBranchCode: String?
        set(value): Unit = set(284, value)
        get(): String? = get(284) as String?

    open var salesPerformanceShozokuCode: String?
        set(value): Unit = set(285, value)
        get(): String? = get(285) as String?

    open var salesPerformanceBranchCode: String?
        set(value): Unit = set(286, value)
        get(): String? = get(286) as String?

    open var companyCode2: String?
        set(value): Unit = set(287, value)
        get(): String? = get(287) as String?

    open var baseCode: String?
        set(value): Unit = set(288, value)
        get(): String? = get(288) as String?

    open var directSupervisorCode2: String?
        set(value): Unit = set(289, value)
        get(): String? = get(289) as String?

    open var employeeCode2: String?
        set(value): Unit = set(290, value)
        get(): String? = get(290) as String?

    open var customerResponsibleBranchCode: String?
        set(value): Unit = set(291, value)
        get(): String? = get(291) as String?

    open var journalEntrySeq: Short?
        set(value): Unit = set(292, value)
        get(): Short? = get(292) as Short?

    open var previousStateDivision: String?
        set(value): Unit = set(293, value)
        get(): String? = get(293) as String?

    open var currentStateDivision: String?
        set(value): Unit = set(294, value)
        get(): String? = get(294) as String?

    open var modificationStateDivision: String?
        set(value): Unit = set(295, value)
        get(): String? = get(295) as String?

    open var interfaceSign: Byte?
        set(value): Unit = set(296, value)
        get(): Byte? = get(296) as Byte?

    open var responseReceipt: String?
        set(value): Unit = set(297, value)
        get(): String? = get(297) as String?

    open var satelliteCode: String?
        set(value): Unit = set(298, value)
        get(): String? = get(298) as String?

    open var responseReceiptDate: Int?
        set(value): Unit = set(299, value)
        get(): Int? = get(299) as Int?

    open var salesOfficeStaff: String?
        set(value): Unit = set(300, value)
        get(): String? = get(300) as String?

    open var parkingAggregationDivision: String?
        set(value): Unit = set(301, value)
        get(): String? = get(301) as String?

    open var ledgerNo: Short?
        set(value): Unit = set(302, value)
        get(): Short? = get(302) as Short?

    open var guarantorNotRequiredDivision: Byte?
        set(value): Unit = set(303, value)
        get(): Byte? = get(303) as Byte?

    open var communicationPartnerDivision: Byte?
        set(value): Unit = set(304, value)
        get(): Byte? = get(304) as Byte?

    open var nonStandardDivision: Byte?
        set(value): Unit = set(305, value)
        get(): Byte? = get(305) as Byte?

    open var contractRenewalImplementer: Byte?
        set(value): Unit = set(306, value)
        get(): Byte? = get(306) as Byte?

    open var corporateHousingAgencySign: Byte?
        set(value): Unit = set(307, value)
        get(): Byte? = get(307) as Byte?

    open var ffPaymentSign: Byte?
        set(value): Unit = set(308, value)
        get(): Byte? = get(308) as Byte?

    open var rentalDivision: Byte?
        set(value): Unit = set(309, value)
        get(): Byte? = get(309) as Byte?

    open var unused6: Byte?
        set(value): Unit = set(310, value)
        get(): Byte? = get(310) as Byte?

    open var unused7: Byte?
        set(value): Unit = set(311, value)
        get(): Byte? = get(311) as Byte?

    open var unused8: Byte?
        set(value): Unit = set(312, value)
        get(): Byte? = get(312) as Byte?

    open var unused9: Byte?
        set(value): Unit = set(313, value)
        get(): Byte? = get(313) as Byte?

    open var contractRent: Int?
        set(value): Unit = set(314, value)
        get(): Int? = get(314) as Int?

    open var specialRentalDivision: String?
        set(value): Unit = set(315, value)
        get(): String? = get(315) as String?

    open var responseReceiver: String?
        set(value): Unit = set(316, value)
        get(): String? = get(316) as String?

    open var parkingContractFee: Int?
        set(value): Unit = set(317, value)
        get(): Int? = get(317) as Int?

    open var parkingContractFeeTax: Int?
        set(value): Unit = set(318, value)
        get(): Int? = get(318) as Int?

    open var parkingContractFeeExemptionDivision: String?
        set(value): Unit = set(319, value)
        get(): String? = get(319) as String?

    open var incomeParkingFeeTenant: Int?
        set(value): Unit = set(320, value)
        get(): Int? = get(320) as Int?

    open var incomeParkingContractFeeTax: Int?
        set(value): Unit = set(321, value)
        get(): Int? = get(321) as Int?

    open var depositParkingContractFee: Int?
        set(value): Unit = set(322, value)
        get(): Int? = get(322) as Int?

    open var depositParkingContractFeeTax: Int?
        set(value): Unit = set(323, value)
        get(): Int? = get(323) as Int?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised TenantContractRecord
     */
    constructor(value: TenantContractPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteSign = value.logicalDeleteSign
            this.tenantContractNumber = value.tenantContractNumber
            this.tenantContractChangeSeq = value.tenantContractChangeSeq
            this.contractContentDivision = value.contractContentDivision
            this.buildingCode = value.buildingCode
            this.roomCode = value.roomCode
            this.parkingCode = value.parkingCode
            this.aggregateContractNumber = value.aggregateContractNumber
            this.aggregateContractChangeSeq = value.aggregateContractChangeSeq
            this.parkingSpaces = value.parkingSpaces
            this.bulkLeaseSign = value.bulkLeaseSign
            this.tenantType = value.tenantType
            this.tenantProspectNumber = value.tenantProspectNumber
            this.tenantCode = value.tenantCode
            this.searchKana = value.searchKana
            this.infoAcquisitionDivision = value.infoAcquisitionDivision
            this.hotInfoReceiptNumber = value.hotInfoReceiptNumber
            this.landlordCode_10 = value.landlordCode_10
            this.taxDivision = value.taxDivision
            this.searchKana2 = value.searchKana2
            this.name = value.name
            this.prefectureCode = value.prefectureCode
            this.cityCode = value.cityCode
            this.townCode = value.townCode
            this.addressDetail = value.addressDetail
            this.buildingName = value.buildingName
            this.phoneNumber = value.phoneNumber
            this.importantDisclosureOutputCount = value.importantDisclosureOutputCount
            this.importantDisclosureOutputDate = value.importantDisclosureOutputDate
            this.importantDisclosureCollectionDate = value.importantDisclosureCollectionDate
            this.importantDisclosureDate = value.importantDisclosureDate
            this.importantDisclosureAgentCode = value.importantDisclosureAgentCode
            this.importantDisclosureDivision = value.importantDisclosureDivision
            this.paymentDate = value.paymentDate
            this.moveInApplicationDate = value.moveInApplicationDate
            this.moveInScheduledDate = value.moveInScheduledDate
            this.usagePurpose = value.usagePurpose
            this.moveInApplicationFee = value.moveInApplicationFee
            this.paymentMethodDivision = value.paymentMethodDivision
            this.mobileReceiptNumber = value.mobileReceiptNumber
            this.reservationApplicationFeeAppliedAmount = value.reservationApplicationFeeAppliedAmount
            this.tenantName = value.tenantName
            this.contactDivision = value.contactDivision
            this.contact = value.contact
            this.phoneNumber2 = value.phoneNumber2
            this.remarks = value.remarks
            this.name2 = value.name2
            this.prefectureCode2 = value.prefectureCode2
            this.cityCode2 = value.cityCode2
            this.townCode2 = value.townCode2
            this.addressDetail2 = value.addressDetail2
            this.buildingName2 = value.buildingName2
            this.phoneNumber3 = value.phoneNumber3
            this.relationship = value.relationship
            this.name3 = value.name3
            this.prefectureCode3 = value.prefectureCode3
            this.cityCode3 = value.cityCode3
            this.townCode3 = value.townCode3
            this.addressDetail3 = value.addressDetail3
            this.buildingName3 = value.buildingName3
            this.phoneNumber4 = value.phoneNumber4
            this.relationship2 = value.relationship2
            this.cohabitantName1 = value.cohabitantName1
            this.cohabitantAge1 = value.cohabitantAge1
            this.cohabitantRelationship1 = value.cohabitantRelationship1
            this.cohabitantName2 = value.cohabitantName2
            this.cohabitantAge2 = value.cohabitantAge2
            this.cohabitantRelationship2 = value.cohabitantRelationship2
            this.cohabitantName3 = value.cohabitantName3
            this.agentShozokuBranch = value.agentShozokuBranch
            this.cohabitantRelationship3 = value.cohabitantRelationship3
            this.cohabitantName4 = value.cohabitantName4
            this.agentShozokuStore = value.agentShozokuStore
            this.cohabitantRelationship4 = value.cohabitantRelationship4
            this.cohabitantName5 = value.cohabitantName5
            this.agentShozokuOffice = value.agentShozokuOffice
            this.cohabitantRelationship5 = value.cohabitantRelationship5
            this.landlordApprovalDate = value.landlordApprovalDate
            this.depositChangeDate = value.depositChangeDate
            this.agencyMemberNumber = value.agencyMemberNumber
            this.contractPeriodDivision = value.contractPeriodDivision
            this.reservationContractSign = value.reservationContractSign
            this.tenantReservationNumber = value.tenantReservationNumber
            this.leaseContractOutputContentDivision = value.leaseContractOutputContentDivision
            this.standardRent = value.standardRent
            this.leaseContractOutputCount = value.leaseContractOutputCount
            this.leaseContractLatestOutputDate = value.leaseContractLatestOutputDate
            this.leaseContractCollectionDate = value.leaseContractCollectionDate
            this.leaseContractDate = value.leaseContractDate
            this.contractStartDate = value.contractStartDate
            this.contractExpiryDate = value.contractExpiryDate
            this.nextRentRevisionScheduledDate = value.nextRentRevisionScheduledDate
            this.rentRevisionPeriod = value.rentRevisionPeriod
            this.frontFreeRentDays = value.frontFreeRentDays
            this.depositMonths = value.depositMonths
            this.depreciationMonths = value.depreciationMonths
            this.keyMoneyAmount = value.keyMoneyAmount
            this.keyMoneyDivision = value.keyMoneyDivision
            this.additionalKeyMoneyAmount = value.additionalKeyMoneyAmount
            this.depositAmount = value.depositAmount
            this.depreciation = value.depreciation
            this.rent = value.rent
            this.rentDivision = value.rentDivision
            this.parkingFeeAggregationSign = value.parkingFeeAggregationSign
            this.parkingFee = value.parkingFee
            this.parkingFeeDivision = value.parkingFeeDivision
            this.commonServiceFee = value.commonServiceFee
            this.commonServiceFeeDivision = value.commonServiceFeeDivision
            this.neighborhoodAssociationFee = value.neighborhoodAssociationFee
            this.additionalDepositAmount = value.additionalDepositAmount
            this.paymentScheduleCreationSign = value.paymentScheduleCreationSign
            this.paymentMethodDivision2 = value.paymentMethodDivision2
            this.differenceProratedDays = value.differenceProratedDays
            this.differenceProratedRentAmount = value.differenceProratedRentAmount
            this.differenceProratedParkingFee = value.differenceProratedParkingFee
            this.differenceProratedManagementFee = value.differenceProratedManagementFee
            this.differenceProratedCommonServiceFee = value.differenceProratedCommonServiceFee
            this.differenceProratedAssociationFee = value.differenceProratedAssociationFee
            this.differenceProratedWaterManagementFee = value.differenceProratedWaterManagementFee
            this.differenceMonths = value.differenceMonths
            this.monthlyDifferenceRentAmount = value.monthlyDifferenceRentAmount
            this.monthlyDifferenceParkingFee = value.monthlyDifferenceParkingFee
            this.monthlyDifferenceCommonServiceFee = value.monthlyDifferenceCommonServiceFee
            this.monthlyDifferenceManagementFee = value.monthlyDifferenceManagementFee
            this.monthlyDifferenceAssociationFee = value.monthlyDifferenceAssociationFee
            this.monthlyDifferenceWaterManagementFee = value.monthlyDifferenceWaterManagementFee
            this.paymentScheduleCreationSign2 = value.paymentScheduleCreationSign2
            this.differenceRentCollectionMethod = value.differenceRentCollectionMethod
            this.rentCollectionMethodDivision = value.rentCollectionMethodDivision
            this.agencyDivision = value.agencyDivision
            this.bankCode = value.bankCode
            this.bankBranchCode = value.bankBranchCode
            this.accountType = value.accountType
            this.accountNumber = value.accountNumber
            this.accountHolderNameKana = value.accountHolderNameKana
            this.accountHolderNameKanji = value.accountHolderNameKanji
            this.initialChangeEffectiveDate = value.initialChangeEffectiveDate
            this.bankCode2 = value.bankCode2
            this.bankBranchCode2 = value.bankBranchCode2
            this.accountType2 = value.accountType2
            this.accountNumber2 = value.accountNumber2
            this.accountHolderNameKana2 = value.accountHolderNameKana2
            this.accountHolderNameKanji2 = value.accountHolderNameKanji2
            this.directDebitTargetDivision = value.directDebitTargetDivision
            this.directDebitAmount = value.directDebitAmount
            this.leaseContractTransactingAgent = value.leaseContractTransactingAgent
            this.contractEffectiveStartDate = value.contractEffectiveStartDate
            this.contractEffectiveEndDate = value.contractEffectiveEndDate
            this.frontFreeRentSign = value.frontFreeRentSign
            this.frontFreeRentAmount = value.frontFreeRentAmount
            this.frontFreeRentMonths = value.frontFreeRentMonths
            this.landlordAdvancePaymentAllocationAmount = value.landlordAdvancePaymentAllocationAmount
            this.landlordAdvancePayment = value.landlordAdvancePayment
            this.mngContractInitialPaymentAmount = value.mngContractInitialPaymentAmount
            this.mngContractInitialPaymentAmountTax = value.mngContractInitialPaymentAmountTax
            this.associationEntryFeeProcess = value.associationEntryFeeProcess
            this.registrationFeeAmount = value.registrationFeeAmount
            this.registrationFeeAmountTax = value.registrationFeeAmountTax
            this.mngContractInitialPaymentTransferred = value.mngContractInitialPaymentTransferred
            this.brokerageFeeExemptionDivision = value.brokerageFeeExemptionDivision
            this.rentManagementStartDate = value.rentManagementStartDate
            this.notarizedDocPaymentSign = value.notarizedDocPaymentSign
            this.moveInCalculationOutputDate = value.moveInCalculationOutputDate
            this.remainingRent = value.remainingRent
            this.remainingRentTax = value.remainingRentTax
            this.remainingParkingFee = value.remainingParkingFee
            this.remainingParkingFeeTax = value.remainingParkingFeeTax
            this.remainingCommonServiceFee = value.remainingCommonServiceFee
            this.remainingCommonServiceFeeTax = value.remainingCommonServiceFeeTax
            this.remainingNeighborhoodFee = value.remainingNeighborhoodFee
            this.remainingMonths = value.remainingMonths
            this.proratedRent = value.proratedRent
            this.proratedRentTax = value.proratedRentTax
            this.proratedParking = value.proratedParking
            this.proratedParkingFeeTax = value.proratedParkingFeeTax
            this.proratedCommonServiceFee = value.proratedCommonServiceFee
            this.proratedCommonServiceFeeTax = value.proratedCommonServiceFeeTax
            this.proratedNeighborhoodFee = value.proratedNeighborhoodFee
            this.keyMoneyAmount2 = value.keyMoneyAmount2
            this.keyMoneyTax = value.keyMoneyTax
            this.depositAmount2 = value.depositAmount2
            this.monthlyManagementFee = value.monthlyManagementFee
            this.notarizedDocCreationCost = value.notarizedDocCreationCost
            this.stampFee = value.stampFee
            this.interiorCooperationFee = value.interiorCooperationFee
            this.interiorCooperationFeeTax = value.interiorCooperationFeeTax
            this.incomeCommissionFeeTenant = value.incomeCommissionFeeTenant
            this.incomeCommissionFeeTax = value.incomeCommissionFeeTax
            this.depositCommissionFee = value.depositCommissionFee
            this.depositCommissionFeeTax = value.depositCommissionFeeTax
            this.brokerageFee = value.brokerageFee
            this.brokerageFeeTax = value.brokerageFeeTax
            this.outsourcedAdvertisingFee = value.outsourcedAdvertisingFee
            this.outsourcedAdvertisingFeeTax = value.outsourcedAdvertisingFeeTax
            this.paymentSign = value.paymentSign
            this.brokerCode = value.brokerCode
            this.brokerageFeeBreakdownDivision = value.brokerageFeeBreakdownDivision
            this.remainingAmount = value.remainingAmount
            this.remainingScheduledDate = value.remainingScheduledDate
            this.remainingDate = value.remainingDate
            this.remainingApprovalDate = value.remainingApprovalDate
            this.offsetAmountManagementFee = value.offsetAmountManagementFee
            this.offsetAmountManagementFeeTax = value.offsetAmountManagementFeeTax
            this.offsetAmountAssociationFee = value.offsetAmountAssociationFee
            this.offsetAmountMaintenanceFee = value.offsetAmountMaintenanceFee
            this.offsetAmountMaintenanceFeeTax = value.offsetAmountMaintenanceFeeTax
            this.neighborhoodFeeDaitoPayment = value.neighborhoodFeeDaitoPayment
            this.offsetAmountWaterManagementFee = value.offsetAmountWaterManagementFee
            this.offsetAmountTax = value.offsetAmountTax
            this.offsetAmountProratedManagementFee = value.offsetAmountProratedManagementFee
            this.proratedManagementFeeTax = value.proratedManagementFeeTax
            this.offsetAmountProratedAssociation = value.offsetAmountProratedAssociation
            this.offsetAmountProratedMaintenance = value.offsetAmountProratedMaintenance
            this.maintenanceProratedTax = value.maintenanceProratedTax
            this.proratedNeighborhoodFeeDaito = value.proratedNeighborhoodFeeDaito
            this.offsetAmountProratedWaterManagement = value.offsetAmountProratedWaterManagement
            this.offsetAmountTax2 = value.offsetAmountTax2
            this.managementContractInitialPayment = value.managementContractInitialPayment
            this.managementContractInitialPaymentTax = value.managementContractInitialPaymentTax
            this.tenantRegistrationFee = value.tenantRegistrationFee
            this.tenantRegistrationFeeTax = value.tenantRegistrationFeeTax
            this.associationEntryFee = value.associationEntryFee
            this.notarizedDocCreationCost2 = value.notarizedDocCreationCost2
            this.stampFee2 = value.stampFee2
            this.moveInSettlementAmount = value.moveInSettlementAmount
            this.moveInSettlementDate = value.moveInSettlementDate
            this.tenantSettlementPaymentScheduleDiv = value.tenantSettlementPaymentScheduleDiv
            this.tenantAddressOverrideDivision = value.tenantAddressOverrideDivision
            this.postMoveContactPhone = value.postMoveContactPhone
            this.fireInsuranceFee = value.fireInsuranceFee
            this.keyHandoverDate = value.keyHandoverDate
            this.renovationApplicationSign = value.renovationApplicationSign
            this.managementFeeTax = value.managementFeeTax
            this.cooperationFeeOffsetMonths = value.cooperationFeeOffsetMonths
            this.reservationContractFeeAllocationAmount = value.reservationContractFeeAllocationAmount
            this.cancellationSign = value.cancellationSign
            this.moveInStartProcessedSign = value.moveInStartProcessedSign
            this.vacateNoticeDate = value.vacateNoticeDate
            this.vacateScheduledDate = value.vacateScheduledDate
            this.breachPeriodExpiryDate = value.breachPeriodExpiryDate
            this.moveOutSettlementDateLandlord = value.moveOutSettlementDateLandlord
            this.moveOutSettlementDateTenant = value.moveOutSettlementDateTenant
            this.associationBenefitStartDate = value.associationBenefitStartDate
            this.restorationCompletionDate = value.restorationCompletionDate
            this.moveOutDate = value.moveOutDate
            this.breachYearMonth = value.breachYearMonth
            this.breachPeriodDays = value.breachPeriodDays
            this.breachPeriodStartDate = value.breachPeriodStartDate
            this.breachPeriodEndDate = value.breachPeriodEndDate
            this.prefectureCode4 = value.prefectureCode4
            this.cityCode4 = value.cityCode4
            this.townCode4 = value.townCode4
            this.addressDetail4 = value.addressDetail4
            this.remarks2 = value.remarks2
            this.bankCode3 = value.bankCode3
            this.bankBranchCode3 = value.bankBranchCode3
            this.accountType3 = value.accountType3
            this.accountNumber3 = value.accountNumber3
            this.accountHolderNameKana3 = value.accountHolderNameKana3
            this.accountHolderNameKanji3 = value.accountHolderNameKanji3
            this.onSiteConfirmationDate = value.onSiteConfirmationDate
            this.restorationWorkExistenceSign = value.restorationWorkExistenceSign
            this.constructionDivision = value.constructionDivision
            this.actualPaymentDivision = value.actualPaymentDivision
            this.constructionOrderNumber = value.constructionOrderNumber
            this.maintenanceWorkSign = value.maintenanceWorkSign
            this.constructionOrderNumber2 = value.constructionOrderNumber2
            this.contractDocumentDivision = value.contractDocumentDivision
            this.rentArrearsMonths = value.rentArrearsMonths
            this.rentArrearsAmount = value.rentArrearsAmount
            this.depositRetainedAmount = value.depositRetainedAmount
            this.advanceRentPaymentRequestFinal = value.advanceRentPaymentRequestFinal
            this.rentBillingFinalCreationYear = value.rentBillingFinalCreationYear
            this.companyCode = value.companyCode
            this.branchCode = value.branchCode
            this.directSupervisorCode = value.directSupervisorCode
            this.employeeCode = value.employeeCode
            this.currentResponsibleShozokuCode = value.currentResponsibleShozokuCode
            this.currentResponsibleBranchCode = value.currentResponsibleBranchCode
            this.salesPerformanceShozokuCode = value.salesPerformanceShozokuCode
            this.salesPerformanceBranchCode = value.salesPerformanceBranchCode
            this.companyCode2 = value.companyCode2
            this.baseCode = value.baseCode
            this.directSupervisorCode2 = value.directSupervisorCode2
            this.employeeCode2 = value.employeeCode2
            this.customerResponsibleBranchCode = value.customerResponsibleBranchCode
            this.journalEntrySeq = value.journalEntrySeq
            this.previousStateDivision = value.previousStateDivision
            this.currentStateDivision = value.currentStateDivision
            this.modificationStateDivision = value.modificationStateDivision
            this.interfaceSign = value.interfaceSign
            this.responseReceipt = value.responseReceipt
            this.satelliteCode = value.satelliteCode
            this.responseReceiptDate = value.responseReceiptDate
            this.salesOfficeStaff = value.salesOfficeStaff
            this.parkingAggregationDivision = value.parkingAggregationDivision
            this.ledgerNo = value.ledgerNo
            this.guarantorNotRequiredDivision = value.guarantorNotRequiredDivision
            this.communicationPartnerDivision = value.communicationPartnerDivision
            this.nonStandardDivision = value.nonStandardDivision
            this.contractRenewalImplementer = value.contractRenewalImplementer
            this.corporateHousingAgencySign = value.corporateHousingAgencySign
            this.ffPaymentSign = value.ffPaymentSign
            this.rentalDivision = value.rentalDivision
            this.unused6 = value.unused6
            this.unused7 = value.unused7
            this.unused8 = value.unused8
            this.unused9 = value.unused9
            this.contractRent = value.contractRent
            this.specialRentalDivision = value.specialRentalDivision
            this.responseReceiver = value.responseReceiver
            this.parkingContractFee = value.parkingContractFee
            this.parkingContractFeeTax = value.parkingContractFeeTax
            this.parkingContractFeeExemptionDivision = value.parkingContractFeeExemptionDivision
            this.incomeParkingFeeTenant = value.incomeParkingFeeTenant
            this.incomeParkingContractFeeTax = value.incomeParkingContractFeeTax
            this.depositParkingContractFee = value.depositParkingContractFee
            this.depositParkingContractFeeTax = value.depositParkingContractFeeTax
            resetChangedOnNotNull()
        }
    }
}
