/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.StationMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.StationMasterPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 駅マスタ 既存システム物理名: EZF61P
 */
@Suppress("UNCHECKED_CAST")
open class StationMasterRecord private constructor() : UpdatableRecordImpl<StationMasterRecord>(StationMasterTable.STATION_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var lineCode: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var stationCode: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var lineName: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var lineShortName: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var stationName: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var stationShortName: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var transferStationBlockCode: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var transferStationCode: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var startYearMonth: Int?
        set(value): Unit = set(14, value)
        get(): Int? = get(14) as Int?

    open var endYearMonth: Int?
        set(value): Unit = set(15, value)
        get(): Int? = get(15) as Int?

    open var blockCode: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var lineCode_3Digit: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised StationMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, lineCode: String, stationCode: String, lineName: String? = null, lineShortName: String? = null, stationName: String? = null, stationShortName: String? = null, transferStationBlockCode: String? = null, transferStationCode: String? = null, startYearMonth: Int? = null, endYearMonth: Int? = null, blockCode: String? = null, lineCode_3Digit: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.lineCode = lineCode
        this.stationCode = stationCode
        this.lineName = lineName
        this.lineShortName = lineShortName
        this.stationName = stationName
        this.stationShortName = stationShortName
        this.transferStationBlockCode = transferStationBlockCode
        this.transferStationCode = transferStationCode
        this.startYearMonth = startYearMonth
        this.endYearMonth = endYearMonth
        this.blockCode = blockCode
        this.lineCode_3Digit = lineCode_3Digit
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised StationMasterRecord
     */
    constructor(value: StationMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.lineCode = value.lineCode
            this.stationCode = value.stationCode
            this.lineName = value.lineName
            this.lineShortName = value.lineShortName
            this.stationName = value.stationName
            this.stationShortName = value.stationShortName
            this.transferStationBlockCode = value.transferStationBlockCode
            this.transferStationCode = value.transferStationCode
            this.startYearMonth = value.startYearMonth
            this.endYearMonth = value.endYearMonth
            this.blockCode = value.blockCode
            this.lineCode_3Digit = value.lineCode_3Digit
            resetChangedOnNotNull()
        }
    }
}
