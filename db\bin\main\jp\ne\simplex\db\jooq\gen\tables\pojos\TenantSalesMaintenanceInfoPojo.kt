/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * テナント営業メンテナンス情報 既存システム物理名: EMETMP
 */
@Suppress("UNCHECKED_CAST")
data class TenantSalesMaintenanceInfoPojo(
    var branchCd: String,
    var residentialBusinessUseDivision: Byte,
    var sortOrder1: Byte? = null,
    var sortOrder2: Byte? = null,
    var sortOrder3: Byte? = null,
    var sortOrder4: Byte? = null,
    var sortOrder5: Byte? = null,
    var ascDescKey1: Byte? = null,
    var ascDescKey2: Byte? = null,
    var ascDescKey3: Byte? = null,
    var ascDescKey4: Byte? = null,
    var ascDescKey5: Byte? = null,
    var displayDivision1: Byte? = null,
    var displayDivision2: Byte? = null,
    var displayDivision3: Byte? = null,
    var displayDivision4: Byte? = null,
    var displayDivision5: Byte? = null,
    var displayDivision6: Byte? = null,
    var displayDivision7: Byte? = null,
    var displayDivision8: Byte? = null,
    var displayDivision9: Byte? = null,
    var displayDivision10: Byte? = null,
    var displayDivision11: Byte? = null,
    var displayDivision12: Byte? = null,
    var displayDivision13: Byte? = null,
    var displayDivision14: Byte? = null,
    var displayDivision15: Byte? = null,
    var displayDivision16: Byte? = null,
    var displayDivision17: Byte? = null,
    var displayDivision18: Byte? = null,
    var displayDivision19: Byte? = null,
    var displayDivision20: Byte? = null,
    var displayDivision21: Byte? = null,
    var displayDivision22: Byte? = null,
    var displayDivision23: Byte? = null,
    var displayDivision24: Byte? = null,
    var displayDivision25: Byte? = null,
    var displayDivision26: Byte? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: TenantSalesMaintenanceInfoPojo = other as TenantSalesMaintenanceInfoPojo
        if (this.branchCd != o.branchCd)
            return false
        if (this.residentialBusinessUseDivision != o.residentialBusinessUseDivision)
            return false
        if (this.sortOrder1 == null) {
            if (o.sortOrder1 != null)
                return false
        }
        else if (this.sortOrder1 != o.sortOrder1)
            return false
        if (this.sortOrder2 == null) {
            if (o.sortOrder2 != null)
                return false
        }
        else if (this.sortOrder2 != o.sortOrder2)
            return false
        if (this.sortOrder3 == null) {
            if (o.sortOrder3 != null)
                return false
        }
        else if (this.sortOrder3 != o.sortOrder3)
            return false
        if (this.sortOrder4 == null) {
            if (o.sortOrder4 != null)
                return false
        }
        else if (this.sortOrder4 != o.sortOrder4)
            return false
        if (this.sortOrder5 == null) {
            if (o.sortOrder5 != null)
                return false
        }
        else if (this.sortOrder5 != o.sortOrder5)
            return false
        if (this.ascDescKey1 == null) {
            if (o.ascDescKey1 != null)
                return false
        }
        else if (this.ascDescKey1 != o.ascDescKey1)
            return false
        if (this.ascDescKey2 == null) {
            if (o.ascDescKey2 != null)
                return false
        }
        else if (this.ascDescKey2 != o.ascDescKey2)
            return false
        if (this.ascDescKey3 == null) {
            if (o.ascDescKey3 != null)
                return false
        }
        else if (this.ascDescKey3 != o.ascDescKey3)
            return false
        if (this.ascDescKey4 == null) {
            if (o.ascDescKey4 != null)
                return false
        }
        else if (this.ascDescKey4 != o.ascDescKey4)
            return false
        if (this.ascDescKey5 == null) {
            if (o.ascDescKey5 != null)
                return false
        }
        else if (this.ascDescKey5 != o.ascDescKey5)
            return false
        if (this.displayDivision1 == null) {
            if (o.displayDivision1 != null)
                return false
        }
        else if (this.displayDivision1 != o.displayDivision1)
            return false
        if (this.displayDivision2 == null) {
            if (o.displayDivision2 != null)
                return false
        }
        else if (this.displayDivision2 != o.displayDivision2)
            return false
        if (this.displayDivision3 == null) {
            if (o.displayDivision3 != null)
                return false
        }
        else if (this.displayDivision3 != o.displayDivision3)
            return false
        if (this.displayDivision4 == null) {
            if (o.displayDivision4 != null)
                return false
        }
        else if (this.displayDivision4 != o.displayDivision4)
            return false
        if (this.displayDivision5 == null) {
            if (o.displayDivision5 != null)
                return false
        }
        else if (this.displayDivision5 != o.displayDivision5)
            return false
        if (this.displayDivision6 == null) {
            if (o.displayDivision6 != null)
                return false
        }
        else if (this.displayDivision6 != o.displayDivision6)
            return false
        if (this.displayDivision7 == null) {
            if (o.displayDivision7 != null)
                return false
        }
        else if (this.displayDivision7 != o.displayDivision7)
            return false
        if (this.displayDivision8 == null) {
            if (o.displayDivision8 != null)
                return false
        }
        else if (this.displayDivision8 != o.displayDivision8)
            return false
        if (this.displayDivision9 == null) {
            if (o.displayDivision9 != null)
                return false
        }
        else if (this.displayDivision9 != o.displayDivision9)
            return false
        if (this.displayDivision10 == null) {
            if (o.displayDivision10 != null)
                return false
        }
        else if (this.displayDivision10 != o.displayDivision10)
            return false
        if (this.displayDivision11 == null) {
            if (o.displayDivision11 != null)
                return false
        }
        else if (this.displayDivision11 != o.displayDivision11)
            return false
        if (this.displayDivision12 == null) {
            if (o.displayDivision12 != null)
                return false
        }
        else if (this.displayDivision12 != o.displayDivision12)
            return false
        if (this.displayDivision13 == null) {
            if (o.displayDivision13 != null)
                return false
        }
        else if (this.displayDivision13 != o.displayDivision13)
            return false
        if (this.displayDivision14 == null) {
            if (o.displayDivision14 != null)
                return false
        }
        else if (this.displayDivision14 != o.displayDivision14)
            return false
        if (this.displayDivision15 == null) {
            if (o.displayDivision15 != null)
                return false
        }
        else if (this.displayDivision15 != o.displayDivision15)
            return false
        if (this.displayDivision16 == null) {
            if (o.displayDivision16 != null)
                return false
        }
        else if (this.displayDivision16 != o.displayDivision16)
            return false
        if (this.displayDivision17 == null) {
            if (o.displayDivision17 != null)
                return false
        }
        else if (this.displayDivision17 != o.displayDivision17)
            return false
        if (this.displayDivision18 == null) {
            if (o.displayDivision18 != null)
                return false
        }
        else if (this.displayDivision18 != o.displayDivision18)
            return false
        if (this.displayDivision19 == null) {
            if (o.displayDivision19 != null)
                return false
        }
        else if (this.displayDivision19 != o.displayDivision19)
            return false
        if (this.displayDivision20 == null) {
            if (o.displayDivision20 != null)
                return false
        }
        else if (this.displayDivision20 != o.displayDivision20)
            return false
        if (this.displayDivision21 == null) {
            if (o.displayDivision21 != null)
                return false
        }
        else if (this.displayDivision21 != o.displayDivision21)
            return false
        if (this.displayDivision22 == null) {
            if (o.displayDivision22 != null)
                return false
        }
        else if (this.displayDivision22 != o.displayDivision22)
            return false
        if (this.displayDivision23 == null) {
            if (o.displayDivision23 != null)
                return false
        }
        else if (this.displayDivision23 != o.displayDivision23)
            return false
        if (this.displayDivision24 == null) {
            if (o.displayDivision24 != null)
                return false
        }
        else if (this.displayDivision24 != o.displayDivision24)
            return false
        if (this.displayDivision25 == null) {
            if (o.displayDivision25 != null)
                return false
        }
        else if (this.displayDivision25 != o.displayDivision25)
            return false
        if (this.displayDivision26 == null) {
            if (o.displayDivision26 != null)
                return false
        }
        else if (this.displayDivision26 != o.displayDivision26)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + this.branchCd.hashCode()
        result = prime * result + this.residentialBusinessUseDivision.hashCode()
        result = prime * result + (if (this.sortOrder1 == null) 0 else this.sortOrder1.hashCode())
        result = prime * result + (if (this.sortOrder2 == null) 0 else this.sortOrder2.hashCode())
        result = prime * result + (if (this.sortOrder3 == null) 0 else this.sortOrder3.hashCode())
        result = prime * result + (if (this.sortOrder4 == null) 0 else this.sortOrder4.hashCode())
        result = prime * result + (if (this.sortOrder5 == null) 0 else this.sortOrder5.hashCode())
        result = prime * result + (if (this.ascDescKey1 == null) 0 else this.ascDescKey1.hashCode())
        result = prime * result + (if (this.ascDescKey2 == null) 0 else this.ascDescKey2.hashCode())
        result = prime * result + (if (this.ascDescKey3 == null) 0 else this.ascDescKey3.hashCode())
        result = prime * result + (if (this.ascDescKey4 == null) 0 else this.ascDescKey4.hashCode())
        result = prime * result + (if (this.ascDescKey5 == null) 0 else this.ascDescKey5.hashCode())
        result = prime * result + (if (this.displayDivision1 == null) 0 else this.displayDivision1.hashCode())
        result = prime * result + (if (this.displayDivision2 == null) 0 else this.displayDivision2.hashCode())
        result = prime * result + (if (this.displayDivision3 == null) 0 else this.displayDivision3.hashCode())
        result = prime * result + (if (this.displayDivision4 == null) 0 else this.displayDivision4.hashCode())
        result = prime * result + (if (this.displayDivision5 == null) 0 else this.displayDivision5.hashCode())
        result = prime * result + (if (this.displayDivision6 == null) 0 else this.displayDivision6.hashCode())
        result = prime * result + (if (this.displayDivision7 == null) 0 else this.displayDivision7.hashCode())
        result = prime * result + (if (this.displayDivision8 == null) 0 else this.displayDivision8.hashCode())
        result = prime * result + (if (this.displayDivision9 == null) 0 else this.displayDivision9.hashCode())
        result = prime * result + (if (this.displayDivision10 == null) 0 else this.displayDivision10.hashCode())
        result = prime * result + (if (this.displayDivision11 == null) 0 else this.displayDivision11.hashCode())
        result = prime * result + (if (this.displayDivision12 == null) 0 else this.displayDivision12.hashCode())
        result = prime * result + (if (this.displayDivision13 == null) 0 else this.displayDivision13.hashCode())
        result = prime * result + (if (this.displayDivision14 == null) 0 else this.displayDivision14.hashCode())
        result = prime * result + (if (this.displayDivision15 == null) 0 else this.displayDivision15.hashCode())
        result = prime * result + (if (this.displayDivision16 == null) 0 else this.displayDivision16.hashCode())
        result = prime * result + (if (this.displayDivision17 == null) 0 else this.displayDivision17.hashCode())
        result = prime * result + (if (this.displayDivision18 == null) 0 else this.displayDivision18.hashCode())
        result = prime * result + (if (this.displayDivision19 == null) 0 else this.displayDivision19.hashCode())
        result = prime * result + (if (this.displayDivision20 == null) 0 else this.displayDivision20.hashCode())
        result = prime * result + (if (this.displayDivision21 == null) 0 else this.displayDivision21.hashCode())
        result = prime * result + (if (this.displayDivision22 == null) 0 else this.displayDivision22.hashCode())
        result = prime * result + (if (this.displayDivision23 == null) 0 else this.displayDivision23.hashCode())
        result = prime * result + (if (this.displayDivision24 == null) 0 else this.displayDivision24.hashCode())
        result = prime * result + (if (this.displayDivision25 == null) 0 else this.displayDivision25.hashCode())
        result = prime * result + (if (this.displayDivision26 == null) 0 else this.displayDivision26.hashCode())
        return result
    }
}
