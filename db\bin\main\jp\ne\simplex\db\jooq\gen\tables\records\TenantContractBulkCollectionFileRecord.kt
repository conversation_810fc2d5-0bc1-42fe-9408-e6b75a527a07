/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.TenantContractBulkCollectionFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.TenantContractBulkCollectionFilePojo

import org.jooq.impl.TableRecordImpl


/**
 * テナント契約一括残集ファイル 既存システム物理名: EDCTNP
 */
@Suppress("UNCHECKED_CAST")
open class TenantContractBulkCollectionFileRecord private constructor() : TableRecordImpl<TenantContractBulkCollectionFileRecord>(TenantContractBulkCollectionFileTable.TENANT_CONTRACT_BULK_COLLECTION_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var tenantContractNumber: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var parkingTenantContract: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var deletionDate: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var remainingCollectionApprovalDate: Int?
        set(value): Unit = set(9, value)
        get(): Int? = get(9) as Int?

    open var cancellationApprovalDate: Int?
        set(value): Unit = set(10, value)
        get(): Int? = get(10) as Int?

    open var moveInApplicationFee: Int?
        set(value): Unit = set(11, value)
        get(): Int? = get(11) as Int?

    open var brokerageFee: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    open var brokerageFeeTax: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var agentFee: Int?
        set(value): Unit = set(14, value)
        get(): Int? = get(14) as Int?

    open var agentFeeTax: Int?
        set(value): Unit = set(15, value)
        get(): Int? = get(15) as Int?

    open var advertisingFee: Int?
        set(value): Unit = set(16, value)
        get(): Int? = get(16) as Int?

    open var postMoveAttachmentDivision: Byte?
        set(value): Unit = set(17, value)
        get(): Byte? = get(17) as Byte?

    open var agencyAutoSumDivision: Byte?
        set(value): Unit = set(18, value)
        get(): Byte? = get(18) as Byte?

    open var transmissionDivision: Byte?
        set(value): Unit = set(19, value)
        get(): Byte? = get(19) as Byte?

    open var transmissionDate: Int?
        set(value): Unit = set(20, value)
        get(): Int? = get(20) as Int?

    open var transmissionTime: Int?
        set(value): Unit = set(21, value)
        get(): Int? = get(21) as Int?

    open var parentTenantBuildingCd: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var parentTenantRoomCd: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var parentTenantParkingCd: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var parentTenantSurrenderPlannedDate: Int?
        set(value): Unit = set(25, value)
        get(): Int? = get(25) as Int?

    open var pkBuildingCd: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var pkParkingCd: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var pkTenantSurrenderPlannedDate: Int?
        set(value): Unit = set(28, value)
        get(): Int? = get(28) as Int?

    open var parkingContractFee: Int?
        set(value): Unit = set(29, value)
        get(): Int? = get(29) as Int?

    open var parkingContractFeeTax: Int?
        set(value): Unit = set(30, value)
        get(): Int? = get(30) as Int?

    open var agentParkingContractFee: Int?
        set(value): Unit = set(31, value)
        get(): Int? = get(31) as Int?

    open var agentParkingContractFeeTax: Int?
        set(value): Unit = set(32, value)
        get(): Int? = get(32) as Int?

    /**
     * Create a detached, initialised TenantContractBulkCollectionFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, tenantContractNumber: String? = null, parkingTenantContract: String? = null, deletionDate: Int? = null, remainingCollectionApprovalDate: Int? = null, cancellationApprovalDate: Int? = null, moveInApplicationFee: Int? = null, brokerageFee: Int? = null, brokerageFeeTax: Int? = null, agentFee: Int? = null, agentFeeTax: Int? = null, advertisingFee: Int? = null, postMoveAttachmentDivision: Byte? = null, agencyAutoSumDivision: Byte? = null, transmissionDivision: Byte? = null, transmissionDate: Int? = null, transmissionTime: Int? = null, parentTenantBuildingCd: String? = null, parentTenantRoomCd: String? = null, parentTenantParkingCd: String? = null, parentTenantSurrenderPlannedDate: Int? = null, pkBuildingCd: String? = null, pkParkingCd: String? = null, pkTenantSurrenderPlannedDate: Int? = null, parkingContractFee: Int? = null, parkingContractFeeTax: Int? = null, agentParkingContractFee: Int? = null, agentParkingContractFeeTax: Int? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.tenantContractNumber = tenantContractNumber
        this.parkingTenantContract = parkingTenantContract
        this.deletionDate = deletionDate
        this.remainingCollectionApprovalDate = remainingCollectionApprovalDate
        this.cancellationApprovalDate = cancellationApprovalDate
        this.moveInApplicationFee = moveInApplicationFee
        this.brokerageFee = brokerageFee
        this.brokerageFeeTax = brokerageFeeTax
        this.agentFee = agentFee
        this.agentFeeTax = agentFeeTax
        this.advertisingFee = advertisingFee
        this.postMoveAttachmentDivision = postMoveAttachmentDivision
        this.agencyAutoSumDivision = agencyAutoSumDivision
        this.transmissionDivision = transmissionDivision
        this.transmissionDate = transmissionDate
        this.transmissionTime = transmissionTime
        this.parentTenantBuildingCd = parentTenantBuildingCd
        this.parentTenantRoomCd = parentTenantRoomCd
        this.parentTenantParkingCd = parentTenantParkingCd
        this.parentTenantSurrenderPlannedDate = parentTenantSurrenderPlannedDate
        this.pkBuildingCd = pkBuildingCd
        this.pkParkingCd = pkParkingCd
        this.pkTenantSurrenderPlannedDate = pkTenantSurrenderPlannedDate
        this.parkingContractFee = parkingContractFee
        this.parkingContractFeeTax = parkingContractFeeTax
        this.agentParkingContractFee = agentParkingContractFee
        this.agentParkingContractFeeTax = agentParkingContractFeeTax
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised TenantContractBulkCollectionFileRecord
     */
    constructor(value: TenantContractBulkCollectionFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.tenantContractNumber = value.tenantContractNumber
            this.parkingTenantContract = value.parkingTenantContract
            this.deletionDate = value.deletionDate
            this.remainingCollectionApprovalDate = value.remainingCollectionApprovalDate
            this.cancellationApprovalDate = value.cancellationApprovalDate
            this.moveInApplicationFee = value.moveInApplicationFee
            this.brokerageFee = value.brokerageFee
            this.brokerageFeeTax = value.brokerageFeeTax
            this.agentFee = value.agentFee
            this.agentFeeTax = value.agentFeeTax
            this.advertisingFee = value.advertisingFee
            this.postMoveAttachmentDivision = value.postMoveAttachmentDivision
            this.agencyAutoSumDivision = value.agencyAutoSumDivision
            this.transmissionDivision = value.transmissionDivision
            this.transmissionDate = value.transmissionDate
            this.transmissionTime = value.transmissionTime
            this.parentTenantBuildingCd = value.parentTenantBuildingCd
            this.parentTenantRoomCd = value.parentTenantRoomCd
            this.parentTenantParkingCd = value.parentTenantParkingCd
            this.parentTenantSurrenderPlannedDate = value.parentTenantSurrenderPlannedDate
            this.pkBuildingCd = value.pkBuildingCd
            this.pkParkingCd = value.pkParkingCd
            this.pkTenantSurrenderPlannedDate = value.pkTenantSurrenderPlannedDate
            this.parkingContractFee = value.parkingContractFee
            this.parkingContractFeeTax = value.parkingContractFeeTax
            this.agentParkingContractFee = value.agentParkingContractFee
            this.agentParkingContractFeeTax = value.agentParkingContractFeeTax
            resetChangedOnNotNull()
        }
    }
}
