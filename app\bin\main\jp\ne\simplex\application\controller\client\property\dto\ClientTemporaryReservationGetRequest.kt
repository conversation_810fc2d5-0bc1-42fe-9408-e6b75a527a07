package jp.ne.simplex.application.controller.client.property.dto

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.Room
import org.springdoc.core.annotations.ParameterObject

@ParameterObject
@Schema(implementation = ClientTemporaryReservationGetRequest::class)
data class ClientTemporaryReservationGetRequest(
    @JsonProperty("buildingCode")
    @field:Schema(description = "建物コード")
    val buildingCode: String,

    @JsonProperty("roomCode")
    @field:Schema(description = "部屋コード")
    val roomCode: String

) {

    @JsonIgnore
    fun getPropertyId(): Property.Id {
        return Property.Id(
            buildingCode = Building.Code.of(buildingCode),
            roomCode = Room.Code.of(roomCode)
        )
    }
}
