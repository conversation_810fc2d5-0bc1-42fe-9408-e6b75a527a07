truncate table SPECIAL_BUILDING_MASTER;
insert into SPECIAL_BUILDING_MASTER (CREATION_DATE, CREATION_TIME, UPDATE_DATE, UPDATE_TIME, UPDATE_PROGRAM_ID, UPDATER, LOGICAL_DELETE_SIGN, BUILDING_CD, IDENTIFICATION_CATEGORY) values
 (20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '015094401', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '014331701', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '014331702', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '014331703', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '014781401', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '013911101', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '013911102', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '015252901', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '015252902', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '015252903', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '014985301', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '015008701', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '014925501', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '015081901', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '014727001', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '015112701', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '014949201', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '014412201', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '014330301', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '015193301', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '015368201', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014474301', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013958401', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014784001', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014563501', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014406201', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014406202', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '015096901', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '015117601', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014102202', '01')
,(20090128, 190000, 20090223, 190000, 'DFU', 'TENANT', 1, '014941801', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014338702', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014470602', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014769501', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014704501', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '015137101', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013488901', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014669901', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '015509101', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013753601', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013803901', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014345301', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013872601', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014870301', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013902701', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '015093902', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '015166201', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014078901', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014241301', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013726001', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014635701', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '015487701', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014217201', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013535401', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013356101', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014000301', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013412401', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013904501', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013288001', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '014001201', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013000301', '01')
,(20090128, 190000, 20090206, 190000, 'DFU', 'TENANT', 1, '013212201', '01')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '004907901', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '011363301', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '011618101', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '011866201', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012065001', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012087501', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012087502', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012112901', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012282301', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012282302', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012335901', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012507501', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012570701', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012570702', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012638301', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012693301', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012702101', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012702102', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012714201', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012721401', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012721402', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012747601', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012761501', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012773401', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012802201', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012802202', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012830401', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012833701', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012840501', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012858901', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012866301', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012867301', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012871901', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012872501', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012874001', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012874002', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012884301', '02')
,(20091102, 53019, 0, 0, 'ETB410R', 'ｾｯﾄｱｯﾌﾟ', 0, '012899901', '02')
;
