/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 物件メモ 既存システム物理名: -
 */
@Suppress("UNCHECKED_CAST")
data class PropertyMemoPojo(
    var buildingCode: String,
    var propertyCode: String,
    var seqNumber: Short,
    var content: String,
    var bookmarkFlag: String,
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var creator: String? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updater: String? = null,
    var deleteFlag: String
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: PropertyMemoPojo = other as PropertyMemoPojo
        if (this.buildingCode != o.buildingCode)
            return false
        if (this.propertyCode != o.propertyCode)
            return false
        if (this.seqNumber != o.seqNumber)
            return false
        if (this.content != o.content)
            return false
        if (this.bookmarkFlag != o.bookmarkFlag)
            return false
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.creator == null) {
            if (o.creator != null)
                return false
        }
        else if (this.creator != o.creator)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.deleteFlag != o.deleteFlag)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + this.buildingCode.hashCode()
        result = prime * result + this.propertyCode.hashCode()
        result = prime * result + this.seqNumber.hashCode()
        result = prime * result + this.content.hashCode()
        result = prime * result + this.bookmarkFlag.hashCode()
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.creator == null) 0 else this.creator.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + this.deleteFlag.hashCode()
        return result
    }
}
