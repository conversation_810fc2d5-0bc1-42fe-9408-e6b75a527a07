/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.UtilityGuideMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.UtilityGuideMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * ライフラインご案内マスタ 既存システム物理名: EMLLGP
 */
@Suppress("UNCHECKED_CAST")
open class UtilityGuideMasterRecord private constructor() : TableRecordImpl<UtilityGuideMasterRecord>(UtilityGuideMasterTable.UTILITY_GUIDE_MASTER) {

    open var registrant: String?
        set(value): Unit = set(0, value)
        get(): String? = get(0) as String?

    open var registrationDate: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updater: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var prefectureCd: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var prefectureName: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var referralCompanyCd: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var electricityNoReferralHq: Byte?
        set(value): Unit = set(7, value)
        get(): Byte? = get(7) as Byte?

    open var gasNoReferralHq: Byte?
        set(value): Unit = set(8, value)
        get(): Byte? = get(8) as Byte?

    open var internetNoReferralHq: Byte?
        set(value): Unit = set(9, value)
        get(): Byte? = get(9) as Byte?

    open var waterServerNoReferralHq: Byte?
        set(value): Unit = set(10, value)
        get(): Byte? = get(10) as Byte?

    open var waterNoReferralHq: Byte?
        set(value): Unit = set(11, value)
        get(): Byte? = get(11) as Byte?

    /**
     * Create a detached, initialised UtilityGuideMasterRecord
     */
    constructor(registrant: String? = null, registrationDate: Int? = null, updater: String? = null, updateDate: Int? = null, prefectureCd: String? = null, prefectureName: String? = null, referralCompanyCd: Byte? = null, electricityNoReferralHq: Byte? = null, gasNoReferralHq: Byte? = null, internetNoReferralHq: Byte? = null, waterServerNoReferralHq: Byte? = null, waterNoReferralHq: Byte? = null): this() {
        this.registrant = registrant
        this.registrationDate = registrationDate
        this.updater = updater
        this.updateDate = updateDate
        this.prefectureCd = prefectureCd
        this.prefectureName = prefectureName
        this.referralCompanyCd = referralCompanyCd
        this.electricityNoReferralHq = electricityNoReferralHq
        this.gasNoReferralHq = gasNoReferralHq
        this.internetNoReferralHq = internetNoReferralHq
        this.waterServerNoReferralHq = waterServerNoReferralHq
        this.waterNoReferralHq = waterNoReferralHq
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised UtilityGuideMasterRecord
     */
    constructor(value: UtilityGuideMasterPojo?): this() {
        if (value != null) {
            this.registrant = value.registrant
            this.registrationDate = value.registrationDate
            this.updater = value.updater
            this.updateDate = value.updateDate
            this.prefectureCd = value.prefectureCd
            this.prefectureName = value.prefectureName
            this.referralCompanyCd = value.referralCompanyCd
            this.electricityNoReferralHq = value.electricityNoReferralHq
            this.gasNoReferralHq = value.gasNoReferralHq
            this.internetNoReferralHq = value.internetNoReferralHq
            this.waterServerNoReferralHq = value.waterServerNoReferralHq
            this.waterNoReferralHq = value.waterNoReferralHq
            resetChangedOnNotNull()
        }
    }
}
