/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.BuildingLocationInfoTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingLocationInfoPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 建物位置情報 既存システム物理名: EMECMP
 */
@Suppress("UNCHECKED_CAST")
open class BuildingLocationInfoRecord private constructor() : UpdatableRecordImpl<BuildingLocationInfoRecord>(BuildingLocationInfoTable.BUILDING_LOCATION_INFO) {

    open var buildingCd: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var latitude: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var longitude: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var matchingLevel: Byte?
        set(value): Unit = set(3, value)
        get(): Byte? = get(3) as Byte?

    open var creator: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var createProgram: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var creationDate: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var creationTime: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var updater: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var updateProgram: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var updateDate: Int?
        set(value): Unit = set(10, value)
        get(): Int? = get(10) as Int?

    open var updateTime: Int?
        set(value): Unit = set(11, value)
        get(): Int? = get(11) as Int?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised BuildingLocationInfoRecord
     */
    constructor(buildingCd: String, latitude: Int? = null, longitude: Int? = null, matchingLevel: Byte? = null, creator: String? = null, createProgram: String? = null, creationDate: Int? = null, creationTime: Int? = null, updater: String? = null, updateProgram: String? = null, updateDate: Int? = null, updateTime: Int? = null): this() {
        this.buildingCd = buildingCd
        this.latitude = latitude
        this.longitude = longitude
        this.matchingLevel = matchingLevel
        this.creator = creator
        this.createProgram = createProgram
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updater = updater
        this.updateProgram = updateProgram
        this.updateDate = updateDate
        this.updateTime = updateTime
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BuildingLocationInfoRecord
     */
    constructor(value: BuildingLocationInfoPojo?): this() {
        if (value != null) {
            this.buildingCd = value.buildingCd
            this.latitude = value.latitude
            this.longitude = value.longitude
            this.matchingLevel = value.matchingLevel
            this.creator = value.creator
            this.createProgram = value.createProgram
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updater = value.updater
            this.updateProgram = value.updateProgram
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            resetChangedOnNotNull()
        }
    }
}
