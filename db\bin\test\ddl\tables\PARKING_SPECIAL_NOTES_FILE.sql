-- TABLE: PARKING_SPECIAL_NOTES_FILE(駐車場特記事項ファイル)

CREATE TABLE PARKING_SPECIAL_NOTES_FILE(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(10)                   
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1,0)                  
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    SPECIAL_NOTES                                varchar(500)                  
,    CONSTRAINT PK_PARKING_SPECIAL_NOTES_FILE PRIMARY KEY (BUILDING_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PARKING_SPECIAL_NOTES_FILE IS '駐車場特記事項ファイル 既存システム物理名: ERB30P';
COMMENT ON COLUMN PARKING_SPECIAL_NOTES_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: ERB01D';
COMMENT ON COLUMN PARKING_SPECIAL_NOTES_FILE.CREATION_TIME IS '作成時刻 既存システム物理名: ERB02H';
COMMENT ON COLUMN PARKING_SPECIAL_NOTES_FILE.CREATOR IS '作成者 既存システム物理名: ERB03C';
COMMENT ON COLUMN PARKING_SPECIAL_NOTES_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: ERB04D';
COMMENT ON COLUMN PARKING_SPECIAL_NOTES_FILE.UPDATE_TIME IS '更新時刻 既存システム物理名: ERB05H';
COMMENT ON COLUMN PARKING_SPECIAL_NOTES_FILE.UPDATER IS '更新者 既存システム物理名: ERB06C';
COMMENT ON COLUMN PARKING_SPECIAL_NOTES_FILE.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: ERB07N';
COMMENT ON COLUMN PARKING_SPECIAL_NOTES_FILE.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: ERB08S';
COMMENT ON COLUMN PARKING_SPECIAL_NOTES_FILE.BUILDING_CODE IS '建物コード 既存システム物理名: ERB09C';
COMMENT ON COLUMN PARKING_SPECIAL_NOTES_FILE.SPECIAL_NOTES IS '特記事項 既存システム物理名: ERB10X';
