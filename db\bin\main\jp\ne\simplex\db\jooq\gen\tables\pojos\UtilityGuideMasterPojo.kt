/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * ライフラインご案内マスタ 既存システム物理名: EMLLGP
 */
@Suppress("UNCHECKED_CAST")
data class UtilityGuideMasterPojo(
    var registrant: String? = null,
    var registrationDate: Int? = null,
    var updater: String? = null,
    var updateDate: Int? = null,
    var prefectureCd: String? = null,
    var prefectureName: String? = null,
    var referralCompanyCd: Byte? = null,
    var electricityNoReferralHq: Byte? = null,
    var gasNoReferralHq: Byte? = null,
    var internetNoReferralHq: Byte? = null,
    var waterServerNoReferralHq: Byte? = null,
    var waterNoReferralHq: Byte? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: UtilityGuideMasterPojo = other as UtilityGuideMasterPojo
        if (this.registrant == null) {
            if (o.registrant != null)
                return false
        }
        else if (this.registrant != o.registrant)
            return false
        if (this.registrationDate == null) {
            if (o.registrationDate != null)
                return false
        }
        else if (this.registrationDate != o.registrationDate)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.prefectureCd == null) {
            if (o.prefectureCd != null)
                return false
        }
        else if (this.prefectureCd != o.prefectureCd)
            return false
        if (this.prefectureName == null) {
            if (o.prefectureName != null)
                return false
        }
        else if (this.prefectureName != o.prefectureName)
            return false
        if (this.referralCompanyCd == null) {
            if (o.referralCompanyCd != null)
                return false
        }
        else if (this.referralCompanyCd != o.referralCompanyCd)
            return false
        if (this.electricityNoReferralHq == null) {
            if (o.electricityNoReferralHq != null)
                return false
        }
        else if (this.electricityNoReferralHq != o.electricityNoReferralHq)
            return false
        if (this.gasNoReferralHq == null) {
            if (o.gasNoReferralHq != null)
                return false
        }
        else if (this.gasNoReferralHq != o.gasNoReferralHq)
            return false
        if (this.internetNoReferralHq == null) {
            if (o.internetNoReferralHq != null)
                return false
        }
        else if (this.internetNoReferralHq != o.internetNoReferralHq)
            return false
        if (this.waterServerNoReferralHq == null) {
            if (o.waterServerNoReferralHq != null)
                return false
        }
        else if (this.waterServerNoReferralHq != o.waterServerNoReferralHq)
            return false
        if (this.waterNoReferralHq == null) {
            if (o.waterNoReferralHq != null)
                return false
        }
        else if (this.waterNoReferralHq != o.waterNoReferralHq)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.registrant == null) 0 else this.registrant.hashCode())
        result = prime * result + (if (this.registrationDate == null) 0 else this.registrationDate.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.prefectureCd == null) 0 else this.prefectureCd.hashCode())
        result = prime * result + (if (this.prefectureName == null) 0 else this.prefectureName.hashCode())
        result = prime * result + (if (this.referralCompanyCd == null) 0 else this.referralCompanyCd.hashCode())
        result = prime * result + (if (this.electricityNoReferralHq == null) 0 else this.electricityNoReferralHq.hashCode())
        result = prime * result + (if (this.gasNoReferralHq == null) 0 else this.gasNoReferralHq.hashCode())
        result = prime * result + (if (this.internetNoReferralHq == null) 0 else this.internetNoReferralHq.hashCode())
        result = prime * result + (if (this.waterServerNoReferralHq == null) 0 else this.waterServerNoReferralHq.hashCode())
        result = prime * result + (if (this.waterNoReferralHq == null) 0 else this.waterNoReferralHq.hashCode())
        return result
    }
}
