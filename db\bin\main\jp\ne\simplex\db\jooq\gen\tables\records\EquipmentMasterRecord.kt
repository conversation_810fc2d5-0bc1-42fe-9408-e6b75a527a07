/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.EquipmentMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.EquipmentMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 設備マスタ 既存システム物理名: EJISMP
 */
@Suppress("UNCHECKED_CAST")
open class EquipmentMasterRecord private constructor() : TableRecordImpl<EquipmentMasterRecord>(EquipmentMasterTable.EQUIPMENT_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var equipmentCategoryCode: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var equipmentCode: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var equipmentName: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var equipmentAbbrev: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var priority: Short?
        set(value): Unit = set(11, value)
        get(): Short? = get(11) as Short?

    open var displayOrder: Byte?
        set(value): Unit = set(12, value)
        get(): Byte? = get(12) as Byte?

    open var equipmentDivision: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var importantExplanationDivision: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var equipmentCharacterCount: Byte?
        set(value): Unit = set(15, value)
        get(): Byte? = get(15) as Byte?

    open var reviewDisplayFlag: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var mqSendFlag: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var mappingPriority: Short?
        set(value): Unit = set(18, value)
        get(): Short? = get(18) as Short?

    open var equipmentAbbrev_2: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    /**
     * Create a detached, initialised EquipmentMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteSign: Byte? = null, equipmentCategoryCode: String? = null, equipmentCode: String? = null, equipmentName: String? = null, equipmentAbbrev: String? = null, priority: Short? = null, displayOrder: Byte? = null, equipmentDivision: String? = null, importantExplanationDivision: String? = null, equipmentCharacterCount: Byte? = null, reviewDisplayFlag: String? = null, mqSendFlag: String? = null, mappingPriority: Short? = null, equipmentAbbrev_2: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteSign = logicalDeleteSign
        this.equipmentCategoryCode = equipmentCategoryCode
        this.equipmentCode = equipmentCode
        this.equipmentName = equipmentName
        this.equipmentAbbrev = equipmentAbbrev
        this.priority = priority
        this.displayOrder = displayOrder
        this.equipmentDivision = equipmentDivision
        this.importantExplanationDivision = importantExplanationDivision
        this.equipmentCharacterCount = equipmentCharacterCount
        this.reviewDisplayFlag = reviewDisplayFlag
        this.mqSendFlag = mqSendFlag
        this.mappingPriority = mappingPriority
        this.equipmentAbbrev_2 = equipmentAbbrev_2
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised EquipmentMasterRecord
     */
    constructor(value: EquipmentMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteSign = value.logicalDeleteSign
            this.equipmentCategoryCode = value.equipmentCategoryCode
            this.equipmentCode = value.equipmentCode
            this.equipmentName = value.equipmentName
            this.equipmentAbbrev = value.equipmentAbbrev
            this.priority = value.priority
            this.displayOrder = value.displayOrder
            this.equipmentDivision = value.equipmentDivision
            this.importantExplanationDivision = value.importantExplanationDivision
            this.equipmentCharacterCount = value.equipmentCharacterCount
            this.reviewDisplayFlag = value.reviewDisplayFlag
            this.mqSendFlag = value.mqSendFlag
            this.mappingPriority = value.mappingPriority
            this.equipmentAbbrev_2 = value.equipmentAbbrev_2
            resetChangedOnNotNull()
        }
    }
}
