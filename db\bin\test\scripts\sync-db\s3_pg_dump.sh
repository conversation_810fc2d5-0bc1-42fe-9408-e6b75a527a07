#!/bin/bash

# RDSのホスト名、ユーザー名、データベース名を変数に設定
db_host="rdsproxy.proxy-chuqsqyuasmg.ap-northeast-1.rds.amazonaws.com"
db_user="app"
db_name="gpb"
s3_bucket="s3://stg2-propetech-pg-dump-bucket"
secret_id="rds-db-credentials/cluster-PUDCUKJCFFYLRKH57ZHZIPJDGY/app/1747213833427"

export PGPASSWORD=$(aws secretsmanager get-secret-value --secret-id "$secret_id" --query SecretString --output text | jq -r .password)

# dump時のテーブルの行数を確認
psql -U $db_user -h $db_host -d $db_name -c "
do \$\$
declare
    r record;
    table_count integer;
begin
    raise notice 'Checking row counts for tables in schema ''app''';
    for r in
        select table_name from information_schema.tables
        where table_schema = 'app' and table_type = 'BASE TABLE'
        order by table_name
    loop
        execute format('select count(*) from app.%I', r.table_name) into table_count;
        raise notice '%: % rows', r.table_name, table_count;
    end loop;
end \$\$;"

start_timestamp=$(TZ="Asia/Tokyo" date +"%Y%m%d_%H%M%S")

# アップロードするパス名を指定
s3_path="prod_${start_timestamp}.dump.gz"

echo "Dump started at ${start_timestamp}"

# pg_dump コマンドを実行し、gzip で圧縮し、S3 にアップロード
pg_dump -h $db_host -U $db_user -d $db_name -F c -b -v --schema='app' | gzip | aws s3 cp - "${s3_bucket}/${s3_path}"

echo "Dump completed and uploaded to ${s3_bucket}/${s3_path}"

