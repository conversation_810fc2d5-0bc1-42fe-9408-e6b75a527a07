package jp.ne.simplex.application.repository.external.eboard.config

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import jp.ne.simplex.application.repository.external.ExternalApiObjectMapper
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.exception.ExternalApiConnectionException
import jp.ne.simplex.exception.ExternalApiServerException
import org.springframework.http.HttpMethod
import org.springframework.http.client.ClientHttpResponse
import org.springframework.web.client.ResponseErrorHandler
import java.net.URI

class EboardAuthErrorHandler : ResponseErrorHandler {
    private val objectMapper: ObjectMapper = ExternalApiObjectMapper.getInstance()

    override fun hasError(response: ClientHttpResponse): Boolean {
        val body = objectMapper.readValue(response.body, EboardAuthResponse::class.java)

        return response.statusCode.is4xxClientError ||
                response.statusCode.is5xxServerError || body.hasError()
    }

    override fun handleError(url: URI, method: HttpMethod, response: ClientHttpResponse) {
        val body = objectMapper.readValue(response.body, EboardAuthResponse::class.java)
        val ex = RuntimeException(body.toString())

        if (response.statusCode.is5xxServerError) {
            throw ExternalApiConnectionException(ErrorType.EBOARD_API_ERROR, ex)
        }
        if (body.hasError()) {
            throw ExternalApiServerException(
                ErrorType.EBOARD_API_ERROR,
                ErrorMessage.EBOARD_RECEIVED_ERROR_RESPONSE.format(body.toString())
            )
        }
    }
}

/** いい部件ボード認証APIのAPIレスポンスに対応する型 */
class EboardAuthResponse(
    @JsonProperty("error_code")
    val errorCode: String?,

    @JsonProperty("error_id")
    val errorId: String?,

    @JsonProperty("error_msg")
    val errorMsg: String?
) {

    fun hasError(): Boolean {
        return getResultCode().isError()
    }

    fun getResultCode(): EboardAuthResultCode {
        return EboardAuthResultCode.entries.find { it.code == errorCode }
            ?: EboardAuthResultCode.SUCCESS
    }

    override fun toString(): String {
        return "{ error_code=${errorCode}, error_id=$errorId, error_msg=$errorMsg }"
    }
}

/** いい物件ボード認証APIの応答コード一覧 */
enum class EboardAuthResultCode(val code: String, val message: String) {

    // 正常に処理が完了した場合
    SUCCESS("0", "正常終了"),

    // パラメータ不正（ユーザID、パスワードがNULL)
    PARAMETER_MISSING("01", "パラメータ不正（ユーザID、パスワードがNULL)"),

    // 内部エラー（通常ありえない、ﾊｯｼｭ値の取得失敗）
    INTERNAL_ERROR("02", "内部エラー（通常ありえない、ハッシュ値の取得失敗）"),

    // 認証用マスタが未整備もしくはユーザIDの登録がない
    USER_NOT_FOUND("03", "認証用マスタが未整備もしくはユーザIDの登録がない"),

    // パスワード、パスフレーズの何れかが不正
    PARAMETER_INVALID("04", "パスワード、パスフレーズの何れかが不正"),

    // 認証用マスタ（連携システムメタ情報）が未整備（通常ありえない）
    MASTER_DATA_MISSING("05", "認証用マスタ（連携システムメタ情報）が未整備（通常ありえない）"),

    // アクセスのあったクライアントIPはサービス提供対象外
    IP_NOT_ALLOWED("06", "アクセスのあったクライアントIPはサービス提供対象外"),

    // 認証処理のサービスが一時的に制限されている（現在のところありえない）
    SERVICE_RESTRICTED("07", "認証処理のサービスが一時的に制限されている（現在のところありえない）"),

    // 認証処理のサービス提供時間外（現在のところありえない）
    OUT_OF_SERVICE_HOURS("08", "認証処理のサービス提供時間外（現在のところありえない）"),

    // DB接続元IPアドレス不正（通常ありえない）
    DB_IP_INVALID("09", "DB接続元IPアドレス不正（通常ありえない）"),

    // マスタキー有効期限切れ（通常ありえない）
    MASTER_KEY_EXPIRED("10", "マスタキー有効期限切れ（通常ありえない）"),

    // マスタキーの取得に失敗（通常ありえない）
    MASTER_KEY_RETRIEVAL_FAILED("11", "マスタキーの取得に失敗（通常ありえない）"),

    // 暗号化処理に失敗（通常ありえない）
    ENCRYPTION_FAILED("12", "暗号化処理に失敗（通常ありえない）"),
    ;

    fun isError(): Boolean {
        return this != SUCCESS
    }
}
