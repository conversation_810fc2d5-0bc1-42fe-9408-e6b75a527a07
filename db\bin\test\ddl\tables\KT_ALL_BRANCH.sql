-- TABLE: KT_ALL_BRANCH(店舗・審査支店マスタ(VIEW))

CREATE TABLE KT_ALL_BRANCH(
     BRANCH_CODE                                  varchar(6)                    
,    BRANCH_NAME                                  varchar(384)                  
,    ZIP_CODE                                     varchar(10)                   
,    <PERSON><PERSON><PERSON><PERSON>                                        varchar(15)                   
,    FAX                                          varchar(15)                   
,    EMAIL                                        varchar(100)                  
,    ADDRESS                                      varchar(384)                  
,    LICENSENO                                    varchar(50)                   
,    PREFCD                                       varchar(2)                    
,    BRANCH_COMMENT                               varchar(900)                  
,    LATITUDE                                     numeric(10,0)                 
,    LONGITUDE                                    numeric(10,0)                 
,    OPEN_HOUR                                    varchar(120)                  
,    WEB_CALLING_NUMBER                           numeric(32,0)                 
,    CLOSED_DAY                                   varchar(150)                  
,    OTHER_COMMENT                                varchar(90)                   
,    ACCESS_CAR                                   varchar(300)                  
,    ACCESS_TRAIN                                 varchar(300)                  
,    PARKING_COMMENT                              varchar(750)                  
,    CALL_DAITO                                   numeric(32,0)                 
,    OPEN_HOUR_COMMENT                            varchar(120)                  
,    COMPANY_CODE                                 numeric(32,0)                 
,    EBOARD_COMPANY_CODE                          varchar(3)                    
,    ADDR_PREFECTURE                              varchar(20)                   
,    ADDR_CITY                                    varchar(80)                   
,    ADDR_TOWN                                    varchar(80)                   
,    ADDR_TYOUME                                  varchar(80)                   
,    ADDR_RESTADDR                                varchar(200)                  
,    KOKUDO_CODE_TEXT                             varchar(20)                   
,    JIS_CODE                                     numeric(6,0)                  
,    UPDATE_DATE                                  varchar(20)                   
,    CONSTRAINT PK_KT_ALL_BRANCH PRIMARY KEY (BRANCH_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE KT_ALL_BRANCH IS '店舗・審査支店マスタ(VIEW) 既存システム物理名: KT_ALL_BRANCH';
COMMENT ON COLUMN KT_ALL_BRANCH.BRANCH_CODE IS '店舗ＣＤ 既存システム物理名: BRANCH_CODE';
COMMENT ON COLUMN KT_ALL_BRANCH.BRANCH_NAME IS '店舗名称 既存システム物理名: BRANCH_NAME';
COMMENT ON COLUMN KT_ALL_BRANCH.ZIP_CODE IS '郵便番号 既存システム物理名: ZIP_CODE';
COMMENT ON COLUMN KT_ALL_BRANCH.PHONE IS '電話番号 既存システム物理名: PHONE';
COMMENT ON COLUMN KT_ALL_BRANCH.FAX IS 'ＦＡＸ番号 既存システム物理名: FAX';
COMMENT ON COLUMN KT_ALL_BRANCH.EMAIL IS 'ｅ－メールアドレス 既存システム物理名: EMAIL';
COMMENT ON COLUMN KT_ALL_BRANCH.ADDRESS IS '住所 既存システム物理名: ADDRESS';
COMMENT ON COLUMN KT_ALL_BRANCH.LICENSENO IS '免許番号 既存システム物理名: LICENSENO';
COMMENT ON COLUMN KT_ALL_BRANCH.PREFCD IS '県ＣＤ 既存システム物理名: PREFCD';
COMMENT ON COLUMN KT_ALL_BRANCH.BRANCH_COMMENT IS 'コメント 既存システム物理名: BRANCH_COMMENT';
COMMENT ON COLUMN KT_ALL_BRANCH.LATITUDE IS '緯度 既存システム物理名: LATITUDE';
COMMENT ON COLUMN KT_ALL_BRANCH.LONGITUDE IS '経度 既存システム物理名: LONGITUDE';
COMMENT ON COLUMN KT_ALL_BRANCH.OPEN_HOUR IS '営業時間 既存システム物理名: OPEN_HOUR';
COMMENT ON COLUMN KT_ALL_BRANCH.WEB_CALLING_NUMBER IS 'ウェブコーリング番号 既存システム物理名: WEB_CALLING_NUMBER';
COMMENT ON COLUMN KT_ALL_BRANCH.CLOSED_DAY IS '定休日（曜日） 既存システム物理名: CLOSED_DAY';
COMMENT ON COLUMN KT_ALL_BRANCH.OTHER_COMMENT IS 'その他備考 既存システム物理名: OTHER_COMMENT';
COMMENT ON COLUMN KT_ALL_BRANCH.ACCESS_CAR IS 'アクセス（車） 既存システム物理名: ACCESS_CAR';
COMMENT ON COLUMN KT_ALL_BRANCH.ACCESS_TRAIN IS 'アクセス（電車） 既存システム物理名: ACCESS_TRAIN';
COMMENT ON COLUMN KT_ALL_BRANCH.PARKING_COMMENT IS '駐車場コメント 既存システム物理名: PARKING_COMMENT';
COMMENT ON COLUMN KT_ALL_BRANCH.CALL_DAITO IS 'コールセンター 既存システム物理名: CALL_DAITO';
COMMENT ON COLUMN KT_ALL_BRANCH.OPEN_HOUR_COMMENT IS '営業時間コメント 既存システム物理名: OPEN_HOUR_COMMENT';
COMMENT ON COLUMN KT_ALL_BRANCH.COMPANY_CODE IS '会社コード 既存システム物理名: COMPANY_CODE';
COMMENT ON COLUMN KT_ALL_BRANCH.EBOARD_COMPANY_CODE IS 'いいボード会社コード 既存システム物理名: EBOARD_COMPANY_CODE';
COMMENT ON COLUMN KT_ALL_BRANCH.ADDR_PREFECTURE IS '店舗住所：都道府県 既存システム物理名: ADDR_PREFECTURE';
COMMENT ON COLUMN KT_ALL_BRANCH.ADDR_CITY IS '店舗住所：市区町村 既存システム物理名: ADDR_CITY';
COMMENT ON COLUMN KT_ALL_BRANCH.ADDR_TOWN IS '店舗住所：町 既存システム物理名: ADDR_TOWN';
COMMENT ON COLUMN KT_ALL_BRANCH.ADDR_TYOUME IS '店舗住所：丁目 既存システム物理名: ADDR_TYOUME';
COMMENT ON COLUMN KT_ALL_BRANCH.ADDR_RESTADDR IS '店舗住所：残り住所 既存システム物理名: ADDR_RESTADDR';
COMMENT ON COLUMN KT_ALL_BRANCH.KOKUDO_CODE_TEXT IS '住所コード 既存システム物理名: KOKUDO_CODE_TEXT';
COMMENT ON COLUMN KT_ALL_BRANCH.JIS_CODE IS 'ＪＩＳコード 既存システム物理名: JIS_CODE';
COMMENT ON COLUMN KT_ALL_BRANCH.UPDATE_DATE IS '更新日時 既存システム物理名: UPDATE_DATE';
