/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 特例建物マスタ 既存システム物理名: ECMD9P
 */
@Suppress("UNCHECKED_CAST")
data class SpecialBuildingMasterPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updater: String? = null,
    var logicalDeleteSign: Byte? = null,
    var buildingCd: String? = null,
    var identificationCategory: String? = null
): Serializable {


    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: SpecialBuildingMasterPojo = other as SpecialBuildingMasterPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.logicalDeleteSign == null) {
            if (o.logicalDeleteSign != null)
                return false
        }
        else if (this.logicalDeleteSign != o.logicalDeleteSign)
            return false
        if (this.buildingCd == null) {
            if (o.buildingCd != null)
                return false
        }
        else if (this.buildingCd != o.buildingCd)
            return false
        if (this.identificationCategory == null) {
            if (o.identificationCategory != null)
                return false
        }
        else if (this.identificationCategory != o.identificationCategory)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.logicalDeleteSign == null) 0 else this.logicalDeleteSign.hashCode())
        result = prime * result + (if (this.buildingCd == null) 0 else this.buildingCd.hashCode())
        result = prime * result + (if (this.identificationCategory == null) 0 else this.identificationCategory.hashCode())
        return result
    }
}
