package jp.ne.simplex.authentication

import jp.ne.simplex.application.model.ExternalSystem
import jp.ne.simplex.application.repository.aws.SecretManagerRepository
import jp.ne.simplex.configuration.WebApiSecurityConfig
import jp.ne.simplex.shared.StringExtension.Companion.toMap
import org.springframework.stereotype.Component

@Component
class ApikeyAuthProcessor(
    private val secretManager: SecretManagerRepository,
    private val authConfig: AuthConfig
) {

    fun verify(apiKey: String, servletPath: String): AuthInfo.ApiKey {
        val secret = secretManager.getValue(authConfig.apiKey.secretId)
        val secretMap = secret.toMap()
        val externalSystemNameInSecret = secretMap.entries.find { it.value == apiKey }?.key
            ?: throw InvalidApiKeyException(apiKey)
        val externalSystem = authConfig.apiKey.getExternalSystem(externalSystemNameInSecret)
            ?: throw ExternalSystemNameNotFoundException(externalSystemNameInSecret)

        // externalSystem に対応する API リストを取得
        val apiList = when (externalSystem) {
            ExternalSystem.EBOARD -> WebApiSecurityConfig.eBoardApiList
            ExternalSystem.KIMAROOM_SIGN -> WebApiSecurityConfig.kimaRoomSignApiList
            ExternalSystem.DK_PORTAL -> WebApiSecurityConfig.dkPortalApiList
            ExternalSystem.WELCOME_PARK -> WebApiSecurityConfig.welcomeParkApiList
        }

        // 想定外接先かどうかを判定
        if (!apiList.any(servletPath::contains)) {
            throw InvalidApiKeyException(apiKey)
        }

        return AuthInfo.ApiKey(externalSystem)
    }
}

class InvalidApiKeyException(apiKey: String) : RuntimeException() {
    override val message = "API key is invalid. Received API Key is $apiKey"
}

class ExternalSystemNameNotFoundException(externalSystemInSecret: String) : RuntimeException() {
    override val message = "$externalSystemInSecret in Secret is not found"
}
