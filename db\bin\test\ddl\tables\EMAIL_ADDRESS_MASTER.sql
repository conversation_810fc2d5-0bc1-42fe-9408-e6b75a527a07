-- TABLE: EMAIL_ADDRESS_MASTER(メールアドレスマスタ)

CREATE TABLE EMAIL_ADDRESS_MASTER(
     EMPLOYEE_NUMBER                              varchar(6)        NOT NULL    
,    NOTES_ID                                     varchar(40)       NOT NULL    
,    ADDRESS                                      varchar(50)       NOT NULL    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE EMAIL_ADDRESS_MASTER IS 'メールアドレスマスタ 既存システム物理名: XXMADP';
COMMENT ON COLUMN EMAIL_ADDRESS_MASTER.EMPLOYEE_NUMBER IS '社員番号 既存システム物理名: XMA01A';
COMMENT ON COLUMN EMAIL_ADDRESS_MASTER.NOTES_ID IS 'ノーツID 既存システム物理名: XMA02A';
COMMENT ON COLUMN EMAIL_ADDRESS_MASTER.ADDRESS IS 'アドレス 既存システム物理名: XMA03A';
