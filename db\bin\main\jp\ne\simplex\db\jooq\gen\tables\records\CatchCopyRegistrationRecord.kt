/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.CatchCopyRegistrationTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.CatchCopyRegistrationPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * キャッチコピー登録 既存システム物理名: EMEKCP
 */
@Suppress("UNCHECKED_CAST")
open class CatchCopyRegistrationRecord private constructor() : UpdatableRecordImpl<CatchCopyRegistrationRecord>(CatchCopyRegistrationTable.CATCH_COPY_REGISTRATION) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var buildingCd: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var catchCopyRegistrant: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var catchCopy: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised CatchCopyRegistrationRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, buildingCd: String, catchCopyRegistrant: String? = null, catchCopy: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.buildingCd = buildingCd
        this.catchCopyRegistrant = catchCopyRegistrant
        this.catchCopy = catchCopy
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised CatchCopyRegistrationRecord
     */
    constructor(value: CatchCopyRegistrationPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.buildingCd = value.buildingCd
            this.catchCopyRegistrant = value.catchCopyRegistrant
            this.catchCopy = value.catchCopy
            resetChangedOnNotNull()
        }
    }
}
