/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.PortalMemberIdLinkMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.PortalMemberIdLinkMasterPojo

import org.jooq.Record3
import org.jooq.impl.UpdatableRecordImpl


/**
 * ポータル版会員ID紐付けマスタ 既存システム物理名: ERA15P
 */
@Suppress("UNCHECKED_CAST")
open class PortalMemberIdLinkMasterRecord private constructor() : UpdatableRecordImpl<PortalMemberIdLinkMasterRecord>(PortalMemberIdLinkMasterTable.PORTAL_MEMBER_ID_LINK_MASTER) {

    open var portalMemberId: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var branchId: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var applicationStartDate: Int
        set(value): Unit = set(2, value)
        get(): Int = get(2) as Int

    open var applicationEndDate: Int
        set(value): Unit = set(3, value)
        get(): Int = get(3) as Int

    open var logicalDeleteSign: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateDate: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var updateTime: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var creationDate: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var creationTime: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var updaterId: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record3<String?, Int?, Int?> = super.key() as Record3<String?, Int?, Int?>

    /**
     * Create a detached, initialised PortalMemberIdLinkMasterRecord
     */
    constructor(portalMemberId: Int? = null, branchId: String, applicationStartDate: Int, applicationEndDate: Int, logicalDeleteSign: String? = null, updateDate: Int? = null, updateTime: Int? = null, creationDate: Int? = null, creationTime: Int? = null, updaterId: String? = null): this() {
        this.portalMemberId = portalMemberId
        this.branchId = branchId
        this.applicationStartDate = applicationStartDate
        this.applicationEndDate = applicationEndDate
        this.logicalDeleteSign = logicalDeleteSign
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updaterId = updaterId
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised PortalMemberIdLinkMasterRecord
     */
    constructor(value: PortalMemberIdLinkMasterPojo?): this() {
        if (value != null) {
            this.portalMemberId = value.portalMemberId
            this.branchId = value.branchId
            this.applicationStartDate = value.applicationStartDate
            this.applicationEndDate = value.applicationEndDate
            this.logicalDeleteSign = value.logicalDeleteSign
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updaterId = value.updaterId
            resetChangedOnNotNull()
        }
    }
}
