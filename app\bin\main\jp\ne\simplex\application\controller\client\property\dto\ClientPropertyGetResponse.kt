package jp.ne.simplex.application.controller.client.property.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.controller.client.shared.ClientPropertyIdDto
import jp.ne.simplex.application.model.Property

data class ClientPropertyGetResponse private constructor(
    @JsonProperty("propertyId")
    @field:Schema(description = "物件ID")
    val propertyId: ClientPropertyIdDto,

    @JsonProperty("roomNumber")
    @field:Schema(description = "部屋番号")
    val roomNumber: String?,
) {

    companion object {
        fun of(source: Property): ClientPropertyGetResponse {
            return ClientPropertyGetResponse(
                propertyId = ClientPropertyIdDto.of(source.id),
                roomNumber = source.roomNumber?.getFormattedValue(),
            )
        }
    }
}
