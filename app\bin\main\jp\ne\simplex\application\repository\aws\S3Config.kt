package jp.ne.simplex.application.repository.aws

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import software.amazon.awssdk.http.apache.ApacheHttpClient
import software.amazon.awssdk.http.apache.ProxyConfiguration
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import java.net.URI
import software.amazon.awssdk.services.s3.S3Configuration
/**
 * AWS S3クライアント設定
 */
@Component
class S3Config(
    @Value("\${aws.endpoint}")
    private val endpoint: String? = null
) {

    /**
     * 本番・STG環境向けのS3クライアント
     */
    @Bean
    @Profile("!dev")
    fun prodS3Client(): S3Client {
        return S3Client.builder()
            .region(Region.AP_NORTHEAST_1)
            .build()
    }

    /**
     * 開発環境向けのS3クライアント
     * LocalStack等のエミュレータに接続するためのエンドポイント設定
     */
    @Bean
    @Profile("dev")
    fun devS3Client(): S3Client {
        if (endpoint == null) {
            throw RuntimeException("AWS endpoint is not configured for dev profile.")
        }

        val proxyConfiguration = ProxyConfiguration.builder()
            .nonProxyHosts(setOf("localhost"))
            .build()

        val httpClient = ApacheHttpClient.builder()
            .proxyConfiguration(proxyConfiguration)
            .build()

return S3Client.builder()
            .region(Region.AP_NORTHEAST_1)
            .httpClient(httpClient)
            .endpointOverride(URI.create(endpoint))
            .serviceConfiguration(
                S3Configuration.builder()
                .pathStyleAccessEnabled(true)  // ← これが重要！
                .build()
            ).build()
    }
}
