package jp.ne.simplex.application.repository.ftp

import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Repository
import java.nio.file.Path

@Profile("batch")
@Repository
class FtpRepository(
    private val ftpTemplate: FtpTemplate,
) : FtpRepositoryInterface {

    override fun sendFileToWelcomePark(localFilePath: Path, remoteFileName: String) {
        try {
            ftpTemplate.connectToWelcomePark()
            ftpTemplate.sendFileToWelcomePark(localFilePath, remoteFileName)
        } finally {
            ftpTemplate.disconnect()
        }
    }

    override fun sendFileToECloud(localFilePath: Path, remoteFileName: String) {
        try {
            ftpTemplate.connectToECloud()
            ftpTemplate.sendFileToECloud(localFilePath, remoteFileName)
        } finally {
            ftpTemplate.disconnect()
        }
    }
}

interface FtpRepositoryInterface {
    fun sendFileToWelcomePark(localFilePath: Path, remoteFileName: String)
    fun sendFileToECloud(localFilePath: Path, remoteFileName: String)

}
