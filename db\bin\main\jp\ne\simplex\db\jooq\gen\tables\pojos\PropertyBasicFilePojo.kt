/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 物件基本ファイル 既存システム物理名: BGNF1P
 */
@Suppress("UNCHECKED_CAST")
data class PropertyBasicFilePojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgram: String? = null,
    var updater: String? = null,
    var deleteFlag: String? = null,
    var orderCode: Int? = null,
    var additionalCode: Short? = null,
    var leaseTaxCellDivision: String? = null,
    var constructionName: String? = null,
    var contractTypeOccurrence: String? = null,
    var buildingCount: Short? = null,
    var clientOtherConstructionDivision: String? = null,
    var clientOtherCompletionDate: Int? = null,
    var municipalityCode_1: Byte? = null,
    var municipalityCode_2: Short? = null,
    var constructionLocation_1: String? = null,
    var constructionLocation_2: String? = null,
    var constructionLocation_3: String? = null,
    var orderCompanyCode: String? = null,
    var orderOfficeCode: Int? = null,
    var contractCompanyCode: String? = null,
    var contractOfficeCode: Int? = null,
    var buildManagerCode: Int? = null,
    var designCompanyCode: String? = null,
    var designOfficeCode: String? = null,
    var designManagerCode: Int? = null,
    var designSupervisorCode: Int? = null,
    var applicationCompanyCode: String? = null,
    var applicationOfficeCode: Int? = null,
    var applicationManagerCode: Int? = null,
    var estimationCompanyCode: String? = null,
    var estimationOfficeCode: String? = null,
    var estimationManagerCode: Int? = null,
    var developmentCompanyCode: String? = null,
    var developmentOfficeCode: String? = null,
    var developmentManagerCode: Int? = null,
    var constructionCompanyCode: String? = null,
    var constructionOfficeCode: Int? = null,
    var constructionSupervisorCode: Int? = null,
    var constructionStaffCode: Int? = null,
    var constructionManagerCode: Int? = null,
    var chiefArchitectCode: Int? = null,
    var orderDate: Int? = null,
    var contractPlannedDate: Int? = null,
    var contractDate: Int? = null,
    var lastChangeContractDate: Int? = null,
    var agreedCancellationDate: Int? = null,
    var completionReportOutputCount: Short? = null,
    var completionReportCreationDate: Int? = null,
    var completionReportCreationCompanyCode: String? = null,
    var completionReportCreationOfficeCode: String? = null,
    var completionReportCreationManagerCode: String? = null,
    var completionHandoverDate: Int? = null,
    var completionApprovalDate: Int? = null,
    var completionReportCollectionInputDate: Int? = null,
    var completionReportCollectionCompanyCd: String? = null,
    var completionReportCollectionOfficeCode: String? = null,
    var completionReportCollectionManagerCd: String? = null,
    var completionReportHeadOfficeReceiptDate: Int? = null,
    var completionReportReceiptCompanyCode: String? = null,
    var completionReportReceiptOfficeCode: String? = null,
    var completionReportReceiptManagerCode: String? = null,
    var occupancyPlannedDate: Int? = null,
    var publicFinancingDivision: String? = null,
    var developmentApplicationDivision: String? = null,
    var reviewDivision: String? = null,
    var applicationProcessStateDivision: String? = null,
    var applicationProcessHoldStartDate: Int? = null,
    var applicationProcessHoldDays: Short? = null,
    var constructionProcessStateDivision: String? = null,
    var constructionProcessHoldStartDate: Int? = null,
    var constructionProcessHoldDays: Short? = null,
    var csOutputCount: Short? = null,
    var processChangeStatus: String? = null,
    var additionalConstructionDivision: String? = null,
    var applicationAcceptancePlannedDate_1: Int? = null,
    var applicationAcceptancePlannedDate_2: Int? = null,
    var applicationAcceptancePlannedDate_3: Int? = null,
    var applicationAcceptancePlannedDate_4: Int? = null,
    var applicationAcceptanceActualDate: Int? = null,
    var applicationPermissionPlannedDate_1: Int? = null,
    var applicationPermissionPlannedDate_2: Int? = null,
    var applicationPermissionPlannedDate_3: Int? = null,
    var applicationPermissionPlannedDate_4: Int? = null,
    var applicationPermissionActualDate: Int? = null,
    var constructionStartPlannedDate_1: Int? = null,
    var constructionStartPlannedDate_2: Int? = null,
    var constructionStartPlannedDate_3: Int? = null,
    var constructionStartPlannedDate_4: Int? = null,
    var constructionStartActualDate: Int? = null,
    var constructionCompletionPlannedDate_1: Int? = null,
    var constructionCompletionPlannedDate_2: Int? = null,
    var constructionCompletionPlannedDate_3: Int? = null,
    var constructionCompletionPlannedDate_4: Int? = null,
    var constructionCompletionActualDate: Int? = null,
    var majorScheduleCode_01: Short? = null,
    var majorScheduleCode_02: Short? = null,
    var majorScheduleCode_03: Short? = null,
    var majorScheduleCode_04: Short? = null,
    var majorScheduleCode_05: Short? = null,
    var majorScheduleCode_06: Short? = null,
    var majorScheduleCode_07: Short? = null,
    var majorScheduleCode_08: Short? = null,
    var majorScheduleCode_09: Short? = null,
    var majorScheduleCode_10: Short? = null,
    var majorScheduleCode_11: Short? = null,
    var majorScheduleCode_12: Short? = null,
    var majorScheduleCode_13: Short? = null,
    var majorScheduleCode_14: Short? = null,
    var majorScheduleCode_15: Short? = null,
    var majorScheduleCode_16: Short? = null,
    var majorScheduleCode_17: Short? = null,
    var majorScheduleCode_18: Short? = null,
    var majorScheduleCode_19: Short? = null,
    var majorScheduleCode_20: Short? = null,
    var majorScheduleCode_21: Short? = null,
    var majorScheduleCode_22: Short? = null,
    var majorScheduleCode_23: Short? = null,
    var majorScheduleCode_24: Short? = null,
    var majorScheduleCode_25: Short? = null,
    var majorScheduleCode_26: Short? = null,
    var majorScheduleCode_27: Short? = null,
    var majorScheduleCode_28: Short? = null,
    var majorScheduleCode_29: Short? = null,
    var majorScheduleCode_30: Short? = null,
    var majorSchedulePlannedDate_01: Int? = null,
    var majorSchedulePlannedDate_02: Int? = null,
    var majorSchedulePlannedDate_03: Int? = null,
    var majorSchedulePlannedDate_04: Int? = null,
    var majorSchedulePlannedDate_05: Int? = null,
    var majorSchedulePlannedDate_06: Int? = null,
    var majorSchedulePlannedDate_07: Int? = null,
    var majorSchedulePlannedDate_08: Int? = null,
    var majorSchedulePlannedDate_09: Int? = null,
    var majorSchedulePlannedDate_10: Int? = null,
    var majorSchedulePlannedDate_11: Int? = null,
    var majorSchedulePlannedDate_12: Int? = null,
    var majorSchedulePlannedDate_13: Int? = null,
    var majorSchedulePlannedDate_14: Int? = null,
    var majorSchedulePlannedDate_15: Int? = null,
    var majorSchedulePlannedDate_16: Int? = null,
    var majorSchedulePlannedDate_17: Int? = null,
    var majorSchedulePlannedDate_18: Int? = null,
    var majorSchedulePlannedDate_19: Int? = null,
    var majorSchedulePlannedDate_20: Int? = null,
    var majorSchedulePlannedDate_21: Int? = null,
    var majorSchedulePlannedDate_22: Int? = null,
    var majorSchedulePlannedDate_23: Int? = null,
    var majorSchedulePlannedDate_24: Int? = null,
    var majorSchedulePlannedDate_25: Int? = null,
    var majorSchedulePlannedDate_26: Int? = null,
    var majorSchedulePlannedDate_27: Int? = null,
    var majorSchedulePlannedDate_28: Int? = null,
    var majorSchedulePlannedDate_29: Int? = null,
    var majorSchedulePlannedDate_30: Int? = null,
    var majorScheduleActualDate_01: Int? = null,
    var majorScheduleActualDate_02: Int? = null,
    var majorScheduleActualDate_03: Int? = null,
    var majorScheduleActualDate_04: Int? = null,
    var majorScheduleActualDate_05: Int? = null,
    var majorScheduleActualDate_06: Int? = null,
    var majorScheduleActualDate_07: Int? = null,
    var majorScheduleActualDate_08: Int? = null,
    var majorScheduleActualDate_09: Int? = null,
    var majorScheduleActualDate_10: Int? = null,
    var majorScheduleActualDate_11: Int? = null,
    var majorScheduleActualDate_12: Int? = null,
    var majorScheduleActualDate_13: Int? = null,
    var majorScheduleActualDate_14: Int? = null,
    var majorScheduleActualDate_15: Int? = null,
    var majorScheduleActualDate_16: Int? = null,
    var majorScheduleActualDate_17: Int? = null,
    var majorScheduleActualDate_18: Int? = null,
    var majorScheduleActualDate_19: Int? = null,
    var majorScheduleActualDate_20: Int? = null,
    var majorScheduleActualDate_21: Int? = null,
    var majorScheduleActualDate_22: Int? = null,
    var majorScheduleActualDate_23: Int? = null,
    var majorScheduleActualDate_24: Int? = null,
    var majorScheduleActualDate_25: Int? = null,
    var majorScheduleActualDate_26: Int? = null,
    var majorScheduleActualDate_27: Int? = null,
    var majorScheduleActualDate_28: Int? = null,
    var majorScheduleActualDate_29: Int? = null,
    var majorScheduleActualDate_30: Int? = null,
    var orderCodeSt: String? = null,
    var additionalCodeSt: String? = null,
    var buildManagerCodeSt: String? = null,
    var designManagerCodeSt: String? = null,
    var designSupervisorCodeSt: String? = null,
    var applicationManagerCodeSt: String? = null,
    var estimationManagerCodeSt: String? = null,
    var developmentManagerCodeSt: String? = null,
    var constructionSupervisorCodeSt: String? = null,
    var constructionStaffCodeSt: String? = null,
    var constructionManagerCodeSt: String? = null,
    var chiefArchitectCodeSt: String? = null,
    var constructionSupervisionOfficeCode: String? = null,
    var mechanismFinancingDivision: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: PropertyBasicFilePojo = other as PropertyBasicFilePojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgram == null) {
            if (o.updateProgram != null)
                return false
        }
        else if (this.updateProgram != o.updateProgram)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.deleteFlag == null) {
            if (o.deleteFlag != null)
                return false
        }
        else if (this.deleteFlag != o.deleteFlag)
            return false
        if (this.orderCode == null) {
            if (o.orderCode != null)
                return false
        }
        else if (this.orderCode != o.orderCode)
            return false
        if (this.additionalCode == null) {
            if (o.additionalCode != null)
                return false
        }
        else if (this.additionalCode != o.additionalCode)
            return false
        if (this.leaseTaxCellDivision == null) {
            if (o.leaseTaxCellDivision != null)
                return false
        }
        else if (this.leaseTaxCellDivision != o.leaseTaxCellDivision)
            return false
        if (this.constructionName == null) {
            if (o.constructionName != null)
                return false
        }
        else if (this.constructionName != o.constructionName)
            return false
        if (this.contractTypeOccurrence == null) {
            if (o.contractTypeOccurrence != null)
                return false
        }
        else if (this.contractTypeOccurrence != o.contractTypeOccurrence)
            return false
        if (this.buildingCount == null) {
            if (o.buildingCount != null)
                return false
        }
        else if (this.buildingCount != o.buildingCount)
            return false
        if (this.clientOtherConstructionDivision == null) {
            if (o.clientOtherConstructionDivision != null)
                return false
        }
        else if (this.clientOtherConstructionDivision != o.clientOtherConstructionDivision)
            return false
        if (this.clientOtherCompletionDate == null) {
            if (o.clientOtherCompletionDate != null)
                return false
        }
        else if (this.clientOtherCompletionDate != o.clientOtherCompletionDate)
            return false
        if (this.municipalityCode_1 == null) {
            if (o.municipalityCode_1 != null)
                return false
        }
        else if (this.municipalityCode_1 != o.municipalityCode_1)
            return false
        if (this.municipalityCode_2 == null) {
            if (o.municipalityCode_2 != null)
                return false
        }
        else if (this.municipalityCode_2 != o.municipalityCode_2)
            return false
        if (this.constructionLocation_1 == null) {
            if (o.constructionLocation_1 != null)
                return false
        }
        else if (this.constructionLocation_1 != o.constructionLocation_1)
            return false
        if (this.constructionLocation_2 == null) {
            if (o.constructionLocation_2 != null)
                return false
        }
        else if (this.constructionLocation_2 != o.constructionLocation_2)
            return false
        if (this.constructionLocation_3 == null) {
            if (o.constructionLocation_3 != null)
                return false
        }
        else if (this.constructionLocation_3 != o.constructionLocation_3)
            return false
        if (this.orderCompanyCode == null) {
            if (o.orderCompanyCode != null)
                return false
        }
        else if (this.orderCompanyCode != o.orderCompanyCode)
            return false
        if (this.orderOfficeCode == null) {
            if (o.orderOfficeCode != null)
                return false
        }
        else if (this.orderOfficeCode != o.orderOfficeCode)
            return false
        if (this.contractCompanyCode == null) {
            if (o.contractCompanyCode != null)
                return false
        }
        else if (this.contractCompanyCode != o.contractCompanyCode)
            return false
        if (this.contractOfficeCode == null) {
            if (o.contractOfficeCode != null)
                return false
        }
        else if (this.contractOfficeCode != o.contractOfficeCode)
            return false
        if (this.buildManagerCode == null) {
            if (o.buildManagerCode != null)
                return false
        }
        else if (this.buildManagerCode != o.buildManagerCode)
            return false
        if (this.designCompanyCode == null) {
            if (o.designCompanyCode != null)
                return false
        }
        else if (this.designCompanyCode != o.designCompanyCode)
            return false
        if (this.designOfficeCode == null) {
            if (o.designOfficeCode != null)
                return false
        }
        else if (this.designOfficeCode != o.designOfficeCode)
            return false
        if (this.designManagerCode == null) {
            if (o.designManagerCode != null)
                return false
        }
        else if (this.designManagerCode != o.designManagerCode)
            return false
        if (this.designSupervisorCode == null) {
            if (o.designSupervisorCode != null)
                return false
        }
        else if (this.designSupervisorCode != o.designSupervisorCode)
            return false
        if (this.applicationCompanyCode == null) {
            if (o.applicationCompanyCode != null)
                return false
        }
        else if (this.applicationCompanyCode != o.applicationCompanyCode)
            return false
        if (this.applicationOfficeCode == null) {
            if (o.applicationOfficeCode != null)
                return false
        }
        else if (this.applicationOfficeCode != o.applicationOfficeCode)
            return false
        if (this.applicationManagerCode == null) {
            if (o.applicationManagerCode != null)
                return false
        }
        else if (this.applicationManagerCode != o.applicationManagerCode)
            return false
        if (this.estimationCompanyCode == null) {
            if (o.estimationCompanyCode != null)
                return false
        }
        else if (this.estimationCompanyCode != o.estimationCompanyCode)
            return false
        if (this.estimationOfficeCode == null) {
            if (o.estimationOfficeCode != null)
                return false
        }
        else if (this.estimationOfficeCode != o.estimationOfficeCode)
            return false
        if (this.estimationManagerCode == null) {
            if (o.estimationManagerCode != null)
                return false
        }
        else if (this.estimationManagerCode != o.estimationManagerCode)
            return false
        if (this.developmentCompanyCode == null) {
            if (o.developmentCompanyCode != null)
                return false
        }
        else if (this.developmentCompanyCode != o.developmentCompanyCode)
            return false
        if (this.developmentOfficeCode == null) {
            if (o.developmentOfficeCode != null)
                return false
        }
        else if (this.developmentOfficeCode != o.developmentOfficeCode)
            return false
        if (this.developmentManagerCode == null) {
            if (o.developmentManagerCode != null)
                return false
        }
        else if (this.developmentManagerCode != o.developmentManagerCode)
            return false
        if (this.constructionCompanyCode == null) {
            if (o.constructionCompanyCode != null)
                return false
        }
        else if (this.constructionCompanyCode != o.constructionCompanyCode)
            return false
        if (this.constructionOfficeCode == null) {
            if (o.constructionOfficeCode != null)
                return false
        }
        else if (this.constructionOfficeCode != o.constructionOfficeCode)
            return false
        if (this.constructionSupervisorCode == null) {
            if (o.constructionSupervisorCode != null)
                return false
        }
        else if (this.constructionSupervisorCode != o.constructionSupervisorCode)
            return false
        if (this.constructionStaffCode == null) {
            if (o.constructionStaffCode != null)
                return false
        }
        else if (this.constructionStaffCode != o.constructionStaffCode)
            return false
        if (this.constructionManagerCode == null) {
            if (o.constructionManagerCode != null)
                return false
        }
        else if (this.constructionManagerCode != o.constructionManagerCode)
            return false
        if (this.chiefArchitectCode == null) {
            if (o.chiefArchitectCode != null)
                return false
        }
        else if (this.chiefArchitectCode != o.chiefArchitectCode)
            return false
        if (this.orderDate == null) {
            if (o.orderDate != null)
                return false
        }
        else if (this.orderDate != o.orderDate)
            return false
        if (this.contractPlannedDate == null) {
            if (o.contractPlannedDate != null)
                return false
        }
        else if (this.contractPlannedDate != o.contractPlannedDate)
            return false
        if (this.contractDate == null) {
            if (o.contractDate != null)
                return false
        }
        else if (this.contractDate != o.contractDate)
            return false
        if (this.lastChangeContractDate == null) {
            if (o.lastChangeContractDate != null)
                return false
        }
        else if (this.lastChangeContractDate != o.lastChangeContractDate)
            return false
        if (this.agreedCancellationDate == null) {
            if (o.agreedCancellationDate != null)
                return false
        }
        else if (this.agreedCancellationDate != o.agreedCancellationDate)
            return false
        if (this.completionReportOutputCount == null) {
            if (o.completionReportOutputCount != null)
                return false
        }
        else if (this.completionReportOutputCount != o.completionReportOutputCount)
            return false
        if (this.completionReportCreationDate == null) {
            if (o.completionReportCreationDate != null)
                return false
        }
        else if (this.completionReportCreationDate != o.completionReportCreationDate)
            return false
        if (this.completionReportCreationCompanyCode == null) {
            if (o.completionReportCreationCompanyCode != null)
                return false
        }
        else if (this.completionReportCreationCompanyCode != o.completionReportCreationCompanyCode)
            return false
        if (this.completionReportCreationOfficeCode == null) {
            if (o.completionReportCreationOfficeCode != null)
                return false
        }
        else if (this.completionReportCreationOfficeCode != o.completionReportCreationOfficeCode)
            return false
        if (this.completionReportCreationManagerCode == null) {
            if (o.completionReportCreationManagerCode != null)
                return false
        }
        else if (this.completionReportCreationManagerCode != o.completionReportCreationManagerCode)
            return false
        if (this.completionHandoverDate == null) {
            if (o.completionHandoverDate != null)
                return false
        }
        else if (this.completionHandoverDate != o.completionHandoverDate)
            return false
        if (this.completionApprovalDate == null) {
            if (o.completionApprovalDate != null)
                return false
        }
        else if (this.completionApprovalDate != o.completionApprovalDate)
            return false
        if (this.completionReportCollectionInputDate == null) {
            if (o.completionReportCollectionInputDate != null)
                return false
        }
        else if (this.completionReportCollectionInputDate != o.completionReportCollectionInputDate)
            return false
        if (this.completionReportCollectionCompanyCd == null) {
            if (o.completionReportCollectionCompanyCd != null)
                return false
        }
        else if (this.completionReportCollectionCompanyCd != o.completionReportCollectionCompanyCd)
            return false
        if (this.completionReportCollectionOfficeCode == null) {
            if (o.completionReportCollectionOfficeCode != null)
                return false
        }
        else if (this.completionReportCollectionOfficeCode != o.completionReportCollectionOfficeCode)
            return false
        if (this.completionReportCollectionManagerCd == null) {
            if (o.completionReportCollectionManagerCd != null)
                return false
        }
        else if (this.completionReportCollectionManagerCd != o.completionReportCollectionManagerCd)
            return false
        if (this.completionReportHeadOfficeReceiptDate == null) {
            if (o.completionReportHeadOfficeReceiptDate != null)
                return false
        }
        else if (this.completionReportHeadOfficeReceiptDate != o.completionReportHeadOfficeReceiptDate)
            return false
        if (this.completionReportReceiptCompanyCode == null) {
            if (o.completionReportReceiptCompanyCode != null)
                return false
        }
        else if (this.completionReportReceiptCompanyCode != o.completionReportReceiptCompanyCode)
            return false
        if (this.completionReportReceiptOfficeCode == null) {
            if (o.completionReportReceiptOfficeCode != null)
                return false
        }
        else if (this.completionReportReceiptOfficeCode != o.completionReportReceiptOfficeCode)
            return false
        if (this.completionReportReceiptManagerCode == null) {
            if (o.completionReportReceiptManagerCode != null)
                return false
        }
        else if (this.completionReportReceiptManagerCode != o.completionReportReceiptManagerCode)
            return false
        if (this.occupancyPlannedDate == null) {
            if (o.occupancyPlannedDate != null)
                return false
        }
        else if (this.occupancyPlannedDate != o.occupancyPlannedDate)
            return false
        if (this.publicFinancingDivision == null) {
            if (o.publicFinancingDivision != null)
                return false
        }
        else if (this.publicFinancingDivision != o.publicFinancingDivision)
            return false
        if (this.developmentApplicationDivision == null) {
            if (o.developmentApplicationDivision != null)
                return false
        }
        else if (this.developmentApplicationDivision != o.developmentApplicationDivision)
            return false
        if (this.reviewDivision == null) {
            if (o.reviewDivision != null)
                return false
        }
        else if (this.reviewDivision != o.reviewDivision)
            return false
        if (this.applicationProcessStateDivision == null) {
            if (o.applicationProcessStateDivision != null)
                return false
        }
        else if (this.applicationProcessStateDivision != o.applicationProcessStateDivision)
            return false
        if (this.applicationProcessHoldStartDate == null) {
            if (o.applicationProcessHoldStartDate != null)
                return false
        }
        else if (this.applicationProcessHoldStartDate != o.applicationProcessHoldStartDate)
            return false
        if (this.applicationProcessHoldDays == null) {
            if (o.applicationProcessHoldDays != null)
                return false
        }
        else if (this.applicationProcessHoldDays != o.applicationProcessHoldDays)
            return false
        if (this.constructionProcessStateDivision == null) {
            if (o.constructionProcessStateDivision != null)
                return false
        }
        else if (this.constructionProcessStateDivision != o.constructionProcessStateDivision)
            return false
        if (this.constructionProcessHoldStartDate == null) {
            if (o.constructionProcessHoldStartDate != null)
                return false
        }
        else if (this.constructionProcessHoldStartDate != o.constructionProcessHoldStartDate)
            return false
        if (this.constructionProcessHoldDays == null) {
            if (o.constructionProcessHoldDays != null)
                return false
        }
        else if (this.constructionProcessHoldDays != o.constructionProcessHoldDays)
            return false
        if (this.csOutputCount == null) {
            if (o.csOutputCount != null)
                return false
        }
        else if (this.csOutputCount != o.csOutputCount)
            return false
        if (this.processChangeStatus == null) {
            if (o.processChangeStatus != null)
                return false
        }
        else if (this.processChangeStatus != o.processChangeStatus)
            return false
        if (this.additionalConstructionDivision == null) {
            if (o.additionalConstructionDivision != null)
                return false
        }
        else if (this.additionalConstructionDivision != o.additionalConstructionDivision)
            return false
        if (this.applicationAcceptancePlannedDate_1 == null) {
            if (o.applicationAcceptancePlannedDate_1 != null)
                return false
        }
        else if (this.applicationAcceptancePlannedDate_1 != o.applicationAcceptancePlannedDate_1)
            return false
        if (this.applicationAcceptancePlannedDate_2 == null) {
            if (o.applicationAcceptancePlannedDate_2 != null)
                return false
        }
        else if (this.applicationAcceptancePlannedDate_2 != o.applicationAcceptancePlannedDate_2)
            return false
        if (this.applicationAcceptancePlannedDate_3 == null) {
            if (o.applicationAcceptancePlannedDate_3 != null)
                return false
        }
        else if (this.applicationAcceptancePlannedDate_3 != o.applicationAcceptancePlannedDate_3)
            return false
        if (this.applicationAcceptancePlannedDate_4 == null) {
            if (o.applicationAcceptancePlannedDate_4 != null)
                return false
        }
        else if (this.applicationAcceptancePlannedDate_4 != o.applicationAcceptancePlannedDate_4)
            return false
        if (this.applicationAcceptanceActualDate == null) {
            if (o.applicationAcceptanceActualDate != null)
                return false
        }
        else if (this.applicationAcceptanceActualDate != o.applicationAcceptanceActualDate)
            return false
        if (this.applicationPermissionPlannedDate_1 == null) {
            if (o.applicationPermissionPlannedDate_1 != null)
                return false
        }
        else if (this.applicationPermissionPlannedDate_1 != o.applicationPermissionPlannedDate_1)
            return false
        if (this.applicationPermissionPlannedDate_2 == null) {
            if (o.applicationPermissionPlannedDate_2 != null)
                return false
        }
        else if (this.applicationPermissionPlannedDate_2 != o.applicationPermissionPlannedDate_2)
            return false
        if (this.applicationPermissionPlannedDate_3 == null) {
            if (o.applicationPermissionPlannedDate_3 != null)
                return false
        }
        else if (this.applicationPermissionPlannedDate_3 != o.applicationPermissionPlannedDate_3)
            return false
        if (this.applicationPermissionPlannedDate_4 == null) {
            if (o.applicationPermissionPlannedDate_4 != null)
                return false
        }
        else if (this.applicationPermissionPlannedDate_4 != o.applicationPermissionPlannedDate_4)
            return false
        if (this.applicationPermissionActualDate == null) {
            if (o.applicationPermissionActualDate != null)
                return false
        }
        else if (this.applicationPermissionActualDate != o.applicationPermissionActualDate)
            return false
        if (this.constructionStartPlannedDate_1 == null) {
            if (o.constructionStartPlannedDate_1 != null)
                return false
        }
        else if (this.constructionStartPlannedDate_1 != o.constructionStartPlannedDate_1)
            return false
        if (this.constructionStartPlannedDate_2 == null) {
            if (o.constructionStartPlannedDate_2 != null)
                return false
        }
        else if (this.constructionStartPlannedDate_2 != o.constructionStartPlannedDate_2)
            return false
        if (this.constructionStartPlannedDate_3 == null) {
            if (o.constructionStartPlannedDate_3 != null)
                return false
        }
        else if (this.constructionStartPlannedDate_3 != o.constructionStartPlannedDate_3)
            return false
        if (this.constructionStartPlannedDate_4 == null) {
            if (o.constructionStartPlannedDate_4 != null)
                return false
        }
        else if (this.constructionStartPlannedDate_4 != o.constructionStartPlannedDate_4)
            return false
        if (this.constructionStartActualDate == null) {
            if (o.constructionStartActualDate != null)
                return false
        }
        else if (this.constructionStartActualDate != o.constructionStartActualDate)
            return false
        if (this.constructionCompletionPlannedDate_1 == null) {
            if (o.constructionCompletionPlannedDate_1 != null)
                return false
        }
        else if (this.constructionCompletionPlannedDate_1 != o.constructionCompletionPlannedDate_1)
            return false
        if (this.constructionCompletionPlannedDate_2 == null) {
            if (o.constructionCompletionPlannedDate_2 != null)
                return false
        }
        else if (this.constructionCompletionPlannedDate_2 != o.constructionCompletionPlannedDate_2)
            return false
        if (this.constructionCompletionPlannedDate_3 == null) {
            if (o.constructionCompletionPlannedDate_3 != null)
                return false
        }
        else if (this.constructionCompletionPlannedDate_3 != o.constructionCompletionPlannedDate_3)
            return false
        if (this.constructionCompletionPlannedDate_4 == null) {
            if (o.constructionCompletionPlannedDate_4 != null)
                return false
        }
        else if (this.constructionCompletionPlannedDate_4 != o.constructionCompletionPlannedDate_4)
            return false
        if (this.constructionCompletionActualDate == null) {
            if (o.constructionCompletionActualDate != null)
                return false
        }
        else if (this.constructionCompletionActualDate != o.constructionCompletionActualDate)
            return false
        if (this.majorScheduleCode_01 == null) {
            if (o.majorScheduleCode_01 != null)
                return false
        }
        else if (this.majorScheduleCode_01 != o.majorScheduleCode_01)
            return false
        if (this.majorScheduleCode_02 == null) {
            if (o.majorScheduleCode_02 != null)
                return false
        }
        else if (this.majorScheduleCode_02 != o.majorScheduleCode_02)
            return false
        if (this.majorScheduleCode_03 == null) {
            if (o.majorScheduleCode_03 != null)
                return false
        }
        else if (this.majorScheduleCode_03 != o.majorScheduleCode_03)
            return false
        if (this.majorScheduleCode_04 == null) {
            if (o.majorScheduleCode_04 != null)
                return false
        }
        else if (this.majorScheduleCode_04 != o.majorScheduleCode_04)
            return false
        if (this.majorScheduleCode_05 == null) {
            if (o.majorScheduleCode_05 != null)
                return false
        }
        else if (this.majorScheduleCode_05 != o.majorScheduleCode_05)
            return false
        if (this.majorScheduleCode_06 == null) {
            if (o.majorScheduleCode_06 != null)
                return false
        }
        else if (this.majorScheduleCode_06 != o.majorScheduleCode_06)
            return false
        if (this.majorScheduleCode_07 == null) {
            if (o.majorScheduleCode_07 != null)
                return false
        }
        else if (this.majorScheduleCode_07 != o.majorScheduleCode_07)
            return false
        if (this.majorScheduleCode_08 == null) {
            if (o.majorScheduleCode_08 != null)
                return false
        }
        else if (this.majorScheduleCode_08 != o.majorScheduleCode_08)
            return false
        if (this.majorScheduleCode_09 == null) {
            if (o.majorScheduleCode_09 != null)
                return false
        }
        else if (this.majorScheduleCode_09 != o.majorScheduleCode_09)
            return false
        if (this.majorScheduleCode_10 == null) {
            if (o.majorScheduleCode_10 != null)
                return false
        }
        else if (this.majorScheduleCode_10 != o.majorScheduleCode_10)
            return false
        if (this.majorScheduleCode_11 == null) {
            if (o.majorScheduleCode_11 != null)
                return false
        }
        else if (this.majorScheduleCode_11 != o.majorScheduleCode_11)
            return false
        if (this.majorScheduleCode_12 == null) {
            if (o.majorScheduleCode_12 != null)
                return false
        }
        else if (this.majorScheduleCode_12 != o.majorScheduleCode_12)
            return false
        if (this.majorScheduleCode_13 == null) {
            if (o.majorScheduleCode_13 != null)
                return false
        }
        else if (this.majorScheduleCode_13 != o.majorScheduleCode_13)
            return false
        if (this.majorScheduleCode_14 == null) {
            if (o.majorScheduleCode_14 != null)
                return false
        }
        else if (this.majorScheduleCode_14 != o.majorScheduleCode_14)
            return false
        if (this.majorScheduleCode_15 == null) {
            if (o.majorScheduleCode_15 != null)
                return false
        }
        else if (this.majorScheduleCode_15 != o.majorScheduleCode_15)
            return false
        if (this.majorScheduleCode_16 == null) {
            if (o.majorScheduleCode_16 != null)
                return false
        }
        else if (this.majorScheduleCode_16 != o.majorScheduleCode_16)
            return false
        if (this.majorScheduleCode_17 == null) {
            if (o.majorScheduleCode_17 != null)
                return false
        }
        else if (this.majorScheduleCode_17 != o.majorScheduleCode_17)
            return false
        if (this.majorScheduleCode_18 == null) {
            if (o.majorScheduleCode_18 != null)
                return false
        }
        else if (this.majorScheduleCode_18 != o.majorScheduleCode_18)
            return false
        if (this.majorScheduleCode_19 == null) {
            if (o.majorScheduleCode_19 != null)
                return false
        }
        else if (this.majorScheduleCode_19 != o.majorScheduleCode_19)
            return false
        if (this.majorScheduleCode_20 == null) {
            if (o.majorScheduleCode_20 != null)
                return false
        }
        else if (this.majorScheduleCode_20 != o.majorScheduleCode_20)
            return false
        if (this.majorScheduleCode_21 == null) {
            if (o.majorScheduleCode_21 != null)
                return false
        }
        else if (this.majorScheduleCode_21 != o.majorScheduleCode_21)
            return false
        if (this.majorScheduleCode_22 == null) {
            if (o.majorScheduleCode_22 != null)
                return false
        }
        else if (this.majorScheduleCode_22 != o.majorScheduleCode_22)
            return false
        if (this.majorScheduleCode_23 == null) {
            if (o.majorScheduleCode_23 != null)
                return false
        }
        else if (this.majorScheduleCode_23 != o.majorScheduleCode_23)
            return false
        if (this.majorScheduleCode_24 == null) {
            if (o.majorScheduleCode_24 != null)
                return false
        }
        else if (this.majorScheduleCode_24 != o.majorScheduleCode_24)
            return false
        if (this.majorScheduleCode_25 == null) {
            if (o.majorScheduleCode_25 != null)
                return false
        }
        else if (this.majorScheduleCode_25 != o.majorScheduleCode_25)
            return false
        if (this.majorScheduleCode_26 == null) {
            if (o.majorScheduleCode_26 != null)
                return false
        }
        else if (this.majorScheduleCode_26 != o.majorScheduleCode_26)
            return false
        if (this.majorScheduleCode_27 == null) {
            if (o.majorScheduleCode_27 != null)
                return false
        }
        else if (this.majorScheduleCode_27 != o.majorScheduleCode_27)
            return false
        if (this.majorScheduleCode_28 == null) {
            if (o.majorScheduleCode_28 != null)
                return false
        }
        else if (this.majorScheduleCode_28 != o.majorScheduleCode_28)
            return false
        if (this.majorScheduleCode_29 == null) {
            if (o.majorScheduleCode_29 != null)
                return false
        }
        else if (this.majorScheduleCode_29 != o.majorScheduleCode_29)
            return false
        if (this.majorScheduleCode_30 == null) {
            if (o.majorScheduleCode_30 != null)
                return false
        }
        else if (this.majorScheduleCode_30 != o.majorScheduleCode_30)
            return false
        if (this.majorSchedulePlannedDate_01 == null) {
            if (o.majorSchedulePlannedDate_01 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_01 != o.majorSchedulePlannedDate_01)
            return false
        if (this.majorSchedulePlannedDate_02 == null) {
            if (o.majorSchedulePlannedDate_02 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_02 != o.majorSchedulePlannedDate_02)
            return false
        if (this.majorSchedulePlannedDate_03 == null) {
            if (o.majorSchedulePlannedDate_03 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_03 != o.majorSchedulePlannedDate_03)
            return false
        if (this.majorSchedulePlannedDate_04 == null) {
            if (o.majorSchedulePlannedDate_04 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_04 != o.majorSchedulePlannedDate_04)
            return false
        if (this.majorSchedulePlannedDate_05 == null) {
            if (o.majorSchedulePlannedDate_05 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_05 != o.majorSchedulePlannedDate_05)
            return false
        if (this.majorSchedulePlannedDate_06 == null) {
            if (o.majorSchedulePlannedDate_06 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_06 != o.majorSchedulePlannedDate_06)
            return false
        if (this.majorSchedulePlannedDate_07 == null) {
            if (o.majorSchedulePlannedDate_07 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_07 != o.majorSchedulePlannedDate_07)
            return false
        if (this.majorSchedulePlannedDate_08 == null) {
            if (o.majorSchedulePlannedDate_08 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_08 != o.majorSchedulePlannedDate_08)
            return false
        if (this.majorSchedulePlannedDate_09 == null) {
            if (o.majorSchedulePlannedDate_09 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_09 != o.majorSchedulePlannedDate_09)
            return false
        if (this.majorSchedulePlannedDate_10 == null) {
            if (o.majorSchedulePlannedDate_10 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_10 != o.majorSchedulePlannedDate_10)
            return false
        if (this.majorSchedulePlannedDate_11 == null) {
            if (o.majorSchedulePlannedDate_11 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_11 != o.majorSchedulePlannedDate_11)
            return false
        if (this.majorSchedulePlannedDate_12 == null) {
            if (o.majorSchedulePlannedDate_12 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_12 != o.majorSchedulePlannedDate_12)
            return false
        if (this.majorSchedulePlannedDate_13 == null) {
            if (o.majorSchedulePlannedDate_13 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_13 != o.majorSchedulePlannedDate_13)
            return false
        if (this.majorSchedulePlannedDate_14 == null) {
            if (o.majorSchedulePlannedDate_14 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_14 != o.majorSchedulePlannedDate_14)
            return false
        if (this.majorSchedulePlannedDate_15 == null) {
            if (o.majorSchedulePlannedDate_15 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_15 != o.majorSchedulePlannedDate_15)
            return false
        if (this.majorSchedulePlannedDate_16 == null) {
            if (o.majorSchedulePlannedDate_16 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_16 != o.majorSchedulePlannedDate_16)
            return false
        if (this.majorSchedulePlannedDate_17 == null) {
            if (o.majorSchedulePlannedDate_17 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_17 != o.majorSchedulePlannedDate_17)
            return false
        if (this.majorSchedulePlannedDate_18 == null) {
            if (o.majorSchedulePlannedDate_18 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_18 != o.majorSchedulePlannedDate_18)
            return false
        if (this.majorSchedulePlannedDate_19 == null) {
            if (o.majorSchedulePlannedDate_19 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_19 != o.majorSchedulePlannedDate_19)
            return false
        if (this.majorSchedulePlannedDate_20 == null) {
            if (o.majorSchedulePlannedDate_20 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_20 != o.majorSchedulePlannedDate_20)
            return false
        if (this.majorSchedulePlannedDate_21 == null) {
            if (o.majorSchedulePlannedDate_21 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_21 != o.majorSchedulePlannedDate_21)
            return false
        if (this.majorSchedulePlannedDate_22 == null) {
            if (o.majorSchedulePlannedDate_22 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_22 != o.majorSchedulePlannedDate_22)
            return false
        if (this.majorSchedulePlannedDate_23 == null) {
            if (o.majorSchedulePlannedDate_23 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_23 != o.majorSchedulePlannedDate_23)
            return false
        if (this.majorSchedulePlannedDate_24 == null) {
            if (o.majorSchedulePlannedDate_24 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_24 != o.majorSchedulePlannedDate_24)
            return false
        if (this.majorSchedulePlannedDate_25 == null) {
            if (o.majorSchedulePlannedDate_25 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_25 != o.majorSchedulePlannedDate_25)
            return false
        if (this.majorSchedulePlannedDate_26 == null) {
            if (o.majorSchedulePlannedDate_26 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_26 != o.majorSchedulePlannedDate_26)
            return false
        if (this.majorSchedulePlannedDate_27 == null) {
            if (o.majorSchedulePlannedDate_27 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_27 != o.majorSchedulePlannedDate_27)
            return false
        if (this.majorSchedulePlannedDate_28 == null) {
            if (o.majorSchedulePlannedDate_28 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_28 != o.majorSchedulePlannedDate_28)
            return false
        if (this.majorSchedulePlannedDate_29 == null) {
            if (o.majorSchedulePlannedDate_29 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_29 != o.majorSchedulePlannedDate_29)
            return false
        if (this.majorSchedulePlannedDate_30 == null) {
            if (o.majorSchedulePlannedDate_30 != null)
                return false
        }
        else if (this.majorSchedulePlannedDate_30 != o.majorSchedulePlannedDate_30)
            return false
        if (this.majorScheduleActualDate_01 == null) {
            if (o.majorScheduleActualDate_01 != null)
                return false
        }
        else if (this.majorScheduleActualDate_01 != o.majorScheduleActualDate_01)
            return false
        if (this.majorScheduleActualDate_02 == null) {
            if (o.majorScheduleActualDate_02 != null)
                return false
        }
        else if (this.majorScheduleActualDate_02 != o.majorScheduleActualDate_02)
            return false
        if (this.majorScheduleActualDate_03 == null) {
            if (o.majorScheduleActualDate_03 != null)
                return false
        }
        else if (this.majorScheduleActualDate_03 != o.majorScheduleActualDate_03)
            return false
        if (this.majorScheduleActualDate_04 == null) {
            if (o.majorScheduleActualDate_04 != null)
                return false
        }
        else if (this.majorScheduleActualDate_04 != o.majorScheduleActualDate_04)
            return false
        if (this.majorScheduleActualDate_05 == null) {
            if (o.majorScheduleActualDate_05 != null)
                return false
        }
        else if (this.majorScheduleActualDate_05 != o.majorScheduleActualDate_05)
            return false
        if (this.majorScheduleActualDate_06 == null) {
            if (o.majorScheduleActualDate_06 != null)
                return false
        }
        else if (this.majorScheduleActualDate_06 != o.majorScheduleActualDate_06)
            return false
        if (this.majorScheduleActualDate_07 == null) {
            if (o.majorScheduleActualDate_07 != null)
                return false
        }
        else if (this.majorScheduleActualDate_07 != o.majorScheduleActualDate_07)
            return false
        if (this.majorScheduleActualDate_08 == null) {
            if (o.majorScheduleActualDate_08 != null)
                return false
        }
        else if (this.majorScheduleActualDate_08 != o.majorScheduleActualDate_08)
            return false
        if (this.majorScheduleActualDate_09 == null) {
            if (o.majorScheduleActualDate_09 != null)
                return false
        }
        else if (this.majorScheduleActualDate_09 != o.majorScheduleActualDate_09)
            return false
        if (this.majorScheduleActualDate_10 == null) {
            if (o.majorScheduleActualDate_10 != null)
                return false
        }
        else if (this.majorScheduleActualDate_10 != o.majorScheduleActualDate_10)
            return false
        if (this.majorScheduleActualDate_11 == null) {
            if (o.majorScheduleActualDate_11 != null)
                return false
        }
        else if (this.majorScheduleActualDate_11 != o.majorScheduleActualDate_11)
            return false
        if (this.majorScheduleActualDate_12 == null) {
            if (o.majorScheduleActualDate_12 != null)
                return false
        }
        else if (this.majorScheduleActualDate_12 != o.majorScheduleActualDate_12)
            return false
        if (this.majorScheduleActualDate_13 == null) {
            if (o.majorScheduleActualDate_13 != null)
                return false
        }
        else if (this.majorScheduleActualDate_13 != o.majorScheduleActualDate_13)
            return false
        if (this.majorScheduleActualDate_14 == null) {
            if (o.majorScheduleActualDate_14 != null)
                return false
        }
        else if (this.majorScheduleActualDate_14 != o.majorScheduleActualDate_14)
            return false
        if (this.majorScheduleActualDate_15 == null) {
            if (o.majorScheduleActualDate_15 != null)
                return false
        }
        else if (this.majorScheduleActualDate_15 != o.majorScheduleActualDate_15)
            return false
        if (this.majorScheduleActualDate_16 == null) {
            if (o.majorScheduleActualDate_16 != null)
                return false
        }
        else if (this.majorScheduleActualDate_16 != o.majorScheduleActualDate_16)
            return false
        if (this.majorScheduleActualDate_17 == null) {
            if (o.majorScheduleActualDate_17 != null)
                return false
        }
        else if (this.majorScheduleActualDate_17 != o.majorScheduleActualDate_17)
            return false
        if (this.majorScheduleActualDate_18 == null) {
            if (o.majorScheduleActualDate_18 != null)
                return false
        }
        else if (this.majorScheduleActualDate_18 != o.majorScheduleActualDate_18)
            return false
        if (this.majorScheduleActualDate_19 == null) {
            if (o.majorScheduleActualDate_19 != null)
                return false
        }
        else if (this.majorScheduleActualDate_19 != o.majorScheduleActualDate_19)
            return false
        if (this.majorScheduleActualDate_20 == null) {
            if (o.majorScheduleActualDate_20 != null)
                return false
        }
        else if (this.majorScheduleActualDate_20 != o.majorScheduleActualDate_20)
            return false
        if (this.majorScheduleActualDate_21 == null) {
            if (o.majorScheduleActualDate_21 != null)
                return false
        }
        else if (this.majorScheduleActualDate_21 != o.majorScheduleActualDate_21)
            return false
        if (this.majorScheduleActualDate_22 == null) {
            if (o.majorScheduleActualDate_22 != null)
                return false
        }
        else if (this.majorScheduleActualDate_22 != o.majorScheduleActualDate_22)
            return false
        if (this.majorScheduleActualDate_23 == null) {
            if (o.majorScheduleActualDate_23 != null)
                return false
        }
        else if (this.majorScheduleActualDate_23 != o.majorScheduleActualDate_23)
            return false
        if (this.majorScheduleActualDate_24 == null) {
            if (o.majorScheduleActualDate_24 != null)
                return false
        }
        else if (this.majorScheduleActualDate_24 != o.majorScheduleActualDate_24)
            return false
        if (this.majorScheduleActualDate_25 == null) {
            if (o.majorScheduleActualDate_25 != null)
                return false
        }
        else if (this.majorScheduleActualDate_25 != o.majorScheduleActualDate_25)
            return false
        if (this.majorScheduleActualDate_26 == null) {
            if (o.majorScheduleActualDate_26 != null)
                return false
        }
        else if (this.majorScheduleActualDate_26 != o.majorScheduleActualDate_26)
            return false
        if (this.majorScheduleActualDate_27 == null) {
            if (o.majorScheduleActualDate_27 != null)
                return false
        }
        else if (this.majorScheduleActualDate_27 != o.majorScheduleActualDate_27)
            return false
        if (this.majorScheduleActualDate_28 == null) {
            if (o.majorScheduleActualDate_28 != null)
                return false
        }
        else if (this.majorScheduleActualDate_28 != o.majorScheduleActualDate_28)
            return false
        if (this.majorScheduleActualDate_29 == null) {
            if (o.majorScheduleActualDate_29 != null)
                return false
        }
        else if (this.majorScheduleActualDate_29 != o.majorScheduleActualDate_29)
            return false
        if (this.majorScheduleActualDate_30 == null) {
            if (o.majorScheduleActualDate_30 != null)
                return false
        }
        else if (this.majorScheduleActualDate_30 != o.majorScheduleActualDate_30)
            return false
        if (this.orderCodeSt == null) {
            if (o.orderCodeSt != null)
                return false
        }
        else if (this.orderCodeSt != o.orderCodeSt)
            return false
        if (this.additionalCodeSt == null) {
            if (o.additionalCodeSt != null)
                return false
        }
        else if (this.additionalCodeSt != o.additionalCodeSt)
            return false
        if (this.buildManagerCodeSt == null) {
            if (o.buildManagerCodeSt != null)
                return false
        }
        else if (this.buildManagerCodeSt != o.buildManagerCodeSt)
            return false
        if (this.designManagerCodeSt == null) {
            if (o.designManagerCodeSt != null)
                return false
        }
        else if (this.designManagerCodeSt != o.designManagerCodeSt)
            return false
        if (this.designSupervisorCodeSt == null) {
            if (o.designSupervisorCodeSt != null)
                return false
        }
        else if (this.designSupervisorCodeSt != o.designSupervisorCodeSt)
            return false
        if (this.applicationManagerCodeSt == null) {
            if (o.applicationManagerCodeSt != null)
                return false
        }
        else if (this.applicationManagerCodeSt != o.applicationManagerCodeSt)
            return false
        if (this.estimationManagerCodeSt == null) {
            if (o.estimationManagerCodeSt != null)
                return false
        }
        else if (this.estimationManagerCodeSt != o.estimationManagerCodeSt)
            return false
        if (this.developmentManagerCodeSt == null) {
            if (o.developmentManagerCodeSt != null)
                return false
        }
        else if (this.developmentManagerCodeSt != o.developmentManagerCodeSt)
            return false
        if (this.constructionSupervisorCodeSt == null) {
            if (o.constructionSupervisorCodeSt != null)
                return false
        }
        else if (this.constructionSupervisorCodeSt != o.constructionSupervisorCodeSt)
            return false
        if (this.constructionStaffCodeSt == null) {
            if (o.constructionStaffCodeSt != null)
                return false
        }
        else if (this.constructionStaffCodeSt != o.constructionStaffCodeSt)
            return false
        if (this.constructionManagerCodeSt == null) {
            if (o.constructionManagerCodeSt != null)
                return false
        }
        else if (this.constructionManagerCodeSt != o.constructionManagerCodeSt)
            return false
        if (this.chiefArchitectCodeSt == null) {
            if (o.chiefArchitectCodeSt != null)
                return false
        }
        else if (this.chiefArchitectCodeSt != o.chiefArchitectCodeSt)
            return false
        if (this.constructionSupervisionOfficeCode == null) {
            if (o.constructionSupervisionOfficeCode != null)
                return false
        }
        else if (this.constructionSupervisionOfficeCode != o.constructionSupervisionOfficeCode)
            return false
        if (this.mechanismFinancingDivision == null) {
            if (o.mechanismFinancingDivision != null)
                return false
        }
        else if (this.mechanismFinancingDivision != o.mechanismFinancingDivision)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgram == null) 0 else this.updateProgram.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.deleteFlag == null) 0 else this.deleteFlag.hashCode())
        result = prime * result + (if (this.orderCode == null) 0 else this.orderCode.hashCode())
        result = prime * result + (if (this.additionalCode == null) 0 else this.additionalCode.hashCode())
        result = prime * result + (if (this.leaseTaxCellDivision == null) 0 else this.leaseTaxCellDivision.hashCode())
        result = prime * result + (if (this.constructionName == null) 0 else this.constructionName.hashCode())
        result = prime * result + (if (this.contractTypeOccurrence == null) 0 else this.contractTypeOccurrence.hashCode())
        result = prime * result + (if (this.buildingCount == null) 0 else this.buildingCount.hashCode())
        result = prime * result + (if (this.clientOtherConstructionDivision == null) 0 else this.clientOtherConstructionDivision.hashCode())
        result = prime * result + (if (this.clientOtherCompletionDate == null) 0 else this.clientOtherCompletionDate.hashCode())
        result = prime * result + (if (this.municipalityCode_1 == null) 0 else this.municipalityCode_1.hashCode())
        result = prime * result + (if (this.municipalityCode_2 == null) 0 else this.municipalityCode_2.hashCode())
        result = prime * result + (if (this.constructionLocation_1 == null) 0 else this.constructionLocation_1.hashCode())
        result = prime * result + (if (this.constructionLocation_2 == null) 0 else this.constructionLocation_2.hashCode())
        result = prime * result + (if (this.constructionLocation_3 == null) 0 else this.constructionLocation_3.hashCode())
        result = prime * result + (if (this.orderCompanyCode == null) 0 else this.orderCompanyCode.hashCode())
        result = prime * result + (if (this.orderOfficeCode == null) 0 else this.orderOfficeCode.hashCode())
        result = prime * result + (if (this.contractCompanyCode == null) 0 else this.contractCompanyCode.hashCode())
        result = prime * result + (if (this.contractOfficeCode == null) 0 else this.contractOfficeCode.hashCode())
        result = prime * result + (if (this.buildManagerCode == null) 0 else this.buildManagerCode.hashCode())
        result = prime * result + (if (this.designCompanyCode == null) 0 else this.designCompanyCode.hashCode())
        result = prime * result + (if (this.designOfficeCode == null) 0 else this.designOfficeCode.hashCode())
        result = prime * result + (if (this.designManagerCode == null) 0 else this.designManagerCode.hashCode())
        result = prime * result + (if (this.designSupervisorCode == null) 0 else this.designSupervisorCode.hashCode())
        result = prime * result + (if (this.applicationCompanyCode == null) 0 else this.applicationCompanyCode.hashCode())
        result = prime * result + (if (this.applicationOfficeCode == null) 0 else this.applicationOfficeCode.hashCode())
        result = prime * result + (if (this.applicationManagerCode == null) 0 else this.applicationManagerCode.hashCode())
        result = prime * result + (if (this.estimationCompanyCode == null) 0 else this.estimationCompanyCode.hashCode())
        result = prime * result + (if (this.estimationOfficeCode == null) 0 else this.estimationOfficeCode.hashCode())
        result = prime * result + (if (this.estimationManagerCode == null) 0 else this.estimationManagerCode.hashCode())
        result = prime * result + (if (this.developmentCompanyCode == null) 0 else this.developmentCompanyCode.hashCode())
        result = prime * result + (if (this.developmentOfficeCode == null) 0 else this.developmentOfficeCode.hashCode())
        result = prime * result + (if (this.developmentManagerCode == null) 0 else this.developmentManagerCode.hashCode())
        result = prime * result + (if (this.constructionCompanyCode == null) 0 else this.constructionCompanyCode.hashCode())
        result = prime * result + (if (this.constructionOfficeCode == null) 0 else this.constructionOfficeCode.hashCode())
        result = prime * result + (if (this.constructionSupervisorCode == null) 0 else this.constructionSupervisorCode.hashCode())
        result = prime * result + (if (this.constructionStaffCode == null) 0 else this.constructionStaffCode.hashCode())
        result = prime * result + (if (this.constructionManagerCode == null) 0 else this.constructionManagerCode.hashCode())
        result = prime * result + (if (this.chiefArchitectCode == null) 0 else this.chiefArchitectCode.hashCode())
        result = prime * result + (if (this.orderDate == null) 0 else this.orderDate.hashCode())
        result = prime * result + (if (this.contractPlannedDate == null) 0 else this.contractPlannedDate.hashCode())
        result = prime * result + (if (this.contractDate == null) 0 else this.contractDate.hashCode())
        result = prime * result + (if (this.lastChangeContractDate == null) 0 else this.lastChangeContractDate.hashCode())
        result = prime * result + (if (this.agreedCancellationDate == null) 0 else this.agreedCancellationDate.hashCode())
        result = prime * result + (if (this.completionReportOutputCount == null) 0 else this.completionReportOutputCount.hashCode())
        result = prime * result + (if (this.completionReportCreationDate == null) 0 else this.completionReportCreationDate.hashCode())
        result = prime * result + (if (this.completionReportCreationCompanyCode == null) 0 else this.completionReportCreationCompanyCode.hashCode())
        result = prime * result + (if (this.completionReportCreationOfficeCode == null) 0 else this.completionReportCreationOfficeCode.hashCode())
        result = prime * result + (if (this.completionReportCreationManagerCode == null) 0 else this.completionReportCreationManagerCode.hashCode())
        result = prime * result + (if (this.completionHandoverDate == null) 0 else this.completionHandoverDate.hashCode())
        result = prime * result + (if (this.completionApprovalDate == null) 0 else this.completionApprovalDate.hashCode())
        result = prime * result + (if (this.completionReportCollectionInputDate == null) 0 else this.completionReportCollectionInputDate.hashCode())
        result = prime * result + (if (this.completionReportCollectionCompanyCd == null) 0 else this.completionReportCollectionCompanyCd.hashCode())
        result = prime * result + (if (this.completionReportCollectionOfficeCode == null) 0 else this.completionReportCollectionOfficeCode.hashCode())
        result = prime * result + (if (this.completionReportCollectionManagerCd == null) 0 else this.completionReportCollectionManagerCd.hashCode())
        result = prime * result + (if (this.completionReportHeadOfficeReceiptDate == null) 0 else this.completionReportHeadOfficeReceiptDate.hashCode())
        result = prime * result + (if (this.completionReportReceiptCompanyCode == null) 0 else this.completionReportReceiptCompanyCode.hashCode())
        result = prime * result + (if (this.completionReportReceiptOfficeCode == null) 0 else this.completionReportReceiptOfficeCode.hashCode())
        result = prime * result + (if (this.completionReportReceiptManagerCode == null) 0 else this.completionReportReceiptManagerCode.hashCode())
        result = prime * result + (if (this.occupancyPlannedDate == null) 0 else this.occupancyPlannedDate.hashCode())
        result = prime * result + (if (this.publicFinancingDivision == null) 0 else this.publicFinancingDivision.hashCode())
        result = prime * result + (if (this.developmentApplicationDivision == null) 0 else this.developmentApplicationDivision.hashCode())
        result = prime * result + (if (this.reviewDivision == null) 0 else this.reviewDivision.hashCode())
        result = prime * result + (if (this.applicationProcessStateDivision == null) 0 else this.applicationProcessStateDivision.hashCode())
        result = prime * result + (if (this.applicationProcessHoldStartDate == null) 0 else this.applicationProcessHoldStartDate.hashCode())
        result = prime * result + (if (this.applicationProcessHoldDays == null) 0 else this.applicationProcessHoldDays.hashCode())
        result = prime * result + (if (this.constructionProcessStateDivision == null) 0 else this.constructionProcessStateDivision.hashCode())
        result = prime * result + (if (this.constructionProcessHoldStartDate == null) 0 else this.constructionProcessHoldStartDate.hashCode())
        result = prime * result + (if (this.constructionProcessHoldDays == null) 0 else this.constructionProcessHoldDays.hashCode())
        result = prime * result + (if (this.csOutputCount == null) 0 else this.csOutputCount.hashCode())
        result = prime * result + (if (this.processChangeStatus == null) 0 else this.processChangeStatus.hashCode())
        result = prime * result + (if (this.additionalConstructionDivision == null) 0 else this.additionalConstructionDivision.hashCode())
        result = prime * result + (if (this.applicationAcceptancePlannedDate_1 == null) 0 else this.applicationAcceptancePlannedDate_1.hashCode())
        result = prime * result + (if (this.applicationAcceptancePlannedDate_2 == null) 0 else this.applicationAcceptancePlannedDate_2.hashCode())
        result = prime * result + (if (this.applicationAcceptancePlannedDate_3 == null) 0 else this.applicationAcceptancePlannedDate_3.hashCode())
        result = prime * result + (if (this.applicationAcceptancePlannedDate_4 == null) 0 else this.applicationAcceptancePlannedDate_4.hashCode())
        result = prime * result + (if (this.applicationAcceptanceActualDate == null) 0 else this.applicationAcceptanceActualDate.hashCode())
        result = prime * result + (if (this.applicationPermissionPlannedDate_1 == null) 0 else this.applicationPermissionPlannedDate_1.hashCode())
        result = prime * result + (if (this.applicationPermissionPlannedDate_2 == null) 0 else this.applicationPermissionPlannedDate_2.hashCode())
        result = prime * result + (if (this.applicationPermissionPlannedDate_3 == null) 0 else this.applicationPermissionPlannedDate_3.hashCode())
        result = prime * result + (if (this.applicationPermissionPlannedDate_4 == null) 0 else this.applicationPermissionPlannedDate_4.hashCode())
        result = prime * result + (if (this.applicationPermissionActualDate == null) 0 else this.applicationPermissionActualDate.hashCode())
        result = prime * result + (if (this.constructionStartPlannedDate_1 == null) 0 else this.constructionStartPlannedDate_1.hashCode())
        result = prime * result + (if (this.constructionStartPlannedDate_2 == null) 0 else this.constructionStartPlannedDate_2.hashCode())
        result = prime * result + (if (this.constructionStartPlannedDate_3 == null) 0 else this.constructionStartPlannedDate_3.hashCode())
        result = prime * result + (if (this.constructionStartPlannedDate_4 == null) 0 else this.constructionStartPlannedDate_4.hashCode())
        result = prime * result + (if (this.constructionStartActualDate == null) 0 else this.constructionStartActualDate.hashCode())
        result = prime * result + (if (this.constructionCompletionPlannedDate_1 == null) 0 else this.constructionCompletionPlannedDate_1.hashCode())
        result = prime * result + (if (this.constructionCompletionPlannedDate_2 == null) 0 else this.constructionCompletionPlannedDate_2.hashCode())
        result = prime * result + (if (this.constructionCompletionPlannedDate_3 == null) 0 else this.constructionCompletionPlannedDate_3.hashCode())
        result = prime * result + (if (this.constructionCompletionPlannedDate_4 == null) 0 else this.constructionCompletionPlannedDate_4.hashCode())
        result = prime * result + (if (this.constructionCompletionActualDate == null) 0 else this.constructionCompletionActualDate.hashCode())
        result = prime * result + (if (this.majorScheduleCode_01 == null) 0 else this.majorScheduleCode_01.hashCode())
        result = prime * result + (if (this.majorScheduleCode_02 == null) 0 else this.majorScheduleCode_02.hashCode())
        result = prime * result + (if (this.majorScheduleCode_03 == null) 0 else this.majorScheduleCode_03.hashCode())
        result = prime * result + (if (this.majorScheduleCode_04 == null) 0 else this.majorScheduleCode_04.hashCode())
        result = prime * result + (if (this.majorScheduleCode_05 == null) 0 else this.majorScheduleCode_05.hashCode())
        result = prime * result + (if (this.majorScheduleCode_06 == null) 0 else this.majorScheduleCode_06.hashCode())
        result = prime * result + (if (this.majorScheduleCode_07 == null) 0 else this.majorScheduleCode_07.hashCode())
        result = prime * result + (if (this.majorScheduleCode_08 == null) 0 else this.majorScheduleCode_08.hashCode())
        result = prime * result + (if (this.majorScheduleCode_09 == null) 0 else this.majorScheduleCode_09.hashCode())
        result = prime * result + (if (this.majorScheduleCode_10 == null) 0 else this.majorScheduleCode_10.hashCode())
        result = prime * result + (if (this.majorScheduleCode_11 == null) 0 else this.majorScheduleCode_11.hashCode())
        result = prime * result + (if (this.majorScheduleCode_12 == null) 0 else this.majorScheduleCode_12.hashCode())
        result = prime * result + (if (this.majorScheduleCode_13 == null) 0 else this.majorScheduleCode_13.hashCode())
        result = prime * result + (if (this.majorScheduleCode_14 == null) 0 else this.majorScheduleCode_14.hashCode())
        result = prime * result + (if (this.majorScheduleCode_15 == null) 0 else this.majorScheduleCode_15.hashCode())
        result = prime * result + (if (this.majorScheduleCode_16 == null) 0 else this.majorScheduleCode_16.hashCode())
        result = prime * result + (if (this.majorScheduleCode_17 == null) 0 else this.majorScheduleCode_17.hashCode())
        result = prime * result + (if (this.majorScheduleCode_18 == null) 0 else this.majorScheduleCode_18.hashCode())
        result = prime * result + (if (this.majorScheduleCode_19 == null) 0 else this.majorScheduleCode_19.hashCode())
        result = prime * result + (if (this.majorScheduleCode_20 == null) 0 else this.majorScheduleCode_20.hashCode())
        result = prime * result + (if (this.majorScheduleCode_21 == null) 0 else this.majorScheduleCode_21.hashCode())
        result = prime * result + (if (this.majorScheduleCode_22 == null) 0 else this.majorScheduleCode_22.hashCode())
        result = prime * result + (if (this.majorScheduleCode_23 == null) 0 else this.majorScheduleCode_23.hashCode())
        result = prime * result + (if (this.majorScheduleCode_24 == null) 0 else this.majorScheduleCode_24.hashCode())
        result = prime * result + (if (this.majorScheduleCode_25 == null) 0 else this.majorScheduleCode_25.hashCode())
        result = prime * result + (if (this.majorScheduleCode_26 == null) 0 else this.majorScheduleCode_26.hashCode())
        result = prime * result + (if (this.majorScheduleCode_27 == null) 0 else this.majorScheduleCode_27.hashCode())
        result = prime * result + (if (this.majorScheduleCode_28 == null) 0 else this.majorScheduleCode_28.hashCode())
        result = prime * result + (if (this.majorScheduleCode_29 == null) 0 else this.majorScheduleCode_29.hashCode())
        result = prime * result + (if (this.majorScheduleCode_30 == null) 0 else this.majorScheduleCode_30.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_01 == null) 0 else this.majorSchedulePlannedDate_01.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_02 == null) 0 else this.majorSchedulePlannedDate_02.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_03 == null) 0 else this.majorSchedulePlannedDate_03.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_04 == null) 0 else this.majorSchedulePlannedDate_04.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_05 == null) 0 else this.majorSchedulePlannedDate_05.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_06 == null) 0 else this.majorSchedulePlannedDate_06.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_07 == null) 0 else this.majorSchedulePlannedDate_07.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_08 == null) 0 else this.majorSchedulePlannedDate_08.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_09 == null) 0 else this.majorSchedulePlannedDate_09.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_10 == null) 0 else this.majorSchedulePlannedDate_10.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_11 == null) 0 else this.majorSchedulePlannedDate_11.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_12 == null) 0 else this.majorSchedulePlannedDate_12.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_13 == null) 0 else this.majorSchedulePlannedDate_13.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_14 == null) 0 else this.majorSchedulePlannedDate_14.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_15 == null) 0 else this.majorSchedulePlannedDate_15.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_16 == null) 0 else this.majorSchedulePlannedDate_16.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_17 == null) 0 else this.majorSchedulePlannedDate_17.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_18 == null) 0 else this.majorSchedulePlannedDate_18.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_19 == null) 0 else this.majorSchedulePlannedDate_19.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_20 == null) 0 else this.majorSchedulePlannedDate_20.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_21 == null) 0 else this.majorSchedulePlannedDate_21.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_22 == null) 0 else this.majorSchedulePlannedDate_22.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_23 == null) 0 else this.majorSchedulePlannedDate_23.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_24 == null) 0 else this.majorSchedulePlannedDate_24.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_25 == null) 0 else this.majorSchedulePlannedDate_25.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_26 == null) 0 else this.majorSchedulePlannedDate_26.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_27 == null) 0 else this.majorSchedulePlannedDate_27.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_28 == null) 0 else this.majorSchedulePlannedDate_28.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_29 == null) 0 else this.majorSchedulePlannedDate_29.hashCode())
        result = prime * result + (if (this.majorSchedulePlannedDate_30 == null) 0 else this.majorSchedulePlannedDate_30.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_01 == null) 0 else this.majorScheduleActualDate_01.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_02 == null) 0 else this.majorScheduleActualDate_02.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_03 == null) 0 else this.majorScheduleActualDate_03.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_04 == null) 0 else this.majorScheduleActualDate_04.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_05 == null) 0 else this.majorScheduleActualDate_05.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_06 == null) 0 else this.majorScheduleActualDate_06.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_07 == null) 0 else this.majorScheduleActualDate_07.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_08 == null) 0 else this.majorScheduleActualDate_08.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_09 == null) 0 else this.majorScheduleActualDate_09.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_10 == null) 0 else this.majorScheduleActualDate_10.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_11 == null) 0 else this.majorScheduleActualDate_11.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_12 == null) 0 else this.majorScheduleActualDate_12.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_13 == null) 0 else this.majorScheduleActualDate_13.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_14 == null) 0 else this.majorScheduleActualDate_14.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_15 == null) 0 else this.majorScheduleActualDate_15.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_16 == null) 0 else this.majorScheduleActualDate_16.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_17 == null) 0 else this.majorScheduleActualDate_17.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_18 == null) 0 else this.majorScheduleActualDate_18.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_19 == null) 0 else this.majorScheduleActualDate_19.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_20 == null) 0 else this.majorScheduleActualDate_20.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_21 == null) 0 else this.majorScheduleActualDate_21.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_22 == null) 0 else this.majorScheduleActualDate_22.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_23 == null) 0 else this.majorScheduleActualDate_23.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_24 == null) 0 else this.majorScheduleActualDate_24.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_25 == null) 0 else this.majorScheduleActualDate_25.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_26 == null) 0 else this.majorScheduleActualDate_26.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_27 == null) 0 else this.majorScheduleActualDate_27.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_28 == null) 0 else this.majorScheduleActualDate_28.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_29 == null) 0 else this.majorScheduleActualDate_29.hashCode())
        result = prime * result + (if (this.majorScheduleActualDate_30 == null) 0 else this.majorScheduleActualDate_30.hashCode())
        result = prime * result + (if (this.orderCodeSt == null) 0 else this.orderCodeSt.hashCode())
        result = prime * result + (if (this.additionalCodeSt == null) 0 else this.additionalCodeSt.hashCode())
        result = prime * result + (if (this.buildManagerCodeSt == null) 0 else this.buildManagerCodeSt.hashCode())
        result = prime * result + (if (this.designManagerCodeSt == null) 0 else this.designManagerCodeSt.hashCode())
        result = prime * result + (if (this.designSupervisorCodeSt == null) 0 else this.designSupervisorCodeSt.hashCode())
        result = prime * result + (if (this.applicationManagerCodeSt == null) 0 else this.applicationManagerCodeSt.hashCode())
        result = prime * result + (if (this.estimationManagerCodeSt == null) 0 else this.estimationManagerCodeSt.hashCode())
        result = prime * result + (if (this.developmentManagerCodeSt == null) 0 else this.developmentManagerCodeSt.hashCode())
        result = prime * result + (if (this.constructionSupervisorCodeSt == null) 0 else this.constructionSupervisorCodeSt.hashCode())
        result = prime * result + (if (this.constructionStaffCodeSt == null) 0 else this.constructionStaffCodeSt.hashCode())
        result = prime * result + (if (this.constructionManagerCodeSt == null) 0 else this.constructionManagerCodeSt.hashCode())
        result = prime * result + (if (this.chiefArchitectCodeSt == null) 0 else this.chiefArchitectCodeSt.hashCode())
        result = prime * result + (if (this.constructionSupervisionOfficeCode == null) 0 else this.constructionSupervisionOfficeCode.hashCode())
        result = prime * result + (if (this.mechanismFinancingDivision == null) 0 else this.mechanismFinancingDivision.hashCode())
        return result
    }
}
