/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.CommonCodeTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.CommonCodePojo

import org.jooq.impl.TableRecordImpl


/**
 * 共用コード 既存システム物理名: EZB20P
 */
@Suppress("UNCHECKED_CAST")
open class CommonCodeRecord private constructor() : TableRecordImpl<CommonCodeRecord>(CommonCodeTable.COMMON_CODE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var codeCategory: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var code: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var officialName: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var abbreviation: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var genericField: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    /**
     * Create a detached, initialised CommonCodeRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, codeCategory: String? = null, code: String? = null, officialName: String? = null, abbreviation: String? = null, genericField: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.codeCategory = codeCategory
        this.code = code
        this.officialName = officialName
        this.abbreviation = abbreviation
        this.genericField = genericField
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised CommonCodeRecord
     */
    constructor(value: CommonCodePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.codeCategory = value.codeCategory
            this.code = value.code
            this.officialName = value.officialName
            this.abbreviation = value.abbreviation
            this.genericField = value.genericField
            resetChangedOnNotNull()
        }
    }
}
