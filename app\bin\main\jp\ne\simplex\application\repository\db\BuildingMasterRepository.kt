package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.repository.db.extension.BuildingInfoMasterEx.Companion.getBuilding
import jp.ne.simplex.application.repository.db.extension.BuildingMasterEx.Companion.getBuilding
import jp.ne.simplex.application.repository.db.pojos.BuildingMasterPojo
import jp.ne.simplex.db.jooq.gen.tables.BuildingInfoMasterTable.Companion.BUILDING_INFO_MASTER
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingInfoMasterPojo
import jp.ne.simplex.db.jooq.gen.tables.references.BUILDING_MASTER
import jp.ne.simplex.db.jooq.gen.tables.references.BUILDING_STORE_MASTER
import org.jooq.DSLContext
import org.springframework.stereotype.Repository

@Repository
class BuildingMasterRepository(private val context: DSLContext) :
    BuildingMasterRepositoryInterface {

    override fun findActiveBy(buildingCode: Building.Code): Building? {
        return context.selectFrom(BUILDING_INFO_MASTER)
            .where(BUILDING_INFO_MASTER.BUILDING_CODE.eq(buildingCode.value))
            .fetchOneInto(BuildingInfoMasterPojo::class.java)?.getBuilding()
    }

    override fun findBy(buildingCode: Building.Code): Building? {
        return context.select(
            BUILDING_MASTER.BUILDING_CODE,
            BUILDING_MASTER.BUILDING_NAME,
            BUILDING_STORE_MASTER.LEASING_STORE_CODE.`as`("LEASING_STORE_CD"),
            BUILDING_MASTER.SCREENING_BRANCH_CODE,
            BUILDING_MASTER.MARKETING_BRANCH_OFFICE_CD,
        ).from(BUILDING_MASTER).leftJoin(
            BUILDING_STORE_MASTER
        ).on(
            BUILDING_MASTER.BUILDING_CODE.eq(BUILDING_STORE_MASTER.BUILDING_CODE)
        )
            .where(BUILDING_MASTER.BUILDING_CODE.eq(buildingCode.value))
            .fetchOneInto(BuildingMasterPojo::class.java)?.getBuilding()
    }

    override fun isExist(buildingCode: Building.Code): Boolean {
        return context.selectFrom(BUILDING_MASTER)
            .where(BUILDING_MASTER.BUILDING_CODE.eq(buildingCode.value))
            .and(BUILDING_MASTER.LOGICAL_DELETE_FLAG.notEqual(1))
            .count() == 1
    }
}

interface BuildingMasterRepositoryInterface {
    /** 募集中の建物情報を取得する */
    fun findActiveBy(buildingCode: Building.Code): Building?

    /** 建物情報を取得する */
    fun findBy(buildingCode: Building.Code): Building?

    /** 有効な建物マスタ存在フラグ */
    fun isExist(buildingCode: Building.Code): Boolean
}
