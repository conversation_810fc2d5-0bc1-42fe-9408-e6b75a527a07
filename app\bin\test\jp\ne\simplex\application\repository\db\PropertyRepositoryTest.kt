package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.db.jooq.gen.tables.references.ROOM_INFO_MASTER
import jp.ne.simplex.stub.stubRoomInfoMasterPojo
import jp.ne.simplex.stub.stubVacantHouseHpPojo
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class PropertyRepositoryTest : AbstractTestContainerTest() {
    private lateinit var repository: PropertyRepository

    override fun beforeEach() {
        repository = PropertyRepository(dslContext)
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(ROOM_INFO_MASTER)
    }

    @Nested
    @DisplayName("指定された物件IDの物件情報を取得できること")
    inner class Scenario1 {

        @Test
        @DisplayName("指定した物件のみが取得できること")
        fun case1() {
            // setup
            val data1 = stubRoomInfoMasterPojo(buildingCode = "000000001")
            val data2 = stubRoomInfoMasterPojo(buildingCode = "000000002")

            dslContext.saveRoomInfoMasterPojo(data1, data2)

            val vacantHouseData1 = stubVacantHouseHpPojo(
                buildingCode = "000000001",
                changeDivision = "1",
                customerCompletionFlag = "0"
            )
            val vacantHouseData2 = stubVacantHouseHpPojo(
                buildingCode = "000000002",
                changeDivision = "1",
                customerCompletionFlag = "0"
            )
            dslContext.saveVacantHousePojo(vacantHouseData1, vacantHouseData2)

            // execute
            val actual = repository.list(listOf(data1.getProperty()!!.id))

            // verify
            assertEquals(1, actual.size)
            assertEquals(data1.propertyBuildingCd, actual.first().id.buildingCode.value)
        }
    }
}


