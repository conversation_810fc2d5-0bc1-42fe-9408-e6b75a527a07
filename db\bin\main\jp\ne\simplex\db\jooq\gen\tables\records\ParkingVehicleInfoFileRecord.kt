/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ParkingVehicleInfoFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingVehicleInfoFilePojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 駐車場車種情報ファイル 既存システム物理名: ERA30P
 */
@Suppress("UNCHECKED_CAST")
open class ParkingVehicleInfoFileRecord private constructor() : UpdatableRecordImpl<ParkingVehicleInfoFileRecord>(ParkingVehicleInfoFileTable.PARKING_VEHICLE_INFO_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var creationProgramId: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var updateDate: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updateTime: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var updater: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var updateProgramId: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var tenantContractNumber: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    open var roomCode: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var tandemSign: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var landTransportName_1: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var type_1: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var businessCategory_1: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var leftNumber_1: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var rightNumber_1: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var manufacturerDivision_1: Byte?
        set(value): Unit = set(16, value)
        get(): Byte? = get(16) as Byte?

    open var carModelName_1: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var lightVehicleSign_1: Byte?
        set(value): Unit = set(18, value)
        get(): Byte? = get(18) as Byte?

    open var landTransportName_2: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var type_2: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var businessCategory_2: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var leftNumber_2: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var rightNumber_2: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var manufacturerDivision_2: Byte?
        set(value): Unit = set(24, value)
        get(): Byte? = get(24) as Byte?

    open var carModelName_2: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var lightVehicleSign_2: Byte?
        set(value): Unit = set(26, value)
        get(): Byte? = get(26) as Byte?

    open var landTransportName_3: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var type_3: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var businessCategory_3: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var leftNumber_3: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var rightNumber_3: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var manufacturerDivision_3: Byte?
        set(value): Unit = set(32, value)
        get(): Byte? = get(32) as Byte?

    open var carModelName_3: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var lightVehicleSign_3: Byte?
        set(value): Unit = set(34, value)
        get(): Byte? = get(34) as Byte?

    open var landTransportName_4: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var type_4: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var businessCategory_4: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var leftNumber_4: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var rightNumber_4: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var manufacturerDivision_4: Byte?
        set(value): Unit = set(40, value)
        get(): Byte? = get(40) as Byte?

    open var carModelName_4: String?
        set(value): Unit = set(41, value)
        get(): String? = get(41) as String?

    open var lightVehicleSign_4: Byte?
        set(value): Unit = set(42, value)
        get(): Byte? = get(42) as Byte?

    open var landTransportName_5: String?
        set(value): Unit = set(43, value)
        get(): String? = get(43) as String?

    open var type_5: String?
        set(value): Unit = set(44, value)
        get(): String? = get(44) as String?

    open var businessCategory_5: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var leftNumber_5: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var rightNumber_5: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var manufacturerDivision_5: Byte?
        set(value): Unit = set(48, value)
        get(): Byte? = get(48) as Byte?

    open var carModelName_5: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    open var lightVehicleSign_5: Byte?
        set(value): Unit = set(50, value)
        get(): Byte? = get(50) as Byte?

    open var parkingCertIssueSign_1: Byte?
        set(value): Unit = set(51, value)
        get(): Byte? = get(51) as Byte?

    open var parkingCertComment_1: String?
        set(value): Unit = set(52, value)
        get(): String? = get(52) as String?

    open var parkingCertIssueSign_2: Byte?
        set(value): Unit = set(53, value)
        get(): Byte? = get(53) as Byte?

    open var parkingCertComment_2: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var parkingCertIssueSign_3: Byte?
        set(value): Unit = set(55, value)
        get(): Byte? = get(55) as Byte?

    open var parkingCertComment_3: String?
        set(value): Unit = set(56, value)
        get(): String? = get(56) as String?

    open var parkingCertIssueSign_4: Byte?
        set(value): Unit = set(57, value)
        get(): Byte? = get(57) as Byte?

    open var parkingCertComment_4: String?
        set(value): Unit = set(58, value)
        get(): String? = get(58) as String?

    open var parkingCertIssueSign_5: Byte?
        set(value): Unit = set(59, value)
        get(): Byte? = get(59) as Byte?

    open var parkingCertComment_5: String?
        set(value): Unit = set(60, value)
        get(): String? = get(60) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised ParkingVehicleInfoFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, creationProgramId: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, updateProgramId: String? = null, tenantContractNumber: String, roomCode: String? = null, tandemSign: String? = null, landTransportName_1: String? = null, type_1: String? = null, businessCategory_1: String? = null, leftNumber_1: String? = null, rightNumber_1: String? = null, manufacturerDivision_1: Byte? = null, carModelName_1: String? = null, lightVehicleSign_1: Byte? = null, landTransportName_2: String? = null, type_2: String? = null, businessCategory_2: String? = null, leftNumber_2: String? = null, rightNumber_2: String? = null, manufacturerDivision_2: Byte? = null, carModelName_2: String? = null, lightVehicleSign_2: Byte? = null, landTransportName_3: String? = null, type_3: String? = null, businessCategory_3: String? = null, leftNumber_3: String? = null, rightNumber_3: String? = null, manufacturerDivision_3: Byte? = null, carModelName_3: String? = null, lightVehicleSign_3: Byte? = null, landTransportName_4: String? = null, type_4: String? = null, businessCategory_4: String? = null, leftNumber_4: String? = null, rightNumber_4: String? = null, manufacturerDivision_4: Byte? = null, carModelName_4: String? = null, lightVehicleSign_4: Byte? = null, landTransportName_5: String? = null, type_5: String? = null, businessCategory_5: String? = null, leftNumber_5: String? = null, rightNumber_5: String? = null, manufacturerDivision_5: Byte? = null, carModelName_5: String? = null, lightVehicleSign_5: Byte? = null, parkingCertIssueSign_1: Byte? = null, parkingCertComment_1: String? = null, parkingCertIssueSign_2: Byte? = null, parkingCertComment_2: String? = null, parkingCertIssueSign_3: Byte? = null, parkingCertComment_3: String? = null, parkingCertIssueSign_4: Byte? = null, parkingCertComment_4: String? = null, parkingCertIssueSign_5: Byte? = null, parkingCertComment_5: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.creationProgramId = creationProgramId
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.updateProgramId = updateProgramId
        this.tenantContractNumber = tenantContractNumber
        this.roomCode = roomCode
        this.tandemSign = tandemSign
        this.landTransportName_1 = landTransportName_1
        this.type_1 = type_1
        this.businessCategory_1 = businessCategory_1
        this.leftNumber_1 = leftNumber_1
        this.rightNumber_1 = rightNumber_1
        this.manufacturerDivision_1 = manufacturerDivision_1
        this.carModelName_1 = carModelName_1
        this.lightVehicleSign_1 = lightVehicleSign_1
        this.landTransportName_2 = landTransportName_2
        this.type_2 = type_2
        this.businessCategory_2 = businessCategory_2
        this.leftNumber_2 = leftNumber_2
        this.rightNumber_2 = rightNumber_2
        this.manufacturerDivision_2 = manufacturerDivision_2
        this.carModelName_2 = carModelName_2
        this.lightVehicleSign_2 = lightVehicleSign_2
        this.landTransportName_3 = landTransportName_3
        this.type_3 = type_3
        this.businessCategory_3 = businessCategory_3
        this.leftNumber_3 = leftNumber_3
        this.rightNumber_3 = rightNumber_3
        this.manufacturerDivision_3 = manufacturerDivision_3
        this.carModelName_3 = carModelName_3
        this.lightVehicleSign_3 = lightVehicleSign_3
        this.landTransportName_4 = landTransportName_4
        this.type_4 = type_4
        this.businessCategory_4 = businessCategory_4
        this.leftNumber_4 = leftNumber_4
        this.rightNumber_4 = rightNumber_4
        this.manufacturerDivision_4 = manufacturerDivision_4
        this.carModelName_4 = carModelName_4
        this.lightVehicleSign_4 = lightVehicleSign_4
        this.landTransportName_5 = landTransportName_5
        this.type_5 = type_5
        this.businessCategory_5 = businessCategory_5
        this.leftNumber_5 = leftNumber_5
        this.rightNumber_5 = rightNumber_5
        this.manufacturerDivision_5 = manufacturerDivision_5
        this.carModelName_5 = carModelName_5
        this.lightVehicleSign_5 = lightVehicleSign_5
        this.parkingCertIssueSign_1 = parkingCertIssueSign_1
        this.parkingCertComment_1 = parkingCertComment_1
        this.parkingCertIssueSign_2 = parkingCertIssueSign_2
        this.parkingCertComment_2 = parkingCertComment_2
        this.parkingCertIssueSign_3 = parkingCertIssueSign_3
        this.parkingCertComment_3 = parkingCertComment_3
        this.parkingCertIssueSign_4 = parkingCertIssueSign_4
        this.parkingCertComment_4 = parkingCertComment_4
        this.parkingCertIssueSign_5 = parkingCertIssueSign_5
        this.parkingCertComment_5 = parkingCertComment_5
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ParkingVehicleInfoFileRecord
     */
    constructor(value: ParkingVehicleInfoFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.creationProgramId = value.creationProgramId
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.updateProgramId = value.updateProgramId
            this.tenantContractNumber = value.tenantContractNumber
            this.roomCode = value.roomCode
            this.tandemSign = value.tandemSign
            this.landTransportName_1 = value.landTransportName_1
            this.type_1 = value.type_1
            this.businessCategory_1 = value.businessCategory_1
            this.leftNumber_1 = value.leftNumber_1
            this.rightNumber_1 = value.rightNumber_1
            this.manufacturerDivision_1 = value.manufacturerDivision_1
            this.carModelName_1 = value.carModelName_1
            this.lightVehicleSign_1 = value.lightVehicleSign_1
            this.landTransportName_2 = value.landTransportName_2
            this.type_2 = value.type_2
            this.businessCategory_2 = value.businessCategory_2
            this.leftNumber_2 = value.leftNumber_2
            this.rightNumber_2 = value.rightNumber_2
            this.manufacturerDivision_2 = value.manufacturerDivision_2
            this.carModelName_2 = value.carModelName_2
            this.lightVehicleSign_2 = value.lightVehicleSign_2
            this.landTransportName_3 = value.landTransportName_3
            this.type_3 = value.type_3
            this.businessCategory_3 = value.businessCategory_3
            this.leftNumber_3 = value.leftNumber_3
            this.rightNumber_3 = value.rightNumber_3
            this.manufacturerDivision_3 = value.manufacturerDivision_3
            this.carModelName_3 = value.carModelName_3
            this.lightVehicleSign_3 = value.lightVehicleSign_3
            this.landTransportName_4 = value.landTransportName_4
            this.type_4 = value.type_4
            this.businessCategory_4 = value.businessCategory_4
            this.leftNumber_4 = value.leftNumber_4
            this.rightNumber_4 = value.rightNumber_4
            this.manufacturerDivision_4 = value.manufacturerDivision_4
            this.carModelName_4 = value.carModelName_4
            this.lightVehicleSign_4 = value.lightVehicleSign_4
            this.landTransportName_5 = value.landTransportName_5
            this.type_5 = value.type_5
            this.businessCategory_5 = value.businessCategory_5
            this.leftNumber_5 = value.leftNumber_5
            this.rightNumber_5 = value.rightNumber_5
            this.manufacturerDivision_5 = value.manufacturerDivision_5
            this.carModelName_5 = value.carModelName_5
            this.lightVehicleSign_5 = value.lightVehicleSign_5
            this.parkingCertIssueSign_1 = value.parkingCertIssueSign_1
            this.parkingCertComment_1 = value.parkingCertComment_1
            this.parkingCertIssueSign_2 = value.parkingCertIssueSign_2
            this.parkingCertComment_2 = value.parkingCertComment_2
            this.parkingCertIssueSign_3 = value.parkingCertIssueSign_3
            this.parkingCertComment_3 = value.parkingCertComment_3
            this.parkingCertIssueSign_4 = value.parkingCertIssueSign_4
            this.parkingCertComment_4 = value.parkingCertComment_4
            this.parkingCertIssueSign_5 = value.parkingCertIssueSign_5
            this.parkingCertComment_5 = value.parkingCertComment_5
            resetChangedOnNotNull()
        }
    }
}
