-- TABLE: PARKING(駐車場)

CREATE TABLE PARKING(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_FLAG                          numeric(1,0)                  
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    ACTUAL_BUILDING_CODE                         varchar(9)                    
,    PARKING_LOT_CODE                             varchar(3)        NOT NULL    
,    PARKING_LOT_NUMBER                           varchar(4)                    
,    CONSOLIDATED_BUILDING_CODE                   varchar(9)                    
,    CONSOLIDATED_PARKING_CODE                    varchar(3)                    
,    CONSOLIDATED_PARKING_COUNT                   numeric(3,0)                  
,    TRANSFERRED_BUILDING_CODE                    varchar(9)                    
,    PRE_CONSOLIDATION_AVAILABLE                  numeric(8,0)                  
,    BULK_LEASE_FLAG                              numeric(1,0)                  
,    DEPOSIT_ZERO_CONSENT_CATEGORY                numeric(1,0)                  
,    LANDLORD_CODE_10                             varchar(10)                   
,    TENANT_CATEGORY                              varchar(1)                    
,    ROOF_FLAG                                    numeric(1,0)                  
,    RECRUITMENT_FLAG                             numeric(1,0)                  
,    AVAILABLE_DATE                               numeric(8,0)                  
,    MANAGEMENT_FLAG                              numeric(1,0)                  
,    MANAGEMENT_UNIT_CATEGORY                     varchar(1)                    
,    TENANT_RECRUITMENT_COUNT                     numeric(1,0)                  
,    MANAGEMENT_CONTRACT_CASH_RECEIVED            numeric(9,0)                  
,    ASSESSMENT_REVIEW_DOCUMENT_NUMBER            varchar(8)                    
,    INTERFACE_FLAG                               numeric(1,0)                  
,    DATA_MIGRATION_SOURCE_KEY_1                  varchar(15)                   
,    DATA_MIGRATION_SOURCE_KEY_2                  varchar(15)                   
,    ADDITIONAL_AD_FEE_MONTHS                     numeric(3,1)                  
,    NEW_GUARANTEE_RATE                           numeric(5,2)                  
,    NEW_GUARANTEE_MANAGEMENT_RATE                numeric(5,2)                  
,    CONTRACT_MUTUAL_AID_FEE_RATE                 numeric(5,2)                  
,    NON_STANDARD_CATEGORY                        numeric(1,0)                  
,    PARKING_CATEGORY                             varchar(1)                    
,    OFF_SITE_PARKING_CATEGORY                    varchar(1)                    
,    CONSTRAINT PK_PARKING PRIMARY KEY (BUILDING_CODE, PARKING_LOT_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PARKING IS '駐車場 既存システム物理名: ECC30P';
COMMENT ON COLUMN PARKING.CREATION_DATE IS '作成年月日 既存システム物理名: ECC01D 1：テナント 2：だいとうけんたく 3：家主 4：モデルルーム';
COMMENT ON COLUMN PARKING.CREATION_TIME IS '作成時刻 既存システム物理名: ECC02H';
COMMENT ON COLUMN PARKING.UPDATE_DATE IS '更新年月日 既存システム物理名: ECC03D';
COMMENT ON COLUMN PARKING.UPDATE_TIME IS '更新時刻 既存システム物理名: ECC04H';
COMMENT ON COLUMN PARKING.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: ECC05N';
COMMENT ON COLUMN PARKING.UPDATER IS '更新者 既存システム物理名: ECC06C';
COMMENT ON COLUMN PARKING.LOGICAL_DELETE_FLAG IS '論理削除サイン 既存システム物理名: ECC17S';
COMMENT ON COLUMN PARKING.BUILDING_CODE IS '建物コード 既存システム物理名: ECCABC';
COMMENT ON COLUMN PARKING.ACTUAL_BUILDING_CODE IS '実際建物コード 既存システム物理名: ECC24C';
COMMENT ON COLUMN PARKING.PARKING_LOT_CODE IS '駐車場コード 既存システム物理名: ECCBSC';
COMMENT ON COLUMN PARKING.PARKING_LOT_NUMBER IS '駐車場番号 既存システム物理名: ECC15N';
COMMENT ON COLUMN PARKING.CONSOLIDATED_BUILDING_CODE IS '合算先建物コード 既存システム物理名: ECC19C';
COMMENT ON COLUMN PARKING.CONSOLIDATED_PARKING_CODE IS '合算先駐車場コード 既存システム物理名: ECC20C';
COMMENT ON COLUMN PARKING.CONSOLIDATED_PARKING_COUNT IS '合算駐車場台数 既存システム物理名: ECC21Q';
COMMENT ON COLUMN PARKING.TRANSFERRED_BUILDING_CODE IS '付替元建物コード 既存システム物理名: ECC22C';
COMMENT ON COLUMN PARKING.PRE_CONSOLIDATION_AVAILABLE IS '合算前使用可能予定 既存システム物理名: ECC23D';
COMMENT ON COLUMN PARKING.BULK_LEASE_FLAG IS '一括借上サイン 既存システム物理名: ECC18S';
COMMENT ON COLUMN PARKING.DEPOSIT_ZERO_CONSENT_CATEGORY IS '敷金ゼロ承諾区分 既存システム物理名: ECC25S';
COMMENT ON COLUMN PARKING.LANDLORD_CODE_10 IS '家主コード(10) 既存システム物理名: ECCCKC';
COMMENT ON COLUMN PARKING.TENANT_CATEGORY IS '入居者区分 既存システム物理名: ECCCSB';
COMMENT ON COLUMN PARKING.ROOF_FLAG IS '屋根有無サイン 既存システム物理名: ECC07S';
COMMENT ON COLUMN PARKING.RECRUITMENT_FLAG IS '募集中サイン 既存システム物理名: ECC08S';
COMMENT ON COLUMN PARKING.AVAILABLE_DATE IS '使用可能予定日 既存システム物理名: ECC09D';
COMMENT ON COLUMN PARKING.MANAGEMENT_FLAG IS '管理サイン 既存システム物理名: ECC10S';
COMMENT ON COLUMN PARKING.MANAGEMENT_UNIT_CATEGORY IS '管理単位区分 既存システム物理名: ECCEUB';
COMMENT ON COLUMN PARKING.TENANT_RECRUITMENT_COUNT IS '客付回数 既存システム物理名: ECC11S';
COMMENT ON COLUMN PARKING.MANAGEMENT_CONTRACT_CASH_RECEIVED IS '管理契約現金受入額 既存システム物理名: ECC12A';
COMMENT ON COLUMN PARKING.ASSESSMENT_REVIEW_DOCUMENT_NUMBER IS '査定審査書番号 既存システム物理名: ECCBHN';
COMMENT ON COLUMN PARKING.INTERFACE_FLAG IS 'インターフェースサイン 既存システム物理名: ECC16S';
COMMENT ON COLUMN PARKING.DATA_MIGRATION_SOURCE_KEY_1 IS 'データ移行元キー1 既存システム物理名: ECC13N';
COMMENT ON COLUMN PARKING.DATA_MIGRATION_SOURCE_KEY_2 IS 'データ移行元キー2 既存システム物理名: ECC14N';
COMMENT ON COLUMN PARKING.ADDITIONAL_AD_FEE_MONTHS IS '広告料上乗月数 既存システム物理名: ECC26Q';
COMMENT ON COLUMN PARKING.NEW_GUARANTEE_RATE IS '新保証率 既存システム物理名: ECC10A';
COMMENT ON COLUMN PARKING.NEW_GUARANTEE_MANAGEMENT_RATE IS '新保証管理率 既存システム物理名: ECC10B';
COMMENT ON COLUMN PARKING.CONTRACT_MUTUAL_AID_FEE_RATE IS '請負契約共済会費率 既存システム物理名: ECC10C';
COMMENT ON COLUMN PARKING.NON_STANDARD_CATEGORY IS '定型外区分 既存システム物理名: ECCTGS';
COMMENT ON COLUMN PARKING.PARKING_CATEGORY IS '駐車場区分 既存システム物理名: ECC27B 1:単 2:縦3:軽4:立';
COMMENT ON COLUMN PARKING.OFF_SITE_PARKING_CATEGORY IS '敷地外駐車場区分 既存システム物理名: ECC28B';
