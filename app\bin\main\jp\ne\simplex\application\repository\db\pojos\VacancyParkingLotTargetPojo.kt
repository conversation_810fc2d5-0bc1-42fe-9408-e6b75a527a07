package jp.ne.simplex.application.repository.db.pojos

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.ParkingLot.AssessmentDivision
import jp.ne.simplex.application.model.VacancyParkingLotTarget
import jp.ne.simplex.application.model.VacancyParkingLotTarget.BulkLeaseFlag
import org.slf4j.LoggerFactory

class VacancyParkingLotTargetPojo(
    val buildingCode: String,
    val parkingLotCode: String,
    val parkingLotNumber: String?,
    val parkingFee: Int? = null,
    val bulkLeaseFlag: Int? = null,
    val assessmentDivision: String? = null,
    val parkingCategory: String? = null,
) {
    companion object {
        private val log = LoggerFactory.getLogger(VacancyParkingLotTargetPojo::class.java)
    }

    fun toVacancyParkingLotTarget(): VacancyParkingLotTarget? {
        return try {
            return VacancyParkingLotTarget(
                id = ParkingLot.Id(
                    Building.Code.of(this.buildingCode),
                    ParkingLot.Code.of(this.parkingLotCode)
                ),
                parkingLotNumber = this.parkingLotNumber!!,
                parkingFee = this.parkingFee!!,
                bulkLeaseFlag = BulkLeaseFlag.fromValue(this.bulkLeaseFlag)!!,
                assessmentDivision = this.assessmentDivision?.let {
                    AssessmentDivision.fromValue(it.toInt())
                },
                parkingLotCategory = ParkingLot.Category.fromValue(this.parkingCategory)!!,
            )
        } catch (e: Exception) {
            // データ不備がなければ発生しないはず
            log.warn("Invalid VacancyParkingLotTargetPojo. VacancyParkingLotTargetPojo=${this}", e)
            null
        }
    }
}
