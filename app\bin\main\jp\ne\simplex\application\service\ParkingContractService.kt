package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.db.ParkingContractPossibilityRepositoryInterface
import jp.ne.simplex.application.repository.db.ParkingRepositoryInterface
import jp.ne.simplex.application.repository.external.dkportal.DKPortalRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.shared.MultiThreadRunBlocking.Companion.runAsyncTasks
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jooq.Configuration
import org.jooq.DSLContext
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.util.*

@Service
class ParkingContractService(
    private val context: DSLContext,
    private val parkingContractPossibilityRepository: ParkingContractPossibilityRepositoryInterface,
    private val parkingDetailsService: ParkingDetailsService,
    private val parkingRepository: ParkingRepositoryInterface,
    private val dkPortalRepository: DKPortalRepositoryInterface,
) {
    @Value("\${app.parking.contract-possibility.update.parallel-num}")
    val parkingContractPossibilityUpdateParallelNum = 1

    companion object {
        private val log = LoggerFactory.getLogger(ParkingContractService::class.java)

        // 区画数による契約可否の判定
        fun getParkingContractPossibility(
            orderCode: Building.OrderCode,
            parkingLotCount: Int,
            vacancyParkingLotCount: Int,
            roomCount: Int,
            vacancyRoomCount: Int,
            specialContractFlag: Boolean,
            isAutoJudge: ParkingContractPossibility.ContractPossibilityAutoJudge,
        ): ParkingContractPossibility {

            val firstParkingStatus = when {
                vacancyParkingLotCount == 0 -> {
                    when {
                        parkingLotCount >= roomCount ->
                            if (specialContractFlag) ParkingContractPossibility.ContractPossibility.REQUIRED_CONFIRM
                            else ParkingContractPossibility.ContractPossibility.IMPOSSIBLE

                        else -> ParkingContractPossibility.ContractPossibility.IMPOSSIBLE
                    }
                }

                else -> ParkingContractPossibility.ContractPossibility.POSSIBLE
            }

            val secondParkingStatus = when {
                vacancyParkingLotCount > vacancyRoomCount -> {
                    if (parkingLotCount > roomCount) ParkingContractPossibility.ContractPossibility.POSSIBLE
                    else ParkingContractPossibility.ContractPossibility.REQUIRED_CONFIRM
                }

                else -> ParkingContractPossibility.ContractPossibility.IMPOSSIBLE
            }

            return ParkingContractPossibility(
                orderCode,
                firstParkingStatus,
                secondParkingStatus,
                isAutoJudge
            )
        }
    }

    /** 指定された受注コードの駐車場契約可否情報を取得する。 */
    fun getParkingContractPossibility(
        orderCode: Building.OrderCode,
        parkingList: List<Parking>,
    ): ParkingContractPossibility {
        return parkingContractPossibilityRepository.findBy(orderCode)
            ?: getNewParkingContractPossibility(
                orderCode = orderCode,
                isAutoJudge = ParkingContractPossibility.ContractPossibilityAutoJudge.AUTO,
                parkingList = parkingList,
                vacancyRoomCount = parkingRepository.getVacancyRoomCountByOrderCode(orderCode),
                roomCount = parkingRepository.getRoomCountByOrderCode(orderCode),
            )
    }

    fun getNewParkingContractPossibility(
        orderCode: Building.OrderCode,
        isAutoJudge: ParkingContractPossibility.ContractPossibilityAutoJudge,
        parkingList: List<Parking>,
        vacancyRoomCount: Int,
        roomCount: Int,
    ): ParkingContractPossibility {
        var parkingLotCount = 0
        var vacancyParkingLotCount = 0
        var specialContractFlag = false
        for (parking in parkingList) {
            for (lot in parking.parkingLotList) {
                if (lot.vacancyParkingStatus == ParkingLot.VacancyStatus.POSSIBLE) {
                    if (lot.parkingLotCategory == ParkingLot.Category.PARALLEL) {
                        return ParkingContractPossibility(
                            orderCode,
                            ParkingContractPossibility.ContractPossibility.POSSIBLE,
                            ParkingContractPossibility.ContractPossibility.POSSIBLE,
                            isAutoJudge,
                        )
                    }
                    vacancyParkingLotCount++
                }
                if (lot.specialContractFlag == ParkingLot.SpecialContractFlag.YES) {
                    specialContractFlag = true
                }
            }
            parkingLotCount += parking.parkingLotList.size
            log.info(
                "建物コード: ${parking.building.code.value}, " +
                        "駐車場区画数: ${parking.parkingLotList.size} "
            )
        }
        log.info(
            "受注コード: $orderCode, " +
                    "自動判定フラグ: $isAutoJudge, " +
                    "駐車場数: ${parkingList.size}, " +
                    "駐車場区画数: $parkingLotCount, " +
                    "空き区画数: $vacancyParkingLotCount " +
                    "空き部屋数: $vacancyRoomCount " +
                    "部屋数: $roomCount " +
                    "特約フラグ: $specialContractFlag "
        )
        return getParkingContractPossibility(
            orderCode,
            parkingLotCount,
            vacancyParkingLotCount,
            roomCount,
            vacancyRoomCount,
            specialContractFlag,
            isAutoJudge,
        )
    }

    /** 駐車場テーブルの存在するすべての受注コードについて駐車場契約可否情報を更新して駐車場サマリを連携する。 */
    fun updateAllParkingContractPossibility(requestUser: AuthInfo.RequestUser):
            List<ParkingContractPossibility> {
        val orderCodes = getAllOrderCodes()
        log.info("start update-parking-summary batch. record count is ${orderCodes.size}")

        val results = Collections.synchronizedList(mutableListOf<ParkingContractPossibility>())
        val errors = Collections.synchronizedList(mutableListOf<Throwable>())

        runAsyncTasks(
            maxThreads = parkingContractPossibilityUpdateParallelNum,
            tasks = orderCodes
        ) { orderCode ->
            val start = System.currentTimeMillis()
            runCatching {
                val parkingList = parkingDetailsService.getParkingList(orderCode)

                val vacantRoomCount =
                    parkingRepository.getVacancyRoomCountByOrderCode(orderCode)

                val roomCount = parkingRepository.getRoomCountByOrderCode(orderCode)

                context.transactionResult { config ->
                    val (_, updated) =
                        updateParkingContractPossibility(
                            config = config,
                            requestUser = requestUser,
                            orderCode = orderCode,
                            parkingList = parkingList,
                            vacancyRoomCount = vacantRoomCount,
                            roomCount = roomCount,
                        )
                    updated
                }
            }.onSuccess {
                results.add(it)
            }.onFailure {
                log.error("updateParkingContractPossibility failed. orderCode=${orderCode}", it)
                errors.add(it)
            }
            log.info("End all process orderCode=${orderCode} time=${System.currentTimeMillis() - start}ms")
        }
        // 例外が発生した場合は最初の例外をthrow
        if (errors.isNotEmpty()) throw errors[0]

        results.sortBy { it.orderCode.value }
        return results
    }

    /** 駐車場テーブルに存在する全ての受注コードを取得する。 */
    private fun getAllOrderCodes(): List<Building.OrderCode> {
        return parkingRepository.getAllOrderCodes()
    }

    /**
     * 現在の駐車場の契約状態、予約状態で駐車場契約可否情報を更新する。存在しない場合は登録。
     */
    fun updateParkingContractPossibility(
        config: Configuration,
        requestUser: AuthInfo.RequestUser,
        orderCode: Building.OrderCode,
        parkingList: List<Parking>,
        vacancyRoomCount: Int,
        roomCount: Int,
    ): Pair<ParkingContractPossibility?, ParkingContractPossibility> {
        val current = parkingContractPossibilityRepository.findByIdForUpdate(config, orderCode)
        if (current == null) {
            val registered = getNewParkingContractPossibility(
                orderCode = orderCode,
                isAutoJudge = ParkingContractPossibility.ContractPossibilityAutoJudge.AUTO,
                parkingList = parkingList,
                vacancyRoomCount = vacancyRoomCount,
                roomCount = roomCount,
            )
            parkingContractPossibilityRepository.register(config, requestUser, registered)
            return Pair(null, registered)
        }

        val updated = getNewParkingContractPossibility(
            orderCode = orderCode,
            isAutoJudge = current.isAutoJudge,
            parkingList = parkingList,
            vacancyRoomCount = vacancyRoomCount,
            roomCount = roomCount,
        )
        if (current != updated) {
            parkingContractPossibilityRepository.update(config, requestUser, updated)
        }
        return Pair(current, updated)
    }

    /**
     * 駐車場予約更新契機で駐車場契約可否情報を更新する。存在しない場合は登録。
     */
    fun updateParkingContractPossibilityAndInformDKPortal(
        requestUser: AuthInfo.RequestUser, orderCode: Building.OrderCode, fromBatch: Boolean = false
    ) {
        val parkingList = parkingDetailsService.getParkingList(orderCode)
        val vacantRoomCount = parkingRepository.getVacancyRoomCountByOrderCode(orderCode)
        val roomCount = parkingRepository.getRoomCountByOrderCode(orderCode)

        context.transactionResult { config ->
            val (current, updated) =
                updateParkingContractPossibility(
                    config = config,
                    requestUser = requestUser,
                    orderCode = orderCode,
                    parkingList = parkingList,
                    vacancyRoomCount = vacantRoomCount,
                    roomCount = roomCount,
                )

            // 更新が必要な場合に非同期で駐車場を更新
            if (current == null || (current != updated
                        && dkPortalRepository.isNeedUpdateParkingLot(updated, current))
            ) {
                if (fromBatch) updateParkingLotSync(updated) else updateParkingLotAsync(updated)
            }
        }
    }

    // DKポータルへの駐車場詳細検索情報連携は非同期で行う
    private fun updateParkingLotAsync(parkingContractPossibility: ParkingContractPossibility) {
        CoroutineScope(Dispatchers.IO).launch {
            updateParkingLotSync(parkingContractPossibility)
        }
    }

    private fun updateParkingLotSync(parkingContractPossibility: ParkingContractPossibility) {
        dkPortalRepository.updateParkingLot(parkingContractPossibility)
    }

    fun updateIsAutoJudge(
        requestUser: AuthInfo.RequestUser,
        updateParkingIsAutoJudge: UpdateParkingContractIsAutoJudge
    ) {
        val (current, updated) = context.transactionResult { config ->
            updateIsAutoJudge(config, requestUser, updateParkingIsAutoJudge)
        }
        if (current == null) {
            // current==nullは更新対象が存在しない場合
            log.warn("parking contract possibility does not exist")
            return
        }
        if (updated == null) {
            // updated==nullは変更前と変わらない場合
            return
        }

        if (!dkPortalRepository.isNeedUpdateParkingLot(current, updated)) {
            // 要問合せフラグを変更した結果、DKポータルでの表示が変わらない場合はAPIをコールしない
            return
        }

        updateParkingLotAsync(updated)
    }

    private fun updateIsAutoJudge(
        config: Configuration,
        requestUser: AuthInfo.RequestUser,
        updateParkingIsAutoJudge: UpdateParkingContractIsAutoJudge
    ): Pair<ParkingContractPossibility?, ParkingContractPossibility?> {
        val orderCode = updateParkingIsAutoJudge.orderCode

        val current = parkingContractPossibilityRepository.findByIdForUpdate(config, orderCode)

        if (current == null) {
            val registered = getNewParkingContractPossibility(
                orderCode = orderCode,
                isAutoJudge = updateParkingIsAutoJudge.isAutoJudge,
                parkingList = parkingDetailsService.getParkingList(orderCode),
                vacancyRoomCount = parkingRepository.getVacancyRoomCountByOrderCode(orderCode),
                roomCount = parkingRepository.getRoomCountByOrderCode(orderCode),
            )
            parkingContractPossibilityRepository.register(config, requestUser, registered)
            return Pair(null, registered)
        }

        // 要問合せフラグが変化しない場合、DB更新しない
        if (current.isAutoJudge == updateParkingIsAutoJudge.isAutoJudge) {
            return Pair(current, null)
        }
        val newStatus = current.copy(isAutoJudge = updateParkingIsAutoJudge.isAutoJudge)
        parkingContractPossibilityRepository.update(config, requestUser, newStatus)
        return Pair(current, newStatus)
    }
}
