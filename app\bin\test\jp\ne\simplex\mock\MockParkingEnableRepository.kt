package jp.ne.simplex.mock

import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.UpdateParkingLotAvailability
import jp.ne.simplex.application.repository.db.ParkingEnableRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo

class MockParkingEnableRepository(
    val isAvailableFunc: (parkingLotId: ParkingLot.Id) -> Boolean = { _ -> true },
    val upsertFunc: (requestUser: AuthInfo.RequestUser, param: UpdateParkingLotAvailability) -> Unit = { _, _ -> },
) : ParkingEnableRepositoryInterface {

    override fun isAvailable(parkingLotId: ParkingLot.Id): Boolean {
        return isAvailableFunc(parkingLotId)
    }

    override fun upsert(requestUser: AuthInfo.RequestUser, param: UpdateParkingLotAvailability) {
        upsertFunc(requestUser, param)
    }
}
