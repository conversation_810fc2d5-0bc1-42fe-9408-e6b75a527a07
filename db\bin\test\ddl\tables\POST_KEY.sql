-- TABLE: POST_KEY(ポスト鍵)

CREATE TABLE POST_KEY(
     CREATION_DATE                                numeric(10,0)                 
,    CREATION_TIME                                numeric(8,0)                  
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(10,0)                 
,    UPDATE_TIME                                  numeric(8,0)                  
,    UPDATER                                      varchar(10)                   
,    BUILDING_CD                                  varchar(9)        NOT NULL    
,    ROOM_CD                                      varchar(5)        NOT NULL    
,    POST_KEY_INFO                                varchar(120)                  
,    INTERNET_ID                                  varchar(40)                   
,    INTERNET_PASSWORD                            varchar(64)                   
,    CONSTRAINT PK_POST_KEY PRIMARY KEY (BUILDING_CD, ROOM_CD)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE POST_KEY IS 'ポスト鍵 既存システム物理名: EMPSTP';
COMMENT ON COLUMN POST_KEY.CREATION_DATE IS '作成年月日 既存システム物理名: EMP01D';
COMMENT ON COLUMN POST_KEY.CREATION_TIME IS '作成時刻 既存システム物理名: EMP02H';
COMMENT ON COLUMN POST_KEY.CREATOR IS '作成者 既存システム物理名: EMP03C';
COMMENT ON COLUMN POST_KEY.UPDATE_DATE IS '更新年月日 既存システム物理名: EMP04D';
COMMENT ON COLUMN POST_KEY.UPDATE_TIME IS '更新時刻 既存システム物理名: EMP05H';
COMMENT ON COLUMN POST_KEY.UPDATER IS '更新者 既存システム物理名: EMP06C';
COMMENT ON COLUMN POST_KEY.BUILDING_CD IS '建物CD 既存システム物理名: EMP07C';
COMMENT ON COLUMN POST_KEY.ROOM_CD IS '部屋CD 既存システム物理名: EMP08C';
COMMENT ON COLUMN POST_KEY.POST_KEY_INFO IS 'ポスト鍵情報 既存システム物理名: EMP09X';
COMMENT ON COLUMN POST_KEY.INTERNET_ID IS 'インターネットID 既存システム物理名: EMP10X';
COMMENT ON COLUMN POST_KEY.INTERNET_PASSWORD IS 'インターネットパスワード 既存システム物理名: EMP11X';
