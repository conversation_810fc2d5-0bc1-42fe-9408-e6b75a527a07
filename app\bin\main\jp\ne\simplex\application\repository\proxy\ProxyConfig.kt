package jp.ne.simplex.application.repository.proxy

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

class Proxy(
    val host: String,
    val port: Int
)

@Configuration
class ProxyConfig(
    @Value("\${proxy.host}") private val proxyHost: String,
    @Value("\${proxy.port}") private val proxyPort: Int,
) {
    @Bean
    fun proxy(): Proxy? {
        if (proxyHost == "dummy" && proxyPort == 0) {
            return null
        }
        return Proxy(proxyHost, proxyPort)
    }
}
