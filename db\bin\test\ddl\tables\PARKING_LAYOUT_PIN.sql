-- TABLE: PARKING_LAYOUT_PIN(駐車場配置図区画ピン)

CREATE TABLE PARKING_LAYOUT_PIN(
     BUILDING_CODE                                varchar(9)        NOT NULL    
,    PARKING_LOT_CODE                             varchar(3)        NOT NULL    
,    X_COORDINATE                                 numeric(5,2)                  
,    Y_COORDINATE                                 numeric(5,2)                  
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    DELETE_FLAG                                  varchar(1)        NOT NULL    
,    CONSTRAINT PK_PARKING_LAYOUT_PIN PRIMARY KEY (BUILDING_CODE, PARKING_LOT_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PARKING_LAYOUT_PIN IS '駐車場配置図区画ピン 既存システム物理名: -';
COMMENT ON COLUMN PARKING_LAYOUT_PIN.BUILDING_CODE IS '建物コード 既存システム物理名: -';
COMMENT ON COLUMN PARKING_LAYOUT_PIN.PARKING_LOT_CODE IS '駐車場コード 既存システム物理名: -';
COMMENT ON COLUMN PARKING_LAYOUT_PIN.X_COORDINATE IS 'X座標 既存システム物理名: -';
COMMENT ON COLUMN PARKING_LAYOUT_PIN.Y_COORDINATE IS 'Y座標 既存システム物理名: -';
COMMENT ON COLUMN PARKING_LAYOUT_PIN.CREATION_DATE IS '作成年月日 既存システム物理名: -';
COMMENT ON COLUMN PARKING_LAYOUT_PIN.CREATION_TIME IS '作成時刻 既存システム物理名: -';
COMMENT ON COLUMN PARKING_LAYOUT_PIN.CREATOR IS '作成者 既存システム物理名: -';
COMMENT ON COLUMN PARKING_LAYOUT_PIN.UPDATE_DATE IS '更新年月日 既存システム物理名: -';
COMMENT ON COLUMN PARKING_LAYOUT_PIN.UPDATE_TIME IS '更新時刻 既存システム物理名: -';
COMMENT ON COLUMN PARKING_LAYOUT_PIN.UPDATER IS '更新者 既存システム物理名: -';
COMMENT ON COLUMN PARKING_LAYOUT_PIN.DELETE_FLAG IS '削除フラグ 既存システム物理名: -';
