package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.model.LoginInfo
import jp.ne.simplex.db.jooq.gen.tables.references.PASSWORD_MASTER
import org.jooq.DSLContext
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository

@Repository
class AuthRepository(
    private val context: DSLContext,
    private val employeeRepository: EmployeeRepositoryInterface,
) {

    companion object {
        private val log = LoggerFactory.getLogger(AuthRepository::class.java)
    }

    fun login(loginInfo: LoginInfo): Employee? {
        val employeeCode = loginInfo.employeeCode
        val password = loginInfo.password

        val loginResult = context.select().from(PASSWORD_MASTER)
            .where(PASSWORD_MASTER.EMPLOYEE_ID.eq(employeeCode.value))
            .and(PASSWORD_MASTER.CURRENT_PASSWORD.eq(password)).count() > 0

        if (!loginResult) {
            log.info("Records matching the search parameters(employeeId=$employeeCode.value, password=******]) do not exist.")
            return null
        }
        return getEmployee(employeeCode)
    }

    fun getEmployee(employeeCode: Employee.Code): Employee? {
        val employee = employeeRepository.findBy(employeeCode)

        if (employee == null) {
            log.info("Employee(employeeId=$employeeCode.value) does not exists.")
            return null
        }
        return employee
    }
}
