-- TABLE: PROPERTY_BASIC_FILE(物件基本ファイル)

CREATE TABLE PROPERTY_BASIC_FILE(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM                               varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    DELETE_FLAG                                  varchar(1)                    
,    ORDER_CODE                                   numeric(7,0)                  
,    ADDITIONAL_CODE                              numeric(3,0)                  
,    LEASE_TAX_CELL_DIVISION                      varchar(1)                    
,    CONSTRUCTION_NAME                            varchar(42)                   
,    CONTRACT_TYPE_OCCURRENCE                     varchar(1)                    
,    BUILDING_COUNT                               numeric(3,0)                  
,    CLIENT_OTHER_CONSTRUCTION_DIVISION           varchar(1)                    
,    CLIENT_OTHER_COMPLETION_DATE                 numeric(8,0)                  
,    MUNICIPALITY_CODE_1                          numeric(2,0)                  
,    MUNICIPALITY_CODE_2                          numeric(3,0)                  
,    CONSTRUCTION_LOCATION_1                      varchar(42)                   
,    CONSTRUCTION_LOCATION_2                      varchar(42)                   
,    CONSTRUCTION_LOCATION_3                      varchar(42)                   
,    ORDER_COMPANY_CODE                           varchar(3)                    
,    ORDER_OFFICE_CODE                            numeric(6,0)                  
,    CONTRACT_COMPANY_CODE                        varchar(3)                    
,    CONTRACT_OFFICE_CODE                         numeric(6,0)                  
,    BUILD_MANAGER_CODE                           numeric(6,0)                  
,    DESIGN_COMPANY_CODE                          varchar(3)                    
,    DESIGN_OFFICE_CODE                           varchar(6)                    
,    DESIGN_MANAGER_CODE                          numeric(6,0)                  
,    DESIGN_SUPERVISOR_CODE                       numeric(6,0)                  
,    APPLICATION_COMPANY_CODE                     varchar(3)                    
,    APPLICATION_OFFICE_CODE                      numeric(6,0)                  
,    APPLICATION_MANAGER_CODE                     numeric(6,0)                  
,    ESTIMATION_COMPANY_CODE                      varchar(3)                    
,    ESTIMATION_OFFICE_CODE                       varchar(6)                    
,    ESTIMATION_MANAGER_CODE                      numeric(6,0)                  
,    DEVELOPMENT_COMPANY_CODE                     varchar(3)                    
,    DEVELOPMENT_OFFICE_CODE                      varchar(6)                    
,    DEVELOPMENT_MANAGER_CODE                     numeric(6,0)                  
,    CONSTRUCTION_COMPANY_CODE                    varchar(3)                    
,    CONSTRUCTION_OFFICE_CODE                     numeric(6,0)                  
,    CONSTRUCTION_SUPERVISOR_CODE                 numeric(6,0)                  
,    CONSTRUCTION_STAFF_CODE                      numeric(6,0)                  
,    CONSTRUCTION_MANAGER_CODE                    numeric(6,0)                  
,    CHIEF_ARCHITECT_CODE                         numeric(6,0)                  
,    ORDER_DATE                                   numeric(8,0)                  
,    CONTRACT_PLANNED_DATE                        numeric(8,0)                  
,    CONTRACT_DATE                                numeric(8,0)                  
,    LAST_CHANGE_CONTRACT_DATE                    numeric(8,0)                  
,    AGREED_CANCELLATION_DATE                     numeric(8,0)                  
,    COMPLETION_REPORT_OUTPUT_COUNT               numeric(3,0)                  
,    COMPLETION_REPORT_CREATION_DATE              numeric(8,0)                  
,    COMPLETION_REPORT_CREATION_COMPANY_CODE      varchar(3)                    
,    COMPLETION_REPORT_CREATION_OFFICE_CODE       varchar(6)                    
,    COMPLETION_REPORT_CREATION_MANAGER_CODE      varchar(6)                    
,    COMPLETION_HANDOVER_DATE                     numeric(8,0)                  
,    COMPLETION_APPROVAL_DATE                     numeric(8,0)                  
,    COMPLETION_REPORT_COLLECTION_INPUT_DATE      numeric(8,0)                  
,    COMPLETION_REPORT_COLLECTION_COMPANY_CD      varchar(3)                    
,    COMPLETION_REPORT_COLLECTION_OFFICE_CODE     varchar(6)                    
,    COMPLETION_REPORT_COLLECTION_MANAGER_CD      varchar(6)                    
,    COMPLETION_REPORT_HEAD_OFFICE_RECEIPT_DATE   numeric(8,0)                  
,    COMPLETION_REPORT_RECEIPT_COMPANY_CODE       varchar(3)                    
,    COMPLETION_REPORT_RECEIPT_OFFICE_CODE        varchar(6)                    
,    COMPLETION_REPORT_RECEIPT_MANAGER_CODE       varchar(6)                    
,    OCCUPANCY_PLANNED_DATE                       numeric(8,0)                  
,    PUBLIC_FINANCING_DIVISION                    varchar(1)                    
,    DEVELOPMENT_APPLICATION_DIVISION             varchar(1)                    
,    REVIEW_DIVISION                              varchar(2)                    
,    APPLICATION_PROCESS_STATE_DIVISION           varchar(1)                    
,    APPLICATION_PROCESS_HOLD_START_DATE          numeric(8,0)                  
,    APPLICATION_PROCESS_HOLD_DAYS                numeric(3,0)                  
,    CONSTRUCTION_PROCESS_STATE_DIVISION          varchar(1)                    
,    CONSTRUCTION_PROCESS_HOLD_START_DATE         numeric(8,0)                  
,    CONSTRUCTION_PROCESS_HOLD_DAYS               numeric(3,0)                  
,    CS_OUTPUT_COUNT                              numeric(3,0)                  
,    PROCESS_CHANGE_STATUS                        varchar(1)                    
,    ADDITIONAL_CONSTRUCTION_DIVISION             varchar(1)                    
,    APPLICATION_ACCEPTANCE_PLANNED_DATE_1        numeric(8,0)                  
,    APPLICATION_ACCEPTANCE_PLANNED_DATE_2        numeric(8,0)                  
,    APPLICATION_ACCEPTANCE_PLANNED_DATE_3        numeric(8,0)                  
,    APPLICATION_ACCEPTANCE_PLANNED_DATE_4        numeric(8,0)                  
,    APPLICATION_ACCEPTANCE_ACTUAL_DATE           numeric(8,0)                  
,    APPLICATION_PERMISSION_PLANNED_DATE_1        numeric(8,0)                  
,    APPLICATION_PERMISSION_PLANNED_DATE_2        numeric(8,0)                  
,    APPLICATION_PERMISSION_PLANNED_DATE_3        numeric(8,0)                  
,    APPLICATION_PERMISSION_PLANNED_DATE_4        numeric(8,0)                  
,    APPLICATION_PERMISSION_ACTUAL_DATE           numeric(8,0)                  
,    CONSTRUCTION_START_PLANNED_DATE_1            numeric(8,0)                  
,    CONSTRUCTION_START_PLANNED_DATE_2            numeric(8,0)                  
,    CONSTRUCTION_START_PLANNED_DATE_3            numeric(8,0)                  
,    CONSTRUCTION_START_PLANNED_DATE_4            numeric(8,0)                  
,    CONSTRUCTION_START_ACTUAL_DATE               numeric(8,0)                  
,    CONSTRUCTION_COMPLETION_PLANNED_DATE_1       numeric(8,0)                  
,    CONSTRUCTION_COMPLETION_PLANNED_DATE_2       numeric(8,0)                  
,    CONSTRUCTION_COMPLETION_PLANNED_DATE_3       numeric(8,0)                  
,    CONSTRUCTION_COMPLETION_PLANNED_DATE_4       numeric(8,0)                  
,    CONSTRUCTION_COMPLETION_ACTUAL_DATE          numeric(8,0)                  
,    MAJOR_SCHEDULE_CODE_01                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_02                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_03                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_04                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_05                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_06                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_07                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_08                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_09                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_10                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_11                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_12                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_13                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_14                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_15                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_16                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_17                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_18                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_19                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_20                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_21                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_22                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_23                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_24                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_25                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_26                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_27                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_28                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_29                       numeric(3,0)                  
,    MAJOR_SCHEDULE_CODE_30                       numeric(3,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_01               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_02               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_03               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_04               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_05               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_06               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_07               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_08               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_09               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_10               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_11               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_12               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_13               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_14               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_15               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_16               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_17               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_18               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_19               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_20               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_21               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_22               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_23               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_24               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_25               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_26               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_27               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_28               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_29               numeric(8,0)                  
,    MAJOR_SCHEDULE_PLANNED_DATE_30               numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_01                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_02                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_03                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_04                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_05                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_06                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_07                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_08                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_09                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_10                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_11                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_12                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_13                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_14                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_15                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_16                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_17                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_18                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_19                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_20                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_21                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_22                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_23                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_24                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_25                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_26                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_27                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_28                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_29                numeric(8,0)                  
,    MAJOR_SCHEDULE_ACTUAL_DATE_30                numeric(8,0)                  
,    ORDER_CODE_ST                                varchar(7)                    
,    ADDITIONAL_CODE_ST                           varchar(3)                    
,    BUILD_MANAGER_CODE_ST                        varchar(6)                    
,    DESIGN_MANAGER_CODE_ST                       varchar(6)                    
,    DESIGN_SUPERVISOR_CODE_ST                    varchar(6)                    
,    APPLICATION_MANAGER_CODE_ST                  varchar(6)                    
,    ESTIMATION_MANAGER_CODE_ST                   varchar(6)                    
,    DEVELOPMENT_MANAGER_CODE_ST                  varchar(6)                    
,    CONSTRUCTION_SUPERVISOR_CODE_ST              varchar(6)                    
,    CONSTRUCTION_STAFF_CODE_ST                   varchar(6)                    
,    CONSTRUCTION_MANAGER_CODE_ST                 varchar(6)                    
,    CHIEF_ARCHITECT_CODE_ST                      varchar(6)                    
,    CONSTRUCTION_SUPERVISION_OFFICE_CODE         varchar(6)                    
,    MECHANISM_FINANCING_DIVISION                 varchar(1)                    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PROPERTY_BASIC_FILE IS '物件基本ファイル 既存システム物理名: BGNF1P';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: BGN01D 削除フラグ ":有効 "C":工事中止 "D":論理 "';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CREATION_TIME IS '作成時刻 既存システム物理名: BGN02H';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: BGN03D';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.UPDATE_TIME IS '更新時刻 既存システム物理名: BGN04H';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.UPDATE_PROGRAM IS '更新プログラム 既存システム物理名: BGN05P';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.UPDATER IS '更新者 既存システム物理名: BGN06P';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.DELETE_FLAG IS '削除フラグ 既存システム物理名: BGN08S';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.ORDER_CODE IS '受注コード 既存システム物理名: BGNJ04';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.ADDITIONAL_CODE IS '追加コード 既存システム物理名: BGNT14';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.LEASE_TAX_CELL_DIVISION IS 'リースタクセル区分 既存システム物理名: BGNRTB';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_NAME IS '工事名称 既存システム物理名: BGNK11';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONTRACT_TYPE_OCCURRENCE IS '工事発生契約種別 既存システム物理名: BGNDIB';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.BUILDING_COUNT IS '棟数 既存システム物理名: BGNT15';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CLIENT_OTHER_CONSTRUCTION_DIVISION IS '施主・他社施工区分 既存システム物理名: BGNEBB';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CLIENT_OTHER_COMPLETION_DATE IS '施主他社施工完了日 既存システム物理名: BGNV1D';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MUNICIPALITY_CODE_1 IS '自治体コード1 既存システム物理名: BGNJ01';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MUNICIPALITY_CODE_2 IS '自治体コード2 既存システム物理名: BGNJ02';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_LOCATION_1 IS '工事所在地1 既存システム物理名: BGNK21';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_LOCATION_2 IS '工事所在地2 既存システム物理名: BGNK22';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_LOCATION_3 IS '工事所在地3 既存システム物理名: BGNK23';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.ORDER_COMPANY_CODE IS '受注会社コード 既存システム物理名: BGNV2C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.ORDER_OFFICE_CODE IS '受注事業所コード 既存システム物理名: BGNJ05';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONTRACT_COMPANY_CODE IS '契約会社コード 既存システム物理名: BGNV3C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONTRACT_OFFICE_CODE IS '契約事業所コード 既存システム物理名: BGNK12';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.BUILD_MANAGER_CODE IS '建営担当者コード 既存システム物理名: BGNK14';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.DESIGN_COMPANY_CODE IS '設計担当会社コード 既存システム物理名: BGNV4C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.DESIGN_OFFICE_CODE IS '設計事業所コード 既存システム物理名: BGNV5C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.DESIGN_MANAGER_CODE IS '設計担当者コード 既存システム物理名: BGNS06';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.DESIGN_SUPERVISOR_CODE IS '設計管理職コード 既存システム物理名: BGNK37';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_COMPANY_CODE IS '申請会社コード 既存システム物理名: BGNV6C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_OFFICE_CODE IS '申請事業所コード 既存システム物理名: BGNS03';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_MANAGER_CODE IS '申請担当者コード 既存システム物理名: BGNS07';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.ESTIMATION_COMPANY_CODE IS '積算会社コード 既存システム物理名: BGNV7C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.ESTIMATION_OFFICE_CODE IS '積算事業所コード 既存システム物理名: BGNV8C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.ESTIMATION_MANAGER_CODE IS '積算担当者コード 既存システム物理名: BGNS05';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.DEVELOPMENT_COMPANY_CODE IS '開発会社コード 既存システム物理名: BGNV9C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.DEVELOPMENT_OFFICE_CODE IS '開発事業所コード 既存システム物理名: BGNW1C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.DEVELOPMENT_MANAGER_CODE IS '開発担当者コード 既存システム物理名: BGNK15';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_COMPANY_CODE IS '施工会社コード 既存システム物理名: BGNW2C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_OFFICE_CODE IS '施工事業所コード 既存システム物理名: BGNS04';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_SUPERVISOR_CODE IS '工事責任者コード 既存システム物理名: BGNS16';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_STAFF_CODE IS '工事担当者コード 既存システム物理名: BGNK13';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_MANAGER_CODE IS '工事管理職コード 既存システム物理名: BGNK24';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CHIEF_ARCHITECT_CODE IS '管理建築士コード 既存システム物理名: BGNK25';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.ORDER_DATE IS '受注年月日 既存システム物理名: BGNJ11';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONTRACT_PLANNED_DATE IS '契約予定年月日 既存システム物理名: BGNK16';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONTRACT_DATE IS '契約年月日 既存システム物理名: BGNK17';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.LAST_CHANGE_CONTRACT_DATE IS '最終変更契約年月日 既存システム物理名: BGNS18';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.AGREED_CANCELLATION_DATE IS '合意解約年月日 既存システム物理名: BGNG03';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_OUTPUT_COUNT IS '工事完成報告書 出力回数 既存システム物理名: BGNW4T';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_CREATION_DATE IS '工事完成報告書 作成日 既存システム物理名: BGNW5D';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_CREATION_COMPANY_CODE IS '工事完成報告書 作成会社コード 既存システム物理名: BGNW6C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_CREATION_OFFICE_CODE IS '工事完成報告書 作成事業所コード 既存システム物理名: BGNW7C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_CREATION_MANAGER_CODE IS '工事完成報告書 作成担当者コード 既存システム物理名: BGNW8C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_HANDOVER_DATE IS '完成引渡日 既存システム物理名: BGNK26';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_APPROVAL_DATE IS '工事完成承認日 既存システム物理名: BGNK38';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_COLLECTION_INPUT_DATE IS '完成報告回収入力日 既存システム物理名: BGNK39';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_COLLECTION_COMPANY_CD IS '工事完成報告書 回収会社コード 既存システム物理名: BGNW9C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_COLLECTION_OFFICE_CODE IS '工事完成報告書 回収事業所コード 既存システム物理名: BGNX1C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_COLLECTION_MANAGER_CD IS '工事完成報告書 回収担当者コード 既存システム物理名: BGNX2C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_HEAD_OFFICE_RECEIPT_DATE IS '工事完成報告書 本社受領日 既存システム物理名: BGNX3D';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_RECEIPT_COMPANY_CODE IS '工事完成報告書 受領会社コード 既存システム物理名: BGNX4C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_RECEIPT_OFFICE_CODE IS '工事完成報告書 受領事業所コード 既存システム物理名: BGNX5C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.COMPLETION_REPORT_RECEIPT_MANAGER_CODE IS '工事完成報告書 受領担当者コード 既存システム物理名: BGNX6C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.OCCUPANCY_PLANNED_DATE IS '入居予定年月日 既存システム物理名: BGNN01';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.PUBLIC_FINANCING_DIVISION IS '公庫融資有無区分 既存システム物理名: BGNK52';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.DEVELOPMENT_APPLICATION_DIVISION IS '開発申請有無区分 既存システム物理名: BGNK53';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.REVIEW_DIVISION IS '見直区分 既存システム物理名: BGNK95';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_PROCESS_STATE_DIVISION IS '申請工程状態区分 既存システム物理名: BGNK58';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_PROCESS_HOLD_START_DATE IS '申請工程保留開始日 既存システム物理名: BGNS32';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_PROCESS_HOLD_DAYS IS '申請工程保留日数 既存システム物理名: BGNS33';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_PROCESS_STATE_DIVISION IS '工事工程状態区分 既存システム物理名: BGNK59';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_PROCESS_HOLD_START_DATE IS '工事工程保留開始日 既存システム物理名: BGNKC1';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_PROCESS_HOLD_DAYS IS '工事工程保留日数 既存システム物理名: BGNKC2';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CS_OUTPUT_COUNT IS 'CS出力回数 既存システム物理名: BGNC01';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.PROCESS_CHANGE_STATUS IS '工程変更処理状況 既存システム物理名: BGNK54';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.ADDITIONAL_CONSTRUCTION_DIVISION IS '追加工事区分 既存システム物理名: BGNK97';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_ACCEPTANCE_PLANNED_DATE_1 IS '申請受理予定日 既存システム物理名: BGNS08';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_ACCEPTANCE_PLANNED_DATE_2 IS '申請受理予定日 既存システム物理名: BGNS09';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_ACCEPTANCE_PLANNED_DATE_3 IS '申請受理予定日 既存システム物理名: BGNS15';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_ACCEPTANCE_PLANNED_DATE_4 IS '申請受理予定日 既存システム物理名: BGNS17';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_ACCEPTANCE_ACTUAL_DATE IS '申請受理実績日 既存システム物理名: BGNS34';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_PERMISSION_PLANNED_DATE_1 IS '申請許可予定日 既存システム物理名: BGNS35';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_PERMISSION_PLANNED_DATE_2 IS '申請許可予定日 既存システム物理名: BGNS36';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_PERMISSION_PLANNED_DATE_3 IS '申請許可予定日 既存システム物理名: BGNS37';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_PERMISSION_PLANNED_DATE_4 IS '申請許可予定日 既存システム物理名: BGNS38';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_PERMISSION_ACTUAL_DATE IS '申請許可実績日 既存システム物理名: BGNS39';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_START_PLANNED_DATE_1 IS '着工予定日 既存システム物理名: BGNC03';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_START_PLANNED_DATE_2 IS '着工予定日 既存システム物理名: BGNC04';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_START_PLANNED_DATE_3 IS '着工予定日 既存システム物理名: BGNC05';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_START_PLANNED_DATE_4 IS '着工予定日 既存システム物理名: BGNC06';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_START_ACTUAL_DATE IS '着工実績日 既存システム物理名: BGNC07';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_COMPLETION_PLANNED_DATE_1 IS '完工予定日 既存システム物理名: BGNK27';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_COMPLETION_PLANNED_DATE_2 IS '完工予定日 既存システム物理名: BGNK28';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_COMPLETION_PLANNED_DATE_3 IS '完工予定日 既存システム物理名: BGNK29';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_COMPLETION_PLANNED_DATE_4 IS '完工予定日 既存システム物理名: BGNK30';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_COMPLETION_ACTUAL_DATE IS '完工実績日 既存システム物理名: BGNK31';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_01 IS '大日程コード01 既存システム物理名: BGND11';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_02 IS '大日程コード02 既存システム物理名: BGND12';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_03 IS '大日程コード03 既存システム物理名: BGND13';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_04 IS '大日程コード04 既存システム物理名: BGND14';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_05 IS '大日程コード05 既存システム物理名: BGND15';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_06 IS '大日程コード06 既存システム物理名: BGND16';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_07 IS '大日程コード07 既存システム物理名: BGND17';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_08 IS '大日程コード08 既存システム物理名: BGND18';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_09 IS '大日程コード09 既存システム物理名: BGND19';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_10 IS '大日程コード10 既存システム物理名: BGND20';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_11 IS '大日程コード11 既存システム物理名: BGND21';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_12 IS '大日程コード12 既存システム物理名: BGND22';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_13 IS '大日程コード13 既存システム物理名: BGND23';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_14 IS '大日程コード14 既存システム物理名: BGND24';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_15 IS '大日程コード15 既存システム物理名: BGND25';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_16 IS '大日程コード16 既存システム物理名: BGND26';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_17 IS '大日程コード17 既存システム物理名: BGND27';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_18 IS '大日程コード18 既存システム物理名: BGND28';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_19 IS '大日程コード19 既存システム物理名: BGND29';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_20 IS '大日程コード20 既存システム物理名: BGND30';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_21 IS '大日程コード21 既存システム物理名: BGND31';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_22 IS '大日程コード22 既存システム物理名: BGND32';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_23 IS '大日程コード23 既存システム物理名: BGND33';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_24 IS '大日程コード24 既存システム物理名: BGND34';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_25 IS '大日程コード25 既存システム物理名: BGND35';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_26 IS '大日程コード26 既存システム物理名: BGND36';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_27 IS '大日程コード27 既存システム物理名: BGND37';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_28 IS '大日程コード28 既存システム物理名: BGND38';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_29 IS '大日程コード29 既存システム物理名: BGND39';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_CODE_30 IS '大日程コード30 既存システム物理名: BGND40';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_01 IS '大日程予定日01 既存システム物理名: BGND51';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_02 IS '大日程予定日02 既存システム物理名: BGND52';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_03 IS '大日程予定日03 既存システム物理名: BGND53';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_04 IS '大日程予定日04 既存システム物理名: BGND54';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_05 IS '大日程予定日05 既存システム物理名: BGND55';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_06 IS '大日程予定日06 既存システム物理名: BGND56';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_07 IS '大日程予定日07 既存システム物理名: BGND57';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_08 IS '大日程予定日08 既存システム物理名: BGND58';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_09 IS '大日程予定日09 既存システム物理名: BGND59';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_10 IS '大日程予定日10 既存システム物理名: BGND60';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_11 IS '大日程予定日11 既存システム物理名: BGND61';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_12 IS '大日程予定日12 既存システム物理名: BGND62';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_13 IS '大日程予定日13 既存システム物理名: BGND63';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_14 IS '大日程予定日14 既存システム物理名: BGND64';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_15 IS '大日程予定日15 既存システム物理名: BGND65';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_16 IS '大日程予定日16 既存システム物理名: BGND66';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_17 IS '大日程予定日17 既存システム物理名: BGND67';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_18 IS '大日程予定日18 既存システム物理名: BGND68';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_19 IS '大日程予定日19 既存システム物理名: BGND69';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_20 IS '大日程予定日20 既存システム物理名: BGND6A';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_21 IS '大日程予定日21 既存システム物理名: BGND6B';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_22 IS '大日程予定日22 既存システム物理名: BGND6C';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_23 IS '大日程予定日23 既存システム物理名: BGND6D';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_24 IS '大日程予定日24 既存システム物理名: BGND6E';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_25 IS '大日程予定日25 既存システム物理名: BGND6F';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_26 IS '大日程予定日26 既存システム物理名: BGND6G';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_27 IS '大日程予定日27 既存システム物理名: BGND6H';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_28 IS '大日程予定日28 既存システム物理名: BGND6I';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_29 IS '大日程予定日29 既存システム物理名: BGND6J';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_PLANNED_DATE_30 IS '大日程予定日30 既存システム物理名: BGND6K';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_01 IS '大日程実績日01 既存システム物理名: BGND71';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_02 IS '大日程実績日02 既存システム物理名: BGND72';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_03 IS '大日程実績日03 既存システム物理名: BGND73';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_04 IS '大日程実績日04 既存システム物理名: BGND74';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_05 IS '大日程実績日05 既存システム物理名: BGND75';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_06 IS '大日程実績日06 既存システム物理名: BGND76';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_07 IS '大日程実績日07 既存システム物理名: BGND77';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_08 IS '大日程実績日08 既存システム物理名: BGND78';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_09 IS '大日程実績日09 既存システム物理名: BGND79';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_10 IS '大日程実績日10 既存システム物理名: BGND80';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_11 IS '大日程実績日11 既存システム物理名: BGND81';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_12 IS '大日程実績日12 既存システム物理名: BGND82';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_13 IS '大日程実績日13 既存システム物理名: BGND83';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_14 IS '大日程実績日14 既存システム物理名: BGND84';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_15 IS '大日程実績日15 既存システム物理名: BGND85';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_16 IS '大日程実績日16 既存システム物理名: BGND86';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_17 IS '大日程実績日17 既存システム物理名: BGND87';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_18 IS '大日程実績日18 既存システム物理名: BGND88';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_19 IS '大日程実績日19 既存システム物理名: BGND89';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_20 IS '大日程実績日20 既存システム物理名: BGND90';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_21 IS '大日程実績日21 既存システム物理名: BGND91';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_22 IS '大日程実績日22 既存システム物理名: BGND92';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_23 IS '大日程実績日23 既存システム物理名: BGND93';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_24 IS '大日程実績日24 既存システム物理名: BGND94';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_25 IS '大日程実績日25 既存システム物理名: BGND95';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_26 IS '大日程実績日26 既存システム物理名: BGND96';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_27 IS '大日程実績日27 既存システム物理名: BGND97';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_28 IS '大日程実績日28 既存システム物理名: BGND98';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_29 IS '大日程実績日29 既存システム物理名: BGND99';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MAJOR_SCHEDULE_ACTUAL_DATE_30 IS '大日程実績日30 既存システム物理名: BGNDA0';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.ORDER_CODE_ST IS '受注コード－ST 既存システム物理名: BGN0ST';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.ADDITIONAL_CODE_ST IS '追加コード－ST 既存システム物理名: BGN1ST';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.BUILD_MANAGER_CODE_ST IS '建営担当者コード－ST 既存システム物理名: BGN2ST';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.DESIGN_MANAGER_CODE_ST IS '設計担当者コード－ST 既存システム物理名: BGN3ST';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.DESIGN_SUPERVISOR_CODE_ST IS '設計管理職コード－ST 既存システム物理名: BGN4ST';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.APPLICATION_MANAGER_CODE_ST IS '申請担当者コード－ST 既存システム物理名: BGN5ST';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.ESTIMATION_MANAGER_CODE_ST IS '積算担当者コード－ST 既存システム物理名: BGN6ST';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.DEVELOPMENT_MANAGER_CODE_ST IS '開発担当者コード－ST 既存システム物理名: BGN7ST';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_SUPERVISOR_CODE_ST IS '工事責任者コード－ST 既存システム物理名: BGN8ST';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_STAFF_CODE_ST IS '工事担当者コード－ST 既存システム物理名: BGN9ST';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_MANAGER_CODE_ST IS '工事管理職コード－ST 既存システム物理名: BGNAST';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CHIEF_ARCHITECT_CODE_ST IS '管理建築士コード－ST 既存システム物理名: BGNBST';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.CONSTRUCTION_SUPERVISION_OFFICE_CODE IS '工事監理事業所 コード 既存システム物理名: BGNKKC';
COMMENT ON COLUMN PROPERTY_BASIC_FILE.MECHANISM_FINANCING_DIVISION IS '機構融資区分 既存システム物理名: BGNKYB';
