/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.NewOfficeMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.NewOfficeMasterPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 新営業所マスタ 既存システム物理名: -
 */
@Suppress("UNCHECKED_CAST")
open class NewOfficeMasterRecord private constructor() : UpdatableRecordImpl<NewOfficeMasterRecord>(NewOfficeMasterTable.NEW_OFFICE_MASTER) {

    open var id: Int
        set(value): Unit = set(0, value)
        get(): Int = get(0) as Int

    open var area: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var businessDepartment: String
        set(value): Unit = set(2, value)
        get(): String = get(2) as String

    open var businessOfficeCode: String
        set(value): Unit = set(3, value)
        get(): String = get(3) as String

    open var businessOfficeName: String
        set(value): Unit = set(4, value)
        get(): String = get(4) as String

    open var prefectureCode: String
        set(value): Unit = set(5, value)
        get(): String = get(5) as String

    open var prefectureName: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var cityCode: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var cityName: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    open var creationDate: Int?
        set(value): Unit = set(9, value)
        get(): Int? = get(9) as Int?

    open var creationTime: Int?
        set(value): Unit = set(10, value)
        get(): Int? = get(10) as Int?

    open var creator: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var updateDate: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    open var updateTime: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var updater: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var deleteFlag: String
        set(value): Unit = set(15, value)
        get(): String = get(15) as String

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<Int?> = super.key() as Record1<Int?>

    /**
     * Create a detached, initialised NewOfficeMasterRecord
     */
    constructor(id: Int, area: String, businessDepartment: String, businessOfficeCode: String, businessOfficeName: String, prefectureCode: String, prefectureName: String, cityCode: String, cityName: String, creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, deleteFlag: String): this() {
        this.id = id
        this.area = area
        this.businessDepartment = businessDepartment
        this.businessOfficeCode = businessOfficeCode
        this.businessOfficeName = businessOfficeName
        this.prefectureCode = prefectureCode
        this.prefectureName = prefectureName
        this.cityCode = cityCode
        this.cityName = cityName
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.deleteFlag = deleteFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised NewOfficeMasterRecord
     */
    constructor(value: NewOfficeMasterPojo?): this() {
        if (value != null) {
            this.id = value.id
            this.area = value.area
            this.businessDepartment = value.businessDepartment
            this.businessOfficeCode = value.businessOfficeCode
            this.businessOfficeName = value.businessOfficeName
            this.prefectureCode = value.prefectureCode
            this.prefectureName = value.prefectureName
            this.cityCode = value.cityCode
            this.cityName = value.cityName
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            resetChangedOnNotNull()
        }
    }
}
