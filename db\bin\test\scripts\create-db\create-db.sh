#!/bin/bash
# 引数 $1:DB名

# shellcheck disable=SC2034
PGPASSWORD=${POSTGRES_PASSWORD} # psql接続用
DBNAME=$1

echo "/**************** ${DBNAME} DB作成 ****************/"
echo
psql -f createDb.sql -U "${POSTGRES_USER}" -h "${DBHOST}" -p "${DBPORT}" -v DB_NAME="${DBNAME}"

echo "/**************** ${DBNAME} タイムゾーン変更 ****************/"
echo
psql -f changeTimezone.sql -U "${POSTGRES_USER}" -h "${DBHOST}" -p "${DBPORT}" -v DB_NAME="${DBNAME}"
