-- TABLE: BUILDING_IMAGE(建物画像)

CREATE TABLE BUILDING_IMAGE(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_USER                                  varchar(10)                   
,    PROPERTY_BUILDING_CD                         varchar(9)        NOT NULL    
,    IMAGE_REGISTRATION_COUNT                     numeric(2,0)                  
,    IMAGE_REGISTRATION_DATE                      numeric(8,0)                  
,    IMAGE_FILE_NAME_1                            varchar(50)                   
,    IMAGE_REGISTRATION_DATE_1                    numeric(8,0)                  
,    IMAGE_FILE_NAME_2                            varchar(50)                   
,    IMAGE_REGISTRATION_DATE_2                    numeric(8,0)                  
,    IMAGE_FILE_NAME_3                            varchar(50)                   
,    IMAGE_REGISTRATION_DATE_3                    numeric(8,0)                  
,    SURROUNDING_IMAGE_FILE_NAME_1                varchar(50)                   
,    SURROUNDING_IMAGE_REGISTRATION_DATE_1        numeric(8,0)                  
,    IMAGE_TYPE_1                                 varchar(2)                    
,    FACILITY_NAME_1                              varchar(62)                   
,    DISTANCE_1                                   varchar(4)                    
,    SURROUNDING_IMAGE_FILE_NAME_2                varchar(50)                   
,    SURROUNDING_IMAGE_REGISTRATION_DATE_2        numeric(8,0)                  
,    IMAGE_TYPE_2                                 varchar(2)                    
,    FACILITY_NAME_2                              varchar(62)                   
,    DISTANCE_2                                   varchar(4)                    
,    SURROUNDING_IMAGE_FILE_NAME_3                varchar(50)                   
,    SURROUNDING_IMAGE_REGISTRATION_DATE_3        numeric(8,0)                  
,    IMAGE_TYPE_3                                 varchar(2)                    
,    FACILITY_NAME_3                              varchar(62)                   
,    DISTANCE_3                                   varchar(4)                    
,    SURROUNDING_IMAGE_FILE_NAME_4                varchar(50)                   
,    SURROUNDING_IMAGE_REGISTRATION_DATE_4        numeric(8,0)                  
,    IMAGE_TYPE_4                                 varchar(2)                    
,    FACILITY_NAME_4                              varchar(62)                   
,    DISTANCE_4                                   varchar(4)                    
,    SURROUNDING_IMAGE_FILE_NAME_5                varchar(50)                   
,    SURROUNDING_IMAGE_REGISTRATION_DATE_5        numeric(8,0)                  
,    IMAGE_TYPE_5                                 varchar(2)                    
,    FACILITY_NAME_5                              varchar(62)                   
,    DISTANCE_5                                   varchar(4)                    
,    SURROUNDING_IMAGE_FILE_NAME_6                varchar(50)                   
,    SURROUNDING_IMAGE_REGISTRATION_DATE_6        numeric(8,0)                  
,    IMAGE_TYPE_6                                 varchar(2)                    
,    FACILITY_NAME_6                              varchar(62)                   
,    DISTANCE_6                                   varchar(4)                    
,    IMAGE_REGISTRATION_TIME_1                    numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_2                    numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_3                    numeric(6,0)                  
,    SURROUNDING_IMAGE_REGISTRATION_TIME_1        numeric(6,0)                  
,    SURROUNDING_IMAGE_REGISTRATION_TIME_2        numeric(6,0)                  
,    SURROUNDING_IMAGE_REGISTRATION_TIME_3        numeric(6,0)                  
,    SURROUNDING_IMAGE_REGISTRATION_TIME_4        numeric(6,0)                  
,    SURROUNDING_IMAGE_REGISTRATION_TIME_5        numeric(6,0)                  
,    SURROUNDING_IMAGE_REGISTRATION_TIME_6        numeric(6,0)                  
,    NEXT_UPDATE_DATE_2                           numeric(8,0)                  
,    NEXT_UPDATE_DATE_3                           numeric(8,0)                  
,    CONSTRAINT PK_BUILDING_IMAGE PRIMARY KEY (PROPERTY_BUILDING_CD)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BUILDING_IMAGE IS '建物画像 既存システム物理名: ERATGP';
COMMENT ON COLUMN BUILDING_IMAGE.CREATION_DATE IS '作成年月日 既存システム物理名: ERA01D 社員番号を格納';
COMMENT ON COLUMN BUILDING_IMAGE.CREATION_TIME IS '作成時刻 既存システム物理名: ERA02H';
COMMENT ON COLUMN BUILDING_IMAGE.UPDATE_DATE IS '更新年月日 既存システム物理名: ERA03D';
COMMENT ON COLUMN BUILDING_IMAGE.UPDATE_TIME IS '更新時刻 既存システム物理名: ERA04H';
COMMENT ON COLUMN BUILDING_IMAGE.UPDATE_USER IS '更新ユーザ 既存システム物理名: ERA05C';
COMMENT ON COLUMN BUILDING_IMAGE.PROPERTY_BUILDING_CD IS '物件建物CD 既存システム物理名: ERA06C';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_REGISTRATION_COUNT IS '画像登録件数 既存システム物理名: ERAGTQ';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_REGISTRATION_DATE IS '画像登録日 既存システム物理名: ERAGTD';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_FILE_NAME_1 IS '画像ファイル名1 既存システム物理名: ERAF1M';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_REGISTRATION_DATE_1 IS '画像登録日1 既存システム物理名: ERAT1D';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_FILE_NAME_2 IS '画像ファイル名2 既存システム物理名: ERAF2M';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_REGISTRATION_DATE_2 IS '画像登録日2 既存システム物理名: ERAT2D';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_FILE_NAME_3 IS '画像ファイル名3 既存システム物理名: ERAF3M';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_REGISTRATION_DATE_3 IS '画像登録日3 既存システム物理名: ERAT3D';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_FILE_NAME_1 IS '周辺画像ファイル名1 既存システム物理名: ERSF1M';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_REGISTRATION_DATE_1 IS '周辺画像登録日1 既存システム物理名: ERST1D';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_TYPE_1 IS '画像種別1 既存システム物理名: ERSS1B';
COMMENT ON COLUMN BUILDING_IMAGE.FACILITY_NAME_1 IS '施設名1 既存システム物理名: ERSN1M';
COMMENT ON COLUMN BUILDING_IMAGE.DISTANCE_1 IS '距離1 既存システム物理名: ERSL1H';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_FILE_NAME_2 IS '周辺画像ファイル名2 既存システム物理名: ERSF2M';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_REGISTRATION_DATE_2 IS '周辺画像登録日2 既存システム物理名: ERST2D';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_TYPE_2 IS '画像種別2 既存システム物理名: ERSS2B';
COMMENT ON COLUMN BUILDING_IMAGE.FACILITY_NAME_2 IS '施設名2 既存システム物理名: ERSN2M';
COMMENT ON COLUMN BUILDING_IMAGE.DISTANCE_2 IS '距離2 既存システム物理名: ERSL2H';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_FILE_NAME_3 IS '周辺画像ファイル名3 既存システム物理名: ERSF3M';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_REGISTRATION_DATE_3 IS '周辺画像登録日3 既存システム物理名: ERST3D';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_TYPE_3 IS '画像種別3 既存システム物理名: ERSS3B';
COMMENT ON COLUMN BUILDING_IMAGE.FACILITY_NAME_3 IS '施設名3 既存システム物理名: ERSN3M';
COMMENT ON COLUMN BUILDING_IMAGE.DISTANCE_3 IS '距離3 既存システム物理名: ERSL3H';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_FILE_NAME_4 IS '周辺画像ファイル名4 既存システム物理名: ERSF4M';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_REGISTRATION_DATE_4 IS '周辺画像登録日4 既存システム物理名: ERST4D';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_TYPE_4 IS '画像種別4 既存システム物理名: ERSS4B';
COMMENT ON COLUMN BUILDING_IMAGE.FACILITY_NAME_4 IS '施設名4 既存システム物理名: ERSN4M';
COMMENT ON COLUMN BUILDING_IMAGE.DISTANCE_4 IS '距離4 既存システム物理名: ERSL4H';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_FILE_NAME_5 IS '周辺画像ファイル名5 既存システム物理名: ERSF5M';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_REGISTRATION_DATE_5 IS '周辺画像登録日5 既存システム物理名: ERST5D';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_TYPE_5 IS '画像種別5 既存システム物理名: ERSS5B';
COMMENT ON COLUMN BUILDING_IMAGE.FACILITY_NAME_5 IS '施設名5 既存システム物理名: ERSN5M';
COMMENT ON COLUMN BUILDING_IMAGE.DISTANCE_5 IS '距離5 既存システム物理名: ERSL5H';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_FILE_NAME_6 IS '周辺画像ファイル名6 既存システム物理名: ERSF6M';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_REGISTRATION_DATE_6 IS '周辺画像登録日6 既存システム物理名: ERST6D';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_TYPE_6 IS '画像種別6 既存システム物理名: ERSS6B';
COMMENT ON COLUMN BUILDING_IMAGE.FACILITY_NAME_6 IS '施設名6 既存システム物理名: ERSN6M';
COMMENT ON COLUMN BUILDING_IMAGE.DISTANCE_6 IS '距離6 既存システム物理名: ERSL6H';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_REGISTRATION_TIME_1 IS '画像登録時間1 既存システム物理名: ERAT1H';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_REGISTRATION_TIME_2 IS '画像登録時間2 既存システム物理名: ERAT2H';
COMMENT ON COLUMN BUILDING_IMAGE.IMAGE_REGISTRATION_TIME_3 IS '画像登録時間3 既存システム物理名: ERAT3H';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_REGISTRATION_TIME_1 IS '周辺画像登録時間1 既存システム物理名: ERST1H';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_REGISTRATION_TIME_2 IS '周辺画像登録時間2 既存システム物理名: ERST2H';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_REGISTRATION_TIME_3 IS '周辺画像登録時間3 既存システム物理名: ERST3H';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_REGISTRATION_TIME_4 IS '周辺画像登録時間4 既存システム物理名: ERST4H';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_REGISTRATION_TIME_5 IS '周辺画像登録時間5 既存システム物理名: ERST5H';
COMMENT ON COLUMN BUILDING_IMAGE.SURROUNDING_IMAGE_REGISTRATION_TIME_6 IS '周辺画像登録時間6 既存システム物理名: ERST6H';
COMMENT ON COLUMN BUILDING_IMAGE.NEXT_UPDATE_DATE_2 IS '次回更新日2 既存システム物理名: ERAN2D';
COMMENT ON COLUMN BUILDING_IMAGE.NEXT_UPDATE_DATE_3 IS '次回更新日3 既存システム物理名: ERAN3D';
