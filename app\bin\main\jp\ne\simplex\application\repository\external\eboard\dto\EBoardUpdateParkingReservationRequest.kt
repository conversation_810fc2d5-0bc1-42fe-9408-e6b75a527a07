package jp.ne.simplex.application.repository.external.eboard.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.external.eboard.config.EboardApiPath
import jp.ne.simplex.application.repository.external.eboard.config.EboardRequest
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.StringExtension.Companion.isOnlyFullWidth
import jp.ne.simplex.shared.StringExtension.Companion.toFullWidth

class EBoardUpdateParkingReservationRequest private constructor(
    @field:JsonProperty("tatemonoCd")
    val tatemonoCd: String,
    @field:JsonProperty("parkingCd")
    val parkingCd: String? = null,
    @field:JsonProperty("status")
    val status: String,
    @field:JsonProperty("uketukeDate")
    val uketukeDate: String,
    @field:JsonProperty("reserveName")
    val reserveName: String? = null,
    @field:JsonProperty("tel")
    val tel: String? = null,
    @field:JsonProperty("tantoName")
    val tantoName: String? = null,
    @field:JsonProperty("bikou")
    val bikou: String? = null,
    @field:JsonProperty("id")
    val id: String,
) : EboardRequest {
    override fun getApiPath(): EboardApiPath {
        return EboardApiPath.RESERVE_PARKING
    }

    companion object {

        fun of(
            action: ParkingReservationAction,
            info: ParkingReservationInfo
        ): EBoardUpdateParkingReservationRequest {

            val status = when (action) {
                is RegisterParkingReservation -> if (action.isFromWelcomePark()) "4" else "0"
                is UpdateParkingReservation -> "1"
                is CancelParkingReservation -> "3"
                is CancelApplicationParkingReservation -> "3"
                is FinishParkingReservation -> "2"
            }

            // takeしている箇所はいい物件ボードのIFの仕様上、文字数オーバーになる可能性があるためトリムして連携する。
            return EBoardUpdateParkingReservationRequest(
                tatemonoCd = info.buildingCode.value,
                parkingCd = info.parkingLotCode?.value,
                status = status,
                uketukeDate = info.receptionDate.yyyyMMdd(),
                reserveName = info.reserverName?.toFullWidth()
                    ?.takeIf { it.isOnlyFullWidth() }?.take(12),
                tel = info.reserverTel?.value,
                tantoName = info.receptionStaff?.toFullWidth()
                    ?.takeIf { it.isOnlyFullWidth() }?.take(12),
                bikou = info.remarks?.value?.toFullWidth()
                    ?.takeIf { it.isOnlyFullWidth() }?.take(50),
                id = info.eBoardParkingReservationId.value
            )
        }
    }
}
