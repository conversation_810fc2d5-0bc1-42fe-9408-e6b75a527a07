package jp.ne.simplex.application.controller.client.shared

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.log.MaskTarget
import jp.ne.simplex.log.MaskValue

@MaskTarget
/** クライアントと Employee をやり取りするための共通DTO */
data class ClientEmployeeDto(
    @JsonProperty("employeeCode")
    @field:Schema(description = "従業員コード", example = "000011")
    val employeeCode: String,

    @MaskValue
    @JsonProperty("employeeName")
    @field:Schema(description = "従業員名（漢字）", example = "山田太郎")
    val employeeName: String,

    @JsonProperty("affiliationCode")
    @field:Schema(description = "所属コード", example = "521000")
    val affiliationCode: String?,
) {

    companion object {

        fun of(employee: Employee): ClientEmployeeDto {
            return ClientEmployeeDto(
                employeeCode = employee.code.value,
                employeeName = employee.name.kanji,
                affiliationCode = employee.affiliationCode,
            )
        }
    }

    @JsonIgnore
    fun getEmployee(): Employee {
        return Employee(
            code = Employee.Code(employeeCode),
            name = Employee.Name.of(employeeName),
            affiliationCode = affiliationCode,
        )
    }
}
