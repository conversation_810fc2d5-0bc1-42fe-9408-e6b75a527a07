package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.db.*
import jp.ne.simplex.application.repository.mail.MailRepository
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmmWithColon
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMddWithSlash
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 駐車場予約メール送信クラス
 * */
@Service
class ParkingReservationMailService(
    @Value("\${mail.parking.reservation.recept.address}")
    private val receptAddress: String,
    @Value("\${mail.parking.reservation.from.name}")
    private val inputName: String,
    @Value("\${mail.parking.reservation.send}")
    private val isSendMail: <PERSON><PERSON><PERSON>,
    val emailAddressRepository: EmailAddressRepositoryInterface,
    val officeBranchMappingRepository: OfficeBranchMappingRepositoryInterface,
    val buildingRepository: BuildingMasterRepositoryInterface,
    val branchRepository: BranchRepositoryInterface,
    val parkingRepository: ParkingRepositoryInterface,
    val mailRepository: MailRepository
) {

    companion object {
        private val log = LoggerFactory.getLogger(ParkingReservationMailService::class.java)
        private const val SUBJECT = "駐車場予約情報"
        private const val SUBJECT_WELCOME_PARK = "ウェルカムパーク駐車場予約情報"
        private const val BATCH_SIZE = 50 // AWSのsnsのクオータである
    }

    fun notifyRegister(authInfo: AuthInfo, registerAction: RegisterParkingReservation) {
        // 仮申込は送信しない
        if (registerAction.parkingReservationStatus == ParkingReservation.Status.TENTATIVE) {
            return
        }
        return this.send(
            authInfo,
            registerAction.parkingLotId.buildingCode,
            registerAction.parkingLotId.parkingLotCode,
            if (registerAction.isFromWelcomePark()) MailProperty.MailTemplateType.PARKING_YOYAKU_INFO_WELCOME_PARK else MailProperty.MailTemplateType.PARKING_YOYAKU_INFO,
            registerAction.remarks,
            registerAction.reserverName,
            registerAction.reserverTel,
            registerAction.reserveEndDatetime,
            if (registerAction.isFromWelcomePark()) SUBJECT_WELCOME_PARK else SUBJECT,
        )
    }

    fun notifyUpdate(
        authInfo: AuthInfo,
        updateTarget: ParkingReservationInfo,
        newStatus: ParkingReservation.Status,
        remarks: ParkingReservation.Remarks?,
    ) {
        if (updateTarget.status == newStatus) {
            return
        }


        return when (newStatus) {
            ParkingReservation.Status.TENTATIVE -> null
            ParkingReservation.Status.RESERVATION -> MailProperty.MailTemplateType.PARKING_YOYAKU_UKETUKE_INFO
            ParkingReservation.Status.FINISHED -> MailProperty.MailTemplateType.PARKING_YOYAKU_KEIYAKU_INFO
            ParkingReservation.Status.CANCEL -> MailProperty.MailTemplateType.PARKING_YOYAKU_CANCEL_INFO
        }
            ?.let {
                this.send(
                    authInfo,
                    updateTarget.buildingCode,
                    updateTarget.parkingLotCode,
                    it,
                    remarks
                )
            }
            ?: Unit
    }

    /**
     * メールを送信する。(駐車場予約メール用）
     */
    private fun send(
        authInfo: AuthInfo,
        buildingCode: Building.Code,
        parkingLotCode: ParkingLot.Code?,
        mailTemplateType: MailProperty.MailTemplateType,
        remarks: ParkingReservation.Remarks?,
        reserverName: String? = null,
        reserverTel: TelephoneNumber? = null,
        reserveEndDatetime: LocalDateTime? = null,
        subject: String = SUBJECT
    ) {
        if (!isSendMail) {
            return
        }
        val transmitList: MutableList<EmailAddress> = mutableListOf() // 送信先リスト
        val localDisplayNumber =
            parkingRepository.getLocalDisplayNumber(buildingCode, parkingLotCode)

        // メール本文パラメータ
        val paramMap: HashMap<String, String> = hashMapOf(
            "TateCD" to buildingCode.value,
            "ParkingCD" to (parkingLotCode?.value ?: "-"),
            "LocalDisplayNumber" to (localDisplayNumber ?: "-"),
            "Remarks" to (remarks?.value ?: "なし"),
            "ReserverName" to (reserverName?.let { "$it　様" } ?: "-"),
            "ReserverTel" to (reserverTel?.value ?: "-"),
            "ReserveEndDatetime" to (reserveEndDatetime?.let { "${it.yyyyMMddWithSlash()}　${it.HHmmWithColon()}まで" }
                ?: "-")
        )

        val building = buildingRepository.findActiveBy(buildingCode) ?: buildingRepository.findBy(buildingCode)

        paramMap["TateNM"] = building?.name?.value ?: "-"
        // 支店名の取得とパラメータマップの設定
        val leasingStoreCode = building?.leasingStoreCode
        paramMap["LeaseShitenName"] = when {
            leasingStoreCode != null -> branchRepository.getKtBranch(leasingStoreCode)?.name?.value ?: "-"
            else -> "-"
        }
        val reviewBranchCode = building?.reviewBranchCode
        paramMap["ShinsaShitenName"] = when {
            reviewBranchCode != null -> branchRepository.getKtBranch(reviewBranchCode)?.name?.value ?: "-"
            else -> "-"
        }

        // 建物の紐づく支店全ユーザーのメールアドレスを取得
        val allBranchUserAddressList = emailAddressRepository.getAddressListByBranchCode(
            listOfNotNull(leasingStoreCode, reviewBranchCode)
        )
        if (allBranchUserAddressList.isEmpty()) {
            log.warn("No branch user to send a mail.")
            return
        }

        if (authInfo is AuthInfo.Jwt) {
            val branchCode = building?.businessOfficeCode?.let {
                officeBranchMappingRepository.get(it)
            }
            if (branchCode != null) {
                // テナント営業のアドレス
                val tenantAddressList = emailAddressRepository.getTenantAddressList(branchCode)
                // 大東建物管理のアドレス
                val daitateAddressList = emailAddressRepository.getDaitateAddressList(branchCode)
                tenantAddressList.forEach { transmitList.add(it) }
                daitateAddressList.forEach { transmitList.add(it) }
            }
        }
        // 建物の紐づく支店全ユーザーのメールアドレス
        allBranchUserAddressList.forEach { transmitList.add(it) }
        try {
            transmitList.chunked(BATCH_SIZE).forEach { batch ->
                val mailProperty = MailProperty(
                    mailTemplateType = mailTemplateType,
                    fromAddress = EmailAddress.of(receptAddress),
                    fromName = inputName,
                    subject = subject,
                    url = "",
                    toList = batch.distinct(),
                    messageParam = paramMap
                )
                mailRepository.sendMailUP(mailProperty)
            }
        } catch (ex: Exception) {
            throw Exception("メールの送信に失敗しました。", ex)
        }
    }
}
