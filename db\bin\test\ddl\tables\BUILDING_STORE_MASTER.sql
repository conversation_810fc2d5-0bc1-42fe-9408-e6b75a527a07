-- TABLE: BUILDING_STORE_MASTER(建物店舗マスタ)

CREATE TABLE BUILDING_STORE_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    BUILDING_CODE                                varchar(9)                    
,    LEASING_STORE_CODE                           varchar(6)                    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BUILDING_STORE_MASTER IS '建物店舗マスタ 既存システム物理名: ECM10P';
COMMENT ON COLUMN BUILDING_STORE_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: EC101D XXRFFPのABC';
COMMENT ON COLUMN BUILDING_STORE_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: EC102H';
COMMENT ON COLUMN BUILDING_STORE_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: EC103D';
COMMENT ON COLUMN BUILDING_STORE_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: EC104H';
COMMENT ON COLUMN BUILDING_STORE_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EC105N';
COMMENT ON COLUMN BUILDING_STORE_MASTER.UPDATER IS '更新者 既存システム物理名: EC106C';
COMMENT ON COLUMN BUILDING_STORE_MASTER.BUILDING_CODE IS '建物コード 既存システム物理名: EC1ABC';
COMMENT ON COLUMN BUILDING_STORE_MASTER.LEASING_STORE_CODE IS 'リーシング店舗コード 既存システム物理名: EC107C';
