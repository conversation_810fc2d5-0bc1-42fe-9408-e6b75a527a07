/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ParkingMemoTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingMemoPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 駐車場メモ 既存システム物理名: -
 */
@Suppress("UNCHECKED_CAST")
open class ParkingMemoRecord private constructor() : UpdatableRecordImpl<ParkingMemoRecord>(ParkingMemoTable.PARKING_MEMO) {

    open var orderCode: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var seqNumber: Short
        set(value): Unit = set(1, value)
        get(): Short = get(1) as Short

    open var content: String
        set(value): Unit = set(2, value)
        get(): String = get(2) as String

    open var bookmarkFlag: String
        set(value): Unit = set(3, value)
        get(): String = get(3) as String

    open var creationDate: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var creationTime: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var creator: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var updateDate: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var updateTime: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var updater: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var deleteFlag: String
        set(value): Unit = set(10, value)
        get(): String = get(10) as String

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, Short?> = super.key() as Record2<String?, Short?>

    /**
     * Create a detached, initialised ParkingMemoRecord
     */
    constructor(orderCode: String, seqNumber: Short, content: String, bookmarkFlag: String, creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, deleteFlag: String): this() {
        this.orderCode = orderCode
        this.seqNumber = seqNumber
        this.content = content
        this.bookmarkFlag = bookmarkFlag
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.deleteFlag = deleteFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ParkingMemoRecord
     */
    constructor(value: ParkingMemoPojo?): this() {
        if (value != null) {
            this.orderCode = value.orderCode
            this.seqNumber = value.seqNumber
            this.content = value.content
            this.bookmarkFlag = value.bookmarkFlag
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            resetChangedOnNotNull()
        }
    }
}
