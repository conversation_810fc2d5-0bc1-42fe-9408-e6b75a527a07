package jp.ne.simplex.application.controller.client.exclusive.dto

import jp.ne.simplex.application.model.Room
import jp.ne.simplex.stub.stubExclusivePropertiesSearchPojo
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class ClientExclusivePropertiesSearchResponseTest {
    @Nested
    @DisplayName("削除日・削除者の変換検証")
    inner class Scenario1 {
        @Test
        @DisplayName("早期終了されてない場合は削除日・削除者がnullに変換確認")
        fun testToResponse1() {
            val pojo = stubExclusivePropertiesSearchPojo(
                id = 1, updateDate = 20251231, updater = "削除 たろう",
                earlyClosureFlag = "0", propertyName = "物件名", roomNumber = "0101"
            )
            val result = ClientExclusivePropertiesSearchResponse.of(
                listOf(pojo.toExclusivePropertyInfo()!!), pojo.totalCount
            )
            assertEquals(pojo.id, result.list.first().id.toLong())
            assertEquals(null, result.list.first().deleter)
            assertEquals(null, result.list.first().deleteDate)
            // 物件名の部屋番号の先頭0が除去されていることを確認
            assertEquals("${pojo.propertyName} ${Room.Number.of(pojo.roomNumber!!).getFormattedValue()}", result.list.first().propertyName)
        }

        @Test
        @DisplayName("早期終了されている場合は削除日・削除者がnull以外に変換確認")
        fun testToResponse2() {
            val pojo = stubExclusivePropertiesSearchPojo(
                id = 1, updateDate = 20251231, updater = "削除 たろう",
                earlyClosureFlag = "1", propertyName = null, roomNumber = "0101"
            )
            val result = ClientExclusivePropertiesSearchResponse.of(
                listOf(pojo.toExclusivePropertyInfo()!!), pojo.totalCount
            )
            assertEquals(pojo.id, result.list.first().id.toLong())
            assertEquals(pojo.updater, result.list.first().deleter)
            assertEquals(pojo.updateDate.toString(), result.list.first().deleteDate)
            // 建物名がない場合は物件名もnullに変換されることを確認
            assertEquals(null, result.list.first().propertyName)
        }
    }
}
