/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.FixedTermRentalInfoMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.FixedTermRentalInfoMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 定期借家情報マスタ 既存システム物理名: ECNE5P
 */
@Suppress("UNCHECKED_CAST")
open class FixedTermRentalInfoMasterRecord private constructor() : TableRecordImpl<FixedTermRentalInfoMasterRecord>(FixedTermRentalInfoMasterTable.FIXED_TERM_RENTAL_INFO_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var creationProgram: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var updateDate: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updateTime: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var updater: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var updateProgram: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(8, value)
        get(): Byte? = get(8) as Byte?

    open var buildingCode: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var roomCode: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var endTerm: Int?
        set(value): Unit = set(11, value)
        get(): Int? = get(11) as Int?

    open var period: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var roomFixedTermFlag: Byte?
        set(value): Unit = set(13, value)
        get(): Byte? = get(13) as Byte?

    open var explanationFixedTermFlag: Byte?
        set(value): Unit = set(14, value)
        get(): Byte? = get(14) as Byte?

    /**
     * Create a detached, initialised FixedTermRentalInfoMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, creationProgram: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, updateProgram: String? = null, logicalDeleteSign: Byte? = null, buildingCode: String? = null, roomCode: String? = null, endTerm: Int? = null, period: String? = null, roomFixedTermFlag: Byte? = null, explanationFixedTermFlag: Byte? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.creationProgram = creationProgram
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.updateProgram = updateProgram
        this.logicalDeleteSign = logicalDeleteSign
        this.buildingCode = buildingCode
        this.roomCode = roomCode
        this.endTerm = endTerm
        this.period = period
        this.roomFixedTermFlag = roomFixedTermFlag
        this.explanationFixedTermFlag = explanationFixedTermFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised FixedTermRentalInfoMasterRecord
     */
    constructor(value: FixedTermRentalInfoMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.creationProgram = value.creationProgram
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.updateProgram = value.updateProgram
            this.logicalDeleteSign = value.logicalDeleteSign
            this.buildingCode = value.buildingCode
            this.roomCode = value.roomCode
            this.endTerm = value.endTerm
            this.period = value.period
            this.roomFixedTermFlag = value.roomFixedTermFlag
            this.explanationFixedTermFlag = value.explanationFixedTermFlag
            resetChangedOnNotNull()
        }
    }
}
