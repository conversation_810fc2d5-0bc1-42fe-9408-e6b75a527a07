-- TABLE: FIXED_ITEM_FILE(固定項目ファイル)

CREATE TABLE FIXED_ITEM_FILE(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    CREATION_PROGRAM                             varchar(10)                   
,    CREATION_TERMINAL_ID                         varchar(8)                    
,    CREATION_RESPONSIBLE_CD                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM                               varchar(10)                   
,    UPDATE_TERMINAL_ID                           varchar(8)                    
,    UPDATE_RESPONSIBLE_CD                        varchar(6)                    
,    LOGICAL_DELETE_SIGN                          numeric(1)                    
,    ITEM_ID                                      varchar(3)                    
,    KEY1                                         varchar(20)                   
,    KEY2                                         numeric(15)                   
,    DATA_KANJI1                                  varchar(42)                   
,    DATA_KANJI2                                  varchar(42)                   
,    DATA_STRING1                                 varchar(20)                   
,    DATA_STRING2                                 varchar(20)                   
,    DATA_NUMBER                                  numeric(15)                   
,    DATA_DECIMAL4V3                              numeric(7,3)                  
,    ITEM_NAME                                    varchar(42)                   
) TABLESPACE :TS_TBL;

COMMENT ON TABLE FIXED_ITEM_FILE IS '固定項目ファイル 既存システム物理名: HVX10P';
COMMENT ON COLUMN FIXED_ITEM_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: HVX01D @290';
COMMENT ON COLUMN FIXED_ITEM_FILE.CREATION_TIME IS '作成時間 既存システム物理名: HVX02H @290';
COMMENT ON COLUMN FIXED_ITEM_FILE.CREATION_PROGRAM IS '作成プログラム 既存システム物理名: HVX03M @290';
COMMENT ON COLUMN FIXED_ITEM_FILE.CREATION_TERMINAL_ID IS '作成端末ID 既存システム物理名: HVX04M';
COMMENT ON COLUMN FIXED_ITEM_FILE.CREATION_RESPONSIBLE_CD IS '作成担当者CD 既存システム物理名: HVX05M';
COMMENT ON COLUMN FIXED_ITEM_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: HVX06D';
COMMENT ON COLUMN FIXED_ITEM_FILE.UPDATE_TIME IS '更新時間 既存システム物理名: HVX07H';
COMMENT ON COLUMN FIXED_ITEM_FILE.UPDATE_PROGRAM IS '更新プログラム 既存システム物理名: HVX08M';
COMMENT ON COLUMN FIXED_ITEM_FILE.UPDATE_TERMINAL_ID IS '更新端末ID 既存システム物理名: HVX09M';
COMMENT ON COLUMN FIXED_ITEM_FILE.UPDATE_RESPONSIBLE_CD IS '更新担当者CD 既存システム物理名: HVX10M';
COMMENT ON COLUMN FIXED_ITEM_FILE.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: HVX11S';
COMMENT ON COLUMN FIXED_ITEM_FILE.ITEM_ID IS '項目ID 既存システム物理名: HVX12B';
COMMENT ON COLUMN FIXED_ITEM_FILE.KEY1 IS 'キー1 既存システム物理名: HVX13C';
COMMENT ON COLUMN FIXED_ITEM_FILE.KEY2 IS 'キー2 既存システム物理名: HVX14C';
COMMENT ON COLUMN FIXED_ITEM_FILE.DATA_KANJI1 IS 'データ漢字1 既存システム物理名: HVX15M';
COMMENT ON COLUMN FIXED_ITEM_FILE.DATA_KANJI2 IS 'データ漢字2 既存システム物理名: HVX16M';
COMMENT ON COLUMN FIXED_ITEM_FILE.DATA_STRING1 IS 'データ文字1 既存システム物理名: HVX17M';
COMMENT ON COLUMN FIXED_ITEM_FILE.DATA_STRING2 IS 'データ文字2 既存システム物理名: HVX18M';
COMMENT ON COLUMN FIXED_ITEM_FILE.DATA_NUMBER IS 'データ数値 既存システム物理名: HVX19X';
COMMENT ON COLUMN FIXED_ITEM_FILE.DATA_DECIMAL4V3 IS 'データ少数4V3 既存システム物理名: HVX20X';
COMMENT ON COLUMN FIXED_ITEM_FILE.ITEM_NAME IS '項目名称 既存システム物理名: HVX21M';
