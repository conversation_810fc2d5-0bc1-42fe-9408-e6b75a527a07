/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.PropertyBasicFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.PropertyBasicFilePojo

import org.jooq.impl.TableRecordImpl


/**
 * 物件基本ファイル 既存システム物理名: BGNF1P
 */
@Suppress("UNCHECKED_CAST")
open class PropertyBasicFileRecord private constructor() : TableRecordImpl<PropertyBasicFileRecord>(PropertyBasicFileTable.PROPERTY_BASIC_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgram: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var deleteFlag: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var orderCode: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var additionalCode: Short?
        set(value): Unit = set(8, value)
        get(): Short? = get(8) as Short?

    open var leaseTaxCellDivision: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var constructionName: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var contractTypeOccurrence: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var buildingCount: Short?
        set(value): Unit = set(12, value)
        get(): Short? = get(12) as Short?

    open var clientOtherConstructionDivision: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var clientOtherCompletionDate: Int?
        set(value): Unit = set(14, value)
        get(): Int? = get(14) as Int?

    open var municipalityCode_1: Byte?
        set(value): Unit = set(15, value)
        get(): Byte? = get(15) as Byte?

    open var municipalityCode_2: Short?
        set(value): Unit = set(16, value)
        get(): Short? = get(16) as Short?

    open var constructionLocation_1: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var constructionLocation_2: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var constructionLocation_3: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var orderCompanyCode: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var orderOfficeCode: Int?
        set(value): Unit = set(21, value)
        get(): Int? = get(21) as Int?

    open var contractCompanyCode: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var contractOfficeCode: Int?
        set(value): Unit = set(23, value)
        get(): Int? = get(23) as Int?

    open var buildManagerCode: Int?
        set(value): Unit = set(24, value)
        get(): Int? = get(24) as Int?

    open var designCompanyCode: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var designOfficeCode: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var designManagerCode: Int?
        set(value): Unit = set(27, value)
        get(): Int? = get(27) as Int?

    open var designSupervisorCode: Int?
        set(value): Unit = set(28, value)
        get(): Int? = get(28) as Int?

    open var applicationCompanyCode: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var applicationOfficeCode: Int?
        set(value): Unit = set(30, value)
        get(): Int? = get(30) as Int?

    open var applicationManagerCode: Int?
        set(value): Unit = set(31, value)
        get(): Int? = get(31) as Int?

    open var estimationCompanyCode: String?
        set(value): Unit = set(32, value)
        get(): String? = get(32) as String?

    open var estimationOfficeCode: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var estimationManagerCode: Int?
        set(value): Unit = set(34, value)
        get(): Int? = get(34) as Int?

    open var developmentCompanyCode: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var developmentOfficeCode: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var developmentManagerCode: Int?
        set(value): Unit = set(37, value)
        get(): Int? = get(37) as Int?

    open var constructionCompanyCode: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var constructionOfficeCode: Int?
        set(value): Unit = set(39, value)
        get(): Int? = get(39) as Int?

    open var constructionSupervisorCode: Int?
        set(value): Unit = set(40, value)
        get(): Int? = get(40) as Int?

    open var constructionStaffCode: Int?
        set(value): Unit = set(41, value)
        get(): Int? = get(41) as Int?

    open var constructionManagerCode: Int?
        set(value): Unit = set(42, value)
        get(): Int? = get(42) as Int?

    open var chiefArchitectCode: Int?
        set(value): Unit = set(43, value)
        get(): Int? = get(43) as Int?

    open var orderDate: Int?
        set(value): Unit = set(44, value)
        get(): Int? = get(44) as Int?

    open var contractPlannedDate: Int?
        set(value): Unit = set(45, value)
        get(): Int? = get(45) as Int?

    open var contractDate: Int?
        set(value): Unit = set(46, value)
        get(): Int? = get(46) as Int?

    open var lastChangeContractDate: Int?
        set(value): Unit = set(47, value)
        get(): Int? = get(47) as Int?

    open var agreedCancellationDate: Int?
        set(value): Unit = set(48, value)
        get(): Int? = get(48) as Int?

    open var completionReportOutputCount: Short?
        set(value): Unit = set(49, value)
        get(): Short? = get(49) as Short?

    open var completionReportCreationDate: Int?
        set(value): Unit = set(50, value)
        get(): Int? = get(50) as Int?

    open var completionReportCreationCompanyCode: String?
        set(value): Unit = set(51, value)
        get(): String? = get(51) as String?

    open var completionReportCreationOfficeCode: String?
        set(value): Unit = set(52, value)
        get(): String? = get(52) as String?

    open var completionReportCreationManagerCode: String?
        set(value): Unit = set(53, value)
        get(): String? = get(53) as String?

    open var completionHandoverDate: Int?
        set(value): Unit = set(54, value)
        get(): Int? = get(54) as Int?

    open var completionApprovalDate: Int?
        set(value): Unit = set(55, value)
        get(): Int? = get(55) as Int?

    open var completionReportCollectionInputDate: Int?
        set(value): Unit = set(56, value)
        get(): Int? = get(56) as Int?

    open var completionReportCollectionCompanyCd: String?
        set(value): Unit = set(57, value)
        get(): String? = get(57) as String?

    open var completionReportCollectionOfficeCode: String?
        set(value): Unit = set(58, value)
        get(): String? = get(58) as String?

    open var completionReportCollectionManagerCd: String?
        set(value): Unit = set(59, value)
        get(): String? = get(59) as String?

    open var completionReportHeadOfficeReceiptDate: Int?
        set(value): Unit = set(60, value)
        get(): Int? = get(60) as Int?

    open var completionReportReceiptCompanyCode: String?
        set(value): Unit = set(61, value)
        get(): String? = get(61) as String?

    open var completionReportReceiptOfficeCode: String?
        set(value): Unit = set(62, value)
        get(): String? = get(62) as String?

    open var completionReportReceiptManagerCode: String?
        set(value): Unit = set(63, value)
        get(): String? = get(63) as String?

    open var occupancyPlannedDate: Int?
        set(value): Unit = set(64, value)
        get(): Int? = get(64) as Int?

    open var publicFinancingDivision: String?
        set(value): Unit = set(65, value)
        get(): String? = get(65) as String?

    open var developmentApplicationDivision: String?
        set(value): Unit = set(66, value)
        get(): String? = get(66) as String?

    open var reviewDivision: String?
        set(value): Unit = set(67, value)
        get(): String? = get(67) as String?

    open var applicationProcessStateDivision: String?
        set(value): Unit = set(68, value)
        get(): String? = get(68) as String?

    open var applicationProcessHoldStartDate: Int?
        set(value): Unit = set(69, value)
        get(): Int? = get(69) as Int?

    open var applicationProcessHoldDays: Short?
        set(value): Unit = set(70, value)
        get(): Short? = get(70) as Short?

    open var constructionProcessStateDivision: String?
        set(value): Unit = set(71, value)
        get(): String? = get(71) as String?

    open var constructionProcessHoldStartDate: Int?
        set(value): Unit = set(72, value)
        get(): Int? = get(72) as Int?

    open var constructionProcessHoldDays: Short?
        set(value): Unit = set(73, value)
        get(): Short? = get(73) as Short?

    open var csOutputCount: Short?
        set(value): Unit = set(74, value)
        get(): Short? = get(74) as Short?

    open var processChangeStatus: String?
        set(value): Unit = set(75, value)
        get(): String? = get(75) as String?

    open var additionalConstructionDivision: String?
        set(value): Unit = set(76, value)
        get(): String? = get(76) as String?

    open var applicationAcceptancePlannedDate_1: Int?
        set(value): Unit = set(77, value)
        get(): Int? = get(77) as Int?

    open var applicationAcceptancePlannedDate_2: Int?
        set(value): Unit = set(78, value)
        get(): Int? = get(78) as Int?

    open var applicationAcceptancePlannedDate_3: Int?
        set(value): Unit = set(79, value)
        get(): Int? = get(79) as Int?

    open var applicationAcceptancePlannedDate_4: Int?
        set(value): Unit = set(80, value)
        get(): Int? = get(80) as Int?

    open var applicationAcceptanceActualDate: Int?
        set(value): Unit = set(81, value)
        get(): Int? = get(81) as Int?

    open var applicationPermissionPlannedDate_1: Int?
        set(value): Unit = set(82, value)
        get(): Int? = get(82) as Int?

    open var applicationPermissionPlannedDate_2: Int?
        set(value): Unit = set(83, value)
        get(): Int? = get(83) as Int?

    open var applicationPermissionPlannedDate_3: Int?
        set(value): Unit = set(84, value)
        get(): Int? = get(84) as Int?

    open var applicationPermissionPlannedDate_4: Int?
        set(value): Unit = set(85, value)
        get(): Int? = get(85) as Int?

    open var applicationPermissionActualDate: Int?
        set(value): Unit = set(86, value)
        get(): Int? = get(86) as Int?

    open var constructionStartPlannedDate_1: Int?
        set(value): Unit = set(87, value)
        get(): Int? = get(87) as Int?

    open var constructionStartPlannedDate_2: Int?
        set(value): Unit = set(88, value)
        get(): Int? = get(88) as Int?

    open var constructionStartPlannedDate_3: Int?
        set(value): Unit = set(89, value)
        get(): Int? = get(89) as Int?

    open var constructionStartPlannedDate_4: Int?
        set(value): Unit = set(90, value)
        get(): Int? = get(90) as Int?

    open var constructionStartActualDate: Int?
        set(value): Unit = set(91, value)
        get(): Int? = get(91) as Int?

    open var constructionCompletionPlannedDate_1: Int?
        set(value): Unit = set(92, value)
        get(): Int? = get(92) as Int?

    open var constructionCompletionPlannedDate_2: Int?
        set(value): Unit = set(93, value)
        get(): Int? = get(93) as Int?

    open var constructionCompletionPlannedDate_3: Int?
        set(value): Unit = set(94, value)
        get(): Int? = get(94) as Int?

    open var constructionCompletionPlannedDate_4: Int?
        set(value): Unit = set(95, value)
        get(): Int? = get(95) as Int?

    open var constructionCompletionActualDate: Int?
        set(value): Unit = set(96, value)
        get(): Int? = get(96) as Int?

    open var majorScheduleCode_01: Short?
        set(value): Unit = set(97, value)
        get(): Short? = get(97) as Short?

    open var majorScheduleCode_02: Short?
        set(value): Unit = set(98, value)
        get(): Short? = get(98) as Short?

    open var majorScheduleCode_03: Short?
        set(value): Unit = set(99, value)
        get(): Short? = get(99) as Short?

    open var majorScheduleCode_04: Short?
        set(value): Unit = set(100, value)
        get(): Short? = get(100) as Short?

    open var majorScheduleCode_05: Short?
        set(value): Unit = set(101, value)
        get(): Short? = get(101) as Short?

    open var majorScheduleCode_06: Short?
        set(value): Unit = set(102, value)
        get(): Short? = get(102) as Short?

    open var majorScheduleCode_07: Short?
        set(value): Unit = set(103, value)
        get(): Short? = get(103) as Short?

    open var majorScheduleCode_08: Short?
        set(value): Unit = set(104, value)
        get(): Short? = get(104) as Short?

    open var majorScheduleCode_09: Short?
        set(value): Unit = set(105, value)
        get(): Short? = get(105) as Short?

    open var majorScheduleCode_10: Short?
        set(value): Unit = set(106, value)
        get(): Short? = get(106) as Short?

    open var majorScheduleCode_11: Short?
        set(value): Unit = set(107, value)
        get(): Short? = get(107) as Short?

    open var majorScheduleCode_12: Short?
        set(value): Unit = set(108, value)
        get(): Short? = get(108) as Short?

    open var majorScheduleCode_13: Short?
        set(value): Unit = set(109, value)
        get(): Short? = get(109) as Short?

    open var majorScheduleCode_14: Short?
        set(value): Unit = set(110, value)
        get(): Short? = get(110) as Short?

    open var majorScheduleCode_15: Short?
        set(value): Unit = set(111, value)
        get(): Short? = get(111) as Short?

    open var majorScheduleCode_16: Short?
        set(value): Unit = set(112, value)
        get(): Short? = get(112) as Short?

    open var majorScheduleCode_17: Short?
        set(value): Unit = set(113, value)
        get(): Short? = get(113) as Short?

    open var majorScheduleCode_18: Short?
        set(value): Unit = set(114, value)
        get(): Short? = get(114) as Short?

    open var majorScheduleCode_19: Short?
        set(value): Unit = set(115, value)
        get(): Short? = get(115) as Short?

    open var majorScheduleCode_20: Short?
        set(value): Unit = set(116, value)
        get(): Short? = get(116) as Short?

    open var majorScheduleCode_21: Short?
        set(value): Unit = set(117, value)
        get(): Short? = get(117) as Short?

    open var majorScheduleCode_22: Short?
        set(value): Unit = set(118, value)
        get(): Short? = get(118) as Short?

    open var majorScheduleCode_23: Short?
        set(value): Unit = set(119, value)
        get(): Short? = get(119) as Short?

    open var majorScheduleCode_24: Short?
        set(value): Unit = set(120, value)
        get(): Short? = get(120) as Short?

    open var majorScheduleCode_25: Short?
        set(value): Unit = set(121, value)
        get(): Short? = get(121) as Short?

    open var majorScheduleCode_26: Short?
        set(value): Unit = set(122, value)
        get(): Short? = get(122) as Short?

    open var majorScheduleCode_27: Short?
        set(value): Unit = set(123, value)
        get(): Short? = get(123) as Short?

    open var majorScheduleCode_28: Short?
        set(value): Unit = set(124, value)
        get(): Short? = get(124) as Short?

    open var majorScheduleCode_29: Short?
        set(value): Unit = set(125, value)
        get(): Short? = get(125) as Short?

    open var majorScheduleCode_30: Short?
        set(value): Unit = set(126, value)
        get(): Short? = get(126) as Short?

    open var majorSchedulePlannedDate_01: Int?
        set(value): Unit = set(127, value)
        get(): Int? = get(127) as Int?

    open var majorSchedulePlannedDate_02: Int?
        set(value): Unit = set(128, value)
        get(): Int? = get(128) as Int?

    open var majorSchedulePlannedDate_03: Int?
        set(value): Unit = set(129, value)
        get(): Int? = get(129) as Int?

    open var majorSchedulePlannedDate_04: Int?
        set(value): Unit = set(130, value)
        get(): Int? = get(130) as Int?

    open var majorSchedulePlannedDate_05: Int?
        set(value): Unit = set(131, value)
        get(): Int? = get(131) as Int?

    open var majorSchedulePlannedDate_06: Int?
        set(value): Unit = set(132, value)
        get(): Int? = get(132) as Int?

    open var majorSchedulePlannedDate_07: Int?
        set(value): Unit = set(133, value)
        get(): Int? = get(133) as Int?

    open var majorSchedulePlannedDate_08: Int?
        set(value): Unit = set(134, value)
        get(): Int? = get(134) as Int?

    open var majorSchedulePlannedDate_09: Int?
        set(value): Unit = set(135, value)
        get(): Int? = get(135) as Int?

    open var majorSchedulePlannedDate_10: Int?
        set(value): Unit = set(136, value)
        get(): Int? = get(136) as Int?

    open var majorSchedulePlannedDate_11: Int?
        set(value): Unit = set(137, value)
        get(): Int? = get(137) as Int?

    open var majorSchedulePlannedDate_12: Int?
        set(value): Unit = set(138, value)
        get(): Int? = get(138) as Int?

    open var majorSchedulePlannedDate_13: Int?
        set(value): Unit = set(139, value)
        get(): Int? = get(139) as Int?

    open var majorSchedulePlannedDate_14: Int?
        set(value): Unit = set(140, value)
        get(): Int? = get(140) as Int?

    open var majorSchedulePlannedDate_15: Int?
        set(value): Unit = set(141, value)
        get(): Int? = get(141) as Int?

    open var majorSchedulePlannedDate_16: Int?
        set(value): Unit = set(142, value)
        get(): Int? = get(142) as Int?

    open var majorSchedulePlannedDate_17: Int?
        set(value): Unit = set(143, value)
        get(): Int? = get(143) as Int?

    open var majorSchedulePlannedDate_18: Int?
        set(value): Unit = set(144, value)
        get(): Int? = get(144) as Int?

    open var majorSchedulePlannedDate_19: Int?
        set(value): Unit = set(145, value)
        get(): Int? = get(145) as Int?

    open var majorSchedulePlannedDate_20: Int?
        set(value): Unit = set(146, value)
        get(): Int? = get(146) as Int?

    open var majorSchedulePlannedDate_21: Int?
        set(value): Unit = set(147, value)
        get(): Int? = get(147) as Int?

    open var majorSchedulePlannedDate_22: Int?
        set(value): Unit = set(148, value)
        get(): Int? = get(148) as Int?

    open var majorSchedulePlannedDate_23: Int?
        set(value): Unit = set(149, value)
        get(): Int? = get(149) as Int?

    open var majorSchedulePlannedDate_24: Int?
        set(value): Unit = set(150, value)
        get(): Int? = get(150) as Int?

    open var majorSchedulePlannedDate_25: Int?
        set(value): Unit = set(151, value)
        get(): Int? = get(151) as Int?

    open var majorSchedulePlannedDate_26: Int?
        set(value): Unit = set(152, value)
        get(): Int? = get(152) as Int?

    open var majorSchedulePlannedDate_27: Int?
        set(value): Unit = set(153, value)
        get(): Int? = get(153) as Int?

    open var majorSchedulePlannedDate_28: Int?
        set(value): Unit = set(154, value)
        get(): Int? = get(154) as Int?

    open var majorSchedulePlannedDate_29: Int?
        set(value): Unit = set(155, value)
        get(): Int? = get(155) as Int?

    open var majorSchedulePlannedDate_30: Int?
        set(value): Unit = set(156, value)
        get(): Int? = get(156) as Int?

    open var majorScheduleActualDate_01: Int?
        set(value): Unit = set(157, value)
        get(): Int? = get(157) as Int?

    open var majorScheduleActualDate_02: Int?
        set(value): Unit = set(158, value)
        get(): Int? = get(158) as Int?

    open var majorScheduleActualDate_03: Int?
        set(value): Unit = set(159, value)
        get(): Int? = get(159) as Int?

    open var majorScheduleActualDate_04: Int?
        set(value): Unit = set(160, value)
        get(): Int? = get(160) as Int?

    open var majorScheduleActualDate_05: Int?
        set(value): Unit = set(161, value)
        get(): Int? = get(161) as Int?

    open var majorScheduleActualDate_06: Int?
        set(value): Unit = set(162, value)
        get(): Int? = get(162) as Int?

    open var majorScheduleActualDate_07: Int?
        set(value): Unit = set(163, value)
        get(): Int? = get(163) as Int?

    open var majorScheduleActualDate_08: Int?
        set(value): Unit = set(164, value)
        get(): Int? = get(164) as Int?

    open var majorScheduleActualDate_09: Int?
        set(value): Unit = set(165, value)
        get(): Int? = get(165) as Int?

    open var majorScheduleActualDate_10: Int?
        set(value): Unit = set(166, value)
        get(): Int? = get(166) as Int?

    open var majorScheduleActualDate_11: Int?
        set(value): Unit = set(167, value)
        get(): Int? = get(167) as Int?

    open var majorScheduleActualDate_12: Int?
        set(value): Unit = set(168, value)
        get(): Int? = get(168) as Int?

    open var majorScheduleActualDate_13: Int?
        set(value): Unit = set(169, value)
        get(): Int? = get(169) as Int?

    open var majorScheduleActualDate_14: Int?
        set(value): Unit = set(170, value)
        get(): Int? = get(170) as Int?

    open var majorScheduleActualDate_15: Int?
        set(value): Unit = set(171, value)
        get(): Int? = get(171) as Int?

    open var majorScheduleActualDate_16: Int?
        set(value): Unit = set(172, value)
        get(): Int? = get(172) as Int?

    open var majorScheduleActualDate_17: Int?
        set(value): Unit = set(173, value)
        get(): Int? = get(173) as Int?

    open var majorScheduleActualDate_18: Int?
        set(value): Unit = set(174, value)
        get(): Int? = get(174) as Int?

    open var majorScheduleActualDate_19: Int?
        set(value): Unit = set(175, value)
        get(): Int? = get(175) as Int?

    open var majorScheduleActualDate_20: Int?
        set(value): Unit = set(176, value)
        get(): Int? = get(176) as Int?

    open var majorScheduleActualDate_21: Int?
        set(value): Unit = set(177, value)
        get(): Int? = get(177) as Int?

    open var majorScheduleActualDate_22: Int?
        set(value): Unit = set(178, value)
        get(): Int? = get(178) as Int?

    open var majorScheduleActualDate_23: Int?
        set(value): Unit = set(179, value)
        get(): Int? = get(179) as Int?

    open var majorScheduleActualDate_24: Int?
        set(value): Unit = set(180, value)
        get(): Int? = get(180) as Int?

    open var majorScheduleActualDate_25: Int?
        set(value): Unit = set(181, value)
        get(): Int? = get(181) as Int?

    open var majorScheduleActualDate_26: Int?
        set(value): Unit = set(182, value)
        get(): Int? = get(182) as Int?

    open var majorScheduleActualDate_27: Int?
        set(value): Unit = set(183, value)
        get(): Int? = get(183) as Int?

    open var majorScheduleActualDate_28: Int?
        set(value): Unit = set(184, value)
        get(): Int? = get(184) as Int?

    open var majorScheduleActualDate_29: Int?
        set(value): Unit = set(185, value)
        get(): Int? = get(185) as Int?

    open var majorScheduleActualDate_30: Int?
        set(value): Unit = set(186, value)
        get(): Int? = get(186) as Int?

    open var orderCodeSt: String?
        set(value): Unit = set(187, value)
        get(): String? = get(187) as String?

    open var additionalCodeSt: String?
        set(value): Unit = set(188, value)
        get(): String? = get(188) as String?

    open var buildManagerCodeSt: String?
        set(value): Unit = set(189, value)
        get(): String? = get(189) as String?

    open var designManagerCodeSt: String?
        set(value): Unit = set(190, value)
        get(): String? = get(190) as String?

    open var designSupervisorCodeSt: String?
        set(value): Unit = set(191, value)
        get(): String? = get(191) as String?

    open var applicationManagerCodeSt: String?
        set(value): Unit = set(192, value)
        get(): String? = get(192) as String?

    open var estimationManagerCodeSt: String?
        set(value): Unit = set(193, value)
        get(): String? = get(193) as String?

    open var developmentManagerCodeSt: String?
        set(value): Unit = set(194, value)
        get(): String? = get(194) as String?

    open var constructionSupervisorCodeSt: String?
        set(value): Unit = set(195, value)
        get(): String? = get(195) as String?

    open var constructionStaffCodeSt: String?
        set(value): Unit = set(196, value)
        get(): String? = get(196) as String?

    open var constructionManagerCodeSt: String?
        set(value): Unit = set(197, value)
        get(): String? = get(197) as String?

    open var chiefArchitectCodeSt: String?
        set(value): Unit = set(198, value)
        get(): String? = get(198) as String?

    open var constructionSupervisionOfficeCode: String?
        set(value): Unit = set(199, value)
        get(): String? = get(199) as String?

    open var mechanismFinancingDivision: String?
        set(value): Unit = set(200, value)
        get(): String? = get(200) as String?

    /**
     * Create a detached, initialised PropertyBasicFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgram: String? = null, updater: String? = null, deleteFlag: String? = null, orderCode: Int? = null, additionalCode: Short? = null, leaseTaxCellDivision: String? = null, constructionName: String? = null, contractTypeOccurrence: String? = null, buildingCount: Short? = null, clientOtherConstructionDivision: String? = null, clientOtherCompletionDate: Int? = null, municipalityCode_1: Byte? = null, municipalityCode_2: Short? = null, constructionLocation_1: String? = null, constructionLocation_2: String? = null, constructionLocation_3: String? = null, orderCompanyCode: String? = null, orderOfficeCode: Int? = null, contractCompanyCode: String? = null, contractOfficeCode: Int? = null, buildManagerCode: Int? = null, designCompanyCode: String? = null, designOfficeCode: String? = null, designManagerCode: Int? = null, designSupervisorCode: Int? = null, applicationCompanyCode: String? = null, applicationOfficeCode: Int? = null, applicationManagerCode: Int? = null, estimationCompanyCode: String? = null, estimationOfficeCode: String? = null, estimationManagerCode: Int? = null, developmentCompanyCode: String? = null, developmentOfficeCode: String? = null, developmentManagerCode: Int? = null, constructionCompanyCode: String? = null, constructionOfficeCode: Int? = null, constructionSupervisorCode: Int? = null, constructionStaffCode: Int? = null, constructionManagerCode: Int? = null, chiefArchitectCode: Int? = null, orderDate: Int? = null, contractPlannedDate: Int? = null, contractDate: Int? = null, lastChangeContractDate: Int? = null, agreedCancellationDate: Int? = null, completionReportOutputCount: Short? = null, completionReportCreationDate: Int? = null, completionReportCreationCompanyCode: String? = null, completionReportCreationOfficeCode: String? = null, completionReportCreationManagerCode: String? = null, completionHandoverDate: Int? = null, completionApprovalDate: Int? = null, completionReportCollectionInputDate: Int? = null, completionReportCollectionCompanyCd: String? = null, completionReportCollectionOfficeCode: String? = null, completionReportCollectionManagerCd: String? = null, completionReportHeadOfficeReceiptDate: Int? = null, completionReportReceiptCompanyCode: String? = null, completionReportReceiptOfficeCode: String? = null, completionReportReceiptManagerCode: String? = null, occupancyPlannedDate: Int? = null, publicFinancingDivision: String? = null, developmentApplicationDivision: String? = null, reviewDivision: String? = null, applicationProcessStateDivision: String? = null, applicationProcessHoldStartDate: Int? = null, applicationProcessHoldDays: Short? = null, constructionProcessStateDivision: String? = null, constructionProcessHoldStartDate: Int? = null, constructionProcessHoldDays: Short? = null, csOutputCount: Short? = null, processChangeStatus: String? = null, additionalConstructionDivision: String? = null, applicationAcceptancePlannedDate_1: Int? = null, applicationAcceptancePlannedDate_2: Int? = null, applicationAcceptancePlannedDate_3: Int? = null, applicationAcceptancePlannedDate_4: Int? = null, applicationAcceptanceActualDate: Int? = null, applicationPermissionPlannedDate_1: Int? = null, applicationPermissionPlannedDate_2: Int? = null, applicationPermissionPlannedDate_3: Int? = null, applicationPermissionPlannedDate_4: Int? = null, applicationPermissionActualDate: Int? = null, constructionStartPlannedDate_1: Int? = null, constructionStartPlannedDate_2: Int? = null, constructionStartPlannedDate_3: Int? = null, constructionStartPlannedDate_4: Int? = null, constructionStartActualDate: Int? = null, constructionCompletionPlannedDate_1: Int? = null, constructionCompletionPlannedDate_2: Int? = null, constructionCompletionPlannedDate_3: Int? = null, constructionCompletionPlannedDate_4: Int? = null, constructionCompletionActualDate: Int? = null, majorScheduleCode_01: Short? = null, majorScheduleCode_02: Short? = null, majorScheduleCode_03: Short? = null, majorScheduleCode_04: Short? = null, majorScheduleCode_05: Short? = null, majorScheduleCode_06: Short? = null, majorScheduleCode_07: Short? = null, majorScheduleCode_08: Short? = null, majorScheduleCode_09: Short? = null, majorScheduleCode_10: Short? = null, majorScheduleCode_11: Short? = null, majorScheduleCode_12: Short? = null, majorScheduleCode_13: Short? = null, majorScheduleCode_14: Short? = null, majorScheduleCode_15: Short? = null, majorScheduleCode_16: Short? = null, majorScheduleCode_17: Short? = null, majorScheduleCode_18: Short? = null, majorScheduleCode_19: Short? = null, majorScheduleCode_20: Short? = null, majorScheduleCode_21: Short? = null, majorScheduleCode_22: Short? = null, majorScheduleCode_23: Short? = null, majorScheduleCode_24: Short? = null, majorScheduleCode_25: Short? = null, majorScheduleCode_26: Short? = null, majorScheduleCode_27: Short? = null, majorScheduleCode_28: Short? = null, majorScheduleCode_29: Short? = null, majorScheduleCode_30: Short? = null, majorSchedulePlannedDate_01: Int? = null, majorSchedulePlannedDate_02: Int? = null, majorSchedulePlannedDate_03: Int? = null, majorSchedulePlannedDate_04: Int? = null, majorSchedulePlannedDate_05: Int? = null, majorSchedulePlannedDate_06: Int? = null, majorSchedulePlannedDate_07: Int? = null, majorSchedulePlannedDate_08: Int? = null, majorSchedulePlannedDate_09: Int? = null, majorSchedulePlannedDate_10: Int? = null, majorSchedulePlannedDate_11: Int? = null, majorSchedulePlannedDate_12: Int? = null, majorSchedulePlannedDate_13: Int? = null, majorSchedulePlannedDate_14: Int? = null, majorSchedulePlannedDate_15: Int? = null, majorSchedulePlannedDate_16: Int? = null, majorSchedulePlannedDate_17: Int? = null, majorSchedulePlannedDate_18: Int? = null, majorSchedulePlannedDate_19: Int? = null, majorSchedulePlannedDate_20: Int? = null, majorSchedulePlannedDate_21: Int? = null, majorSchedulePlannedDate_22: Int? = null, majorSchedulePlannedDate_23: Int? = null, majorSchedulePlannedDate_24: Int? = null, majorSchedulePlannedDate_25: Int? = null, majorSchedulePlannedDate_26: Int? = null, majorSchedulePlannedDate_27: Int? = null, majorSchedulePlannedDate_28: Int? = null, majorSchedulePlannedDate_29: Int? = null, majorSchedulePlannedDate_30: Int? = null, majorScheduleActualDate_01: Int? = null, majorScheduleActualDate_02: Int? = null, majorScheduleActualDate_03: Int? = null, majorScheduleActualDate_04: Int? = null, majorScheduleActualDate_05: Int? = null, majorScheduleActualDate_06: Int? = null, majorScheduleActualDate_07: Int? = null, majorScheduleActualDate_08: Int? = null, majorScheduleActualDate_09: Int? = null, majorScheduleActualDate_10: Int? = null, majorScheduleActualDate_11: Int? = null, majorScheduleActualDate_12: Int? = null, majorScheduleActualDate_13: Int? = null, majorScheduleActualDate_14: Int? = null, majorScheduleActualDate_15: Int? = null, majorScheduleActualDate_16: Int? = null, majorScheduleActualDate_17: Int? = null, majorScheduleActualDate_18: Int? = null, majorScheduleActualDate_19: Int? = null, majorScheduleActualDate_20: Int? = null, majorScheduleActualDate_21: Int? = null, majorScheduleActualDate_22: Int? = null, majorScheduleActualDate_23: Int? = null, majorScheduleActualDate_24: Int? = null, majorScheduleActualDate_25: Int? = null, majorScheduleActualDate_26: Int? = null, majorScheduleActualDate_27: Int? = null, majorScheduleActualDate_28: Int? = null, majorScheduleActualDate_29: Int? = null, majorScheduleActualDate_30: Int? = null, orderCodeSt: String? = null, additionalCodeSt: String? = null, buildManagerCodeSt: String? = null, designManagerCodeSt: String? = null, designSupervisorCodeSt: String? = null, applicationManagerCodeSt: String? = null, estimationManagerCodeSt: String? = null, developmentManagerCodeSt: String? = null, constructionSupervisorCodeSt: String? = null, constructionStaffCodeSt: String? = null, constructionManagerCodeSt: String? = null, chiefArchitectCodeSt: String? = null, constructionSupervisionOfficeCode: String? = null, mechanismFinancingDivision: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgram = updateProgram
        this.updater = updater
        this.deleteFlag = deleteFlag
        this.orderCode = orderCode
        this.additionalCode = additionalCode
        this.leaseTaxCellDivision = leaseTaxCellDivision
        this.constructionName = constructionName
        this.contractTypeOccurrence = contractTypeOccurrence
        this.buildingCount = buildingCount
        this.clientOtherConstructionDivision = clientOtherConstructionDivision
        this.clientOtherCompletionDate = clientOtherCompletionDate
        this.municipalityCode_1 = municipalityCode_1
        this.municipalityCode_2 = municipalityCode_2
        this.constructionLocation_1 = constructionLocation_1
        this.constructionLocation_2 = constructionLocation_2
        this.constructionLocation_3 = constructionLocation_3
        this.orderCompanyCode = orderCompanyCode
        this.orderOfficeCode = orderOfficeCode
        this.contractCompanyCode = contractCompanyCode
        this.contractOfficeCode = contractOfficeCode
        this.buildManagerCode = buildManagerCode
        this.designCompanyCode = designCompanyCode
        this.designOfficeCode = designOfficeCode
        this.designManagerCode = designManagerCode
        this.designSupervisorCode = designSupervisorCode
        this.applicationCompanyCode = applicationCompanyCode
        this.applicationOfficeCode = applicationOfficeCode
        this.applicationManagerCode = applicationManagerCode
        this.estimationCompanyCode = estimationCompanyCode
        this.estimationOfficeCode = estimationOfficeCode
        this.estimationManagerCode = estimationManagerCode
        this.developmentCompanyCode = developmentCompanyCode
        this.developmentOfficeCode = developmentOfficeCode
        this.developmentManagerCode = developmentManagerCode
        this.constructionCompanyCode = constructionCompanyCode
        this.constructionOfficeCode = constructionOfficeCode
        this.constructionSupervisorCode = constructionSupervisorCode
        this.constructionStaffCode = constructionStaffCode
        this.constructionManagerCode = constructionManagerCode
        this.chiefArchitectCode = chiefArchitectCode
        this.orderDate = orderDate
        this.contractPlannedDate = contractPlannedDate
        this.contractDate = contractDate
        this.lastChangeContractDate = lastChangeContractDate
        this.agreedCancellationDate = agreedCancellationDate
        this.completionReportOutputCount = completionReportOutputCount
        this.completionReportCreationDate = completionReportCreationDate
        this.completionReportCreationCompanyCode = completionReportCreationCompanyCode
        this.completionReportCreationOfficeCode = completionReportCreationOfficeCode
        this.completionReportCreationManagerCode = completionReportCreationManagerCode
        this.completionHandoverDate = completionHandoverDate
        this.completionApprovalDate = completionApprovalDate
        this.completionReportCollectionInputDate = completionReportCollectionInputDate
        this.completionReportCollectionCompanyCd = completionReportCollectionCompanyCd
        this.completionReportCollectionOfficeCode = completionReportCollectionOfficeCode
        this.completionReportCollectionManagerCd = completionReportCollectionManagerCd
        this.completionReportHeadOfficeReceiptDate = completionReportHeadOfficeReceiptDate
        this.completionReportReceiptCompanyCode = completionReportReceiptCompanyCode
        this.completionReportReceiptOfficeCode = completionReportReceiptOfficeCode
        this.completionReportReceiptManagerCode = completionReportReceiptManagerCode
        this.occupancyPlannedDate = occupancyPlannedDate
        this.publicFinancingDivision = publicFinancingDivision
        this.developmentApplicationDivision = developmentApplicationDivision
        this.reviewDivision = reviewDivision
        this.applicationProcessStateDivision = applicationProcessStateDivision
        this.applicationProcessHoldStartDate = applicationProcessHoldStartDate
        this.applicationProcessHoldDays = applicationProcessHoldDays
        this.constructionProcessStateDivision = constructionProcessStateDivision
        this.constructionProcessHoldStartDate = constructionProcessHoldStartDate
        this.constructionProcessHoldDays = constructionProcessHoldDays
        this.csOutputCount = csOutputCount
        this.processChangeStatus = processChangeStatus
        this.additionalConstructionDivision = additionalConstructionDivision
        this.applicationAcceptancePlannedDate_1 = applicationAcceptancePlannedDate_1
        this.applicationAcceptancePlannedDate_2 = applicationAcceptancePlannedDate_2
        this.applicationAcceptancePlannedDate_3 = applicationAcceptancePlannedDate_3
        this.applicationAcceptancePlannedDate_4 = applicationAcceptancePlannedDate_4
        this.applicationAcceptanceActualDate = applicationAcceptanceActualDate
        this.applicationPermissionPlannedDate_1 = applicationPermissionPlannedDate_1
        this.applicationPermissionPlannedDate_2 = applicationPermissionPlannedDate_2
        this.applicationPermissionPlannedDate_3 = applicationPermissionPlannedDate_3
        this.applicationPermissionPlannedDate_4 = applicationPermissionPlannedDate_4
        this.applicationPermissionActualDate = applicationPermissionActualDate
        this.constructionStartPlannedDate_1 = constructionStartPlannedDate_1
        this.constructionStartPlannedDate_2 = constructionStartPlannedDate_2
        this.constructionStartPlannedDate_3 = constructionStartPlannedDate_3
        this.constructionStartPlannedDate_4 = constructionStartPlannedDate_4
        this.constructionStartActualDate = constructionStartActualDate
        this.constructionCompletionPlannedDate_1 = constructionCompletionPlannedDate_1
        this.constructionCompletionPlannedDate_2 = constructionCompletionPlannedDate_2
        this.constructionCompletionPlannedDate_3 = constructionCompletionPlannedDate_3
        this.constructionCompletionPlannedDate_4 = constructionCompletionPlannedDate_4
        this.constructionCompletionActualDate = constructionCompletionActualDate
        this.majorScheduleCode_01 = majorScheduleCode_01
        this.majorScheduleCode_02 = majorScheduleCode_02
        this.majorScheduleCode_03 = majorScheduleCode_03
        this.majorScheduleCode_04 = majorScheduleCode_04
        this.majorScheduleCode_05 = majorScheduleCode_05
        this.majorScheduleCode_06 = majorScheduleCode_06
        this.majorScheduleCode_07 = majorScheduleCode_07
        this.majorScheduleCode_08 = majorScheduleCode_08
        this.majorScheduleCode_09 = majorScheduleCode_09
        this.majorScheduleCode_10 = majorScheduleCode_10
        this.majorScheduleCode_11 = majorScheduleCode_11
        this.majorScheduleCode_12 = majorScheduleCode_12
        this.majorScheduleCode_13 = majorScheduleCode_13
        this.majorScheduleCode_14 = majorScheduleCode_14
        this.majorScheduleCode_15 = majorScheduleCode_15
        this.majorScheduleCode_16 = majorScheduleCode_16
        this.majorScheduleCode_17 = majorScheduleCode_17
        this.majorScheduleCode_18 = majorScheduleCode_18
        this.majorScheduleCode_19 = majorScheduleCode_19
        this.majorScheduleCode_20 = majorScheduleCode_20
        this.majorScheduleCode_21 = majorScheduleCode_21
        this.majorScheduleCode_22 = majorScheduleCode_22
        this.majorScheduleCode_23 = majorScheduleCode_23
        this.majorScheduleCode_24 = majorScheduleCode_24
        this.majorScheduleCode_25 = majorScheduleCode_25
        this.majorScheduleCode_26 = majorScheduleCode_26
        this.majorScheduleCode_27 = majorScheduleCode_27
        this.majorScheduleCode_28 = majorScheduleCode_28
        this.majorScheduleCode_29 = majorScheduleCode_29
        this.majorScheduleCode_30 = majorScheduleCode_30
        this.majorSchedulePlannedDate_01 = majorSchedulePlannedDate_01
        this.majorSchedulePlannedDate_02 = majorSchedulePlannedDate_02
        this.majorSchedulePlannedDate_03 = majorSchedulePlannedDate_03
        this.majorSchedulePlannedDate_04 = majorSchedulePlannedDate_04
        this.majorSchedulePlannedDate_05 = majorSchedulePlannedDate_05
        this.majorSchedulePlannedDate_06 = majorSchedulePlannedDate_06
        this.majorSchedulePlannedDate_07 = majorSchedulePlannedDate_07
        this.majorSchedulePlannedDate_08 = majorSchedulePlannedDate_08
        this.majorSchedulePlannedDate_09 = majorSchedulePlannedDate_09
        this.majorSchedulePlannedDate_10 = majorSchedulePlannedDate_10
        this.majorSchedulePlannedDate_11 = majorSchedulePlannedDate_11
        this.majorSchedulePlannedDate_12 = majorSchedulePlannedDate_12
        this.majorSchedulePlannedDate_13 = majorSchedulePlannedDate_13
        this.majorSchedulePlannedDate_14 = majorSchedulePlannedDate_14
        this.majorSchedulePlannedDate_15 = majorSchedulePlannedDate_15
        this.majorSchedulePlannedDate_16 = majorSchedulePlannedDate_16
        this.majorSchedulePlannedDate_17 = majorSchedulePlannedDate_17
        this.majorSchedulePlannedDate_18 = majorSchedulePlannedDate_18
        this.majorSchedulePlannedDate_19 = majorSchedulePlannedDate_19
        this.majorSchedulePlannedDate_20 = majorSchedulePlannedDate_20
        this.majorSchedulePlannedDate_21 = majorSchedulePlannedDate_21
        this.majorSchedulePlannedDate_22 = majorSchedulePlannedDate_22
        this.majorSchedulePlannedDate_23 = majorSchedulePlannedDate_23
        this.majorSchedulePlannedDate_24 = majorSchedulePlannedDate_24
        this.majorSchedulePlannedDate_25 = majorSchedulePlannedDate_25
        this.majorSchedulePlannedDate_26 = majorSchedulePlannedDate_26
        this.majorSchedulePlannedDate_27 = majorSchedulePlannedDate_27
        this.majorSchedulePlannedDate_28 = majorSchedulePlannedDate_28
        this.majorSchedulePlannedDate_29 = majorSchedulePlannedDate_29
        this.majorSchedulePlannedDate_30 = majorSchedulePlannedDate_30
        this.majorScheduleActualDate_01 = majorScheduleActualDate_01
        this.majorScheduleActualDate_02 = majorScheduleActualDate_02
        this.majorScheduleActualDate_03 = majorScheduleActualDate_03
        this.majorScheduleActualDate_04 = majorScheduleActualDate_04
        this.majorScheduleActualDate_05 = majorScheduleActualDate_05
        this.majorScheduleActualDate_06 = majorScheduleActualDate_06
        this.majorScheduleActualDate_07 = majorScheduleActualDate_07
        this.majorScheduleActualDate_08 = majorScheduleActualDate_08
        this.majorScheduleActualDate_09 = majorScheduleActualDate_09
        this.majorScheduleActualDate_10 = majorScheduleActualDate_10
        this.majorScheduleActualDate_11 = majorScheduleActualDate_11
        this.majorScheduleActualDate_12 = majorScheduleActualDate_12
        this.majorScheduleActualDate_13 = majorScheduleActualDate_13
        this.majorScheduleActualDate_14 = majorScheduleActualDate_14
        this.majorScheduleActualDate_15 = majorScheduleActualDate_15
        this.majorScheduleActualDate_16 = majorScheduleActualDate_16
        this.majorScheduleActualDate_17 = majorScheduleActualDate_17
        this.majorScheduleActualDate_18 = majorScheduleActualDate_18
        this.majorScheduleActualDate_19 = majorScheduleActualDate_19
        this.majorScheduleActualDate_20 = majorScheduleActualDate_20
        this.majorScheduleActualDate_21 = majorScheduleActualDate_21
        this.majorScheduleActualDate_22 = majorScheduleActualDate_22
        this.majorScheduleActualDate_23 = majorScheduleActualDate_23
        this.majorScheduleActualDate_24 = majorScheduleActualDate_24
        this.majorScheduleActualDate_25 = majorScheduleActualDate_25
        this.majorScheduleActualDate_26 = majorScheduleActualDate_26
        this.majorScheduleActualDate_27 = majorScheduleActualDate_27
        this.majorScheduleActualDate_28 = majorScheduleActualDate_28
        this.majorScheduleActualDate_29 = majorScheduleActualDate_29
        this.majorScheduleActualDate_30 = majorScheduleActualDate_30
        this.orderCodeSt = orderCodeSt
        this.additionalCodeSt = additionalCodeSt
        this.buildManagerCodeSt = buildManagerCodeSt
        this.designManagerCodeSt = designManagerCodeSt
        this.designSupervisorCodeSt = designSupervisorCodeSt
        this.applicationManagerCodeSt = applicationManagerCodeSt
        this.estimationManagerCodeSt = estimationManagerCodeSt
        this.developmentManagerCodeSt = developmentManagerCodeSt
        this.constructionSupervisorCodeSt = constructionSupervisorCodeSt
        this.constructionStaffCodeSt = constructionStaffCodeSt
        this.constructionManagerCodeSt = constructionManagerCodeSt
        this.chiefArchitectCodeSt = chiefArchitectCodeSt
        this.constructionSupervisionOfficeCode = constructionSupervisionOfficeCode
        this.mechanismFinancingDivision = mechanismFinancingDivision
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised PropertyBasicFileRecord
     */
    constructor(value: PropertyBasicFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgram = value.updateProgram
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            this.orderCode = value.orderCode
            this.additionalCode = value.additionalCode
            this.leaseTaxCellDivision = value.leaseTaxCellDivision
            this.constructionName = value.constructionName
            this.contractTypeOccurrence = value.contractTypeOccurrence
            this.buildingCount = value.buildingCount
            this.clientOtherConstructionDivision = value.clientOtherConstructionDivision
            this.clientOtherCompletionDate = value.clientOtherCompletionDate
            this.municipalityCode_1 = value.municipalityCode_1
            this.municipalityCode_2 = value.municipalityCode_2
            this.constructionLocation_1 = value.constructionLocation_1
            this.constructionLocation_2 = value.constructionLocation_2
            this.constructionLocation_3 = value.constructionLocation_3
            this.orderCompanyCode = value.orderCompanyCode
            this.orderOfficeCode = value.orderOfficeCode
            this.contractCompanyCode = value.contractCompanyCode
            this.contractOfficeCode = value.contractOfficeCode
            this.buildManagerCode = value.buildManagerCode
            this.designCompanyCode = value.designCompanyCode
            this.designOfficeCode = value.designOfficeCode
            this.designManagerCode = value.designManagerCode
            this.designSupervisorCode = value.designSupervisorCode
            this.applicationCompanyCode = value.applicationCompanyCode
            this.applicationOfficeCode = value.applicationOfficeCode
            this.applicationManagerCode = value.applicationManagerCode
            this.estimationCompanyCode = value.estimationCompanyCode
            this.estimationOfficeCode = value.estimationOfficeCode
            this.estimationManagerCode = value.estimationManagerCode
            this.developmentCompanyCode = value.developmentCompanyCode
            this.developmentOfficeCode = value.developmentOfficeCode
            this.developmentManagerCode = value.developmentManagerCode
            this.constructionCompanyCode = value.constructionCompanyCode
            this.constructionOfficeCode = value.constructionOfficeCode
            this.constructionSupervisorCode = value.constructionSupervisorCode
            this.constructionStaffCode = value.constructionStaffCode
            this.constructionManagerCode = value.constructionManagerCode
            this.chiefArchitectCode = value.chiefArchitectCode
            this.orderDate = value.orderDate
            this.contractPlannedDate = value.contractPlannedDate
            this.contractDate = value.contractDate
            this.lastChangeContractDate = value.lastChangeContractDate
            this.agreedCancellationDate = value.agreedCancellationDate
            this.completionReportOutputCount = value.completionReportOutputCount
            this.completionReportCreationDate = value.completionReportCreationDate
            this.completionReportCreationCompanyCode = value.completionReportCreationCompanyCode
            this.completionReportCreationOfficeCode = value.completionReportCreationOfficeCode
            this.completionReportCreationManagerCode = value.completionReportCreationManagerCode
            this.completionHandoverDate = value.completionHandoverDate
            this.completionApprovalDate = value.completionApprovalDate
            this.completionReportCollectionInputDate = value.completionReportCollectionInputDate
            this.completionReportCollectionCompanyCd = value.completionReportCollectionCompanyCd
            this.completionReportCollectionOfficeCode = value.completionReportCollectionOfficeCode
            this.completionReportCollectionManagerCd = value.completionReportCollectionManagerCd
            this.completionReportHeadOfficeReceiptDate = value.completionReportHeadOfficeReceiptDate
            this.completionReportReceiptCompanyCode = value.completionReportReceiptCompanyCode
            this.completionReportReceiptOfficeCode = value.completionReportReceiptOfficeCode
            this.completionReportReceiptManagerCode = value.completionReportReceiptManagerCode
            this.occupancyPlannedDate = value.occupancyPlannedDate
            this.publicFinancingDivision = value.publicFinancingDivision
            this.developmentApplicationDivision = value.developmentApplicationDivision
            this.reviewDivision = value.reviewDivision
            this.applicationProcessStateDivision = value.applicationProcessStateDivision
            this.applicationProcessHoldStartDate = value.applicationProcessHoldStartDate
            this.applicationProcessHoldDays = value.applicationProcessHoldDays
            this.constructionProcessStateDivision = value.constructionProcessStateDivision
            this.constructionProcessHoldStartDate = value.constructionProcessHoldStartDate
            this.constructionProcessHoldDays = value.constructionProcessHoldDays
            this.csOutputCount = value.csOutputCount
            this.processChangeStatus = value.processChangeStatus
            this.additionalConstructionDivision = value.additionalConstructionDivision
            this.applicationAcceptancePlannedDate_1 = value.applicationAcceptancePlannedDate_1
            this.applicationAcceptancePlannedDate_2 = value.applicationAcceptancePlannedDate_2
            this.applicationAcceptancePlannedDate_3 = value.applicationAcceptancePlannedDate_3
            this.applicationAcceptancePlannedDate_4 = value.applicationAcceptancePlannedDate_4
            this.applicationAcceptanceActualDate = value.applicationAcceptanceActualDate
            this.applicationPermissionPlannedDate_1 = value.applicationPermissionPlannedDate_1
            this.applicationPermissionPlannedDate_2 = value.applicationPermissionPlannedDate_2
            this.applicationPermissionPlannedDate_3 = value.applicationPermissionPlannedDate_3
            this.applicationPermissionPlannedDate_4 = value.applicationPermissionPlannedDate_4
            this.applicationPermissionActualDate = value.applicationPermissionActualDate
            this.constructionStartPlannedDate_1 = value.constructionStartPlannedDate_1
            this.constructionStartPlannedDate_2 = value.constructionStartPlannedDate_2
            this.constructionStartPlannedDate_3 = value.constructionStartPlannedDate_3
            this.constructionStartPlannedDate_4 = value.constructionStartPlannedDate_4
            this.constructionStartActualDate = value.constructionStartActualDate
            this.constructionCompletionPlannedDate_1 = value.constructionCompletionPlannedDate_1
            this.constructionCompletionPlannedDate_2 = value.constructionCompletionPlannedDate_2
            this.constructionCompletionPlannedDate_3 = value.constructionCompletionPlannedDate_3
            this.constructionCompletionPlannedDate_4 = value.constructionCompletionPlannedDate_4
            this.constructionCompletionActualDate = value.constructionCompletionActualDate
            this.majorScheduleCode_01 = value.majorScheduleCode_01
            this.majorScheduleCode_02 = value.majorScheduleCode_02
            this.majorScheduleCode_03 = value.majorScheduleCode_03
            this.majorScheduleCode_04 = value.majorScheduleCode_04
            this.majorScheduleCode_05 = value.majorScheduleCode_05
            this.majorScheduleCode_06 = value.majorScheduleCode_06
            this.majorScheduleCode_07 = value.majorScheduleCode_07
            this.majorScheduleCode_08 = value.majorScheduleCode_08
            this.majorScheduleCode_09 = value.majorScheduleCode_09
            this.majorScheduleCode_10 = value.majorScheduleCode_10
            this.majorScheduleCode_11 = value.majorScheduleCode_11
            this.majorScheduleCode_12 = value.majorScheduleCode_12
            this.majorScheduleCode_13 = value.majorScheduleCode_13
            this.majorScheduleCode_14 = value.majorScheduleCode_14
            this.majorScheduleCode_15 = value.majorScheduleCode_15
            this.majorScheduleCode_16 = value.majorScheduleCode_16
            this.majorScheduleCode_17 = value.majorScheduleCode_17
            this.majorScheduleCode_18 = value.majorScheduleCode_18
            this.majorScheduleCode_19 = value.majorScheduleCode_19
            this.majorScheduleCode_20 = value.majorScheduleCode_20
            this.majorScheduleCode_21 = value.majorScheduleCode_21
            this.majorScheduleCode_22 = value.majorScheduleCode_22
            this.majorScheduleCode_23 = value.majorScheduleCode_23
            this.majorScheduleCode_24 = value.majorScheduleCode_24
            this.majorScheduleCode_25 = value.majorScheduleCode_25
            this.majorScheduleCode_26 = value.majorScheduleCode_26
            this.majorScheduleCode_27 = value.majorScheduleCode_27
            this.majorScheduleCode_28 = value.majorScheduleCode_28
            this.majorScheduleCode_29 = value.majorScheduleCode_29
            this.majorScheduleCode_30 = value.majorScheduleCode_30
            this.majorSchedulePlannedDate_01 = value.majorSchedulePlannedDate_01
            this.majorSchedulePlannedDate_02 = value.majorSchedulePlannedDate_02
            this.majorSchedulePlannedDate_03 = value.majorSchedulePlannedDate_03
            this.majorSchedulePlannedDate_04 = value.majorSchedulePlannedDate_04
            this.majorSchedulePlannedDate_05 = value.majorSchedulePlannedDate_05
            this.majorSchedulePlannedDate_06 = value.majorSchedulePlannedDate_06
            this.majorSchedulePlannedDate_07 = value.majorSchedulePlannedDate_07
            this.majorSchedulePlannedDate_08 = value.majorSchedulePlannedDate_08
            this.majorSchedulePlannedDate_09 = value.majorSchedulePlannedDate_09
            this.majorSchedulePlannedDate_10 = value.majorSchedulePlannedDate_10
            this.majorSchedulePlannedDate_11 = value.majorSchedulePlannedDate_11
            this.majorSchedulePlannedDate_12 = value.majorSchedulePlannedDate_12
            this.majorSchedulePlannedDate_13 = value.majorSchedulePlannedDate_13
            this.majorSchedulePlannedDate_14 = value.majorSchedulePlannedDate_14
            this.majorSchedulePlannedDate_15 = value.majorSchedulePlannedDate_15
            this.majorSchedulePlannedDate_16 = value.majorSchedulePlannedDate_16
            this.majorSchedulePlannedDate_17 = value.majorSchedulePlannedDate_17
            this.majorSchedulePlannedDate_18 = value.majorSchedulePlannedDate_18
            this.majorSchedulePlannedDate_19 = value.majorSchedulePlannedDate_19
            this.majorSchedulePlannedDate_20 = value.majorSchedulePlannedDate_20
            this.majorSchedulePlannedDate_21 = value.majorSchedulePlannedDate_21
            this.majorSchedulePlannedDate_22 = value.majorSchedulePlannedDate_22
            this.majorSchedulePlannedDate_23 = value.majorSchedulePlannedDate_23
            this.majorSchedulePlannedDate_24 = value.majorSchedulePlannedDate_24
            this.majorSchedulePlannedDate_25 = value.majorSchedulePlannedDate_25
            this.majorSchedulePlannedDate_26 = value.majorSchedulePlannedDate_26
            this.majorSchedulePlannedDate_27 = value.majorSchedulePlannedDate_27
            this.majorSchedulePlannedDate_28 = value.majorSchedulePlannedDate_28
            this.majorSchedulePlannedDate_29 = value.majorSchedulePlannedDate_29
            this.majorSchedulePlannedDate_30 = value.majorSchedulePlannedDate_30
            this.majorScheduleActualDate_01 = value.majorScheduleActualDate_01
            this.majorScheduleActualDate_02 = value.majorScheduleActualDate_02
            this.majorScheduleActualDate_03 = value.majorScheduleActualDate_03
            this.majorScheduleActualDate_04 = value.majorScheduleActualDate_04
            this.majorScheduleActualDate_05 = value.majorScheduleActualDate_05
            this.majorScheduleActualDate_06 = value.majorScheduleActualDate_06
            this.majorScheduleActualDate_07 = value.majorScheduleActualDate_07
            this.majorScheduleActualDate_08 = value.majorScheduleActualDate_08
            this.majorScheduleActualDate_09 = value.majorScheduleActualDate_09
            this.majorScheduleActualDate_10 = value.majorScheduleActualDate_10
            this.majorScheduleActualDate_11 = value.majorScheduleActualDate_11
            this.majorScheduleActualDate_12 = value.majorScheduleActualDate_12
            this.majorScheduleActualDate_13 = value.majorScheduleActualDate_13
            this.majorScheduleActualDate_14 = value.majorScheduleActualDate_14
            this.majorScheduleActualDate_15 = value.majorScheduleActualDate_15
            this.majorScheduleActualDate_16 = value.majorScheduleActualDate_16
            this.majorScheduleActualDate_17 = value.majorScheduleActualDate_17
            this.majorScheduleActualDate_18 = value.majorScheduleActualDate_18
            this.majorScheduleActualDate_19 = value.majorScheduleActualDate_19
            this.majorScheduleActualDate_20 = value.majorScheduleActualDate_20
            this.majorScheduleActualDate_21 = value.majorScheduleActualDate_21
            this.majorScheduleActualDate_22 = value.majorScheduleActualDate_22
            this.majorScheduleActualDate_23 = value.majorScheduleActualDate_23
            this.majorScheduleActualDate_24 = value.majorScheduleActualDate_24
            this.majorScheduleActualDate_25 = value.majorScheduleActualDate_25
            this.majorScheduleActualDate_26 = value.majorScheduleActualDate_26
            this.majorScheduleActualDate_27 = value.majorScheduleActualDate_27
            this.majorScheduleActualDate_28 = value.majorScheduleActualDate_28
            this.majorScheduleActualDate_29 = value.majorScheduleActualDate_29
            this.majorScheduleActualDate_30 = value.majorScheduleActualDate_30
            this.orderCodeSt = value.orderCodeSt
            this.additionalCodeSt = value.additionalCodeSt
            this.buildManagerCodeSt = value.buildManagerCodeSt
            this.designManagerCodeSt = value.designManagerCodeSt
            this.designSupervisorCodeSt = value.designSupervisorCodeSt
            this.applicationManagerCodeSt = value.applicationManagerCodeSt
            this.estimationManagerCodeSt = value.estimationManagerCodeSt
            this.developmentManagerCodeSt = value.developmentManagerCodeSt
            this.constructionSupervisorCodeSt = value.constructionSupervisorCodeSt
            this.constructionStaffCodeSt = value.constructionStaffCodeSt
            this.constructionManagerCodeSt = value.constructionManagerCodeSt
            this.chiefArchitectCodeSt = value.chiefArchitectCodeSt
            this.constructionSupervisionOfficeCode = value.constructionSupervisionOfficeCode
            this.mechanismFinancingDivision = value.mechanismFinancingDivision
            resetChangedOnNotNull()
        }
    }
}
