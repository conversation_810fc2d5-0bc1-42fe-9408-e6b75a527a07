/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 仮押さえファイル 既存システム物理名: ERA03P
 */
@Suppress("UNCHECKED_CAST")
data class TemporaryReservationFilePojo(
    var buildingCd: String? = null,
    var roomCd: String? = null,
    var status: String? = null,
    var applicationScheduledDate: String? = null,
    var state: String? = null,
    var applicationScheduledPersonCd: String? = null,
    var customerRepCd: String? = null,
    var customerRepBranchCd: String? = null,
    var customerRepShozokuCd: String? = null,
    var comment: String? = null,
    var contractFormECode: String? = null,
    var listComment: String? = null,
    var registrationDate: String? = null,
    var linkCdRegistrationTime: String? = null,
    var otherCompanyFlag: String? = null,
    var otherCompanyMemberId: String? = null,
    var otherCompanyName: String? = null,
    var otherCompanyStoreName: String? = null,
    var otherCompanyRepName: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: TemporaryReservationFilePojo = other as TemporaryReservationFilePojo
        if (this.buildingCd == null) {
            if (o.buildingCd != null)
                return false
        }
        else if (this.buildingCd != o.buildingCd)
            return false
        if (this.roomCd == null) {
            if (o.roomCd != null)
                return false
        }
        else if (this.roomCd != o.roomCd)
            return false
        if (this.status == null) {
            if (o.status != null)
                return false
        }
        else if (this.status != o.status)
            return false
        if (this.applicationScheduledDate == null) {
            if (o.applicationScheduledDate != null)
                return false
        }
        else if (this.applicationScheduledDate != o.applicationScheduledDate)
            return false
        if (this.state == null) {
            if (o.state != null)
                return false
        }
        else if (this.state != o.state)
            return false
        if (this.applicationScheduledPersonCd == null) {
            if (o.applicationScheduledPersonCd != null)
                return false
        }
        else if (this.applicationScheduledPersonCd != o.applicationScheduledPersonCd)
            return false
        if (this.customerRepCd == null) {
            if (o.customerRepCd != null)
                return false
        }
        else if (this.customerRepCd != o.customerRepCd)
            return false
        if (this.customerRepBranchCd == null) {
            if (o.customerRepBranchCd != null)
                return false
        }
        else if (this.customerRepBranchCd != o.customerRepBranchCd)
            return false
        if (this.customerRepShozokuCd == null) {
            if (o.customerRepShozokuCd != null)
                return false
        }
        else if (this.customerRepShozokuCd != o.customerRepShozokuCd)
            return false
        if (this.comment == null) {
            if (o.comment != null)
                return false
        }
        else if (this.comment != o.comment)
            return false
        if (this.contractFormECode == null) {
            if (o.contractFormECode != null)
                return false
        }
        else if (this.contractFormECode != o.contractFormECode)
            return false
        if (this.listComment == null) {
            if (o.listComment != null)
                return false
        }
        else if (this.listComment != o.listComment)
            return false
        if (this.registrationDate == null) {
            if (o.registrationDate != null)
                return false
        }
        else if (this.registrationDate != o.registrationDate)
            return false
        if (this.linkCdRegistrationTime == null) {
            if (o.linkCdRegistrationTime != null)
                return false
        }
        else if (this.linkCdRegistrationTime != o.linkCdRegistrationTime)
            return false
        if (this.otherCompanyFlag == null) {
            if (o.otherCompanyFlag != null)
                return false
        }
        else if (this.otherCompanyFlag != o.otherCompanyFlag)
            return false
        if (this.otherCompanyMemberId == null) {
            if (o.otherCompanyMemberId != null)
                return false
        }
        else if (this.otherCompanyMemberId != o.otherCompanyMemberId)
            return false
        if (this.otherCompanyName == null) {
            if (o.otherCompanyName != null)
                return false
        }
        else if (this.otherCompanyName != o.otherCompanyName)
            return false
        if (this.otherCompanyStoreName == null) {
            if (o.otherCompanyStoreName != null)
                return false
        }
        else if (this.otherCompanyStoreName != o.otherCompanyStoreName)
            return false
        if (this.otherCompanyRepName == null) {
            if (o.otherCompanyRepName != null)
                return false
        }
        else if (this.otherCompanyRepName != o.otherCompanyRepName)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.buildingCd == null) 0 else this.buildingCd.hashCode())
        result = prime * result + (if (this.roomCd == null) 0 else this.roomCd.hashCode())
        result = prime * result + (if (this.status == null) 0 else this.status.hashCode())
        result = prime * result + (if (this.applicationScheduledDate == null) 0 else this.applicationScheduledDate.hashCode())
        result = prime * result + (if (this.state == null) 0 else this.state.hashCode())
        result = prime * result + (if (this.applicationScheduledPersonCd == null) 0 else this.applicationScheduledPersonCd.hashCode())
        result = prime * result + (if (this.customerRepCd == null) 0 else this.customerRepCd.hashCode())
        result = prime * result + (if (this.customerRepBranchCd == null) 0 else this.customerRepBranchCd.hashCode())
        result = prime * result + (if (this.customerRepShozokuCd == null) 0 else this.customerRepShozokuCd.hashCode())
        result = prime * result + (if (this.comment == null) 0 else this.comment.hashCode())
        result = prime * result + (if (this.contractFormECode == null) 0 else this.contractFormECode.hashCode())
        result = prime * result + (if (this.listComment == null) 0 else this.listComment.hashCode())
        result = prime * result + (if (this.registrationDate == null) 0 else this.registrationDate.hashCode())
        result = prime * result + (if (this.linkCdRegistrationTime == null) 0 else this.linkCdRegistrationTime.hashCode())
        result = prime * result + (if (this.otherCompanyFlag == null) 0 else this.otherCompanyFlag.hashCode())
        result = prime * result + (if (this.otherCompanyMemberId == null) 0 else this.otherCompanyMemberId.hashCode())
        result = prime * result + (if (this.otherCompanyName == null) 0 else this.otherCompanyName.hashCode())
        result = prime * result + (if (this.otherCompanyStoreName == null) 0 else this.otherCompanyStoreName.hashCode())
        result = prime * result + (if (this.otherCompanyRepName == null) 0 else this.otherCompanyRepName.hashCode())
        return result
    }
}
