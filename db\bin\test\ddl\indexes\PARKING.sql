-- INDEX: IDX_PARKING_01
CREATE INDEX IDX_PARKING_01 ON PARKING(CONSOLIDATED_BUILDING_CODE, CONSOLIDATED_PARKING_CODE)
TABLESPACE :TS_IDX;

-- INDEX: IDX_PARKING_02
CREATE INDEX IDX_PARKING_02 ON PARKING(PARKING_LOT_CODE)
TABLESPACE :TS_IDX;

-- INDEX: IDX_PARKING_03
CREATE INDEX IDX_PARKING_03 ON PARKING(BUILDING_CODE text_pattern_ops)
TABLESPACE :TS_IDX;

-- INDEX: IDX_PARKING_04
-- building_codeのLIKE検索 + 論理削除フラグの組み合わせ
CREATE INDEX IDX_PARKING_04 ON PARKING(BUILDING_CODE text_pattern_ops, LOGICAL_DELETE_FLAG)
TABLESPACE :TS_IDX
WHERE (LOGICAL_DELETE_FLAG = '0' OR CONSOLIDATED_BUILDING_CODE IS NOT NULL);

-- INDEX: IDX_PARKING_05
-- consolidated_building_codeのLIKE検索専用 + 論理削除フラグの組み合わせ
CREATE INDEX IDX_PARKING_05 ON PARKING(CONSOLIDATED_BUILDING_CODE text_pattern_ops, LOGICAL_DELETE_FLAG)
TABLESPACE :TS_IDX
WHERE (LOGICAL_DELETE_FLAG = '0' OR CONSOLIDATED_BUILDING_CODE IS NOT NULL);

-- INDEX: IDX_PARKING_06
-- 複合条件用の最適化インデックス（OR条件対応）
-- このインデックスは、building_codeとconsolidated_building_codeの両方のLIKE検索をサポート
CREATE INDEX IDX_PARKING_06 ON PARKING(BUILDING_CODE text_pattern_ops, CONSOLIDATED_BUILDING_CODE text_pattern_ops)
TABLESPACE :TS_IDX
WHERE (LOGICAL_DELETE_FLAG = '0' OR CONSOLIDATED_BUILDING_CODE IS NOT NULL);
