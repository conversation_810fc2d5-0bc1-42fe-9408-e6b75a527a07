package jp.ne.simplex.application.controller.client.property.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.*
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import java.time.format.DateTimeParseException

data class ClientTemporaryReservationRegisterOtherRequest(
    @JsonProperty("buildingCd")
    @field:Schema(description = "建物コード", example = "000130305")
    val buildingCd: String,

    @JsonProperty("roomCd")
    @field:Schema(description = "部屋コード", example = "01010")
    val roomCd: String,

    @JsonProperty("scheduledMoveInDate")
    @field:Schema(description = "入居予定日（yyyyMMddフォーマット）", example = "20241031")
    val scheduledMoveInDate: String,

    @JsonProperty("comment")
    @field:Schema(description = "コメント", example = "コメントが入ります")
    val comment: String?,

    @JsonProperty("otherCompanyCode")
    @field:Schema(description = "他社コード", example = "DKP943")
    val otherCompanyCode: String,

    @JsonProperty("otherCompanyName")
    @field:Schema(description = "他社会社名", example = "〇〇株式会社")
    val otherCompanyName: String,

    @JsonProperty("otherCompanyStoreName")
    @field:Schema(description = "他社店舗名", example = "〇〇支店")
    val otherCompanyStoreName: String,

    @JsonProperty("otherCompanyStaffName")
    @field:Schema(description = "他社担当者名", example = "田中太郎")
    val otherCompanyStaffName: String,

    @JsonProperty("registrationDate")
    @field:Schema(description = "登録日", example = "20241031")
    val registrationDate: String,

    @JsonProperty("registrationTime")
    @field:Schema(description = "登録時刻", example = "101952")
    val registrationTime: String,
) {

    // Service層の Interface に変換する
    fun toServiceInterface(): TemporaryReservation {
        try {
            return RegisterTemporaryReservation.OtherCompanyRegisterTemporaryReservation(
                id = Property.Id(
                    buildingCode = Building.Code.of(buildingCd),
                    roomCode = Room.Code.of(roomCd),
                ),
                otherCompanyInfo = TemporaryReservation.OtherCompanyInfo(
                    companyCode = otherCompanyCode,
                    companyName = otherCompanyName,
                    storeName = otherCompanyStoreName,
                    staffName = otherCompanyStaffName,
                ),
                scheduledMoveInDate = scheduledMoveInDate.yyyyMMdd(),
                comment = TemporaryReservation.Comment.of(comment),
                version = TemporaryReservation.Version.of(
                    updateDate = registrationDate,
                    updateTime = registrationTime,
                )
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        } catch (e: DateTimeParseException) {
            throw ClientValidationException(ErrorMessage.INVALID_DATE_FORMAT.format("yyyyMMdd"))
        }
    }
}
