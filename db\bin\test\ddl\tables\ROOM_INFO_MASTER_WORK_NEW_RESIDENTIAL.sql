-- TABLE: ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL(物件ボード用空き新築物件ワーク)

CREATE TABLE ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL(
     BRANCH_CODE                                  varchar(3)                    
,    STATUS                                       varchar(1)                    
,    DATE                                         numeric(8,0)                  
,    NEGOTIATION                                  varchar(8)                    
,    BUILDING_CODE                                varchar(9)                    
,    ROOM_CODE                                    varchar(5)                    
,    LANDLORD_NAME                                varchar(42)                   
,    BUILDING_NAME                                varchar(84)                   
,    LAYOUT_NAME                                  varchar(36)                   
,    LAYOUT                                       varchar(36)                   
,    ADDRESS_1                                    varchar(22)                   
,    ADDRESS_2                                    varchar(22)                   
,    RENT                                         numeric(5,0)                  
,    PARKING_FEE                                  numeric(5,0)                  
,    COMMON_FEE                                   numeric(4,0)                  
,    KEY_MONEY                                    numeric(2,0)                  
,    SECURITY_DEPOSIT                             numeric(2,0)                  
,    NEIGHBORHOOD_ASSOCIATION_FEE                 numeric(9,0)                  
,    REMARKS_1                                    varchar(50)                   
,    REMARKS_2                                    varchar(50)                   
,    ROCKY_CATEGORY                               varchar(1)                    
,    CATEGORY_A                                   varchar(1)                    
,    CATEGORY_B                                   varchar(1)                    
,    PREFECTURE_CODE                              varchar(2)                    
,    CITY_CODE                                    varchar(2)                    
,    TOWN_CODE                                    varchar(6)                    
,    PROPERTY_ADDRESS_KANA                        varchar(62)                   
,    TOWN_NAME_KANA                               varchar(30)                   
,    ROOM_NUMBER                                  varchar(4)                    
,    MOVE_IN_YEAR                                 numeric(4,0)                  
,    MOVE_IN_MONTH                                numeric(2,0)                  
,    SEASON                                       varchar(4)                    
,    TIME                                         numeric(4,0)                  
,    EXTRACTION_BRANCH_CODE                       varchar(6)                    
,    ADDRESS_3                                    varchar(42)                   
,    MOVE_IN_AVAILABLE_DATE                       numeric(8,0)                  
) TABLESPACE :TS_TBL;

COMMENT ON TABLE ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL IS '物件ボード用空き新築物件ワーク 既存システム物理名: EMU31P';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.BRANCH_CODE IS '支店CD 既存システム物理名: EMA01A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.STATUS IS '状況 既存システム物理名: EMA02A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.DATE IS '日付 既存システム物理名: EMA03S';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.NEGOTIATION IS '交渉 既存システム物理名: EMA04A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.BUILDING_CODE IS '建物CD 既存システム物理名: EMA05A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.ROOM_CODE IS '部屋CD 既存システム物理名: EMA06A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.LANDLORD_NAME IS '家主名称 既存システム物理名: EMA07A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.BUILDING_NAME IS '建物名称 既存システム物理名: EMA08A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.LAYOUT_NAME IS '間取り名 既存システム物理名: EMA09A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.LAYOUT IS '間取り 既存システム物理名: EMA10A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.ADDRESS_1 IS '所在地1 既存システム物理名: EMA11A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.ADDRESS_2 IS '所在地2 既存システム物理名: EMA12A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.RENT IS '家賃 既存システム物理名: EMA13S';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.PARKING_FEE IS '駐車料 既存システム物理名: EMA14S';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.COMMON_FEE IS '共益費 既存システム物理名: EMA15S';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.KEY_MONEY IS '礼金 既存システム物理名: EMA16S';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.SECURITY_DEPOSIT IS '保証金（敷金） 既存システム物理名: EMA17S';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.NEIGHBORHOOD_ASSOCIATION_FEE IS '町内会費 既存システム物理名: EMA18S';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.REMARKS_1 IS '備考1 既存システム物理名: EMA21A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.REMARKS_2 IS '備考2 既存システム物理名: EMA22A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.ROCKY_CATEGORY IS 'ロッキー区分 既存システム物理名: EMA23A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.CATEGORY_A IS '区分A 既存システム物理名: EMAKB1';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.CATEGORY_B IS '区分B 既存システム物理名: EMAKB2';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.PREFECTURE_CODE IS '都道府県CD 既存システム物理名: EMAAWC';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.CITY_CODE IS '市区郡CD 既存システム物理名: EMAAXC';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.TOWN_CODE IS '町村字通称CD 既存システム物理名: EMAAYC';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.PROPERTY_ADDRESS_KANA IS '物件住所かな 既存システム物理名: EMAKAN';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.TOWN_NAME_KANA IS '町村かな 既存システム物理名: EMAKAM';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.ROOM_NUMBER IS '部屋番号 既存システム物理名: EMA24A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.MOVE_IN_YEAR IS '入居年 既存システム物理名: EMA25S';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.MOVE_IN_MONTH IS '入居月 既存システム物理名: EMA19S';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.SEASON IS '旬 既存システム物理名: EMA20A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.TIME IS '時間 既存システム物理名: EMATIE';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.EXTRACTION_BRANCH_CODE IS '抽出用支店CD 既存システム物理名: EMA26A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.ADDRESS_3 IS '所在地3 既存システム物理名: EMA27A';
COMMENT ON COLUMN ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL.MOVE_IN_AVAILABLE_DATE IS '入居可能年月日 既存システム物理名: EMA28D';
