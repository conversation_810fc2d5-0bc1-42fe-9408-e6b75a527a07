package jp.ne.simplex.application.repository.external.dkportal.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.UpdatePropertyMaintenance
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalOperationName
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalRequest

class DKPortalUpdateAdFfRequest private constructor(
    @field:JsonProperty("property_type")
    val propertyType: Int,

    @field:JsonProperty("kentaku_building_code")
    val kentakuBuildingCode: String,

    @field:JsonProperty("kentaku_room_code")
    val kentakuRoomCode: String,

    @field:JsonProperty("ad_price")
    val adPrice: Int,

    @field:JsonProperty("ad_unit_type")
    val adUnitType: Int,

    @field:JsonProperty("ff_month")
    val ffMonth: Float,
) : DKPortalRequest {
    override fun getDKPortalOperationName(): DKPortalOperationName {
        return DKPortalOperationName.UPDATE_ADFF_AND_DEPOSIT_KEY_MONEY
    }

    companion object {

        fun of(updateAdFf: UpdatePropertyMaintenance.AdFf): DKPortalUpdateAdFfRequest {
            return DKPortalUpdateAdFfRequest(
                propertyType = when (updateAdFf.propertyType) {
                    Property.Type.RESIDENTIAL -> 1
                    Property.Type.COMMERCIAL -> 2
                },
                kentakuBuildingCode = updateAdFf.id.buildingCode.value,
                kentakuRoomCode = updateAdFf.id.roomCode.value,
                adPrice = updateAdFf.adFf.advertisementFee ?: 0,
                adUnitType = 3, // 2:ヶ月, 3:円
                ffMonth = updateAdFf.adFf.frontFreerentPeriod ?: 0F,
            )
        }
    }
}
