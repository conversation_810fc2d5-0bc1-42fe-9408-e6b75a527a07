package jp.ne.simplex.application.controller.external.dto

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import jp.ne.simplex.application.controller.external.parking.dto.ExternalReservationType
import jp.ne.simplex.application.controller.external.parking.dto.ExternalReservationType.Companion.toReservationType
import jp.ne.simplex.application.model.ParkingReservation
import jp.ne.simplex.shared.TestCase

class ExternalParkingReservationTest : FunSpec({

    context("駐車場予約種別(外部)と駐車場予約種別の紐付けの確認") {

        listOf(
            // @formatter:off
            TestCase(input = ExternalReservationType.APPLICATION, expected = ParkingReservation.Type.AUTO_APPLICATION),
            TestCase(input = ExternalReservationType.WORK, expected = ParkingReservation.Type.WORK),
            TestCase(input = ExternalReservationType.REPLACE, expected = ParkingReservation.Type.REPLACE),
            TestCase(input = ExternalReservationType.ONE_DAY, expected = ParkingReservation.Type.ONE_DAY),
            // @formatter:on
        ).forEach {
            test("駐車場予約種別(外部)=${it.input}の時、駐車場予約種別は${it.expected}であること") {
                it.input.toReservationType().shouldBe(it.expected)
            }
        }
    }

    context("駐車場予約種別と駐車場予約種別(外部)の紐付けの確認") {

        listOf(
            // @formatter:off
            TestCase(input = ParkingReservation.Type.AUTO_APPLICATION, expected = ExternalReservationType.APPLICATION),
            TestCase(input = ParkingReservation.Type.MANUAL_APPLICATION, expected = ExternalReservationType.APPLICATION),
            TestCase(input = ParkingReservation.Type.WORK, expected = ExternalReservationType.WORK),
            TestCase(input = ParkingReservation.Type.REPLACE, expected = ExternalReservationType.REPLACE),
            TestCase(input = ParkingReservation.Type.ONE_DAY, expected = ExternalReservationType.ONE_DAY),
            // @formatter:on
        ).forEach {
            test("駐車場予約種別=${it.input}の時、駐車場予約種別(外部)は${it.expected}であること") {
                ExternalReservationType.fromReservationType(it.input).shouldBe(it.expected)
            }
        }
    }
})
