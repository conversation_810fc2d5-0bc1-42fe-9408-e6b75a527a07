/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.GeographicSurveyAddressMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.GeographicSurveyAddressMasterPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 国土地理院住所マスタ 既存システム物理名: EDIADP
 */
@Suppress("UNCHECKED_CAST")
open class GeographicSurveyAddressMasterRecord private constructor() : UpdatableRecordImpl<GeographicSurveyAddressMasterRecord>(GeographicSurveyAddressMasterTable.GEOGRAPHIC_SURVEY_ADDRESS_MASTER) {

    open var geospatialAuthority_11digitCd: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var prefectureId: Byte?
        set(value): Unit = set(1, value)
        get(): Byte? = get(1) as Byte?

    open var cityWardTownId: Short?
        set(value): Unit = set(2, value)
        get(): Short? = get(2) as Short?

    open var jisCode: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var oazaAliasId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var chomeId: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var postalCode: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var prefecture: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var cityWardTown: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var oazaAlias: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var chome: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var prefectureKana: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var cityWardTownKana: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var oazaAliasKana: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var chomeKana: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised GeographicSurveyAddressMasterRecord
     */
    constructor(geospatialAuthority_11digitCd: String, prefectureId: Byte? = null, cityWardTownId: Short? = null, jisCode: Int? = null, oazaAliasId: String? = null, chomeId: String? = null, postalCode: String? = null, prefecture: String? = null, cityWardTown: String? = null, oazaAlias: String? = null, chome: String? = null, prefectureKana: String? = null, cityWardTownKana: String? = null, oazaAliasKana: String? = null, chomeKana: String? = null): this() {
        this.geospatialAuthority_11digitCd = geospatialAuthority_11digitCd
        this.prefectureId = prefectureId
        this.cityWardTownId = cityWardTownId
        this.jisCode = jisCode
        this.oazaAliasId = oazaAliasId
        this.chomeId = chomeId
        this.postalCode = postalCode
        this.prefecture = prefecture
        this.cityWardTown = cityWardTown
        this.oazaAlias = oazaAlias
        this.chome = chome
        this.prefectureKana = prefectureKana
        this.cityWardTownKana = cityWardTownKana
        this.oazaAliasKana = oazaAliasKana
        this.chomeKana = chomeKana
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised GeographicSurveyAddressMasterRecord
     */
    constructor(value: GeographicSurveyAddressMasterPojo?): this() {
        if (value != null) {
            this.geospatialAuthority_11digitCd = value.geospatialAuthority_11digitCd
            this.prefectureId = value.prefectureId
            this.cityWardTownId = value.cityWardTownId
            this.jisCode = value.jisCode
            this.oazaAliasId = value.oazaAliasId
            this.chomeId = value.chomeId
            this.postalCode = value.postalCode
            this.prefecture = value.prefecture
            this.cityWardTown = value.cityWardTown
            this.oazaAlias = value.oazaAlias
            this.chome = value.chome
            this.prefectureKana = value.prefectureKana
            this.cityWardTownKana = value.cityWardTownKana
            this.oazaAliasKana = value.oazaAliasKana
            this.chomeKana = value.chomeKana
            resetChangedOnNotNull()
        }
    }
}
