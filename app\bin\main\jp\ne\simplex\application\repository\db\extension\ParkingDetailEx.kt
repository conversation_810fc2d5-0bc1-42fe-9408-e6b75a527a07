package jp.ne.simplex.application.repository.db.extension

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.repository.db.ParkingDetailsRepositoryInterface
import jp.ne.simplex.application.repository.db.pojos.AggregateTenantContractPojo
import jp.ne.simplex.application.repository.db.pojos.ParkingDetailPojo
import jp.ne.simplex.application.repository.db.pojos.ParkingDetailPojo.Companion.LocalDate
import jp.ne.simplex.application.repository.db.pojos.PreviousParkingTenantContractPojo
import jp.ne.simplex.application.repository.db.pojos.PropertyTenantContractPojo
import java.time.LocalDate

/**
 * EBoardレガシーコードの移植クラス
 */
class ParkingDetailEx {

    companion object {

        fun List<ParkingDetailPojo>.applyEBoard(
            pdRepo: ParkingDetailsRepositoryInterface,
            isWelcomePark: <PERSON><PERSON><PERSON>,
            forClient: Boolean
        ): List<ParkingDetailPojo> {
            if (isWelcomePark) {
                return this.map {
                    toEBoardParkingPojo(
                        it,
                        pdRepo,
                        true,
                        forClient
                    )
                }
            } else {
                return toEBoardHandleConsolidatedRentEvaluation(this.map {
                    toEBoardParkingPojo(
                        it,
                        pdRepo,
                        false,
                        forClient
                    )
                }).toEBoardFilteredParkingList()
            }
        }

        //-- ここから下はEBoardレガシーコードからの移植 --//
        /**
         * いい物件の変換ロジック
         * jp/co/daito/eboard/Parking/EB800ParkingComBean.java
         */
        private fun toEBoardParkingPojo(
            pojo: ParkingDetailPojo,
            pdRepo: ParkingDetailsRepositoryInterface,
            isWelcomePark: Boolean,
            forClient: Boolean
        ): ParkingDetailPojo {
            var ret = pojo
            if (ret.parkingLotCode.isNullOrEmpty()) return ret // 建物情報のみの場合はreturn
            val tenantContractNumber = pojo.tenantContractNumber
            if (!tenantContractNumber.isNullOrBlank()) {
                ret = handleTenantContract(ret, tenantContractNumber, pdRepo)
                ret = handlePropertyTenantContract(ret, pdRepo, isWelcomePark, forClient)
            }
            // 駐車場区画ステータスの導出
            ret = ret.computeParkingStatusDivision(isWelcomePark)
            return ret
        }

        /**
         * テナント契約がある場合の特殊処理
         */
        private fun handleTenantContract(
            pojo: ParkingDetailPojo,
            tenantContractNumber: String,
            pdRepo: ParkingDetailsRepositoryInterface
        ): ParkingDetailPojo {
            // 状態、論理削除サイン、合算先契約番号を取得
            var status = pojo.currentStateDivision!!
            val saku = pojo.logicalDeleteSign?.toString() ?: "0"
            val gassan = pojo.aggregateContractNumber ?: ""

            // 修正状態、テナント契約番号、入居日、退居日を取得
            var updStatus = pojo.modificationStateDivision!!
            var nyukyoDate = pojo.moveInScheduledDate?.toString()?.LocalDate()
            var taikyoDate = pojo.moveOutDate?.toString()?.LocalDate()
            val heyaCd = pojo.roomCode

            var ret = pojo
            // 入居前合算の処理
            if (shouldHandlePreApplicationAggregate(
                    saku,
                    gassan,
                    updStatus,
                    heyaCd,
                    status,
                    taikyoDate
                )
            ) {
                ret = handlePreApplicationAggregate(ret, gassan, pdRepo)
                // 一時変数の再セット
                status = ret.currentStateDivision!!
                updStatus = ret.modificationStateDivision!!
                nyukyoDate = ret.moveInScheduledDate?.toString()?.LocalDate()
                taikyoDate = ret.moveOutDate?.toString()?.LocalDate()
            }

            // 最新テ契が有効情報でない時、前テ契を取得
            if (shouldGetPreviousContract(updStatus, status, taikyoDate, nyukyoDate)) {
                ret = handlePreviousContractRetrieval(ret, tenantContractNumber, pdRepo)
            }

            return ret
        }

        // 入居前テ契約合算処理判定
        private fun shouldHandlePreApplicationAggregate(
            saku: String,
            gassan: String,
            updStatus: String,
            heyaCd: String?,
            status: String,
            taikyoDate: LocalDate?
        ): Boolean {
            return (saku == "1" && gassan.isNotEmpty() && updStatus == "00") ||
                    (gassan.isNotEmpty() && heyaCd.isNullOrEmpty() && 50 < status.toInt() && taikyoDate == null)
        }

        // 入居前テ契約合算の処理
        private fun handlePreApplicationAggregate(
            pojo: ParkingDetailPojo,
            gassan: String,
            pdRepo: ParkingDetailsRepositoryInterface
        ): ParkingDetailPojo {
            // 取得した情報に入れ替える
            pdRepo.findAggregateTenantContract(gassan).let {
                if (it != null) return pojo.applyAggregateTenantContract(it)
            }
            return pojo
        }

        // 前テ契取得判定(無効なテ契であるかどうか)
        private fun shouldGetPreviousContract(
            updStatus: String,
            status: String,
            taikyoDate: LocalDate?,
            nyukyoDate: LocalDate?
        ): Boolean {
            return updStatus == "00" || status == "27" || status == "90" || (taikyoDate != null && nyukyoDate != null && taikyoDate < nyukyoDate)
        }

        // 前テ契の処理を行う
        private fun handlePreviousContractRetrieval(
            pojo: ParkingDetailPojo,
            tenantContractNumber: String,
            pdRepo: ParkingDetailsRepositoryInterface
        ): ParkingDetailPojo {
            var ret = pojo
            var beFlg: String? = null // 駐車場区画ステータスの導出に必要
            var hasPrevContract = false // 有効な前テ契があるかどうかのフラグ
            var prevTenantContractNumber = tenantContractNumber
            for (iBack in 0 until 10) {
                beFlg = null // フラグ初期化
                var beSubFlg = "" // サブフラグ初期化
                if (ret.modificationStateDivision == "00") {
                    beSubFlg = "1" // 修正状態が00の時はサブフラグを1に設定
                }

                // 受注コード、駐車場コードを取得
                val orderCode = Building.Code.of(ret.buildingCode).getOrderCode()
                val parkingCode = ret.parkingLotCode!!
                val prevContract = pdRepo.findPreviousTenantContract(
                    orderCode,
                    parkingCode,
                    prevTenantContractNumber
                ) // 前回より小さいテナント契約を取得
                // 有効な前テ契があるかどうかのフラグを毎回セット
                hasPrevContract = prevContract != null
                // １つ前テ契が取得出来た時
                if (prevContract != null) {
                    // 状態、論理削除サイン、合算先契約番号を取得
                    var status = prevContract.currentStateDivision!!
                    val saku = prevContract.logicalDeleteSign?.toString() ?: "0"
                    val gassan = prevContract.aggregateContractNumber ?: ""
                    var updStatus = prevContract.modificationStateDivision!!
                    var nyukyoDate = prevContract.moveInScheduledDate?.toString()?.LocalDate()
                    var taikyoDate = prevContract.moveOutDate?.toString()?.LocalDate()
                    val heyaCd = prevContract.roomCode
                    prevTenantContractNumber = prevContract.tenantContractNumber!!

                    // 入居前合算処理判定
                    if (shouldHandlePreApplicationAggregate(
                            saku,
                            gassan,
                            updStatus,
                            heyaCd,
                            status,
                            taikyoDate
                        )
                    ) {
                        pdRepo.findAggregateTenantContract(gassan).let { combContract ->
                            if (combContract != null) {
                                // 一時変数の再セット
                                status = combContract.currentStateDivision!!
                                updStatus = combContract.modificationStateDivision!!
                                nyukyoDate = combContract.moveInScheduledDate?.toString()?.LocalDate()
                                taikyoDate = combContract.moveOutDate?.toString()?.LocalDate()
                                handleApplyPreviousTenantContract(
                                    status,
                                    ret,
                                    prevContract,
                                    combContract,
                                    beSubFlg
                                ).let {
                                    if (it != null) {
                                        ret = it.pojo
                                        beFlg = it.beFlg
                                    }
                                }
                            } else {
                                // 合算先が取得できなかった場合は、前テ契の情報は無視して何もしない
                            }
                        }
                    } else {
                        handleApplyPreviousTenantContract(
                            status,
                            ret,
                            prevContract,
                            null,
                            beSubFlg
                        ).let {
                            if (it != null) {
                                ret = it.pojo
                                beFlg = it.beFlg
                            }
                        }
                    }
                    // １つ前テ契が有効情報の時は抜ける
                    if (!shouldGetPreviousContract(updStatus, status, taikyoDate, nyukyoDate)) {
                        break
                    }
                } else {
                    break // １つ前テ契が取得出来ない時は抜ける
                }
            }
            // 有効情報がなければ空き部屋のフラグをたてる
            if (!hasPrevContract || beFlg == null) beFlg = "2"
            return ret.apply {
                this.beFlg = beFlg // 空き部屋フラグをセット
            }
        }

        data class ContractWithBeFlg(
            val pojo: ParkingDetailPojo,
            val beFlg: String,
        )

        private fun handleApplyPreviousTenantContract(
            status: String,
            pojo: ParkingDetailPojo,
            prevContract: PreviousParkingTenantContractPojo,
            combContract: AggregateTenantContractPojo?,
            beSubFlg: String
        ): ContractWithBeFlg? {
            // 残集承認以降、退居精算前のデータの場合
            return if (status.isNotBlank() && status.toInt() >= 35 && status.toInt() < 50) {
                var applied = pojo.applyPreviousTenantContract(prevContract)
                if (combContract != null) applied =
                    applied.applyAggregateTenantContract(combContract) // 合算先テ契がある場合は、合算先テ契の情報をセット
                ContractWithBeFlg(
                    pojo = applied,
                    beFlg = if (beSubFlg == "1") "3" else "4"
                )
            } else null
        }

        // 部屋テナント契約の建物、部屋コードと付随情報を取得する
        private fun handlePropertyTenantContract(
            pojo: ParkingDetailPojo,
            pdRepo: ParkingDetailsRepositoryInterface,
            isWelcomePark: Boolean,
            forClient: Boolean
        ): ParkingDetailPojo {
            if (pojo.tenantContractNumber.isNullOrBlank() || isWelcomePark) return pojo // WelcomeParkの場合は不要情報のため処理しない
            val propertyContractPojo = if (forClient) {
                // クライアント用にはテナント契約一括残集ファイルで処理する
                pdRepo.findBulkTenantContract(pojo.tenantContractNumber)
            } else {
                // EBoard用には駐車場付加情報DBで処理する
                PropertyTenantContractPojo(
                    tenantBuildingCode = pojo.roomBuildingCode,
                    tenantRoomCode = pojo.roomRoomCode,
                    tenantRoomNumber = pojo.roomRoomNumber,
                )
            }
            return if (propertyContractPojo != null) {
                pojo.apply {
                    propertyTenant = propertyContractPojo
                }
            } else {
                pojo // 変更がない場合は空値でそのまま返す
            }
        }

        // 建物合算先があり合算元の論理削除サインが「1」の場合、合算先から合算元の礼金、敷金、駐車料を減算する
        private fun toEBoardHandleConsolidatedRentEvaluation(pojos: List<ParkingDetailPojo>): List<ParkingDetailPojo> {
            val ret = pojos.toMutableList()
            for (i in ret.indices) {
                var pojo = ret[i] // 合算先
                for (j in ret.indices) {
                    var pojoComb = ret[j] // 合算元
                    if (pojo.buildingCode == pojoComb.consolidatedBuildingCode &&
                        pojo.parkingLotCode == pojoComb.consolidatedParkingCode && pojoComb.logicalDeleteFlag == 1
                    ) {
                        // 礼金
                        pojo = pojo.copy(
                            keyMoneyAmount = subtractFees(
                                pojo.keyMoneyAmount,
                                pojoComb.keyMoneyAmount
                            )
                        )
                        // 敷金
                        pojo = pojo.copy(
                            depositAmount = subtractFees(
                                pojo.depositAmount,
                                pojoComb.depositAmount
                            )
                        )
                        // 駐車料
                        pojo = pojo.copy(
                            parkingFee = subtractFees(
                                pojo.parkingFee,
                                pojoComb.parkingFee
                            )
                        )
                        // 賃料税込サインが「1」の場合、合算先も「1」にする
                        if (pojoComb.parkingFeeInTax == "1") {
                            pojo = pojo.copy(parkingFeeInTax = "1")
                        }
                        // 礼金税込サインが「1」の場合、合算先も「1」にする
                        if (pojoComb.keyMoneyInTax == "1") {
                            pojo = pojo.copy(keyMoneyInTax = "1")
                        }
                        // 合算元を更新
                        pojoComb = pojoComb.applyConsolidatedOriginal(pojo)
                        // それぞれのpojoを置換する
                        ret[i] = pojo // 合算先を更新
                        ret[j] = pojoComb // 合算元を更新
                    }
                }
            }
            return ret
        }

        private fun subtractFees(fee: Int?, feeComb: Int?): Int? {
            return if (fee != null && feeComb != null) fee - feeComb else fee
        }

        // EBoardで非表示としている区画を除外する
        private fun List<ParkingDetailPojo>.toEBoardFilteredParkingList(
        ): List<ParkingDetailPojo> {
            var ret = this
            // 付替元建物コードが入っている場合は、駐車場コードを"***"にする
            ret = ret.map {
                if (it.transferredBuildingCode.isNullOrEmpty()) it else it.copy(parkingLotCode = "***")
            }
            // 駐車場コードが"***"の場合は除外する。※棟が削除されている場合はSQLで弾いているため、そもそも取得されていないはず
            return ret.filter { it.parkingLotCode != "***" }
        }

    }
}

private fun ParkingDetailPojo.applyAggregateTenantContract(pojo: AggregateTenantContractPojo): ParkingDetailPojo {
    return copy(
        tenantName = pojo.tenantName,
        moveInScheduledDate = pojo.moveInScheduledDate,
        tenantNameKanji = pojo.tenantNameKanji,
        contractExpiryDate = pojo.contractExpiryDate,
        contractEffectiveEndDate = pojo.contractEffectiveEndDate,
        currentStateDivision = pojo.currentStateDivision,
        modificationStateDivision = pojo.modificationStateDivision,
        moveInStartProcessedSign = pojo.moveInStartProcessedSign,
        moveOutDate = pojo.moveOutDate,
        cancellationSign = pojo.cancellationSign,
        vacateScheduledDate = pojo.vacateScheduledDate,
        vacateNoticeDate = pojo.vacateNoticeDate,
    )
}

private fun ParkingDetailPojo.applyPreviousTenantContract(pojo: PreviousParkingTenantContractPojo): ParkingDetailPojo {
    return copy(
        tenantContractNumber = pojo.tenantContractNumber,
        landTransportName = pojo.landTransportName,
        type = pojo.type,
        businessCategory = pojo.businessCategory,
        leftNumber = pojo.leftNumber,
        rightNumber = pojo.rightNumber,
        manufacturerDivision = pojo.manufacturerDivision,
        carModelName = pojo.carModelName,
        lightVehicleSign = pojo.lightVehicleSign,
        tenantName = pojo.tenantName,
        moveInScheduledDate = pojo.moveInScheduledDate,
        tenantNameKanji = pojo.tenantNameKanji,
        contractExpiryDate = pojo.contractExpiryDate,
        contractEffectiveEndDate = pojo.contractEffectiveEndDate,
        currentStateDivision = pojo.currentStateDivision,
        modificationStateDivision = pojo.modificationStateDivision,
        moveInStartProcessedSign = pojo.moveInStartProcessedSign,
        moveOutDate = pojo.moveOutDate,
        cancellationSign = pojo.cancellationSign,
        tandemSign = pojo.tandemSign,
        parkingCertIssueSign = pojo.parkingCertIssueSign,
        parkingCertComment = pojo.parkingCertComment,
        logicalDeleteSign = pojo.logicalDeleteSign,
        vacateScheduledDate = pojo.vacateScheduledDate,
        vacateNoticeDate = pojo.vacateNoticeDate,
        aggregateContractNumber = pojo.aggregateContractNumber,
    )
}

private fun ParkingDetailPojo.applyConsolidatedOriginal(pojo: ParkingDetailPojo): ParkingDetailPojo {
    copy(
        parkingStatusDivision = pojo.parkingStatusDivision, //導出済みの駐車場区画ステータス
        tenantName = pojo.tenantName,
        moveInScheduledDate = pojo.moveInScheduledDate,
        tenantNameKanji = pojo.tenantNameKanji,
        contractExpiryDate = pojo.contractExpiryDate,
        contractEffectiveEndDate = pojo.contractEffectiveEndDate,
        expectedMoveOutDate = pojo.expectedMoveOutDate,  //導出済みの退去予定日
        tenantContractNumber = this.orgParkingVehiclePojo?.tenantContractNumber,
        landTransportName = this.orgParkingVehiclePojo?.landTransportName,
        type = this.orgParkingVehiclePojo?.type,
        businessCategory = this.orgParkingVehiclePojo?.businessCategory,
        leftNumber = this.orgParkingVehiclePojo?.leftNumber,
        rightNumber = this.orgParkingVehiclePojo?.rightNumber,
        manufacturerDivision = this.orgParkingVehiclePojo?.manufacturerDivision,
        carModelName = this.orgParkingVehiclePojo?.carModelName,
    ).let {
        if (!pojo.transferredBuildingCode.isNullOrEmpty()) {
            return it.copy(
                tenantContractNumber = pojo.tenantContractNumber
            ).apply {
                propertyTenant = pojo.propertyTenant // 付替元のテナント情報をセット
            }
        }
        return it
    }
}
