\i tables/PASSWORD_MASTER.sql
\i tables/AFFILIATION_MASTER.sql
\i tables/MAIL_FORWARD_MASTER.sql
\i tables/TEMPORARY_RESERVATION_FILE.sql
\i tables/BRANCH_FILE.sql
\i tables/BUILDING_LOCATION_INFO.sql
\i tables/TENANT_SALES_MAINTENANCE_INFO.sql
\i tables/PROPERTY_MAINTENANCE_INFO.sql
\i tables/BUILDING_INFO_MASTER.sql
\i tables/ROOM_INFO_MASTER.sql
\i tables/BUILDING_MASTER.sql
\i tables/CUSTOMER.sql
\i tables/TENANT_CONTRACT.sql
\i tables/TENANT.sql
\i tables/LATEST_RENT_EVALUATION.sql
\i tables/ADDRESS_MASTER.sql
\i tables/TENANT_CONTRACT_BULK_COLLECTION_FILE.sql
\i tables/PARKING.sql
\i tables/BULK_LEASE_PARKING.sql
\i tables/PARKING_VEHICLE_INFO_FILE.sql
\i tables/PARKING_SPECIAL_NOTES_FILE.sql
\i tables/PARKING_RESERVATION_FILE.sql
\i tables/BUILDING_IMAGE.sql
\i tables/ROOM_IMAGE.sql
\i tables/PRODUCT_NAME_MASTER.sql
\i tables/PROPERTY_BASIC_FILE.sql
\i tables/RENT_REVIEW_CENTER_BRANCH.sql
\i tables/EQUIPMENT_MASTER.sql
\i tables/LATEST_ROOM_EQUIPMENT_FILE.sql
\i tables/PARKING_IMAGE_REGISTRATION.sql
\i tables/VACANT_HOUSE_HP.sql
\i tables/CATCH_COPY_REGISTRATION.sql
\i tables/RECOMMEND_COMMENT_REGISTRATION.sql
\i tables/LAYOUT_IMAGE_REGISTRATION.sql
\i tables/NG_WORD_COMMENT_REGISTRATION_RESULT.sql
\i tables/NG_WORD_CATCH_REGISTRATION_RESULT.sql
\i tables/PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.sql
\i tables/PROPERTY_DETAIL_FILE_EXISTING_COMMERCIAL.sql
\i tables/PROPERTY_DETAIL_FILE_NEW_RESIDENTIAL.sql
\i tables/PROPERTY_DETAIL_FILE_NEW_COMMERCIAL.sql
\i tables/PANORAMA_ASSOCIATED_FILE.sql
\i tables/PORTAL_MEMBER_ID_LINK_MASTER.sql
\i tables/IMAGE_COMMENT_MASTER.sql
\i tables/IMAGE_COPY_SUPPORT_FILE.sql
\i tables/COMMON_CODE.sql
\i tables/DAITO_BUILDING_MANAGEMENT_TABLE.sql
\i tables/AGENT.sql
\i tables/ROOM_MASTER.sql
\i tables/EMAIL_ADDRESS_MASTER.sql
\i tables/EMPLOYEE_MASTER.sql
\i tables/PERMANENT_SIGNBOARD_INFO.sql
\i tables/ACCIDENT_PROPERTY_MANAGEMENT.sql
\i tables/SITE_COMMENT_REGISTRATION.sql
\i tables/WAON_POINT_AUTHORITY_MASTER.sql
\i tables/POST_KEY.sql
\i tables/AUTO_LOCK_NO.sql
\i tables/NEW_PROPERTY_NEAREST_STATION.sql
\i tables/CONSUMPTION_TAX_RATE_MASTER.sql
\i tables/OFFICE_MASTER.sql
\i tables/BUILDING_TYPE_MASTER.sql
\i tables/CALENDAR_MASTER.sql
\i tables/HR_CATEGORY_TABLE_B.sql
\i tables/MANAGED_PROPERTY_DATA_IBM_LAYOUT.sql
\i tables/SURROUNDING_INFO_FILE.sql
\i tables/LEASING_STORE_TABLE.sql
\i tables/BUILDING_STORE_MASTER.sql
\i tables/GEOGRAPHIC_SURVEY_ADDRESS_MASTER.sql
\i tables/BUILDING_MAINTENANCE_INFO.sql
\i tables/DAITO_PARTNERS_OFFICE_CD_TABLE.sql
\i tables/BUILDING_11_DIGIT_ADDRESS.sql
\i tables/TEMPORARY_CONTRACT.sql
\i tables/CONTRACT.sql
\i tables/THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.sql
\i tables/CONTRACT_FILE.sql
\i tables/DAITO_BULK_LEASE_CONTRACT_DETAILS.sql
\i tables/MNG_ONLY_UPD_CONT_FEE_APPROVAL_BUILDING.sql
\i tables/RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.sql
\i tables/LATEST_PRODUCT_MASTER.sql
\i tables/SPECIAL_BUILDING_MASTER.sql
\i tables/BUILDING_INFO_FILE.sql
\i tables/CORE_PRODUCT_MASTER.sql
\i tables/ENERGY_SAVING_LABEL_INFO_FILE.sql
\i tables/MNG_ONLY_24H_SUPPORT_BUILDING.sql
\i tables/CORE_PRODUCT_LINK_MASTER.sql
\i tables/PRODUCT_MASTER.sql
\i tables/VACANT_PARKING_LIST.sql
\i tables/CITY_GAS_RATE_ADDRESS_FILE.sql
\i tables/FIXED_ITEM_FILE.sql
\i tables/GASPAR_BUILDING_INFO_MASTER.sql
\i tables/UTILITY_MASTER.sql
\i tables/BUILDING_BASIC_INFO_FILE.sql
\i tables/UTILITY_DEPARTMENT_MASTER.sql
\i tables/UTILITY_COMPANY_MASTER.sql
\i tables/BUILDING_SPECIFIC_UTILITY_INFO.sql
\i tables/PARKING_HOURLY_RENTAL_APPROVAL.sql
\i tables/FIXED_TERM_RENTAL_INFO_MASTER.sql
\i tables/OFF_SITE_PARKING.sql
\i tables/DAIKEN_OFFICE_MASTER.sql
\i tables/UTILITY_GUIDE_MASTER.sql
\i tables/ELECTRICITY_BUSINESS_MASTER.sql
\i tables/BELS_APPLICATION_RESULT_PROGRESS_FILE.sql
\i tables/KT_ALL_BRANCH.sql
\i tables/SITE_PROPERTY_KENTAKU_FOR_P.sql
truncate table IMPORT_DB_HISTORY;
\i tables/PARKING_LAYOUT_PIN.sql
\i tables/PARKING_RESERVATION.sql
truncate table PARKING_CONTRACT_POSSIBILITY;
\i tables/BUILDING_MEMO.sql
\i tables/PARKING_LOT_MEMO.sql
\i tables/PARKING_MEMO.sql
\i tables/PROPERTY_MEMO.sql
\i tables/PARKING_ADDITIONAL_INFO_MASTER.sql
\i tables/PARKING_INFO_MASTER.sql
truncate table PARKING_ENABLE;
\i tables/EXCLUSIVE_PROPERTY.sql
\i tables/EXCLUSIVE_PROPERTY_E_CODE.sql
truncate table BATCH_EXECUTE_HISTORY;
truncate table ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL;
truncate table ROOM_INFO_MASTER_WORK_EXISTING_RESIDENTIAL;
truncate table ROOM_INFO_MASTER_WORK_COMMERCIAL;
