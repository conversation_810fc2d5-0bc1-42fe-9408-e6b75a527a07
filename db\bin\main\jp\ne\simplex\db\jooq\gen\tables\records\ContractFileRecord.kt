/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.ContractFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ContractFilePojo

import org.jooq.impl.TableRecordImpl


/**
 * 請負契約ファイル 既存システム物理名: AEUKYP
 */
@Suppress("UNCHECKED_CAST")
open class ContractFileRecord private constructor() : TableRecordImpl<ContractFileRecord>(ContractFileTable.CONTRACT_FILE) {

    open var contractCd: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var contractAdditionalCd: Short?
        set(value): Unit = set(1, value)
        get(): Short? = get(1) as Short?

    open var changeHistoryNo: Short?
        set(value): Unit = set(2, value)
        get(): Short? = get(2) as Short?

    open var recordDeleteFlag: Byte?
        set(value): Unit = set(3, value)
        get(): Byte? = get(3) as Byte?

    open var creationDate: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var creationTime: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var updateDate: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var updateTime: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var updateProgram: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var updateEmployeeNo: Int?
        set(value): Unit = set(9, value)
        get(): Int? = get(9) as Int?

    open var contractStatusCategory: Byte?
        set(value): Unit = set(10, value)
        get(): Byte? = get(10) as Byte?

    open var contractContentReportFlag: Byte?
        set(value): Unit = set(11, value)
        get(): Byte? = get(11) as Byte?

    open var transactionCategory: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var ordererCd: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var contractDateChangeDate: Int?
        set(value): Unit = set(14, value)
        get(): Int? = get(14) as Int?

    open var originalContractDate: Int?
        set(value): Unit = set(15, value)
        get(): Int? = get(15) as Int?

    open var agreedTerminationDate: Int?
        set(value): Unit = set(16, value)
        get(): Int? = get(16) as Int?

    open var contractJudgmentCategory: Byte?
        set(value): Unit = set(17, value)
        get(): Byte? = get(17) as Byte?

    open var contractJudgmentDate: Int?
        set(value): Unit = set(18, value)
        get(): Int? = get(18) as Int?

    open var contractJudgmentDeptCategory: Byte?
        set(value): Unit = set(19, value)
        get(): Byte? = get(19) as Byte?

    open var constructionBranch: Short?
        set(value): Unit = set(20, value)
        get(): Short? = get(20) as Short?

    open var contractBranch: Short?
        set(value): Unit = set(21, value)
        get(): Short? = get(21) as Short?

    open var affiliatedSection: Short?
        set(value): Unit = set(22, value)
        get(): Short? = get(22) as Short?

    open var contractResponsiblePerson: Int?
        set(value): Unit = set(23, value)
        get(): Int? = get(23) as Int?

    open var contractSectionManager: Int?
        set(value): Unit = set(24, value)
        get(): Int? = get(24) as Int?

    open var contractCompanion: Int?
        set(value): Unit = set(25, value)
        get(): Int? = get(25) as Int?

    open var businessProjectionNo: Short?
        set(value): Unit = set(26, value)
        get(): Short? = get(26) as Short?

    open var repeatCategory: Byte?
        set(value): Unit = set(27, value)
        get(): Byte? = get(27) as Byte?

    open var contractCategory: Byte?
        set(value): Unit = set(28, value)
        get(): Byte? = get(28) as Byte?

    open var maintenanceCategory: Byte?
        set(value): Unit = set(29, value)
        get(): Byte? = get(29) as Byte?

    open var contractPartnerCategory: Byte?
        set(value): Unit = set(30, value)
        get(): Byte? = get(30) as Byte?

    open var activityArea: Byte?
        set(value): Unit = set(31, value)
        get(): Byte? = get(31) as Byte?

    open var reserve1: Int?
        set(value): Unit = set(32, value)
        get(): Int? = get(32) as Int?

    open var otherCompanyFlag: Byte?
        set(value): Unit = set(33, value)
        get(): Byte? = get(33) as Byte?

    open var additionalConstructionCategory: Byte?
        set(value): Unit = set(34, value)
        get(): Byte? = get(34) as Byte?

    open var landCd: Int?
        set(value): Unit = set(35, value)
        get(): Int? = get(35) as Int?

    open var constructionName: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var constructionStartTimingCategory: Byte?
        set(value): Unit = set(37, value)
        get(): Byte? = get(37) as Byte?

    open var constructionPeriodStartTiming: Short?
        set(value): Unit = set(38, value)
        get(): Short? = get(38) as Short?

    open var constructionPeriodCompletionTiming: Short?
        set(value): Unit = set(39, value)
        get(): Short? = get(39) as Short?

    open var landAreaM: BigDecimal?
        set(value): Unit = set(40, value)
        get(): BigDecimal? = get(40) as BigDecimal?

    open var landAreaTsubo: BigDecimal?
        set(value): Unit = set(41, value)
        get(): BigDecimal? = get(41) as BigDecimal?

    open var constructionFloorAreaM: BigDecimal?
        set(value): Unit = set(42, value)
        get(): BigDecimal? = get(42) as BigDecimal?

    open var constructionFloorAreaTsubo: BigDecimal?
        set(value): Unit = set(43, value)
        get(): BigDecimal? = get(43) as BigDecimal?

    open var warehouseFactoryAreaM: BigDecimal?
        set(value): Unit = set(44, value)
        get(): BigDecimal? = get(44) as BigDecimal?

    open var warehouseFactoryAreaTsubo: BigDecimal?
        set(value): Unit = set(45, value)
        get(): BigDecimal? = get(45) as BigDecimal?

    open var buildingTypeSt: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var numberOfBuildings: Byte?
        set(value): Unit = set(47, value)
        get(): Byte? = get(47) as Byte?

    open var numberOfRooms: Short?
        set(value): Unit = set(48, value)
        get(): Short? = get(48) as Short?

    open var fireProtectionCategory: Byte?
        set(value): Unit = set(49, value)
        get(): Byte? = get(49) as Byte?

    open var landUseCategory: Byte?
        set(value): Unit = set(50, value)
        get(): Byte? = get(50) as Byte?

    open var leaseCategory: Byte?
        set(value): Unit = set(51, value)
        get(): Byte? = get(51) as Byte?

    open var setCategory: Byte?
        set(value): Unit = set(52, value)
        get(): Byte? = get(52) as Byte?

    open var rentReviewCategory: Byte?
        set(value): Unit = set(53, value)
        get(): Byte? = get(53) as Byte?

    open var managementAvailabilityCategory: Byte?
        set(value): Unit = set(54, value)
        get(): Byte? = get(54) as Byte?

    open var guaranteeCategory: Byte?
        set(value): Unit = set(55, value)
        get(): Byte? = get(55) as Byte?

    open var mutualAidAssociationAvailability: Byte?
        set(value): Unit = set(56, value)
        get(): Byte? = get(56) as Byte?

    open var waterCategory: Byte?
        set(value): Unit = set(57, value)
        get(): Byte? = get(57) as Byte?

    open var developmentApplicationCategory: Byte?
        set(value): Unit = set(58, value)
        get(): Byte? = get(58) as Byte?

    open var agriculturalConversionCategory: Byte?
        set(value): Unit = set(59, value)
        get(): Byte? = get(59) as Byte?

    open var taxAccountantCompanionCategory: Byte?
        set(value): Unit = set(60, value)
        get(): Byte? = get(60) as Byte?

    open var information: Int?
        set(value): Unit = set(61, value)
        get(): Int? = get(61) as Int?

    open var numberOfContractPlots: BigDecimal?
        set(value): Unit = set(62, value)
        get(): BigDecimal? = get(62) as BigDecimal?

    open var contractPaymentCategory: Byte?
        set(value): Unit = set(63, value)
        get(): Byte? = get(63) as Byte?

    open var financingSourceCategory1: Byte?
        set(value): Unit = set(64, value)
        get(): Byte? = get(64) as Byte?

    open var financingSourceCategory2: Byte?
        set(value): Unit = set(65, value)
        get(): Byte? = get(65) as Byte?

    open var financingSourceCategory3: Byte?
        set(value): Unit = set(66, value)
        get(): Byte? = get(66) as Byte?

    open var totalContractAmount: Long?
        set(value): Unit = set(67, value)
        get(): Long? = get(67) as Long?

    open var totalContractPrice: Long?
        set(value): Unit = set(68, value)
        get(): Long? = get(68) as Long?

    open var mainContractAmount: Long?
        set(value): Unit = set(69, value)
        get(): Long? = get(69) as Long?

    open var mainContractPrice: Long?
        set(value): Unit = set(70, value)
        get(): Long? = get(70) as Long?

    open var separateContractAmount: Long?
        set(value): Unit = set(71, value)
        get(): Long? = get(71) as Long?

    open var separateContractPrice: Long?
        set(value): Unit = set(72, value)
        get(): Long? = get(72) as Long?

    open var reserve2: Long?
        set(value): Unit = set(73, value)
        get(): Long? = get(73) as Long?

    open var stampDutyCollectionAmount: Long?
        set(value): Unit = set(74, value)
        get(): Long? = get(74) as Long?

    open var prepFundAApplicationAmountTaxIncl: Long?
        set(value): Unit = set(75, value)
        get(): Long? = get(75) as Long?

    open var contractDiscountAmount: Long?
        set(value): Unit = set(76, value)
        get(): Long? = get(76) as Long?

    open var discountApprovalNo1: String?
        set(value): Unit = set(77, value)
        get(): String? = get(77) as String?

    open var discountUsageAmount1TaxIncl: Long?
        set(value): Unit = set(78, value)
        get(): Long? = get(78) as Long?

    open var discountApprovalNo2: String?
        set(value): Unit = set(79, value)
        get(): String? = get(79) as String?

    open var discountUsageAmount2TaxIncl: Long?
        set(value): Unit = set(80, value)
        get(): Long? = get(80) as Long?

    open var discountApprovalNo3: String?
        set(value): Unit = set(81, value)
        get(): String? = get(81) as String?

    open var discountUsageAmount3TaxIncl: Long?
        set(value): Unit = set(82, value)
        get(): Long? = get(82) as Long?

    open var discountApprovalNo4: String?
        set(value): Unit = set(83, value)
        get(): String? = get(83) as String?

    open var discountUsageAmount4TaxIncl: Long?
        set(value): Unit = set(84, value)
        get(): Long? = get(84) as Long?

    open var discountApprovalNo5: String?
        set(value): Unit = set(85, value)
        get(): String? = get(85) as String?

    open var discountUsageAmount5TaxIncl: Long?
        set(value): Unit = set(86, value)
        get(): Long? = get(86) as Long?

    open var discountUnallocatedAmount: Long?
        set(value): Unit = set(87, value)
        get(): Long? = get(87) as Long?

    open var informationRewardUsageAmount: Long?
        set(value): Unit = set(88, value)
        get(): Long? = get(88) as Long?

    open var preparationFundAUsageAmountTaxIncl: Long?
        set(value): Unit = set(89, value)
        get(): Long? = get(89) as Long?

    open var informationApprovalNo1: String?
        set(value): Unit = set(90, value)
        get(): String? = get(90) as String?

    open var informationUsageAmount1TaxIncl: Long?
        set(value): Unit = set(91, value)
        get(): Long? = get(91) as Long?

    open var informationApprovalNo2: String?
        set(value): Unit = set(92, value)
        get(): String? = get(92) as String?

    open var informationUsageAmount2TaxIncl: Long?
        set(value): Unit = set(93, value)
        get(): Long? = get(93) as Long?

    open var informationApprovalNo3: String?
        set(value): Unit = set(94, value)
        get(): String? = get(94) as String?

    open var informationUsageAmount3TaxIncl: Long?
        set(value): Unit = set(95, value)
        get(): Long? = get(95) as Long?

    open var informationApprovalNo4: String?
        set(value): Unit = set(96, value)
        get(): String? = get(96) as String?

    open var informationUsageAmount4TaxIncl: Long?
        set(value): Unit = set(97, value)
        get(): Long? = get(97) as Long?

    open var informationUnallocatedAmount: Long?
        set(value): Unit = set(98, value)
        get(): Long? = get(98) as Long?

    open var processCd1: Byte?
        set(value): Unit = set(99, value)
        get(): Byte? = get(99) as Byte?

    open var paymentAmount1: Long?
        set(value): Unit = set(100, value)
        get(): Long? = get(100) as Long?

    open var processCd2: Byte?
        set(value): Unit = set(101, value)
        get(): Byte? = get(101) as Byte?

    open var paymentAmount2: Long?
        set(value): Unit = set(102, value)
        get(): Long? = get(102) as Long?

    open var processCd3: Byte?
        set(value): Unit = set(103, value)
        get(): Byte? = get(103) as Byte?

    open var paymentAmount3: Long?
        set(value): Unit = set(104, value)
        get(): Long? = get(104) as Long?

    open var processCd4: Byte?
        set(value): Unit = set(105, value)
        get(): Byte? = get(105) as Byte?

    open var paymentAmount4: Long?
        set(value): Unit = set(106, value)
        get(): Long? = get(106) as Long?

    open var processCd5: Byte?
        set(value): Unit = set(107, value)
        get(): Byte? = get(107) as Byte?

    open var paymentAmount5: Long?
        set(value): Unit = set(108, value)
        get(): Long? = get(108) as Long?

    open var processCd6: Byte?
        set(value): Unit = set(109, value)
        get(): Byte? = get(109) as Byte?

    open var paymentAmount6: Long?
        set(value): Unit = set(110, value)
        get(): Long? = get(110) as Long?

    open var grossMarginRateContract: BigDecimal?
        set(value): Unit = set(111, value)
        get(): BigDecimal? = get(111) as BigDecimal?

    open var grossMarginRateCurrent: BigDecimal?
        set(value): Unit = set(112, value)
        get(): BigDecimal? = get(112) as BigDecimal?

    open var mainGrossMarginRateContract: BigDecimal?
        set(value): Unit = set(113, value)
        get(): BigDecimal? = get(113) as BigDecimal?

    open var mainGrossMarginRateCurrent: BigDecimal?
        set(value): Unit = set(114, value)
        get(): BigDecimal? = get(114) as BigDecimal?

    open var separateGrossMarginRateContract: BigDecimal?
        set(value): Unit = set(115, value)
        get(): BigDecimal? = get(115) as BigDecimal?

    open var separateGrossMarginRateCurrent: BigDecimal?
        set(value): Unit = set(116, value)
        get(): BigDecimal? = get(116) as BigDecimal?

    open var additionalCommissionRateContract: BigDecimal?
        set(value): Unit = set(117, value)
        get(): BigDecimal? = get(117) as BigDecimal?

    open var additionalCommissionRateCurrent: BigDecimal?
        set(value): Unit = set(118, value)
        get(): BigDecimal? = get(118) as BigDecimal?

    open var mainCommissionRateContract: BigDecimal?
        set(value): Unit = set(119, value)
        get(): BigDecimal? = get(119) as BigDecimal?

    open var mainCommissionRateCurrent: BigDecimal?
        set(value): Unit = set(120, value)
        get(): BigDecimal? = get(120) as BigDecimal?

    open var separateCommissionRateContract: BigDecimal?
        set(value): Unit = set(121, value)
        get(): BigDecimal? = get(121) as BigDecimal?

    open var separateCommissionRateCurrent: BigDecimal?
        set(value): Unit = set(122, value)
        get(): BigDecimal? = get(122) as BigDecimal?

    open var contractChangeCount: Byte?
        set(value): Unit = set(123, value)
        get(): Byte? = get(123) as Byte?

    open var budgetVariationExistenceCategory: Byte?
        set(value): Unit = set(124, value)
        get(): Byte? = get(124) as Byte?

    open var specialCondChangeExistenceCategory: Byte?
        set(value): Unit = set(125, value)
        get(): Byte? = get(125) as Byte?

    open var startTimingOriginalContractDays: Short?
        set(value): Unit = set(126, value)
        get(): Short? = get(126) as Short?

    open var completionTimingOriginalContractDays: Short?
        set(value): Unit = set(127, value)
        get(): Short? = get(127) as Short?

    open var amountChangeBeforeChangeAmount: Long?
        set(value): Unit = set(128, value)
        get(): Long? = get(128) as Long?

    open var amountChangeVariationAmount: Long?
        set(value): Unit = set(129, value)
        get(): Long? = get(129) as Long?

    open var additionalConstructionAmount: Long?
        set(value): Unit = set(130, value)
        get(): Long? = get(130) as Long?

    open var cancelledConstructionAmount: Long?
        set(value): Unit = set(131, value)
        get(): Long? = get(131) as Long?

    open var discountAmount: Long?
        set(value): Unit = set(132, value)
        get(): Long? = get(132) as Long?

    open var additionalConstructionAmountCumulative: Long?
        set(value): Unit = set(133, value)
        get(): Long? = get(133) as Long?

    open var cancelledConstructionAmountCumulative: Long?
        set(value): Unit = set(134, value)
        get(): Long? = get(134) as Long?

    open var discountAmountCumulative: Long?
        set(value): Unit = set(135, value)
        get(): Long? = get(135) as Long?

    open var reserve3: Byte?
        set(value): Unit = set(136, value)
        get(): Byte? = get(136) as Byte?

    open var returnCategory: Byte?
        set(value): Unit = set(137, value)
        get(): Byte? = get(137) as Byte?

    open var agreedTerminationReasonCd: Byte?
        set(value): Unit = set(138, value)
        get(): Byte? = get(138) as Byte?

    open var returnAmount: Long?
        set(value): Unit = set(139, value)
        get(): Long? = get(139) as Long?

    open var transferAmount: Long?
        set(value): Unit = set(140, value)
        get(): Long? = get(140) as Long?

    open var additionalCollectionAmount: Long?
        set(value): Unit = set(141, value)
        get(): Long? = get(141) as Long?

    open var constructionAdvanceRemainingBalance: Long?
        set(value): Unit = set(142, value)
        get(): Long? = get(142) as Long?

    open var miscellaneousExpenses: Long?
        set(value): Unit = set(143, value)
        get(): Long? = get(143) as Long?

    open var transferDestinationProjectCd: Int?
        set(value): Unit = set(144, value)
        get(): Int? = get(144) as Int?

    open var transferDestinationContractNo: Short?
        set(value): Unit = set(145, value)
        get(): Short? = get(145) as Short?

    open var terminationReportFlag: Byte?
        set(value): Unit = set(146, value)
        get(): Byte? = get(146) as Byte?

    open var contractContentReportDate: Int?
        set(value): Unit = set(147, value)
        get(): Int? = get(147) as Int?

    open var agreedTerminationReportDate: Int?
        set(value): Unit = set(148, value)
        get(): Int? = get(148) as Int?

    open var processCd7: Byte?
        set(value): Unit = set(149, value)
        get(): Byte? = get(149) as Byte?

    open var paymentAmount7: Long?
        set(value): Unit = set(150, value)
        get(): Long? = get(150) as Long?

    open var processCd8: Byte?
        set(value): Unit = set(151, value)
        get(): Byte? = get(151) as Byte?

    open var paymentAmount8: Long?
        set(value): Unit = set(152, value)
        get(): Long? = get(152) as Long?

    open var processCd9: Byte?
        set(value): Unit = set(153, value)
        get(): Byte? = get(153) as Byte?

    open var paymentAmount9: Long?
        set(value): Unit = set(154, value)
        get(): Long? = get(154) as Long?

    open var processCd10: Byte?
        set(value): Unit = set(155, value)
        get(): Byte? = get(155) as Byte?

    open var paymentAmount10: Long?
        set(value): Unit = set(156, value)
        get(): Long? = get(156) as Long?

    open var collectionPerformanceAmount: Long?
        set(value): Unit = set(157, value)
        get(): Long? = get(157) as Long?

    open var plotChangeBeforeChangePlots: BigDecimal?
        set(value): Unit = set(158, value)
        get(): BigDecimal? = get(158) as BigDecimal?

    open var plotChangeVariationPlots: BigDecimal?
        set(value): Unit = set(159, value)
        get(): BigDecimal? = get(159) as BigDecimal?

    open var reserve4: Byte?
        set(value): Unit = set(160, value)
        get(): Byte? = get(160) as Byte?

    open var reserve5: Short?
        set(value): Unit = set(161, value)
        get(): Short? = get(161) as Short?

    open var reserve6: Byte?
        set(value): Unit = set(162, value)
        get(): Byte? = get(162) as Byte?

    open var reserve7: Short?
        set(value): Unit = set(163, value)
        get(): Short? = get(163) as Short?

    open var branchTransferProcessDate: Int?
        set(value): Unit = set(164, value)
        get(): Int? = get(164) as Int?

    open var processDate: Int?
        set(value): Unit = set(165, value)
        get(): Int? = get(165) as Int?

    open var branchTransferBusinessPlace: Short?
        set(value): Unit = set(166, value)
        get(): Short? = get(166) as Short?

    open var postCompletionDiscountAmount: Long?
        set(value): Unit = set(167, value)
        get(): Long? = get(167) as Long?

    open var postCompletionDiscountApprovalNo: String?
        set(value): Unit = set(168, value)
        get(): String? = get(168) as String?

    open var postCompletionDiscountAchievementDate: Int?
        set(value): Unit = set(169, value)
        get(): Int? = get(169) as Int?

    open var postCompletionDiscountInputDate: Int?
        set(value): Unit = set(170, value)
        get(): Int? = get(170) as Int?

    open var settlementAmountSalesCompany: Long?
        set(value): Unit = set(171, value)
        get(): Long? = get(171) as Long?

    open var settlementAmountConstruction: Long?
        set(value): Unit = set(172, value)
        get(): Long? = get(172) as Long?

    open var projectCd: Int?
        set(value): Unit = set(173, value)
        get(): Int? = get(173) as Int?

    open var branchManager: Int?
        set(value): Unit = set(174, value)
        get(): Int? = get(174) as Int?

    open var costAccountingMonth: Int?
        set(value): Unit = set(175, value)
        get(): Int? = get(175) as Int?

    open var ancillaryContractAmount: Long?
        set(value): Unit = set(176, value)
        get(): Long? = get(176) as Long?

    open var ancillaryContractPrice: Long?
        set(value): Unit = set(177, value)
        get(): Long? = get(177) as Long?

    open var landscapingContractAmount: Long?
        set(value): Unit = set(178, value)
        get(): Long? = get(178) as Long?

    open var landscapingContractPrice: Long?
        set(value): Unit = set(179, value)
        get(): Long? = get(179) as Long?

    open var otherContractAmount: Long?
        set(value): Unit = set(180, value)
        get(): Long? = get(180) as Long?

    open var otherContractPrice: Long?
        set(value): Unit = set(181, value)
        get(): Long? = get(181) as Long?

    open var ancillaryGrossMarginRateContract: BigDecimal?
        set(value): Unit = set(182, value)
        get(): BigDecimal? = get(182) as BigDecimal?

    open var ancillaryGrossMarginRateCurrent: BigDecimal?
        set(value): Unit = set(183, value)
        get(): BigDecimal? = get(183) as BigDecimal?

    open var landscapingGrossMarginRateContract: BigDecimal?
        set(value): Unit = set(184, value)
        get(): BigDecimal? = get(184) as BigDecimal?

    open var landscapingGrossMarginRateCurrent: BigDecimal?
        set(value): Unit = set(185, value)
        get(): BigDecimal? = get(185) as BigDecimal?

    open var otherGrossMarginRateContract: BigDecimal?
        set(value): Unit = set(186, value)
        get(): BigDecimal? = get(186) as BigDecimal?

    open var otherGrossMarginRateCurrent: BigDecimal?
        set(value): Unit = set(187, value)
        get(): BigDecimal? = get(187) as BigDecimal?

    open var ancillaryCommissionRateContract: BigDecimal?
        set(value): Unit = set(188, value)
        get(): BigDecimal? = get(188) as BigDecimal?

    open var ancillaryCommissionRateCurrent: BigDecimal?
        set(value): Unit = set(189, value)
        get(): BigDecimal? = get(189) as BigDecimal?

    open var landscapingCommissionRateContract: BigDecimal?
        set(value): Unit = set(190, value)
        get(): BigDecimal? = get(190) as BigDecimal?

    open var landscapingCommissionRateCurrent: BigDecimal?
        set(value): Unit = set(191, value)
        get(): BigDecimal? = get(191) as BigDecimal?

    open var otherCommissionRateContract: BigDecimal?
        set(value): Unit = set(192, value)
        get(): BigDecimal? = get(192) as BigDecimal?

    open var otherCommissionRateCurrent: BigDecimal?
        set(value): Unit = set(193, value)
        get(): BigDecimal? = get(193) as BigDecimal?

    open var contractAmountExcludingTax: Long?
        set(value): Unit = set(194, value)
        get(): Long? = get(194) as Long?

    open var contractAmountConsumptionTax1: Long?
        set(value): Unit = set(195, value)
        get(): Long? = get(195) as Long?

    open var contractAmountConsumptionTax2: Long?
        set(value): Unit = set(196, value)
        get(): Long? = get(196) as Long?

    open var mainConstructionExcludingTax: Long?
        set(value): Unit = set(197, value)
        get(): Long? = get(197) as Long?

    open var mainConstructionConsumptionTax: Long?
        set(value): Unit = set(198, value)
        get(): Long? = get(198) as Long?

    open var ancillaryConstructionExcludingTax: Long?
        set(value): Unit = set(199, value)
        get(): Long? = get(199) as Long?

    open var ancillaryConstructionConsumptionTax: Long?
        set(value): Unit = set(200, value)
        get(): Long? = get(200) as Long?

    open var landscapingConstructionExcludingTax: Long?
        set(value): Unit = set(201, value)
        get(): Long? = get(201) as Long?

    open var landscapingConstructionConsumptionTax: Long?
        set(value): Unit = set(202, value)
        get(): Long? = get(202) as Long?

    open var otherConstructionExcludingTax: Long?
        set(value): Unit = set(203, value)
        get(): Long? = get(203) as Long?

    open var otherConstructionConsumptionTax: Long?
        set(value): Unit = set(204, value)
        get(): Long? = get(204) as Long?

    open var separateConstructionExcludingTax: Long?
        set(value): Unit = set(205, value)
        get(): Long? = get(205) as Long?

    open var separateConstructionConsumptionTax: Long?
        set(value): Unit = set(206, value)
        get(): Long? = get(206) as Long?

    open var preparationFundAApplicationAmount: Long?
        set(value): Unit = set(207, value)
        get(): Long? = get(207) as Long?

    open var discountUsageAmount1: Long?
        set(value): Unit = set(208, value)
        get(): Long? = get(208) as Long?

    open var discountUsageAmount2: Long?
        set(value): Unit = set(209, value)
        get(): Long? = get(209) as Long?

    open var discountUsageAmount3: Long?
        set(value): Unit = set(210, value)
        get(): Long? = get(210) as Long?

    open var discountUsageAmount4: Long?
        set(value): Unit = set(211, value)
        get(): Long? = get(211) as Long?

    open var discountUsageAmount5: Long?
        set(value): Unit = set(212, value)
        get(): Long? = get(212) as Long?

    open var preparationFundAUsageAmount: Long?
        set(value): Unit = set(213, value)
        get(): Long? = get(213) as Long?

    open var informationUsageAmount1: Long?
        set(value): Unit = set(214, value)
        get(): Long? = get(214) as Long?

    open var informationUsageAmount2: Long?
        set(value): Unit = set(215, value)
        get(): Long? = get(215) as Long?

    open var informationUsageAmount3: Long?
        set(value): Unit = set(216, value)
        get(): Long? = get(216) as Long?

    open var informationUsageAmount4: Long?
        set(value): Unit = set(217, value)
        get(): Long? = get(217) as Long?

    open var planChangeExistenceCategory: Byte?
        set(value): Unit = set(218, value)
        get(): Byte? = get(218) as Byte?

    open var commissionSystemChangeCategory: Byte?
        set(value): Unit = set(219, value)
        get(): Byte? = get(219) as Byte?

    open var commissionApplicableDate: Int?
        set(value): Unit = set(220, value)
        get(): Int? = get(220) as Int?

    open var consumptionTaxAmountChangeExistence: Byte?
        set(value): Unit = set(221, value)
        get(): Byte? = get(221) as Byte?

    open var bulkOrderFlag: String?
        set(value): Unit = set(222, value)
        get(): String? = get(222) as String?

    open var specialRentalCategory: String?
        set(value): Unit = set(223, value)
        get(): String? = get(223) as String?

    open var largePropertyCategory: String?
        set(value): Unit = set(224, value)
        get(): String? = get(224) as String?

    open var commissionTargetCategory: String?
        set(value): Unit = set(225, value)
        get(): String? = get(225) as String?

    open var informationPartner: String?
        set(value): Unit = set(226, value)
        get(): String? = get(226) as String?

    open var tenantContract: String?
        set(value): Unit = set(227, value)
        get(): String? = get(227) as String?

    open var rewardAmount: Long?
        set(value): Unit = set(228, value)
        get(): Long? = get(228) as Long?

    open var conversionComplexCategory: String?
        set(value): Unit = set(229, value)
        get(): String? = get(229) as String?

    open var compensationAmount: Long?
        set(value): Unit = set(230, value)
        get(): Long? = get(230) as Long?

    open var assessmentSectionManager: String?
        set(value): Unit = set(231, value)
        get(): String? = get(231) as String?

    open var reserve8: String?
        set(value): Unit = set(232, value)
        get(): String? = get(232) as String?

    open var reserve9: String?
        set(value): Unit = set(233, value)
        get(): String? = get(233) as String?

    open var reserve10: String?
        set(value): Unit = set(234, value)
        get(): String? = get(234) as String?

    open var firstExclusionFlag: String?
        set(value): Unit = set(235, value)
        get(): String? = get(235) as String?

    open var exclusionAmount: Long?
        set(value): Unit = set(236, value)
        get(): Long? = get(236) as Long?

    open var reserve11: String?
        set(value): Unit = set(237, value)
        get(): String? = get(237) as String?

    open var reserve12: String?
        set(value): Unit = set(238, value)
        get(): String? = get(238) as String?

    open var interestBurden: String?
        set(value): Unit = set(239, value)
        get(): String? = get(239) as String?

    open var subsidyType: String?
        set(value): Unit = set(240, value)
        get(): String? = get(240) as String?

    open var maintenanceType: String?
        set(value): Unit = set(241, value)
        get(): String? = get(241) as String?

    open var surveyProjectAmount: Long?
        set(value): Unit = set(242, value)
        get(): Long? = get(242) as Long?

    open var basicDesignAmount: Long?
        set(value): Unit = set(243, value)
        get(): Long? = get(243) as Long?

    open var detailedDesignAmount: Long?
        set(value): Unit = set(244, value)
        get(): Long? = get(244) as Long?

    open var managementForm: Byte?
        set(value): Unit = set(245, value)
        get(): Byte? = get(245) as Byte?

    open var partnerCompanyCd: String?
        set(value): Unit = set(246, value)
        get(): String? = get(246) as String?

    open var assessmentBranchCd: String?
        set(value): Unit = set(247, value)
        get(): String? = get(247) as String?

    open var mortgageSetting: Byte?
        set(value): Unit = set(248, value)
        get(): Byte? = get(248) as Byte?

    open var mortgageAmount: Long?
        set(value): Unit = set(249, value)
        get(): Long? = get(249) as Long?

    open var managementFee: Byte?
        set(value): Unit = set(250, value)
        get(): Byte? = get(250) as Byte?

    open var exclusionConversionAmount: Long?
        set(value): Unit = set(251, value)
        get(): Long? = get(251) as Long?

    open var totalPrice: Long?
        set(value): Unit = set(252, value)
        get(): Long? = get(252) as Long?

    open var mainPrice: Long?
        set(value): Unit = set(253, value)
        get(): Long? = get(253) as Long?

    open var ancillaryPrice: Long?
        set(value): Unit = set(254, value)
        get(): Long? = get(254) as Long?

    open var landscapingPrice: Long?
        set(value): Unit = set(255, value)
        get(): Long? = get(255) as Long?

    open var otherPrice: Long?
        set(value): Unit = set(256, value)
        get(): Long? = get(256) as Long?

    open var separatePrice: Long?
        set(value): Unit = set(257, value)
        get(): Long? = get(257) as Long?

    open var totalPriceGrossMarginRate: BigDecimal?
        set(value): Unit = set(258, value)
        get(): BigDecimal? = get(258) as BigDecimal?

    open var mainPriceGrossMarginRate: BigDecimal?
        set(value): Unit = set(259, value)
        get(): BigDecimal? = get(259) as BigDecimal?

    open var ancillaryPriceGrossMarginRate: BigDecimal?
        set(value): Unit = set(260, value)
        get(): BigDecimal? = get(260) as BigDecimal?

    open var landscapingPriceGrossMarginRate: BigDecimal?
        set(value): Unit = set(261, value)
        get(): BigDecimal? = get(261) as BigDecimal?

    open var otherPriceGrossMarginRate: BigDecimal?
        set(value): Unit = set(262, value)
        get(): BigDecimal? = get(262) as BigDecimal?

    open var separatePriceGrossMarginRate: BigDecimal?
        set(value): Unit = set(263, value)
        get(): BigDecimal? = get(263) as BigDecimal?

    open var totalPriceUsageAmount: Long?
        set(value): Unit = set(264, value)
        get(): Long? = get(264) as Long?

    open var mainPriceUsageAmount: Long?
        set(value): Unit = set(265, value)
        get(): Long? = get(265) as Long?

    open var ancillaryPriceUsageAmount: Long?
        set(value): Unit = set(266, value)
        get(): Long? = get(266) as Long?

    open var landscapingPriceUsageAmount: Long?
        set(value): Unit = set(267, value)
        get(): Long? = get(267) as Long?

    open var otherPriceUsageAmount: Long?
        set(value): Unit = set(268, value)
        get(): Long? = get(268) as Long?

    open var separatePriceUsageAmount: Long?
        set(value): Unit = set(269, value)
        get(): Long? = get(269) as Long?

    open var contractCommissionRateContract: BigDecimal?
        set(value): Unit = set(270, value)
        get(): BigDecimal? = get(270) as BigDecimal?

    open var contractCommissionRateCurrent: BigDecimal?
        set(value): Unit = set(271, value)
        get(): BigDecimal? = get(271) as BigDecimal?

    open var informationPreparationFundBUsage: Long?
        set(value): Unit = set(272, value)
        get(): Long? = get(272) as Long?

    open var informationRegulations: Byte?
        set(value): Unit = set(273, value)
        get(): Byte? = get(273) as Byte?

    open var rewardRate: BigDecimal?
        set(value): Unit = set(274, value)
        get(): BigDecimal? = get(274) as BigDecimal?

    open var rewardAmount2: Long?
        set(value): Unit = set(275, value)
        get(): Long? = get(275) as Long?

    open var businessProjectionCd1: Int?
        set(value): Unit = set(276, value)
        get(): Int? = get(276) as Int?

    open var businessProjectionCd2: Int?
        set(value): Unit = set(277, value)
        get(): Int? = get(277) as Int?

    open var changeApprovalFlag: Byte?
        set(value): Unit = set(278, value)
        get(): Byte? = get(278) as Byte?

    open var partnerCompanyIntroduction: String?
        set(value): Unit = set(279, value)
        get(): String? = get(279) as String?

    open var contractCdSt: String?
        set(value): Unit = set(280, value)
        get(): String? = get(280) as String?

    open var contractAdditionalCdSt: String?
        set(value): Unit = set(281, value)
        get(): String? = get(281) as String?

    open var updateEmployeeNoSt: String?
        set(value): Unit = set(282, value)
        get(): String? = get(282) as String?

    open var contractResponsiblePersonSt: String?
        set(value): Unit = set(283, value)
        get(): String? = get(283) as String?

    open var contractSectionManagerSt: String?
        set(value): Unit = set(284, value)
        get(): String? = get(284) as String?

    open var contractCompanionSt: String?
        set(value): Unit = set(285, value)
        get(): String? = get(285) as String?

    open var landUseCategorySt: String?
        set(value): Unit = set(286, value)
        get(): String? = get(286) as String?

    open var transferDestinationProjectCdSt: String?
        set(value): Unit = set(287, value)
        get(): String? = get(287) as String?

    open var transferDestinationContractNoSt: String?
        set(value): Unit = set(288, value)
        get(): String? = get(288) as String?

    open var branchManagerSt: String?
        set(value): Unit = set(289, value)
        get(): String? = get(289) as String?

    open var constructionBranchJa: String?
        set(value): Unit = set(290, value)
        get(): String? = get(290) as String?

    open var contractBranchJa: String?
        set(value): Unit = set(291, value)
        get(): String? = get(291) as String?

    open var branchTransferBusinessPlaceJa: String?
        set(value): Unit = set(292, value)
        get(): String? = get(292) as String?

    open var contractDeputyManager: String?
        set(value): Unit = set(293, value)
        get(): String? = get(293) as String?

    open var affiliatedMember1: String?
        set(value): Unit = set(294, value)
        get(): String? = get(294) as String?

    open var affiliatedMember2: String?
        set(value): Unit = set(295, value)
        get(): String? = get(295) as String?

    open var affiliatedMember3: String?
        set(value): Unit = set(296, value)
        get(): String? = get(296) as String?

    open var affiliatedMember4: String?
        set(value): Unit = set(297, value)
        get(): String? = get(297) as String?

    open var affiliatedMember5: String?
        set(value): Unit = set(298, value)
        get(): String? = get(298) as String?

    open var affiliatedMember6: String?
        set(value): Unit = set(299, value)
        get(): String? = get(299) as String?

    open var affiliatedMember7: String?
        set(value): Unit = set(300, value)
        get(): String? = get(300) as String?

    open var performanceRecordingBranch: String?
        set(value): Unit = set(301, value)
        get(): String? = get(301) as String?

    open var performanceRecordingSection: String?
        set(value): Unit = set(302, value)
        get(): String? = get(302) as String?

    open var rentPrepaymentApplicableCategory: Byte?
        set(value): Unit = set(303, value)
        get(): Byte? = get(303) as Byte?

    /**
     * Create a detached, initialised ContractFileRecord
     */
    constructor(value: ContractFilePojo?): this() {
        if (value != null) {
            this.contractCd = value.contractCd
            this.contractAdditionalCd = value.contractAdditionalCd
            this.changeHistoryNo = value.changeHistoryNo
            this.recordDeleteFlag = value.recordDeleteFlag
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgram = value.updateProgram
            this.updateEmployeeNo = value.updateEmployeeNo
            this.contractStatusCategory = value.contractStatusCategory
            this.contractContentReportFlag = value.contractContentReportFlag
            this.transactionCategory = value.transactionCategory
            this.ordererCd = value.ordererCd
            this.contractDateChangeDate = value.contractDateChangeDate
            this.originalContractDate = value.originalContractDate
            this.agreedTerminationDate = value.agreedTerminationDate
            this.contractJudgmentCategory = value.contractJudgmentCategory
            this.contractJudgmentDate = value.contractJudgmentDate
            this.contractJudgmentDeptCategory = value.contractJudgmentDeptCategory
            this.constructionBranch = value.constructionBranch
            this.contractBranch = value.contractBranch
            this.affiliatedSection = value.affiliatedSection
            this.contractResponsiblePerson = value.contractResponsiblePerson
            this.contractSectionManager = value.contractSectionManager
            this.contractCompanion = value.contractCompanion
            this.businessProjectionNo = value.businessProjectionNo
            this.repeatCategory = value.repeatCategory
            this.contractCategory = value.contractCategory
            this.maintenanceCategory = value.maintenanceCategory
            this.contractPartnerCategory = value.contractPartnerCategory
            this.activityArea = value.activityArea
            this.reserve1 = value.reserve1
            this.otherCompanyFlag = value.otherCompanyFlag
            this.additionalConstructionCategory = value.additionalConstructionCategory
            this.landCd = value.landCd
            this.constructionName = value.constructionName
            this.constructionStartTimingCategory = value.constructionStartTimingCategory
            this.constructionPeriodStartTiming = value.constructionPeriodStartTiming
            this.constructionPeriodCompletionTiming = value.constructionPeriodCompletionTiming
            this.landAreaM = value.landAreaM
            this.landAreaTsubo = value.landAreaTsubo
            this.constructionFloorAreaM = value.constructionFloorAreaM
            this.constructionFloorAreaTsubo = value.constructionFloorAreaTsubo
            this.warehouseFactoryAreaM = value.warehouseFactoryAreaM
            this.warehouseFactoryAreaTsubo = value.warehouseFactoryAreaTsubo
            this.buildingTypeSt = value.buildingTypeSt
            this.numberOfBuildings = value.numberOfBuildings
            this.numberOfRooms = value.numberOfRooms
            this.fireProtectionCategory = value.fireProtectionCategory
            this.landUseCategory = value.landUseCategory
            this.leaseCategory = value.leaseCategory
            this.setCategory = value.setCategory
            this.rentReviewCategory = value.rentReviewCategory
            this.managementAvailabilityCategory = value.managementAvailabilityCategory
            this.guaranteeCategory = value.guaranteeCategory
            this.mutualAidAssociationAvailability = value.mutualAidAssociationAvailability
            this.waterCategory = value.waterCategory
            this.developmentApplicationCategory = value.developmentApplicationCategory
            this.agriculturalConversionCategory = value.agriculturalConversionCategory
            this.taxAccountantCompanionCategory = value.taxAccountantCompanionCategory
            this.information = value.information
            this.numberOfContractPlots = value.numberOfContractPlots
            this.contractPaymentCategory = value.contractPaymentCategory
            this.financingSourceCategory1 = value.financingSourceCategory1
            this.financingSourceCategory2 = value.financingSourceCategory2
            this.financingSourceCategory3 = value.financingSourceCategory3
            this.totalContractAmount = value.totalContractAmount
            this.totalContractPrice = value.totalContractPrice
            this.mainContractAmount = value.mainContractAmount
            this.mainContractPrice = value.mainContractPrice
            this.separateContractAmount = value.separateContractAmount
            this.separateContractPrice = value.separateContractPrice
            this.reserve2 = value.reserve2
            this.stampDutyCollectionAmount = value.stampDutyCollectionAmount
            this.prepFundAApplicationAmountTaxIncl = value.prepFundAApplicationAmountTaxIncl
            this.contractDiscountAmount = value.contractDiscountAmount
            this.discountApprovalNo1 = value.discountApprovalNo1
            this.discountUsageAmount1TaxIncl = value.discountUsageAmount1TaxIncl
            this.discountApprovalNo2 = value.discountApprovalNo2
            this.discountUsageAmount2TaxIncl = value.discountUsageAmount2TaxIncl
            this.discountApprovalNo3 = value.discountApprovalNo3
            this.discountUsageAmount3TaxIncl = value.discountUsageAmount3TaxIncl
            this.discountApprovalNo4 = value.discountApprovalNo4
            this.discountUsageAmount4TaxIncl = value.discountUsageAmount4TaxIncl
            this.discountApprovalNo5 = value.discountApprovalNo5
            this.discountUsageAmount5TaxIncl = value.discountUsageAmount5TaxIncl
            this.discountUnallocatedAmount = value.discountUnallocatedAmount
            this.informationRewardUsageAmount = value.informationRewardUsageAmount
            this.preparationFundAUsageAmountTaxIncl = value.preparationFundAUsageAmountTaxIncl
            this.informationApprovalNo1 = value.informationApprovalNo1
            this.informationUsageAmount1TaxIncl = value.informationUsageAmount1TaxIncl
            this.informationApprovalNo2 = value.informationApprovalNo2
            this.informationUsageAmount2TaxIncl = value.informationUsageAmount2TaxIncl
            this.informationApprovalNo3 = value.informationApprovalNo3
            this.informationUsageAmount3TaxIncl = value.informationUsageAmount3TaxIncl
            this.informationApprovalNo4 = value.informationApprovalNo4
            this.informationUsageAmount4TaxIncl = value.informationUsageAmount4TaxIncl
            this.informationUnallocatedAmount = value.informationUnallocatedAmount
            this.processCd1 = value.processCd1
            this.paymentAmount1 = value.paymentAmount1
            this.processCd2 = value.processCd2
            this.paymentAmount2 = value.paymentAmount2
            this.processCd3 = value.processCd3
            this.paymentAmount3 = value.paymentAmount3
            this.processCd4 = value.processCd4
            this.paymentAmount4 = value.paymentAmount4
            this.processCd5 = value.processCd5
            this.paymentAmount5 = value.paymentAmount5
            this.processCd6 = value.processCd6
            this.paymentAmount6 = value.paymentAmount6
            this.grossMarginRateContract = value.grossMarginRateContract
            this.grossMarginRateCurrent = value.grossMarginRateCurrent
            this.mainGrossMarginRateContract = value.mainGrossMarginRateContract
            this.mainGrossMarginRateCurrent = value.mainGrossMarginRateCurrent
            this.separateGrossMarginRateContract = value.separateGrossMarginRateContract
            this.separateGrossMarginRateCurrent = value.separateGrossMarginRateCurrent
            this.additionalCommissionRateContract = value.additionalCommissionRateContract
            this.additionalCommissionRateCurrent = value.additionalCommissionRateCurrent
            this.mainCommissionRateContract = value.mainCommissionRateContract
            this.mainCommissionRateCurrent = value.mainCommissionRateCurrent
            this.separateCommissionRateContract = value.separateCommissionRateContract
            this.separateCommissionRateCurrent = value.separateCommissionRateCurrent
            this.contractChangeCount = value.contractChangeCount
            this.budgetVariationExistenceCategory = value.budgetVariationExistenceCategory
            this.specialCondChangeExistenceCategory = value.specialCondChangeExistenceCategory
            this.startTimingOriginalContractDays = value.startTimingOriginalContractDays
            this.completionTimingOriginalContractDays = value.completionTimingOriginalContractDays
            this.amountChangeBeforeChangeAmount = value.amountChangeBeforeChangeAmount
            this.amountChangeVariationAmount = value.amountChangeVariationAmount
            this.additionalConstructionAmount = value.additionalConstructionAmount
            this.cancelledConstructionAmount = value.cancelledConstructionAmount
            this.discountAmount = value.discountAmount
            this.additionalConstructionAmountCumulative = value.additionalConstructionAmountCumulative
            this.cancelledConstructionAmountCumulative = value.cancelledConstructionAmountCumulative
            this.discountAmountCumulative = value.discountAmountCumulative
            this.reserve3 = value.reserve3
            this.returnCategory = value.returnCategory
            this.agreedTerminationReasonCd = value.agreedTerminationReasonCd
            this.returnAmount = value.returnAmount
            this.transferAmount = value.transferAmount
            this.additionalCollectionAmount = value.additionalCollectionAmount
            this.constructionAdvanceRemainingBalance = value.constructionAdvanceRemainingBalance
            this.miscellaneousExpenses = value.miscellaneousExpenses
            this.transferDestinationProjectCd = value.transferDestinationProjectCd
            this.transferDestinationContractNo = value.transferDestinationContractNo
            this.terminationReportFlag = value.terminationReportFlag
            this.contractContentReportDate = value.contractContentReportDate
            this.agreedTerminationReportDate = value.agreedTerminationReportDate
            this.processCd7 = value.processCd7
            this.paymentAmount7 = value.paymentAmount7
            this.processCd8 = value.processCd8
            this.paymentAmount8 = value.paymentAmount8
            this.processCd9 = value.processCd9
            this.paymentAmount9 = value.paymentAmount9
            this.processCd10 = value.processCd10
            this.paymentAmount10 = value.paymentAmount10
            this.collectionPerformanceAmount = value.collectionPerformanceAmount
            this.plotChangeBeforeChangePlots = value.plotChangeBeforeChangePlots
            this.plotChangeVariationPlots = value.plotChangeVariationPlots
            this.reserve4 = value.reserve4
            this.reserve5 = value.reserve5
            this.reserve6 = value.reserve6
            this.reserve7 = value.reserve7
            this.branchTransferProcessDate = value.branchTransferProcessDate
            this.processDate = value.processDate
            this.branchTransferBusinessPlace = value.branchTransferBusinessPlace
            this.postCompletionDiscountAmount = value.postCompletionDiscountAmount
            this.postCompletionDiscountApprovalNo = value.postCompletionDiscountApprovalNo
            this.postCompletionDiscountAchievementDate = value.postCompletionDiscountAchievementDate
            this.postCompletionDiscountInputDate = value.postCompletionDiscountInputDate
            this.settlementAmountSalesCompany = value.settlementAmountSalesCompany
            this.settlementAmountConstruction = value.settlementAmountConstruction
            this.projectCd = value.projectCd
            this.branchManager = value.branchManager
            this.costAccountingMonth = value.costAccountingMonth
            this.ancillaryContractAmount = value.ancillaryContractAmount
            this.ancillaryContractPrice = value.ancillaryContractPrice
            this.landscapingContractAmount = value.landscapingContractAmount
            this.landscapingContractPrice = value.landscapingContractPrice
            this.otherContractAmount = value.otherContractAmount
            this.otherContractPrice = value.otherContractPrice
            this.ancillaryGrossMarginRateContract = value.ancillaryGrossMarginRateContract
            this.ancillaryGrossMarginRateCurrent = value.ancillaryGrossMarginRateCurrent
            this.landscapingGrossMarginRateContract = value.landscapingGrossMarginRateContract
            this.landscapingGrossMarginRateCurrent = value.landscapingGrossMarginRateCurrent
            this.otherGrossMarginRateContract = value.otherGrossMarginRateContract
            this.otherGrossMarginRateCurrent = value.otherGrossMarginRateCurrent
            this.ancillaryCommissionRateContract = value.ancillaryCommissionRateContract
            this.ancillaryCommissionRateCurrent = value.ancillaryCommissionRateCurrent
            this.landscapingCommissionRateContract = value.landscapingCommissionRateContract
            this.landscapingCommissionRateCurrent = value.landscapingCommissionRateCurrent
            this.otherCommissionRateContract = value.otherCommissionRateContract
            this.otherCommissionRateCurrent = value.otherCommissionRateCurrent
            this.contractAmountExcludingTax = value.contractAmountExcludingTax
            this.contractAmountConsumptionTax1 = value.contractAmountConsumptionTax1
            this.contractAmountConsumptionTax2 = value.contractAmountConsumptionTax2
            this.mainConstructionExcludingTax = value.mainConstructionExcludingTax
            this.mainConstructionConsumptionTax = value.mainConstructionConsumptionTax
            this.ancillaryConstructionExcludingTax = value.ancillaryConstructionExcludingTax
            this.ancillaryConstructionConsumptionTax = value.ancillaryConstructionConsumptionTax
            this.landscapingConstructionExcludingTax = value.landscapingConstructionExcludingTax
            this.landscapingConstructionConsumptionTax = value.landscapingConstructionConsumptionTax
            this.otherConstructionExcludingTax = value.otherConstructionExcludingTax
            this.otherConstructionConsumptionTax = value.otherConstructionConsumptionTax
            this.separateConstructionExcludingTax = value.separateConstructionExcludingTax
            this.separateConstructionConsumptionTax = value.separateConstructionConsumptionTax
            this.preparationFundAApplicationAmount = value.preparationFundAApplicationAmount
            this.discountUsageAmount1 = value.discountUsageAmount1
            this.discountUsageAmount2 = value.discountUsageAmount2
            this.discountUsageAmount3 = value.discountUsageAmount3
            this.discountUsageAmount4 = value.discountUsageAmount4
            this.discountUsageAmount5 = value.discountUsageAmount5
            this.preparationFundAUsageAmount = value.preparationFundAUsageAmount
            this.informationUsageAmount1 = value.informationUsageAmount1
            this.informationUsageAmount2 = value.informationUsageAmount2
            this.informationUsageAmount3 = value.informationUsageAmount3
            this.informationUsageAmount4 = value.informationUsageAmount4
            this.planChangeExistenceCategory = value.planChangeExistenceCategory
            this.commissionSystemChangeCategory = value.commissionSystemChangeCategory
            this.commissionApplicableDate = value.commissionApplicableDate
            this.consumptionTaxAmountChangeExistence = value.consumptionTaxAmountChangeExistence
            this.bulkOrderFlag = value.bulkOrderFlag
            this.specialRentalCategory = value.specialRentalCategory
            this.largePropertyCategory = value.largePropertyCategory
            this.commissionTargetCategory = value.commissionTargetCategory
            this.informationPartner = value.informationPartner
            this.tenantContract = value.tenantContract
            this.rewardAmount = value.rewardAmount
            this.conversionComplexCategory = value.conversionComplexCategory
            this.compensationAmount = value.compensationAmount
            this.assessmentSectionManager = value.assessmentSectionManager
            this.reserve8 = value.reserve8
            this.reserve9 = value.reserve9
            this.reserve10 = value.reserve10
            this.firstExclusionFlag = value.firstExclusionFlag
            this.exclusionAmount = value.exclusionAmount
            this.reserve11 = value.reserve11
            this.reserve12 = value.reserve12
            this.interestBurden = value.interestBurden
            this.subsidyType = value.subsidyType
            this.maintenanceType = value.maintenanceType
            this.surveyProjectAmount = value.surveyProjectAmount
            this.basicDesignAmount = value.basicDesignAmount
            this.detailedDesignAmount = value.detailedDesignAmount
            this.managementForm = value.managementForm
            this.partnerCompanyCd = value.partnerCompanyCd
            this.assessmentBranchCd = value.assessmentBranchCd
            this.mortgageSetting = value.mortgageSetting
            this.mortgageAmount = value.mortgageAmount
            this.managementFee = value.managementFee
            this.exclusionConversionAmount = value.exclusionConversionAmount
            this.totalPrice = value.totalPrice
            this.mainPrice = value.mainPrice
            this.ancillaryPrice = value.ancillaryPrice
            this.landscapingPrice = value.landscapingPrice
            this.otherPrice = value.otherPrice
            this.separatePrice = value.separatePrice
            this.totalPriceGrossMarginRate = value.totalPriceGrossMarginRate
            this.mainPriceGrossMarginRate = value.mainPriceGrossMarginRate
            this.ancillaryPriceGrossMarginRate = value.ancillaryPriceGrossMarginRate
            this.landscapingPriceGrossMarginRate = value.landscapingPriceGrossMarginRate
            this.otherPriceGrossMarginRate = value.otherPriceGrossMarginRate
            this.separatePriceGrossMarginRate = value.separatePriceGrossMarginRate
            this.totalPriceUsageAmount = value.totalPriceUsageAmount
            this.mainPriceUsageAmount = value.mainPriceUsageAmount
            this.ancillaryPriceUsageAmount = value.ancillaryPriceUsageAmount
            this.landscapingPriceUsageAmount = value.landscapingPriceUsageAmount
            this.otherPriceUsageAmount = value.otherPriceUsageAmount
            this.separatePriceUsageAmount = value.separatePriceUsageAmount
            this.contractCommissionRateContract = value.contractCommissionRateContract
            this.contractCommissionRateCurrent = value.contractCommissionRateCurrent
            this.informationPreparationFundBUsage = value.informationPreparationFundBUsage
            this.informationRegulations = value.informationRegulations
            this.rewardRate = value.rewardRate
            this.rewardAmount2 = value.rewardAmount2
            this.businessProjectionCd1 = value.businessProjectionCd1
            this.businessProjectionCd2 = value.businessProjectionCd2
            this.changeApprovalFlag = value.changeApprovalFlag
            this.partnerCompanyIntroduction = value.partnerCompanyIntroduction
            this.contractCdSt = value.contractCdSt
            this.contractAdditionalCdSt = value.contractAdditionalCdSt
            this.updateEmployeeNoSt = value.updateEmployeeNoSt
            this.contractResponsiblePersonSt = value.contractResponsiblePersonSt
            this.contractSectionManagerSt = value.contractSectionManagerSt
            this.contractCompanionSt = value.contractCompanionSt
            this.landUseCategorySt = value.landUseCategorySt
            this.transferDestinationProjectCdSt = value.transferDestinationProjectCdSt
            this.transferDestinationContractNoSt = value.transferDestinationContractNoSt
            this.branchManagerSt = value.branchManagerSt
            this.constructionBranchJa = value.constructionBranchJa
            this.contractBranchJa = value.contractBranchJa
            this.branchTransferBusinessPlaceJa = value.branchTransferBusinessPlaceJa
            this.contractDeputyManager = value.contractDeputyManager
            this.affiliatedMember1 = value.affiliatedMember1
            this.affiliatedMember2 = value.affiliatedMember2
            this.affiliatedMember3 = value.affiliatedMember3
            this.affiliatedMember4 = value.affiliatedMember4
            this.affiliatedMember5 = value.affiliatedMember5
            this.affiliatedMember6 = value.affiliatedMember6
            this.affiliatedMember7 = value.affiliatedMember7
            this.performanceRecordingBranch = value.performanceRecordingBranch
            this.performanceRecordingSection = value.performanceRecordingSection
            this.rentPrepaymentApplicableCategory = value.rentPrepaymentApplicableCategory
            resetChangedOnNotNull()
        }
    }
}
