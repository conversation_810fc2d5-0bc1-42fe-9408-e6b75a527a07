package jp.ne.simplex.application.model

import org.springframework.web.multipart.MultipartFile

class RegisterGarbageImage private constructor(
    // 建物コード
    val buildingCode: Building.Code,
    // ゴミ置き場画像
    val imageFile: ImageFile,
) {
    companion object {
        private const val IMAGE_MAX_BYTES: Long = 200 * 1024 // 200KB

        fun of(buildingCode: Building.Code, image: MultipartFile): RegisterGarbageImage {
            return RegisterGarbageImage(
                buildingCode = buildingCode,
                imageFile = ImageFile.of(image, IMAGE_MAX_BYTES)
            )
        }
    }
}
