/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 物件ボード用住所M 既存システム物理名: EMEADP
 */
@Suppress("UNCHECKED_CAST")
data class PropertyBoardAddressMasterPojo(
    var prefectureCd: String,
    var cityCd: String,
    var prefectureName: String? = null,
    var cityName: String? = null,
    var prefectureCityName: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: PropertyBoardAddressMasterPojo = other as PropertyBoardAddressMasterPojo
        if (this.prefectureCd != o.prefectureCd)
            return false
        if (this.cityCd != o.cityCd)
            return false
        if (this.prefectureName == null) {
            if (o.prefectureName != null)
                return false
        }
        else if (this.prefectureName != o.prefectureName)
            return false
        if (this.cityName == null) {
            if (o.cityName != null)
                return false
        }
        else if (this.cityName != o.cityName)
            return false
        if (this.prefectureCityName == null) {
            if (o.prefectureCityName != null)
                return false
        }
        else if (this.prefectureCityName != o.prefectureCityName)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + this.prefectureCd.hashCode()
        result = prime * result + this.cityCd.hashCode()
        result = prime * result + (if (this.prefectureName == null) 0 else this.prefectureName.hashCode())
        result = prime * result + (if (this.cityName == null) 0 else this.cityName.hashCode())
        result = prime * result + (if (this.prefectureCityName == null) 0 else this.prefectureCityName.hashCode())
        return result
    }
}
