-- TABLE: SPECIAL_BUILDING_MASTER(特例建物マスタ)

CREATE TABLE SPECIAL_BUILDING_MASTER(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1)                    
,    BUILDING_CD                                  varchar(9)                    
,    IDENTIFICATION_CATEGORY                      varchar(2)                    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE SPECIAL_BUILDING_MASTER IS '特例建物マスタ 既存システム物理名: ECMD9P';
COMMENT ON COLUMN SPECIAL_BUILDING_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: E9M01D @290';
COMMENT ON COLUMN SPECIAL_BUILDING_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: E9M02H @290';
COMMENT ON COLUMN SPECIAL_BUILDING_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: E9M03D';
COMMENT ON COLUMN SPECIAL_BUILDING_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: E9M04H';
COMMENT ON COLUMN SPECIAL_BUILDING_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: E9M05N';
COMMENT ON COLUMN SPECIAL_BUILDING_MASTER.UPDATER IS '更新者 既存システム物理名: E9M06C';
COMMENT ON COLUMN SPECIAL_BUILDING_MASTER.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: E9M07S';
COMMENT ON COLUMN SPECIAL_BUILDING_MASTER.BUILDING_CD IS '建物コード 既存システム物理名: E9MABC';
COMMENT ON COLUMN SPECIAL_BUILDING_MASTER.IDENTIFICATION_CATEGORY IS '識別区分 既存システム物理名: E9MSKB';
