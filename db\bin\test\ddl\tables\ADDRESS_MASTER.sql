-- TABLE: ADDRESS_MASTER(住所マスタ)

CREATE TABLE ADDRESS_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    REFLECT_DATE                                 numeric(8,0)                  
,    DELETE_FLAG                                  varchar(1)                    
,    ADDRESS_CODE                                 varchar(10)       NOT NULL    
,    ORGANIZATION_NUMBER                          varchar(7)                    
,    NEW_ADDRESS_CODE                             varchar(10)                   
,    PREFECTURE_KANA_NAME                         varchar(8)                    
,    CITY_KANA_NAME                               varchar(20)                   
,    TOWN_KANA_NAME                               varchar(30)                   
,    COMMON_KANA_NAME                             varchar(40)                   
,    POSTAL_CODE                                  varchar(8)                    
,    PREFECTURE_KANA_COUNT                        numeric(2,0)                  
,    CITY_KANA_COUNT                              numeric(2,0)                  
,    TOWN_KANA_COUNT                              numeric(2,0)                  
,    COMMON_KANA_COUNT                            numeric(2,0)                  
,    KANA_TOTAL_COUNT                             numeric(2,0)                  
,    PREFECTURE_KANJI_NAME                        varchar(10)                   
,    CITY_KANJI_NAME                              varchar(22)                   
,    TOWN_KANJI_NAME                              varchar(42)                   
,    COMMON_KANJI_NAME                            varchar(52)                   
,    POSTAL_CODE_KANJI                            varchar(18)                   
,    PREFECTURE_KANJI_COUNT                       numeric(2,0)                  
,    CITY_KANJI_COUNT                             numeric(2,0)                  
,    TOWN_KANJI_COUNT                             numeric(2,0)                  
,    COMMON_KANJI_COUNT                           numeric(2,0)                  
,    KANJI_TOTAL_COUNT                            numeric(2,0)                  
,    ABOLITION_DATE                               numeric(8,0)                  
,    IMPLEMENTATION_DATE                          numeric(8,0)                  
,    PREFECTURE_NAME_NOT_REQUIRED_CODE            varchar(1)                    
,    LOT_CHANGE_DIVISION                          varchar(1)                    
,    SPLIT_DIVISION                               varchar(1)                    
,    CURRENT_NEW_CD_COEXISTENCE_MONTH1            numeric(6,0)                  
,    CURRENT_NEW_CD_COEXISTENCE_MONTH2            numeric(6,0)                  
,    LOT_CHANGE_MONTH1                            numeric(6,0)                  
,    LOT_CHANGE_MONTH2                            numeric(6,0)                  
,    NOMENCLATURE_CHANGE_MONTH                    numeric(6,0)                  
,    POSTAL_CODE_CHANGE_MONTH                     numeric(6,0)                  
,    ADMIN_CHANGE_DIVISION                        varchar(1)                    
,    ADMIN_CHANGE_MONTH                           numeric(6,0)                  
,    CHANGE_MONTH                                 numeric(6,0)                  
,    JIS_CODE                                     varchar(5)                    
,    NEW_JIS_CODE                                 varchar(5)                    
,    CORRECTION_DIVISION                          varchar(1)                    
,    DATA_DIVISION                                varchar(1)                    
,    ADDRESS_COLLECTION_DIVISION                  varchar(2)                    
,    CONSTRAINT PK_ADDRESS_MASTER PRIMARY KEY (ADDRESS_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE ADDRESS_MASTER IS '住所マスタ 既存システム物理名: XXADRP';
COMMENT ON COLUMN ADDRESS_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: XXA01D';
COMMENT ON COLUMN ADDRESS_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: XXA02H';
COMMENT ON COLUMN ADDRESS_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: XXA03D';
COMMENT ON COLUMN ADDRESS_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: XXA04H';
COMMENT ON COLUMN ADDRESS_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: XXA05P';
COMMENT ON COLUMN ADDRESS_MASTER.UPDATER IS '更新者 既存システム物理名: XXA06P';
COMMENT ON COLUMN ADDRESS_MASTER.REFLECT_DATE IS '反映日付 既存システム物理名: XXA07D';
COMMENT ON COLUMN ADDRESS_MASTER.DELETE_FLAG IS '削除フラグ 既存システム物理名: XXA08S';
COMMENT ON COLUMN ADDRESS_MASTER.ADDRESS_CODE IS '住所コード 既存システム物理名: XXA10K';
COMMENT ON COLUMN ADDRESS_MASTER.ORGANIZATION_NUMBER IS '整理番号 既存システム物理名: XXA11N';
COMMENT ON COLUMN ADDRESS_MASTER.NEW_ADDRESS_CODE IS '新住所コード 既存システム物理名: XXA12C';
COMMENT ON COLUMN ADDRESS_MASTER.PREFECTURE_KANA_NAME IS '都道府県カナ名 既存システム物理名: XXA13M';
COMMENT ON COLUMN ADDRESS_MASTER.CITY_KANA_NAME IS '市区郡カナ名 既存システム物理名: XXA14M';
COMMENT ON COLUMN ADDRESS_MASTER.TOWN_KANA_NAME IS '町村字通称カナ名 既存システム物理名: XXA15M';
COMMENT ON COLUMN ADDRESS_MASTER.COMMON_KANA_NAME IS '通称カナ名 既存システム物理名: XXA16M';
COMMENT ON COLUMN ADDRESS_MASTER.POSTAL_CODE IS '郵便番号 既存システム物理名: XXA17N';
COMMENT ON COLUMN ADDRESS_MASTER.PREFECTURE_KANA_COUNT IS '都道府県カナ字数 既存システム物理名: XXA18Q';
COMMENT ON COLUMN ADDRESS_MASTER.CITY_KANA_COUNT IS '市区郡カナ字数 既存システム物理名: XXA19Q';
COMMENT ON COLUMN ADDRESS_MASTER.TOWN_KANA_COUNT IS '町村字通称カナ字数 既存システム物理名: XXA20Q';
COMMENT ON COLUMN ADDRESS_MASTER.COMMON_KANA_COUNT IS '通称カナ字数 既存システム物理名: XXA21Q';
COMMENT ON COLUMN ADDRESS_MASTER.KANA_TOTAL_COUNT IS 'カナ総文字数 既存システム物理名: XXA22Q';
COMMENT ON COLUMN ADDRESS_MASTER.PREFECTURE_KANJI_NAME IS '都道府県漢字名 既存システム物理名: XXA23M';
COMMENT ON COLUMN ADDRESS_MASTER.CITY_KANJI_NAME IS '市区郡漢字名 既存システム物理名: XXA24M';
COMMENT ON COLUMN ADDRESS_MASTER.TOWN_KANJI_NAME IS '町村字通称漢字名 既存システム物理名: XXA25M';
COMMENT ON COLUMN ADDRESS_MASTER.COMMON_KANJI_NAME IS '通称漢字名 既存システム物理名: XXA26M';
COMMENT ON COLUMN ADDRESS_MASTER.POSTAL_CODE_KANJI IS '郵便番号漢字 既存システム物理名: XXA27N';
COMMENT ON COLUMN ADDRESS_MASTER.PREFECTURE_KANJI_COUNT IS '都道府県漢字字数 既存システム物理名: XXA28Q';
COMMENT ON COLUMN ADDRESS_MASTER.CITY_KANJI_COUNT IS '市区郡漢字字数 既存システム物理名: XXA29Q';
COMMENT ON COLUMN ADDRESS_MASTER.TOWN_KANJI_COUNT IS '町村字通称漢字字数 既存システム物理名: XXA30Q';
COMMENT ON COLUMN ADDRESS_MASTER.COMMON_KANJI_COUNT IS '通称漢字字数 既存システム物理名: XXA31Q';
COMMENT ON COLUMN ADDRESS_MASTER.KANJI_TOTAL_COUNT IS '漢字総文字数 既存システム物理名: XXA32Q';
COMMENT ON COLUMN ADDRESS_MASTER.ABOLITION_DATE IS '廃止年月日 既存システム物理名: XXA33D';
COMMENT ON COLUMN ADDRESS_MASTER.IMPLEMENTATION_DATE IS '施行年月日 既存システム物理名: XXA34D';
COMMENT ON COLUMN ADDRESS_MASTER.PREFECTURE_NAME_NOT_REQUIRED_CODE IS '都道府県名不要コード 既存システム物理名: XXA35C';
COMMENT ON COLUMN ADDRESS_MASTER.LOT_CHANGE_DIVISION IS '地番変更区分 既存システム物理名: XXA36B';
COMMENT ON COLUMN ADDRESS_MASTER.SPLIT_DIVISION IS '分割区分 既存システム物理名: XXA37B';
COMMENT ON COLUMN ADDRESS_MASTER.CURRENT_NEW_CD_COEXISTENCE_MONTH1 IS '現新CD併存年月1 既存システム物理名: XXA38D';
COMMENT ON COLUMN ADDRESS_MASTER.CURRENT_NEW_CD_COEXISTENCE_MONTH2 IS '現新CD併存年月2 既存システム物理名: XXA39D';
COMMENT ON COLUMN ADDRESS_MASTER.LOT_CHANGE_MONTH1 IS '地番変更年月1 既存システム物理名: XXA40D';
COMMENT ON COLUMN ADDRESS_MASTER.LOT_CHANGE_MONTH2 IS '地番変更年月2 既存システム物理名: XXA41D';
COMMENT ON COLUMN ADDRESS_MASTER.NOMENCLATURE_CHANGE_MONTH IS '呼称変更年月 既存システム物理名: XXA42D';
COMMENT ON COLUMN ADDRESS_MASTER.POSTAL_CODE_CHANGE_MONTH IS '郵便番号変更年月 既存システム物理名: XXA43D';
COMMENT ON COLUMN ADDRESS_MASTER.ADMIN_CHANGE_DIVISION IS '行政変更区分 既存システム物理名: XXA44B';
COMMENT ON COLUMN ADDRESS_MASTER.ADMIN_CHANGE_MONTH IS '行政変更年月 既存システム物理名: XXA45D';
COMMENT ON COLUMN ADDRESS_MASTER.CHANGE_MONTH IS '変更年月 既存システム物理名: XXA46D';
COMMENT ON COLUMN ADDRESS_MASTER.JIS_CODE IS 'JISコード 既存システム物理名: XXA47C';
COMMENT ON COLUMN ADDRESS_MASTER.NEW_JIS_CODE IS '新JISコード 既存システム物理名: XXA48C';
COMMENT ON COLUMN ADDRESS_MASTER.CORRECTION_DIVISION IS '修正区分 既存システム物理名: XXA49B';
COMMENT ON COLUMN ADDRESS_MASTER.DATA_DIVISION IS 'データ区分 既存システム物理名: XXA50B';
COMMENT ON COLUMN ADDRESS_MASTER.ADDRESS_COLLECTION_DIVISION IS '住所収録区分 既存システム物理名: XXA51B';
