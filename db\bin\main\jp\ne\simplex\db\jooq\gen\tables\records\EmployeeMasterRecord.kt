/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.EmployeeMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.EmployeeMasterPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 社員マスタ 既存システム物理名: XXEMPP
 */
@Suppress("UNCHECKED_CAST")
open class EmployeeMasterRecord private constructor() : UpdatableRecordImpl<EmployeeMasterRecord>(EmployeeMasterTable.EMPLOYEE_MASTER) {

    open var employeeNumber: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var nameKanji: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    open var nameKana: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var gender: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var dateOfBirth: Long?
        set(value): Unit = set(4, value)
        get(): Long? = get(4) as Long?

    open var joinDate: Long?
        set(value): Unit = set(5, value)
        get(): Long? = get(5) as Long?

    open var resignationDate: Long?
        set(value): Unit = set(6, value)
        get(): Long? = get(6) as Long?

    open var companyCode: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var affiliationStartDate: Long?
        set(value): Unit = set(8, value)
        get(): Long? = get(8) as Long?

    open var affiliationCode: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var positionCode: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var jobTypeCode: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var recruitmentCategory: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var employmentCategory: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var accountingAffiliationCode: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var dedicatedTechnicianCategory_1: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var assignedAffiliationCode_1: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var dedicatedTechnicianCategory_2: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var assignedAffiliationCode_2: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var dedicatedTechnicianCategory_3: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var assignedAffiliationCode_3: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var dedicatedTechnicianCategory_4: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var assignedAffiliationCode_4: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var dedicatedTechnicianCategory_5: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var assignedAffiliationCode_5: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var newOldSalaryUseCategory: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var newOldSalaryUseDate: Long?
        set(value): Unit = set(26, value)
        get(): Long? = get(26) as Long?

    open var positionPromotionDemotionDate: Long?
        set(value): Unit = set(27, value)
        get(): Long? = get(27) as Long?

    open var organizationManagementTransferDate: Long?
        set(value): Unit = set(28, value)
        get(): Long? = get(28) as Long?

    open var organizationManagementPositionCode: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var originalAffiliationStartDate: Long?
        set(value): Unit = set(30, value)
        get(): Long? = get(30) as Long?

    open var originalAffiliationCompanyCode: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var employmentCategoryStartDate: Long?
        set(value): Unit = set(32, value)
        get(): Long? = get(32) as Long?

    open var deleteCategory: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var creator: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var creationProgram: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var creationDate: Long?
        set(value): Unit = set(36, value)
        get(): Long? = get(36) as Long?

    open var creationTime: Int?
        set(value): Unit = set(37, value)
        get(): Int? = get(37) as Int?

    open var updater: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var updateProgram: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var updateDate: Long?
        set(value): Unit = set(40, value)
        get(): Long? = get(40) as Long?

    open var updateTime: Int?
        set(value): Unit = set(41, value)
        get(): Int? = get(41) as Int?

    open var terminalId: String?
        set(value): Unit = set(42, value)
        get(): String? = get(42) as String?

    open var qualificationPromotionDemotionDate: Long?
        set(value): Unit = set(43, value)
        get(): Long? = get(43) as Long?

    open var qualificationCode: String?
        set(value): Unit = set(44, value)
        get(): String? = get(44) as String?

    open var storeTransferDate: Long?
        set(value): Unit = set(45, value)
        get(): Long? = get(45) as Long?

    open var satelliteStoreCode: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var reserve_1: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var reserve_2: String?
        set(value): Unit = set(48, value)
        get(): String? = get(48) as String?

    open var reserve_3: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    open var reserve_4: String?
        set(value): Unit = set(50, value)
        get(): String? = get(50) as String?

    open var reserve_5: String?
        set(value): Unit = set(51, value)
        get(): String? = get(51) as String?

    open var reserve_6: String?
        set(value): Unit = set(52, value)
        get(): String? = get(52) as String?

    open var reserve_7: String?
        set(value): Unit = set(53, value)
        get(): String? = get(53) as String?

    open var reserve_8: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var reserve_9: String?
        set(value): Unit = set(55, value)
        get(): String? = get(55) as String?

    open var reserve_10: Long?
        set(value): Unit = set(56, value)
        get(): Long? = get(56) as Long?

    open var reserve_11: Long?
        set(value): Unit = set(57, value)
        get(): Long? = get(57) as Long?

    open var reserve_12: Long?
        set(value): Unit = set(58, value)
        get(): Long? = get(58) as Long?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised EmployeeMasterRecord
     */
    constructor(employeeNumber: String, nameKanji: String? = null, nameKana: String? = null, gender: String? = null, dateOfBirth: Long? = null, joinDate: Long? = null, resignationDate: Long? = null, companyCode: String? = null, affiliationStartDate: Long? = null, affiliationCode: String? = null, positionCode: String? = null, jobTypeCode: String? = null, recruitmentCategory: String? = null, employmentCategory: String? = null, accountingAffiliationCode: String? = null, dedicatedTechnicianCategory_1: String? = null, assignedAffiliationCode_1: String? = null, dedicatedTechnicianCategory_2: String? = null, assignedAffiliationCode_2: String? = null, dedicatedTechnicianCategory_3: String? = null, assignedAffiliationCode_3: String? = null, dedicatedTechnicianCategory_4: String? = null, assignedAffiliationCode_4: String? = null, dedicatedTechnicianCategory_5: String? = null, assignedAffiliationCode_5: String? = null, newOldSalaryUseCategory: String? = null, newOldSalaryUseDate: Long? = null, positionPromotionDemotionDate: Long? = null, organizationManagementTransferDate: Long? = null, organizationManagementPositionCode: String? = null, originalAffiliationStartDate: Long? = null, originalAffiliationCompanyCode: String? = null, employmentCategoryStartDate: Long? = null, deleteCategory: String? = null, creator: String? = null, creationProgram: String? = null, creationDate: Long? = null, creationTime: Int? = null, updater: String? = null, updateProgram: String? = null, updateDate: Long? = null, updateTime: Int? = null, terminalId: String? = null, qualificationPromotionDemotionDate: Long? = null, qualificationCode: String? = null, storeTransferDate: Long? = null, satelliteStoreCode: String? = null, reserve_1: String? = null, reserve_2: String? = null, reserve_3: String? = null, reserve_4: String? = null, reserve_5: String? = null, reserve_6: String? = null, reserve_7: String? = null, reserve_8: String? = null, reserve_9: String? = null, reserve_10: Long? = null, reserve_11: Long? = null, reserve_12: Long? = null): this() {
        this.employeeNumber = employeeNumber
        this.nameKanji = nameKanji
        this.nameKana = nameKana
        this.gender = gender
        this.dateOfBirth = dateOfBirth
        this.joinDate = joinDate
        this.resignationDate = resignationDate
        this.companyCode = companyCode
        this.affiliationStartDate = affiliationStartDate
        this.affiliationCode = affiliationCode
        this.positionCode = positionCode
        this.jobTypeCode = jobTypeCode
        this.recruitmentCategory = recruitmentCategory
        this.employmentCategory = employmentCategory
        this.accountingAffiliationCode = accountingAffiliationCode
        this.dedicatedTechnicianCategory_1 = dedicatedTechnicianCategory_1
        this.assignedAffiliationCode_1 = assignedAffiliationCode_1
        this.dedicatedTechnicianCategory_2 = dedicatedTechnicianCategory_2
        this.assignedAffiliationCode_2 = assignedAffiliationCode_2
        this.dedicatedTechnicianCategory_3 = dedicatedTechnicianCategory_3
        this.assignedAffiliationCode_3 = assignedAffiliationCode_3
        this.dedicatedTechnicianCategory_4 = dedicatedTechnicianCategory_4
        this.assignedAffiliationCode_4 = assignedAffiliationCode_4
        this.dedicatedTechnicianCategory_5 = dedicatedTechnicianCategory_5
        this.assignedAffiliationCode_5 = assignedAffiliationCode_5
        this.newOldSalaryUseCategory = newOldSalaryUseCategory
        this.newOldSalaryUseDate = newOldSalaryUseDate
        this.positionPromotionDemotionDate = positionPromotionDemotionDate
        this.organizationManagementTransferDate = organizationManagementTransferDate
        this.organizationManagementPositionCode = organizationManagementPositionCode
        this.originalAffiliationStartDate = originalAffiliationStartDate
        this.originalAffiliationCompanyCode = originalAffiliationCompanyCode
        this.employmentCategoryStartDate = employmentCategoryStartDate
        this.deleteCategory = deleteCategory
        this.creator = creator
        this.creationProgram = creationProgram
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updater = updater
        this.updateProgram = updateProgram
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.terminalId = terminalId
        this.qualificationPromotionDemotionDate = qualificationPromotionDemotionDate
        this.qualificationCode = qualificationCode
        this.storeTransferDate = storeTransferDate
        this.satelliteStoreCode = satelliteStoreCode
        this.reserve_1 = reserve_1
        this.reserve_2 = reserve_2
        this.reserve_3 = reserve_3
        this.reserve_4 = reserve_4
        this.reserve_5 = reserve_5
        this.reserve_6 = reserve_6
        this.reserve_7 = reserve_7
        this.reserve_8 = reserve_8
        this.reserve_9 = reserve_9
        this.reserve_10 = reserve_10
        this.reserve_11 = reserve_11
        this.reserve_12 = reserve_12
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised EmployeeMasterRecord
     */
    constructor(value: EmployeeMasterPojo?): this() {
        if (value != null) {
            this.employeeNumber = value.employeeNumber
            this.nameKanji = value.nameKanji
            this.nameKana = value.nameKana
            this.gender = value.gender
            this.dateOfBirth = value.dateOfBirth
            this.joinDate = value.joinDate
            this.resignationDate = value.resignationDate
            this.companyCode = value.companyCode
            this.affiliationStartDate = value.affiliationStartDate
            this.affiliationCode = value.affiliationCode
            this.positionCode = value.positionCode
            this.jobTypeCode = value.jobTypeCode
            this.recruitmentCategory = value.recruitmentCategory
            this.employmentCategory = value.employmentCategory
            this.accountingAffiliationCode = value.accountingAffiliationCode
            this.dedicatedTechnicianCategory_1 = value.dedicatedTechnicianCategory_1
            this.assignedAffiliationCode_1 = value.assignedAffiliationCode_1
            this.dedicatedTechnicianCategory_2 = value.dedicatedTechnicianCategory_2
            this.assignedAffiliationCode_2 = value.assignedAffiliationCode_2
            this.dedicatedTechnicianCategory_3 = value.dedicatedTechnicianCategory_3
            this.assignedAffiliationCode_3 = value.assignedAffiliationCode_3
            this.dedicatedTechnicianCategory_4 = value.dedicatedTechnicianCategory_4
            this.assignedAffiliationCode_4 = value.assignedAffiliationCode_4
            this.dedicatedTechnicianCategory_5 = value.dedicatedTechnicianCategory_5
            this.assignedAffiliationCode_5 = value.assignedAffiliationCode_5
            this.newOldSalaryUseCategory = value.newOldSalaryUseCategory
            this.newOldSalaryUseDate = value.newOldSalaryUseDate
            this.positionPromotionDemotionDate = value.positionPromotionDemotionDate
            this.organizationManagementTransferDate = value.organizationManagementTransferDate
            this.organizationManagementPositionCode = value.organizationManagementPositionCode
            this.originalAffiliationStartDate = value.originalAffiliationStartDate
            this.originalAffiliationCompanyCode = value.originalAffiliationCompanyCode
            this.employmentCategoryStartDate = value.employmentCategoryStartDate
            this.deleteCategory = value.deleteCategory
            this.creator = value.creator
            this.creationProgram = value.creationProgram
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updater = value.updater
            this.updateProgram = value.updateProgram
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.terminalId = value.terminalId
            this.qualificationPromotionDemotionDate = value.qualificationPromotionDemotionDate
            this.qualificationCode = value.qualificationCode
            this.storeTransferDate = value.storeTransferDate
            this.satelliteStoreCode = value.satelliteStoreCode
            this.reserve_1 = value.reserve_1
            this.reserve_2 = value.reserve_2
            this.reserve_3 = value.reserve_3
            this.reserve_4 = value.reserve_4
            this.reserve_5 = value.reserve_5
            this.reserve_6 = value.reserve_6
            this.reserve_7 = value.reserve_7
            this.reserve_8 = value.reserve_8
            this.reserve_9 = value.reserve_9
            this.reserve_10 = value.reserve_10
            this.reserve_11 = value.reserve_11
            this.reserve_12 = value.reserve_12
            resetChangedOnNotNull()
        }
    }
}
