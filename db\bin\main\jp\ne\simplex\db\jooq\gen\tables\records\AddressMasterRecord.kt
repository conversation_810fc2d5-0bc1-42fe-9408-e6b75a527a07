/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.AddressMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.AddressMasterPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 住所マスタ 既存システム物理名: XXADRP
 */
@Suppress("UNCHECKED_CAST")
open class AddressMasterRecord private constructor() : UpdatableRecordImpl<AddressMasterRecord>(AddressMasterTable.ADDRESS_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var reflectDate: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var deleteFlag: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var addressCode: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    open var organizationNumber: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var newAddressCode: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var prefectureKanaName: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var cityKanaName: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var townKanaName: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var commonKanaName: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var postalCode: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var prefectureKanaCount: Byte?
        set(value): Unit = set(16, value)
        get(): Byte? = get(16) as Byte?

    open var cityKanaCount: Byte?
        set(value): Unit = set(17, value)
        get(): Byte? = get(17) as Byte?

    open var townKanaCount: Byte?
        set(value): Unit = set(18, value)
        get(): Byte? = get(18) as Byte?

    open var commonKanaCount: Byte?
        set(value): Unit = set(19, value)
        get(): Byte? = get(19) as Byte?

    open var kanaTotalCount: Byte?
        set(value): Unit = set(20, value)
        get(): Byte? = get(20) as Byte?

    open var prefectureKanjiName: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var cityKanjiName: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var townKanjiName: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var commonKanjiName: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var postalCodeKanji: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var prefectureKanjiCount: Byte?
        set(value): Unit = set(26, value)
        get(): Byte? = get(26) as Byte?

    open var cityKanjiCount: Byte?
        set(value): Unit = set(27, value)
        get(): Byte? = get(27) as Byte?

    open var townKanjiCount: Byte?
        set(value): Unit = set(28, value)
        get(): Byte? = get(28) as Byte?

    open var commonKanjiCount: Byte?
        set(value): Unit = set(29, value)
        get(): Byte? = get(29) as Byte?

    open var kanjiTotalCount: Byte?
        set(value): Unit = set(30, value)
        get(): Byte? = get(30) as Byte?

    open var abolitionDate: Int?
        set(value): Unit = set(31, value)
        get(): Int? = get(31) as Int?

    open var implementationDate: Int?
        set(value): Unit = set(32, value)
        get(): Int? = get(32) as Int?

    open var prefectureNameNotRequiredCode: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var lotChangeDivision: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var splitDivision: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var currentNewCdCoexistenceMonth1: Int?
        set(value): Unit = set(36, value)
        get(): Int? = get(36) as Int?

    open var currentNewCdCoexistenceMonth2: Int?
        set(value): Unit = set(37, value)
        get(): Int? = get(37) as Int?

    open var lotChangeMonth1: Int?
        set(value): Unit = set(38, value)
        get(): Int? = get(38) as Int?

    open var lotChangeMonth2: Int?
        set(value): Unit = set(39, value)
        get(): Int? = get(39) as Int?

    open var nomenclatureChangeMonth: Int?
        set(value): Unit = set(40, value)
        get(): Int? = get(40) as Int?

    open var postalCodeChangeMonth: Int?
        set(value): Unit = set(41, value)
        get(): Int? = get(41) as Int?

    open var adminChangeDivision: String?
        set(value): Unit = set(42, value)
        get(): String? = get(42) as String?

    open var adminChangeMonth: Int?
        set(value): Unit = set(43, value)
        get(): Int? = get(43) as Int?

    open var changeMonth: Int?
        set(value): Unit = set(44, value)
        get(): Int? = get(44) as Int?

    open var jisCode: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var newJisCode: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var correctionDivision: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var dataDivision: String?
        set(value): Unit = set(48, value)
        get(): String? = get(48) as String?

    open var addressCollectionDivision: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised AddressMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, reflectDate: Int? = null, deleteFlag: String? = null, addressCode: String, organizationNumber: String? = null, newAddressCode: String? = null, prefectureKanaName: String? = null, cityKanaName: String? = null, townKanaName: String? = null, commonKanaName: String? = null, postalCode: String? = null, prefectureKanaCount: Byte? = null, cityKanaCount: Byte? = null, townKanaCount: Byte? = null, commonKanaCount: Byte? = null, kanaTotalCount: Byte? = null, prefectureKanjiName: String? = null, cityKanjiName: String? = null, townKanjiName: String? = null, commonKanjiName: String? = null, postalCodeKanji: String? = null, prefectureKanjiCount: Byte? = null, cityKanjiCount: Byte? = null, townKanjiCount: Byte? = null, commonKanjiCount: Byte? = null, kanjiTotalCount: Byte? = null, abolitionDate: Int? = null, implementationDate: Int? = null, prefectureNameNotRequiredCode: String? = null, lotChangeDivision: String? = null, splitDivision: String? = null, currentNewCdCoexistenceMonth1: Int? = null, currentNewCdCoexistenceMonth2: Int? = null, lotChangeMonth1: Int? = null, lotChangeMonth2: Int? = null, nomenclatureChangeMonth: Int? = null, postalCodeChangeMonth: Int? = null, adminChangeDivision: String? = null, adminChangeMonth: Int? = null, changeMonth: Int? = null, jisCode: String? = null, newJisCode: String? = null, correctionDivision: String? = null, dataDivision: String? = null, addressCollectionDivision: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.reflectDate = reflectDate
        this.deleteFlag = deleteFlag
        this.addressCode = addressCode
        this.organizationNumber = organizationNumber
        this.newAddressCode = newAddressCode
        this.prefectureKanaName = prefectureKanaName
        this.cityKanaName = cityKanaName
        this.townKanaName = townKanaName
        this.commonKanaName = commonKanaName
        this.postalCode = postalCode
        this.prefectureKanaCount = prefectureKanaCount
        this.cityKanaCount = cityKanaCount
        this.townKanaCount = townKanaCount
        this.commonKanaCount = commonKanaCount
        this.kanaTotalCount = kanaTotalCount
        this.prefectureKanjiName = prefectureKanjiName
        this.cityKanjiName = cityKanjiName
        this.townKanjiName = townKanjiName
        this.commonKanjiName = commonKanjiName
        this.postalCodeKanji = postalCodeKanji
        this.prefectureKanjiCount = prefectureKanjiCount
        this.cityKanjiCount = cityKanjiCount
        this.townKanjiCount = townKanjiCount
        this.commonKanjiCount = commonKanjiCount
        this.kanjiTotalCount = kanjiTotalCount
        this.abolitionDate = abolitionDate
        this.implementationDate = implementationDate
        this.prefectureNameNotRequiredCode = prefectureNameNotRequiredCode
        this.lotChangeDivision = lotChangeDivision
        this.splitDivision = splitDivision
        this.currentNewCdCoexistenceMonth1 = currentNewCdCoexistenceMonth1
        this.currentNewCdCoexistenceMonth2 = currentNewCdCoexistenceMonth2
        this.lotChangeMonth1 = lotChangeMonth1
        this.lotChangeMonth2 = lotChangeMonth2
        this.nomenclatureChangeMonth = nomenclatureChangeMonth
        this.postalCodeChangeMonth = postalCodeChangeMonth
        this.adminChangeDivision = adminChangeDivision
        this.adminChangeMonth = adminChangeMonth
        this.changeMonth = changeMonth
        this.jisCode = jisCode
        this.newJisCode = newJisCode
        this.correctionDivision = correctionDivision
        this.dataDivision = dataDivision
        this.addressCollectionDivision = addressCollectionDivision
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised AddressMasterRecord
     */
    constructor(value: AddressMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.reflectDate = value.reflectDate
            this.deleteFlag = value.deleteFlag
            this.addressCode = value.addressCode
            this.organizationNumber = value.organizationNumber
            this.newAddressCode = value.newAddressCode
            this.prefectureKanaName = value.prefectureKanaName
            this.cityKanaName = value.cityKanaName
            this.townKanaName = value.townKanaName
            this.commonKanaName = value.commonKanaName
            this.postalCode = value.postalCode
            this.prefectureKanaCount = value.prefectureKanaCount
            this.cityKanaCount = value.cityKanaCount
            this.townKanaCount = value.townKanaCount
            this.commonKanaCount = value.commonKanaCount
            this.kanaTotalCount = value.kanaTotalCount
            this.prefectureKanjiName = value.prefectureKanjiName
            this.cityKanjiName = value.cityKanjiName
            this.townKanjiName = value.townKanjiName
            this.commonKanjiName = value.commonKanjiName
            this.postalCodeKanji = value.postalCodeKanji
            this.prefectureKanjiCount = value.prefectureKanjiCount
            this.cityKanjiCount = value.cityKanjiCount
            this.townKanjiCount = value.townKanjiCount
            this.commonKanjiCount = value.commonKanjiCount
            this.kanjiTotalCount = value.kanjiTotalCount
            this.abolitionDate = value.abolitionDate
            this.implementationDate = value.implementationDate
            this.prefectureNameNotRequiredCode = value.prefectureNameNotRequiredCode
            this.lotChangeDivision = value.lotChangeDivision
            this.splitDivision = value.splitDivision
            this.currentNewCdCoexistenceMonth1 = value.currentNewCdCoexistenceMonth1
            this.currentNewCdCoexistenceMonth2 = value.currentNewCdCoexistenceMonth2
            this.lotChangeMonth1 = value.lotChangeMonth1
            this.lotChangeMonth2 = value.lotChangeMonth2
            this.nomenclatureChangeMonth = value.nomenclatureChangeMonth
            this.postalCodeChangeMonth = value.postalCodeChangeMonth
            this.adminChangeDivision = value.adminChangeDivision
            this.adminChangeMonth = value.adminChangeMonth
            this.changeMonth = value.changeMonth
            this.jisCode = value.jisCode
            this.newJisCode = value.newJisCode
            this.correctionDivision = value.correctionDivision
            this.dataDivision = value.dataDivision
            this.addressCollectionDivision = value.addressCollectionDivision
            resetChangedOnNotNull()
        }
    }
}
