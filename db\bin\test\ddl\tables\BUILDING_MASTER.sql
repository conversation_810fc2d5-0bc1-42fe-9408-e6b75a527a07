-- TABLE: BUILDING_MASTER(建物マスタ)

CREATE TABLE BUILDING_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_FLAG                          numeric(1,0)                  
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    BULK_LEASE_FLAG                              numeric(1,0)                  
,    BUILDING_NAME                                varchar(32)                   
,    BUILDING_CATEGORY                            varchar(1)                    
,    PRODUCT_NAME_CODE_ST                         numeric(3,0)                  
,    PRODUCT_GRADE_CODE_ST                        numeric(2,0)                  
,    PRODUCT_TYPE_SERIAL_ST                       numeric(3,0)                  
,    BUILDING_TYPE_CODE                           varchar(3)                    
,    BUILDING_STRUCTURE_CATEGORY                  varchar(2)                    
,    FOUNDATION_SHAPE_CATEGORY                    varchar(1)                    
,    PREFECTURE_CODE                              varchar(2)                    
,    CITY_CODE                                    varchar(2)                    
,    TOWN_CODE                                    varchar(6)                    
,    ADDRESS_DETAIL                               varchar(62)                   
,    BUILDING_NAME2                               varchar(32)                   
,    ORIENTATION_CATEGORY                         varchar(2)                    
,    BUSINESS_USE_ROOM_COUNT                      numeric(3,0)                  
,    RESIDENTIAL_USE_ROOM_COUNT                   numeric(3,0)                  
,    FLOORS_ABOVE_GROUND                          numeric(3,0)                  
,    FLOORS_BELOW_GROUND                          numeric(1,0)                  
,    TOTAL_FLOOR_AREA_SQM                         numeric(7,2)                  
,    CONSTRUCTION_FLOOR_AREA_SQM                  numeric(7,2)                  
,    WAREHOUSE_FACTORY_AREA_SQM                   numeric(7,2)                  
,    OFFICE_FLAG                                  numeric(1,0)                  
,    BUILDING_COVERAGE_RATIO                      numeric(5,2)                  
,    FLOOR_AREA_RATIO                             numeric(5,2)                  
,    RESIDENTIAL_CATEGORY                         varchar(1)                    
,    EXTERIOR_WALL_CATEGORY                       varchar(1)                    
,    ROOF_CATEGORY                                varchar(2)                    
,    APPROACH_ENTRANCE_FLAG                       numeric(1,0)                  
,    FENCE_FLAG                                   numeric(1,0)                  
,    APPROACH_LIGHT_SECURITY_LIGHT_FLAG           numeric(1,0)                  
,    LANDSCAPING_FLAG                             numeric(1,0)                  
,    WATER_TAP_FLAG                               numeric(1,0)                  
,    GARBAGE_STATION_FLAG                         numeric(1,0)                  
,    PARKING_SPACE_PER_UNIT                       numeric(1,0)                  
,    TOTAL_PARKING_SPACES                         numeric(3,0)                  
,    LEASED_PARKING_SPACES                        numeric(3,0)                  
,    S_LEASED_PARKING_SPACES                      numeric(3,0)                  
,    BICYCLE_PARKING_FLAG                         numeric(1,0)                  
,    WATER_SUPPLY_CATEGORY                        varchar(1)                    
,    SEWAGE_CATEGORY                              varchar(1)                    
,    GAS_CATEGORY                                 varchar(1)                    
,    TOILET_CATEGORY                              varchar(1)                    
,    TELEPHONE_CATEGORY                           varchar(1)                    
,    ELECTRICITY_FLAG                             numeric(1,0)                  
,    EQUIPMENT_CODE                               varchar(30)                   
,    FEATURE_CODE                                 varchar(12)                   
,    REMARKS_CODE                                 varchar(10)                   
,    SEPTIC_TANK_ASSESSMENT                       numeric(5,0)                  
,    SEPTIC_TANK_CATEGORY                         varchar(1)                    
,    LAND_CODE                                    varchar(8)                    
,    RAILWAY_LINE_CODE                            varchar(4)                    
,    STATION_CODE                                 varchar(4)                    
,    NEAREST_BUS_LINE_NAME                        varchar(22)                   
,    NEAREST_BUS_STOP_NAME                        varchar(22)                   
,    BUS_RIDE_TIME                                numeric(3,0)                  
,    WALKING_TIME                                 numeric(3,0)                  
,    RAILWAY_NEAREST_STATION_DISTANCE             numeric(3,1)                  
,    SURROUNDING_FACILITIES_DISTANCE              numeric(3,0)                  
,    SURROUNDING_FACILITIES_TIME                  numeric(3,0)                  
,    SURROUNDING_FACILITIES_CODE                  varchar(40)                   
,    ORDER_CODE                                   varchar(7)                    
,    CONTRACTOR_CONSTRUCTION_FLAG                 numeric(1,0)                  
,    THIRD_PARTY_CONSTRUCTION_FLAG                numeric(1,0)                  
,    LANDLORD_CODE                                varchar(8)                    
,    MANAGEMENT_FLAG                              numeric(1,0)                  
,    CONSENT_FORM_COLLECTION_FLAG                 numeric(1,0)                  
,    EXTERNAL_VACANT_CATEGORY                     varchar(1)                    
,    ELECTRICITY_SUPPLIER_CODE                    varchar(2)                    
,    ELECTRICITY_METER_CATEGORY                   varchar(1)                    
,    POWER_FLAG                                   numeric(1,0)                  
,    GAS_METER_CATEGORY                           varchar(1)                    
,    WATER_SUPPLY_CATEGORY_FOR_DISASTER           varchar(1)                    
,    WATER_METER_CATEGORY                         varchar(1)                    
,    ELECTRICAL_EQUIPMENT_MANAGER_CODE            varchar(9)                    
,    GAS_EQUIPMENT_MANAGER_CODE                   varchar(9)                    
,    SEWAGE_EQUIPMENT_MANAGER_CODE                varchar(9)                    
,    WATER_TANK_MANAGER_CODE                      varchar(9)                    
,    SEPTIC_TANK_MANAGER_CODE                     varchar(9)                    
,    BUILDING_SITE_CLEANING_MANAGER_CODE          varchar(9)                    
,    FIRE_EQUIPMENT_MANAGER_CODE                  varchar(9)                    
,    CONTRACT_HOLD_FLAG                           numeric(1,0)                  
,    APPLICATION_PROCESS_HOLD_FLAG                numeric(1,0)                  
,    CONSTRUCTION_PROCESS_HOLD_FLAG               numeric(1,0)                  
,    CONSTRUCTION_STOP_FLAG                       numeric(1,0)                  
,    INSPECTION_FLAG                              numeric(1,0)                  
,    GOVERNMENT_INSPECTION_ENTRY_DATE             numeric(8,0)                  
,    GOVERNMENT_INSPECTION_COMPLETION_DATE        numeric(8,0)                  
,    TEMPORARY_USE_INSPECTION_ENTRY_DATE          numeric(8,0)                  
,    TEMPORARY_USE_APPLICATION_APPROVAL_DATE      numeric(8,0)                  
,    STANDARD_DEVIATION_FLAG                      numeric(1,0)                  
,    VACANT_HOUSE_ACCOUNTING_DATE                 numeric(8,0)                  
,    RENT_GUARANTEE_DATE                          numeric(8,0)                  
,    COMPLETION_EXPECTED_DATE                     numeric(8,0)                  
,    COMPLETION_DELIVERY_DATE                     numeric(8,0)                  
,    CONTRACTOR_CONSTRUCTION_COMPLETION_DATE      numeric(8,0)                  
,    HOUSING_LOAN_CORPORATION_LOAN_FLAG           numeric(1,0)                  
,    LOAN_CORPORATION_APPROVAL_START_DATE         numeric(8,0)                  
,    APPLICATION_ACCEPTANCE_DATE                  numeric(8,0)                  
,    PRINCIPAL_ADVANCE_FLAG                       numeric(1,0)                  
,    TENANT_RESTRICTION_CODE                      varchar(2)                    
,    CONTRACT_BRANCH_CODE                         varchar(6)                    
,    TENANT_RECRUITMENT_BRANCH_CODE               varchar(6)                    
,    MANAGEMENT_BRANCH_CODE                       varchar(6)                    
,    FEE_BEARING_LANDLORD                         numeric(3,0)                  
,    FEE_BEARING_TENANT                           numeric(3,0)                  
,    FEE_DISTRIBUTION_ORIGIN                      numeric(3,0)                  
,    FEE_DISTRIBUTION_TENANT                      numeric(3,0)                  
,    TENANT_RECRUITMENT_NOTES                     varchar(182)                  
,    AREA_CATEGORY_1                              varchar(2)                    
,    AREA_BASE_CODE_1                             varchar(3)                    
,    AREA_DETAIL_CODE_1                           varchar(2)                    
,    AREA_CATEGORY_2                              varchar(2)                    
,    AREA_BASE_CODE_2                             varchar(3)                    
,    AREA_DETAIL_CODE_2                           varchar(2)                    
,    AREA_CATEGORY_3                              varchar(2)                    
,    AREA_BASE_CODE_3                             varchar(3)                    
,    AREA_DETAIL_CODE_3                           varchar(2)                    
,    AREA_CATEGORY_4                              varchar(2)                    
,    AREA_BASE_CODE_4                             varchar(3)                    
,    AREA_DETAIL_CODE_4                           varchar(2)                    
,    AREA_CATEGORY_5                              varchar(2)                    
,    AREA_BASE_CODE_5                             varchar(3)                    
,    AREA_DETAIL_CODE_5                           varchar(2)                    
,    AREA_CATEGORY_6                              varchar(2)                    
,    AREA_BASE_CODE_6                             varchar(3)                    
,    AREA_DETAIL_CODE_6                           varchar(2)                    
,    AREA_CATEGORY_7                              varchar(2)                    
,    AREA_BASE_CODE_7                             varchar(3)                    
,    AREA_DETAIL_CODE_7                           varchar(2)                    
,    BUILDING_MANAGER_CODE                        varchar(6)                    
,    RENT_MANAGER_CODE                            varchar(6)                    
,    CONTRACT_MANAGER_CODE                        varchar(6)                    
,    HOUSE_COM_PURCHASE_FLAG                      numeric(1,0)                  
,    PREFECTURE_CODE2                             varchar(2)                    
,    CITY_CODE2                                   varchar(2)                    
,    TOWN_CODE2                                   varchar(6)                    
,    ADDRESS_DETAIL2                              varchar(62)                   
,    OWNER_CODE_1                                 varchar(8)                    
,    OWNER_CODE_2                                 varchar(8)                    
,    OWNER_CODE_3                                 varchar(8)                    
,    OWNERSHIP_DETAIL_1                           varchar(26)                   
,    OWNERSHIP_DETAIL_2                           varchar(26)                   
,    OWNERSHIP_DETAIL_3                           varchar(26)                   
,    OWNERSHIP_DETAIL_4                           varchar(26)                   
,    OWNERSHIP_DETAIL_5                           varchar(26)                   
,    NON_OWNERSHIP_DETAIL_1                       varchar(26)                   
,    NON_OWNERSHIP_DETAIL_2                       varchar(26)                   
,    NON_OWNERSHIP_DETAIL_3                       varchar(26)                   
,    NON_OWNERSHIP_DETAIL_4                       varchar(26)                   
,    NON_OWNERSHIP_DETAIL_5                       varchar(26)                   
,    MANAGEMENT_START_DATE                        numeric(8,0)                  
,    MANAGEMENT_END_DATE                          numeric(8,0)                  
,    AGREED_TERMINATION_DATE                      numeric(8,0)                  
,    BRANCH_CODE                                  varchar(6)                    
,    MANAGEMENT_STAFF_CODE                        varchar(5)                    
,    MANAGEMENT_START_DATE2                       numeric(8,0)                  
,    INTERFACE_FLAG                               numeric(1,0)                  
,    TENANT_EXTRACTION_FLAG                       numeric(1,0)                  
,    DATA_MIGRATION_SOURCE_KEY_1                  varchar(15)                   
,    DATA_MIGRATION_SOURCE_KEY_2                  varchar(15)                   
,    NEAREST_BUS_STOP_DISTANCE                    numeric(3,1)                  
,    CATCHCOPY_CONTENT                            varchar(62)                   
,    RECRUITMENT_INFO_INPUT_FLAG                  numeric(1,0)                  
,    LIST_OUTPUT_FLAG                             numeric(1,0)                  
,    NEW_BUILD_RENT_CATEGORY                      varchar(1)                    
,    COMPANY_CODE                                 varchar(3)                    
,    DEPOSIT_CATEGORY                             varchar(1)                    
,    GUARANTEE_DEDUCT_CATEGORY                    varchar(1)                    
,    NEW_HOUSE_GUARANTEE_10Y_PK_COUNT             numeric(3,0)                  
,    NEW_GUARANTEE_RATE                           numeric(5,2)                  
,    NEW_GUARANTEE_MANAGEMENT_RATE                numeric(5,2)                  
,    CONTRACT_MUTUAL_AID_FEE_RATE                 numeric(5,2)                  
,    KEY_MONEY_FLAG                               varchar(1)                    
,    MANAGEMENT_FEE_ACCOUNTING_CATEGORY           varchar(1)                    
,    SCREENING_BRANCH_CODE                        varchar(6)                    
,    RESIDENCE_DISPLAY_CATEGORY                   varchar(1)                    
,    LANDLORD_SIGNATURE_CONSENT_CATEGORY          varchar(1)                    
,    LOAN_CORPORATION_RESTORE_CATEGORY            varchar(1)                    
,    LEASE_CONTRACT_NUMBER                        varchar(3)                    
,    GUARANTOR_NOT_REQUIRED_CATEGORY              numeric(1,0)                  
,    COM_PARTNERSHIP_CATEGORY                     numeric(1,0)                  
,    CONTRACTOR_COMPANY_CATEGORY                  numeric(1,0)                  
,    SPECIAL_RENT                                 numeric(1,0)                  
,    REAL_ESTATE_PARTNERSHIP_CATEGORY             numeric(1,0)                  
,    SUPPORT_MECHANISM_CATEGORY                   numeric(1,0)                  
,    TELEPHONE_CONSENT_CATEGORY                   numeric(1,0)                  
,    BUILDING_NAME_FLAG                           numeric(1,0)                  
,    FIRE_RESISTANT_STRUCTURE_CATEGORY            numeric(1,0)                  
,    MANAGEMENT_INHERITANCE_CATEGORY              numeric(1,0)                  
,    UNUSED_9                                     numeric(1,0)                  
,    MANAGEMENT_STAFF_CODE_ST                     varchar(6)                    
,    DAIKEN_BRANCH_CODE                           varchar(6)                    
,    HOUSE_NUMBER                                 varchar(62)                   
,    MARKETING_BRANCH_OFFICE_CD                   varchar(3)                    
,    CONSTRAINT PK_BUILDING_MASTER PRIMARY KEY (BUILDING_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BUILDING_MASTER IS '建物マスタ 既存システム物理名: ECMD0P';
COMMENT ON COLUMN BUILDING_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: ECM01D 0：管理契約(大東) 1：サブリース 2：保証10年型 3：保証30年型 4：30年一括借上 5：管理契約(パートナーズ) 6：入居賃料連動 7：長期賃料変更無';
COMMENT ON COLUMN BUILDING_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: ECM02H';
COMMENT ON COLUMN BUILDING_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: ECM03D';
COMMENT ON COLUMN BUILDING_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: ECM04H';
COMMENT ON COLUMN BUILDING_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: ECM05N';
COMMENT ON COLUMN BUILDING_MASTER.UPDATER IS '更新者 既存システム物理名: ECM06C';
COMMENT ON COLUMN BUILDING_MASTER.LOGICAL_DELETE_FLAG IS '論理削除サイン 既存システム物理名: ECM07S';
COMMENT ON COLUMN BUILDING_MASTER.BUILDING_CODE IS '建物コード 既存システム物理名: ECMABC';
COMMENT ON COLUMN BUILDING_MASTER.BULK_LEASE_FLAG IS '一括借上サイン 既存システム物理名: ECM95S';
COMMENT ON COLUMN BUILDING_MASTER.BUILDING_NAME IS '建物名称 既存システム物理名: ECM08M';
COMMENT ON COLUMN BUILDING_MASTER.BUILDING_CATEGORY IS '建物区分 既存システム物理名: ECMBDB';
COMMENT ON COLUMN BUILDING_MASTER.PRODUCT_NAME_CODE_ST IS '商品名称コード－ST 既存システム物理名: ECMAKC';
COMMENT ON COLUMN BUILDING_MASTER.PRODUCT_GRADE_CODE_ST IS '商品グレードコード－ST 既存システム物理名: ECMALC';
COMMENT ON COLUMN BUILDING_MASTER.PRODUCT_TYPE_SERIAL_ST IS '商品形式連番－ST 既存システム物理名: ECMATC';
COMMENT ON COLUMN BUILDING_MASTER.BUILDING_TYPE_CODE IS '建物種別コード 既存システム物理名: ECMADC';
COMMENT ON COLUMN BUILDING_MASTER.BUILDING_STRUCTURE_CATEGORY IS '建物構造区分 既存システム物理名: ECMBEB';
COMMENT ON COLUMN BUILDING_MASTER.FOUNDATION_SHAPE_CATEGORY IS '基礎形状区分 既存システム物理名: ECMFPB';
COMMENT ON COLUMN BUILDING_MASTER.PREFECTURE_CODE IS '都道府県コード 既存システム物理名: ECMAWC';
COMMENT ON COLUMN BUILDING_MASTER.CITY_CODE IS '市区郡コード 既存システム物理名: ECMAXC';
COMMENT ON COLUMN BUILDING_MASTER.TOWN_CODE IS '町村字通称コード 既存システム物理名: ECMAYC';
COMMENT ON COLUMN BUILDING_MASTER.ADDRESS_DETAIL IS '住所詳細 既存システム物理名: ECM09X';
COMMENT ON COLUMN BUILDING_MASTER.BUILDING_NAME2 IS 'ビル名称 既存システム物理名: ECM20X';
COMMENT ON COLUMN BUILDING_MASTER.ORIENTATION_CATEGORY IS '方位区分 既存システム物理名: ECM45B';
COMMENT ON COLUMN BUILDING_MASTER.BUSINESS_USE_ROOM_COUNT IS '事業用部屋数 既存システム物理名: ECM16Q';
COMMENT ON COLUMN BUILDING_MASTER.RESIDENTIAL_USE_ROOM_COUNT IS '居住用部屋数 既存システム物理名: ECM18Q';
COMMENT ON COLUMN BUILDING_MASTER.FLOORS_ABOVE_GROUND IS '階数(地上) 既存システム物理名: ECM11Q';
COMMENT ON COLUMN BUILDING_MASTER.FLOORS_BELOW_GROUND IS '階数(地下) 既存システム物理名: ECM12Q';
COMMENT ON COLUMN BUILDING_MASTER.TOTAL_FLOOR_AREA_SQM IS '建築延面積(平米) 既存システム物理名: ECM13Q';
COMMENT ON COLUMN BUILDING_MASTER.CONSTRUCTION_FLOOR_AREA_SQM IS '建築施工床面積 (平米) 既存システム物理名: ECM15Q';
COMMENT ON COLUMN BUILDING_MASTER.WAREHOUSE_FACTORY_AREA_SQM IS '倉庫・工場部分の面積(平米) 既存システム物理名: ECM17Q';
COMMENT ON COLUMN BUILDING_MASTER.OFFICE_FLAG IS '事務所サイン 既存システム物理名: ECM19S';
COMMENT ON COLUMN BUILDING_MASTER.BUILDING_COVERAGE_RATIO IS '建ぺい率 既存システム物理名: ECM21Q';
COMMENT ON COLUMN BUILDING_MASTER.FLOOR_AREA_RATIO IS '容積率 既存システム物理名: ECM22Q';
COMMENT ON COLUMN BUILDING_MASTER.RESIDENTIAL_CATEGORY IS '居住区分 既存システム物理名: ECMBZB';
COMMENT ON COLUMN BUILDING_MASTER.EXTERIOR_WALL_CATEGORY IS '外壁区分 既存システム物理名: ECMCJB';
COMMENT ON COLUMN BUILDING_MASTER.ROOF_CATEGORY IS '屋根区分 既存システム物理名: ECMBGB';
COMMENT ON COLUMN BUILDING_MASTER.APPROACH_ENTRANCE_FLAG IS 'アプローチ・エントランスサイン 既存システム物理名: ECM44S';
COMMENT ON COLUMN BUILDING_MASTER.FENCE_FLAG IS 'フェンス有無サイン 既存システム物理名: ECM45S';
COMMENT ON COLUMN BUILDING_MASTER.APPROACH_LIGHT_SECURITY_LIGHT_FLAG IS 'アプローチライト・防犯灯有無サイン 既存システム物理名: ECM47S';
COMMENT ON COLUMN BUILDING_MASTER.LANDSCAPING_FLAG IS '植栽有無サイン 既存システム物理名: ECM48S';
COMMENT ON COLUMN BUILDING_MASTER.WATER_TAP_FLAG IS '散水栓有無サイン 既存システム物理名: ECM49S';
COMMENT ON COLUMN BUILDING_MASTER.GARBAGE_STATION_FLAG IS 'ゴミ置き場有無サイン 既存システム物理名: ECM50S';
COMMENT ON COLUMN BUILDING_MASTER.PARKING_SPACE_PER_UNIT IS '駐車場(全戸各△台) 既存システム物理名: ECM23Q';
COMMENT ON COLUMN BUILDING_MASTER.TOTAL_PARKING_SPACES IS '駐車場総台数 既存システム物理名: ECM24Q';
COMMENT ON COLUMN BUILDING_MASTER.LEASED_PARKING_SPACES IS 'リース駐車場台数 既存システム物理名: ECMLPQ';
COMMENT ON COLUMN BUILDING_MASTER.S_LEASED_PARKING_SPACES IS 'Sリース駐車場台数 既存システム物理名: ECMSPQ';
COMMENT ON COLUMN BUILDING_MASTER.BICYCLE_PARKING_FLAG IS '駐輪場有無サイン 既存システム物理名: ECM25S';
COMMENT ON COLUMN BUILDING_MASTER.WATER_SUPPLY_CATEGORY IS '給水区分 既存システム物理名: ECMCKB';
COMMENT ON COLUMN BUILDING_MASTER.SEWAGE_CATEGORY IS '排水区分 既存システム物理名: ECMEDB';
COMMENT ON COLUMN BUILDING_MASTER.GAS_CATEGORY IS 'ガス区分 既存システム物理名: ECMFFB';
COMMENT ON COLUMN BUILDING_MASTER.TOILET_CATEGORY IS 'トイレ区分 既存システム物理名: ECMFOB';
COMMENT ON COLUMN BUILDING_MASTER.TELEPHONE_CATEGORY IS '電話区分 既存システム物理名: ECMGEB';
COMMENT ON COLUMN BUILDING_MASTER.ELECTRICITY_FLAG IS '電力有無サイン 既存システム物理名: ECM89S';
COMMENT ON COLUMN BUILDING_MASTER.EQUIPMENT_CODE IS '設備コード 既存システム物理名: ECMBHB';
COMMENT ON COLUMN BUILDING_MASTER.FEATURE_CODE IS '特徴コード 既存システム物理名: ECMBIB';
COMMENT ON COLUMN BUILDING_MASTER.REMARKS_CODE IS '備考コード 既存システム物理名: ECMBJB';
COMMENT ON COLUMN BUILDING_MASTER.SEPTIC_TANK_ASSESSMENT IS '浄化槽算定 既存システム物理名: ECM27Q';
COMMENT ON COLUMN BUILDING_MASTER.SEPTIC_TANK_CATEGORY IS '浄化槽区分 既存システム物理名: ECMFJB';
COMMENT ON COLUMN BUILDING_MASTER.LAND_CODE IS '土地コード 既存システム物理名: ECMAAC';
COMMENT ON COLUMN BUILDING_MASTER.RAILWAY_LINE_CODE IS '沿線コード 既存システム物理名: ECMAPC';
COMMENT ON COLUMN BUILDING_MASTER.STATION_CODE IS '駅コード 既存システム物理名: ECMAQC';
COMMENT ON COLUMN BUILDING_MASTER.NEAREST_BUS_LINE_NAME IS '最寄バス沿線名 既存システム物理名: ECM10M';
COMMENT ON COLUMN BUILDING_MASTER.NEAREST_BUS_STOP_NAME IS '最寄バス停名 既存システム物理名: ECM14M';
COMMENT ON COLUMN BUILDING_MASTER.BUS_RIDE_TIME IS 'バス乗車時間 既存システム物理名: ECM82Q';
COMMENT ON COLUMN BUILDING_MASTER.WALKING_TIME IS '所要時間(徒歩) 既存システム物理名: ECM83Q';
COMMENT ON COLUMN BUILDING_MASTER.RAILWAY_NEAREST_STATION_DISTANCE IS '鉄道最寄駅距離 既存システム物理名: ECM84Q';
COMMENT ON COLUMN BUILDING_MASTER.SURROUNDING_FACILITIES_DISTANCE IS '周辺施設条件距離 既存システム物理名: ECM85Q';
COMMENT ON COLUMN BUILDING_MASTER.SURROUNDING_FACILITIES_TIME IS '周辺施設条件時間 既存システム物理名: ECM86Q';
COMMENT ON COLUMN BUILDING_MASTER.SURROUNDING_FACILITIES_CODE IS '周辺施設コード 既存システム物理名: ECM26C';
COMMENT ON COLUMN BUILDING_MASTER.ORDER_CODE IS '受注コード 既存システム物理名: ECMBIC';
COMMENT ON COLUMN BUILDING_MASTER.CONTRACTOR_CONSTRUCTION_FLAG IS '施主施工サイン 既存システム物理名: ECM29S';
COMMENT ON COLUMN BUILDING_MASTER.THIRD_PARTY_CONSTRUCTION_FLAG IS '他社施工サイン 既存システム物理名: ECMB5S';
COMMENT ON COLUMN BUILDING_MASTER.LANDLORD_CODE IS '家主コード 既存システム物理名: ECMAMC';
COMMENT ON COLUMN BUILDING_MASTER.MANAGEMENT_FLAG IS '管理サイン 既存システム物理名: ECM93S';
COMMENT ON COLUMN BUILDING_MASTER.CONSENT_FORM_COLLECTION_FLAG IS '承諾書回収サイン 既存システム物理名: ECM43S';
COMMENT ON COLUMN BUILDING_MASTER.EXTERNAL_VACANT_CATEGORY IS '外部空地区分 既存システム物理名: ECMCLB';
COMMENT ON COLUMN BUILDING_MASTER.ELECTRICITY_SUPPLIER_CODE IS '電力供給会社コード 既存システム物理名: ECMCMB';
COMMENT ON COLUMN BUILDING_MASTER.ELECTRICITY_METER_CATEGORY IS '電気メーター区分 既存システム物理名: ECMCNB';
COMMENT ON COLUMN BUILDING_MASTER.POWER_FLAG IS '動力有無サイン 既存システム物理名: ECM30S';
COMMENT ON COLUMN BUILDING_MASTER.GAS_METER_CATEGORY IS 'ガスメーター区分 既存システム物理名: ECMCOB';
COMMENT ON COLUMN BUILDING_MASTER.WATER_SUPPLY_CATEGORY_FOR_DISASTER IS '重説用水道区分 既存システム物理名: ECMCPB';
COMMENT ON COLUMN BUILDING_MASTER.WATER_METER_CATEGORY IS '水道メーター区分 既存システム物理名: ECMCQB';
COMMENT ON COLUMN BUILDING_MASTER.ELECTRICAL_EQUIPMENT_MANAGER_CODE IS '電気設備管理業者コード 既存システム物理名: ECM31C';
COMMENT ON COLUMN BUILDING_MASTER.GAS_EQUIPMENT_MANAGER_CODE IS 'ガス設備管理業者コード 既存システム物理名: ECM32C';
COMMENT ON COLUMN BUILDING_MASTER.SEWAGE_EQUIPMENT_MANAGER_CODE IS '排水設備管理業者コード 既存システム物理名: ECM33C';
COMMENT ON COLUMN BUILDING_MASTER.WATER_TANK_MANAGER_CODE IS '受水槽設備管理業者コード 既存システム物理名: ECM34C';
COMMENT ON COLUMN BUILDING_MASTER.SEPTIC_TANK_MANAGER_CODE IS '浄化槽設備管理業者コード 既存システム物理名: ECM35C';
COMMENT ON COLUMN BUILDING_MASTER.BUILDING_SITE_CLEANING_MANAGER_CODE IS '建物・敷地清掃業者コード 既存システム物理名: ECM36C';
COMMENT ON COLUMN BUILDING_MASTER.FIRE_EQUIPMENT_MANAGER_CODE IS '消防設備管理業者コード 既存システム物理名: ECM37C';
COMMENT ON COLUMN BUILDING_MASTER.CONTRACT_HOLD_FLAG IS '請負契約保留サイン 既存システム物理名: ECMB6S';
COMMENT ON COLUMN BUILDING_MASTER.APPLICATION_PROCESS_HOLD_FLAG IS '申請工程保留サイン 既存システム物理名: ECMB7S';
COMMENT ON COLUMN BUILDING_MASTER.CONSTRUCTION_PROCESS_HOLD_FLAG IS '工事工程保留サイン 既存システム物理名: ECMB8S';
COMMENT ON COLUMN BUILDING_MASTER.CONSTRUCTION_STOP_FLAG IS '建築中止サイン 既存システム物理名: ECM87S';
COMMENT ON COLUMN BUILDING_MASTER.INSPECTION_FLAG IS '検査指摘有無サイン 既存システム物理名: ECMC3S';
COMMENT ON COLUMN BUILDING_MASTER.GOVERNMENT_INSPECTION_ENTRY_DATE IS '行政検査立入日 既存システム物理名: ECMB9D';
COMMENT ON COLUMN BUILDING_MASTER.GOVERNMENT_INSPECTION_COMPLETION_DATE IS '行政検査完了日 既存システム物理名: ECMC0D';
COMMENT ON COLUMN BUILDING_MASTER.TEMPORARY_USE_INSPECTION_ENTRY_DATE IS '仮使用検査立入日 既存システム物理名: ECMD2D';
COMMENT ON COLUMN BUILDING_MASTER.TEMPORARY_USE_APPLICATION_APPROVAL_DATE IS '仮使用申請許可日 既存システム物理名: ECMC1D';
COMMENT ON COLUMN BUILDING_MASTER.STANDARD_DEVIATION_FLAG IS '基準判定例外サイン 既存システム物理名: ECMC4S';
COMMENT ON COLUMN BUILDING_MASTER.VACANT_HOUSE_ACCOUNTING_DATE IS '空家計上基準日 既存システム物理名: ECMC2D';
COMMENT ON COLUMN BUILDING_MASTER.RENT_GUARANTEE_DATE IS '家賃保証基準日 既存システム物理名: ECMD3D';
COMMENT ON COLUMN BUILDING_MASTER.COMPLETION_EXPECTED_DATE IS '完成予定日 既存システム物理名: ECM96D';
COMMENT ON COLUMN BUILDING_MASTER.COMPLETION_DELIVERY_DATE IS '完成引渡日 既存システム物理名: ECM52D';
COMMENT ON COLUMN BUILDING_MASTER.CONTRACTOR_CONSTRUCTION_COMPLETION_DATE IS '施主施工完了日 既存システム物理名: ECM39D';
COMMENT ON COLUMN BUILDING_MASTER.HOUSING_LOAN_CORPORATION_LOAN_FLAG IS '住宅金融公庫融資サイン 既存システム物理名: ECM42S';
COMMENT ON COLUMN BUILDING_MASTER.LOAN_CORPORATION_APPROVAL_START_DATE IS '公庫許可公募開始日 既存システム物理名: ECM88D';
COMMENT ON COLUMN BUILDING_MASTER.APPLICATION_ACCEPTANCE_DATE IS '確認申請受理日 既存システム物理名: ECM97D';
COMMENT ON COLUMN BUILDING_MASTER.PRINCIPAL_ADVANCE_FLAG IS '元金立替サイン 既存システム物理名: ECM40S';
COMMENT ON COLUMN BUILDING_MASTER.TENANT_RESTRICTION_CODE IS '入居者制限コード 既存システム物理名: ECMBMB';
COMMENT ON COLUMN BUILDING_MASTER.CONTRACT_BRANCH_CODE IS '契約支店コード 既存システム物理名: ECM53C';
COMMENT ON COLUMN BUILDING_MASTER.TENANT_RECRUITMENT_BRANCH_CODE IS '客付責任支店コード 既存システム物理名: ECM54C';
COMMENT ON COLUMN BUILDING_MASTER.MANAGEMENT_BRANCH_CODE IS '管理支店コード 既存システム物理名: ECM55C';
COMMENT ON COLUMN BUILDING_MASTER.FEE_BEARING_LANDLORD IS '手数料負担貸主 既存システム物理名: ECM56A';
COMMENT ON COLUMN BUILDING_MASTER.FEE_BEARING_TENANT IS '手数料負担借主 既存システム物理名: ECM57A';
COMMENT ON COLUMN BUILDING_MASTER.FEE_DISTRIBUTION_ORIGIN IS '手数料配分元付 既存システム物理名: ECM58A';
COMMENT ON COLUMN BUILDING_MASTER.FEE_DISTRIBUTION_TENANT IS '手数料配分客付 既存システム物理名: ECM61A';
COMMENT ON COLUMN BUILDING_MASTER.TENANT_RECRUITMENT_NOTES IS '客付留意事項 既存システム物理名: ECM38X';
COMMENT ON COLUMN BUILDING_MASTER.AREA_CATEGORY_1 IS 'エリア区分 既存システム物理名: ECMBTB';
COMMENT ON COLUMN BUILDING_MASTER.AREA_BASE_CODE_1 IS 'エリア基本コード 既存システム物理名: ECM62C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_DETAIL_CODE_1 IS 'エリア明細コード 既存システム物理名: ECM63C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_CATEGORY_2 IS 'エリア区分 既存システム物理名: ECM64C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_BASE_CODE_2 IS 'エリア基本コード 既存システム物理名: ECM65C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_DETAIL_CODE_2 IS 'エリア明細コード 既存システム物理名: ECM66C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_CATEGORY_3 IS 'エリア区分 既存システム物理名: ECM67C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_BASE_CODE_3 IS 'エリア基本コード 既存システム物理名: ECM68C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_DETAIL_CODE_3 IS 'エリア明細コード 既存システム物理名: ECM69C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_CATEGORY_4 IS 'エリア区分 既存システム物理名: ECM70C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_BASE_CODE_4 IS 'エリア基本コード 既存システム物理名: ECM71C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_DETAIL_CODE_4 IS 'エリア明細コード 既存システム物理名: ECM72C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_CATEGORY_5 IS 'エリア区分 既存システム物理名: ECM73C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_BASE_CODE_5 IS 'エリア基本コード 既存システム物理名: ECM74C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_DETAIL_CODE_5 IS 'エリア明細コード 既存システム物理名: ECM75C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_CATEGORY_6 IS 'エリア区分 既存システム物理名: ECM76C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_BASE_CODE_6 IS 'エリア基本コード 既存システム物理名: ECM77C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_DETAIL_CODE_6 IS 'エリア明細コード 既存システム物理名: ECM78C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_CATEGORY_7 IS 'エリア区分 既存システム物理名: ECM79C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_BASE_CODE_7 IS 'エリア基本コード 既存システム物理名: ECM80C';
COMMENT ON COLUMN BUILDING_MASTER.AREA_DETAIL_CODE_7 IS 'エリア明細コード 既存システム物理名: ECM81C';
COMMENT ON COLUMN BUILDING_MASTER.BUILDING_MANAGER_CODE IS '建物管理担当者コー 既存システム物理名: ECM90C';
COMMENT ON COLUMN BUILDING_MASTER.RENT_MANAGER_CODE IS '家賃管理担当者コー 既存システム物理名: ECM91C';
COMMENT ON COLUMN BUILDING_MASTER.CONTRACT_MANAGER_CODE IS '契約管理担当者コー 既存システム物理名: ECM92C';
COMMENT ON COLUMN BUILDING_MASTER.HOUSE_COM_PURCHASE_FLAG IS 'ハウスコム仕入サイン 既存システム物理名: ECM94S';
COMMENT ON COLUMN BUILDING_MASTER.PREFECTURE_CODE2 IS '都道府県コード 既存システム物理名: ECMA0C';
COMMENT ON COLUMN BUILDING_MASTER.CITY_CODE2 IS '市区郡コード 既存システム物理名: ECMA1C';
COMMENT ON COLUMN BUILDING_MASTER.TOWN_CODE2 IS '町村字通称コード 既存システム物理名: ECMA2C';
COMMENT ON COLUMN BUILDING_MASTER.ADDRESS_DETAIL2 IS '住所詳細 既存システム物理名: ECM99X';
COMMENT ON COLUMN BUILDING_MASTER.OWNER_CODE_1 IS '名義人コード1 既存システム物理名: ECM98C';
COMMENT ON COLUMN BUILDING_MASTER.OWNER_CODE_2 IS '名義人コード2 既存システム物理名: ECMA9C';
COMMENT ON COLUMN BUILDING_MASTER.OWNER_CODE_3 IS '名義人コード3 既存システム物理名: ECMB0C';
COMMENT ON COLUMN BUILDING_MASTER.OWNERSHIP_DETAIL_1 IS '所有権事項1 既存システム物理名: ECMA3X';
COMMENT ON COLUMN BUILDING_MASTER.OWNERSHIP_DETAIL_2 IS '所有権事項2 既存システム物理名: ECMA4X';
COMMENT ON COLUMN BUILDING_MASTER.OWNERSHIP_DETAIL_3 IS '所有権事項3 既存システム物理名: ECMA5X';
COMMENT ON COLUMN BUILDING_MASTER.OWNERSHIP_DETAIL_4 IS '所有権事項4 既存システム物理名: ECMB1X';
COMMENT ON COLUMN BUILDING_MASTER.OWNERSHIP_DETAIL_5 IS '所有権事項5 既存システム物理名: ECMB2X';
COMMENT ON COLUMN BUILDING_MASTER.NON_OWNERSHIP_DETAIL_1 IS '所有権外事項1 既存システム物理名: ECMA6X';
COMMENT ON COLUMN BUILDING_MASTER.NON_OWNERSHIP_DETAIL_2 IS '所有権外事項2 既存システム物理名: ECMA7X';
COMMENT ON COLUMN BUILDING_MASTER.NON_OWNERSHIP_DETAIL_3 IS '所有権外事項3 既存システム物理名: ECMA8X';
COMMENT ON COLUMN BUILDING_MASTER.NON_OWNERSHIP_DETAIL_4 IS '所有権外事項4 既存システム物理名: ECMB3X';
COMMENT ON COLUMN BUILDING_MASTER.NON_OWNERSHIP_DETAIL_5 IS '所有権外事項5 既存システム物理名: ECMB4X';
COMMENT ON COLUMN BUILDING_MASTER.MANAGEMENT_START_DATE IS '管理開始日 既存システム物理名: ECMC5D';
COMMENT ON COLUMN BUILDING_MASTER.MANAGEMENT_END_DATE IS '管理終了日 既存システム物理名: ECMC6D';
COMMENT ON COLUMN BUILDING_MASTER.AGREED_TERMINATION_DATE IS '合意解約日 既存システム物理名: ECMC7D';
COMMENT ON COLUMN BUILDING_MASTER.BRANCH_CODE IS '支店コード 既存システム物理名: ECMC8C';
COMMENT ON COLUMN BUILDING_MASTER.MANAGEMENT_STAFF_CODE IS '管理担当コード 既存システム物理名: ECMC9C';
COMMENT ON COLUMN BUILDING_MASTER.MANAGEMENT_START_DATE2 IS '管理開始日 既存システム物理名: ECMD1D';
COMMENT ON COLUMN BUILDING_MASTER.INTERFACE_FLAG IS 'インターフェースサイン 既存システム物理名: ECM51S';
COMMENT ON COLUMN BUILDING_MASTER.TENANT_EXTRACTION_FLAG IS '客付要抽出サイン 既存システム物理名: ECM41S';
COMMENT ON COLUMN BUILDING_MASTER.DATA_MIGRATION_SOURCE_KEY_1 IS 'データ移行元キー1 既存システム物理名: ECM59N';
COMMENT ON COLUMN BUILDING_MASTER.DATA_MIGRATION_SOURCE_KEY_2 IS 'データ移行元キー2 既存システム物理名: ECM60N';
COMMENT ON COLUMN BUILDING_MASTER.NEAREST_BUS_STOP_DISTANCE IS '最寄バス停距離 既存システム物理名: ECMD4Q';
COMMENT ON COLUMN BUILDING_MASTER.CATCHCOPY_CONTENT IS 'キャッチコピー内容 既存システム物理名: ECMD5X';
COMMENT ON COLUMN BUILDING_MASTER.RECRUITMENT_INFO_INPUT_FLAG IS '募集情報入力済サイン 既存システム物理名: ECMD6X';
COMMENT ON COLUMN BUILDING_MASTER.LIST_OUTPUT_FLAG IS 'リスト出力サイン 既存システム物理名: ECMD7X';
COMMENT ON COLUMN BUILDING_MASTER.NEW_BUILD_RENT_CATEGORY IS '新築上乗家賃区分 既存システム物理名: ECMD8X';
COMMENT ON COLUMN BUILDING_MASTER.COMPANY_CODE IS '会社コード 既存システム物理名: ECMAEC';
COMMENT ON COLUMN BUILDING_MASTER.DEPOSIT_CATEGORY IS '敷金預かり区分 既存システム物理名: ECMAZB';
COMMENT ON COLUMN BUILDING_MASTER.GUARANTEE_DEDUCT_CATEGORY IS '保証金相殺区分 既存システム物理名: ECMSSB';
COMMENT ON COLUMN BUILDING_MASTER.NEW_HOUSE_GUARANTEE_10Y_PK_COUNT IS '新家保10年PK数 既存システム物理名: ECMBS1';
COMMENT ON COLUMN BUILDING_MASTER.NEW_GUARANTEE_RATE IS '新保証率 既存システム物理名: ECM10A';
COMMENT ON COLUMN BUILDING_MASTER.NEW_GUARANTEE_MANAGEMENT_RATE IS '新保証管理率 既存システム物理名: ECM10B';
COMMENT ON COLUMN BUILDING_MASTER.CONTRACT_MUTUAL_AID_FEE_RATE IS '請負契約共済会費率 既存システム物理名: ECM10C';
COMMENT ON COLUMN BUILDING_MASTER.KEY_MONEY_FLAG IS '礼金有無区分 既存システム物理名: ECMRIB';
COMMENT ON COLUMN BUILDING_MASTER.MANAGEMENT_FEE_ACCOUNTING_CATEGORY IS '管契時金計上区分 既存システム物理名: ECMKKB';
COMMENT ON COLUMN BUILDING_MASTER.SCREENING_BRANCH_CODE IS '審査支店コード 既存システム物理名: ECMSNC';
COMMENT ON COLUMN BUILDING_MASTER.RESIDENCE_DISPLAY_CATEGORY IS '住居表示区分 既存システム物理名: ECMJHB';
COMMENT ON COLUMN BUILDING_MASTER.LANDLORD_SIGNATURE_CONSENT_CATEGORY IS '家主署名承諾区分 既存システム物理名: ECMSDB';
COMMENT ON COLUMN BUILDING_MASTER.LOAN_CORPORATION_RESTORE_CATEGORY IS '公庫原状回復区分 既存システム物理名: ECMGKB';
COMMENT ON COLUMN BUILDING_MASTER.LEASE_CONTRACT_NUMBER IS '賃貸借契約書番号 既存システム物理名: ECMKYB';
COMMENT ON COLUMN BUILDING_MASTER.GUARANTOR_NOT_REQUIRED_CATEGORY IS '保証人不要区分 既存システム物理名: ECMHFS';
COMMENT ON COLUMN BUILDING_MASTER.COM_PARTNERSHIP_CATEGORY IS 'コム提携区分 既存システム物理名: ECMKB1';
COMMENT ON COLUMN BUILDING_MASTER.CONTRACTOR_COMPANY_CATEGORY IS '施工会社区分 既存システム物理名: ECMKB2';
COMMENT ON COLUMN BUILDING_MASTER.SPECIAL_RENT IS '特優賃 既存システム物理名: ECMKB3';
COMMENT ON COLUMN BUILDING_MASTER.REAL_ESTATE_PARTNERSHIP_CATEGORY IS '不動産提携区分 既存システム物理名: ECMKB4';
COMMENT ON COLUMN BUILDING_MASTER.SUPPORT_MECHANISM_CATEGORY IS '支援機構区分 既存システム物理名: ECMKB5';
COMMENT ON COLUMN BUILDING_MASTER.TELEPHONE_CONSENT_CATEGORY IS '電話承諾区分 既存システム物理名: ECMKB6';
COMMENT ON COLUMN BUILDING_MASTER.BUILDING_NAME_FLAG IS '建物名称判別フラグ 既存システム物理名: ECMKB7';
COMMENT ON COLUMN BUILDING_MASTER.FIRE_RESISTANT_STRUCTURE_CATEGORY IS '耐火構造区分 既存システム物理名: ECMKB8';
COMMENT ON COLUMN BUILDING_MASTER.MANAGEMENT_INHERITANCE_CATEGORY IS '管理継承区分 既存システム物理名: ECMKB9';
COMMENT ON COLUMN BUILDING_MASTER.UNUSED_9 IS '未使用9 既存システム物理名: ECMKB0';
COMMENT ON COLUMN BUILDING_MASTER.MANAGEMENT_STAFF_CODE_ST IS '管理担当コード－ST 既存システム物理名: ECM0ST';
COMMENT ON COLUMN BUILDING_MASTER.DAIKEN_BRANCH_CODE IS '大建支店CD 既存システム物理名: ECMKC1';
COMMENT ON COLUMN BUILDING_MASTER.HOUSE_NUMBER IS '家屋番号 既存システム物理名: ECM0AX';
COMMENT ON COLUMN BUILDING_MASTER.MARKETING_BRANCH_OFFICE_CD IS 'マーケティング営業所CD 既存システム物理名: -';
