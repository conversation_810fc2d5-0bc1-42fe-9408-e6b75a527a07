/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.SpecialBuildingMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.SpecialBuildingMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 特例建物マスタ 既存システム物理名: ECMD9P
 */
@Suppress("UNCHECKED_CAST")
open class SpecialBuildingMasterRecord private constructor() : TableRecordImpl<SpecialBuildingMasterRecord>(SpecialBuildingMasterTable.SPECIAL_BUILDING_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var buildingCd: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var identificationCategory: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    /**
     * Create a detached, initialised SpecialBuildingMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteSign: Byte? = null, buildingCd: String? = null, identificationCategory: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteSign = logicalDeleteSign
        this.buildingCd = buildingCd
        this.identificationCategory = identificationCategory
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised SpecialBuildingMasterRecord
     */
    constructor(value: SpecialBuildingMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteSign = value.logicalDeleteSign
            this.buildingCd = value.buildingCd
            this.identificationCategory = value.identificationCategory
            resetChangedOnNotNull()
        }
    }
}
