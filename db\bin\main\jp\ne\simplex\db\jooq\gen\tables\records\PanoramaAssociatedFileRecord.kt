/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.PanoramaAssociatedFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.PanoramaAssociatedFilePojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * パノラマ関連付けファイル 既存システム物理名: ERA10P
 */
@Suppress("UNCHECKED_CAST")
open class PanoramaAssociatedFileRecord private constructor() : UpdatableRecordImpl<PanoramaAssociatedFileRecord>(PanoramaAssociatedFileTable.PANORAMA_ASSOCIATED_FILE) {

    open var portalGoodRoomMemberId: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var panoramaId: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var buildingCd: String
        set(value): Unit = set(2, value)
        get(): String = get(2) as String

    open var roomCd: String
        set(value): Unit = set(3, value)
        get(): String = get(3) as String

    open var logicalDeleteFlag: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateDate: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var updateTime: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var creationDate: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var updateTime2: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var updaterId: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var panoramaType: Byte?
        set(value): Unit = set(10, value)
        get(): Byte? = get(10) as Byte?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised PanoramaAssociatedFileRecord
     */
    constructor(portalGoodRoomMemberId: Int? = null, panoramaId: Int? = null, buildingCd: String, roomCd: String, logicalDeleteFlag: String? = null, updateDate: Int? = null, updateTime: Int? = null, creationDate: Int? = null, updateTime2: Int? = null, updaterId: String? = null, panoramaType: Byte? = null): this() {
        this.portalGoodRoomMemberId = portalGoodRoomMemberId
        this.panoramaId = panoramaId
        this.buildingCd = buildingCd
        this.roomCd = roomCd
        this.logicalDeleteFlag = logicalDeleteFlag
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.creationDate = creationDate
        this.updateTime2 = updateTime2
        this.updaterId = updaterId
        this.panoramaType = panoramaType
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised PanoramaAssociatedFileRecord
     */
    constructor(value: PanoramaAssociatedFilePojo?): this() {
        if (value != null) {
            this.portalGoodRoomMemberId = value.portalGoodRoomMemberId
            this.panoramaId = value.panoramaId
            this.buildingCd = value.buildingCd
            this.roomCd = value.roomCd
            this.logicalDeleteFlag = value.logicalDeleteFlag
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.creationDate = value.creationDate
            this.updateTime2 = value.updateTime2
            this.updaterId = value.updaterId
            this.panoramaType = value.panoramaType
            resetChangedOnNotNull()
        }
    }
}
