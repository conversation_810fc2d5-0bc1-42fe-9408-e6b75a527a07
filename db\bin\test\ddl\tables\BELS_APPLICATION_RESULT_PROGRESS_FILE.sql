-- TABLE: BELS_APPLICATION_RESULT_PROGRESS_FILE(BELS申請結果進捗ファイル)

CREATE TABLE BELS_APPLICATION_RESULT_PROGRESS_FILE(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM                               varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    DELETE_FLAG                                  varchar(1)                    
,    ORDER_CODE                                   numeric(7)                    
,    ADDITIONAL_CODE                              numeric(3)                    
,    BUILDING_NUMBER                              numeric(2)                    
,    ENERGY_SAVING_PERFORMANCE                    numeric(3)                    
,    CERTIFICATION_DATE                           numeric(8)                    
,    ATTACHED_FILE_NAME                           varchar(16)                   
,    PROPERTY_BUILDING_CD                         varchar(9)                    
,    CONSTRAINT UQ_BELS_APPLICATION_RESULT_PROGRESS_FILE UNIQUE (ORDER_CODE, ADDITIONAL_CODE, BUILDING_NUMBER, DELETE_FLAG)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BELS_APPLICATION_RESULT_PROGRESS_FILE IS 'BELS申請結果進捗ファイル 既存システム物理名: BELSMP';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: BEL01D @290';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.CREATION_TIME IS '作成時刻 既存システム物理名: BEL02H @290';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: BEL03D @290';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.UPDATE_TIME IS '更新時刻 既存システム物理名: BEL04H';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.UPDATE_PROGRAM IS '更新プログラム 既存システム物理名: BEL05N';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.UPDATER IS '更新者 既存システム物理名: BEL06C';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.DELETE_FLAG IS '削除フラグ 既存システム物理名: BEL07S';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.ORDER_CODE IS '受注コード 既存システム物理名: BEL08C';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.ADDITIONAL_CODE IS '追加コード 既存システム物理名: BEL09C';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.BUILDING_NUMBER IS '棟番号 既存システム物理名: BEL10C';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.ENERGY_SAVING_PERFORMANCE IS '省エネルギー性能 既存システム物理名: BEL11E';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.CERTIFICATION_DATE IS '認証取得日 既存システム物理名: BEL12D';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.ATTACHED_FILE_NAME IS '添付ファイル名 既存システム物理名: BEL13F';
COMMENT ON COLUMN BELS_APPLICATION_RESULT_PROGRESS_FILE.PROPERTY_BUILDING_CD IS '物件建物CD 既存システム物理名: -';
