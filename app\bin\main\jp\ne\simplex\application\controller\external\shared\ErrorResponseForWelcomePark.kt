package jp.ne.simplex.application.controller.external.shared

import jp.ne.simplex.exception.ErrorMessage

enum class ErrorResponseForWelcomePark(val code: String, val message: String) {
    BUILDING_CODE_OR_PARKING_CODE_INVALID("10", "建物コードまたは、駐車場コードが存在しません。"),
    PARKING_IS_IN_USE("20", "使用中です。"),
    UNEXPECTED("99", "予期せぬエラーが発生しました。");

    companion object {
        fun of(errorMessage: ErrorMessage?): ErrorResponseForWelcomePark {
            return when (errorMessage) {
                ErrorMessage.PARKING_DOES_NOT_EXIST
                    -> BUILDING_CODE_OR_PARKING_CODE_INVALID

                ErrorMessage.PARKING_IS_SIGNED,
                ErrorMessage.PARKING_IS_NOT_AVAILABLE,
                ErrorMessage.PARKING_RESERVATION_ALREADY_RESERVED,
                ErrorMessage.PARKING_RESERVATION_DATE_CONFLICTED
                    -> PARKING_IS_IN_USE

                else -> UNEXPECTED
            }
        }
    }
}
