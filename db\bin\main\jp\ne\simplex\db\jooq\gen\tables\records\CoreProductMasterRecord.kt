/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.CoreProductMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.CoreProductMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 基幹商品マスタ 既存システム物理名: ERA16P
 */
@Suppress("UNCHECKED_CAST")
open class CoreProductMasterRecord private constructor() : TableRecordImpl<CoreProductMasterRecord>(CoreProductMasterTable.CORE_PRODUCT_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var updateProgramId: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(7, value)
        get(): Byte? = get(7) as Byte?

    open var coreProductId: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var coreProductName: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var typeName: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var displayOrder: Short?
        set(value): Unit = set(11, value)
        get(): Short? = get(11) as Short?

    /**
     * Create a detached, initialised CoreProductMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, updateProgramId: String? = null, logicalDeleteSign: Byte? = null, coreProductId: Int? = null, coreProductName: String? = null, typeName: String? = null, displayOrder: Short? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.updateProgramId = updateProgramId
        this.logicalDeleteSign = logicalDeleteSign
        this.coreProductId = coreProductId
        this.coreProductName = coreProductName
        this.typeName = typeName
        this.displayOrder = displayOrder
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised CoreProductMasterRecord
     */
    constructor(value: CoreProductMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.updateProgramId = value.updateProgramId
            this.logicalDeleteSign = value.logicalDeleteSign
            this.coreProductId = value.coreProductId
            this.coreProductName = value.coreProductName
            this.typeName = value.typeName
            this.displayOrder = value.displayOrder
            resetChangedOnNotNull()
        }
    }
}
