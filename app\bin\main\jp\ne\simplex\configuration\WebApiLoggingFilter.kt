package jp.ne.simplex.configuration

import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import jp.ne.simplex.log.LogType
import jp.ne.simplex.log.MdcType
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.util.StreamUtils
import org.springframework.web.filter.OncePerRequestFilter
import org.springframework.web.util.ContentCachingRequestWrapper
import org.springframework.web.util.ContentCachingResponseWrapper
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.*

/**
 * SpringのControllerに適用されるFilter
 * Clientからのアクセスログ出力として利用する
 */
class WebApiLoggingFilter : OncePerRequestFilter() {

    companion object {
        private val log = LoggerFactory.getLogger(WebApiLoggingFilter::class.java)

        private val logType = LogType.CLIENT

        private const val REQUEST_CONTENT_LOG_TYPE = "[CLIENT_REQ_CONTENT]"
    }

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        val requestDateTime = LocalDateTime.now()

        val requestWrapper = wrapRequest(request)
        val responseWrapper = wrapResponse(response)
        try {
            MDC.put(MdcType.TRANSACTION_ID.key, UUID.randomUUID().toString())
            requestLog(requestWrapper)

            filterChain.doFilter(requestWrapper, responseWrapper)
        } finally {
            requestedLog(requestWrapper)
            responseLog(responseWrapper, requestDateTime)
            responseWrapper.copyBodyToResponse()

            MDC.clear()
        }
    }

    private fun requestLog(request: ContentCachingRequestWrapper) {
        MDC.put(MdcType.LOG_TYPE.key, logType.request)
        MDC.put(MdcType.METHOD.key, request.method)
        MDC.put(MdcType.URI.key, request.requestURI)

        // ここでStreamからJson形式の内容を消費してしまうと、Controller層でRequestBodyを読めなくなるため、処理後のログで出力
        log.info("Refer to $REQUEST_CONTENT_LOG_TYPE log for details of request.")

        MDC.remove(MdcType.LOG_TYPE.key)
        MDC.remove(MdcType.METHOD.key)
        MDC.remove(MdcType.URI.key)
    }

    private fun requestedLog(request: ContentCachingRequestWrapper) {
        MDC.put(MdcType.LOG_TYPE.key, REQUEST_CONTENT_LOG_TYPE)

        request.parameterNames
        val json = String(request.contentAsByteArray, StandardCharsets.UTF_8)
        val content =
            json.ifEmpty { request.queryString ?: "" }
                .replace("\n", "")
                .filterNot { it.isWhitespace() }
        log.info(content)

        MDC.remove(MdcType.LOG_TYPE.key)
    }

    private fun responseLog(
        response: ContentCachingResponseWrapper,
        requestDateTime: LocalDateTime
    ) {
        val latency = ChronoUnit.MILLIS.between(requestDateTime, LocalDateTime.now())

        MDC.put(MdcType.LOG_TYPE.key, logType.response)
        MDC.put(MdcType.STATUS.key, response.status.toString())
        MDC.put(MdcType.LATENCY.key, latency.toString())

        val content = StreamUtils.copyToString(response.contentInputStream, StandardCharsets.UTF_8)
        log.info(content.ifEmpty { "{}" })

        MDC.remove(MdcType.LOG_TYPE.key)
        MDC.remove(MdcType.STATUS.key)
        MDC.remove(MdcType.LATENCY.key)
    }

    private fun wrapRequest(request: HttpServletRequest): ContentCachingRequestWrapper {
        return ContentCachingRequestWrapper(request)
    }

    private fun wrapResponse(response: HttpServletResponse): ContentCachingResponseWrapper {
        return if (response is ContentCachingResponseWrapper) {
            response
        } else {
            ContentCachingResponseWrapper(response)
        }
    }
}