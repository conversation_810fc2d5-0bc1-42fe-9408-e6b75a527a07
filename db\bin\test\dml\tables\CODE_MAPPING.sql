truncate table CODE_MAPPING;
insert into CODE_MAPPING (FC_CODE, E_CODE) values
 ('fc00001', 'E32817000')
,('fc00002', 'E61757000')
,('fc00003', 'E58157000')
,('fc00005', 'E58699002')
,('fc00006', 'E33380000')
,('fc00009', 'E51487001')
,('fc00010', 'E66009001')
,('fc00011', 'E66049000')
,('fc00012', 'E65956000')
,('fc00013', 'E05444000')
,('fc00014', 'E05444003')
,('fc00015', 'E05444001')
,('fc00016', 'E05444002')
,('fc00020', 'E70660003')
,('fc00021', 'E70660001')
,('fc00022', 'E70660007')
,('fc00023', 'E70660004')
,('fc00024', 'E70660000')
,('fc00025', 'E71025000')
,('fc00026', 'E70660002')
,('fc00027', 'E71025002')
,('fc00028', 'E71025001')
,('fc00029', 'E71025004')
,('fc00030', 'E39057002')
,('fc00032', 'E28725001')
,('fc00035', 'E41955000')
,('fc00037', 'E66875000')
,('fc00038', 'E58677001')
,('fc00039', 'E60187001')
,('fc00040', 'E65956001')
,('fc00041', 'E39247000')
,('fc00044', 'E66738001')
,('fc00045', 'E67521000')
,('fc00046', 'E60884000')
,('fc00047', 'E65367007')
,('fc00048', 'E67458000')
,('fc00049', 'E58060001')
,('fc00052', 'E66123000')
,('fc00053', 'E06417005')
,('fc00054', 'E62452000')
,('fc00056', 'E66794000')
,('fc00058', 'E67995000')
,('fc00059', 'E68061000')
,('fc00061', 'E68632000')
,('fc00067', 'E68511000')
,('fc00068', 'E68840000')
,('fc00069', 'E60529000')
,('fc00070', 'E56931001')
,('fc00071', 'E61193001')
,('fc00072', 'E26106000')
,('fc00073', 'E66738000')
,('fc00074', 'E66049001')
,('fc00075', 'E55928001')
,('fc00076', 'E50105000')
,('fc00077', 'E67662000')
,('fc00078', 'E69081000')
,('fc00079', 'E60187002')
,('fc00080', 'E25360001')
,('fc00082', 'E69903000')
,('fc00083', 'E06059011')
,('fc00084', 'E69669000')
,('fc00085', 'E66738002')
,('fc00086', 'E70072000')
,('fc00087', 'E69567000')
,('fc00088', 'E69081002')
,('fc00089', 'E52666000')
,('fc00090', 'E40642001')
,('fc00091', 'E40642002')
,('fc00092', 'E68633001')
,('fc00093', 'E02677004')
,('fc00094', 'E60210000')
,('fc00096', 'E65367006')
,('fc00097', 'E69942000')
,('fc00098', 'E69938000')
,('fc00099', 'E05310021')
,('fc00100', 'E69081001')
,('fc00101', 'E55185003')
,('fc00102', 'E70168000')
,('fc00103', 'E50592002')
,('fc00104', 'E20473000')
,('fc00105', 'E68061001')
,('fc00106', 'E17999000')
,('fc00107', 'E70612000')
,('fc00108', 'E01560027')
,('fc00109', 'E43996000')
,('fc00110', 'E04357008')
,('fc00111', 'E60651001')
,('fc00112', 'E66484000')
,('fc00113', 'E38278000')
,('fc00114', 'E71139000')
,('fc00115', 'E66520001')
,('fc00116', 'E50548003')
,('fc00117', 'E50548004')
,('fc00118', 'E71295000')
,('fc00120', 'E70510000')
,('fc00121', 'E62280000')
,('fc00122', 'E25360004')
,('fc00123', 'E06059001')
,('fc00124', 'E20509000')
,('fc00126', 'E63580000')
,('fc00127', 'E65775000')
,('fc00128', 'E05310008')
,('fc00130', 'E71525000')
,('fc00131', 'E68840001')
,('fc00132', 'E06059003')
,('fc00133', 'E71760000')
,('fc00134', 'E53969000')
,('fc00135', 'E39057000')
,('fc00136', 'E65367008')
,('fc00137', 'E06614003')
,('fc00139', 'E39781018')
,('fc00140', 'E39781006')
,('fc00141', 'E39781008')
,('fc00142', 'E65203000')
,('fc00143', 'E46521000')
,('fc00144', 'E73315000')
,('fc00145', 'E72487000')
,('fc00146', 'E66520002')
,('fc00147', 'E72118000')
,('fc00148', 'E69501002')
,('fc00149', 'E04357011')
,('fc00150', 'E39781002')
,('fc00151', 'E33573008')
,('fc00152', 'E04373001')
,('fc00153', 'E41955001')
;
