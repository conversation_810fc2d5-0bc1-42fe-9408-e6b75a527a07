-- TABLE: ROOM_INFO_MASTER(部屋情報マスタ)

CREATE TABLE ROOM_INFO_MASTER(
     RECORD_TYPE                                  varchar(1)        NOT NULL    
,    PROPERTY_CD_TYPE                             varchar(1)        NOT NULL    
,    PROPERTY_CD_PART1                            varchar(1)                    
,    PROPERTY_BUILDING_CD                         varchar(9)        NOT NULL    
,    PROPERTY_CD_PART2                            varchar(1)                    
,    PROPERTY_ROOM_CD                             varchar(5)        NOT NULL    
,    DELETE_FLAG                                  varchar(1)                    
,    CUSTOMER_COMPANY_CD                          varchar(3)                    
,    CUSTOMER_BRANCH_CD                           varchar(3)                    
,    CUSTOMER_DEPARTMENT_CD                       varchar(3)                    
,    CUSTOMER_COMPLETION_FLAG                     varchar(1)                    
,    PREFECTURE_CD                                varchar(2)                    
,    CITY_CD                                      varchar(3)                    
,    LINE_CD                                      varchar(4)                    
,    STATION_CD                                   varchar(4)                    
,    RENT                                         numeric(7,0)                  
,    LAYOUT_ROOM_COUNT                            varchar(2)                    
,    EXCLUSIVE_AREA                               varchar(6)                    
,    PROPERTY_TYPE                                varchar(3)                    
,    PREFERENCE_1                                 numeric(1,0)                  
,    PREFERENCE_2                                 numeric(1,0)                  
,    PREFERENCE_3                                 numeric(1,0)                  
,    PREFERENCE_4                                 numeric(1,0)                  
,    PREFERENCE_5                                 numeric(1,0)                  
,    PREFERENCE_6                                 numeric(1,0)                  
,    PREFERENCE_7                                 numeric(1,0)                  
,    PREFERENCE_8                                 numeric(1,0)                  
,    PREFERENCE_9                                 numeric(1,0)                  
,    PREFERENCE_10                                numeric(1,0)                  
,    PREFERENCE_11                                numeric(1,0)                  
,    PREFERENCE_12                                numeric(1,0)                  
,    PREFERENCE_13                                numeric(1,0)                  
,    PREFERENCE_14                                numeric(1,0)                  
,    PREFERENCE_15                                numeric(1,0)                  
,    PREFERENCE_16                                numeric(1,0)                  
,    PREFERENCE_17                                numeric(1,0)                  
,    PREFERENCE_18                                numeric(1,0)                  
,    PREFERENCE_19                                numeric(1,0)                  
,    PREFERENCE_20                                numeric(1,0)                  
,    PREFERENCE_21                                numeric(1,0)                  
,    PREFERENCE_22                                numeric(1,0)                  
,    PREFERENCE_23                                numeric(1,0)                  
,    PREFERENCE_24                                numeric(1,0)                  
,    PREFERENCE_25                                numeric(1,0)                  
,    PREFERENCE_26                                numeric(1,0)                  
,    PREFERENCE_27                                numeric(1,0)                  
,    PREFERENCE_28                                numeric(1,0)                  
,    PREFERENCE_29                                numeric(1,0)                  
,    PREFERENCE_30                                numeric(1,0)                  
,    PREFERENCE_31                                numeric(1,0)                  
,    PREFERENCE_32                                numeric(1,0)                  
,    PREFERENCE_33                                numeric(1,0)                  
,    PREFERENCE_34                                numeric(1,0)                  
,    PREFERENCE_35                                numeric(1,0)                  
,    PREFERENCE_36                                numeric(1,0)                  
,    PREFERENCE_37                                numeric(1,0)                  
,    PREFERENCE_38                                numeric(1,0)                  
,    PREFERENCE_39                                numeric(1,0)                  
,    PREFERENCE_40                                numeric(1,0)                  
,    PREFERENCE_41                                numeric(1,0)                  
,    PREFERENCE_42                                numeric(1,0)                  
,    PREFERENCE_43                                numeric(1,0)                  
,    PREFERENCE_44                                numeric(1,0)                  
,    PREFERENCE_45                                numeric(1,0)                  
,    PREFERENCE_46                                numeric(1,0)                  
,    PREFERENCE_47                                numeric(1,0)                  
,    PREFERENCE_48                                numeric(1,0)                  
,    PREFERENCE_49                                numeric(1,0)                  
,    PREFERENCE_50                                numeric(1,0)                  
,    PREFERENCE_51                                numeric(1,0)                  
,    PREFERENCE_52                                numeric(1,0)                  
,    PREFERENCE_53                                numeric(1,0)                  
,    PREFERENCE_54                                numeric(1,0)                  
,    PREFERENCE_55                                numeric(1,0)                  
,    PREFERENCE_56                                numeric(1,0)                  
,    PREFERENCE_57                                numeric(1,0)                  
,    PREFERENCE_58                                numeric(1,0)                  
,    PREFERENCE_59                                numeric(1,0)                  
,    PREFERENCE_60                                numeric(1,0)                  
,    PREFERENCE_99                                numeric(1,0)                  
,    PREFERENCE_NEW_BUILD                         numeric(1,0)                  
,    PREFERENCE_CORNER_ROOM                       numeric(1,0)                  
,    PREFERENCE_ABOVE_2ND_FLOOR                   numeric(1,0)                  
,    LINE_NAME                                    varchar(42)                   
,    STATION_NAME                                 varchar(32)                   
,    BUS_STOP_NAME                                varchar(22)                   
,    BUS_TIME                                     numeric(3,0)                  
,    WALKING_TIME                                 numeric(3,0)                  
,    DISTANCE                                     numeric(3,0)                  
,    KEY_MONEY                                    varchar(16)                   
,    DEPOSIT                                      varchar(16)                   
,    NEIGHBORHOOD_ASSOCIATION_FEE                 varchar(16)                   
,    COMMON_SERVICE_FEE                           varchar(16)                   
,    ROOM_TYPE_NAME                               varchar(22)                   
,    LAYOUT_TYPE                                  varchar(2)                    
,    LAYOUT                                       varchar(10)                   
,    LAYOUT_DETAILS                               varchar(42)                   
,    PARKING_TYPE                                 varchar(1)                    
,    PARKING_FEE                                  varchar(10)                   
,    CONSTRUCTION_YEAR_MONTH                      varchar(14)                   
,    HANDLING_STORE_COMPANY                       varchar(104)                  
,    LOCATION_LISTING_AREA                        varchar(257)                  
,    FLOOR_NUMBER                                 varchar(30)                   
,    DIRECTION                                    varchar(6)                    
,    ROOM_POSITION                                varchar(8)                    
,    AVAILABLE_MOVE_IN_YEAR_MONTH                 varchar(22)                   
,    TRANSPORTATION                               varchar(152)                  
,    EQUIPMENT                                    varchar(402)                  
,    NOTES                                        varchar(257)                  
,    INQUIRY_BRANCH_NAME                          varchar(102)                  
,    BRANCH_PHONE_NUMBER                          varchar(16)                   
,    BRANCH_FAX_NUMBER                            varchar(16)                   
,    TRANSACTION_TYPE                             varchar(52)                   
,    BUILDING_NAME                                varchar(84)                   
,    STRUCTURE_NAME                               varchar(32)                   
,    AGENT_ASSIGNABLE_TYPE                        varchar(1)                    
,    SUBLEASE_TYPE                                varchar(1)                    
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(10)                   
,    BRANCH_ADDRESS                               varchar(104)                  
,    RECOMMENDATION_COMMENT                       varchar(242)                  
,    COMPLETION_YEAR_MONTH                        numeric(6,0)                  
,    PROPERTY_POSTAL_CODE                         varchar(7)                    
,    VACATE_NOTICE_DATE                           numeric(8,0)                  
,    EXPECTED_MOVE_OUT_DATE                       numeric(8,0)                  
,    MOVE_OUT_DATE                                numeric(8,0)                  
,    EXPECTED_COMPLETION_DATE                     numeric(8,0)                  
,    AVAILABLE_MOVE_IN_DATE                       numeric(8,0)                  
,    MOVE_IN_APPLICATION_DATE                     numeric(8,0)                  
,    DEPOSIT_DATE                                 numeric(8,0)                  
,    BALANCE_COLLECTION_DATE                      numeric(8,0)                  
,    MOVE_IN_DATE                                 numeric(8,0)                  
,    COMPLETION_DATE                              numeric(8,0)                  
,    TENANT_RECRUITMENT_COLLECTION_DATE           numeric(8,0)                  
,    TENANT                                       varchar(42)                   
,    OWNER                                        varchar(42)                   
,    CUSTOMER_AGENT_BRANCH_CD                     varchar(3)                    
,    CUSTOMER_AGENT_DEPARTMENT_CD                 varchar(3)                    
,    CUSTOMER_AGENT_EMPLOYEE_CD                   varchar(6)                    
,    RENT_TAX                                     numeric(7,0)                  
,    KEY_MONEY_TAX                                numeric(7,0)                  
,    KEY_MONEY_TOTAL                              numeric(7,0)                  
,    COMMON_SERVICE_FEE_TAX                       numeric(7,0)                  
,    PARKING_FEE_TAX                              numeric(7,0)                  
,    ROOM_NUMBER                                  varchar(4)                    
,    NEW_EXISTING_FLAG                            varchar(1)                    
,    ROOM_STATUS_TYPE                             varchar(2)                    
,    RECORD_STATUS_TYPE                           varchar(2)                    
,    FF_USAGE_PERIOD                              varchar(30)                   
,    AD_PAYABLE_AMOUNT                            varchar(30)                   
,    LOCATION_CITY                                varchar(62)                   
,    TENANT_CONTRACT_NUMBER                       varchar(8)                    
,    OWNER_CONTACT                                varchar(15)                   
,    VACANT_PERIOD                                numeric(3)                    
,    FLOOR_AREA_1F                                numeric(7,2)                  
,    FLOOR_AREA_2F                                numeric(7,2)                  
,    FLOOR_AREA_3F                                numeric(7,2)                  
,    RECRUITMENT_CREATION_DATE                    numeric(8,0)                  
,    RECRUITMENT_APPROVAL_DATE                    numeric(8,0)                  
,    MOVE_OUT_INSPECTION_DATE                     numeric(8,0)                  
,    RESTORATION_DATE                             numeric(8,0)                  
,    RESTORATION_COMPLETION_DATE                  numeric(8,0)                  
,    VACANT_BOOKING_DATE                          numeric(8,0)                  
,    VACANT_BOOKING_COMPLETION_DATE               numeric(8,0)                  
,    ADDITIONAL_KEY_MONEY                         numeric(7,0)                  
,    MUTUAL_AID_JOIN_SIGN                         numeric(1,0)                  
,    RENTAL_TYPE                                  numeric(1,0)                  
,    SPECIAL_RENTAL_TYPE                          varchar(1)                    
,    DISTANCE2                                    numeric(3,1)                  
,    APPROVAL_TYPE                                numeric(2,0)                  
,    RECORD_SEPARATOR                             varchar(1)                    
,    CHANGE_TYPE                                  varchar(1)                    
,    NO_DEPOSIT_FLAG                              numeric(1,0)                  
,    CAMPAIGN_TARGET_FLAG                         numeric(1,0)                  
,    PREFERENCE_61                                numeric(1,0)                  
,    PREFERENCE_62                                numeric(1,0)                  
,    PREFERENCE_63                                numeric(1,0)                  
,    PREFERENCE_64                                numeric(1,0)                  
,    PREFERENCE_65                                numeric(1,0)                  
,    PREFERENCE_66                                numeric(1,0)                  
,    PREFERENCE_67                                numeric(1,0)                  
,    PREFERENCE_68                                numeric(1,0)                  
,    PREFERENCE_69                                numeric(1,0)                  
,    PREFERENCE_70                                numeric(1,0)                  
,    PREFERENCE_71                                numeric(1,0)                  
,    PREFERENCE_72                                numeric(1,0)                  
,    PREFERENCE_73                                numeric(1,0)                  
,    PREFERENCE_74                                numeric(1,0)                  
,    PREFERENCE_75                                numeric(1,0)                  
,    PREFERENCE_76                                numeric(1,0)                  
,    PREFERENCE_77                                numeric(1,0)                  
,    PREFERENCE_78                                numeric(1,0)                  
,    PREFERENCE_79                                numeric(1,0)                  
,    PREFERENCE_80                                numeric(1,0)                  
,    PREFERENCE_81                                numeric(1,0)                  
,    PREFERENCE_82                                numeric(1,0)                  
,    PREFERENCE_83                                numeric(1,0)                  
,    PREFERENCE_84                                numeric(1,0)                  
,    PREFERENCE_85                                numeric(1,0)                  
,    PREFERENCE_86                                numeric(1,0)                  
,    PREFERENCE_87                                numeric(1,0)                  
,    PREFERENCE_88                                numeric(1,0)                  
,    PREFERENCE_89                                numeric(1,0)                  
,    PREFERENCE_90                                numeric(1,0)                  
,    PREFERENCE_91                                numeric(1,0)                  
,    PREFERENCE_92                                numeric(1,0)                  
,    PREFERENCE_93                                numeric(1,0)                  
,    PREFERENCE_94                                numeric(1,0)                  
,    PREFERENCE_95                                numeric(1,0)                  
,    PREFERENCE_96                                numeric(1,0)                  
,    PREFERENCE_97                                numeric(1,0)                  
,    PREFERENCE_98                                numeric(1,0)                  
,    PROPERTY_ADDRESS                             varchar(74)                   
,    PROPERTY_ADDRESS_DETAIL                      varchar(62)                   
,    SERVICE_ROOM_SIGN                            numeric(1,0)                  
,    HIGH_VOLTAGE_BULK_RECEIPT                    numeric(1,0)                  
,    HIGH_RENTAL_SIGN                             numeric(1,0)                  
,    SOLAR_DISCOUNT_TARGET                        numeric(1,0)                  
,    CLEANING_COST_FIXED                          numeric(1,0)                  
,    PREVIOUS_RENT                                numeric(7,0)                  
,    EXISTING_REVIEW_UPDATE_DATE                  numeric(8,0)                  
,    MOVE_OUT_INSPECTION_TIME                     numeric(8,0)                  
,    RECRUITMENT_START_DATE                       numeric(8,0)                  
,    CLEANING_COST_TOTAL                          numeric(7,0)                  
,    DISCOUNT_INITIAL_VALUE_SIGN                  numeric(1,0)                  
,    FLAG_RESERVE_7                               numeric(1,0)                  
,    PET_FLAG                                     numeric(1,0)                  
,    FLAG_RESERVE_9                               numeric(1,0)                  
,    FLAG_RESERVE_10                              numeric(1,0)                  
,    CHALLENGE_START_DATE                         numeric(8,0)                  
,    CHALLENGE_END_DATE                           numeric(8,0)                  
,    APPLICATION_END_DATE                         numeric(8,0)                  
,    MOVE_IN_END_DATE                             numeric(8,0)                  
,    ADDITIONAL_RELEASE_DATE                      numeric(8,0)                  
,    RECRUITMENT_RENT                             numeric(7,0)                  
,    CHALLENGE_ADDITIONAL_AMOUNT                  numeric(7,0)                  
,    REVIEW_RENT                                  numeric(7,0)                  
,    DATE_RESERVE_11                              numeric(8,0)                  
,    DATE_RESERVE_12                              numeric(8,0)                  
,    DATE_RESERVE_13                              numeric(8,0)                  
,    AMOUNT_RESERVE_1                             numeric(7,0)                  
,    AMOUNT_RESERVE_2                             numeric(7,0)                  
,    AMOUNT_RESERVE_3                             numeric(7,0)                  
,    COMMON_SERVICE_FEE_BASE                      numeric(9,0)                  
,    GENERAL_CABLE_TV_BASE                        numeric(9,0)                  
,    GENERAL_CABLE_TV_TAX                         numeric(9,0)                  
,    GENERAL_INTERNET_BASE                        numeric(9,0)                  
,    GENERAL_INTERNET_TAX                         numeric(9,0)                  
,    GENERAL_WATER_QUALITY_BASE                   numeric(9,0)                  
,    GENERAL_WATER_QUALITY_TAX                    numeric(9,0)                  
,    GENERAL_TENANT_WATER_BASE                    numeric(9,0)                  
,    GENERAL_TENANT_WATER_TAX                     numeric(9,0)                  
,    GENERAL_DRAIN_USE_BASE                       numeric(9,0)                  
,    GENERAL_DRAIN_USE_TAX                        numeric(9,0)                  
,    GENERAL_GARBAGE_COLLECTION_BASE              numeric(9,0)                  
,    GENERAL_GARBAGE_COLLECTION_TAX               numeric(9,0)                  
,    GENERAL_SHARED_ANTENNA_BASE                  numeric(9,0)                  
,    GENERAL_SHARED_ANTENNA_TAX                   numeric(9,0)                  
,    GENERAL_OWNER_CLEANING_BASE                  numeric(9,0)                  
,    GENERAL_OWNER_CLEANING_TAX                   numeric(9,0)                  
,    GENERAL_BUILDING_MAINTENANCE_BASE            numeric(9,0)                  
,    GENERAL_BUILDING_MAINTENANCE_TAX             numeric(9,0)                  
,    GENERAL_BUILDING_MANAGEMENT_BASE             numeric(9,0)                  
,    GENERAL_BUILDING_MANAGEMENT_TAX              numeric(9,0)                  
,    GENERAL_NEIGHBORHOOD_ASSOC_BASE              numeric(9,0)                  
,    GENERAL_NEIGHBORHOOD_ASSOC_TAX               numeric(9,0)                  
,    GENERAL_NEIGHBORHOOD_OTHER_BASE              numeric(9,0)                  
,    GENERAL_NEIGHBORHOOD_OTHER_TAX               numeric(9,0)                  
,    GENERAL_REPAYMENT_AGENT_BASE                 numeric(9,0)                  
,    GENERAL_REPAYMENT_AGENT_TAX                  numeric(9,0)                  
,    GENERAL_HL_COMMISSION_BASE                   numeric(9,0)                  
,    GENERAL_HL_COMMISSION_TAX                    numeric(9,0)                  
,    GENERAL_FURNISHED_BASE                       numeric(9,0)                  
,    GENERAL_FURNISHED_TAX                        numeric(9,0)                  
,    GENERAL_TENANT_DEPOSIT_BASE                  numeric(9,0)                  
,    GENERAL_TENANT_DEPOSIT_TAX                   numeric(9,0)                  
,    GENERAL_RENTAL_BASE                          numeric(9,0)                  
,    GENERAL_RENTAL_TAX                           numeric(9,0)                  
,    RESERVE_AMOUNT_1_BASE                        numeric(9,0)                  
,    RESERVE_AMOUNT_1_TAX                         numeric(9,0)                  
,    RESERVE_AMOUNT_2_BASE                        numeric(9,0)                  
,    RESERVE_AMOUNT_2_TAX                         numeric(9,0)                  
,    RESERVE_AMOUNT_3_BASE                        numeric(9,0)                  
,    RESERVE_AMOUNT_3_TAX                         numeric(9,0)                  
,    FLAG_RESERVE_11                              numeric(1,0)                  
,    FLAG_RESERVE_12                              numeric(1,0)                  
,    BUNDLE_WATER                                 numeric(1,0)                  
,    BUNDLE_ELECTRICITY                           numeric(1,0)                  
,    BUNDLE_GAS                                   numeric(1,0)                  
,    CATEGORY_2DIGIT_RESERVE_1                    varchar(2)                    
,    CATEGORY_2DIGIT_RESERVE_2                    varchar(2)                    
,    CATEGORY_2DIGIT_RESERVE_3                    varchar(2)                    
,    CATEGORY_2DIGIT_RESERVE_4                    varchar(2)                    
,    CATEGORY_2DIGIT_RESERVE_5                    varchar(2)                    
,    AMOUNT_RESERVE_4                             numeric(9,0)                  
,    AMOUNT_RESERVE_5                             numeric(9,0)                  
,    AMOUNT_RESERVE_6                             numeric(9,0)                  
,    AMOUNT_RESERVE_7                             numeric(9,0)                  
,    AMOUNT_RESERVE_8                             numeric(9,0)                  
,    DATE_RESERVE_14                              numeric(8,0)                  
,    DATE_RESERVE_15                              numeric(8,0)                  
,    DATE_RESERVE_16                              numeric(8,0)                  
,    DATE_RESERVE_17                              numeric(8,0)                  
,    DATE_RESERVE_18                              numeric(8,0)                  
,    CATEGORY_1DIGIT_RESERVE_1                    varchar(1)                    
,    CATEGORY_1DIGIT_RESERVE_2                    varchar(1)                    
,    CATEGORY_1DIGIT_RESERVE_3                    varchar(1)                    
,    CATEGORY_1DIGIT_RESERVE_4                    varchar(1)                    
,    CATEGORY_1DIGIT_RESERVE_5                    varchar(1)                    
,    LEASING_STORE_CD                             varchar(3)                    
,    MANAGEMENT_BRANCH_CD                         varchar(3)                    
,    SALES_OFFICE_CD                              varchar(3)                    
,    SCREENING_BRANCH_CD                          varchar(3)                    
,    PREFERENCE_100                               numeric(1,0)                  
,    PREFERENCE_101                               numeric(1,0)                  
,    PREFERENCE_102                               numeric(1,0)                  
,    PREFERENCE_103                               numeric(1,0)                  
,    PREFERENCE_104                               numeric(1,0)                  
,    PREFERENCE_105                               numeric(1,0)                  
,    PREFERENCE_106                               numeric(1,0)                  
,    PREFERENCE_107                               numeric(1,0)                  
,    PREFERENCE_108                               numeric(1,0)                  
,    PREFERENCE_109                               numeric(1,0)                  
,    PREFERENCE_110                               numeric(1,0)                  
,    PREFERENCE_111                               numeric(1,0)                  
,    PREFERENCE_112                               numeric(1,0)                  
,    PREFERENCE_113                               numeric(1,0)                  
,    PREFERENCE_114                               numeric(1,0)                  
,    PREFERENCE_115                               numeric(1,0)                  
,    PREFERENCE_116                               numeric(1,0)                  
,    PREFERENCE_117                               numeric(1,0)                  
,    PREFERENCE_118                               numeric(1,0)                  
,    PREFERENCE_119                               numeric(1,0)                  
,    PREFERENCE_120                               numeric(1,0)                  
,    PREFERENCE_121                               numeric(1,0)                  
,    PREFERENCE_122                               numeric(1,0)                  
,    PREFERENCE_123                               numeric(1,0)                  
,    PREFERENCE_124                               numeric(1,0)                  
,    PREFERENCE_125                               numeric(1,0)                  
,    PREFERENCE_126                               numeric(1,0)                  
,    PREFERENCE_127                               numeric(1,0)                  
,    PREFERENCE_128                               numeric(1,0)                  
,    PREFERENCE_129                               numeric(1,0)                  
,    PREFERENCE_130                               numeric(1,0)                  
,    PREFERENCE_131                               numeric(1,0)                  
,    PREFERENCE_132                               numeric(1,0)                  
,    PREFERENCE_133                               numeric(1,0)                  
,    PREFERENCE_134                               numeric(1,0)                  
,    PREFERENCE_135                               numeric(1,0)                  
,    PREFERENCE_136                               numeric(1,0)                  
,    PREFERENCE_137                               numeric(1,0)                  
,    PREFERENCE_138                               numeric(1,0)                  
,    PREFERENCE_139                               numeric(1,0)                  
,    PREFERENCE_140                               numeric(1,0)                  
,    PREFERENCE_141                               numeric(1,0)                  
,    PREFERENCE_142                               numeric(1,0)                  
,    PREFERENCE_143                               numeric(1,0)                  
,    PREFERENCE_144                               numeric(1,0)                  
,    PREFERENCE_145                               numeric(1,0)                  
,    PREFERENCE_146                               numeric(1,0)                  
,    PREFERENCE_147                               numeric(1,0)                  
,    PREFERENCE_148                               numeric(1,0)                  
,    PREFERENCE_149                               numeric(1,0)                  
,    PREFERENCE_150                               numeric(1,0)                  
,    PREFERENCE_151                               numeric(1,0)                  
,    PREFERENCE_152                               numeric(1,0)                  
,    PREFERENCE_153                               numeric(1,0)                  
,    PREFERENCE_154                               numeric(1,0)                  
,    PREFERENCE_155                               numeric(1,0)                  
,    PREFERENCE_156                               numeric(1,0)                  
,    PREFERENCE_157                               numeric(1,0)                  
,    PREFERENCE_158                               numeric(1,0)                  
,    PREFERENCE_159                               numeric(1,0)                  
,    PREFERENCE_160                               numeric(1,0)                  
,    PREFERENCE_161                               numeric(1,0)                  
,    PREFERENCE_162                               numeric(1,0)                  
,    PREFERENCE_163                               numeric(1,0)                  
,    PREFERENCE_164                               numeric(1,0)                  
,    PREFERENCE_165                               numeric(1,0)                  
,    PREFERENCE_166                               numeric(1,0)                  
,    PREFERENCE_167                               numeric(1,0)                  
,    PREFERENCE_168                               numeric(1,0)                  
,    PREFERENCE_169                               numeric(1,0)                  
,    PREFERENCE_170                               numeric(1,0)                  
,    PREFERENCE_171                               numeric(1,0)                  
,    PREFERENCE_172                               numeric(1,0)                  
,    PREFERENCE_173                               numeric(1,0)                  
,    PREFERENCE_174                               numeric(1,0)                  
,    PREFERENCE_175                               numeric(1,0)                  
,    PREFERENCE_176                               numeric(1,0)                  
,    PREFERENCE_177                               numeric(1,0)                  
,    PREFERENCE_178                               numeric(1,0)                  
,    PREFERENCE_179                               numeric(1,0)                  
,    PREFERENCE_180                               numeric(1,0)                  
,    PREFERENCE_181                               numeric(1,0)                  
,    PREFERENCE_182                               numeric(1,0)                  
,    PREFERENCE_183                               numeric(1,0)                  
,    PREFERENCE_184                               numeric(1,0)                  
,    PREFERENCE_185                               numeric(1,0)                  
,    PREFERENCE_186                               numeric(1,0)                  
,    PREFERENCE_187                               numeric(1,0)                  
,    PREFERENCE_188                               numeric(1,0)                  
,    PREFERENCE_189                               numeric(1,0)                  
,    PREFERENCE_190                               numeric(1,0)                  
,    PREFERENCE_191                               numeric(1,0)                  
,    PREFERENCE_192                               numeric(1,0)                  
,    PREFERENCE_193                               numeric(1,0)                  
,    PREFERENCE_194                               numeric(1,0)                  
,    PREFERENCE_195                               numeric(1,0)                  
,    PREFERENCE_196                               numeric(1,0)                  
,    PREFERENCE_197                               numeric(1,0)                  
,    PREFERENCE_198                               numeric(1,0)                  
,    PREFERENCE_199                               numeric(1,0)                  
,    MARKETING_BRANCH_OFFICE_CD                   varchar(3)                    
,    PROPERTY_SITUATION                           varchar(9)                    
,    PREFECTURE_CITY_CD                           varchar(6)                    
,    CONSTRAINT PK_ROOM_INFO_MASTER PRIMARY KEY (RECORD_TYPE, PROPERTY_CD_TYPE, PROPERTY_BUILDING_CD, PROPERTY_ROOM_CD)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE ROOM_INFO_MASTER IS '部屋情報マスタ 既存システム物理名: EMUR1P';
COMMENT ON COLUMN ROOM_INFO_MASTER.RECORD_TYPE IS 'レコード区分 既存システム物理名: EMERKB 1追加2更新3削除';
COMMENT ON COLUMN ROOM_INFO_MASTER.PROPERTY_CD_TYPE IS '物件CD区分 既存システム物理名: EMEK1C 建託物件＝1';
COMMENT ON COLUMN ROOM_INFO_MASTER.PROPERTY_CD_PART1 IS '物件CD区切1 既存システム物理名: EMEK2C －';
COMMENT ON COLUMN ROOM_INFO_MASTER.PROPERTY_BUILDING_CD IS '物件建物CD 既存システム物理名: EMEK3C 建物CD';
COMMENT ON COLUMN ROOM_INFO_MASTER.PROPERTY_CD_PART2 IS '物件CD区切2 既存システム物理名: EMEK4C －';
COMMENT ON COLUMN ROOM_INFO_MASTER.PROPERTY_ROOM_CD IS '物件部屋CD 既存システム物理名: EMEK5C 部屋CD';
COMMENT ON COLUMN ROOM_INFO_MASTER.DELETE_FLAG IS '削除フラグ 既存システム物理名: EMEDLF';
COMMENT ON COLUMN ROOM_INFO_MASTER.CUSTOMER_COMPANY_CD IS '客付会社CD 既存システム物理名: EME02C';
COMMENT ON COLUMN ROOM_INFO_MASTER.CUSTOMER_BRANCH_CD IS '客付支店CD 既存システム物理名: EME03C';
COMMENT ON COLUMN ROOM_INFO_MASTER.CUSTOMER_DEPARTMENT_CD IS '客付所属CD 既存システム物理名: EME04C';
COMMENT ON COLUMN ROOM_INFO_MASTER.CUSTOMER_COMPLETION_FLAG IS '客付完了フラグ 既存システム物理名: EMEKNF';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFECTURE_CD IS '県CD 既存システム物理名: EME05C';
COMMENT ON COLUMN ROOM_INFO_MASTER.CITY_CD IS '市区群町村CD 既存システム物理名: EME06C';
COMMENT ON COLUMN ROOM_INFO_MASTER.LINE_CD IS '路線CD 既存システム物理名: EME07C';
COMMENT ON COLUMN ROOM_INFO_MASTER.STATION_CD IS '駅CD 既存システム物理名: EME08C';
COMMENT ON COLUMN ROOM_INFO_MASTER.RENT IS '家賃 既存システム物理名: EMEYTN';
COMMENT ON COLUMN ROOM_INFO_MASTER.LAYOUT_ROOM_COUNT IS '間取り(部屋数) 既存システム物理名: EMEHYS';
COMMENT ON COLUMN ROOM_INFO_MASTER.EXCLUSIVE_AREA IS '専有面積 既存システム物理名: EMESY1';
COMMENT ON COLUMN ROOM_INFO_MASTER.PROPERTY_TYPE IS '物件タイプ 既存システム物理名: EME09C';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_1 IS 'こだわり1 既存システム物理名: EME01K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_2 IS 'こだわり2 既存システム物理名: EME02K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_3 IS 'こだわり3 既存システム物理名: EME03K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_4 IS 'こだわり4 既存システム物理名: EME04K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_5 IS 'こだわり5 既存システム物理名: EME05K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_6 IS 'こだわり6 既存システム物理名: EME06K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_7 IS 'こだわり7 既存システム物理名: EME07K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_8 IS 'こだわり8 既存システム物理名: EME08K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_9 IS 'こだわり9 既存システム物理名: EME09K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_10 IS 'こだわり10 既存システム物理名: EME10K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_11 IS 'こだわり11 既存システム物理名: EME11K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_12 IS 'こだわり12 既存システム物理名: EME12K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_13 IS 'こだわり13 既存システム物理名: EME13K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_14 IS 'こだわり14 既存システム物理名: EME14K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_15 IS 'こだわり15 既存システム物理名: EME15K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_16 IS 'こだわり16 既存システム物理名: EME16K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_17 IS 'こだわり17 既存システム物理名: EME17K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_18 IS 'こだわり18 既存システム物理名: EME18K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_19 IS 'こだわり19 既存システム物理名: EME19K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_20 IS 'こだわり20 既存システム物理名: EME20K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_21 IS 'こだわり21 既存システム物理名: EME21K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_22 IS 'こだわり22 既存システム物理名: EME22K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_23 IS 'こだわり23 既存システム物理名: EME23K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_24 IS 'こだわり24 既存システム物理名: EME24K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_25 IS 'こだわり25 既存システム物理名: EME25K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_26 IS 'こだわり26 既存システム物理名: EME26K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_27 IS 'こだわり27 既存システム物理名: EME27K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_28 IS 'こだわり28 既存システム物理名: EME28K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_29 IS 'こだわり29 既存システム物理名: EME29K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_30 IS 'こだわり30 既存システム物理名: EME30K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_31 IS 'こだわり31 既存システム物理名: EME31K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_32 IS 'こだわり32 既存システム物理名: EME32K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_33 IS 'こだわり33 既存システム物理名: EME33K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_34 IS 'こだわり34 既存システム物理名: EME34K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_35 IS 'こだわり35 既存システム物理名: EME35K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_36 IS 'こだわり36 既存システム物理名: EME36K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_37 IS 'こだわり37 既存システム物理名: EME37K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_38 IS 'こだわり38 既存システム物理名: EME38K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_39 IS 'こだわり39 既存システム物理名: EME39K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_40 IS 'こだわり40 既存システム物理名: EME40K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_41 IS 'こだわり41 既存システム物理名: EME41K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_42 IS 'こだわり42 既存システム物理名: EME42K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_43 IS 'こだわり43 既存システム物理名: EME43K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_44 IS 'こだわり44 既存システム物理名: EME44K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_45 IS 'こだわり45 既存システム物理名: EME45K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_46 IS 'こだわり46 既存システム物理名: EME46K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_47 IS 'こだわり47 既存システム物理名: EME47K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_48 IS 'こだわり48 既存システム物理名: EME48K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_49 IS 'こだわり49 既存システム物理名: EME49K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_50 IS 'こだわり50 既存システム物理名: EME50K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_51 IS 'こだわり51 既存システム物理名: EME51K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_52 IS 'こだわり52 既存システム物理名: EME52K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_53 IS 'こだわり53 既存システム物理名: EME53K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_54 IS 'こだわり54 既存システム物理名: EME54K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_55 IS 'こだわり55 既存システム物理名: EME55K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_56 IS 'こだわり56 既存システム物理名: EME56K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_57 IS 'こだわり57 既存システム物理名: EME57K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_58 IS 'こだわり58 既存システム物理名: EME58K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_59 IS 'こだわり59 既存システム物理名: EME59K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_60 IS 'こだわり60 既存システム物理名: EME60K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_99 IS 'こだわり99 既存システム物理名: EME99K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_NEW_BUILD IS 'こだわり新築 既存システム物理名: EMEA1K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_CORNER_ROOM IS 'こだわり角部屋 既存システム物理名: EMEA2K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_ABOVE_2ND_FLOOR IS 'こだわり2階以上 既存システム物理名: EMEA3K';
COMMENT ON COLUMN ROOM_INFO_MASTER.LINE_NAME IS '路線名称 既存システム物理名: EMERSM';
COMMENT ON COLUMN ROOM_INFO_MASTER.STATION_NAME IS '駅名称 既存システム物理名: EMEEKM';
COMMENT ON COLUMN ROOM_INFO_MASTER.BUS_STOP_NAME IS 'バス停名称 既存システム物理名: EMEBTM';
COMMENT ON COLUMN ROOM_INFO_MASTER.BUS_TIME IS 'バス時間 既存システム物理名: EMEBTT';
COMMENT ON COLUMN ROOM_INFO_MASTER.WALKING_TIME IS '徒歩時間 既存システム物理名: EMETTT';
COMMENT ON COLUMN ROOM_INFO_MASTER.DISTANCE IS '距離 既存システム物理名: EMEKYO';
COMMENT ON COLUMN ROOM_INFO_MASTER.KEY_MONEY IS '礼金 既存システム物理名: EMERK1';
COMMENT ON COLUMN ROOM_INFO_MASTER.DEPOSIT IS '敷金／保証金 既存システム物理名: EMESI1';
COMMENT ON COLUMN ROOM_INFO_MASTER.NEIGHBORHOOD_ASSOCIATION_FEE IS '町内会費 既存システム物理名: EMESO1';
COMMENT ON COLUMN ROOM_INFO_MASTER.COMMON_SERVICE_FEE IS '共益費(管理費) 既存システム物理名: EMEKE1';
COMMENT ON COLUMN ROOM_INFO_MASTER.ROOM_TYPE_NAME IS '部屋種別名称 既存システム物理名: EME05M';
COMMENT ON COLUMN ROOM_INFO_MASTER.LAYOUT_TYPE IS '間取り区分 既存システム物理名: EMEBPB';
COMMENT ON COLUMN ROOM_INFO_MASTER.LAYOUT IS '間取り 既存システム物理名: EMEMDR';
COMMENT ON COLUMN ROOM_INFO_MASTER.LAYOUT_DETAILS IS '間取り内容 既存システム物理名: EMEMDN';
COMMENT ON COLUMN ROOM_INFO_MASTER.PARKING_TYPE IS '駐車場区分 既存システム物理名: EMETSK';
COMMENT ON COLUMN ROOM_INFO_MASTER.PARKING_FEE IS '駐車料 既存システム物理名: EMETS1';
COMMENT ON COLUMN ROOM_INFO_MASTER.CONSTRUCTION_YEAR_MONTH IS '築年月 既存システム物理名: EMEKN1';
COMMENT ON COLUMN ROOM_INFO_MASTER.HANDLING_STORE_COMPANY IS '取扱店舗(会社) 既存システム物理名: EMETRT';
COMMENT ON COLUMN ROOM_INFO_MASTER.LOCATION_LISTING_AREA IS '所在地(掲載地区名 既存システム物理名: EMESZT';
COMMENT ON COLUMN ROOM_INFO_MASTER.FLOOR_NUMBER IS '階数 既存システム物理名: EMEKS1';
COMMENT ON COLUMN ROOM_INFO_MASTER.DIRECTION IS '方位 既存システム物理名: EMEHOI';
COMMENT ON COLUMN ROOM_INFO_MASTER.ROOM_POSITION IS '部屋位置 既存システム物理名: EMEHYI';
COMMENT ON COLUMN ROOM_INFO_MASTER.AVAILABLE_MOVE_IN_YEAR_MONTH IS '入居可能年月 既存システム物理名: EMESK1';
COMMENT ON COLUMN ROOM_INFO_MASTER.TRANSPORTATION IS '交通 既存システム物理名: EMEKTU';
COMMENT ON COLUMN ROOM_INFO_MASTER.EQUIPMENT IS '設備 既存システム物理名: EMESTB';
COMMENT ON COLUMN ROOM_INFO_MASTER.NOTES IS '備考 既存システム物理名: EMEBKO';
COMMENT ON COLUMN ROOM_INFO_MASTER.INQUIRY_BRANCH_NAME IS '問い合わせ支店名称 既存システム物理名: EMETAM';
COMMENT ON COLUMN ROOM_INFO_MASTER.BRANCH_PHONE_NUMBER IS '支店電話番号 既存システム物理名: EMETAT';
COMMENT ON COLUMN ROOM_INFO_MASTER.BRANCH_FAX_NUMBER IS '支店FAX番号 既存システム物理名: EMETAF';
COMMENT ON COLUMN ROOM_INFO_MASTER.TRANSACTION_TYPE IS '取引形態 既存システム物理名: EMETKM';
COMMENT ON COLUMN ROOM_INFO_MASTER.BUILDING_NAME IS '建物名称 既存システム物理名: EMEBKM';
COMMENT ON COLUMN ROOM_INFO_MASTER.STRUCTURE_NAME IS '構造名称 既存システム物理名: EMEKZM';
COMMENT ON COLUMN ROOM_INFO_MASTER.AGENT_ASSIGNABLE_TYPE IS '業者付け可能区分 既存システム物理名: EMEGKK';
COMMENT ON COLUMN ROOM_INFO_MASTER.SUBLEASE_TYPE IS 'サブリース区分 既存システム物理名: EMESRK';
COMMENT ON COLUMN ROOM_INFO_MASTER.CREATION_DATE IS '作成日 既存システム物理名: EMESKD';
COMMENT ON COLUMN ROOM_INFO_MASTER.CREATION_TIME IS '作成時間 既存システム物理名: EMESKT';
COMMENT ON COLUMN ROOM_INFO_MASTER.CREATOR IS '作成者 既存システム物理名: EMESKS';
COMMENT ON COLUMN ROOM_INFO_MASTER.UPDATE_DATE IS '更新日 既存システム物理名: EMEHKD';
COMMENT ON COLUMN ROOM_INFO_MASTER.UPDATE_TIME IS '更新時間 既存システム物理名: EMEHKT';
COMMENT ON COLUMN ROOM_INFO_MASTER.UPDATER IS '更新者 既存システム物理名: EMEHKS';
COMMENT ON COLUMN ROOM_INFO_MASTER.BRANCH_ADDRESS IS '支店住所 既存システム物理名: EMEADR';
COMMENT ON COLUMN ROOM_INFO_MASTER.RECOMMENDATION_COMMENT IS 'おすすめコメント 既存システム物理名: EMEOSS';
COMMENT ON COLUMN ROOM_INFO_MASTER.COMPLETION_YEAR_MONTH IS '完工年月 既存システム物理名: EMEKAN';
COMMENT ON COLUMN ROOM_INFO_MASTER.PROPERTY_POSTAL_CODE IS '物件郵便番号 既存システム物理名: EMEYUB';
COMMENT ON COLUMN ROOM_INFO_MASTER.VACATE_NOTICE_DATE IS '明渡通告日 既存システム物理名: EMEAKD';
COMMENT ON COLUMN ROOM_INFO_MASTER.EXPECTED_MOVE_OUT_DATE IS '退去予定日 既存システム物理名: EMETYD';
COMMENT ON COLUMN ROOM_INFO_MASTER.MOVE_OUT_DATE IS '退去日 既存システム物理名: EMETKD';
COMMENT ON COLUMN ROOM_INFO_MASTER.EXPECTED_COMPLETION_DATE IS '完工予定日 既存システム物理名: EMEKYD';
COMMENT ON COLUMN ROOM_INFO_MASTER.AVAILABLE_MOVE_IN_DATE IS '入居可能日 既存システム物理名: EMENKD';
COMMENT ON COLUMN ROOM_INFO_MASTER.MOVE_IN_APPLICATION_DATE IS '入居申込日 既存システム物理名: EMENMD';
COMMENT ON COLUMN ROOM_INFO_MASTER.DEPOSIT_DATE IS '手付日 既存システム物理名: EMETED';
COMMENT ON COLUMN ROOM_INFO_MASTER.BALANCE_COLLECTION_DATE IS '残集日 既存システム物理名: EMEZAD';
COMMENT ON COLUMN ROOM_INFO_MASTER.MOVE_IN_DATE IS '入居日 既存システム物理名: EMENYD';
COMMENT ON COLUMN ROOM_INFO_MASTER.COMPLETION_DATE IS '完工日 既存システム物理名: EMEKAD';
COMMENT ON COLUMN ROOM_INFO_MASTER.TENANT_RECRUITMENT_COLLECTION_DATE IS '入居者斡旋回収日 既存システム物理名: EMENAD';
COMMENT ON COLUMN ROOM_INFO_MASTER.TENANT IS '入居者 既存システム物理名: EMENYM';
COMMENT ON COLUMN ROOM_INFO_MASTER.OWNER IS '施主様 既存システム物理名: EMESSM';
COMMENT ON COLUMN ROOM_INFO_MASTER.CUSTOMER_AGENT_BRANCH_CD IS '客付担当者支店CD 既存システム物理名: EMESTC';
COMMENT ON COLUMN ROOM_INFO_MASTER.CUSTOMER_AGENT_DEPARTMENT_CD IS '客付担当者所属CD 既存システム物理名: EMESZC';
COMMENT ON COLUMN ROOM_INFO_MASTER.CUSTOMER_AGENT_EMPLOYEE_CD IS '客付担当者社員CD 既存システム物理名: EMESYC';
COMMENT ON COLUMN ROOM_INFO_MASTER.RENT_TAX IS '家賃(消費税) 既存システム物理名: EMEYTS';
COMMENT ON COLUMN ROOM_INFO_MASTER.KEY_MONEY_TAX IS '礼金(消費税) 既存システム物理名: EMERKS';
COMMENT ON COLUMN ROOM_INFO_MASTER.KEY_MONEY_TOTAL IS '礼金(総額) 既存システム物理名: EMERKG';
COMMENT ON COLUMN ROOM_INFO_MASTER.COMMON_SERVICE_FEE_TAX IS '共益費(消費税) 既存システム物理名: EMEKES';
COMMENT ON COLUMN ROOM_INFO_MASTER.PARKING_FEE_TAX IS '駐車料(消費税) 既存システム物理名: EMETSS';
COMMENT ON COLUMN ROOM_INFO_MASTER.ROOM_NUMBER IS '部屋番号 既存システム物理名: EME31N';
COMMENT ON COLUMN ROOM_INFO_MASTER.NEW_EXISTING_FLAG IS '新規・既存フラグ 既存システム物理名: EMESKF';
COMMENT ON COLUMN ROOM_INFO_MASTER.ROOM_STATUS_TYPE IS '部屋状況区分 既存システム物理名: EMEHJM';
COMMENT ON COLUMN ROOM_INFO_MASTER.RECORD_STATUS_TYPE IS 'レコード状態区分 既存システム物理名: EMERJM';
COMMENT ON COLUMN ROOM_INFO_MASTER.FF_USAGE_PERIOD IS 'FF使用可能期間 既存システム物理名: EMEFFM';
COMMENT ON COLUMN ROOM_INFO_MASTER.AD_PAYABLE_AMOUNT IS 'AD支払可能金額 既存システム物理名: EMEADM';
COMMENT ON COLUMN ROOM_INFO_MASTER.LOCATION_CITY IS '所在地(市区郡) 既存システム物理名: EMESZS';
COMMENT ON COLUMN ROOM_INFO_MASTER.TENANT_CONTRACT_NUMBER IS 'テナント契約番号 既存システム物理名: EMEAKN';
COMMENT ON COLUMN ROOM_INFO_MASTER.OWNER_CONTACT IS '施主様連絡先 既存システム物理名: EMESST';
COMMENT ON COLUMN ROOM_INFO_MASTER.VACANT_PERIOD IS '空家期間 既存システム物理名: EMEAKS';
COMMENT ON COLUMN ROOM_INFO_MASTER.FLOOR_AREA_1F IS '1階床面積(平米) 既存システム物理名: EME43Q';
COMMENT ON COLUMN ROOM_INFO_MASTER.FLOOR_AREA_2F IS '2階床面積(平米) 既存システム物理名: EME44Q';
COMMENT ON COLUMN ROOM_INFO_MASTER.FLOOR_AREA_3F IS '3階床面積(平米) 既存システム物理名: EME45Q';
COMMENT ON COLUMN ROOM_INFO_MASTER.RECRUITMENT_CREATION_DATE IS '斡旋作成日 既存システム物理名: EME27D';
COMMENT ON COLUMN ROOM_INFO_MASTER.RECRUITMENT_APPROVAL_DATE IS '斡旋承認日 既存システム物理名: EME41D';
COMMENT ON COLUMN ROOM_INFO_MASTER.MOVE_OUT_INSPECTION_DATE IS '退居立会予定日 既存システム物理名: EMET1D';
COMMENT ON COLUMN ROOM_INFO_MASTER.RESTORATION_DATE IS '現状回復予定日 既存システム物理名: EMEG1D';
COMMENT ON COLUMN ROOM_INFO_MASTER.RESTORATION_COMPLETION_DATE IS '現状回復完了日 既存システム物理名: EMEG2D';
COMMENT ON COLUMN ROOM_INFO_MASTER.VACANT_BOOKING_DATE IS '空家計上予定日 既存システム物理名: EMEA1D';
COMMENT ON COLUMN ROOM_INFO_MASTER.VACANT_BOOKING_COMPLETION_DATE IS '空家計上日 既存システム物理名: EMEA2D';
COMMENT ON COLUMN ROOM_INFO_MASTER.ADDITIONAL_KEY_MONEY IS '上乗礼金額(内額) 既存システム物理名: EME43A';
COMMENT ON COLUMN ROOM_INFO_MASTER.MUTUAL_AID_JOIN_SIGN IS '共済会加入サイン 既存システム物理名: EME13S';
COMMENT ON COLUMN ROOM_INFO_MASTER.RENTAL_TYPE IS '賃貸区分 既存システム物理名: EME07B';
COMMENT ON COLUMN ROOM_INFO_MASTER.SPECIAL_RENTAL_TYPE IS '特優賃区分 既存システム物理名: EMETKY';
COMMENT ON COLUMN ROOM_INFO_MASTER.DISTANCE2 IS '距離 既存システム物理名: EMEKYR';
COMMENT ON COLUMN ROOM_INFO_MASTER.APPROVAL_TYPE IS '承諾区分 既存システム物理名: EME11C';
COMMENT ON COLUMN ROOM_INFO_MASTER.RECORD_SEPARATOR IS 'レコード区切り 既存システム物理名: EMEEND';
COMMENT ON COLUMN ROOM_INFO_MASTER.CHANGE_TYPE IS '変更区分 既存システム物理名: EMECHG';
COMMENT ON COLUMN ROOM_INFO_MASTER.NO_DEPOSIT_FLAG IS '敷金ゼロ判定フラグ 既存システム物理名: EME12C';
COMMENT ON COLUMN ROOM_INFO_MASTER.CAMPAIGN_TARGET_FLAG IS 'キャンペーン対象フラグ 既存システム物理名: EME13C';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_61 IS 'こだわり61 既存システム物理名: EME61K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_62 IS 'こだわり62 既存システム物理名: EME62K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_63 IS 'こだわり63 既存システム物理名: EME63K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_64 IS 'こだわり64 既存システム物理名: EME64K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_65 IS 'こだわり65 既存システム物理名: EME65K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_66 IS 'こだわり66 既存システム物理名: EME66K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_67 IS 'こだわり67 既存システム物理名: EME67K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_68 IS 'こだわり68 既存システム物理名: EME68K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_69 IS 'こだわり69 既存システム物理名: EME69K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_70 IS 'こだわり70 既存システム物理名: EME70K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_71 IS 'こだわり71 既存システム物理名: EME71K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_72 IS 'こだわり72 既存システム物理名: EME72K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_73 IS 'こだわり73 既存システム物理名: EME73K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_74 IS 'こだわり74 既存システム物理名: EME74K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_75 IS 'こだわり75 既存システム物理名: EME75K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_76 IS 'こだわり76 既存システム物理名: EME76K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_77 IS 'こだわり77 既存システム物理名: EME77K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_78 IS 'こだわり78 既存システム物理名: EME78K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_79 IS 'こだわり79 既存システム物理名: EME79K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_80 IS 'こだわり80 既存システム物理名: EME80K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_81 IS 'こだわり81 既存システム物理名: EME81K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_82 IS 'こだわり82 既存システム物理名: EME82K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_83 IS 'こだわり83 既存システム物理名: EME83K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_84 IS 'こだわり84 既存システム物理名: EME84K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_85 IS 'こだわり85 既存システム物理名: EME85K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_86 IS 'こだわり86 既存システム物理名: EME86K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_87 IS 'こだわり87 既存システム物理名: EME87K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_88 IS 'こだわり88 既存システム物理名: EME88K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_89 IS 'こだわり89 既存システム物理名: EME89K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_90 IS 'こだわり90 既存システム物理名: EME90K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_91 IS 'こだわり91 既存システム物理名: EME91K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_92 IS 'こだわり92 既存システム物理名: EME92K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_93 IS 'こだわり93 既存システム物理名: EME93K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_94 IS 'こだわり94 既存システム物理名: EME94K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_95 IS 'こだわり95 既存システム物理名: EME95K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_96 IS 'こだわり96 既存システム物理名: EME96K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_97 IS 'こだわり97 既存システム物理名: EME97K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_98 IS 'こだわり98 既存システム物理名: EME98K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PROPERTY_ADDRESS IS '物件住所 既存システム物理名: EMEJST';
COMMENT ON COLUMN ROOM_INFO_MASTER.PROPERTY_ADDRESS_DETAIL IS '物件住所詳細 既存システム物理名: EMEJSJ';
COMMENT ON COLUMN ROOM_INFO_MASTER.SERVICE_ROOM_SIGN IS 'サービスルームサイン 既存システム物理名: EMEA1S';
COMMENT ON COLUMN ROOM_INFO_MASTER.HIGH_VOLTAGE_BULK_RECEIPT IS '高圧電力一括受電 既存システム物理名: EMEA2S';
COMMENT ON COLUMN ROOM_INFO_MASTER.HIGH_RENTAL_SIGN IS '高円賃サイン 既存システム物理名: EMEA3S';
COMMENT ON COLUMN ROOM_INFO_MASTER.SOLAR_DISCOUNT_TARGET IS '太陽光割引対象 既存システム物理名: EMEA4S';
COMMENT ON COLUMN ROOM_INFO_MASTER.CLEANING_COST_FIXED IS 'クリーニング費定額 既存システム物理名: EMEA5S';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREVIOUS_RENT IS '従前家賃 既存システム物理名: EMEJZN';
COMMENT ON COLUMN ROOM_INFO_MASTER.EXISTING_REVIEW_UPDATE_DATE IS '既存審査更新日 既存システム物理名: EMEA3D';
COMMENT ON COLUMN ROOM_INFO_MASTER.MOVE_OUT_INSPECTION_TIME IS '退居立会予定時間 既存システム物理名: EMEA4D';
COMMENT ON COLUMN ROOM_INFO_MASTER.RECRUITMENT_START_DATE IS '募集開始日 既存システム物理名: EMEA5D';
COMMENT ON COLUMN ROOM_INFO_MASTER.CLEANING_COST_TOTAL IS 'クリーニング費総額 既存システム物理名: EMEA6A';
COMMENT ON COLUMN ROOM_INFO_MASTER.DISCOUNT_INITIAL_VALUE_SIGN IS '割引初期値サイン 既存システム物理名: EMEB2S';
COMMENT ON COLUMN ROOM_INFO_MASTER.FLAG_RESERVE_7 IS 'フラグ予備7 既存システム物理名: EMEB3S';
COMMENT ON COLUMN ROOM_INFO_MASTER.PET_FLAG IS 'ペットフラグ 既存システム物理名: EMEB4S';
COMMENT ON COLUMN ROOM_INFO_MASTER.FLAG_RESERVE_9 IS 'フラグ予備9 既存システム物理名: EMEB5S';
COMMENT ON COLUMN ROOM_INFO_MASTER.FLAG_RESERVE_10 IS 'フラグ予備10 既存システム物理名: EMEB6S';
COMMENT ON COLUMN ROOM_INFO_MASTER.CHALLENGE_START_DATE IS 'チャレンジ開始日 既存システム物理名: EMEB7D';
COMMENT ON COLUMN ROOM_INFO_MASTER.CHALLENGE_END_DATE IS 'チャレンジ終了日 既存システム物理名: EMEB8D';
COMMENT ON COLUMN ROOM_INFO_MASTER.APPLICATION_END_DATE IS '申込可能終了日 既存システム物理名: EMEB9D';
COMMENT ON COLUMN ROOM_INFO_MASTER.MOVE_IN_END_DATE IS '入居可能終了日 既存システム物理名: EMEC1D';
COMMENT ON COLUMN ROOM_INFO_MASTER.ADDITIONAL_RELEASE_DATE IS '上乗せ解除可能日／めやす日 既存システム物理名: EMEC2D';
COMMENT ON COLUMN ROOM_INFO_MASTER.RECRUITMENT_RENT IS '募集家賃 既存システム物理名: EMEC3A';
COMMENT ON COLUMN ROOM_INFO_MASTER.CHALLENGE_ADDITIONAL_AMOUNT IS 'チャレンジ上乗せ額 既存システム物理名: EMEC4A';
COMMENT ON COLUMN ROOM_INFO_MASTER.REVIEW_RENT IS '審査家賃 既存システム物理名: EMEC5A';
COMMENT ON COLUMN ROOM_INFO_MASTER.DATE_RESERVE_11 IS '日付予備11 既存システム物理名: EMEC6D';
COMMENT ON COLUMN ROOM_INFO_MASTER.DATE_RESERVE_12 IS '日付予備12 既存システム物理名: EMEC7D';
COMMENT ON COLUMN ROOM_INFO_MASTER.DATE_RESERVE_13 IS '日付予備13 既存システム物理名: EMEC8D';
COMMENT ON COLUMN ROOM_INFO_MASTER.AMOUNT_RESERVE_1 IS '金額予備1 既存システム物理名: EMEC9A';
COMMENT ON COLUMN ROOM_INFO_MASTER.AMOUNT_RESERVE_2 IS '金額予備2 既存システム物理名: EMED1A';
COMMENT ON COLUMN ROOM_INFO_MASTER.AMOUNT_RESERVE_3 IS '金額予備3 既存システム物理名: EMED2A';
COMMENT ON COLUMN ROOM_INFO_MASTER.COMMON_SERVICE_FEE_BASE IS '共益費(本体) 既存システム物理名: EMEKE2';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_CABLE_TV_BASE IS '一般 (ケーブルTV) 本 既存システム物理名: EMED3A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_CABLE_TV_TAX IS '一般 (ケーブルTV) 税 既存システム物理名: EMED3S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_INTERNET_BASE IS '一般 (インターネット) 本 既存システム物理名: EMED4A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_INTERNET_TAX IS '一般 (インターネット) 税 既存システム物理名: EMED4S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_WATER_QUALITY_BASE IS '一般(水質保全)本 既存システム物理名: EMED5A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_WATER_QUALITY_TAX IS '一般(水質保全)税 既存システム物理名: EMED5S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_TENANT_WATER_BASE IS '一般(テナ水道)本 既存システム物理名: EMED6A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_TENANT_WATER_TAX IS '一般(テナ水道)税 既存システム物理名: EMED6S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_DRAIN_USE_BASE IS '一般(排水溝使)本 既存システム物理名: EMED7A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_DRAIN_USE_TAX IS '一般(排水溝使)税 既存システム物理名: EMED7S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_GARBAGE_COLLECTION_BASE IS '一般(ゴミ回収)本 既存システム物理名: EMED8A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_GARBAGE_COLLECTION_TAX IS '一般(ゴミ回収)税 既存システム物理名: EMED8S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_SHARED_ANTENNA_BASE IS '一般(共同アン)本 既存システム物理名: EMED9A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_SHARED_ANTENNA_TAX IS '一般(共同アン)税 既存システム物理名: EMED9S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_OWNER_CLEANING_BASE IS '一般(家主清掃)本 既存システム物理名: EMEE1A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_OWNER_CLEANING_TAX IS '一般(家主清掃)税 既存システム物理名: EMEE1S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_BUILDING_MAINTENANCE_BASE IS '一般(建物維持)本 既存システム物理名: EMEE2A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_BUILDING_MAINTENANCE_TAX IS '一般(建物維持)税 既存システム物理名: EMEE2S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_BUILDING_MANAGEMENT_BASE IS '一般(建物管理)本 既存システム物理名: EMEE3A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_BUILDING_MANAGEMENT_TAX IS '一般(建物管理)税 既存システム物理名: EMEE3S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_NEIGHBORHOOD_ASSOC_BASE IS '一般(自治会)本 既存システム物理名: EMEE4A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_NEIGHBORHOOD_ASSOC_TAX IS '一般(自治会)税 既存システム物理名: EMEE4S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_NEIGHBORHOOD_OTHER_BASE IS '一般(自治会他)本 既存システム物理名: EMEE5A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_NEIGHBORHOOD_OTHER_TAX IS '一般(自治会他)税 既存システム物理名: EMEE5S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_REPAYMENT_AGENT_BASE IS '一般(返済代行)本 既存システム物理名: EMEE6A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_REPAYMENT_AGENT_TAX IS '一般(返済代行)税 既存システム物理名: EMEE6S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_HL_COMMISSION_BASE IS '一般(HL委託)本 既存システム物理名: EMEE7A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_HL_COMMISSION_TAX IS '一般(HL委託)税 既存システム物理名: EMEE7S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_FURNISHED_BASE IS '一般(家具付)本体 既存システム物理名: EMEE8A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_FURNISHED_TAX IS '一般(家具付)税 既存システム物理名: EMEE8S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_TENANT_DEPOSIT_BASE IS '一般(入居者預)本 既存システム物理名: EMEE9A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_TENANT_DEPOSIT_TAX IS '一般(入居者預)税 既存システム物理名: EMEE9S';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_RENTAL_BASE IS '一般(レンタル)本 既存システム物理名: EMEF1A';
COMMENT ON COLUMN ROOM_INFO_MASTER.GENERAL_RENTAL_TAX IS '一般(レンタル)税 既存システム物理名: EMEF1S';
COMMENT ON COLUMN ROOM_INFO_MASTER.RESERVE_AMOUNT_1_BASE IS '予備金額1(本体) 既存システム物理名: EMEF2A';
COMMENT ON COLUMN ROOM_INFO_MASTER.RESERVE_AMOUNT_1_TAX IS '予備金額1(税) 既存システム物理名: EMEF2S';
COMMENT ON COLUMN ROOM_INFO_MASTER.RESERVE_AMOUNT_2_BASE IS '予備金額2(本体) 既存システム物理名: EMEF3A';
COMMENT ON COLUMN ROOM_INFO_MASTER.RESERVE_AMOUNT_2_TAX IS '予備金額2(税) 既存システム物理名: EMEF3S';
COMMENT ON COLUMN ROOM_INFO_MASTER.RESERVE_AMOUNT_3_BASE IS '予備金額3(本体) 既存システム物理名: EMEF4A';
COMMENT ON COLUMN ROOM_INFO_MASTER.RESERVE_AMOUNT_3_TAX IS '予備金額3(税) 既存システム物理名: EMEF4S';
COMMENT ON COLUMN ROOM_INFO_MASTER.FLAG_RESERVE_11 IS 'フラグ予備11 既存システム物理名: EMEF5S';
COMMENT ON COLUMN ROOM_INFO_MASTER.FLAG_RESERVE_12 IS 'フラグ予備12 既存システム物理名: EMEF6S';
COMMENT ON COLUMN ROOM_INFO_MASTER.BUNDLE_WATER IS 'おまとめ(水道) 既存システム物理名: EMEF7S';
COMMENT ON COLUMN ROOM_INFO_MASTER.BUNDLE_ELECTRICITY IS 'おまとめ(電気) 既存システム物理名: EMEF8S';
COMMENT ON COLUMN ROOM_INFO_MASTER.BUNDLE_GAS IS 'おまとめ(ガス) 既存システム物理名: EMEF9S';
COMMENT ON COLUMN ROOM_INFO_MASTER.CATEGORY_2DIGIT_RESERVE_1 IS '区分(2ケタ)予備1 既存システム物理名: EMEG1B';
COMMENT ON COLUMN ROOM_INFO_MASTER.CATEGORY_2DIGIT_RESERVE_2 IS '区分(2ケタ)予備2 既存システム物理名: EMEG2B';
COMMENT ON COLUMN ROOM_INFO_MASTER.CATEGORY_2DIGIT_RESERVE_3 IS '区分(2ケタ)予備3 既存システム物理名: EMEG3B';
COMMENT ON COLUMN ROOM_INFO_MASTER.CATEGORY_2DIGIT_RESERVE_4 IS '区分(2ケタ)予備4 既存システム物理名: EMEG4B';
COMMENT ON COLUMN ROOM_INFO_MASTER.CATEGORY_2DIGIT_RESERVE_5 IS '区分(2ケタ)予備5 既存システム物理名: EMEG5B';
COMMENT ON COLUMN ROOM_INFO_MASTER.AMOUNT_RESERVE_4 IS '金額予備4 既存システム物理名: EMEG6A';
COMMENT ON COLUMN ROOM_INFO_MASTER.AMOUNT_RESERVE_5 IS '金額予備5 既存システム物理名: EMEG7A';
COMMENT ON COLUMN ROOM_INFO_MASTER.AMOUNT_RESERVE_6 IS '金額予備6 既存システム物理名: EMEG8A';
COMMENT ON COLUMN ROOM_INFO_MASTER.AMOUNT_RESERVE_7 IS '金額予備7 既存システム物理名: EMEG9A';
COMMENT ON COLUMN ROOM_INFO_MASTER.AMOUNT_RESERVE_8 IS '金額予備8 既存システム物理名: EMEH1A';
COMMENT ON COLUMN ROOM_INFO_MASTER.DATE_RESERVE_14 IS '日付予備14 既存システム物理名: EMEH2D';
COMMENT ON COLUMN ROOM_INFO_MASTER.DATE_RESERVE_15 IS '日付予備15 既存システム物理名: EMEH3D';
COMMENT ON COLUMN ROOM_INFO_MASTER.DATE_RESERVE_16 IS '日付予備16 既存システム物理名: EMEH4D';
COMMENT ON COLUMN ROOM_INFO_MASTER.DATE_RESERVE_17 IS '日付予備17 既存システム物理名: EMEH5D';
COMMENT ON COLUMN ROOM_INFO_MASTER.DATE_RESERVE_18 IS '日付予備18 既存システム物理名: EMEH6D';
COMMENT ON COLUMN ROOM_INFO_MASTER.CATEGORY_1DIGIT_RESERVE_1 IS '区分(1ケタ)予備1 既存システム物理名: EMEH7B';
COMMENT ON COLUMN ROOM_INFO_MASTER.CATEGORY_1DIGIT_RESERVE_2 IS '区分(1ケタ)予備2 既存システム物理名: EMEH8B';
COMMENT ON COLUMN ROOM_INFO_MASTER.CATEGORY_1DIGIT_RESERVE_3 IS '区分(1ケタ)予備3 既存システム物理名: EMEH9B';
COMMENT ON COLUMN ROOM_INFO_MASTER.CATEGORY_1DIGIT_RESERVE_4 IS '区分(1ケタ)予備4 既存システム物理名: EMEI1B';
COMMENT ON COLUMN ROOM_INFO_MASTER.CATEGORY_1DIGIT_RESERVE_5 IS '区分(1ケタ)予備5 既存システム物理名: EMEI2B';
COMMENT ON COLUMN ROOM_INFO_MASTER.LEASING_STORE_CD IS 'リーシング店舗CD 既存システム物理名: EMEI3C';
COMMENT ON COLUMN ROOM_INFO_MASTER.MANAGEMENT_BRANCH_CD IS '管理支店CD 既存システム物理名: EMEI4C';
COMMENT ON COLUMN ROOM_INFO_MASTER.SALES_OFFICE_CD IS '営業所CD 既存システム物理名: EMEI5C';
COMMENT ON COLUMN ROOM_INFO_MASTER.SCREENING_BRANCH_CD IS '審査支店CD 既存システム物理名: EMEI6C';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_100 IS 'こだわり100 既存システム物理名: EMEB0K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_101 IS 'こだわり101 既存システム物理名: EMEB1K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_102 IS 'こだわり102 既存システム物理名: EMEB2K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_103 IS 'こだわり103 既存システム物理名: EMEB3K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_104 IS 'こだわり104 既存システム物理名: EMEB4K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_105 IS 'こだわり105 既存システム物理名: EMEB5K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_106 IS 'こだわり106 既存システム物理名: EMEB6K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_107 IS 'こだわり107 既存システム物理名: EMEB7K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_108 IS 'こだわり108 既存システム物理名: EMEB8K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_109 IS 'こだわり109 既存システム物理名: EMEB9K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_110 IS 'こだわり110 既存システム物理名: EMEC0K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_111 IS 'こだわり111 既存システム物理名: EMEC1K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_112 IS 'こだわり112 既存システム物理名: EMEC2K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_113 IS 'こだわり113 既存システム物理名: EMEC3K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_114 IS 'こだわり114 既存システム物理名: EMEC4K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_115 IS 'こだわり115 既存システム物理名: EMEC5K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_116 IS 'こだわり116 既存システム物理名: EMEC6K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_117 IS 'こだわり117 既存システム物理名: EMEC7K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_118 IS 'こだわり118 既存システム物理名: EMEC8K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_119 IS 'こだわり119 既存システム物理名: EMEC9K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_120 IS 'こだわり120 既存システム物理名: EMED0K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_121 IS 'こだわり121 既存システム物理名: EMED1K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_122 IS 'こだわり122 既存システム物理名: EMED2K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_123 IS 'こだわり123 既存システム物理名: EMED3K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_124 IS 'こだわり124 既存システム物理名: EMED4K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_125 IS 'こだわり125 既存システム物理名: EMED5K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_126 IS 'こだわり126 既存システム物理名: EMED6K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_127 IS 'こだわり127 既存システム物理名: EMED7K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_128 IS 'こだわり128 既存システム物理名: EMED8K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_129 IS 'こだわり129 既存システム物理名: EMED9K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_130 IS 'こだわり130 既存システム物理名: EMEE0K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_131 IS 'こだわり131 既存システム物理名: EMEE1K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_132 IS 'こだわり132 既存システム物理名: EMEE2K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_133 IS 'こだわり133 既存システム物理名: EMEE3K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_134 IS 'こだわり134 既存システム物理名: EMEE4K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_135 IS 'こだわり135 既存システム物理名: EMEE5K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_136 IS 'こだわり136 既存システム物理名: EMEE6K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_137 IS 'こだわり137 既存システム物理名: EMEE7K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_138 IS 'こだわり138 既存システム物理名: EMEE8K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_139 IS 'こだわり139 既存システム物理名: EMEE9K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_140 IS 'こだわり140 既存システム物理名: EMEF0K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_141 IS 'こだわり141 既存システム物理名: EMEF1K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_142 IS 'こだわり142 既存システム物理名: EMEF2K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_143 IS 'こだわり143 既存システム物理名: EMEF3K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_144 IS 'こだわり144 既存システム物理名: EMEF4K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_145 IS 'こだわり145 既存システム物理名: EMEF5K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_146 IS 'こだわり146 既存システム物理名: EMEF6K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_147 IS 'こだわり147 既存システム物理名: EMEF7K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_148 IS 'こだわり148 既存システム物理名: EMEF8K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_149 IS 'こだわり149 既存システム物理名: EMEF9K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_150 IS 'こだわり150 既存システム物理名: EMEG0K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_151 IS 'こだわり151 既存システム物理名: EMEG1K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_152 IS 'こだわり152 既存システム物理名: EMEG2K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_153 IS 'こだわり153 既存システム物理名: EMEG3K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_154 IS 'こだわり154 既存システム物理名: EMEG4K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_155 IS 'こだわり155 既存システム物理名: EMEG5K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_156 IS 'こだわり156 既存システム物理名: EMEG6K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_157 IS 'こだわり157 既存システム物理名: EMEG7K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_158 IS 'こだわり158 既存システム物理名: EMEG8K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_159 IS 'こだわり159 既存システム物理名: EMEG9K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_160 IS 'こだわり160 既存システム物理名: EMEH0K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_161 IS 'こだわり161 既存システム物理名: EMEH1K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_162 IS 'こだわり162 既存システム物理名: EMEH2K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_163 IS 'こだわり163 既存システム物理名: EMEH3K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_164 IS 'こだわり164 既存システム物理名: EMEH4K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_165 IS 'こだわり165 既存システム物理名: EMEH5K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_166 IS 'こだわり166 既存システム物理名: EMEH6K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_167 IS 'こだわり167 既存システム物理名: EMEH7K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_168 IS 'こだわり168 既存システム物理名: EMEH8K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_169 IS 'こだわり169 既存システム物理名: EMEH9K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_170 IS 'こだわり170 既存システム物理名: EMEI0K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_171 IS 'こだわり171 既存システム物理名: EMEI1K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_172 IS 'こだわり172 既存システム物理名: EMEI2K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_173 IS 'こだわり173 既存システム物理名: EMEI3K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_174 IS 'こだわり174 既存システム物理名: EMEI4K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_175 IS 'こだわり175 既存システム物理名: EMEI5K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_176 IS 'こだわり176 既存システム物理名: EMEI6K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_177 IS 'こだわり177 既存システム物理名: EMEI7K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_178 IS 'こだわり178 既存システム物理名: EMEI8K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_179 IS 'こだわり179 既存システム物理名: EMEI9K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_180 IS 'こだわり180 既存システム物理名: EMEJ0K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_181 IS 'こだわり181 既存システム物理名: EMEJ1K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_182 IS 'こだわり182 既存システム物理名: EMEJ2K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_183 IS 'こだわり183 既存システム物理名: EMEJ3K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_184 IS 'こだわり184 既存システム物理名: EMEJ4K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_185 IS 'こだわり185 既存システム物理名: EMEJ5K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_186 IS 'こだわり186 既存システム物理名: EMEJ6K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_187 IS 'こだわり187 既存システム物理名: EMEJ7K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_188 IS 'こだわり188 既存システム物理名: EMEJ8K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_189 IS 'こだわり189 既存システム物理名: EMEJ9K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_190 IS 'こだわり190 既存システム物理名: EMEK0K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_191 IS 'こだわり191 既存システム物理名: EMEK1K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_192 IS 'こだわり192 既存システム物理名: EMEK2K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_193 IS 'こだわり193 既存システム物理名: EMEK3K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_194 IS 'こだわり194 既存システム物理名: EMEK4K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_195 IS 'こだわり195 既存システム物理名: EMEK5K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_196 IS 'こだわり196 既存システム物理名: EMEK6K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_197 IS 'こだわり197 既存システム物理名: EMEK7K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_198 IS 'こだわり198 既存システム物理名: EMEK8K';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFERENCE_199 IS 'こだわり199 既存システム物理名: EMEK9K';
COMMENT ON COLUMN ROOM_INFO_MASTER.MARKETING_BRANCH_OFFICE_CD IS 'マーケティング営業所ＣＤ 既存システム物理名: EMEI7C';
COMMENT ON COLUMN ROOM_INFO_MASTER.PROPERTY_SITUATION IS '状況 既存システム物理名: -';
COMMENT ON COLUMN ROOM_INFO_MASTER.PREFECTURE_CITY_CD IS '県CD-市区群町村CD 既存システム物理名: -';
