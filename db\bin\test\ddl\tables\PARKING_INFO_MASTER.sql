-- TABLE: PARKING_INFO_MASTER(駐車場情報DB)

CREATE TABLE PARKING_INFO_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_FLAG                          numeric(1,0)                  
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    PARKING_CODE                                 varchar(3)        NOT NULL    
,    LOCAL_DISPLAY_NUMBER                         varchar(4)                    
,    PARKING_FEE_BASE                             numeric(9,0)                  
,    PARKING_FEE_TAX                              numeric(9,0)                  
,    PARKING_CONTRACT_NUMBER                      varchar(8)                    
,    TENANT_CODE                                  varchar(9)                    
,    CURRENT_PARKING_STATUS                       varchar(2)                    
,    FIXED_PARKING_STATUS                         varchar(2)                    
,    ASSESSMENT_DIVISION                          varchar(1)                    
,    AGGREGATION_DIVISION                         varchar(1)                    
,    SPECIAL_CONTRACT_FLAG                        numeric(1,0)                  
,    EXPECTED_MOVE_OUT_DATE                       numeric(8,0)                  
,    MOVE_IN_SCHEDULED_DATE                       numeric(8,0)                  
,    MANAGEMENT_FLAG                              varchar(1)                    
,    BROKER_APPLICATION_POSSIBILITY               varchar(1)                    
,    CONSTRAINT PK_PARKING_INFO_MASTER PRIMARY KEY (BUILDING_CODE, PARKING_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PARKING_INFO_MASTER IS '駐車場情報DB 既存システム物理名: ECC80P';
COMMENT ON COLUMN PARKING_INFO_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: EC801D';
COMMENT ON COLUMN PARKING_INFO_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: EC802H';
COMMENT ON COLUMN PARKING_INFO_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: EC803D';
COMMENT ON COLUMN PARKING_INFO_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: EC804H';
COMMENT ON COLUMN PARKING_INFO_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムＩＤ 既存システム物理名: EC805N';
COMMENT ON COLUMN PARKING_INFO_MASTER.UPDATER IS '更新者 既存システム物理名: EC806C';
COMMENT ON COLUMN PARKING_INFO_MASTER.LOGICAL_DELETE_FLAG IS '論理削除サイン 既存システム物理名: EC807S';
COMMENT ON COLUMN PARKING_INFO_MASTER.BUILDING_CODE IS '建物コード 既存システム物理名: EC8ABC';
COMMENT ON COLUMN PARKING_INFO_MASTER.PARKING_CODE IS '駐車場コード 既存システム物理名: EC8BSC';
COMMENT ON COLUMN PARKING_INFO_MASTER.LOCAL_DISPLAY_NUMBER IS '現地表示番号 既存システム物理名: EC810N';
COMMENT ON COLUMN PARKING_INFO_MASTER.PARKING_FEE_BASE IS '駐車料本体 既存システム物理名: EC811A';
COMMENT ON COLUMN PARKING_INFO_MASTER.PARKING_FEE_TAX IS '駐車料消費税 既存システム物理名: EC812A';
COMMENT ON COLUMN PARKING_INFO_MASTER.PARKING_CONTRACT_NUMBER IS '駐車場／契約番号 既存システム物理名: EC8PKN';
COMMENT ON COLUMN PARKING_INFO_MASTER.TENANT_CODE IS 'テナントコード 既存システム物理名: EC8ANC';
COMMENT ON COLUMN PARKING_INFO_MASTER.CURRENT_PARKING_STATUS IS '状態（現在） 既存システム物理名: EC815B';
COMMENT ON COLUMN PARKING_INFO_MASTER.FIXED_PARKING_STATUS IS '状態（修正）　 既存システム物理名: EC816B';
COMMENT ON COLUMN PARKING_INFO_MASTER.ASSESSMENT_DIVISION IS '査定区分 既存システム物理名: EC817B';
COMMENT ON COLUMN PARKING_INFO_MASTER.AGGREGATION_DIVISION IS '合算区分 既存システム物理名: EC818B ブランク：無し、1：入居前、2：入居後';
COMMENT ON COLUMN PARKING_INFO_MASTER.SPECIAL_CONTRACT_FLAG IS '特約有無サイン 既存システム物理名: EC819S 0：無し、1：有り';
COMMENT ON COLUMN PARKING_INFO_MASTER.EXPECTED_MOVE_OUT_DATE IS '退去予定日 既存システム物理名: ECB20D';
COMMENT ON COLUMN PARKING_INFO_MASTER.MOVE_IN_SCHEDULED_DATE IS '入居予定日 既存システム物理名: ECB21D';
COMMENT ON COLUMN PARKING_INFO_MASTER.MANAGEMENT_FLAG IS '管理サイン 既存システム物理名: ECB22B';
COMMENT ON COLUMN PARKING_INFO_MASTER.BROKER_APPLICATION_POSSIBILITY IS '斡旋可否 既存システム物理名: ECB23B';
