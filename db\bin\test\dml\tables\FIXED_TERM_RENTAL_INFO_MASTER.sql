truncate table FIXED_TERM_RENTAL_INFO_MASTER;
insert into FIXED_TERM_RENTAL_INFO_MASTER (CREATION_DATE, CREATION_TIME, CREATOR, CREATION_PROGRAM, UPDATE_DATE, UPDATE_TIME, UPDATER, UPDATE_PROGRAM, LO<PERSON>CAL_DELETE_SIGN, BUILDING_CODE, ROOM_CODE, END_TERM, PERIOD, ROOM_FIXED_TERM_FLAG, EXPLANATION_FIXED_TERM_FLAG) values
 (20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '011020701', '02050', 20221214, '0000', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '011020701', '50100', 20221216, '0000', 0, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '022663701', '02010', 20231201, '0008', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '022663701', '02020', 20231215, '0008', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '022663701', '01010', 20241216, '0108', 0, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '000024301', '02030', 20221115, '0000', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '000179801', '03020', 20221116, '0000', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '000439501', '02010', 20221117, '0000', 0, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '000514601', '02010', 20241116, '0107', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '000574701', '01010', 20221231, '0000', 0, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '000013401', '01010', 20221117, '0000', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '000020901', '01010', 20231117, '0007', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '000024301', '09020', 20230130, '0000', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010156201', '03020', 20230131, '0000', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010156201', '03030', 20240326, '0011', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010156201', '03040', 20240327, '0011', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010184301', '01010', 20250326, '0111', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010184301', '01020', 20250426, '0200', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010184301', '01030', 20240326, '0011', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010184301', '02010', 20240327, '0011', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010184301', '02020', 20250326, '0111', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010184301', '02030', 20250426, '0200', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010184301', '01040', 20571231, '3408', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010184301', '02040', 0, '0000', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010185101', '01010', 20290331, '0511', 1, 1)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010185101', '01020', 20241231, '0108', 1, 1)
,(20230210, 202902, '063822', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '011020701', '04010', 20221215, '0000', 1, 0)
,(20230210, 203000, '108129', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '004065501', '01020', 20230301, '0000', 1, 2)
,(20230215, 122919, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '000197002', '01020', 20230531, '0001', 1, 0)
,(20230216, 175826, '017300', 'ECC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '010888101', '02020', 0, '0000', 0, 2)
,(20230221, 104956, '071350', 'ECC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '019834203', '01010', 0, '0000', 0, 2)
,(20230221, 105704, '071350', 'ECC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '023452402', '01020', 0, '0000', 0, 2)
,(20230222, 101806, '063822', 'ECC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '014764501', '03020', 0, '0000', 0, 2)
,(20230222, 154500, '104092', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012530401', '02030', 0, '0000', 0, 2)
,(20230222, 203609, '063822', 'ECC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '007617201', '02020', 0, '0000', 0, 0)
,(20230224, 114826, '017300', 'ECC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '021659301', '02040', 0, '0000', 0, 2)
,(20230226, 193108, '063822', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '023878502', '01060', 20540331, '3011', 1, 0)
,(20230227, 160000, '104092', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024295901', '01030', 20210227, '0000', 0, 2)
,(20230227, 163000, '104092', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '021273201', '02040', 20230227, '0000', 0, 2)
,(20230302, 140211, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '099197301', '07070', 20230322, '0000', 1, 0)
,(20230302, 160919, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024876201', '08080', 20540930, '3105', 1, 0)
,(20230302, 160919, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024876201', '07070', 20540930, '3105', 1, 0)
,(20230201, 113000, 'DFU', 'DFU', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '013037001', '09030', 20221214, '0000', 0, 1)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '017692001', '09030', 20221214, '0000', 0, 1)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '013512001', '09030', 20221214, '0000', 2, 1)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '006973004', '09030', 20221214, '0000', 1, 0)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '017799701', '09030', 20231214, '0008', 1, 0)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '013261902', '09030', 20271214, '0408', 1, 0)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '020096201', '07010', 20971214, '7408', 0, 1)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '023995701', '02030', 20971214, '7408', 0, 1)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '013517301', '02020', 20971214, '7408', 0, 1)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '025018201', '02030', 20971214, '7408', 0, 1)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '013668301', '02020', 20971214, '7408', 0, 1)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '014131601', '08050', 20971214, '7408', 1, 0)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '011212201', '01030', 20971214, '7408', 1, 0)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '023995801', '02030', 20971214, '7408', 1, 0)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '013596601', '02020', 20971214, '7408', 1, 0)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '023954601', '02060', 20671214, '4408', 1, 0)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012979503', '01020', 20671214, '4408', 1, 0)
,(20230302, 160919, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012650701', '09090', 20540930, '3105', 1, 0)
,(20230303, 141857, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012636401', '09090', 20230315, '0000', 1, 0)
,(20230303, 142050, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012636401', '09080', 20230318, '0000', 1, 0)
,(20230303, 142222, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012636401', '09070', 20230317, '0000', 1, 0)
,(20230303, 142400, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012636401', '09060', 20230316, '0000', 1, 0)
,(20230303, 142523, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012636401', '09050', 20230305, '0000', 1, 0)
,(20230303, 143014, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012618502', '09090', 20230329, '0000', 1, 0)
,(20230303, 143942, '063822', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '005003301', '01050', 20230331, '0000', 1, 0)
,(20230303, 144600, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012618502', '09080', 20230328, '0000', 1, 0)
,(20230303, 144852, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012618502', '09070', 20230327, '0000', 1, 0)
,(20230303, 145011, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012618502', '09060', 20230326, '0000', 1, 0)
,(20230303, 145104, '063822', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '005003301', '01060', 20250331, '0111', 1, 0)
,(20230303, 145200, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012618502', '09050', 20230325, '0000', 1, 0)
,(20230303, 145457, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024748102', '09010', 20230321, '0000', 1, 0)
,(20230303, 145920, '063822', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '000020901', '01030', 20300331, '0611', 1, 0)
,(20230303, 150155, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024748102', '09020', 20230321, '0000', 1, 0)
,(20230303, 150335, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024748102', '09030', 20230323, '0000', 1, 0)
,(20230303, 150451, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024748102', '09040', 20230324, '0000', 1, 0)
,(20230303, 150645, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024748102', '09050', 20230325, '0000', 1, 0)
,(20230303, 152222, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012747601', '09010', 20370731, '1403', 1, 0)
,(20230303, 152341, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012747601', '09020', 20370731, '1403', 1, 0)
,(20230303, 152537, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012747601', '09030', 20370731, '1403', 1, 0)
,(20230303, 152754, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012747601', '09040', 20370731, '1403', 1, 0)
,(20230303, 154620, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012747601', '09050', 20370731, '1403', 1, 0)
,(20230303, 155015, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024932701', '09010', 20540930, '3105', 1, 0)
,(20230303, 155140, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024932701', '09020', 20540930, '3105', 1, 0)
,(20230303, 155314, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024932701', '09030', 20540930, '3105', 1, 0)
,(20230303, 155452, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024932701', '09040', 20540930, '3105', 1, 0)
,(20230303, 155608, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024932701', '09050', 20540930, '3105', 1, 0)
,(20230303, 160401, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024791401', '09010', 20230321, '0000', 1, 0)
,(20230303, 160534, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024791401', '09020', 20230322, '0000', 1, 0)
,(20230303, 160635, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024791401', '09030', 20230323, '0000', 1, 0)
,(20230303, 160746, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024791401', '09040', 20230324, '0000', 1, 0)
,(20230303, 160847, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '024791401', '09050', 20230325, '0000', 1, 0)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '022678201', '13040', 20261224, '0308', 2, 1)
,(20230303, 163151, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012694801', '09010', 20230311, '0000', 0, 0)
,(20230303, 163638, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012694801', '09020', 20230329, '0000', 0, 0)
,(20230303, 163847, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012694801', '09030', 20230313, '0000', 0, 0)
,(20230303, 164140, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012694801', '09040', 20230321, '0000', 0, 0)
,(20230201, 113000, 'FURUNO', 'FURUNO', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '020872901', '09040', 20261224, '0308', 0, 1)
,(20230303, 164655, '071350', 'EBC010R', 20230323, 110034, 'ﾊﾞｯﾁ', 'ECN100R', 0, '012694801', '09050', 20230320, '0000', 0, 0)
;
