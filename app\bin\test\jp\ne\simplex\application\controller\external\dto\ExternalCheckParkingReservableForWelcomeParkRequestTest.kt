package jp.ne.simplex.application.controller.external.dto

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import jp.ne.simplex.application.controller.external.parking.dto.ExternalCheckParkingReservableForWelcomeParkRequest
import jp.ne.simplex.application.model.ParkingReservation
import jp.ne.simplex.application.model.RegisterParkingReservation
import jp.ne.simplex.mock.MockLocalDateTime
import java.time.LocalDateTime

class ExternalCheckParkingReservableForWelcomeParkRequestTest : FunSpec({
    context("リクエスト受信時刻により前日/当日の予約状況の確認が切り替えられること") {
        val base = ExternalCheckParkingReservableForWelcomeParkRequest(
            buildingCode = "000000001",
            parkingCode = "001",
        )

        test("リクエスト受信時刻が7:30より前の場合、前日の予約状況の確認リクエストに変換できること") {

            MockLocalDateTime.setNow(LocalDateTime.of(2025, 3, 24, 7, 29, 59))
            val actual = base.toServiceInterface()

            actual.shouldBeInstanceOf<RegisterParkingReservation>()
            actual.parkingLotId.buildingCode.value.shouldBe(base.buildingCode)
            actual.parkingLotId.parkingLotCode.value.shouldBe(base.parkingCode)
            actual.parkingReservationStatus.shouldBe(ParkingReservation.Status.RESERVATION)
            actual.reservationType.value.shouldBe(ParkingReservation.Type.ONE_DAY.value)
            actual.reserveStartDatetime.shouldBe(LocalDateTime.of(2025, 3, 23, 8, 0, 0))
            actual.reserveEndDatetime.shouldBe(LocalDateTime.of(2025, 3, 24, 7, 59, 59))
            actual.requestSource.value.shouldBe(ParkingReservation.RequestSource.WELCOME_PARK.value)
            actual.linkedBuildingCode.shouldBeNull()
            actual.linkedRoomCode.shouldBeNull()

            MockLocalDateTime.close()
        }

        test("リクエスト受信時刻が7:30以降の場合、当日の予約状況の確認リクエストに変換できること") {

            MockLocalDateTime.setNow(LocalDateTime.of(2025, 3, 24, 7, 30, 0))
            val actual = base.toServiceInterface()

            actual.shouldBeInstanceOf<RegisterParkingReservation>()
            actual.parkingLotId.buildingCode.value.shouldBe(base.buildingCode)
            actual.parkingLotId.parkingLotCode.value.shouldBe(base.parkingCode)
            actual.parkingReservationStatus.shouldBe(ParkingReservation.Status.RESERVATION)
            actual.reservationType.value.shouldBe(ParkingReservation.Type.ONE_DAY.value)
            actual.reserveStartDatetime.shouldBe(LocalDateTime.of(2025, 3, 24, 8, 0, 0))
            actual.reserveEndDatetime.shouldBe(LocalDateTime.of(2025, 3, 25, 7, 59, 59))
            actual.requestSource.value.shouldBe(ParkingReservation.RequestSource.WELCOME_PARK.value)
            actual.linkedBuildingCode.shouldBeNull()
            actual.linkedRoomCode.shouldBeNull()

            MockLocalDateTime.close()
        }
    }
})

