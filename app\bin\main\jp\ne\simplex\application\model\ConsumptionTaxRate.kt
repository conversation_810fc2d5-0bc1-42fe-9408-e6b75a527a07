package jp.ne.simplex.application.model

import java.math.BigDecimal

class ConsumptionTaxRate(
    val nationalTaxConsumptionPercent: BigDecimal,
) {
    companion object {
        fun of(nationalTaxConsumptionPercent: BigDecimal?): ConsumptionTaxRate {
            return ConsumptionTaxRate(
                nationalTaxConsumptionPercent?.divide(
                    BigDecimal(100)
                )!!
            )
        }
    }
}
