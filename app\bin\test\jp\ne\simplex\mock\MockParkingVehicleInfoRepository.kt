package jp.ne.simplex.mock

import jp.ne.simplex.application.model.ParkingVehicleInfo
import jp.ne.simplex.application.repository.db.ParkingVehicleInfoRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo

class MockParkingVehicleInfoRepository(
    val updateFunc: (parameter: ParkingVehicleInfo) -> Unit = { _ -> }
) : ParkingVehicleInfoRepositoryInterface {

    override fun upsert(requestUser: AuthInfo.RequestUser, parameter: ParkingVehicleInfo) {
        return updateFunc(parameter)
    }

}
