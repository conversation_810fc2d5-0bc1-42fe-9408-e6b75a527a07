-- TABLE: SITE_PROPERTY_KENTAKU_FOR_P(DK-PORTAL用物件データファイル)

CREATE TABLE SITE_PROPERTY_KENTAKU_FOR_P(
     PROPERTY_FULL_ID                             numeric(27,0)                 
,    BUILDING_ID                                  numeric(6,0)                  
,    RENEW_DATE                                   varchar(26)                   
,    DISTANCE_FROM_STATION_1                      numeric(4,2)                  
,    WALK_FROM_STATION_1                          numeric(3,0)                  
,    BUS_FROM_STATION_1                           numeric(3,0)                  
,    BUS_STOP_NAME_1                              varchar(150)                  
,    FROM_BUS_STOP_1                              numeric(3,0)                  
,    DISTANCE_FROM_BUSSTOP_1                      numeric(4,2)                  
,    NEAREST_ROUTE_1                              varchar(75)                   
,    NEAREST_STATION_1                            varchar(75)                   
,    KINDAIKA_CODE_TEXT_1                         varchar(10)                   
,    WAY_TO_CODE_1                                numeric(2,0)                  
,    DISTANCE_FROM_STATION_2                      numeric(4,2)                  
,    WALK_FROM_STATION_2                          numeric(3,0)                  
,    BUS_FROM_STATION_2                           numeric(3,0)                  
,    BUS_STOP_NAME_2                              varchar(150)                  
,    FROM_BUS_STOP_2                              numeric(3,0)                  
,    DISTANCE_FROM_BUSSTOP_2                      numeric(4,2)                  
,    NEAREST_ROUTE_2                              varchar(75)                   
,    NEAREST_STATION_2                            varchar(75)                   
,    KINDAIKA_CODE_TEXT_2                         varchar(10)                   
,    WAY_TO_CODE_2                                numeric(2,0)                  
,    DISTANCE_FROM_STATION_3                      numeric(4,2)                  
,    WALK_FROM_STATION_3                          numeric(3,0)                  
,    BUS_FROM_STATION_3                           numeric(3,0)                  
,    BUS_STOP_NAME_3                              varchar(150)                  
,    FROM_BUS_STOP_3                              numeric(3,0)                  
,    DISTANCE_FROM_BUSSTOP_3                      numeric(4,2)                  
,    NEAREST_ROUTE_3                              varchar(75)                   
,    NEAREST_STATION_3                            varchar(75)                   
,    KINDAIKA_CODE_TEXT_3                         varchar(10)                   
,    WAY_TO_CODE_3                                numeric(2,0)                  
,    ZIP_CODE_TEXT                                varchar(10)                   
,    PREFECTURE                                   varchar(30)                   
,    CITY                                         varchar(60)                   
,    TOWN                                         varchar(120)                  
,    TYOUME                                       varchar(120)                  
,    KOKUDO_CODE_TEXT                             varchar(11)                   
,    JIS_CODE_VALUE                               numeric(5,0)                  
,    TOWN_CODE_VALUE                              varchar(3)                    
,    TYOUME_CODE_VALUE                            numeric(3,0)                  
,    RESTADDR1                                    varchar(120)                  
,    LATITUDE                                     numeric(20,0)                 
,    LONGITUDE                                    numeric(20,0)                 
,    BUILDING_NAME                                varchar(300)                  
,    DISP_NAME_CODE                               numeric(2,0)                  
,    BUILDING_FURIGANA                            varchar(300)                  
,    KIND_CODE                                    numeric(2,0)                  
,    KIND_DISP_NAME                               varchar(150)                  
,    SALE_BLOCK_NUM                               numeric(5,0)                  
,    EMPTY_HOUSES_NUM                             numeric(5,0)                  
,    SELLING_COMPANY                              varchar(150)                  
,    COMPLETION_DATE                              numeric(9,0)                  
,    AREA_WAYS1_CODE                              numeric(2,0)                  
,    STRUCTURE_CODE                               numeric(2,0)                  
,    STRUCTURE_DISP_NAME                          varchar(150)                  
,    BUILDING_TYPE_CODE                           numeric(2,0)                  
,    ALL_FLOOR_NUM                                numeric(3,0)                  
,    UNDER_FLOOR_NUM                              numeric(2,0)                  
,    NEW_USED_CODE                                numeric(2,0)                  
,    MANAGER_STYLE_CODE                           numeric(2,0)                  
,    MANAGER_COMMENT                              varchar(300)                  
,    QUIET_CODE                                   numeric(2,0)                  
,    GAS_CODE                                     numeric(2,0)                  
,    WATER_SUPPLY_CODE                            numeric(2,0)                  
,    WASTE_WATER_CODE                             numeric(2,0)                  
,    ELEC_POWER_CODE                              numeric(2,0)                  
,    TWO_BY_FOUR_CODE                             numeric(2,0)                  
,    SELL_TYPE_CODE                               numeric(2,0)                  
,    AVOID_QUAKE_CODE                             numeric(2,0)                  
,    BARRIER_FREE_CODE                            numeric(2,0)                  
,    FULLTIME_MANAGEMENT_CODE                     numeric(2,0)                  
,    LIFT_CODE                                    numeric(2,0)                  
,    LIFT_NUM_CODE                                numeric(2,0)                  
,    WALL_TYPE_CODE                               numeric(2,0)                  
,    DELIVERY_MAILBOX_CODE                        numeric(2,0)                  
,    LAUNDERETTE_CODE                             numeric(2,0)                  
,    ROOM_NUMBER_TEXT                             varchar(150)                  
,    DISP_ROOM_NUMBER_CODE                        numeric(2,0)                  
,    SALES_POINT                                  varchar(765)                  
,    REMARK1                                      varchar(3000)                 
,    REMARK2                                      varchar(765)                  
,    SPECIAL_REMARK                               varchar(2000)                 
,    NOTE                                         varchar(765)                  
,    PRICE                                        numeric(12,0)                 
,    PRICE_TAX_CODE                               numeric(2,0)                  
,    CONSUMPTION_TAX                              numeric(9,2)                  
,    QUERY_PERSON                                 varchar(150)                  
,    FIRM_SIDE_CODE                               varchar(50)                   
,    INTO_CODE                                    numeric(2,0)                  
,    INTO_DATE                                    numeric(9,0)                  
,    LEAVE_DATE                                   numeric(9,0)                  
,    OTHER_COMPANY_CODE                           numeric(2,0)                  
,    MESSAGE_TO_OTHER_COMPANY                     varchar(300)                  
,    REGIST_DATE                                  numeric(9,0)                  
,    REGIST_TIME                                  varchar(5)                    
,    RENT_EXCHANGE_STYLE_CODE                     numeric(2,0)                  
,    HOUSE_PLAN_CODE                              numeric(2,0)                  
,    ROOM_NUM                                     numeric(4,0)                  
,    HOUSE_PLAN_EQUIV                             numeric(6,0)                  
,    WINDOW_DIRECTION_CODE                        numeric(2,0)                  
,    FLOOR_NUM                                    varchar(10)                   
,    NON_MOVEINTO_CODE                            numeric(2,0)                  
,    MANAGED_PROPERTY_CODE                        numeric(2,0)                  
,    PET_CODE                                     numeric(2,0)                  
,    OFFICE_CODE                                  numeric(2,0)                  
,    MUSICAL_CODE                                 numeric(2,0)                  
,    HOUSE_PLAN_DISP_NAME                         varchar(300)                  
,    USE_PART_AREA                                numeric(9,2)                  
,    KEY_MONEY                                    numeric(9,2)                  
,    KEY_MONEY_UNIT_CODE                          numeric(2,0)                  
,    KEY_MONEY_TAX_CODE                           numeric(2,0)                  
,    DEPOSIT                                      numeric(9,2)                  
,    DEPOSIT_UNIT_CODE                            numeric(2,0)                  
,    REPAIR_COST                                  numeric(9,2)                  
,    REPAIR_COST_UNIT_CODE                        numeric(2,0)                  
,    GUARANTY                                     numeric(9,2)                  
,    GUARANTY_UNIT_CODE                           numeric(2,0)                  
,    SYOKYAKU_CLASS_CODE                          numeric(2,0)                  
,    SYOKYAKU                                     numeric(9,2)                  
,    SYOKYAKU_UNIT_CODE                           numeric(2,0)                  
,    PREMIUM                                      numeric(9,2)                  
,    PREMIUM_UNIT_CODE                            numeric(2,0)                  
,    PREMIUM_TAX_CODE                             numeric(2,0)                  
,    MANAGE_COST                                  numeric(9,0)                  
,    MANAGE_COST_TAX_CODE                         numeric(2,0)                  
,    SERVICE_FEE                                  numeric(9,0)                  
,    SERVICE_FEE_FREE_CODE                        numeric(2,0)                  
,    SERVICE_FEE_TAX_CODE                         numeric(2,0)                  
,    ZAPPI                                        numeric(9,0)                  
,    ZAPPI_TAX_CODE                               numeric(2,0)                  
,    OTHER_COST_COMMENT                           varchar(300)                  
,    OTHER_COST_1                                 numeric(9,0)                  
,    OTHER_COST_ITEM_1                            varchar(150)                  
,    OTHER_COST_TAX_CODE_1                        numeric(2,0)                  
,    OTHER_COST_2                                 numeric(9,0)                  
,    OTHER_COST_ITEM_2                            varchar(150)                  
,    OTHER_COST_TAX_CODE_2                        numeric(2,0)                  
,    OTHER_COST_3                                 numeric(9,0)                  
,    OTHER_COST_ITEM_3                            varchar(150)                  
,    OTHER_COST_TAX_CODE_3                        numeric(2,0)                  
,    OTHER_COST_4                                 numeric(9,0)                  
,    OTHER_COST_ITEM_4                            varchar(150)                  
,    OTHER_COST_TAX_CODE_4                        numeric(2,0)                  
,    OTHER_COST_5                                 numeric(9,0)                  
,    OTHER_COST_ITEM_5                            varchar(150)                  
,    OTHER_COST_TAX_CODE_5                        numeric(2,0)                  
,    OUTER_FACILITY_CODE_1                        numeric(2,0)                  
,    OUTER_FACILITY_CODE_2                        numeric(2,0)                  
,    OUTER_AREA_2                                 numeric(9,2)                  
,    RENEWAL_FEE                                  numeric(9,2)                  
,    RENEWAL_FEE_UNIT_CODE                        numeric(2,0)                  
,    RENEWAL_FEE_CLASS_CODE                       numeric(2,0)                  
,    HOUSE_RENT_LIMIT_DATE                        numeric(9,0)                  
,    INSURANCE_CODE                               numeric(2,0)                  
,    SPECIAL_RENTAL_LOWER_COST                    numeric(9,0)                  
,    SPECIAL_RENTAL_UPPER_COST                    numeric(9,0)                  
,    ADDITIONAL_DEPOSIT_UNIT_CODE                 numeric(2,0)                  
,    ADDITIONAL_DEPOSIT_REASON_CODE               numeric(2,0)                  
,    BROKERAGE                                    numeric(9,2)                  
,    BROKERAGE_UNIT_CODE                          numeric(2,0)                  
,    RENEWAL_CHARGE                               numeric(9,2)                  
,    RENEWAL_CHARGE_UNIT_CODE                     numeric(2,0)                  
,    STUDENT_ONLY_CODE                            numeric(2,0)                  
,    SEX_CONDITION_CODE                           numeric(2,0)                  
,    KIDS_CODE                                    numeric(2,0)                  
,    ALONE_CODE                                   numeric(2,0)                  
,    TWO_PEOPLE_CODE                              numeric(2,0)                  
,    ELDER_CODE                                   numeric(2,0)                  
,    CORPORATION_ONLY_CODE                        numeric(2,0)                  
,    RESIDENCE_HOUSE_RENT_CODE                    numeric(2,0)                  
,    ROOM_STYLE_CODE_1                            numeric(2,0)                  
,    ROOM_AREA_1                                  numeric(6,2)                  
,    ROOM_UNIT_CODE_1                             numeric(2,0)                  
,    ROOM_STYLE_CODE_2                            numeric(2,0)                  
,    ROOM_AREA_2                                  numeric(6,2)                  
,    ROOM_UNIT_CODE_2                             numeric(2,0)                  
,    ROOM_STYLE_CODE_3                            numeric(2,0)                  
,    ROOM_AREA_3                                  numeric(6,2)                  
,    ROOM_UNIT_CODE_3                             numeric(2,0)                  
,    ROOM_STYLE_CODE_4                            numeric(2,0)                  
,    ROOM_AREA_4                                  numeric(6,2)                  
,    ROOM_UNIT_CODE_4                             numeric(2,0)                  
,    ROOM_STYLE_CODE_5                            numeric(2,0)                  
,    ROOM_AREA_5                                  numeric(6,2)                  
,    ROOM_UNIT_CODE_5                             numeric(2,0)                  
,    ROOM_STYLE_CODE_6                            numeric(2,0)                  
,    ROOM_AREA_6                                  numeric(6,2)                  
,    ROOM_UNIT_CODE_6                             numeric(2,0)                  
,    ROOM_STYLE_CODE_7                            numeric(2,0)                  
,    ROOM_AREA_7                                  numeric(6,2)                  
,    ROOM_UNIT_CODE_7                             numeric(2,0)                  
,    ROOM_STYLE_CODE_8                            numeric(2,0)                  
,    ROOM_AREA_8                                  numeric(6,2)                  
,    ROOM_UNIT_CODE_8                             numeric(2,0)                  
,    ROOM_STYLE_CODE_9                            numeric(2,0)                  
,    ROOM_AREA_9                                  numeric(6,2)                  
,    ROOM_UNIT_CODE_9                             numeric(2,0)                  
,    ROOM_STYLE_CODE_10                           numeric(2,0)                  
,    ROOM_AREA_10                                 numeric(6,2)                  
,    ROOM_UNIT_CODE_10                            numeric(2,0)                  
,    PARKING_CODE                                 numeric(2,0)                  
,    FROM_NEAR_PARKING                            numeric(6,2)                  
,    PARKING_NUM                                  numeric(4,0)                  
,    PARKING_TYPE_CODE                            numeric(2,0)                  
,    PARKING_SHUTTER_CODE                         numeric(2,0)                  
,    PARKING_LOWER_COST                           numeric(6,0)                  
,    PARKING_TAX_CODE                             numeric(2,0)                  
,    PARKABLE_NUM_CODE                            numeric(2,0)                  
,    PARKING_FREE_CODE                            numeric(2,0)                  
,    BIKE_PARK_CODE                               numeric(2,0)                  
,    BIKE_PARK_COST                               numeric(6,0)                  
,    MOTORBIKE_PARK_CODE                          numeric(2,0)                  
,    MOTORBIKE_COST                               numeric(6,0)                  
,    AIRCON_CODE                                  numeric(2,0)                  
,    COOLER_CODE                                  numeric(2,0)                  
,    HEATING_CODE                                 numeric(2,0)                  
,    LOAD_HEATER_CODE                             numeric(2,0)                  
,    STOVE_CODE                                   numeric(2,0)                  
,    FLOOR_HEATING_CODE                           numeric(2,0)                  
,    CATV_CODE                                    numeric(2,0)                  
,    COMMUNITY_BROADCAST_CODE                     numeric(2,0)                  
,    BS_CODE                                      numeric(2,0)                  
,    CS_CODE                                      numeric(2,0)                  
,    INTERNET_CODE                                numeric(2,0)                  
,    CLOSET_CODE                                  numeric(2,0)                  
,    WALKIN_WARDROBE_CODE                         numeric(2,0)                  
,    CLOSET_UNDER_FLOOR_CODE                      numeric(2,0)                  
,    TRUNK_ROOM_CODE                              numeric(2,0)                  
,    OSHIIRE_CODE                                 numeric(2,0)                  
,    GARRET_CLOSET_CODE                           numeric(2,0)                  
,    SHOE_CUPBOARD_CODE                           numeric(2,0)                  
,    STOREROOM_CODE                               numeric(2,0)                  
,    BATH_TOILET_CODE                             numeric(2,0)                  
,    BATH_CODE                                    numeric(2,0)                  
,    SHOWER_CODE                                  numeric(2,0)                  
,    AUTO_BATH_CODE                               numeric(2,0)                  
,    DRESSING_ROOM_CODE                           numeric(2,0)                  
,    REBOIL_BATH_CODE                             numeric(2,0)                  
,    TOILET_CODE                                  numeric(2,0)                  
,    BATH_DRIER_CODE                              numeric(2,0)                  
,    SHAMPOO_DRESSER_CODE                         numeric(2,0)                  
,    WASHLET_CODE                                 numeric(2,0)                  
,    BATH_OVER_1TSUBO_CODE                        numeric(2,0)                  
,    WARMLET_CODE                                 numeric(2,0)                  
,    COOKING_STOVE_CODE                           numeric(2,0)                  
,    KITCHEN_CODE                                 numeric(2,0)                  
,    MICROWAVE_OVEN_CODE                          numeric(2,0)                  
,    IH_COOKING_HEATER_CODE                       numeric(2,0)                  
,    COLD_STORAGE_CODE                            numeric(2,0)                  
,    GRILL_CODE                                   numeric(2,0)                  
,    DISPOSER_CODE                                numeric(2,0)                  
,    DISH_WASHER_CODE                             numeric(2,0)                  
,    WATER_CLEANER_CODE                           numeric(2,0)                  
,    WOODEN_FLOOR_CODE                            numeric(2,0)                  
,    LOFT_CODE                                    numeric(2,0)                  
,    CUSHION_FLOOR_CODE                           numeric(2,0)                  
,    HIGHEST_FLOOR_CODE                           numeric(2,0)                  
,    MAISONETTE_CODE                              numeric(2,0)                  
,    OVER_SECOND_FLOOR_CODE                       numeric(2,0)                  
,    CAVE_CODE                                    numeric(2,0)                  
,    SOUNDPROOF_CODE                              numeric(2,0)                  
,    CORNER_HOUSE_CODE                            numeric(2,0)                  
,    SUNROOM_CODE                                 numeric(2,0)                  
,    BASEMENT_CODE                                numeric(2,0)                  
,    SOUTH_ROOM_CODE                              numeric(2,0)                  
,    PATIO_CODE                                   numeric(2,0)                  
,    CRIME_PREV_SHUTTER_CODE                      numeric(2,0)                  
,    CRIME_PREV_CAMERA_CODE                       numeric(2,0)                  
,    AUTOLOCK_CODE                                numeric(2,0)                  
,    DOUBLE_LOCK_CODE                             numeric(2,0)                  
,    WASHING_MACHINE_CODE                         numeric(2,0)                  
,    DRIER_CODE                                   numeric(2,0)                  
,    WASHING_MACHINE_PLACE_CODE                   numeric(2,0)                  
,    CARD_KEY_CODE                                numeric(2,0)                  
,    BOW_WINDOW_CODE                              numeric(2,0)                  
,    LIGHT_CODE                                   numeric(2,0)                  
,    ALL_ELECTRIC_CODE                            numeric(2,0)                  
,    HOT_WATER_SUPPLY_CODE                        numeric(2,0)                  
,    INTERPHONE_CODE                              numeric(2,0)                  
,    FULLTIME_FUN_CODE                            numeric(2,0)                  
,    ECOCUTE_CODE                                 numeric(2,0)                  
,    DOUBLE_SIDE_BALCONY_CODE                     numeric(2,0)                  
,    BALCONY_SIDE_NUM_CODE                        numeric(2,0)                  
,    BATH_TV_CODE                                 numeric(2,0)                  
,    PORCH_CODE                                   numeric(2,0)                  
,    UP_START_DATE                                numeric(9,0)                  
,    UP_END_DATE                                  numeric(9,0)                  
,    DRESSING_TABLE_CODE                          numeric(2,0)                  
,    PRIVATE_DUST_BOX_CODE                        numeric(2,0)                  
,    PIANO_CODE                                   numeric(2,0)                  
,    LARGE_SHOES_BOX_CODE                         numeric(2,0)                  
,    CLOSET_UNDER_TATAMI_CODE                     numeric(2,0)                  
,    INDOORS_BICYCLE_PARKING_CODE                 numeric(2,0)                  
,    SECURITY_KEY_CODE                            numeric(2,0)                  
,    SHUTTER_CODE                                 numeric(2,0)                  
,    FOR_SOUTH_CODE                               numeric(2,0)                  
,    CLOSET_UNDERSTAIR_CODE                       numeric(2,0)                  
,    NEARBY_CONVENIENCE_STORE_CODE                numeric(2,0)                  
,    NEARBY_BANK_CODE                             numeric(2,0)                  
,    NEARBY_RENTAL_VIDEO_CODE                     numeric(2,0)                  
,    LARGE_SCALE_RENEWAL_CODE                     numeric(2,0)                  
,    RECOVERY_COST_CODE                           numeric(2,0)                  
,    GUARANTOR_CODE                               numeric(2,0)                  
,    GUARANTOR_PROXY_CODE                         numeric(2,0)                  
,    GUARANTOR_PROXY_COM_CODE                     numeric(2,0)                  
,    GUARANTOR_PROXY_COMMENT                      varchar(900)                  
,    DISP_MAP_CODE                                numeric(2,0)                  
,    LATITUDE_WORLD                               numeric(20,0)                 
,    LONGITUDE_WORLD                              numeric(20,0)                 
,    GARDEN_CODE                                  numeric(2,0)                  
,    BALCONY_CODE                                 numeric(2,0)                  
,    PANORAMA_ID                                  numeric(9,0)                  
,    LARGE_SCALE_RENEWAL_DATE                     numeric(9,0)                  
,    SHATAKU_KANOU_CODE                           numeric(2,0)                  
,    NO_DEPOSIT_PLAN_CODE                         numeric(2,0)                  
,    KENTAKU_KIND_CODE                            varchar(3)                    
,    PRICE_SALE_FLAG                              numeric(2,0)                  
,    REFOME_FLAG                                  numeric(2,0)                  
,    PRODUCT_CODE                                 numeric(3,0)                  
,    OWNER_SHIP_BRANCH_CODE                       varchar(3)                    
,    KENTAKU_BUILDING_CODE                        varchar(11)                   
,    KENTAKU_ROOM_CODE                            varchar(5)                    
,    KEY_EXCHANGE_FREE_CODE                       numeric(2,0)                  
,    AD_PRICE                                     numeric(7,0)                  
,    FF_PRICE                                     varchar(300)                  
,    LEAVE_DATE_TP                                numeric(8,0)                  
,    LEAVE_FINISH_DATE                            numeric(8,0)                  
,    LOW_PARKING_PRICE                            numeric(9,0)                  
,    HIGH_PARKING_PRICE                           numeric(9,0)                  
,    STRUCTURE_DISP_NAME_TP                       varchar(96)                   
,    DISPLACE_CODE                                numeric(1,0)                  
,    FINANCE_CORPORATION_CODE                     numeric(1,0)                  
,    WATER_COMPANY_NAME                           varchar(126)                  
,    WATER_COMPANY_TEL                            varchar(45)                   
,    ELECTRIC_COMPANY_NAME                        varchar(126)                  
,    ELECTRIC_COMPANY_TEL                         varchar(45)                   
,    GAS_COMPANY_NAME                             varchar(126)                  
,    GAS_COMPANY_TEL                              varchar(45)                   
,    COLLECT_DATE                                 varchar(26)                   
,    KOUENTIN_CODE                                numeric(1,0)                  
,    INTO_DATE_TXT                                varchar(60)                   
,    ROOM_SITUATION_CODE                          varchar(2)                    
,    RECORD_SITUATION_CODE                        varchar(2)                    
,    ELECTRIC_DISCOUNT_FLAG                       numeric(2,0)                  
,    FLETS_HIKARI_CODE                            numeric(1,0)                  
,    AKIYA_TERM                                   numeric(4,0)                  
,    CLEANING_FEE_CODE                            numeric(1,0)                  
,    CLEANING_FEE                                 numeric(7,0)                  
,    POWER_CODE                                   numeric(1,0)                  
,    FIRE_ZONE_CODE                               numeric(1,0)                  
,    DISCOUNT_RATE                                numeric(3,0)                  
,    DISCOUNT_TERM                                numeric(9,0)                  
,    PET_FLAG                                     numeric(2,0)                  
,    INTERNET_FREE_CODE                           numeric(2,0)                  
,    ALL_ROOM_CLOSET                              numeric(2,0)                  
,    WALK_THROUGH_CLOSET                          numeric(2,0)                  
,    FREE_WASH_ROOM                               numeric(2,0)                  
,    AUTO_BATH                                    numeric(2,0)                  
,    INDOOR_CLOTHES_DRYING                        numeric(2,0)                  
,    MOTION_SENSOR_LIGHTING                       numeric(2,0)                  
,    OPEN_KITCHEN                                 numeric(2,0)                  
,    ISLAND_KITCHEN                               numeric(2,0)                  
,    GAS_COOKER_ATTACHED                          numeric(2,0)                  
,    THREE_OVER_GAS                               numeric(2,0)                  
,    DOUBLE_GLAZING                               numeric(2,0)                  
,    SECURITY_GLAZING                             numeric(2,0)                  
,    VIBRATION_CONTROL_FLOOR                      numeric(2,0)                  
,    SNOW_VANISHING_FACILITY                      numeric(2,0)                  
,    KEROSENE_HEATER                              numeric(2,0)                  
,    BATH_WINDOW                                  numeric(2,0)                  
,    JAPANESE_STYLE_ROOM                          numeric(2,0)                  
,    EARTHQUAKE_RESIST_CONST                      numeric(2,0)                  
,    ALLINONE_SERVICE_WATER                       numeric(2,0)                  
,    ALLINONE_SERVICE_ELECTRICITY                 numeric(2,0)                  
,    ALLINONE_SERVICE_GAS                         numeric(2,0)                  
,    PRICE_AND_COST                               numeric(12,0)                 
,    VAL_CODE_1                                   varchar(5)                    
,    VAL_CODE_2                                   varchar(5)                    
,    VAL_CODE_3                                   varchar(5)                    
,    PANORAMA_TYPE                                numeric(2,0)                  
,    SERVICE_FEE_DETAILS                          varchar(512)                  
,    SHINSA_BRANCH_CODE                           varchar(3)                    
,    CONTRACT_CONFIRM_CODE                        numeric(2,0)                  
,    PREFECTURE_EN                                varchar(24)                   
,    SHIKUGUNCHOUSON_EN                           varchar(72)                   
,    OOAZA_TSUUSYOU_EN                            varchar(108)                  
,    AZA_CHOUME_EN                                varchar(72)                   
,    RESTADDR_ALPHABET                            varchar(200)                  
,    UP_STATE                                     numeric(2,0)                  
,    AD_PRICE_UNIT_CODE                           numeric(2,0)                  
,    DELETE_DATE                                  numeric(8,0)                  
,    REALTIME_UP_TIME                             varchar(26)                   
,    REALTIME_UP_TYPE                             numeric(2,0)                  
,    PRODUCT_TYPE_CD                              numeric(2,0)                  
,    MONEY_UPDATE_TIME                            varchar(26)                   
,    KODAWARI100_199                              varchar(100)                  
,    FLOOR_MAX_ROOM                               numeric(3,0)                  
,    RENEWAL_FEE_FLG                              numeric(2,0)                  
,    FULLTIME_SUPPORT_FLG                         numeric(2,0)                  
,    MEMBERSHIP_FEE_EXEMPTION_KBN                 numeric(1,0)                  
,    MEMBERSHIP_FEE_EXEMPTION_DAYS                numeric(2,0)                  
,    EBOARD_COMMENT                               varchar(1000)                 
,    MANAGEMENT_PARKING_KBN                       numeric(1,0)                  
,    NET_SERVICE_JCOM                             numeric(1,0)                  
,    NET_SERVICE_STARCAT                          numeric(1,0)                  
,    ZEH_ORIENTED                                 numeric(1,0)                  
,    ZEH_DK_SOLEIL                                numeric(1,0)                  
,    ZEH_DK_ALPHA                                 numeric(1,0)                  
,    KEY_SET_COST_FLAG                            numeric(1,0)                  
,    ELECTRIC_INTRODUCTION                        numeric(1,0)                  
,    ELECTRIC_TYPE                                numeric(1,0)                  
,    EMERGENCY_E_COMPANY_NAME                     varchar(126)                  
,    EMERGENCY_E_COMPANY_TEL                      varchar(45)                   
,    GAS_INTRODUCTION                             numeric(1,0)                  
,    EMERGENCY_GAS_COMPANY_NAME                   varchar(126)                  
,    EMERGENCY_GAS_COMPANY_TEL                    varchar(45)                   
,    WATER_INTRODUCTION                           numeric(1,0)                  
,    WATER_METER_TYPE                             numeric(1,0)                  
,    INTERNET_TYPE                                numeric(1,0)                  
,    INTERNET_NAME                                varchar(126)                  
,    INTERNET_TEL                                 varchar(45)                   
,    INTERNET_INTRODUCTION                        numeric(1,0)                  
,    WATER_SERVER                                 numeric(1,0)                  
,    LIFELINE_GUIDANCE_TYPE                       numeric(1,0)                  
,    ROOM_SAVE_ENERGY_CERT_DATE                   numeric(8,0)                  
,    ROOM_THIRD_PARTY_EVAL_FLG                    numeric(1,0)                  
,    ROOM_SAVE_ENERGY_LEVEL                       varchar(300)                  
,    ROOM_ENERGY_COST                             varchar(3)                    
,    ROOM_ENERGY_COST_SUN                         varchar(3)                    
,    ROOM_RENEW_ENERGY_FLG                        numeric(1,0)                  
,    ROOM_INSULATION_LEVEL                        varchar(3)                    
,    ROOM_EASY_UTILITY_COSTS                      numeric(8,0)                  
,    ROOM_ZEH_LEVEL_FLG                           numeric(1,0)                  
,    ROOM_NET_ZERO_ENERGY_FLG                     numeric(1,0)                  
,    BUILDING_SAVE_ENERGY_CERT_DATE               numeric(8,0)                  
,    BUILDING_THIRD_PARTY_EVAL_FLG                numeric(1,0)                  
,    BUILDING_SAVE_ENERGY_LEVEL                   varchar(300)                  
,    BUILDING_ENERGY_COST                         varchar(3)                    
,    BUILDING_ENERGY_COST_SUN                     varchar(3)                    
,    BUILDING_RENEW_ENERGY_FLG                    numeric(1,0)                  
,    BUILDING_INSULATION_LEVEL                    varchar(3)                    
,    BUILDING_EASY_UTILITY_COSTS                  numeric(8,0)                  
,    BUILDING_ZEH_LEVEL_FLG                       numeric(1,0)                  
,    BUILDING_NET_ZERO_ENERGY_FLG                 numeric(1,0)                  
,    SHINSA_BUSINESS_OFFICE_CODE                  varchar(3)                    
,    CHALLENGE_START                              numeric(8,0)                  
,    CHALLENGE_END                                numeric(8,0)                  
,    CHALLENGE_DISCOUNT_PRICE                     numeric(8,0)                  
) TABLESPACE :TS_TBL;

COMMENT ON TABLE SITE_PROPERTY_KENTAKU_FOR_P IS 'DK-PORTAL用物件データファイル 既存システム物理名: SITE_PROPERTY_KENTAKU_FOR_P';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PROPERTY_FULL_ID IS '物件フルID 既存システム物理名: PROPERTY_FULL_ID';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_ID IS '建物コード 既存システム物理名: BUILDING_ID';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RENEW_DATE IS '更新日付 既存システム物理名: RENEW_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISTANCE_FROM_STATION_1 IS '駅からの距離１ 既存システム物理名: DISTANCE_FROM_STATION_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WALK_FROM_STATION_1 IS '駅から徒歩時間１ 既存システム物理名: WALK_FROM_STATION_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUS_FROM_STATION_1 IS '駅からバス停までの時間 既存システム物理名: BUS_FROM_STATION_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUS_STOP_NAME_1 IS 'バス停名称１ 既存システム物理名: BUS_STOP_NAME_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FROM_BUS_STOP_1 IS 'バス停から徒歩時間１ 既存システム物理名: FROM_BUS_STOP_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISTANCE_FROM_BUSSTOP_1 IS 'バス停からの距離１ 既存システム物理名: DISTANCE_FROM_BUSSTOP_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NEAREST_ROUTE_1 IS '最寄道路１→最寄路線１？ 既存システム物理名: NEAREST_ROUTE_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NEAREST_STATION_1 IS '最寄駅１ 既存システム物理名: NEAREST_STATION_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KINDAIKA_CODE_TEXT_1 IS '近代化コード１ 既存システム物理名: KINDAIKA_CODE_TEXT_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WAY_TO_CODE_1 IS '移動手段１ 既存システム物理名: WAY_TO_CODE_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISTANCE_FROM_STATION_2 IS '駅からの距離２ 既存システム物理名: DISTANCE_FROM_STATION_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WALK_FROM_STATION_2 IS '駅から徒歩時間２ 既存システム物理名: WALK_FROM_STATION_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUS_FROM_STATION_2 IS '駅からバス停までの距離２ 既存システム物理名: BUS_FROM_STATION_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUS_STOP_NAME_2 IS 'バス停名称２ 既存システム物理名: BUS_STOP_NAME_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FROM_BUS_STOP_2 IS 'バス停から徒歩時間２ 既存システム物理名: FROM_BUS_STOP_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISTANCE_FROM_BUSSTOP_2 IS 'バス停からの距離２ 既存システム物理名: DISTANCE_FROM_BUSSTOP_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NEAREST_ROUTE_2 IS '最寄道路２ 既存システム物理名: NEAREST_ROUTE_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NEAREST_STATION_2 IS '最寄駅２ 既存システム物理名: NEAREST_STATION_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KINDAIKA_CODE_TEXT_2 IS '近代化コード２ 既存システム物理名: KINDAIKA_CODE_TEXT_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WAY_TO_CODE_2 IS '移動手段２ 既存システム物理名: WAY_TO_CODE_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISTANCE_FROM_STATION_3 IS '駅からの距離３ 既存システム物理名: DISTANCE_FROM_STATION_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WALK_FROM_STATION_3 IS '駅から徒歩時間３ 既存システム物理名: WALK_FROM_STATION_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUS_FROM_STATION_3 IS '駅からバス停までの距離３ 既存システム物理名: BUS_FROM_STATION_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUS_STOP_NAME_3 IS 'バス停名称３ 既存システム物理名: BUS_STOP_NAME_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FROM_BUS_STOP_3 IS 'バス停から徒歩時間３ 既存システム物理名: FROM_BUS_STOP_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISTANCE_FROM_BUSSTOP_3 IS 'バス停からの距離３ 既存システム物理名: DISTANCE_FROM_BUSSTOP_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NEAREST_ROUTE_3 IS '最寄道路３ 既存システム物理名: NEAREST_ROUTE_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NEAREST_STATION_3 IS '最寄駅３ 既存システム物理名: NEAREST_STATION_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KINDAIKA_CODE_TEXT_3 IS '近代化コード３ 既存システム物理名: KINDAIKA_CODE_TEXT_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WAY_TO_CODE_3 IS '移動手段３ 既存システム物理名: WAY_TO_CODE_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ZIP_CODE_TEXT IS '郵便番号 既存システム物理名: ZIP_CODE_TEXT';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PREFECTURE IS '都道府県名 既存システム物理名: PREFECTURE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CITY IS '市区郡名 既存システム物理名: CITY';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.TOWN IS '町名 既存システム物理名: TOWN';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.TYOUME IS '丁目 既存システム物理名: TYOUME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KOKUDO_CODE_TEXT IS '国土コード 既存システム物理名: KOKUDO_CODE_TEXT';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.JIS_CODE_VALUE IS 'JISコード 既存システム物理名: JIS_CODE_VALUE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.TOWN_CODE_VALUE IS '町コード 既存システム物理名: TOWN_CODE_VALUE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.TYOUME_CODE_VALUE IS '丁目コード 既存システム物理名: TYOUME_CODE_VALUE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RESTADDR1 IS '番地 既存システム物理名: RESTADDR1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LATITUDE IS '緯度 既存システム物理名: LATITUDE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LONGITUDE IS '経度 既存システム物理名: LONGITUDE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_NAME IS '建物名称 既存システム物理名: BUILDING_NAME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISP_NAME_CODE IS '物件名表示フラグ 既存システム物理名: DISP_NAME_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_FURIGANA IS '建物名称ふりがな 既存システム物理名: BUILDING_FURIGANA';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KIND_CODE IS '物件タイプ 既存システム物理名: KIND_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KIND_DISP_NAME IS '物件タイプのタイプ表示フラグ 既存システム物理名: KIND_DISP_NAME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SALE_BLOCK_NUM IS '賃貸戸数／販売戸数 既存システム物理名: SALE_BLOCK_NUM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.EMPTY_HOUSES_NUM IS '空戸数 既存システム物理名: EMPTY_HOUSES_NUM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SELLING_COMPANY IS '販売会社名 既存システム物理名: SELLING_COMPANY';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.COMPLETION_DATE IS '完了日付 既存システム物理名: COMPLETION_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.AREA_WAYS1_CODE IS '用途地域 既存システム物理名: AREA_WAYS1_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.STRUCTURE_CODE IS '構造コード 既存システム物理名: STRUCTURE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.STRUCTURE_DISP_NAME IS '表示用構造名称 既存システム物理名: STRUCTURE_DISP_NAME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_TYPE_CODE IS '建物タイプコード 既存システム物理名: BUILDING_TYPE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ALL_FLOOR_NUM IS '総階数 既存システム物理名: ALL_FLOOR_NUM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.UNDER_FLOOR_NUM IS '地下階数 既存システム物理名: UNDER_FLOOR_NUM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NEW_USED_CODE IS '新築、中古別 既存システム物理名: NEW_USED_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MANAGER_STYLE_CODE IS '管理スタイルコード 既存システム物理名: MANAGER_STYLE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MANAGER_COMMENT IS '管理コメント 既存システム物理名: MANAGER_COMMENT';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.QUIET_CODE IS '閑静な住宅街 既存システム物理名: QUIET_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GAS_CODE IS 'ガスコード 既存システム物理名: GAS_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WATER_SUPPLY_CODE IS '水道供給コード 既存システム物理名: WATER_SUPPLY_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WASTE_WATER_CODE IS '雑排水 既存システム物理名: WASTE_WATER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ELEC_POWER_CODE IS '電気の有無 既存システム物理名: ELEC_POWER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.TWO_BY_FOUR_CODE IS '２×４工法 既存システム物理名: TWO_BY_FOUR_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SELL_TYPE_CODE IS '分譲タイプ 既存システム物理名: SELL_TYPE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.AVOID_QUAKE_CODE IS '免震構造 既存システム物理名: AVOID_QUAKE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BARRIER_FREE_CODE IS 'バリアフリーコード 既存システム物理名: BARRIER_FREE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FULLTIME_MANAGEMENT_CODE IS '２４時間管理コード 既存システム物理名: FULLTIME_MANAGEMENT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LIFT_CODE IS 'エレベータ 既存システム物理名: LIFT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LIFT_NUM_CODE IS 'エレベータ基数 既存システム物理名: LIFT_NUM_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WALL_TYPE_CODE IS '壁タイプコード 既存システム物理名: WALL_TYPE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DELIVERY_MAILBOX_CODE IS '宅配ボックスコード 既存システム物理名: DELIVERY_MAILBOX_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LAUNDERETTE_CODE IS 'コインランドリ 既存システム物理名: LAUNDERETTE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_NUMBER_TEXT IS '部屋番号 既存システム物理名: ROOM_NUMBER_TEXT';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISP_ROOM_NUMBER_CODE IS '表示用部屋番号コード 既存システム物理名: DISP_ROOM_NUMBER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SALES_POINT IS 'セールスポイント 既存システム物理名: SALES_POINT';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.REMARK1 IS '備考１ 既存システム物理名: REMARK1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.REMARK2 IS '備考２ 既存システム物理名: REMARK2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SPECIAL_REMARK IS '特記事項 既存システム物理名: SPECIAL_REMARK';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NOTE IS '注意事項 既存システム物理名: NOTE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PRICE IS '家賃 既存システム物理名: PRICE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PRICE_TAX_CODE IS '消費税コード 既存システム物理名: PRICE_TAX_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CONSUMPTION_TAX IS '消費税 既存システム物理名: CONSUMPTION_TAX';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.QUERY_PERSON IS '問い合わせ担当氏名 既存システム物理名: QUERY_PERSON';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FIRM_SIDE_CODE IS '照会NO 既存システム物理名: FIRM_SIDE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INTO_CODE IS '入居／引渡し 既存システム物理名: INTO_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INTO_DATE IS '入力日付 既存システム物理名: INTO_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LEAVE_DATE IS '退去予定日 既存システム物理名: LEAVE_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COMPANY_CODE IS '客付け 既存システム物理名: OTHER_COMPANY_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MESSAGE_TO_OTHER_COMPANY IS '他会社コメント 既存システム物理名: MESSAGE_TO_OTHER_COMPANY';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.REGIST_DATE IS '登録日付 既存システム物理名: REGIST_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.REGIST_TIME IS '登録時間 既存システム物理名: REGIST_TIME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RENT_EXCHANGE_STYLE_CODE IS '賃貸取引形態 既存システム物理名: RENT_EXCHANGE_STYLE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.HOUSE_PLAN_CODE IS '間取タイプ 既存システム物理名: HOUSE_PLAN_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_NUM IS '部屋数 既存システム物理名: ROOM_NUM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.HOUSE_PLAN_EQUIV IS '状況表示名称 既存システム物理名: HOUSE_PLAN_EQUIV';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WINDOW_DIRECTION_CODE IS '窓向きコード 既存システム物理名: WINDOW_DIRECTION_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FLOOR_NUM IS 'フロア階数 既存システム物理名: FLOOR_NUM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NON_MOVEINTO_CODE IS '未入居 既存システム物理名: NON_MOVEINTO_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MANAGED_PROPERTY_CODE IS '管理物件コード 既存システム物理名: MANAGED_PROPERTY_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PET_CODE IS 'ペットコード 既存システム物理名: PET_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OFFICE_CODE IS '事務所コード 既存システム物理名: OFFICE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MUSICAL_CODE IS '楽器等の使用 既存システム物理名: MUSICAL_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.HOUSE_PLAN_DISP_NAME IS '間取り表示用名称 既存システム物理名: HOUSE_PLAN_DISP_NAME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.USE_PART_AREA IS '専有／建物／使用部面積 既存システム物理名: USE_PART_AREA';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KEY_MONEY IS '礼金 既存システム物理名: KEY_MONEY';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KEY_MONEY_UNIT_CODE IS '礼金単位 既存システム物理名: KEY_MONEY_UNIT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KEY_MONEY_TAX_CODE IS '礼金消費税区分 既存システム物理名: KEY_MONEY_TAX_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DEPOSIT IS '敷金 既存システム物理名: DEPOSIT';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DEPOSIT_UNIT_CODE IS '敷金単位 既存システム物理名: DEPOSIT_UNIT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.REPAIR_COST IS '敷引 既存システム物理名: REPAIR_COST';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.REPAIR_COST_UNIT_CODE IS '敷引単位 既存システム物理名: REPAIR_COST_UNIT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GUARANTY IS '保証金 既存システム物理名: GUARANTY';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GUARANTY_UNIT_CODE IS '保証金単位 既存システム物理名: GUARANTY_UNIT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SYOKYAKU_CLASS_CODE IS '保証金償却区分 既存システム物理名: SYOKYAKU_CLASS_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SYOKYAKU IS '償却 既存システム物理名: SYOKYAKU';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SYOKYAKU_UNIT_CODE IS '償却単位 既存システム物理名: SYOKYAKU_UNIT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PREMIUM IS '権利金 既存システム物理名: PREMIUM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PREMIUM_UNIT_CODE IS '権利金単位 既存システム物理名: PREMIUM_UNIT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PREMIUM_TAX_CODE IS '権利金消費税区分 既存システム物理名: PREMIUM_TAX_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MANAGE_COST IS '管理費 既存システム物理名: MANAGE_COST';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MANAGE_COST_TAX_CODE IS '管理費消費税区分 既存システム物理名: MANAGE_COST_TAX_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SERVICE_FEE IS '共益費 既存システム物理名: SERVICE_FEE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SERVICE_FEE_FREE_CODE IS '共益費無し区分 既存システム物理名: SERVICE_FEE_FREE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SERVICE_FEE_TAX_CODE IS '共益費消費税区分 既存システム物理名: SERVICE_FEE_TAX_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ZAPPI IS '雑費 既存システム物理名: ZAPPI';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ZAPPI_TAX_CODE IS '雑費費消費税区分 既存システム物理名: ZAPPI_TAX_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_COMMENT IS 'その他費用 既存システム物理名: OTHER_COST_COMMENT';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_1 IS 'その他費用１ 既存システム物理名: OTHER_COST_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_ITEM_1 IS 'その他費用項目１ 既存システム物理名: OTHER_COST_ITEM_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_TAX_CODE_1 IS 'その他費用消費税コード１ 既存システム物理名: OTHER_COST_TAX_CODE_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_2 IS 'その他費用２ 既存システム物理名: OTHER_COST_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_ITEM_2 IS 'その他費用項目２ 既存システム物理名: OTHER_COST_ITEM_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_TAX_CODE_2 IS 'その他費用消費税コード２ 既存システム物理名: OTHER_COST_TAX_CODE_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_3 IS 'その他費用３ 既存システム物理名: OTHER_COST_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_ITEM_3 IS 'その他費用項目３ 既存システム物理名: OTHER_COST_ITEM_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_TAX_CODE_3 IS 'その他費用消費税コード３ 既存システム物理名: OTHER_COST_TAX_CODE_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_4 IS 'その他費用４ 既存システム物理名: OTHER_COST_4';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_ITEM_4 IS 'その他費用項目４ 既存システム物理名: OTHER_COST_ITEM_4';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_TAX_CODE_4 IS 'その他費用消費税コード４ 既存システム物理名: OTHER_COST_TAX_CODE_4';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_5 IS 'その他費用５ 既存システム物理名: OTHER_COST_5';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_ITEM_5 IS 'その他費用項目５ 既存システム物理名: OTHER_COST_ITEM_5';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OTHER_COST_TAX_CODE_5 IS 'その他費用消費税コード５ 既存システム物理名: OTHER_COST_TAX_CODE_5';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OUTER_FACILITY_CODE_1 IS 'その他施設コード１ 既存システム物理名: OUTER_FACILITY_CODE_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OUTER_FACILITY_CODE_2 IS '部屋外設備２ 既存システム物理名: OUTER_FACILITY_CODE_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OUTER_AREA_2 IS 'その他エリア２ 既存システム物理名: OUTER_AREA_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RENEWAL_FEE IS '更新フラグ 既存システム物理名: RENEWAL_FEE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RENEWAL_FEE_UNIT_CODE IS '更新料単位 既存システム物理名: RENEWAL_FEE_UNIT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RENEWAL_FEE_CLASS_CODE IS '更新料区分 既存システム物理名: RENEWAL_FEE_CLASS_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.HOUSE_RENT_LIMIT_DATE IS '契約期日 既存システム物理名: HOUSE_RENT_LIMIT_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INSURANCE_CODE IS '損保コード 既存システム物理名: INSURANCE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SPECIAL_RENTAL_LOWER_COST IS '特優賃入居者負担賃料（ＦＲＯＭ） 既存システム物理名: SPECIAL_RENTAL_LOWER_COST';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SPECIAL_RENTAL_UPPER_COST IS '特優賃入居者負担賃料（ＴＯ） 既存システム物理名: SPECIAL_RENTAL_UPPER_COST';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ADDITIONAL_DEPOSIT_UNIT_CODE IS '積増し敷金単位 既存システム物理名: ADDITIONAL_DEPOSIT_UNIT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ADDITIONAL_DEPOSIT_REASON_CODE IS '敷金積増し理由 既存システム物理名: ADDITIONAL_DEPOSIT_REASON_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BROKERAGE IS '仲介手数料 既存システム物理名: BROKERAGE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BROKERAGE_UNIT_CODE IS '仲介料単位 既存システム物理名: BROKERAGE_UNIT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RENEWAL_CHARGE IS 'リニューアル料金 既存システム物理名: RENEWAL_CHARGE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RENEWAL_CHARGE_UNIT_CODE IS '更新手数料単位 既存システム物理名: RENEWAL_CHARGE_UNIT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.STUDENT_ONLY_CODE IS '学生限定コード 既存システム物理名: STUDENT_ONLY_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SEX_CONDITION_CODE IS '性別限定コード 既存システム物理名: SEX_CONDITION_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KIDS_CODE IS '子供コード 既存システム物理名: KIDS_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ALONE_CODE IS '一人コード 既存システム物理名: ALONE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.TWO_PEOPLE_CODE IS 'シェアコード 既存システム物理名: TWO_PEOPLE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ELDER_CODE IS '高齢者コード 既存システム物理名: ELDER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CORPORATION_ONLY_CODE IS '法人のみコード 既存システム物理名: CORPORATION_ONLY_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RESIDENCE_HOUSE_RENT_CODE IS '居住用契約区分 既存システム物理名: RESIDENCE_HOUSE_RENT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_STYLE_CODE_1 IS '部屋スタイルコード１ 既存システム物理名: ROOM_STYLE_CODE_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_AREA_1 IS '部屋エリア１ 既存システム物理名: ROOM_AREA_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_UNIT_CODE_1 IS '部屋の広さ単位１ 既存システム物理名: ROOM_UNIT_CODE_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_STYLE_CODE_2 IS '部屋スタイルコード２ 既存システム物理名: ROOM_STYLE_CODE_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_AREA_2 IS '部屋エリア２ 既存システム物理名: ROOM_AREA_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_UNIT_CODE_2 IS '部屋ユニットコード２ 既存システム物理名: ROOM_UNIT_CODE_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_STYLE_CODE_3 IS '部屋スタイルコード３ 既存システム物理名: ROOM_STYLE_CODE_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_AREA_3 IS '部屋エリア３ 既存システム物理名: ROOM_AREA_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_UNIT_CODE_3 IS '部屋ユニットコード３ 既存システム物理名: ROOM_UNIT_CODE_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_STYLE_CODE_4 IS '部屋スタイルコード４ 既存システム物理名: ROOM_STYLE_CODE_4';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_AREA_4 IS '部屋エリア４ 既存システム物理名: ROOM_AREA_4';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_UNIT_CODE_4 IS '部屋ユニットコード４ 既存システム物理名: ROOM_UNIT_CODE_4';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_STYLE_CODE_5 IS '部屋スタイルコード５ 既存システム物理名: ROOM_STYLE_CODE_5';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_AREA_5 IS '部屋エリア５ 既存システム物理名: ROOM_AREA_5';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_UNIT_CODE_5 IS '部屋ユニットコード５ 既存システム物理名: ROOM_UNIT_CODE_5';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_STYLE_CODE_6 IS '部屋スタイルコード６ 既存システム物理名: ROOM_STYLE_CODE_6';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_AREA_6 IS '部屋エリア６ 既存システム物理名: ROOM_AREA_6';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_UNIT_CODE_6 IS '部屋ユニットコード６ 既存システム物理名: ROOM_UNIT_CODE_6';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_STYLE_CODE_7 IS '部屋スタイルコード７ 既存システム物理名: ROOM_STYLE_CODE_7';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_AREA_7 IS '部屋エリア７ 既存システム物理名: ROOM_AREA_7';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_UNIT_CODE_7 IS '部屋ユニットコード７ 既存システム物理名: ROOM_UNIT_CODE_7';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_STYLE_CODE_8 IS '部屋スタイルコード８ 既存システム物理名: ROOM_STYLE_CODE_8';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_AREA_8 IS '部屋エリア８ 既存システム物理名: ROOM_AREA_8';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_UNIT_CODE_8 IS '部屋ユニットコード８ 既存システム物理名: ROOM_UNIT_CODE_8';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_STYLE_CODE_9 IS '部屋スタイルコード９ 既存システム物理名: ROOM_STYLE_CODE_9';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_AREA_9 IS '部屋エリア９ 既存システム物理名: ROOM_AREA_9';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_UNIT_CODE_9 IS '部屋ユニットコード９ 既存システム物理名: ROOM_UNIT_CODE_9';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_STYLE_CODE_10 IS '部屋スタイルコード１０ 既存システム物理名: ROOM_STYLE_CODE_10';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_AREA_10 IS '部屋エリア１０ 既存システム物理名: ROOM_AREA_10';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_UNIT_CODE_10 IS '部屋ユニットコード１０ 既存システム物理名: ROOM_UNIT_CODE_10';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PARKING_CODE IS '駐車場コード 既存システム物理名: PARKING_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FROM_NEAR_PARKING IS '最寄駐車場距離 既存システム物理名: FROM_NEAR_PARKING';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PARKING_NUM IS '駐車場番号 既存システム物理名: PARKING_NUM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PARKING_TYPE_CODE IS '駐車場タイプコード 既存システム物理名: PARKING_TYPE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PARKING_SHUTTER_CODE IS '駐車場シャッターコード 既存システム物理名: PARKING_SHUTTER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PARKING_LOWER_COST IS '駐車場下段コード 既存システム物理名: PARKING_LOWER_COST';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PARKING_TAX_CODE IS '駐車場消費税コード 既存システム物理名: PARKING_TAX_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PARKABLE_NUM_CODE IS '駐車可能台数 既存システム物理名: PARKABLE_NUM_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PARKING_FREE_CODE IS '駐車場無料コード 既存システム物理名: PARKING_FREE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BIKE_PARK_CODE IS '二輪車駐車場コード 既存システム物理名: BIKE_PARK_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BIKE_PARK_COST IS '二輪車駐車場料金 既存システム物理名: BIKE_PARK_COST';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MOTORBIKE_PARK_CODE IS '自動二輪駐車場コード 既存システム物理名: MOTORBIKE_PARK_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MOTORBIKE_COST IS '自動二輪駐車場料金 既存システム物理名: MOTORBIKE_COST';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.AIRCON_CODE IS 'エアコンコード 既存システム物理名: AIRCON_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.COOLER_CODE IS '冷房コード 既存システム物理名: COOLER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.HEATING_CODE IS '暖房コード 既存システム物理名: HEATING_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LOAD_HEATER_CODE IS 'ロードヒーターコード 既存システム物理名: LOAD_HEATER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.STOVE_CODE IS 'ストーブコード 既存システム物理名: STOVE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FLOOR_HEATING_CODE IS '床暖房コード 既存システム物理名: FLOOR_HEATING_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CATV_CODE IS 'ケーブルTVコード 既存システム物理名: CATV_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.COMMUNITY_BROADCAST_CODE IS '有線コード 既存システム物理名: COMMUNITY_BROADCAST_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BS_CODE IS 'BSコード 既存システム物理名: BS_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CS_CODE IS 'CSコード 既存システム物理名: CS_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INTERNET_CODE IS 'インターネットコード 既存システム物理名: INTERNET_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CLOSET_CODE IS 'クローゼットコード 既存システム物理名: CLOSET_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WALKIN_WARDROBE_CODE IS 'ウォークインクローゼットコード 既存システム物理名: WALKIN_WARDROBE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CLOSET_UNDER_FLOOR_CODE IS '床下収納 既存システム物理名: CLOSET_UNDER_FLOOR_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.TRUNK_ROOM_CODE IS 'トランクルームコード 既存システム物理名: TRUNK_ROOM_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OSHIIRE_CODE IS '押入れコード 既存システム物理名: OSHIIRE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GARRET_CLOSET_CODE IS '屋根裏収納コード 既存システム物理名: GARRET_CLOSET_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SHOE_CUPBOARD_CODE IS '靴入れコード 既存システム物理名: SHOE_CUPBOARD_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.STOREROOM_CODE IS '物置コード 既存システム物理名: STOREROOM_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BATH_TOILET_CODE IS 'バストイレコード 既存システム物理名: BATH_TOILET_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BATH_CODE IS 'バスコード 既存システム物理名: BATH_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SHOWER_CODE IS 'シャワーコード 既存システム物理名: SHOWER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.AUTO_BATH_CODE IS '全自動風呂コード 既存システム物理名: AUTO_BATH_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DRESSING_ROOM_CODE IS '脱衣所コード 既存システム物理名: DRESSING_ROOM_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.REBOIL_BATH_CODE IS '追い焚き風呂コード 既存システム物理名: REBOIL_BATH_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.TOILET_CODE IS 'トイレコード 既存システム物理名: TOILET_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BATH_DRIER_CODE IS '浴室乾燥コード 既存システム物理名: BATH_DRIER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SHAMPOO_DRESSER_CODE IS '洗髪洗面化粧台コード 既存システム物理名: SHAMPOO_DRESSER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WASHLET_CODE IS 'ウォシュレットコード 既存システム物理名: WASHLET_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BATH_OVER_1TSUBO_CODE IS '風呂一坪以上 既存システム物理名: BATH_OVER_1TSUBO_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WARMLET_CODE IS '温水洗浄便座コード 既存システム物理名: WARMLET_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.COOKING_STOVE_CODE IS 'ガスコンロ対応区分 既存システム物理名: COOKING_STOVE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KITCHEN_CODE IS 'キッチンコード 既存システム物理名: KITCHEN_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MICROWAVE_OVEN_CODE IS '電子レンジコード 既存システム物理名: MICROWAVE_OVEN_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.IH_COOKING_HEATER_CODE IS 'IHクッキングヒーターコード 既存システム物理名: IH_COOKING_HEATER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.COLD_STORAGE_CODE IS '冷蔵庫コード 既存システム物理名: COLD_STORAGE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GRILL_CODE IS 'グリルコード 既存システム物理名: GRILL_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISPOSER_CODE IS 'ディスポーザーコード 既存システム物理名: DISPOSER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISH_WASHER_CODE IS '食器洗浄乾燥機 既存システム物理名: DISH_WASHER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WATER_CLEANER_CODE IS '浄水器コード 既存システム物理名: WATER_CLEANER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WOODEN_FLOOR_CODE IS 'フローリングコード 既存システム物理名: WOODEN_FLOOR_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LOFT_CODE IS 'ロフトコード 既存システム物理名: LOFT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CUSHION_FLOOR_CODE IS 'クッションフロアコード 既存システム物理名: CUSHION_FLOOR_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.HIGHEST_FLOOR_CODE IS '最上階コード 既存システム物理名: HIGHEST_FLOOR_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MAISONETTE_CODE IS 'メゾネット 既存システム物理名: MAISONETTE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OVER_SECOND_FLOOR_CODE IS '２階以上コード 既存システム物理名: OVER_SECOND_FLOOR_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CAVE_CODE IS '吹抜コード 既存システム物理名: CAVE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SOUNDPROOF_CODE IS '防音コード 既存システム物理名: SOUNDPROOF_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CORNER_HOUSE_CODE IS '角部屋コード 既存システム物理名: CORNER_HOUSE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SUNROOM_CODE IS 'サンルームコード 既存システム物理名: SUNROOM_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BASEMENT_CODE IS '地下室コード 既存システム物理名: BASEMENT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SOUTH_ROOM_CODE IS '南向きコード 既存システム物理名: SOUTH_ROOM_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PATIO_CODE IS '庭付コード 既存システム物理名: PATIO_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CRIME_PREV_SHUTTER_CODE IS '防犯シャッター 既存システム物理名: CRIME_PREV_SHUTTER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CRIME_PREV_CAMERA_CODE IS '防犯カメラ 既存システム物理名: CRIME_PREV_CAMERA_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.AUTOLOCK_CODE IS 'オートロックコード 既存システム物理名: AUTOLOCK_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DOUBLE_LOCK_CODE IS '二重ロックコード 既存システム物理名: DOUBLE_LOCK_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WASHING_MACHINE_CODE IS '洗濯機コード 既存システム物理名: WASHING_MACHINE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DRIER_CODE IS '乾燥機コード 既存システム物理名: DRIER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WASHING_MACHINE_PLACE_CODE IS '洗濯機置き場コード 既存システム物理名: WASHING_MACHINE_PLACE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CARD_KEY_CODE IS 'カードキーコード 既存システム物理名: CARD_KEY_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BOW_WINDOW_CODE IS '出窓コード 既存システム物理名: BOW_WINDOW_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LIGHT_CODE IS '照明コード 既存システム物理名: LIGHT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ALL_ELECTRIC_CODE IS 'オール電化コード 既存システム物理名: ALL_ELECTRIC_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.HOT_WATER_SUPPLY_CODE IS '給湯 既存システム物理名: HOT_WATER_SUPPLY_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INTERPHONE_CODE IS 'インターフォンコード 既存システム物理名: INTERPHONE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FULLTIME_FUN_CODE IS '24時間換気コード 既存システム物理名: FULLTIME_FUN_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ECOCUTE_CODE IS 'エコキュートコード 既存システム物理名: ECOCUTE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DOUBLE_SIDE_BALCONY_CODE IS '両面バルコニーコード 既存システム物理名: DOUBLE_SIDE_BALCONY_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BALCONY_SIDE_NUM_CODE IS 'バルコニー面数 既存システム物理名: BALCONY_SIDE_NUM_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BATH_TV_CODE IS '浴室TVコード 既存システム物理名: BATH_TV_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PORCH_CODE IS '玄関コード 既存システム物理名: PORCH_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.UP_START_DATE IS '掲載開始日付 既存システム物理名: UP_START_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.UP_END_DATE IS '掲載終了日付 既存システム物理名: UP_END_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DRESSING_TABLE_CODE IS '洗面化粧台コード 既存システム物理名: DRESSING_TABLE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PRIVATE_DUST_BOX_CODE IS 'シャッターコード 既存システム物理名: PRIVATE_DUST_BOX_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PIANO_CODE IS 'ピアノコード 既存システム物理名: PIANO_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LARGE_SHOES_BOX_CODE IS 'シューズボックスコード 既存システム物理名: LARGE_SHOES_BOX_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CLOSET_UNDER_TATAMI_CODE IS '凍結防止機能付給湯コード 既存システム物理名: CLOSET_UNDER_TATAMI_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INDOORS_BICYCLE_PARKING_CODE IS '屋内駐輪所コード 既存システム物理名: INDOORS_BICYCLE_PARKING_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SECURITY_KEY_CODE IS 'セキュリティーキーコード 既存システム物理名: SECURITY_KEY_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SHUTTER_CODE IS 'シャッターコード 既存システム物理名: SHUTTER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FOR_SOUTH_CODE IS '南向きコード 既存システム物理名: FOR_SOUTH_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CLOSET_UNDERSTAIR_CODE IS '床下収納コード 既存システム物理名: CLOSET_UNDERSTAIR_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NEARBY_CONVENIENCE_STORE_CODE IS '近隣コンビニコード 既存システム物理名: NEARBY_CONVENIENCE_STORE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NEARBY_BANK_CODE IS '近隣銀行コード 既存システム物理名: NEARBY_BANK_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NEARBY_RENTAL_VIDEO_CODE IS '近隣レンタルビデオコード 既存システム物理名: NEARBY_RENTAL_VIDEO_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LARGE_SCALE_RENEWAL_CODE IS '大規模リニューアル済みコード 既存システム物理名: LARGE_SCALE_RENEWAL_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RECOVERY_COST_CODE IS '現状回復費用負担少仕様コード 既存システム物理名: RECOVERY_COST_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GUARANTOR_CODE IS '保証人不要区分 既存システム物理名: GUARANTOR_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GUARANTOR_PROXY_CODE IS '保証人代行区分 既存システム物理名: GUARANTOR_PROXY_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GUARANTOR_PROXY_COM_CODE IS '保証人代行会社区分 既存システム物理名: GUARANTOR_PROXY_COM_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GUARANTOR_PROXY_COMMENT IS '保証人代行詳細 既存システム物理名: GUARANTOR_PROXY_COMMENT';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISP_MAP_CODE IS '地図表示可能フラグ 既存システム物理名: DISP_MAP_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LATITUDE_WORLD IS '緯度(世界測地系) 既存システム物理名: LATITUDE_WORLD';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LONGITUDE_WORLD IS '経度(世界測地系) 既存システム物理名: LONGITUDE_WORLD';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GARDEN_CODE IS '庭・専用庭コード 既存システム物理名: GARDEN_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BALCONY_CODE IS 'バルコニーコード 既存システム物理名: BALCONY_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PANORAMA_ID IS 'パノラマID 既存システム物理名: PANORAMA_ID';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LARGE_SCALE_RENEWAL_DATE IS '大規模リニューアル日 既存システム物理名: LARGE_SCALE_RENEWAL_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SHATAKU_KANOU_CODE IS '社宅代行可能フラグ 既存システム物理名: SHATAKU_KANOU_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NO_DEPOSIT_PLAN_CODE IS '敷金ゼロプラン 既存システム物理名: NO_DEPOSIT_PLAN_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KENTAKU_KIND_CODE IS '建託版建物種別 既存システム物理名: KENTAKU_KIND_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PRICE_SALE_FLAG IS '家賃割引フラグ 既存システム物理名: PRICE_SALE_FLAG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.REFOME_FLAG IS 'リフォームフラグ 既存システム物理名: REFOME_FLAG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PRODUCT_CODE IS '商品名称CD 既存システム物理名: PRODUCT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OWNER_SHIP_BRANCH_CODE IS '客付支店コード 既存システム物理名: OWNER_SHIP_BRANCH_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KENTAKU_BUILDING_CODE IS '建物コード 既存システム物理名: KENTAKU_BUILDING_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KENTAKU_ROOM_CODE IS '部屋コード 既存システム物理名: KENTAKU_ROOM_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KEY_EXCHANGE_FREE_CODE IS 'カギ交換無料フラグ 既存システム物理名: KEY_EXCHANGE_FREE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.AD_PRICE IS 'AD金額 既存システム物理名: AD_PRICE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FF_PRICE IS 'FF金額 既存システム物理名: FF_PRICE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LEAVE_DATE_TP IS '退去予定日(TP用) 既存システム物理名: LEAVE_DATE_TP';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LEAVE_FINISH_DATE IS '退去日 既存システム物理名: LEAVE_FINISH_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LOW_PARKING_PRICE IS '最低駐車料 既存システム物理名: LOW_PARKING_PRICE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.HIGH_PARKING_PRICE IS '最高駐車料 既存システム物理名: HIGH_PARKING_PRICE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.STRUCTURE_DISP_NAME_TP IS '表示用構造名称(TP用) 既存システム物理名: STRUCTURE_DISP_NAME_TP';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISPLACE_CODE IS '排水区分 既存システム物理名: DISPLACE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FINANCE_CORPORATION_CODE IS '公庫融資サイン 既存システム物理名: FINANCE_CORPORATION_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WATER_COMPANY_NAME IS '水道局会社名 既存システム物理名: WATER_COMPANY_NAME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WATER_COMPANY_TEL IS '水道局会社連絡先 既存システム物理名: WATER_COMPANY_TEL';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ELECTRIC_COMPANY_NAME IS '電気会社名 既存システム物理名: ELECTRIC_COMPANY_NAME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ELECTRIC_COMPANY_TEL IS '電気会社連絡先 既存システム物理名: ELECTRIC_COMPANY_TEL';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GAS_COMPANY_NAME IS 'ガス会社名 既存システム物理名: GAS_COMPANY_NAME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GAS_COMPANY_TEL IS 'ガス会社連絡先 既存システム物理名: GAS_COMPANY_TEL';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.COLLECT_DATE IS '斡旋回収日 既存システム物理名: COLLECT_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KOUENTIN_CODE IS '高円賃サイン 既存システム物理名: KOUENTIN_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INTO_DATE_TXT IS '入居可能日 既存システム物理名: INTO_DATE_TXT';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_SITUATION_CODE IS '部屋状況区分 既存システム物理名: ROOM_SITUATION_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RECORD_SITUATION_CODE IS 'レコード状態区分 既存システム物理名: RECORD_SITUATION_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ELECTRIC_DISCOUNT_FLAG IS '太陽光発電割引対象フラグ 既存システム物理名: ELECTRIC_DISCOUNT_FLAG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FLETS_HIKARI_CODE IS 'フレッツ光ＣＤ 既存システム物理名: FLETS_HIKARI_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.AKIYA_TERM IS '空家期間 既存システム物理名: AKIYA_TERM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CLEANING_FEE_CODE IS 'クリーニング費発生フラグ 既存システム物理名: CLEANING_FEE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CLEANING_FEE IS 'クリーニング費 既存システム物理名: CLEANING_FEE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.POWER_CODE IS '動力有無サイン 既存システム物理名: POWER_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FIRE_ZONE_CODE IS '防火地域区分 既存システム物理名: FIRE_ZONE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISCOUNT_RATE IS '家賃割割引率 既存システム物理名: DISCOUNT_RATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DISCOUNT_TERM IS '家賃割引期間 既存システム物理名: DISCOUNT_TERM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PET_FLAG IS 'ペットフラグ 既存システム物理名: PET_FLAG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INTERNET_FREE_CODE IS 'インターネット無料 既存システム物理名: INTERNET_FREE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ALL_ROOM_CLOSET IS '全居室収納 既存システム物理名: ALL_ROOM_CLOSET';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WALK_THROUGH_CLOSET IS 'ウォークスルークローゼット 既存システム物理名: WALK_THROUGH_CLOSET';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FREE_WASH_ROOM IS '洗面所独立 既存システム物理名: FREE_WASH_ROOM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.AUTO_BATH IS 'オートバス 既存システム物理名: AUTO_BATH';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INDOOR_CLOTHES_DRYING IS '室内物干し 既存システム物理名: INDOOR_CLOTHES_DRYING';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MOTION_SENSOR_LIGHTING IS '人感照明センサー 既存システム物理名: MOTION_SENSOR_LIGHTING';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OPEN_KITCHEN IS 'オープンキッチン 既存システム物理名: OPEN_KITCHEN';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ISLAND_KITCHEN IS 'アイランドキッチン 既存システム物理名: ISLAND_KITCHEN';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GAS_COOKER_ATTACHED IS 'ガスコンロ付 既存システム物理名: GAS_COOKER_ATTACHED';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.THREE_OVER_GAS IS '3口以上コンロ 既存システム物理名: THREE_OVER_GAS';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DOUBLE_GLAZING IS '複層ガラス 既存システム物理名: DOUBLE_GLAZING';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SECURITY_GLAZING IS '防犯ガラス 既存システム物理名: SECURITY_GLAZING';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.VIBRATION_CONTROL_FLOOR IS '防振フローリング 既存システム物理名: VIBRATION_CONTROL_FLOOR';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SNOW_VANISHING_FACILITY IS '消雪設備 既存システム物理名: SNOW_VANISHING_FACILITY';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KEROSENE_HEATER IS '灯油暖房 既存システム物理名: KEROSENE_HEATER';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BATH_WINDOW IS '浴室窓 既存システム物理名: BATH_WINDOW';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.JAPANESE_STYLE_ROOM IS '和室 既存システム物理名: JAPANESE_STYLE_ROOM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.EARTHQUAKE_RESIST_CONST IS '耐震構造 既存システム物理名: EARTHQUAKE_RESIST_CONST';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ALLINONE_SERVICE_WATER IS 'おまとめサービス（水道） 既存システム物理名: ALLINONE_SERVICE_WATER';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ALLINONE_SERVICE_ELECTRICITY IS 'おまとめサービス（電気） 既存システム物理名: ALLINONE_SERVICE_ELECTRICITY';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ALLINONE_SERVICE_GAS IS 'おまとめサービス（ガス） 既存システム物理名: ALLINONE_SERVICE_GAS';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PRICE_AND_COST IS '共益費/管理費を含む 既存システム物理名: PRICE_AND_COST';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.VAL_CODE_1 IS 'バリューコード１ 既存システム物理名: VAL_CODE_1';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.VAL_CODE_2 IS 'バリューコード２ 既存システム物理名: VAL_CODE_2';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.VAL_CODE_3 IS 'バリューコード３ 既存システム物理名: VAL_CODE_3';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PANORAMA_TYPE IS 'パノラマタイプ 既存システム物理名: PANORAMA_TYPE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SERVICE_FEE_DETAILS IS '共益費等明細 既存システム物理名: SERVICE_FEE_DETAILS';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SHINSA_BRANCH_CODE IS '家賃審査センターコード 既存システム物理名: SHINSA_BRANCH_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CONTRACT_CONFIRM_CODE IS '契約形態 既存システム物理名: CONTRACT_CONFIRM_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PREFECTURE_EN IS '英語都道府県名 既存システム物理名: PREFECTURE_EN';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SHIKUGUNCHOUSON_EN IS '英語市区郡名 既存システム物理名: SHIKUGUNCHOUSON_EN';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.OOAZA_TSUUSYOU_EN IS '英語町名 既存システム物理名: OOAZA_TSUUSYOU_EN';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.AZA_CHOUME_EN IS '英語丁目 既存システム物理名: AZA_CHOUME_EN';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RESTADDR_ALPHABET IS '住所詳細アルファベット表記 既存システム物理名: RESTADDR_ALPHABET';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.UP_STATE IS '掲載状態 既存システム物理名: UP_STATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.AD_PRICE_UNIT_CODE IS 'AD単位 既存システム物理名: AD_PRICE_UNIT_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.DELETE_DATE IS '削除予定日 既存システム物理名: DELETE_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.REALTIME_UP_TIME IS '即時更新用時刻 既存システム物理名: REALTIME_UP_TIME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.REALTIME_UP_TYPE IS '即時更新用種別 既存システム物理名: REALTIME_UP_TYPE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.PRODUCT_TYPE_CD IS '商品タイプCD 既存システム物理名: PRODUCT_TYPE_CD';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MONEY_UPDATE_TIME IS '金額更新時刻 既存システム物理名: MONEY_UPDATE_TIME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KODAWARI100_199 IS '近隣駐車場距離 既存システム物理名: KODAWARI100_199';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FLOOR_MAX_ROOM IS '階最大戸数 既存システム物理名: FLOOR_MAX_ROOM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.RENEWAL_FEE_FLG IS '更新手数料フラグ 既存システム物理名: RENEWAL_FEE_FLG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.FULLTIME_SUPPORT_FLG IS '24Hサポートフラグ 既存システム物理名: FULLTIME_SUPPORT_FLG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MEMBERSHIP_FEE_EXEMPTION_KBN IS '自治会費 免除区分 既存システム物理名: MEMBERSHIP_FEE_EXEMPTION_KBN';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MEMBERSHIP_FEE_EXEMPTION_DAYS IS '自治会費 免除判定日数 既存システム物理名: MEMBERSHIP_FEE_EXEMPTION_DAYS';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.EBOARD_COMMENT IS 'いい物件コメント追加 既存システム物理名: EBOARD_COMMENT';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.MANAGEMENT_PARKING_KBN IS '管理駐車場対象区分 既存システム物理名: MANAGEMENT_PARKING_KBN';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NET_SERVICE_JCOM IS 'DKネットJCOM 既存システム物理名: NET_SERVICE_JCOM';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.NET_SERVICE_STARCAT IS 'DKネットスターキャット 既存システム物理名: NET_SERVICE_STARCAT';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ZEH_ORIENTED IS 'ZEHｵﾘｴﾝﾃｯﾄﾞ 既存システム物理名: ZEH_ORIENTED';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ZEH_DK_SOLEIL IS 'DK-ZEHｿﾚｲﾕ 既存システム物理名: ZEH_DK_SOLEIL';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ZEH_DK_ALPHA IS 'DK-ZEHα 既存システム物理名: ZEH_DK_ALPHA';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.KEY_SET_COST_FLAG IS '鍵セット費用フラグ 既存システム物理名: KEY_SET_COST_FLAG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ELECTRIC_INTRODUCTION IS '電気紹介 既存システム物理名: ELECTRIC_INTRODUCTION';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ELECTRIC_TYPE IS '電気種別 既存システム物理名: ELECTRIC_TYPE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.EMERGENCY_E_COMPANY_NAME IS '停電・緊急時　会社名 既存システム物理名: EMERGENCY_E_COMPANY_NAME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.EMERGENCY_E_COMPANY_TEL IS '停電・緊急時　連絡先 既存システム物理名: EMERGENCY_E_COMPANY_TEL';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.GAS_INTRODUCTION IS 'ガス紹介 既存システム物理名: GAS_INTRODUCTION';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.EMERGENCY_GAS_COMPANY_NAME IS '急ぎの開栓時　会社名 既存システム物理名: EMERGENCY_GAS_COMPANY_NAME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.EMERGENCY_GAS_COMPANY_TEL IS '急ぎの開栓時　連絡先 既存システム物理名: EMERGENCY_GAS_COMPANY_TEL';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WATER_INTRODUCTION IS '水道紹介 既存システム物理名: WATER_INTRODUCTION';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WATER_METER_TYPE IS '水道メーター 既存システム物理名: WATER_METER_TYPE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INTERNET_TYPE IS 'インターネット種別 既存システム物理名: INTERNET_TYPE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INTERNET_NAME IS 'インターネット会社名 既存システム物理名: INTERNET_NAME';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INTERNET_TEL IS 'インターネット会社連絡先 既存システム物理名: INTERNET_TEL';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.INTERNET_INTRODUCTION IS 'インターネット紹介 既存システム物理名: INTERNET_INTRODUCTION';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.WATER_SERVER IS 'ウォーターサーバー紹介 既存システム物理名: WATER_SERVER';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.LIFELINE_GUIDANCE_TYPE IS 'ライフラインのご案内 既存システム物理名: LIFELINE_GUIDANCE_TYPE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_SAVE_ENERGY_CERT_DATE IS '住戸 省エネ認証取得日 既存システム物理名: ROOM_SAVE_ENERGY_CERT_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_THIRD_PARTY_EVAL_FLG IS '住戸 第三者評価フラグ 既存システム物理名: ROOM_THIRD_PARTY_EVAL_FLG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_SAVE_ENERGY_LEVEL IS '住戸 省エネ性能ラベルファイル 既存システム物理名: ROOM_SAVE_ENERGY_LEVEL';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_ENERGY_COST IS '住戸 エネルギー消費性能 既存システム物理名: ROOM_ENERGY_COST';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_ENERGY_COST_SUN IS '住戸 エネルギー消費性能（太陽光） 既存システム物理名: ROOM_ENERGY_COST_SUN';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_RENEW_ENERGY_FLG IS '住戸 再エネ有無 既存システム物理名: ROOM_RENEW_ENERGY_FLG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_INSULATION_LEVEL IS '住戸 断熱性能 既存システム物理名: ROOM_INSULATION_LEVEL';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_EASY_UTILITY_COSTS IS '住戸 目安光熱費 既存システム物理名: ROOM_EASY_UTILITY_COSTS';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_ZEH_LEVEL_FLG IS '住戸 ZEH水準フラグ 既存システム物理名: ROOM_ZEH_LEVEL_FLG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.ROOM_NET_ZERO_ENERGY_FLG IS '住戸 ネットゼロエネルギーフラグ 既存システム物理名: ROOM_NET_ZERO_ENERGY_FLG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_SAVE_ENERGY_CERT_DATE IS '住棟 省エネ認証取得日 既存システム物理名: BUILDING_SAVE_ENERGY_CERT_DATE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_THIRD_PARTY_EVAL_FLG IS '住棟 第三者評価フラグ 既存システム物理名: BUILDING_THIRD_PARTY_EVAL_FLG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_SAVE_ENERGY_LEVEL IS '住棟 省エネ性能ラベルファイル 既存システム物理名: BUILDING_SAVE_ENERGY_LEVEL';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_ENERGY_COST IS '住棟 エネルギー消費性能 既存システム物理名: BUILDING_ENERGY_COST';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_ENERGY_COST_SUN IS '住棟 エネルギー消費性能（太陽光） 既存システム物理名: BUILDING_ENERGY_COST_SUN';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_RENEW_ENERGY_FLG IS '住棟 再エネ有無 既存システム物理名: BUILDING_RENEW_ENERGY_FLG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_INSULATION_LEVEL IS '住棟 断熱性能 既存システム物理名: BUILDING_INSULATION_LEVEL';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_EASY_UTILITY_COSTS IS '住棟 目安光熱費 既存システム物理名: BUILDING_EASY_UTILITY_COSTS';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_ZEH_LEVEL_FLG IS '住棟 ZEH水準フラグ 既存システム物理名: BUILDING_ZEH_LEVEL_FLG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.BUILDING_NET_ZERO_ENERGY_FLG IS '住棟 ネットゼロエネルギーフラグ 既存システム物理名: BUILDING_NET_ZERO_ENERGY_FLG';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.SHINSA_BUSINESS_OFFICE_CODE IS '審査営業所 既存システム物理名: SHINSA_BUSINESS_OFFICE_CODE';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CHALLENGE_START IS 'チャレンジ開始日 既存システム物理名: CHALLENGE_START';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CHALLENGE_END IS 'チャレンジ終了日 既存システム物理名: CHALLENGE_END';
COMMENT ON COLUMN SITE_PROPERTY_KENTAKU_FOR_P.CHALLENGE_DISCOUNT_PRICE IS '割引金額 既存システム物理名: CHALLENGE_DISCOUNT_PRICE';
