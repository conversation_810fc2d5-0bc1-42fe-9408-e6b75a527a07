package jp.ne.simplex.application.repository.sftp

import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Repository
import java.nio.file.Path

@Profile("batch")
@Repository
class SftpRepository(
    private val sftpTemplate: SftpTemplate
) : SftpRepositoryInterface {

    override fun sendFile(localFilePath: Path, remoteFileName: String) {
        try {
            sftpTemplate.connect()
            sftpTemplate.sendFile(localFilePath, remoteFileName)

        } finally {
            sftpTemplate.disconnect()
        }
    }
}

interface SftpRepositoryInterface {
    fun sendFile(localFilePath: Path, remoteFileName: String)
}
