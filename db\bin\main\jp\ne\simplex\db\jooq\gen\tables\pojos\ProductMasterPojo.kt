/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 商品マスタ 既存システム物理名: BGKMBP
 */
@Suppress("UNCHECKED_CAST")
data class ProductMasterPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgram: String? = null,
    var updater: String? = null,
    var deleteFlag: String? = null,
    var productNameCode: Short,
    var productCodeBranch: Byte,
    var effectiveStartDate: Int,
    var effectiveEndDate: Int? = null,
    var gradeName: String? = null,
    var gradeAbbreviation: String? = null,
    var buildingTypeCodeSt: String? = null,
    var minUnitAlignment: Short? = null,
    var maxUnitAlignment: Short? = null
): Serializable {


    override fun equals(other: Any?): Bo<PERSON>an {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: ProductMasterPojo = other as ProductMasterPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgram == null) {
            if (o.updateProgram != null)
                return false
        }
        else if (this.updateProgram != o.updateProgram)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.deleteFlag == null) {
            if (o.deleteFlag != null)
                return false
        }
        else if (this.deleteFlag != o.deleteFlag)
            return false
        if (this.productNameCode != o.productNameCode)
            return false
        if (this.productCodeBranch != o.productCodeBranch)
            return false
        if (this.effectiveStartDate != o.effectiveStartDate)
            return false
        if (this.effectiveEndDate == null) {
            if (o.effectiveEndDate != null)
                return false
        }
        else if (this.effectiveEndDate != o.effectiveEndDate)
            return false
        if (this.gradeName == null) {
            if (o.gradeName != null)
                return false
        }
        else if (this.gradeName != o.gradeName)
            return false
        if (this.gradeAbbreviation == null) {
            if (o.gradeAbbreviation != null)
                return false
        }
        else if (this.gradeAbbreviation != o.gradeAbbreviation)
            return false
        if (this.buildingTypeCodeSt == null) {
            if (o.buildingTypeCodeSt != null)
                return false
        }
        else if (this.buildingTypeCodeSt != o.buildingTypeCodeSt)
            return false
        if (this.minUnitAlignment == null) {
            if (o.minUnitAlignment != null)
                return false
        }
        else if (this.minUnitAlignment != o.minUnitAlignment)
            return false
        if (this.maxUnitAlignment == null) {
            if (o.maxUnitAlignment != null)
                return false
        }
        else if (this.maxUnitAlignment != o.maxUnitAlignment)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgram == null) 0 else this.updateProgram.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.deleteFlag == null) 0 else this.deleteFlag.hashCode())
        result = prime * result + this.productNameCode.hashCode()
        result = prime * result + this.productCodeBranch.hashCode()
        result = prime * result + this.effectiveStartDate.hashCode()
        result = prime * result + (if (this.effectiveEndDate == null) 0 else this.effectiveEndDate.hashCode())
        result = prime * result + (if (this.gradeName == null) 0 else this.gradeName.hashCode())
        result = prime * result + (if (this.gradeAbbreviation == null) 0 else this.gradeAbbreviation.hashCode())
        result = prime * result + (if (this.buildingTypeCodeSt == null) 0 else this.buildingTypeCodeSt.hashCode())
        result = prime * result + (if (this.minUnitAlignment == null) 0 else this.minUnitAlignment.hashCode())
        result = prime * result + (if (this.maxUnitAlignment == null) 0 else this.maxUnitAlignment.hashCode())
        return result
    }
}
