-- TABLE: NEW_OFFICE_MASTER(新営業所マスタ)

CREATE TABLE NEW_OFFICE_MASTER(
     ID                                           numeric(5,0)      NOT NULL    
,    AREA                                         varchar(22)       NOT NULL    
,    BUSINESS_DEPARTMENT                          varchar(22)       NOT NULL    
,    BUSINESS_OFFICE_CODE                         varchar(3)        NOT NULL    
,    BUSINESS_OFFICE_NAME                         varchar(22)       NOT NULL    
,    PREFECTURE_CODE                              varchar(2)        NOT NULL    
,    PREFECTURE_NAME                              varchar(18)       NOT NULL    
,    CITY_CODE                                    varchar(3)        NOT NULL    
,    CITY_NAME                                    varchar(18)       NOT NULL    
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    DELETE_FLAG                                  varchar(1)        NOT NULL    
,    CONSTRAINT PK_NEW_OFFICE_MASTER PRIMARY KEY (ID)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE NEW_OFFICE_MASTER IS '新営業所マスタ 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.ID IS 'ID 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.AREA IS '地域 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.BUSINESS_DEPARTMENT IS '事業部 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.BUSINESS_OFFICE_CODE IS '営業所コード 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.BUSINESS_OFFICE_NAME IS '営業所名 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.PREFECTURE_CODE IS '都道府県コード 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.PREFECTURE_NAME IS '都道府県名 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.CITY_CODE IS '市区町村コード 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.CITY_NAME IS '市区町村名 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.CREATOR IS '作成者 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.UPDATER IS '更新者 既存システム物理名: -';
COMMENT ON COLUMN NEW_OFFICE_MASTER.DELETE_FLAG IS '削除フラグ 既存システム物理名: -';
