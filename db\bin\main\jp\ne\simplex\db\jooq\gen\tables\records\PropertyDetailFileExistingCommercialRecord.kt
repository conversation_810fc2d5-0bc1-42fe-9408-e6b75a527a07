/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.PropertyDetailFileExistingCommercialTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.PropertyDetailFileExistingCommercialPojo

import org.jooq.impl.TableRecordImpl


/**
 * 物件明細ファイル(既存事業用)商品管理Web 既存システム物理名: EMUU2P
 */
@Suppress("UNCHECKED_CAST")
open class PropertyDetailFileExistingCommercialRecord private constructor() : TableRecordImpl<PropertyDetailFileExistingCommercialRecord>(PropertyDetailFileExistingCommercialTable.PROPERTY_DETAIL_FILE_EXISTING_COMMERCIAL) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var occurrenceMonthDivision: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var occurrenceMonth: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var salesDeptCd: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var salesDeptName: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var salesDeptOutputOrderCd: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var branchCd: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var branchName: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var branchOutputOrderCd: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var processDate: Int?
        set(value): Unit = set(15, value)
        get(): Int? = get(15) as Int?

    open var statusName: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var customerFlag: Byte?
        set(value): Unit = set(17, value)
        get(): Byte? = get(17) as Byte?

    open var customerRepCd: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var customerRepName: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var occupancyFlag: Byte?
        set(value): Unit = set(20, value)
        get(): Byte? = get(20) as Byte?

    open var buildingCode: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var buildingName: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var addressCd: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var location: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var expectedCompletionDate: Int?
        set(value): Unit = set(25, value)
        get(): Int? = get(25) as Int?

    open var completionDate: Int?
        set(value): Unit = set(26, value)
        get(): Int? = get(26) as Int?

    open var roomCode: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var roomNumber: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var landlordCd: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var landlordName: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var bulkLeasingSign: Byte?
        set(value): Unit = set(31, value)
        get(): Byte? = get(31) as Byte?

    open var contractTypeName: String?
        set(value): Unit = set(32, value)
        get(): String? = get(32) as String?

    open var roomTypeCd: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var roomTypeName: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var layoutDivision: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var layoutName: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var exclusiveArea: BigDecimal?
        set(value): Unit = set(37, value)
        get(): BigDecimal? = get(37) as BigDecimal?

    open var tenantContractNumber: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var currentStatusDivision: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var modifiedStatusDivision: String?
        set(value): Unit = set(40, value)
        get(): String? = get(40) as String?

    open var vacationNoticeDate: Int?
        set(value): Unit = set(41, value)
        get(): Int? = get(41) as Int?

    open var reviewApprovalDate: Int?
        set(value): Unit = set(42, value)
        get(): Int? = get(42) as Int?

    open var reviewApprovalDaysElapsed: Int?
        set(value): Unit = set(43, value)
        get(): Int? = get(43) as Int?

    open var arrangementOutputDate: Int?
        set(value): Unit = set(44, value)
        get(): Int? = get(44) as Int?

    open var arrangementApprovalDate: Int?
        set(value): Unit = set(45, value)
        get(): Int? = get(45) as Int?

    open var arrangementCollectionDate: Int?
        set(value): Unit = set(46, value)
        get(): Int? = get(46) as Int?

    open var arrangementCollectionDaysElapsed: Int?
        set(value): Unit = set(47, value)
        get(): Int? = get(47) as Int?

    open var moveOutMeetingDate: Int?
        set(value): Unit = set(48, value)
        get(): Int? = get(48) as Int?

    open var expectedMoveOutDate: Int?
        set(value): Unit = set(49, value)
        get(): Int? = get(49) as Int?

    open var moveOutDate: Int?
        set(value): Unit = set(50, value)
        get(): Int? = get(50) as Int?

    open var moveOutRent: Int?
        set(value): Unit = set(51, value)
        get(): Int? = get(51) as Int?

    open var reviewRent: Int?
        set(value): Unit = set(52, value)
        get(): Int? = get(52) as Int?

    open var currentRent: Int?
        set(value): Unit = set(53, value)
        get(): Int? = get(53) as Int?

    open var differenceReviewMoveOut: Int?
        set(value): Unit = set(54, value)
        get(): Int? = get(54) as Int?

    open var differenceCurrentMoveOut: Int?
        set(value): Unit = set(55, value)
        get(): Int? = get(55) as Int?

    open var differenceCurrentReview: Int?
        set(value): Unit = set(56, value)
        get(): Int? = get(56) as Int?

    open var discrepancyStatus: String?
        set(value): Unit = set(57, value)
        get(): String? = get(57) as String?

    open var longestVacancyPeriodHistory: Int?
        set(value): Unit = set(58, value)
        get(): Int? = get(58) as Int?

    open var residentialRoomCount: Short?
        set(value): Unit = set(59, value)
        get(): Short? = get(59) as Short?

    open var residentialRoomCountVacant: Short?
        set(value): Unit = set(60, value)
        get(): Short? = get(60) as Short?

    open var restorationCompletionExpectedDate: Int?
        set(value): Unit = set(61, value)
        get(): Int? = get(61) as Int?

    open var restorationCompletionDate: Int?
        set(value): Unit = set(62, value)
        get(): Int? = get(62) as Int?

    open var vacancyAccountingExpectedDate: Int?
        set(value): Unit = set(63, value)
        get(): Int? = get(63) as Int?

    open var vacancyAccountingDate: Int?
        set(value): Unit = set(64, value)
        get(): Int? = get(64) as Int?

    open var vacancyMonths: Short?
        set(value): Unit = set(65, value)
        get(): Short? = get(65) as Short?

    open var vacancyPeriod: Int?
        set(value): Unit = set(66, value)
        get(): Int? = get(66) as Int?

    open var occupancyApplicationDate: Int?
        set(value): Unit = set(67, value)
        get(): Int? = get(67) as Int?

    open var contractDate: Int?
        set(value): Unit = set(68, value)
        get(): Int? = get(68) as Int?

    open var remainingCollectionExpectedDate: Int?
        set(value): Unit = set(69, value)
        get(): Int? = get(69) as Int?

    open var remainingCollectionDate: Int?
        set(value): Unit = set(70, value)
        get(): Int? = get(70) as Int?

    open var expectedOccupancyDate: Int?
        set(value): Unit = set(71, value)
        get(): Int? = get(71) as Int?

    open var occupancyDate: Int?
        set(value): Unit = set(72, value)
        get(): Int? = get(72) as Int?

    open var ad: Byte?
        set(value): Unit = set(73, value)
        get(): Byte? = get(73) as Byte?

    open var ff: Byte?
        set(value): Unit = set(74, value)
        get(): Byte? = get(74) as Byte?

    open var supportMechanismDivision: String?
        set(value): Unit = set(75, value)
        get(): String? = get(75) as String?

    open var preferredRentalDivision: String?
        set(value): Unit = set(76, value)
        get(): String? = get(76) as String?

    open var financingDivision: String?
        set(value): Unit = set(77, value)
        get(): String? = get(77) as String?

    open var hlApprovalDivision: String?
        set(value): Unit = set(78, value)
        get(): String? = get(78) as String?

    open var hl: String?
        set(value): Unit = set(79, value)
        get(): String? = get(79) as String?

    open var rentDiscountFlag: Byte?
        set(value): Unit = set(80, value)
        get(): Byte? = get(80) as Byte?

    open var rentDiscount: String?
        set(value): Unit = set(81, value)
        get(): String? = get(81) as String?

    open var reformType: String?
        set(value): Unit = set(82, value)
        get(): String? = get(82) as String?

    open var reform: Byte?
        set(value): Unit = set(83, value)
        get(): Byte? = get(83) as Byte?

    open var threeMonthFf: Byte?
        set(value): Unit = set(84, value)
        get(): Byte? = get(84) as Byte?

    open var recruitmentStatusDivision: String?
        set(value): Unit = set(85, value)
        get(): String? = get(85) as String?

    open var reserveDivision_2: String?
        set(value): Unit = set(86, value)
        get(): String? = get(86) as String?

    open var reserveDivision_3: String?
        set(value): Unit = set(87, value)
        get(): String? = get(87) as String?

    open var reserveDivision_4: String?
        set(value): Unit = set(88, value)
        get(): String? = get(88) as String?

    open var reserveDivision_5: String?
        set(value): Unit = set(89, value)
        get(): String? = get(89) as String?

    open var reserveDivision_6: String?
        set(value): Unit = set(90, value)
        get(): String? = get(90) as String?

    open var reserveDivision_7: String?
        set(value): Unit = set(91, value)
        get(): String? = get(91) as String?

    open var reserveDivision_8: String?
        set(value): Unit = set(92, value)
        get(): String? = get(92) as String?

    open var reserveDivision_9: String?
        set(value): Unit = set(93, value)
        get(): String? = get(93) as String?

    open var reserveDivision_10: String?
        set(value): Unit = set(94, value)
        get(): String? = get(94) as String?

    open var constructionRepCd: String?
        set(value): Unit = set(95, value)
        get(): String? = get(95) as String?

    open var constructionRepName: String?
        set(value): Unit = set(96, value)
        get(): String? = get(96) as String?

    open var implementationRepCd: String?
        set(value): Unit = set(97, value)
        get(): String? = get(97) as String?

    open var implementationRepName: String?
        set(value): Unit = set(98, value)
        get(): String? = get(98) as String?

    open var adActualAmount: Int?
        set(value): Unit = set(99, value)
        get(): Int? = get(99) as Int?

    open var ffActualAmount: Int?
        set(value): Unit = set(100, value)
        get(): Int? = get(100) as Int?

    open var municipalityCd: String?
        set(value): Unit = set(101, value)
        get(): String? = get(101) as String?

    open var municipalityName: String?
        set(value): Unit = set(102, value)
        get(): String? = get(102) as String?

    open var businessRoomCountVacant: Short?
        set(value): Unit = set(103, value)
        get(): Short? = get(103) as Short?

    open var longestVacancyPeriodBusiness: Int?
        set(value): Unit = set(104, value)
        get(): Int? = get(104) as Int?

    open var businessRoomCount: Short?
        set(value): Unit = set(105, value)
        get(): Short? = get(105) as Short?

    open var statusCode: String?
        set(value): Unit = set(106, value)
        get(): String? = get(106) as String?

    open var exclusionFlag: Byte?
        set(value): Unit = set(107, value)
        get(): Byte? = get(107) as Byte?

    open var collectionRepCd: String?
        set(value): Unit = set(108, value)
        get(): String? = get(108) as String?

    open var collectionRepName: String?
        set(value): Unit = set(109, value)
        get(): String? = get(109) as String?

    open var abcDivision: String?
        set(value): Unit = set(110, value)
        get(): String? = get(110) as String?

    open var moveOutMeetingTime: Short?
        set(value): Unit = set(111, value)
        get(): Short? = get(111) as Short?

    open var managementRepCd: String?
        set(value): Unit = set(112, value)
        get(): String? = get(112) as String?

    open var managementRepName: String?
        set(value): Unit = set(113, value)
        get(): String? = get(113) as String?

    open var supplyPlanAreaCode: String?
        set(value): Unit = set(114, value)
        get(): String? = get(114) as String?

    open var supplyPlanAreaName: String?
        set(value): Unit = set(115, value)
        get(): String? = get(115) as String?

    open var assessmentArea: String?
        set(value): Unit = set(116, value)
        get(): String? = get(116) as String?

    open var storeSalesDeptCd: String?
        set(value): Unit = set(117, value)
        get(): String? = get(117) as String?

    open var storeSalesDeptName: String?
        set(value): Unit = set(118, value)
        get(): String? = get(118) as String?

    open var storeSalesDeptOutputOrderCd: String?
        set(value): Unit = set(119, value)
        get(): String? = get(119) as String?

    open var storeCd: String?
        set(value): Unit = set(120, value)
        get(): String? = get(120) as String?

    open var storeName: String?
        set(value): Unit = set(121, value)
        get(): String? = get(121) as String?

    open var storeOutputOrderCd: String?
        set(value): Unit = set(122, value)
        get(): String? = get(122) as String?

    /**
     * Create a detached, initialised PropertyDetailFileExistingCommercialRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteSign: Byte? = null, occurrenceMonthDivision: String? = null, occurrenceMonth: Int? = null, salesDeptCd: String? = null, salesDeptName: String? = null, salesDeptOutputOrderCd: String? = null, branchCd: String? = null, branchName: String? = null, branchOutputOrderCd: String? = null, processDate: Int? = null, statusName: String? = null, customerFlag: Byte? = null, customerRepCd: String? = null, customerRepName: String? = null, occupancyFlag: Byte? = null, buildingCode: String? = null, buildingName: String? = null, addressCd: String? = null, location: String? = null, expectedCompletionDate: Int? = null, completionDate: Int? = null, roomCode: String? = null, roomNumber: String? = null, landlordCd: String? = null, landlordName: String? = null, bulkLeasingSign: Byte? = null, contractTypeName: String? = null, roomTypeCd: String? = null, roomTypeName: String? = null, layoutDivision: String? = null, layoutName: String? = null, exclusiveArea: BigDecimal? = null, tenantContractNumber: String? = null, currentStatusDivision: String? = null, modifiedStatusDivision: String? = null, vacationNoticeDate: Int? = null, reviewApprovalDate: Int? = null, reviewApprovalDaysElapsed: Int? = null, arrangementOutputDate: Int? = null, arrangementApprovalDate: Int? = null, arrangementCollectionDate: Int? = null, arrangementCollectionDaysElapsed: Int? = null, moveOutMeetingDate: Int? = null, expectedMoveOutDate: Int? = null, moveOutDate: Int? = null, moveOutRent: Int? = null, reviewRent: Int? = null, currentRent: Int? = null, differenceReviewMoveOut: Int? = null, differenceCurrentMoveOut: Int? = null, differenceCurrentReview: Int? = null, discrepancyStatus: String? = null, longestVacancyPeriodHistory: Int? = null, residentialRoomCount: Short? = null, residentialRoomCountVacant: Short? = null, restorationCompletionExpectedDate: Int? = null, restorationCompletionDate: Int? = null, vacancyAccountingExpectedDate: Int? = null, vacancyAccountingDate: Int? = null, vacancyMonths: Short? = null, vacancyPeriod: Int? = null, occupancyApplicationDate: Int? = null, contractDate: Int? = null, remainingCollectionExpectedDate: Int? = null, remainingCollectionDate: Int? = null, expectedOccupancyDate: Int? = null, occupancyDate: Int? = null, ad: Byte? = null, ff: Byte? = null, supportMechanismDivision: String? = null, preferredRentalDivision: String? = null, financingDivision: String? = null, hlApprovalDivision: String? = null, hl: String? = null, rentDiscountFlag: Byte? = null, rentDiscount: String? = null, reformType: String? = null, reform: Byte? = null, threeMonthFf: Byte? = null, recruitmentStatusDivision: String? = null, reserveDivision_2: String? = null, reserveDivision_3: String? = null, reserveDivision_4: String? = null, reserveDivision_5: String? = null, reserveDivision_6: String? = null, reserveDivision_7: String? = null, reserveDivision_8: String? = null, reserveDivision_9: String? = null, reserveDivision_10: String? = null, constructionRepCd: String? = null, constructionRepName: String? = null, implementationRepCd: String? = null, implementationRepName: String? = null, adActualAmount: Int? = null, ffActualAmount: Int? = null, municipalityCd: String? = null, municipalityName: String? = null, businessRoomCountVacant: Short? = null, longestVacancyPeriodBusiness: Int? = null, businessRoomCount: Short? = null, statusCode: String? = null, exclusionFlag: Byte? = null, collectionRepCd: String? = null, collectionRepName: String? = null, abcDivision: String? = null, moveOutMeetingTime: Short? = null, managementRepCd: String? = null, managementRepName: String? = null, supplyPlanAreaCode: String? = null, supplyPlanAreaName: String? = null, assessmentArea: String? = null, storeSalesDeptCd: String? = null, storeSalesDeptName: String? = null, storeSalesDeptOutputOrderCd: String? = null, storeCd: String? = null, storeName: String? = null, storeOutputOrderCd: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteSign = logicalDeleteSign
        this.occurrenceMonthDivision = occurrenceMonthDivision
        this.occurrenceMonth = occurrenceMonth
        this.salesDeptCd = salesDeptCd
        this.salesDeptName = salesDeptName
        this.salesDeptOutputOrderCd = salesDeptOutputOrderCd
        this.branchCd = branchCd
        this.branchName = branchName
        this.branchOutputOrderCd = branchOutputOrderCd
        this.processDate = processDate
        this.statusName = statusName
        this.customerFlag = customerFlag
        this.customerRepCd = customerRepCd
        this.customerRepName = customerRepName
        this.occupancyFlag = occupancyFlag
        this.buildingCode = buildingCode
        this.buildingName = buildingName
        this.addressCd = addressCd
        this.location = location
        this.expectedCompletionDate = expectedCompletionDate
        this.completionDate = completionDate
        this.roomCode = roomCode
        this.roomNumber = roomNumber
        this.landlordCd = landlordCd
        this.landlordName = landlordName
        this.bulkLeasingSign = bulkLeasingSign
        this.contractTypeName = contractTypeName
        this.roomTypeCd = roomTypeCd
        this.roomTypeName = roomTypeName
        this.layoutDivision = layoutDivision
        this.layoutName = layoutName
        this.exclusiveArea = exclusiveArea
        this.tenantContractNumber = tenantContractNumber
        this.currentStatusDivision = currentStatusDivision
        this.modifiedStatusDivision = modifiedStatusDivision
        this.vacationNoticeDate = vacationNoticeDate
        this.reviewApprovalDate = reviewApprovalDate
        this.reviewApprovalDaysElapsed = reviewApprovalDaysElapsed
        this.arrangementOutputDate = arrangementOutputDate
        this.arrangementApprovalDate = arrangementApprovalDate
        this.arrangementCollectionDate = arrangementCollectionDate
        this.arrangementCollectionDaysElapsed = arrangementCollectionDaysElapsed
        this.moveOutMeetingDate = moveOutMeetingDate
        this.expectedMoveOutDate = expectedMoveOutDate
        this.moveOutDate = moveOutDate
        this.moveOutRent = moveOutRent
        this.reviewRent = reviewRent
        this.currentRent = currentRent
        this.differenceReviewMoveOut = differenceReviewMoveOut
        this.differenceCurrentMoveOut = differenceCurrentMoveOut
        this.differenceCurrentReview = differenceCurrentReview
        this.discrepancyStatus = discrepancyStatus
        this.longestVacancyPeriodHistory = longestVacancyPeriodHistory
        this.residentialRoomCount = residentialRoomCount
        this.residentialRoomCountVacant = residentialRoomCountVacant
        this.restorationCompletionExpectedDate = restorationCompletionExpectedDate
        this.restorationCompletionDate = restorationCompletionDate
        this.vacancyAccountingExpectedDate = vacancyAccountingExpectedDate
        this.vacancyAccountingDate = vacancyAccountingDate
        this.vacancyMonths = vacancyMonths
        this.vacancyPeriod = vacancyPeriod
        this.occupancyApplicationDate = occupancyApplicationDate
        this.contractDate = contractDate
        this.remainingCollectionExpectedDate = remainingCollectionExpectedDate
        this.remainingCollectionDate = remainingCollectionDate
        this.expectedOccupancyDate = expectedOccupancyDate
        this.occupancyDate = occupancyDate
        this.ad = ad
        this.ff = ff
        this.supportMechanismDivision = supportMechanismDivision
        this.preferredRentalDivision = preferredRentalDivision
        this.financingDivision = financingDivision
        this.hlApprovalDivision = hlApprovalDivision
        this.hl = hl
        this.rentDiscountFlag = rentDiscountFlag
        this.rentDiscount = rentDiscount
        this.reformType = reformType
        this.reform = reform
        this.threeMonthFf = threeMonthFf
        this.recruitmentStatusDivision = recruitmentStatusDivision
        this.reserveDivision_2 = reserveDivision_2
        this.reserveDivision_3 = reserveDivision_3
        this.reserveDivision_4 = reserveDivision_4
        this.reserveDivision_5 = reserveDivision_5
        this.reserveDivision_6 = reserveDivision_6
        this.reserveDivision_7 = reserveDivision_7
        this.reserveDivision_8 = reserveDivision_8
        this.reserveDivision_9 = reserveDivision_9
        this.reserveDivision_10 = reserveDivision_10
        this.constructionRepCd = constructionRepCd
        this.constructionRepName = constructionRepName
        this.implementationRepCd = implementationRepCd
        this.implementationRepName = implementationRepName
        this.adActualAmount = adActualAmount
        this.ffActualAmount = ffActualAmount
        this.municipalityCd = municipalityCd
        this.municipalityName = municipalityName
        this.businessRoomCountVacant = businessRoomCountVacant
        this.longestVacancyPeriodBusiness = longestVacancyPeriodBusiness
        this.businessRoomCount = businessRoomCount
        this.statusCode = statusCode
        this.exclusionFlag = exclusionFlag
        this.collectionRepCd = collectionRepCd
        this.collectionRepName = collectionRepName
        this.abcDivision = abcDivision
        this.moveOutMeetingTime = moveOutMeetingTime
        this.managementRepCd = managementRepCd
        this.managementRepName = managementRepName
        this.supplyPlanAreaCode = supplyPlanAreaCode
        this.supplyPlanAreaName = supplyPlanAreaName
        this.assessmentArea = assessmentArea
        this.storeSalesDeptCd = storeSalesDeptCd
        this.storeSalesDeptName = storeSalesDeptName
        this.storeSalesDeptOutputOrderCd = storeSalesDeptOutputOrderCd
        this.storeCd = storeCd
        this.storeName = storeName
        this.storeOutputOrderCd = storeOutputOrderCd
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised PropertyDetailFileExistingCommercialRecord
     */
    constructor(value: PropertyDetailFileExistingCommercialPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteSign = value.logicalDeleteSign
            this.occurrenceMonthDivision = value.occurrenceMonthDivision
            this.occurrenceMonth = value.occurrenceMonth
            this.salesDeptCd = value.salesDeptCd
            this.salesDeptName = value.salesDeptName
            this.salesDeptOutputOrderCd = value.salesDeptOutputOrderCd
            this.branchCd = value.branchCd
            this.branchName = value.branchName
            this.branchOutputOrderCd = value.branchOutputOrderCd
            this.processDate = value.processDate
            this.statusName = value.statusName
            this.customerFlag = value.customerFlag
            this.customerRepCd = value.customerRepCd
            this.customerRepName = value.customerRepName
            this.occupancyFlag = value.occupancyFlag
            this.buildingCode = value.buildingCode
            this.buildingName = value.buildingName
            this.addressCd = value.addressCd
            this.location = value.location
            this.expectedCompletionDate = value.expectedCompletionDate
            this.completionDate = value.completionDate
            this.roomCode = value.roomCode
            this.roomNumber = value.roomNumber
            this.landlordCd = value.landlordCd
            this.landlordName = value.landlordName
            this.bulkLeasingSign = value.bulkLeasingSign
            this.contractTypeName = value.contractTypeName
            this.roomTypeCd = value.roomTypeCd
            this.roomTypeName = value.roomTypeName
            this.layoutDivision = value.layoutDivision
            this.layoutName = value.layoutName
            this.exclusiveArea = value.exclusiveArea
            this.tenantContractNumber = value.tenantContractNumber
            this.currentStatusDivision = value.currentStatusDivision
            this.modifiedStatusDivision = value.modifiedStatusDivision
            this.vacationNoticeDate = value.vacationNoticeDate
            this.reviewApprovalDate = value.reviewApprovalDate
            this.reviewApprovalDaysElapsed = value.reviewApprovalDaysElapsed
            this.arrangementOutputDate = value.arrangementOutputDate
            this.arrangementApprovalDate = value.arrangementApprovalDate
            this.arrangementCollectionDate = value.arrangementCollectionDate
            this.arrangementCollectionDaysElapsed = value.arrangementCollectionDaysElapsed
            this.moveOutMeetingDate = value.moveOutMeetingDate
            this.expectedMoveOutDate = value.expectedMoveOutDate
            this.moveOutDate = value.moveOutDate
            this.moveOutRent = value.moveOutRent
            this.reviewRent = value.reviewRent
            this.currentRent = value.currentRent
            this.differenceReviewMoveOut = value.differenceReviewMoveOut
            this.differenceCurrentMoveOut = value.differenceCurrentMoveOut
            this.differenceCurrentReview = value.differenceCurrentReview
            this.discrepancyStatus = value.discrepancyStatus
            this.longestVacancyPeriodHistory = value.longestVacancyPeriodHistory
            this.residentialRoomCount = value.residentialRoomCount
            this.residentialRoomCountVacant = value.residentialRoomCountVacant
            this.restorationCompletionExpectedDate = value.restorationCompletionExpectedDate
            this.restorationCompletionDate = value.restorationCompletionDate
            this.vacancyAccountingExpectedDate = value.vacancyAccountingExpectedDate
            this.vacancyAccountingDate = value.vacancyAccountingDate
            this.vacancyMonths = value.vacancyMonths
            this.vacancyPeriod = value.vacancyPeriod
            this.occupancyApplicationDate = value.occupancyApplicationDate
            this.contractDate = value.contractDate
            this.remainingCollectionExpectedDate = value.remainingCollectionExpectedDate
            this.remainingCollectionDate = value.remainingCollectionDate
            this.expectedOccupancyDate = value.expectedOccupancyDate
            this.occupancyDate = value.occupancyDate
            this.ad = value.ad
            this.ff = value.ff
            this.supportMechanismDivision = value.supportMechanismDivision
            this.preferredRentalDivision = value.preferredRentalDivision
            this.financingDivision = value.financingDivision
            this.hlApprovalDivision = value.hlApprovalDivision
            this.hl = value.hl
            this.rentDiscountFlag = value.rentDiscountFlag
            this.rentDiscount = value.rentDiscount
            this.reformType = value.reformType
            this.reform = value.reform
            this.threeMonthFf = value.threeMonthFf
            this.recruitmentStatusDivision = value.recruitmentStatusDivision
            this.reserveDivision_2 = value.reserveDivision_2
            this.reserveDivision_3 = value.reserveDivision_3
            this.reserveDivision_4 = value.reserveDivision_4
            this.reserveDivision_5 = value.reserveDivision_5
            this.reserveDivision_6 = value.reserveDivision_6
            this.reserveDivision_7 = value.reserveDivision_7
            this.reserveDivision_8 = value.reserveDivision_8
            this.reserveDivision_9 = value.reserveDivision_9
            this.reserveDivision_10 = value.reserveDivision_10
            this.constructionRepCd = value.constructionRepCd
            this.constructionRepName = value.constructionRepName
            this.implementationRepCd = value.implementationRepCd
            this.implementationRepName = value.implementationRepName
            this.adActualAmount = value.adActualAmount
            this.ffActualAmount = value.ffActualAmount
            this.municipalityCd = value.municipalityCd
            this.municipalityName = value.municipalityName
            this.businessRoomCountVacant = value.businessRoomCountVacant
            this.longestVacancyPeriodBusiness = value.longestVacancyPeriodBusiness
            this.businessRoomCount = value.businessRoomCount
            this.statusCode = value.statusCode
            this.exclusionFlag = value.exclusionFlag
            this.collectionRepCd = value.collectionRepCd
            this.collectionRepName = value.collectionRepName
            this.abcDivision = value.abcDivision
            this.moveOutMeetingTime = value.moveOutMeetingTime
            this.managementRepCd = value.managementRepCd
            this.managementRepName = value.managementRepName
            this.supplyPlanAreaCode = value.supplyPlanAreaCode
            this.supplyPlanAreaName = value.supplyPlanAreaName
            this.assessmentArea = value.assessmentArea
            this.storeSalesDeptCd = value.storeSalesDeptCd
            this.storeSalesDeptName = value.storeSalesDeptName
            this.storeSalesDeptOutputOrderCd = value.storeSalesDeptOutputOrderCd
            this.storeCd = value.storeCd
            this.storeName = value.storeName
            this.storeOutputOrderCd = value.storeOutputOrderCd
            resetChangedOnNotNull()
        }
    }
}
