package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.ParkingLot.Category.*
import jp.ne.simplex.application.model.ParkingVehicleInfo
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.db.jooq.gen.tables.references.PARKING_VEHICLE_INFO_FILE
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmmss
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import org.jooq.DSLContext
import org.jooq.Field
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class ParkingVehicleInfoRepository(private val context: DSLContext) :
    ParkingVehicleInfoRepositoryInterface {

    /** 駐車場車種情報更新 */
    override fun upsert(requestUser: AuthInfo.RequestUser, parameter: ParkingVehicleInfo) {

        // 駐車場区分
        val parkingLotCategory = parameter.parkingLotCategory

        // 車種ナンバー
        val vehicleNumber = parameter.vehicleNumber

        // 車種
        val vehicleCategory = parameter.vehicleCategory

        // 現在の日時を取得
        val currentDateTime = LocalDateTime.now()
        val currentDate = currentDateTime.yyyyMMdd().toInt()
        val currentTime = currentDateTime.HHmmss().toInt()

        // 新規登録のカラムと値をマップに格納
        val insertTargets = mapOf<Field<*>, Any?>(
            PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER to parameter.tenantContractNumber.value, // ERA05C
            PARKING_VEHICLE_INFO_FILE.CREATION_DATE to currentDate, // ERASTD
            PARKING_VEHICLE_INFO_FILE.CREATION_TIME to currentTime, // ERASTT
            PARKING_VEHICLE_INFO_FILE.CREATOR to requestUser.value, // ERASTS
            PARKING_VEHICLE_INFO_FILE.CREATION_PROGRAM_ID to null, // ERAPID
        )

        // 新規登録・更新共通のカラムと値をマップに格納
        val updateTargets = mapOf<Field<*>, Any?>(
            PARKING_VEHICLE_INFO_FILE.UPDATE_DATE to currentDate, // EME03D
            PARKING_VEHICLE_INFO_FILE.UPDATE_TIME to currentTime, // EME03T
            PARKING_VEHICLE_INFO_FILE.UPDATER to requestUser.value, // EME04C
            PARKING_VEHICLE_INFO_FILE.UPDATE_PROGRAM_ID to null, // EMEKNF
            PARKING_VEHICLE_INFO_FILE.ROOM_CODE to parameter.roomCd?.value,// ERA06C
            PARKING_VEHICLE_INFO_FILE.TANDEM_SIGN to parkingLotCategory?.resolveTandemSign(), // ERA07C
            PARKING_VEHICLE_INFO_FILE.LAND_TRANSPORT_NAME_1 to vehicleNumber.landTransportName, // ERA08C
            PARKING_VEHICLE_INFO_FILE.TYPE_1 to vehicleNumber.type, // ERA09C
            PARKING_VEHICLE_INFO_FILE.BUSINESS_CATEGORY_1 to vehicleNumber.businessCategory, // ERA10C
            PARKING_VEHICLE_INFO_FILE.LEFT_NUMBER_1 to vehicleNumber.leftNumber, // ERA11C
            PARKING_VEHICLE_INFO_FILE.RIGHT_NUMBER_1 to vehicleNumber.rightNumber, // ERA12C
            PARKING_VEHICLE_INFO_FILE.MANUFACTURER_DIVISION_1 to vehicleCategory.manufacturerDivision?.byte, // ERA13C
            PARKING_VEHICLE_INFO_FILE.CAR_MODEL_NAME_1 to vehicleCategory.carModelName, // ERA14C
            PARKING_VEHICLE_INFO_FILE.LIGHT_VEHICLE_SIGN_1 to parkingLotCategory?.resolveLightVehicleSign(), // ERA15C
            PARKING_VEHICLE_INFO_FILE.PARKING_CERT_ISSUE_SIGN_1 to parameter.parkingCertIssueSign?.byte, // ERA56S
            PARKING_VEHICLE_INFO_FILE.PARKING_CERT_COMMENT_1 to parameter.parkingCertComment, // ERA57X
        )

        // レガシーでは排他制御を行っていないため常にあと勝ちUPSERTとしている(EB800ParkingComBean)
        context.insertInto(PARKING_VEHICLE_INFO_FILE)
            .set(insertTargets)
            .set(updateTargets)
            .onDuplicateKeyUpdate()
            .set(updateTargets)
            .execute()
    }

    // 縦列サインに変換します
    private fun ParkingLot.Category.resolveTandemSign(): String {
        return if (this in listOf(PARALLEL, MULTI_LEVEL)) "1" else "0"
    }

    // 軽サインに変換します
    private fun ParkingLot.Category.resolveLightVehicleSign(): Byte {
        return if (this in listOf(KEI)) 1 else 0
    }
}

interface ParkingVehicleInfoRepositoryInterface {
    fun upsert(requestUser: AuthInfo.RequestUser, parameter: ParkingVehicleInfo)
}
