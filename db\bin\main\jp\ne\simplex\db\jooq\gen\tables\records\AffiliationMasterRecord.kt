/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.AffiliationMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.AffiliationMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 所属マスタ 既存システム物理名: JXB1MP
 */
@Suppress("UNCHECKED_CAST")
open class AffiliationMasterRecord private constructor() : TableRecordImpl<AffiliationMasterRecord>(AffiliationMasterTable.AFFILIATION_MASTER) {

    open var shozokuCode: String?
        set(value): Unit = set(0, value)
        get(): String? = get(0) as String?

    open var usageStartDate: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var usageEndDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var shozokuName: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var shozokuAbbrev1: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var shozokuAbbrev2: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var shozokuAbbrev3: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var shozokuAbbrev4: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var hierarchyDivision: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var companyCode: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var locationCode: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var upperShozokuCode: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var hqBranchDivision: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var aggregationDetailDivision: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var accountingSpecificDivision: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var costDepartmentDivision: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var hrShozokuOutputOrder: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var accountingShozokuOutputOrder: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var accountingShozokuCode: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var personnelCostCode: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var jobTypeCode: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var hrOutputOrder0: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var hrOutputOrder1: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var hrOutputOrder2: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var hrOutputOrder3: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var hrOutputOrder4: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var hrOutputOrder5: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var hrOutputOrder6: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var jurisdictionCode: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var deleteDivision: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var creator: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var creationProgram: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var creationDate: Int?
        set(value): Unit = set(32, value)
        get(): Int? = get(32) as Int?

    open var creationTime: Int?
        set(value): Unit = set(33, value)
        get(): Int? = get(33) as Int?

    open var updater: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var updateProgram: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var updateDate: Int?
        set(value): Unit = set(36, value)
        get(): Int? = get(36) as Int?

    open var updateTime: Int?
        set(value): Unit = set(37, value)
        get(): Int? = get(37) as Int?

    open var terminalId: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    /**
     * Create a detached, initialised AffiliationMasterRecord
     */
    constructor(shozokuCode: String? = null, usageStartDate: Int? = null, usageEndDate: Int? = null, shozokuName: String? = null, shozokuAbbrev1: String? = null, shozokuAbbrev2: String? = null, shozokuAbbrev3: String? = null, shozokuAbbrev4: String? = null, hierarchyDivision: String? = null, companyCode: String? = null, locationCode: String? = null, upperShozokuCode: String? = null, hqBranchDivision: String? = null, aggregationDetailDivision: String? = null, accountingSpecificDivision: String? = null, costDepartmentDivision: String? = null, hrShozokuOutputOrder: String? = null, accountingShozokuOutputOrder: String? = null, accountingShozokuCode: String? = null, personnelCostCode: String? = null, jobTypeCode: String? = null, hrOutputOrder0: String? = null, hrOutputOrder1: String? = null, hrOutputOrder2: String? = null, hrOutputOrder3: String? = null, hrOutputOrder4: String? = null, hrOutputOrder5: String? = null, hrOutputOrder6: String? = null, jurisdictionCode: String? = null, deleteDivision: String? = null, creator: String? = null, creationProgram: String? = null, creationDate: Int? = null, creationTime: Int? = null, updater: String? = null, updateProgram: String? = null, updateDate: Int? = null, updateTime: Int? = null, terminalId: String? = null): this() {
        this.shozokuCode = shozokuCode
        this.usageStartDate = usageStartDate
        this.usageEndDate = usageEndDate
        this.shozokuName = shozokuName
        this.shozokuAbbrev1 = shozokuAbbrev1
        this.shozokuAbbrev2 = shozokuAbbrev2
        this.shozokuAbbrev3 = shozokuAbbrev3
        this.shozokuAbbrev4 = shozokuAbbrev4
        this.hierarchyDivision = hierarchyDivision
        this.companyCode = companyCode
        this.locationCode = locationCode
        this.upperShozokuCode = upperShozokuCode
        this.hqBranchDivision = hqBranchDivision
        this.aggregationDetailDivision = aggregationDetailDivision
        this.accountingSpecificDivision = accountingSpecificDivision
        this.costDepartmentDivision = costDepartmentDivision
        this.hrShozokuOutputOrder = hrShozokuOutputOrder
        this.accountingShozokuOutputOrder = accountingShozokuOutputOrder
        this.accountingShozokuCode = accountingShozokuCode
        this.personnelCostCode = personnelCostCode
        this.jobTypeCode = jobTypeCode
        this.hrOutputOrder0 = hrOutputOrder0
        this.hrOutputOrder1 = hrOutputOrder1
        this.hrOutputOrder2 = hrOutputOrder2
        this.hrOutputOrder3 = hrOutputOrder3
        this.hrOutputOrder4 = hrOutputOrder4
        this.hrOutputOrder5 = hrOutputOrder5
        this.hrOutputOrder6 = hrOutputOrder6
        this.jurisdictionCode = jurisdictionCode
        this.deleteDivision = deleteDivision
        this.creator = creator
        this.creationProgram = creationProgram
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updater = updater
        this.updateProgram = updateProgram
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.terminalId = terminalId
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised AffiliationMasterRecord
     */
    constructor(value: AffiliationMasterPojo?): this() {
        if (value != null) {
            this.shozokuCode = value.shozokuCode
            this.usageStartDate = value.usageStartDate
            this.usageEndDate = value.usageEndDate
            this.shozokuName = value.shozokuName
            this.shozokuAbbrev1 = value.shozokuAbbrev1
            this.shozokuAbbrev2 = value.shozokuAbbrev2
            this.shozokuAbbrev3 = value.shozokuAbbrev3
            this.shozokuAbbrev4 = value.shozokuAbbrev4
            this.hierarchyDivision = value.hierarchyDivision
            this.companyCode = value.companyCode
            this.locationCode = value.locationCode
            this.upperShozokuCode = value.upperShozokuCode
            this.hqBranchDivision = value.hqBranchDivision
            this.aggregationDetailDivision = value.aggregationDetailDivision
            this.accountingSpecificDivision = value.accountingSpecificDivision
            this.costDepartmentDivision = value.costDepartmentDivision
            this.hrShozokuOutputOrder = value.hrShozokuOutputOrder
            this.accountingShozokuOutputOrder = value.accountingShozokuOutputOrder
            this.accountingShozokuCode = value.accountingShozokuCode
            this.personnelCostCode = value.personnelCostCode
            this.jobTypeCode = value.jobTypeCode
            this.hrOutputOrder0 = value.hrOutputOrder0
            this.hrOutputOrder1 = value.hrOutputOrder1
            this.hrOutputOrder2 = value.hrOutputOrder2
            this.hrOutputOrder3 = value.hrOutputOrder3
            this.hrOutputOrder4 = value.hrOutputOrder4
            this.hrOutputOrder5 = value.hrOutputOrder5
            this.hrOutputOrder6 = value.hrOutputOrder6
            this.jurisdictionCode = value.jurisdictionCode
            this.deleteDivision = value.deleteDivision
            this.creator = value.creator
            this.creationProgram = value.creationProgram
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updater = value.updater
            this.updateProgram = value.updateProgram
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.terminalId = value.terminalId
            resetChangedOnNotNull()
        }
    }
}
