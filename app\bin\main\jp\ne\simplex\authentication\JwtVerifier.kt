package jp.ne.simplex.authentication

import com.auth0.jwt.exceptions.JWTVerificationException
import com.auth0.jwt.exceptions.TokenExpiredException
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.shared.HttpServletRequestExtension.Companion.getJwtString
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class JwtVerifier(private val jwtAuthProcessor: JwtAuthProcessor) {

    companion object {
        private val log = LoggerFactory.getLogger(JwtVerifier::class.java)
    }

    fun verify(
        request: HttpServletRequest,
        response: HttpServletResponse,
    ) {
        val jwtString = request.getJwtString(AuthConfig.Jwt.COOKIE_ACCESS_TOKEN_KEY) ?: return
        try {
            val authInfo = jwtAuthProcessor.verify(AuthTokenType.ACCESS_TOKEN, jwtString)
            AuthSecurityContext.save(authInfo)
        } catch (ex: Exception) {
            AuthSecurityContext.clear()

            log.info(ex.message, ex)

            // NOTE: AuthFilter内のエラー内容をクライアントに返却するためのいい実装方法があれば修正したい。
            // HttpServletResponse.wrapUnauthorizedResponse（kotlin/jp/ne/simplex/configuration/WebApiSecurityConfig.kt l.79）
            // で、filter内で発生したExceptionをキャッチして、クライアントレスポンスを整形している。
            when (ex) {
                is TokenExpiredException -> request.setErrorAttribute(ErrorType.ACCESS_TOKEN_EXPIRED)
                is JWTVerificationException -> request.setErrorAttribute(ErrorType.AUTHENTICATION_FAILURE)
                else -> request.setErrorAttribute(ErrorType.UNEXPECTED_ERROR)
            }
        }
    }

    private fun HttpServletRequest.setErrorAttribute(errorType: ErrorType) {
        setAttribute("error_type", errorType)
    }
}
