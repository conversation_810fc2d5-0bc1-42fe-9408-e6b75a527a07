package jp.ne.simplex.application.repository.external.eboard.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.repository.external.eboard.config.EboardResponse
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.exception.ExternalApiServerException

class EBoardUpdateParkingReservationResponse(
    @JsonProperty("updateResult")
    val updateResult: String,
) : EboardResponse {

    fun throwIfReceivedError() {
        if (updateResult == "OK") {
            return
        }
        throw ExternalApiServerException(
            ErrorType.EBOARD_API_ERROR,
            ErrorMessage.EBOARD_RECEIVED_ERROR_RESPONSE.format("updateResult=${updateResult}")
        )
    }
}
