-- TABLE: BUILDING_11_DIGIT_ADDRESS(建物11桁住所)

CREATE TABLE BUILDING_11_DIGIT_ADDRESS(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(10)                   
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    ADDRESS_CD_11DIGIT                           varchar(11)                   
,    ADDRESS_DETAIL                               varchar(62)                   
,    ADDRESS_CD_11DIGIT_BASIC                     varchar(11)                   
,    ADDRESS_DETAIL_BASIC                         varchar(62)                   
,    CONSTRAINT PK_BUILDING_11_DIGIT_ADDRESS PRIMARY KEY (BUILDING_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BUILDING_11_DIGIT_ADDRESS IS '建物11桁住所 既存システム物理名: EAD11P';
COMMENT ON COLUMN BUILDING_11_DIGIT_ADDRESS.CREATION_DATE IS '作成年月日 既存システム物理名: EAD01D いい物件：データ連動用';
COMMENT ON COLUMN BUILDING_11_DIGIT_ADDRESS.CREATION_TIME IS '作成時刻 既存システム物理名: EAD02H いい物件：データ連動用';
COMMENT ON COLUMN BUILDING_11_DIGIT_ADDRESS.CREATOR IS '作成者 既存システム物理名: EAD03C DKWORKS：基本情報用';
COMMENT ON COLUMN BUILDING_11_DIGIT_ADDRESS.UPDATE_DATE IS '更新年月日 既存システム物理名: EAD04D DKWORKS：基本情報用';
COMMENT ON COLUMN BUILDING_11_DIGIT_ADDRESS.UPDATE_TIME IS '更新時刻 既存システム物理名: EAD05H';
COMMENT ON COLUMN BUILDING_11_DIGIT_ADDRESS.UPDATER IS '更新者 既存システム物理名: EAD06C';
COMMENT ON COLUMN BUILDING_11_DIGIT_ADDRESS.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EAD07N';
COMMENT ON COLUMN BUILDING_11_DIGIT_ADDRESS.BUILDING_CODE IS '建物コード 既存システム物理名: EAD08C';
COMMENT ON COLUMN BUILDING_11_DIGIT_ADDRESS.ADDRESS_CD_11DIGIT IS '11桁住所CD 既存システム物理名: EAD09C';
COMMENT ON COLUMN BUILDING_11_DIGIT_ADDRESS.ADDRESS_DETAIL IS '住所詳細 既存システム物理名: EAD10X';
COMMENT ON COLUMN BUILDING_11_DIGIT_ADDRESS.ADDRESS_CD_11DIGIT_BASIC IS '住所CD11桁(基本情報) 既存システム物理名: EAD11C';
COMMENT ON COLUMN BUILDING_11_DIGIT_ADDRESS.ADDRESS_DETAIL_BASIC IS '住所詳細(基本情報) 既存システム物理名: EAD12X';
