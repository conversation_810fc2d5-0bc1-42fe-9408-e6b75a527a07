/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.PropertyMaintenanceInfoTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.PropertyMaintenanceInfoPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 物件メンテナンス情報 既存システム物理名: EMEBMP
 */
@Suppress("UNCHECKED_CAST")
open class PropertyMaintenanceInfoRecord private constructor() : UpdatableRecordImpl<PropertyMaintenanceInfoRecord>(PropertyMaintenanceInfoTable.PROPERTY_MAINTENANCE_INFO) {

    open var buildingCd: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var roomCd: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var rentalPrice: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var securityDeposit: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var keyMoney: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var listingCategory: Byte?
        set(value): Unit = set(5, value)
        get(): Byte? = get(5) as Byte?

    open var listingCategoryGoodRoomNet: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var lowRepairCostSpecification: Byte?
        set(value): Unit = set(7, value)
        get(): Byte? = get(7) as Byte?

    open var creationDate: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var creationTime: Int?
        set(value): Unit = set(9, value)
        get(): Int? = get(9) as Int?

    open var updateDate: Int?
        set(value): Unit = set(10, value)
        get(): Int? = get(10) as Int?

    open var updateTime: Int?
        set(value): Unit = set(11, value)
        get(): Int? = get(11) as Int?

    open var updater: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var adAmount: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var comment: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var homesPanoramaSendFlag: Byte?
        set(value): Unit = set(15, value)
        get(): Byte? = get(15) as Byte?

    open var adUnit: Byte?
        set(value): Unit = set(16, value)
        get(): Byte? = get(16) as Byte?

    open var ffAmount: BigDecimal?
        set(value): Unit = set(17, value)
        get(): BigDecimal? = get(17) as BigDecimal?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised PropertyMaintenanceInfoRecord
     */
    constructor(buildingCd: String, roomCd: String, rentalPrice: Int? = null, securityDeposit: Int? = null, keyMoney: Int? = null, listingCategory: Byte? = null, listingCategoryGoodRoomNet: Byte? = null, lowRepairCostSpecification: Byte? = null, creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, adAmount: Int? = null, comment: String? = null, homesPanoramaSendFlag: Byte? = null, adUnit: Byte? = null, ffAmount: BigDecimal? = null): this() {
        this.buildingCd = buildingCd
        this.roomCd = roomCd
        this.rentalPrice = rentalPrice
        this.securityDeposit = securityDeposit
        this.keyMoney = keyMoney
        this.listingCategory = listingCategory
        this.listingCategoryGoodRoomNet = listingCategoryGoodRoomNet
        this.lowRepairCostSpecification = lowRepairCostSpecification
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.adAmount = adAmount
        this.comment = comment
        this.homesPanoramaSendFlag = homesPanoramaSendFlag
        this.adUnit = adUnit
        this.ffAmount = ffAmount
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised PropertyMaintenanceInfoRecord
     */
    constructor(value: PropertyMaintenanceInfoPojo?): this() {
        if (value != null) {
            this.buildingCd = value.buildingCd
            this.roomCd = value.roomCd
            this.rentalPrice = value.rentalPrice
            this.securityDeposit = value.securityDeposit
            this.keyMoney = value.keyMoney
            this.listingCategory = value.listingCategory
            this.listingCategoryGoodRoomNet = value.listingCategoryGoodRoomNet
            this.lowRepairCostSpecification = value.lowRepairCostSpecification
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.adAmount = value.adAmount
            this.comment = value.comment
            this.homesPanoramaSendFlag = value.homesPanoramaSendFlag
            this.adUnit = value.adUnit
            this.ffAmount = value.ffAmount
            resetChangedOnNotNull()
        }
    }
}
