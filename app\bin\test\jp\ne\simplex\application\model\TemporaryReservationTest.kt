package jp.ne.simplex.application.model

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe

class TemporaryReservationTest : FunSpec({

    context("仮押さえにおけるコメントは、空白除去及び、NULLの場合は空白に置き換えたものに変換されること") {
        test("prefixに空白が付与されている文字列は、空白が除去された結果をコメントとして扱われること") {
            TemporaryReservation.Comment.of(
                "  コメントの前に空白があります"
            ).value.shouldBe("コメントの前に空白があります")
        }

        test("suffixに空白が付与されている文字列は、空白が除去された結果をコメントとして扱われること") {
            TemporaryReservation.Comment.of(
                "コメントの後ろに空白があります  "
            ).value.shouldBe("コメントの後ろに空白があります")
        }

        test("prefix/suffixに空白が付与されている文字列は、空白が除去された結果をコメントとして扱われること") {
            TemporaryReservation.Comment.of(
                "  コメントの前後に空白があります  "
            ).value.shouldBe("コメントの前後に空白があります")
        }

        test("空白のみ文字列は、空文字として扱われること") {
            TemporaryReservation.Comment.of(
                "    "
            ).value.shouldBe("")
        }

        test("NULL は、空文字として扱われること") {
            TemporaryReservation.Comment.of(
                null
            ).value.shouldBe("")
        }
    }
})
