-- TABLE: ROOM_IMAGE(部屋画像)

CREATE TABLE ROOM_IMAGE(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_USER                                  varchar(10)                   
,    PROPERTY_BUILDING_CD                         varchar(9)        NOT NULL    
,    PROPERTY_ROOM_CD                             varchar(5)        NOT NULL    
,    IMAGE_REGISTRATION_COUNT                     numeric(2,0)                  
,    LATEST_IMAGE_REGISTRATION_DATE               numeric(8,0)                  
,    FLOOR_PLAN_IMAGE_FILE_NAME                   varchar(50)                   
,    FLOOR_PLAN_IMAGE_REGISTRATION_DATE           numeric(8,0)                  
,    ROOM_IMAGE_FILE_NAME_1                       varchar(50)                   
,    ROOM_IMAGE_TYPE_1                            varchar(2)                    
,    IMAGE_REGISTRATION_DATE_1                    numeric(8,0)                  
,    ROOM_IMAGE_FILE_NAME_2                       varchar(50)                   
,    ROOM_IMAGE_TYPE_2                            varchar(2)                    
,    IMAGE_REGISTRATION_DATE_2                    numeric(8,0)                  
,    ROOM_IMAGE_FILE_NAME_3                       varchar(50)                   
,    ROOM_IMAGE_TYPE_3                            varchar(2)                    
,    IMAGE_REGISTRATION_DATE_3                    numeric(8,0)                  
,    ROOM_IMAGE_FILE_NAME_4                       varchar(50)                   
,    ROOM_IMAGE_TYPE_4                            varchar(2)                    
,    IMAGE_REGISTRATION_DATE_4                    numeric(8,0)                  
,    ROOM_IMAGE_FILE_NAME_5                       varchar(50)                   
,    ROOM_IMAGE_TYPE_5                            varchar(2)                    
,    IMAGE_REGISTRATION_DATE_5                    numeric(8,0)                  
,    ROOM_IMAGE_FILE_NAME_6                       varchar(50)                   
,    ROOM_IMAGE_TYPE_6                            varchar(2)                    
,    IMAGE_REGISTRATION_DATE_6                    numeric(8,0)                  
,    ROOM_IMAGE_FILE_NAME_7                       varchar(50)                   
,    ROOM_IMAGE_TYPE_7                            varchar(2)                    
,    IMAGE_REGISTRATION_DATE_7                    numeric(8,0)                  
,    ROOM_IMAGE_FILE_NAME_8                       varchar(50)                   
,    ROOM_IMAGE_TYPE_8                            varchar(2)                    
,    IMAGE_REGISTRATION_DATE_8                    numeric(8,0)                  
,    ROOM_IMAGE_FILE_NAME_9                       varchar(50)                   
,    ROOM_IMAGE_TYPE_9                            varchar(2)                    
,    IMAGE_REGISTRATION_DATE_9                    numeric(8,0)                  
,    SEARCH_BRANCH_CD                             numeric(6,0)                  
,    ROOM_IMAGE_FILE_NAME_10                      varchar(50)                   
,    ROOM_IMAGE_TYPE_10                           varchar(2)                    
,    IMAGE_REGISTRATION_DATE_10                   numeric(8,0)                  
,    ROOM_IMAGE_FILE_NAME_11                      varchar(50)                   
,    ROOM_IMAGE_TYPE_11                           varchar(2)                    
,    IMAGE_REGISTRATION_DATE_11                   numeric(8,0)                  
,    ROOM_IMAGE_FILE_NAME_12                      varchar(50)                   
,    ROOM_IMAGE_TYPE_12                           varchar(2)                    
,    IMAGE_REGISTRATION_DATE_12                   numeric(8,0)                  
,    FLOOR_PLAN_IMAGE_REGISTRATION_TIME           numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_1                    numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_2                    numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_3                    numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_4                    numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_5                    numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_6                    numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_7                    numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_8                    numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_9                    numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_10                   numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_11                   numeric(6,0)                  
,    IMAGE_REGISTRATION_TIME_12                   numeric(6,0)                  
,    CONSTRAINT PK_ROOM_IMAGE PRIMARY KEY (PROPERTY_BUILDING_CD, PROPERTY_ROOM_CD)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE ROOM_IMAGE IS '部屋画像 既存システム物理名: ERAHMP';
COMMENT ON COLUMN ROOM_IMAGE.CREATION_DATE IS '作成年月日 既存システム物理名: ERA01D 社員番号を格納';
COMMENT ON COLUMN ROOM_IMAGE.CREATION_TIME IS '作成時刻 既存システム物理名: ERA02H';
COMMENT ON COLUMN ROOM_IMAGE.UPDATE_DATE IS '更新年月日 既存システム物理名: ERA03D';
COMMENT ON COLUMN ROOM_IMAGE.UPDATE_TIME IS '更新時刻 既存システム物理名: ERA04H';
COMMENT ON COLUMN ROOM_IMAGE.UPDATE_USER IS '更新ユーザ 既存システム物理名: ERA05C';
COMMENT ON COLUMN ROOM_IMAGE.PROPERTY_BUILDING_CD IS '物件建物CD 既存システム物理名: ERA06C';
COMMENT ON COLUMN ROOM_IMAGE.PROPERTY_ROOM_CD IS '物件部屋CD 既存システム物理名: ERA07C';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_COUNT IS '画像登録件数 既存システム物理名: ERAGTQ';
COMMENT ON COLUMN ROOM_IMAGE.LATEST_IMAGE_REGISTRATION_DATE IS '最新画像登録日 既存システム物理名: ERAGTD';
COMMENT ON COLUMN ROOM_IMAGE.FLOOR_PLAN_IMAGE_FILE_NAME IS '間取画像ファイル名 既存システム物理名: ERAMGM';
COMMENT ON COLUMN ROOM_IMAGE.FLOOR_PLAN_IMAGE_REGISTRATION_DATE IS '間取画像登録日 既存システム物理名: ERAMGD';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_FILE_NAME_1 IS '部屋画像ファイル名1 既存システム物理名: ERAF1M';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_TYPE_1 IS '部屋画像種別1 既存システム物理名: ERAS1B';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_DATE_1 IS '画像登録日1 既存システム物理名: ERAT1D';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_FILE_NAME_2 IS '部屋画像ファイル名2 既存システム物理名: ERAF2M';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_TYPE_2 IS '部屋画像種別2 既存システム物理名: ERAS2B';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_DATE_2 IS '画像登録日2 既存システム物理名: ERAT2D';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_FILE_NAME_3 IS '部屋画像ファイル名3 既存システム物理名: ERAF3M';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_TYPE_3 IS '部屋画像種別3 既存システム物理名: ERAS3B';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_DATE_3 IS '画像登録日3 既存システム物理名: ERAT3D';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_FILE_NAME_4 IS '部屋画像ファイル名4 既存システム物理名: ERAF4M';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_TYPE_4 IS '部屋画像種別4 既存システム物理名: ERAS4B';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_DATE_4 IS '画像登録日4 既存システム物理名: ERAT4D';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_FILE_NAME_5 IS '部屋画像ファイル名5 既存システム物理名: ERAF5M';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_TYPE_5 IS '部屋画像種別5 既存システム物理名: ERAS5B';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_DATE_5 IS '画像登録日5 既存システム物理名: ERAT5D';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_FILE_NAME_6 IS '部屋画像ファイル名6 既存システム物理名: ERAF6M';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_TYPE_6 IS '部屋画像種別6 既存システム物理名: ERAS6B';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_DATE_6 IS '画像登録日6 既存システム物理名: ERAT6D';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_FILE_NAME_7 IS '部屋画像ファイル名7 既存システム物理名: ERAF7M';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_TYPE_7 IS '部屋画像種別7 既存システム物理名: ERAS7B';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_DATE_7 IS '画像登録日7 既存システム物理名: ERAT7D';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_FILE_NAME_8 IS '部屋画像ファイル名8 既存システム物理名: ERAF8M';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_TYPE_8 IS '部屋画像種別8 既存システム物理名: ERAS8B';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_DATE_8 IS '画像登録日8 既存システム物理名: ERAT8D';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_FILE_NAME_9 IS '部屋画像ファイル名9 既存システム物理名: ERAF9M';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_TYPE_9 IS '部屋画像種別9 既存システム物理名: ERAS9B';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_DATE_9 IS '画像登録日9 既存システム物理名: ERAT9D';
COMMENT ON COLUMN ROOM_IMAGE.SEARCH_BRANCH_CD IS '検索用支店CD 既存システム物理名: ERASTC';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_FILE_NAME_10 IS '部屋画像ファイル名10 既存システム物理名: ERF10M';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_TYPE_10 IS '部屋画像種別10 既存システム物理名: ERS10B';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_DATE_10 IS '画像登録日10 既存システム物理名: ERT10D';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_FILE_NAME_11 IS '部屋画像ファイル名11 既存システム物理名: ERF11M';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_TYPE_11 IS '部屋画像種別11 既存システム物理名: ERS11B';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_DATE_11 IS '画像登録日11 既存システム物理名: ERT11D';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_FILE_NAME_12 IS '部屋画像ファイル名12 既存システム物理名: ERF12M';
COMMENT ON COLUMN ROOM_IMAGE.ROOM_IMAGE_TYPE_12 IS '部屋画像種別12 既存システム物理名: ERS12B';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_DATE_12 IS '画像登録日12 既存システム物理名: ERT12D';
COMMENT ON COLUMN ROOM_IMAGE.FLOOR_PLAN_IMAGE_REGISTRATION_TIME IS '間取画像登録時間 既存システム物理名: ERAMGH';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_TIME_1 IS '画像登録時間1 既存システム物理名: ERAT1H';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_TIME_2 IS '画像登録時間2 既存システム物理名: ERAT2H';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_TIME_3 IS '画像登録時間3 既存システム物理名: ERAT3H';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_TIME_4 IS '画像登録時間4 既存システム物理名: ERAT4H';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_TIME_5 IS '画像登録時間5 既存システム物理名: ERAT5H';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_TIME_6 IS '画像登録時間6 既存システム物理名: ERAT6H';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_TIME_7 IS '画像登録時間7 既存システム物理名: ERAT7H';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_TIME_8 IS '画像登録時間8 既存システム物理名: ERAT8H';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_TIME_9 IS '画像登録時間9 既存システム物理名: ERAT9H';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_TIME_10 IS '画像登録時間10 既存システム物理名: ERT10H';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_TIME_11 IS '画像登録時間11 既存システム物理名: ERT11H';
COMMENT ON COLUMN ROOM_IMAGE.IMAGE_REGISTRATION_TIME_12 IS '画像登録時間12 既存システム物理名: ERT12H';
