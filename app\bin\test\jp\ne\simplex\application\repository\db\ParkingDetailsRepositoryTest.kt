package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.ParkingVehicleInfo
import jp.ne.simplex.application.repository.db.pojos.VacancyParkingLotTargetPojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.DaitoBulkLeaseContractDetailsPojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.DkLinkControlPojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingAdditionalInfoMasterPojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.TenantContractBulkCollectionFilePojo
import jp.ne.simplex.db.jooq.gen.tables.records.DaitoBulkLeaseContractDetailsRecord
import jp.ne.simplex.db.jooq.gen.tables.records.DkLinkControlRecord
import jp.ne.simplex.db.jooq.gen.tables.records.ParkingAdditionalInfoMasterRecord
import jp.ne.simplex.db.jooq.gen.tables.records.TenantContractBulkCollectionFileRecord
import jp.ne.simplex.db.jooq.gen.tables.references.*
import jp.ne.simplex.mock.MockParkingRepository
import jp.ne.simplex.mock.MockParkingReservationRepository
import jp.ne.simplex.stub.*
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import kotlin.test.assertEquals
import kotlin.test.assertNull

class ParkingDetailsRepositoryTest : AbstractTestContainerTest() {

    companion object {
        private val log = LoggerFactory.getLogger(ParkingDetailsRepositoryTest::class.java)
        private const val BUILDING_CODE = "000024301"
        private const val BUILDING_CODE_2 = "000024302"
        private const val BUILDING_CODE_3 = "000024303"
        private const val PARKING_LOT_CODE = "701"
        private const val PARKING_LOT_CODE_2 = "702"
    }

    private lateinit var repository: ParkingDetailsRepository

    override fun beforeEach() {
        repository = ParkingDetailsRepository(
            dslContext,
            MockParkingRepository(), MockParkingReservationRepository()
        )
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(
            PARKING,
            BUILDING_MASTER,
            PARKING_VEHICLE_INFO_FILE,
            BULK_LEASE_PARKING,
            OFF_SITE_PARKING,
            DAITO_BULK_LEASE_CONTRACT_DETAILS,
            CONTRACT_FILE,
            TENANT,
            TENANT_CONTRACT,
            ADDRESS_MASTER,
            CUSTOMER,
            BUILDING_INFO_MASTER,
            PARKING_INFO_MASTER,
            PARKING_ENABLE,
            PARKING_ADDITIONAL_INFO_MASTER,
            DK_LINK_CONTROL,
            ROOM_MASTER,
            LATEST_RENT_EVALUATION,
            TENANT_CONTRACT_BULK_COLLECTION_FILE
        )
    }

    @Nested
    @DisplayName("駐車場詳細検索の検証")
    inner class Scenario1 {

        @BeforeEach
        fun setup() {
            dslContext.save(
                table = DK_LINK_CONTROL,
                recordConstructor = { p: DkLinkControlPojo -> DkLinkControlRecord(p) },
                pojos = listOf(
                    DkLinkControlPojo(
                        key = "ALLOW_ALL_PARKING_LOT_AVAILABILITY_EDIT",
                        value = "1",
                    )
                )
            )

            dslContext.saveParkingPojo(
                stubParkingPojo(
                    buildingCode = BUILDING_CODE,
                    parkingLotCode = PARKING_LOT_CODE,
                    parkingCategory = "1",
                    bulkLeaseFlag = 4,
                    offSiteParkingCategory = "0",
                    logicalDeleteFlag = 0,
                    parkingLotNumber = "1",
                    consolidatedBuildingCode = "*********",
                    consolidatedParkingCode = "999",
                    transferredBuildingCode = "000099801",
                ),
                stubParkingPojo(
                    buildingCode = BUILDING_CODE,
                    parkingLotCode = PARKING_LOT_CODE_2,
                    parkingCategory = "3",
                    bulkLeaseFlag = 7,
                    offSiteParkingCategory = "1",
                    logicalDeleteFlag = 0,
                    parkingLotNumber = "2",
                ),
                stubParkingPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingLotCode = "101",
                    logicalDeleteFlag = 1,
                    consolidatedBuildingCode = "000024303",
                ),
            )
            dslContext.saveBuildingMasterPojo(
                stubBuildingMasterPojo(
                    buildingCode = BUILDING_CODE,
                    buildingName = "テスト建物名1",
                    bulkLeaseFlag = 1,
                    prefectureCode = "01",
                    cityCode = "00",
                    townCode = "000000",
                    addressDetail = "テスト建物1の所在地",
                    landlordCode = "********",
                    completionDeliveryDate = 20250101,
                    daikenBranchCode = "990000",
                ),
                stubBuildingMasterPojo(
                    buildingCode = BUILDING_CODE_2,
                    buildingName = "テスト建物名2",
                    addressDetail = "テスト建物2の所在地",
                ),
            )
            dslContext.saveAddressMasterPojo(
                stubAddressMasterPojo(
                    addressCode = "0100000000",
                    postalCode = "1234567",
                    prefectureKanjiName = "東京都",
                    cityKanjiName = "世田谷区",
                    townKanjiName = "三軒茶屋",
                ),
            )
            dslContext.saveCustomerPojo(
                stubCustomerPojo(
                    integratedClientCode = "********00",
                    clientNameKanji = "家主名",
                ),
            )
            dslContext.saveTenantContractPojo(
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = PARKING_LOT_CODE,
                    contractNumber = "10000001",
                    roomCode = "01010",
                    tenantName = "駐車場入居者",
                    tenantCode = "B02483019",
                    currentStateDivision = "00",
                    modificationStateDivision = "10",
                    cancellationSign = 0,
                    moveInStartProcessedSign = 1,
                    moveOutDate = 20260304,
                    moveInScheduledDate = 20250102,
                    contractExpiryDate = 20240102,
                    contractEffectiveEndDate = 20240102,
                    vacateScheduledDate = 20240102,
                    vacateNoticeDate = 0,
                    aggregateContractNumber = "10000021",
                    logicalDeleteSign = 0
                ),
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = PARKING_LOT_CODE,
                    contractNumber = "10000011",
                    currentStateDivision = "00",
                    modificationStateDivision = "10",
                    cancellationSign = 0,
                    moveInStartProcessedSign = 1,
                    moveOutDate = 0,
                    logicalDeleteSign = 0,
                    contractEffectiveStartDate = 20250604
                ),
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = PARKING_LOT_CODE_2,
                    contractNumber = "10000002",
                    currentStateDivision = "12",
                    modificationStateDivision = "34",
                    cancellationSign = 1,
                    moveInStartProcessedSign = 0,
                    moveOutDate = 0,
                    logicalDeleteSign = 0
                )
            )
            dslContext.saveTenantPojo(
                stubTenantPojo(
                    tenantCode = "B02483010",
                    tenantNameKanji = "駐車場契約者",
                ),
            )
            dslContext.saveParkingVehicleInfoFilePojo(
                stubParkingVehicleInfoFilePojo(
                    tenantContractNumber = "10000001",
                    landTransportName = "世田谷",
                    type = "330",
                    businessCategory = "い",
                    leftNumber = "12",
                    rightNumber = "34",
                    manufacturerDivision = ParkingVehicleInfo.Category.ManufacturerDivision.TOYOTA,
                    carModelName = "レクサス",
                    lightVehicleSign = "1",
                    parkingCertIssueSign = ParkingVehicleInfo.ParkingCertIssueSign.ISSUED.byte.toString(),
                    parkingCertComment = "発給あり",
                    tandemSign = "1",
                ),
            )
            dslContext.saveBuildingInfoMasterPojo(
                stubBuildingInfoMasterPojo(
                    buildingCode = BUILDING_CODE,
                    marketingBranchOfficeCd = "101",
                ),
            )
            dslContext.saveParkingInfoMasterPojo(
                stubParkingInfoMasterPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = PARKING_LOT_CODE,
                    localDisplayNumber = "1",
                    currentParkingStatus = "27",
                    fixedParkingStatus = "10",
                    specialContractFlag = 0,
                    brokerApplicationPossibility = "1",
                    logicalDeleteFlag = 0
                ),
                stubParkingInfoMasterPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = PARKING_LOT_CODE_2,
                    localDisplayNumber = "2",
                    currentParkingStatus = "12",
                    fixedParkingStatus = "34",
                    specialContractFlag = 1,
                    brokerApplicationPossibility = "0",
                ),
            )
            dslContext.saveParkingEnablePojo(
                stubParkingEnablePojo(
                    buildingCode = BUILDING_CODE,
                    parkingLotCode = PARKING_LOT_CODE,
                    parkingLotEnable = "1",
                    deleteFlag = "0",
                ),
                stubParkingEnablePojo(
                    buildingCode = BUILDING_CODE,
                    parkingLotCode = PARKING_LOT_CODE_2,
                    parkingLotEnable = "0",
                    deleteFlag = "0",
                ),
            )
            dslContext.save(
                table = PARKING_ADDITIONAL_INFO_MASTER,
                recordConstructor = { p: ParkingAdditionalInfoMasterPojo ->
                    ParkingAdditionalInfoMasterRecord(p)
                },
                pojos = listOf(
                    ParkingAdditionalInfoMasterPojo(
                        buildingCode = BUILDING_CODE,
                        parkingCode = PARKING_LOT_CODE,
                        roomBuildingCode = "*********",
                        roomRoomCode = "09090",
                    )
                )
            )
            dslContext.saveRoomMasterPojo(
                stubRoomMasterPojo(
                    buildingCode = "*********",
                    roomCode = "09090",
                    roomNumber = "0909",
                ),
            )
            dslContext.saveLatestRentEvaluationPojo(
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "00000001",
                    latestRentAssessmentHistory = "00",
                    buildingCode = BUILDING_CODE,
                    propertyCode = PARKING_LOT_CODE,
                    roomParkingDivision = "2",
                    keyMoneyAmount = 10000,
                    depositAmount = 20000,
                    parkingFee = 25000,
                    taxDivision = "1",
                    keyMoneyInoutDivision = "1",
                    parkingFeeInoutDivision = "1",
                    brokerApplicationCollectionDivision = "1",
                    standardRentForCoop = 10001,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "00000002",
                    latestRentAssessmentHistory = "00",
                    buildingCode = BUILDING_CODE,
                    propertyCode = PARKING_LOT_CODE_2,
                    roomParkingDivision = "1",
                    parkingFee = 27000,
                ),
            )
            dslContext.saveBulkLeaseParkingPojo(
                stubBulkLeaseParkingPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = PARKING_LOT_CODE,
                    assessmentDivision = "1",
                    logicalDeleteSign = 0,
                ),
                stubBulkLeaseParkingPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = PARKING_LOT_CODE_2,
                    assessmentDivision = "2",
                ),
            )
            dslContext.saveOffSiteParkingPojo(
                stubOffSiteParkingPojo(
                    orderCd = "0000243",
                    parkingCd = PARKING_LOT_CODE,
                    distance = 300,
                    logicalDeleteSign = 0,
                ),
                stubOffSiteParkingPojo(
                    orderCd = "0000243",
                    parkingCd = PARKING_LOT_CODE_2,
                    distance = 250,
                ),
            )
            dslContext.save(
                table = DAITO_BULK_LEASE_CONTRACT_DETAILS,
                recordConstructor = { p: DaitoBulkLeaseContractDetailsPojo ->
                    DaitoBulkLeaseContractDetailsRecord(
                        p
                    )
                },
                pojos = listOf(
                    DaitoBulkLeaseContractDetailsPojo(
                        buildingCd = BUILDING_CODE,
                        thirtyFiveYearLumpSumCategory = 1,
                    )
                )
            )
        }

        @Test
        @DisplayName("駐車場詳細情報が0件取得されること")
        fun case01() {
            val results =
                repository.findParkingDetailPojoByOrderCode(Building.OrderCode.of("0000241"))

            assertEquals(0, results.size)
        }

        @Test
        @DisplayName("駐車場詳細情報が複数件取得されること")
        fun case02() {
            val results =
                repository.findParkingDetailPojoByOrderCode(Building.OrderCode.of("0000243"))

            log.info("results=$results")
            assertEquals(3, results.size)
            // 全項目アサーション
            assertEquals(BUILDING_CODE, results[0].buildingCode)
            assertEquals(PARKING_LOT_CODE, results[0].parkingLotCode)
            assertEquals("テスト建物名1", results[0].buildingName)
            assertEquals(1, results[0].buildingBulkLeaseFlag)
            assertEquals("1", results[0].buildingThirtyFiveYearLumpSumCategory)
            assertEquals("テスト建物1の所在地", results[0].addressDetail)
            assertEquals("10000001", results[0].tenantContractNumber)
            assertEquals("01010", results[0].roomCode)
            assertNull(results[0].propertyTenant)
            assertEquals("1", results[0].parkingLotNumber)
            assertEquals("世田谷", results[0].landTransportName)
            assertEquals("330", results[0].type)
            assertEquals("い", results[0].businessCategory)
            assertEquals("12", results[0].leftNumber)
            assertEquals("34", results[0].rightNumber)
            assertEquals(
                ParkingVehicleInfo.Category.ManufacturerDivision.TOYOTA.byte.toString(),
                results[0].manufacturerDivision
            )
            assertEquals("レクサス", results[0].carModelName)
            assertEquals("1", results[0].lightVehicleSign)
            assertEquals("駐車場入居者", results[0].tenantName)
            assertEquals(20250102, results[0].moveInScheduledDate)
            assertEquals("駐車場契約者", results[0].tenantNameKanji)
            assertEquals(20240102, results[0].contractExpiryDate)
            assertEquals(20240102, results[0].contractEffectiveEndDate)
            assertEquals("00", results[0].currentStateDivision)
            assertEquals("10", results[0].modificationStateDivision)
            assertEquals(1, results[0].moveInStartProcessedSign)
            assertEquals(20260304, results[0].moveOutDate)
            assertNull(results[0].expectedMoveOutDate)
            assertEquals(20240102, results[0].vacateScheduledDate)
            assertEquals("01", results[0].tou)
            assertEquals("1", results[0].tandemSign)
            assertEquals(0, results[0].cancellationSign)
            assertEquals(0, results[0].logicalDeleteFlag)
            assertEquals("*********", results[0].consolidatedBuildingCode)
            assertEquals("999", results[0].consolidatedParkingCode)
            assertEquals("000099801", results[0].transferredBuildingCode)
            assertEquals(0, results[0].logicalDeleteSign)
            assertEquals(0, results[0].vacateNoticeDate)
            assertEquals("1", results[0].assessmentDivision)
            assertEquals("1", results[0].parkingCertIssueSign)
            assertEquals("発給あり", results[0].parkingCertComment)
            assertEquals(4, results[0].bulkLeaseFlag)
            assertEquals("1", results[0].parkingCategory)
            assertEquals("10000021", results[0].aggregateContractNumber)
            assertEquals("1", results[0].thirtyFiveYearLumpSumCategory)
            assertEquals("0", results[0].offSiteParkingCategory)
            assertEquals(300, results[0].distance)
            assertEquals("1234567", results[0].postalCode)
            assertEquals("東京都", results[0].prefectureKanjiName)
            assertEquals("世田谷区", results[0].cityKanjiName)
            assertEquals("三軒茶屋", results[0].townKanjiName)
            assertEquals("家主名", results[0].clientNameKanji)
            assertEquals(20250101, results[0].completionDeliveryDate)
            assertEquals("101", results[0].businessOfficeCode)
            assertEquals("990", results[0].businessOfficeCode2)
            assertEquals(0, results[0].specialContractFlag)
            assertEquals("1", results[0].brokerApplicationPossibility)
            assertEquals("1", results[0].parkingLotEnable)
            assertEquals("1", results[0].allowAllParkingLotAvailabilityEdit)
            assertEquals(10000, results[0].keyMoneyAmount)
            assertEquals(20000, results[0].depositAmount)
            assertEquals(25000, results[0].parkingFee)
            assertEquals("1", results[0].brokerApplicationCollectionDivision)
            assertEquals("1", results[0].parkingFeeInTax)
            assertEquals("1", results[0].keyMoneyInTax)
            assertEquals(10001, results[0].standardRentForCoop)
            assertEquals("*********", results[0].roomBuildingCode)
            assertEquals("09090", results[0].roomRoomCode)
            assertEquals("0909", results[0].roomRoomNumber)

            assertEquals(BUILDING_CODE, results[1].buildingCode)
            assertEquals("テスト建物名1", results[1].buildingName)
            assertEquals("1234567", results[1].postalCode)
            assertEquals("テスト建物1の所在地", results[1].addressDetail)
            assertEquals("101", results[1].businessOfficeCode)
            assertEquals(PARKING_LOT_CODE_2, results[1].parkingLotCode)
            assertEquals("2", results[1].parkingLotNumber)
            assertEquals("3", results[1].parkingCategory)
            assertEquals("12", results[1].currentStateDivision)
            assertEquals("34", results[1].modificationStateDivision)
            assertEquals("10000002", results[1].tenantContractNumber)
            assertNull(results[1].roomCode)
            assertNull(results[1].propertyTenant)
            assertEquals(1, results[1].cancellationSign)
            assertEquals(0, results[1].moveInStartProcessedSign)
            assertEquals(0, results[1].moveOutDate)
            assertEquals("0", results[1].parkingLotEnable)
            assertNull(results[1].parkingFee)
            assertEquals("2", results[1].assessmentDivision)
            assertEquals(7, results[1].bulkLeaseFlag)
            assertEquals(1, results[1].specialContractFlag)
            assertEquals("0", results[1].brokerApplicationPossibility)
            assertEquals("1", results[1].offSiteParkingCategory)

            assertEquals(BUILDING_CODE_2, results[2].buildingCode)
            assertEquals("テスト建物名2", results[2].buildingName)
            assertNull(results[2].postalCode)
            assertEquals("テスト建物2の所在地", results[2].addressDetail)
            assertNull(results[2].businessOfficeCode)
            assertEquals("101", results[2].parkingLotCode)
            assertNull(results[2].parkingLotNumber)
            assertNull(results[2].parkingCategory)
            assertNull(results[2].currentStateDivision)
            assertNull(results[2].modificationStateDivision)
            assertNull(results[2].propertyTenant)
            assertNull(results[2].cancellationSign)
            assertNull(results[2].moveInStartProcessedSign)
            assertNull(results[2].moveOutDate)
            assertNull(results[2].parkingLotEnable)
            assertNull(results[2].parkingFee)
            assertNull(results[2].assessmentDivision)
            assertNull(results[2].bulkLeaseFlag)
            assertNull(results[2].specialContractFlag)
            assertNull(results[2].brokerApplicationPossibility)
            assertNull(results[2].offSiteParkingCategory)
            assertNull(results[2].roomBuildingCode)
        }

        @Test
        @DisplayName("複数棟あるが駐車場区画のない棟も取得できること")
        fun case03() {
            dslContext.saveBuildingMasterPojo(
                stubBuildingMasterPojo(
                    buildingCode = BUILDING_CODE_3,
                    buildingName = "テスト建物名3",
                    addressDetail = "テスト建物3の所在地",
                ),
            )
            val results =
                repository.findParkingDetailPojoByOrderCode(Building.OrderCode.of("0000243"))

            log.info("results=$results")
            assertEquals(4, results.size)
            assertEquals(BUILDING_CODE, results[0].buildingCode)
            assertEquals(PARKING_LOT_CODE, results[0].parkingLotCode)
            assertEquals(BUILDING_CODE, results[1].buildingCode)
            assertEquals(PARKING_LOT_CODE_2, results[1].parkingLotCode)
            assertEquals(BUILDING_CODE_2, results[2].buildingCode)
            assertEquals("101", results[2].parkingLotCode)
            assertEquals(BUILDING_CODE_3, results[3].buildingCode)
            assertNull(results[3].parkingLotCode) // 区画のない棟
        }
    }

    @Nested
    @DisplayName("入居前合算/前テナント契約情報取得の検証")
    inner class Scenario2 {
        @BeforeEach
        fun setup() {
            dslContext.saveTenantContractPojo(
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = PARKING_LOT_CODE,
                    roomCode = "02030",
                    contractNumber = "10000001",
                    tenantName = "駐車場入居者",
                    tenantCode = "B02483019",
                    currentStateDivision = "00",
                    modificationStateDivision = "10",
                    cancellationSign = 0,
                    moveInStartProcessedSign = 1,
                    moveOutDate = 20260304,
                    moveInScheduledDate = 20250102,
                    contractExpiryDate = 20240102,
                    contractEffectiveStartDate = 20230102,
                    contractEffectiveEndDate = 20240102,
                    vacateScheduledDate = 20240102,
                    vacateNoticeDate = 0,
                    aggregateContractNumber = "10000021",
                    logicalDeleteSign = 0
                ),
            )
            dslContext.saveTenantPojo(
                stubTenantPojo(
                    tenantCode = "B02483010",
                    tenantNameKanji = "駐車場契約者",
                ),
            )
            dslContext.saveRoomMasterPojo(
                stubRoomMasterPojo(
                    buildingCode = BUILDING_CODE,
                    roomCode = "02030",
                    roomNumber = "0203",
                ),
            )
            dslContext.saveParkingVehicleInfoFilePojo(
                stubParkingVehicleInfoFilePojo(
                    tenantContractNumber = "10000001",
                    landTransportName = "世田谷",
                    type = "330",
                    businessCategory = "い",
                    leftNumber = "12",
                    rightNumber = "34",
                    manufacturerDivision = ParkingVehicleInfo.Category.ManufacturerDivision.TOYOTA,
                    carModelName = "レクサス",
                    lightVehicleSign = "1",
                    parkingCertIssueSign = ParkingVehicleInfo.ParkingCertIssueSign.ISSUED.byte.toString(),
                    parkingCertComment = "発給あり",
                    tandemSign = "1",
                ),
            )
        }

        @Test
        @DisplayName("入居前合算テナント契約が1件取得できること")
        fun case01() {
            val tenantContractNumber = "10000001"
            val result =
                repository.findAggregateTenantContract(tenantContractNumber)

            log.info("result=$result")
            if (result == null) {
                throw AssertionError("入居前合算テナント契約が取得できませんでした。")
            }
            // 全項目アサーション
            assertEquals("駐車場入居者", result.tenantName)
            assertEquals(20250102, result.moveInScheduledDate)
            assertEquals("駐車場契約者", result.tenantNameKanji)
            assertEquals(20240102, result.contractExpiryDate)
            assertEquals(20240102, result.contractEffectiveEndDate)
            assertEquals("00", result.currentStateDivision)
            assertEquals("10", result.modificationStateDivision)
            assertEquals(1, result.moveInStartProcessedSign)
            assertEquals(20260304, result.moveOutDate)
            assertEquals(20240102, result.vacateScheduledDate)
            assertEquals(0, result.cancellationSign)
            assertEquals(0, result.vacateNoticeDate)
        }

        @Test
        @DisplayName("前テナント契約が1件取得できること")
        fun case02() {
            val tenantContractNumber = "10000001".toInt().plus(1).toString()
            val result =
                repository.findPreviousTenantContract(
                    Building.OrderCode.of("0000243"),
                    PARKING_LOT_CODE,
                    tenantContractNumber
                )

            log.info("result=$result")
            if (result == null) {
                throw AssertionError("前テナント契約が取得できませんでした。")
            }
            // 全項目アサーション
            assertEquals(20230102, result.contractEffectiveStartDate)
            assertEquals("10000001", result.tenantContractNumber)
            assertEquals("02030", result.roomCode)
            assertEquals("世田谷", result.landTransportName)
            assertEquals("330", result.type)
            assertEquals("い", result.businessCategory)
            assertEquals("12", result.leftNumber)
            assertEquals("34", result.rightNumber)
            assertEquals(
                ParkingVehicleInfo.Category.ManufacturerDivision.TOYOTA.byte.toString(),
                result.manufacturerDivision
            )
            assertEquals("レクサス", result.carModelName)
            assertEquals("1", result.lightVehicleSign)
            assertEquals("駐車場入居者", result.tenantName)
            assertEquals(20250102, result.moveInScheduledDate)
            assertEquals("駐車場契約者", result.tenantNameKanji)
            assertEquals(20240102, result.contractExpiryDate)
            assertEquals(20240102, result.contractEffectiveEndDate)
            assertEquals("00", result.currentStateDivision)
            assertEquals("10", result.modificationStateDivision)
            assertEquals(1, result.moveInStartProcessedSign)
            assertEquals(20260304, result.moveOutDate)
            assertEquals(20240102, result.vacateScheduledDate)
            assertEquals("1", result.tandemSign)
            assertEquals(0, result.cancellationSign)
            assertEquals(0, result.logicalDeleteSign)
            assertEquals(0, result.vacateNoticeDate)
            assertEquals("1", result.parkingCertIssueSign)
            assertEquals("発給あり", result.parkingCertComment)
            assertEquals("10000021", result.aggregateContractNumber)
        }

        @Test
        @DisplayName("親契約と部屋契約情報が取得できること")
        fun case03() {
            dslContext.save(
                table = TENANT_CONTRACT_BULK_COLLECTION_FILE,
                recordConstructor = { p: TenantContractBulkCollectionFilePojo ->
                    TenantContractBulkCollectionFileRecord(
                        p
                    )
                },
                pojos = listOf(
                    TenantContractBulkCollectionFilePojo(
                        tenantContractNumber = "20000001",
                        parkingTenantContract = "10000001",
                        parentTenantBuildingCd = "30000001",
                        parentTenantRoomCd = "03030",
                        deletionDate = 0
                    )
                )
            )
            dslContext.saveRoomMasterPojo(
                stubRoomMasterPojo(
                    buildingCode = "30000001",
                    roomCode = "03030",
                    roomNumber = "0303",
                ),
            )
            dslContext.saveTenantContractPojo(
                stubTenantContractPojo(
                    contractNumber = "20000001",
                    tenantName = "部屋入居者",
                    tenantCode = "B12483019",
                ),
            )
            dslContext.saveTenantPojo(
                stubTenantPojo(
                    tenantCode = "B12483010",
                    tenantNameKanji = "部屋契約者",
                ),
            )

            val tenantContractNumber = "10000001"
            val result =
                repository.findBulkTenantContract(tenantContractNumber)

            log.info("result=$result")
            if (result == null) {
                throw AssertionError("契約が取得できませんでした。")
            }
            // 全項目アサーション
            assertEquals("30000001", result.tenantBuildingCode)
            assertEquals("03030", result.tenantRoomCode)
            assertEquals("0303", result.tenantRoomNumber)
            assertEquals("部屋契約者", result.tenantNameKanji)
            assertEquals("部屋入居者", result.tenantName)
        }

        @Test
        @DisplayName("親契約と部屋契約情報は取得できないこと")
        fun case04() {
            val tenantContractNumber = "10000001"
            val result =
                repository.findBulkTenantContract(tenantContractNumber)

            log.info("result=$result")
            if (result == null) {
                throw AssertionError("契約が取得できませんでした。")
            }
            // 全項目アサーション
            assertEquals(BUILDING_CODE, result.tenantBuildingCode)
            assertEquals("02030", result.tenantRoomCode)
            assertEquals("0203", result.tenantRoomNumber)
            assertNull(result.tenantNameKanji)
            assertNull(result.tenantName)
        }
    }

    @Nested
    @DisplayName("ウェルカムパーク向けバッチ用駐車場詳細取得の検証")
    inner class Scenario3 {

        fun setup() {
            dslContext.saveParkingPojo(
                // 取れない
                stubParkingPojo(
                    buildingCode = "100000001",
                    parkingLotCode = "101",
                    parkingLotNumber = "1101",
                    parkingCategory = "1",
                    bulkLeaseFlag = 0,
                    logicalDeleteFlag = 0
                ),
                // 取れない
                stubParkingPojo(
                    buildingCode = "100000001",
                    parkingLotCode = "102",
                    parkingLotNumber = "1102",
                    parkingCategory = "1",
                    bulkLeaseFlag = 1,
                    logicalDeleteFlag = 0
                ),
                // 取れる
                stubParkingPojo(
                    buildingCode = "100000001",
                    parkingLotCode = "103",
                    parkingLotNumber = "1103",
                    parkingCategory = "1",
                    bulkLeaseFlag = 2,
                    logicalDeleteFlag = 0
                ),
                // 取れる
                stubParkingPojo(
                    buildingCode = "100000001",
                    parkingLotCode = "104",
                    parkingLotNumber = "1104",
                    parkingCategory = "1",
                    bulkLeaseFlag = 3,
                    logicalDeleteFlag = 0
                ),
                // 取れる
                stubParkingPojo(
                    buildingCode = "100000001",
                    parkingLotCode = "105",
                    parkingLotNumber = "1105",
                    parkingCategory = "1",
                    bulkLeaseFlag = 4,
                    logicalDeleteFlag = 0
                ),
                // 取れない
                stubParkingPojo(
                    buildingCode = "100000001",
                    parkingLotCode = "106",
                    parkingLotNumber = "1106",
                    parkingCategory = "1",
                    bulkLeaseFlag = 5,
                    logicalDeleteFlag = 0
                ),
                // 取れる
                stubParkingPojo(
                    buildingCode = "100000001",
                    parkingLotCode = "107",
                    parkingLotNumber = "1107",
                    parkingCategory = "1",
                    bulkLeaseFlag = 6,
                    logicalDeleteFlag = 0
                ),
                // 取れる
                stubParkingPojo(
                    buildingCode = "100000001",
                    parkingLotCode = "108",
                    parkingLotNumber = "1108",
                    parkingCategory = "1",
                    bulkLeaseFlag = 7,
                    logicalDeleteFlag = 0
                ),
                // 取れる
                stubParkingPojo(
                    buildingCode = "100000002",
                    parkingLotCode = "101",
                    parkingLotNumber = "2101",
                    parkingCategory = "1",
                    bulkLeaseFlag = 3,
                    logicalDeleteFlag = 0
                ),
                // 取れる
                stubParkingPojo(
                    buildingCode = "100000002",
                    parkingLotCode = "102",
                    parkingLotNumber = "2102",
                    parkingCategory = "2",
                    bulkLeaseFlag = 3,
                    logicalDeleteFlag = 0
                ),
                // 取れない
                stubParkingPojo(
                    buildingCode = "100000002",
                    parkingLotCode = "103",
                    parkingLotNumber = "2103",
                    parkingCategory = "3",
                    bulkLeaseFlag = 3,
                    logicalDeleteFlag = 0
                ),
                // 取れない
                stubParkingPojo(
                    buildingCode = "100000002",
                    parkingLotCode = "104",
                    parkingLotNumber = "2104",
                    parkingCategory = "4",
                    bulkLeaseFlag = 3,
                    logicalDeleteFlag = 0
                ),
                // 取れない
                stubParkingPojo(
                    buildingCode = "100000002",
                    parkingLotCode = "105",
                    parkingLotNumber = "2105",
                    parkingCategory = "5",
                    bulkLeaseFlag = 3,
                    logicalDeleteFlag = 0
                ),
                // 取れない
                stubParkingPojo(
                    buildingCode = "100000003",
                    parkingLotCode = "101",
                    parkingLotNumber = "3101",
                    parkingCategory = "1",
                    bulkLeaseFlag = 3,
                    logicalDeleteFlag = 0
                ),
                // 取れない
                stubParkingPojo(
                    buildingCode = "100000003",
                    parkingLotCode = "102",
                    parkingLotNumber = "3102",
                    parkingCategory = "1",
                    bulkLeaseFlag = 3,
                    logicalDeleteFlag = 0
                ),
                // 取れない
                stubParkingPojo(
                    buildingCode = "100000004",
                    parkingLotCode = "101",
                    parkingLotNumber = "4101",
                    parkingCategory = "1",
                    bulkLeaseFlag = 3,
                    tenantCategory = "0",
                    recruitmentFlag = 1,
                    logicalDeleteFlag = 0
                ),
                // 取れない
                stubParkingPojo(
                    buildingCode = "100000004",
                    parkingLotCode = "102",
                    parkingLotNumber = "4102",
                    parkingCategory = "1",
                    bulkLeaseFlag = 3,
                    tenantCategory = "1",
                    recruitmentFlag = 0,
                    logicalDeleteFlag = 0
                ),
                // 取れない
                stubParkingPojo(
                    buildingCode = "100000004",
                    parkingLotCode = "103",
                    parkingLotNumber = null,
                    parkingCategory = "1",
                    bulkLeaseFlag = 3,
                    tenantCategory = "1",
                    recruitmentFlag = 0,
                    logicalDeleteFlag = 0
                ),
                // 取れるけど例外発生
                stubParkingPojo(
                    buildingCode = "100000005",
                    parkingLotCode = "101",
                    parkingLotNumber = "5101",
                    parkingCategory = "0",
                    bulkLeaseFlag = 3,
                    tenantCategory = "1",
                    recruitmentFlag = 1,
                    logicalDeleteFlag = 0
                ),
            )
            dslContext.saveLatestRentEvaluationPojo(
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "10000101",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000001",
                    propertyCode = "101",
                    roomParkingDivision = "2",
                    parkingFee = 25000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "10000102",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000001",
                    propertyCode = "102",
                    roomParkingDivision = "2",
                    parkingFee = 27000,
                    brokerApplicationCollectionDate = 0 //斡旋回収前
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "10000103",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000001",
                    propertyCode = "103",
                    roomParkingDivision = "2",
                    parkingFee = 25000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "10000104",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000001",
                    propertyCode = "104",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "10000105",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000001",
                    propertyCode = "105",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "10000106",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000001",
                    propertyCode = "106",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "10000107",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000001",
                    propertyCode = "107",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "10000108",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000001",
                    propertyCode = "108",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "20000101",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000002",
                    propertyCode = "101",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "20000102",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000002",
                    propertyCode = "102",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "20000103",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000002",
                    propertyCode = "103",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "20000104",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000002",
                    propertyCode = "104",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "20000105",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000002",
                    propertyCode = "105",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "30000101",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000003",
                    propertyCode = "101",
                    roomParkingDivision = "2",
                    parkingFee = 0,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "30000102",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000003",
                    propertyCode = "102",
                    roomParkingDivision = "1",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "40000101",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000004",
                    propertyCode = "101",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "40000102",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000004",
                    propertyCode = "102",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "40000103",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000004",
                    propertyCode = "103",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "50000101",
                    latestRentAssessmentHistory = "00",
                    buildingCode = "100000005",
                    propertyCode = "101",
                    roomParkingDivision = "2",
                    parkingFee = 30000,
                ),
            )
            dslContext.saveParkingHourlyRentalApprovalPojo(
                stubParkingHourlyRentalApprovalPojo(
                    buildingCode = "100000001",
                    parkingTimeRentalConsentType = "1"
                ),
                stubParkingHourlyRentalApprovalPojo(
                    buildingCode = "100000002",
                    parkingTimeRentalConsentType = "1"
                ),
                stubParkingHourlyRentalApprovalPojo(
                    buildingCode = "100000003",
                    parkingTimeRentalConsentType = "1"
                ),
                stubParkingHourlyRentalApprovalPojo(
                    buildingCode = "100000004",
                    parkingTimeRentalConsentType = "1"
                ),
                stubParkingHourlyRentalApprovalPojo(
                    buildingCode = "100000005",
                    parkingTimeRentalConsentType = "1"
                ),
            )
            dslContext.saveBulkLeaseParkingPojo(
                stubBulkLeaseParkingPojo(
                    buildingCode = "100000001",
                    parkingCode = "103",
                    assessmentDivision = "1",
                    logicalDeleteSign = 0,
                ),
                stubBulkLeaseParkingPojo(
                    buildingCode = "100000001",
                    parkingCode = "104",
                    assessmentDivision = "2",
                ),
            )
        }

        @Test
        @DisplayName("駐車場が0件取得されること")
        fun case01() {
            val results =
                repository.findParkingDetailForWelcomeParkBatch(Building.OrderCode.of("0000000"))

            assertEquals(0, results.size)
        }

        @Test
        @DisplayName("駐車場が複数件取得されること")
        fun case02() {
            setup()
            val result = dslContext.selectFrom(PARKING)
                .fetchInto(VacancyParkingLotTargetPojo::class.java)
            log.info("ParkingLot=${result}")
            val results =
                repository.findParkingDetailForWelcomeParkBatch(Building.OrderCode.of("1000000"))

            assertEquals(5, results.size)
            assertEquals(18, results.fold(0) { acc, it -> acc + it.parkingLotList.size })

            assertEquals(
                ParkingLot.StatusDivision.VACANT,
                results[0].parkingLotList[0].parkingStatusDivision
            )
            // 斡旋回収前となること
            assertEquals(
                ParkingLot.StatusDivision.BEFORE_BROKER_COLLECTION,
                results[0].parkingLotList[1].parkingStatusDivision
            )
            // TODO : 取得したデータのアサーションを追加する
        }
    }
}
