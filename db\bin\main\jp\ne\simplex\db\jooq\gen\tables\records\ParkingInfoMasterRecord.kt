/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ParkingInfoMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingInfoMasterPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 駐車場情報DB 既存システム物理名: ECC80P
 */
@Suppress("UNCHECKED_CAST")
open class ParkingInfoMasterRecord private constructor() : UpdatableRecordImpl<ParkingInfoMasterRecord>(ParkingInfoMasterTable.PARKING_INFO_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteFlag: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var buildingCode: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var parkingCode: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    open var localDisplayNumber: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var parkingFeeBase: Int?
        set(value): Unit = set(10, value)
        get(): Int? = get(10) as Int?

    open var parkingFeeTax: Int?
        set(value): Unit = set(11, value)
        get(): Int? = get(11) as Int?

    open var parkingContractNumber: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var tenantCode: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var currentParkingStatus: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var fixedParkingStatus: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var assessmentDivision: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var aggregationDivision: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var specialContractFlag: Byte?
        set(value): Unit = set(18, value)
        get(): Byte? = get(18) as Byte?

    open var expectedMoveOutDate: Int?
        set(value): Unit = set(19, value)
        get(): Int? = get(19) as Int?

    open var moveInScheduledDate: Int?
        set(value): Unit = set(20, value)
        get(): Int? = get(20) as Int?

    open var managementFlag: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var brokerApplicationPossibility: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised ParkingInfoMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteFlag: Byte? = null, buildingCode: String, parkingCode: String, localDisplayNumber: String? = null, parkingFeeBase: Int? = null, parkingFeeTax: Int? = null, parkingContractNumber: String? = null, tenantCode: String? = null, currentParkingStatus: String? = null, fixedParkingStatus: String? = null, assessmentDivision: String? = null, aggregationDivision: String? = null, specialContractFlag: Byte? = null, expectedMoveOutDate: Int? = null, moveInScheduledDate: Int? = null, managementFlag: String? = null, brokerApplicationPossibility: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteFlag = logicalDeleteFlag
        this.buildingCode = buildingCode
        this.parkingCode = parkingCode
        this.localDisplayNumber = localDisplayNumber
        this.parkingFeeBase = parkingFeeBase
        this.parkingFeeTax = parkingFeeTax
        this.parkingContractNumber = parkingContractNumber
        this.tenantCode = tenantCode
        this.currentParkingStatus = currentParkingStatus
        this.fixedParkingStatus = fixedParkingStatus
        this.assessmentDivision = assessmentDivision
        this.aggregationDivision = aggregationDivision
        this.specialContractFlag = specialContractFlag
        this.expectedMoveOutDate = expectedMoveOutDate
        this.moveInScheduledDate = moveInScheduledDate
        this.managementFlag = managementFlag
        this.brokerApplicationPossibility = brokerApplicationPossibility
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ParkingInfoMasterRecord
     */
    constructor(value: ParkingInfoMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteFlag = value.logicalDeleteFlag
            this.buildingCode = value.buildingCode
            this.parkingCode = value.parkingCode
            this.localDisplayNumber = value.localDisplayNumber
            this.parkingFeeBase = value.parkingFeeBase
            this.parkingFeeTax = value.parkingFeeTax
            this.parkingContractNumber = value.parkingContractNumber
            this.tenantCode = value.tenantCode
            this.currentParkingStatus = value.currentParkingStatus
            this.fixedParkingStatus = value.fixedParkingStatus
            this.assessmentDivision = value.assessmentDivision
            this.aggregationDivision = value.aggregationDivision
            this.specialContractFlag = value.specialContractFlag
            this.expectedMoveOutDate = value.expectedMoveOutDate
            this.moveInScheduledDate = value.moveInScheduledDate
            this.managementFlag = value.managementFlag
            this.brokerApplicationPossibility = value.brokerApplicationPossibility
            resetChangedOnNotNull()
        }
    }
}
