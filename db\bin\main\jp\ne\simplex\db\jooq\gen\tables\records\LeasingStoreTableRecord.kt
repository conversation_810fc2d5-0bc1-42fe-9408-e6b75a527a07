/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.LeasingStoreTableTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.LeasingStoreTablePojo

import org.jooq.impl.TableRecordImpl


/**
 * リーシング店舗対応表 既存システム物理名: EMEBLP
 */
@Suppress("UNCHECKED_CAST")
open class LeasingStoreTableRecord private constructor() : TableRecordImpl<LeasingStoreTableRecord>(LeasingStoreTableTable.LEASING_STORE_TABLE) {

    open var creationDate: Long?
        set(value): Unit = set(0, value)
        get(): Long? = get(0) as Long?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Long?
        set(value): Unit = set(3, value)
        get(): Long? = get(3) as Long?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var branchCd: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var branchName: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var leasingStoreCd: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var leasingStoreName: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var startDate: Long?
        set(value): Unit = set(10, value)
        get(): Long? = get(10) as Long?

    open var endDate: Long?
        set(value): Unit = set(11, value)
        get(): Long? = get(11) as Long?

    open var leasingStoreCompanyName: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var leasingStorePhoneNumber: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var storeNorthOrder: Short?
        set(value): Unit = set(14, value)
        get(): Short? = get(14) as Short?

    /**
     * Create a detached, initialised LeasingStoreTableRecord
     */
    constructor(creationDate: Long? = null, creationTime: Int? = null, creator: String? = null, updateDate: Long? = null, updateTime: Int? = null, updater: String? = null, branchCd: String? = null, branchName: String? = null, leasingStoreCd: String? = null, leasingStoreName: String? = null, startDate: Long? = null, endDate: Long? = null, leasingStoreCompanyName: String? = null, leasingStorePhoneNumber: String? = null, storeNorthOrder: Short? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.branchCd = branchCd
        this.branchName = branchName
        this.leasingStoreCd = leasingStoreCd
        this.leasingStoreName = leasingStoreName
        this.startDate = startDate
        this.endDate = endDate
        this.leasingStoreCompanyName = leasingStoreCompanyName
        this.leasingStorePhoneNumber = leasingStorePhoneNumber
        this.storeNorthOrder = storeNorthOrder
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised LeasingStoreTableRecord
     */
    constructor(value: LeasingStoreTablePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.branchCd = value.branchCd
            this.branchName = value.branchName
            this.leasingStoreCd = value.leasingStoreCd
            this.leasingStoreName = value.leasingStoreName
            this.startDate = value.startDate
            this.endDate = value.endDate
            this.leasingStoreCompanyName = value.leasingStoreCompanyName
            this.leasingStorePhoneNumber = value.leasingStorePhoneNumber
            this.storeNorthOrder = value.storeNorthOrder
            resetChangedOnNotNull()
        }
    }
}
