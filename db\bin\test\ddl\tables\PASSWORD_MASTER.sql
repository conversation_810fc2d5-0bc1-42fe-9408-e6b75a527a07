-- TABLE: PASSWORD_MASTER(パスワードマスタ)

CREATE TABLE PASSWORD_MASTER(
     CREATE_DATE                                  numeric(8,0)                  
,    CREATE_TIME                                  numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATE_USER                                  varchar(10)                   
,    APPLY_DATE                                   numeric(8,0)                  
,    IS_DELETED                                   varchar(1)                    
,    EMPLOYEE_ID                                  varchar(6)        NOT NULL    
,    CURRENT_PASSWORD                             varchar(10)                   
,    PREV_PASSWORD                                varchar(10)                   
,    PREV_PREV_PASSWORD                           varchar(10)                   
,    SECURITY_CLASS                               varchar(1)                    
,    UNDEFINED                                    varchar(162)                  
,    CONSTRAINT PK_PASSWORD_MASTER PRIMARY KEY (EMPLOYEE_ID)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PASSWORD_MASTER IS 'パスワードマスタ 既存システム物理名: XSPASP';
COMMENT ON COLUMN PASSWORD_MASTER.CREATE_DATE IS '作成年月日 既存システム物理名: XSP01D';
COMMENT ON COLUMN PASSWORD_MASTER.CREATE_TIME IS '作成時刻 既存システム物理名: XSP02H';
COMMENT ON COLUMN PASSWORD_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: XSP03D';
COMMENT ON COLUMN PASSWORD_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: XSP04H';
COMMENT ON COLUMN PASSWORD_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: XSP05P';
COMMENT ON COLUMN PASSWORD_MASTER.UPDATE_USER IS '更新者 既存システム物理名: XSP06P';
COMMENT ON COLUMN PASSWORD_MASTER.APPLY_DATE IS '反映日付 既存システム物理名: XSP07D';
COMMENT ON COLUMN PASSWORD_MASTER.IS_DELETED IS '削除フラグ 既存システム物理名: XSP08S';
COMMENT ON COLUMN PASSWORD_MASTER.EMPLOYEE_ID IS '社員番号 既存システム物理名: XSP09C';
COMMENT ON COLUMN PASSWORD_MASTER.CURRENT_PASSWORD IS 'パスワード現在 既存システム物理名: XSP10X';
COMMENT ON COLUMN PASSWORD_MASTER.PREV_PASSWORD IS 'パスワード前回 既存システム物理名: XSP11X';
COMMENT ON COLUMN PASSWORD_MASTER.PREV_PREV_PASSWORD IS 'パスワード前々回 既存システム物理名: XSP12X';
COMMENT ON COLUMN PASSWORD_MASTER.SECURITY_CLASS IS 'セキュリティ・クラス 既存システム物理名: XSP13S';
COMMENT ON COLUMN PASSWORD_MASTER.UNDEFINED IS '未定義 既存システム物理名: XSP99X';
