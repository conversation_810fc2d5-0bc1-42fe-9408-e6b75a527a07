package jp.ne.simplex.exception

import jakarta.servlet.http.HttpServletRequest
import jp.ne.simplex.configuration.ApplicationConstants

sealed interface ErrorResponse {

    data class ClientErrorResponse(
        val code: String,
        val message: String,
        val detail: String? = null
    ) : ErrorResponse {
        companion object {
            fun of(errorType: ErrorType): ClientErrorResponse {
                return ClientErrorResponse(errorType.code, errorType.message)
            }

            fun of(e: BaseException): ClientErrorResponse {
                return ClientErrorResponse(e.type.code, e.type.message, e.detail.message)
            }
        }
    }

    class ExternalErrorResponse private constructor(
        val error: Messages
    ) : ErrorResponse {
        class Messages(val messages: List<String>)

        companion object {
            fun of(messages: String, detail: String? = null): ExternalErrorResponse {
                return ExternalErrorResponse(Messages(listOfNotNull(messages, detail)))
            }
        }
    }

    companion object {
        fun of(errorType: ErrorType, request: HttpServletRequest): ErrorResponse {
            return if (request.servletPath.startsWith(ApplicationConstants.EXTERNAL_SERVLET_PATH)) {
                ExternalErrorResponse.of(errorType.message)
            } else {
                ClientErrorResponse.of(errorType)
            }
        }

        fun of(e: BaseException, request: HttpServletRequest): ErrorResponse {
            return if (request.servletPath.startsWith(ApplicationConstants.EXTERNAL_SERVLET_PATH)) {
                ExternalErrorResponse.of(e.type.message, e.detail.message)
            } else {
                ClientErrorResponse.of(e)
            }
        }
    }
}
