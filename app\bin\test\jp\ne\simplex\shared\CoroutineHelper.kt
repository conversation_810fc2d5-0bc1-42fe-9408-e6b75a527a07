package jp.ne.simplex.shared

import kotlinx.coroutines.*

class CoroutineHelper {
    companion object {
        fun runAsyncTasks(
            task1: suspend CoroutineScope.() -> Unit,
            task2: suspend CoroutineScope.() -> Unit,
            delayBetweenTasks: Long = 0L
        ) {
            return runBlocking {

                // タスク1の非同期処理
                val task1Job = async(Dispatchers.Default) {
                    task1()
                }

                // タスク2の非同期処理（指定された遅延を挟む）
                val task2Job = async(Dispatchers.Default) {
                    delay(delayBetweenTasks).let { task2() }

                }

                // 両方のタスクが完了するまで待機
                listOf(task1Job, task2Job).awaitAll()
            }
        }
    }
}