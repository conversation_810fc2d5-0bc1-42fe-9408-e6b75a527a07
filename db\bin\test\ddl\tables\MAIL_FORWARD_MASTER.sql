-- TABLE: MAIL_FORWARD_MASTER(メール転送マスタ)

CREATE TABLE MAIL_FORWARD_MASTER(
     CREATION_DATE                                numeric(10,0)                 
,    CREATION_TIME                                numeric(8,0)                  
,    CREATOR                                      varchar(8)                    
,    BRANCH_CD                                    varchar(6)                    
,    EMPLOYEE_NUMBER                              varchar(6)                    
,    ADDRESS                                      varchar(60)                   
,    RECIPIENT_NAME                               varchar(42)                   
) TABLESPACE :TS_TBL;

COMMENT ON TABLE MAIL_FORWARD_MASTER IS 'メール転送マスタ 既存システム物理名: ERAMSP';
COMMENT ON COLUMN MAIL_FORWARD_MASTER.CREATION_DATE IS '作成日 既存システム物理名: ERA01D';
COMMENT ON COLUMN MAIL_FORWARD_MASTER.CREATION_TIME IS '作成時間 既存システム物理名: ERA02H';
COMMENT ON COLUMN MAIL_FORWARD_MASTER.CREATOR IS '作成者 既存システム物理名: ERA03H';
COMMENT ON COLUMN MAIL_FORWARD_MASTER.BRANCH_CD IS '支店CD 既存システム物理名: ERA04C';
COMMENT ON COLUMN MAIL_FORWARD_MASTER.EMPLOYEE_NUMBER IS '社員番号 既存システム物理名: ERA05C';
COMMENT ON COLUMN MAIL_FORWARD_MASTER.ADDRESS IS 'アドレス 既存システム物理名: ERA06C';
COMMENT ON COLUMN MAIL_FORWARD_MASTER.RECIPIENT_NAME IS '宛先名称 既存システム物理名: ERA07C';
