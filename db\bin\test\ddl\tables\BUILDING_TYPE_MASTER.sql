-- TABLE: BUILDING_TYPE_MASTER(建物種別マスタ)

CREATE TABLE BUILDING_TYPE_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    REFLECTION_DATE                              numeric(8,0)                  
,    DELETE_FLAG                                  varchar(1)                    
,    BUILDING_TYPE_CODE                           varchar(3)                    
,    NAME                                         varchar(22)                   
,    ABBREVIATION_1                               varchar(8)                    
,    ABBREVIATION_2                               varchar(12)                   
,    TYPE_CATEGORY                                varchar(2)                    
,    USAGE_CATEGORY                               varchar(1)                    
,    BUILDING_ACTUAL_SEQ                          varchar(3)                    
,    BUILDING_ACTUAL_CD                           varchar(2)                    
,    BUILDING_ACTUAL_NAME                         varchar(22)                   
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BUILDING_TYPE_MASTER IS '建物種別マスタ 既存システム物理名: XXHUSP';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: SAKUSEI_DT';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: SAKUSEI_TM';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: KOSHIN_DT';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: KOSHIN_TM';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: KOSHIN_PGM_ID';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.UPDATER IS '更新者 既存システム物理名: KOSHINSHA';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.REFLECTION_DATE IS '反映日付 既存システム物理名: HANEI_DT';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.DELETE_FLAG IS '削除フラグ 既存システム物理名: SAKUJO_FLG';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.BUILDING_TYPE_CODE IS '建物種別コード 既存システム物理名: TATEMONO_SHUBETSU_CD';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.NAME IS '名称 既存システム物理名: MEISHO';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.ABBREVIATION_1 IS '略称1 既存システム物理名: RYAKUSHO_1';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.ABBREVIATION_2 IS '略称2 既存システム物理名: RYAKUSHO_2';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.TYPE_CATEGORY IS '種類区分 既存システム物理名: SYURUI_KBN';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.USAGE_CATEGORY IS '用途区分 既存システム物理名: YOTO_KBN';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.BUILDING_ACTUAL_SEQ IS '建物実績SEQ 既存システム物理名: TATEMONO_JISSEKI_SEQ';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.BUILDING_ACTUAL_CD IS '建物実績CD 既存システム物理名: TATEMONO_JISSEKI_CD';
COMMENT ON COLUMN BUILDING_TYPE_MASTER.BUILDING_ACTUAL_NAME IS '建物実績名称 既存システム物理名: TATEMONO_JISSEKI_MEISHO';
