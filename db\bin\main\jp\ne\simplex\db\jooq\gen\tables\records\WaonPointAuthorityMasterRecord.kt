/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.WaonPointAuthorityMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.WaonPointAuthorityMasterPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * WAONポイント権限マスタ 既存システム物理名: EMWAMP
 */
@Suppress("UNCHECKED_CAST")
open class WaonPointAuthorityMasterRecord private constructor() : UpdatableRecordImpl<WaonPointAuthorityMasterRecord>(WaonPointAuthorityMasterTable.WAON_POINT_AUTHORITY_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var employeeNumber: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var effectiveStartDate: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var effectiveEndDate: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var applicationScope: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised WaonPointAuthorityMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, employeeNumber: String, effectiveStartDate: Int? = null, effectiveEndDate: Int? = null, applicationScope: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.employeeNumber = employeeNumber
        this.effectiveStartDate = effectiveStartDate
        this.effectiveEndDate = effectiveEndDate
        this.applicationScope = applicationScope
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised WaonPointAuthorityMasterRecord
     */
    constructor(value: WaonPointAuthorityMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.employeeNumber = value.employeeNumber
            this.effectiveStartDate = value.effectiveStartDate
            this.effectiveEndDate = value.effectiveEndDate
            this.applicationScope = value.applicationScope
            resetChangedOnNotNull()
        }
    }
}
