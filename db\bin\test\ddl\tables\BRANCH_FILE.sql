-- TABLE: BRANCH_FILE(支店ファイル)

CREATE TABLE BRANCH_FILE(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    LAST_UPDATE_PROGRAM                          varchar(10)                   
,    BRANCH_CODE                                  varchar(6)                    
,    BRANCH_NAME                                  varchar(42)                   
,    POSTAL_CODE                                  varchar(8)                    
,    PHONE_NUMBER                                 varchar(15)                   
,    FAX_NUMBER                                   varchar(15)                   
,    ADDRESS                                      varchar(210)                  
,    LICENSE_NUMBER                               varchar(50)                   
,    CONSTRAINT UQ_BRANCH_FILE UNIQUE (<PERSON><PERSON><PERSON>_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BRANCH_FILE IS '支店ファイル 既存システム物理名: EMUSIP';
COMMENT ON COLUMN BRANCH_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: EMU01D';
COMMENT ON COLUMN BRANCH_FILE.CREATION_TIME IS '作成時刻 既存システム物理名: EMU02H';
COMMENT ON COLUMN BRANCH_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: EMU03D';
COMMENT ON COLUMN BRANCH_FILE.UPDATE_TIME IS '更新時刻 既存システム物理名: EMU04H';
COMMENT ON COLUMN BRANCH_FILE.LAST_UPDATE_PROGRAM IS '最終更新PGM 既存システム物理名: EMU05N';
COMMENT ON COLUMN BRANCH_FILE.BRANCH_CODE IS '支店コード 既存システム物理名: EMU08C';
COMMENT ON COLUMN BRANCH_FILE.BRANCH_NAME IS '支店名称 既存システム物理名: EMU09M';
COMMENT ON COLUMN BRANCH_FILE.POSTAL_CODE IS '郵便番号 既存システム物理名: EMU10N';
COMMENT ON COLUMN BRANCH_FILE.PHONE_NUMBER IS '電話番号 既存システム物理名: EMU11N';
COMMENT ON COLUMN BRANCH_FILE.FAX_NUMBER IS 'FAX番号 既存システム物理名: EMU12N';
COMMENT ON COLUMN BRANCH_FILE.ADDRESS IS '住所 既存システム物理名: EMU13M';
COMMENT ON COLUMN BRANCH_FILE.LICENSE_NUMBER IS '免許番号 既存システム物理名: EMU14M';
