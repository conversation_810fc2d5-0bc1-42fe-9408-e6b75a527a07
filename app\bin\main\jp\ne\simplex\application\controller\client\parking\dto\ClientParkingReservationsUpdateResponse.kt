package jp.ne.simplex.application.controller.client.parking.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.ParkingReservation

data class ClientParkingReservationsUpdateResponse(
    @JsonProperty("failedRecords")
    @field:Schema(
        description = "駐車場予約更新失敗IDリスト",
        example = "[\"91c7e3d0-53ab-5f26-8f48-7794e2ba8150\", \"4e020f6d-0290-8f3c-e0dc-a29bced415d2\"]"
    )
    val failedIds: List<String>,
) {

    companion object {
        fun fromServiceInterface(value: List<ParkingReservation.Id>): ClientParkingReservationsUpdateResponse {
            return ClientParkingReservationsUpdateResponse(
                failedIds = value.map { it.value }
            )
        }
    }

}
