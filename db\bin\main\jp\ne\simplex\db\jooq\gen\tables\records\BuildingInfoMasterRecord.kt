/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.BuildingInfoMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingInfoMasterPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 建物情報マスタ 既存システム物理名: EMUR2P
 */
@Suppress("UNCHECKED_CAST")
open class BuildingInfoMasterRecord private constructor() : UpdatableRecordImpl<BuildingInfoMasterRecord>(BuildingInfoMasterTable.BUILDING_INFO_MASTER) {

    open var buildingCode: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var customerBranchCode: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    open var managementBranchCode: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var prefectureCode: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var cityCode: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var lineCode: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var stationCode: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var lineName: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var stationName: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var busStopName: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var busTime: Short?
        set(value): Unit = set(10, value)
        get(): Short? = get(10) as Short?

    open var walkingTime: Short?
        set(value): Unit = set(11, value)
        get(): Short? = get(11) as Short?

    open var distance: Short?
        set(value): Unit = set(12, value)
        get(): Short? = get(12) as Short?

    open var constructionYearMonth: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var location: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var totalFloors: Short?
        set(value): Unit = set(15, value)
        get(): Short? = get(15) as Short?

    open var maxUnitsPerFloor: Short?
        set(value): Unit = set(16, value)
        get(): Short? = get(16) as Short?

    open var transportation: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var notes: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var propertyName: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var structureName: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var postalCode: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var owner: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var gasCompanyName: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var gasCompanyContact: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var waterCompanyName: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var waterCompanyContact: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var electricityCompanyName: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var electricityCompanyContact: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var buildingEquipment: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var vacantRooms: Short?
        set(value): Unit = set(30, value)
        get(): Short? = get(30) as Short?

    open var vacantParkingSpots: Short?
        set(value): Unit = set(31, value)
        get(): Short? = get(31) as Short?

    open var minParkingFee: Int?
        set(value): Unit = set(32, value)
        get(): Int? = get(32) as Int?

    open var maxParkingFee: Int?
        set(value): Unit = set(33, value)
        get(): Int? = get(33) as Int?

    open var locationCity: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var waterSupplyType: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var waterMeterType: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var powerSupplyCompanyCode: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var electricityMeterType: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var gasType: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var gasMeterType: String?
        set(value): Unit = set(40, value)
        get(): String? = get(40) as String?

    open var toiletType: String?
        set(value): Unit = set(41, value)
        get(): String? = get(41) as String?

    open var powerPresence: Byte?
        set(value): Unit = set(42, value)
        get(): Byte? = get(42) as Byte?

    open var drainageType: String?
        set(value): Unit = set(43, value)
        get(): String? = get(43) as String?

    open var septicTankCalculation: Int?
        set(value): Unit = set(44, value)
        get(): Int? = get(44) as Int?

    open var telephoneType: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var buildingStructureType: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var roofType: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var urbanPlanningAreaType: String?
        set(value): Unit = set(48, value)
        get(): String? = get(48) as String?

    open var landUseType: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    open var fireProtectionAreaType: String?
        set(value): Unit = set(50, value)
        get(): String? = get(50) as String?

    open var plannedRoadSign: Byte?
        set(value): Unit = set(51, value)
        get(): Byte? = get(51) as Byte?

    open var projectPlanDecisionSign: Byte?
        set(value): Unit = set(52, value)
        get(): Byte? = get(52) as Byte?

    open var businessYear: Int?
        set(value): Unit = set(53, value)
        get(): Int? = get(53) as Int?

    open var buildingTypeCode: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var contractBranch: String?
        set(value): Unit = set(55, value)
        get(): String? = get(55) as String?

    open var constructionBranch: String?
        set(value): Unit = set(56, value)
        get(): String? = get(56) as String?

    open var buildingConfirmationDate: Int?
        set(value): Unit = set(57, value)
        get(): Int? = get(57) as Int?

    open var exteriorConstructionStartDate: Int?
        set(value): Unit = set(58, value)
        get(): Int? = get(58) as Int?

    open var governmentInspectionDate: Int?
        set(value): Unit = set(59, value)
        get(): Int? = get(59) as Int?

    open var governmentInspectionCompletionDate: Int?
        set(value): Unit = set(60, value)
        get(): Int? = get(60) as Int?

    open var internalInspectionDate: Int?
        set(value): Unit = set(61, value)
        get(): Int? = get(61) as Int?

    open var internalInspectionCompletionDate: Int?
        set(value): Unit = set(62, value)
        get(): Int? = get(62) as Int?

    open var finalInspectionDate: Int?
        set(value): Unit = set(63, value)
        get(): Int? = get(63) as Int?

    open var finalInspectionCompletionDate: Int?
        set(value): Unit = set(64, value)
        get(): Int? = get(64) as Int?

    open var expectedCompletionDate: Int?
        set(value): Unit = set(65, value)
        get(): Int? = get(65) as Int?

    open var completionDate: Int?
        set(value): Unit = set(66, value)
        get(): Int? = get(66) as Int?

    open var moveInDate: Int?
        set(value): Unit = set(67, value)
        get(): Int? = get(67) as Int?

    open var startDate: Int?
        set(value): Unit = set(68, value)
        get(): Int? = get(68) as Int?

    open var publicLoanSign: Byte?
        set(value): Unit = set(69, value)
        get(): Byte? = get(69) as Byte?

    open var fletsSupportCd: Byte?
        set(value): Unit = set(70, value)
        get(): Byte? = get(70) as Byte?

    open var skyPerfectTvSupportCd: Byte?
        set(value): Unit = set(71, value)
        get(): Byte? = get(71) as Byte?

    open var propertyAddress: String?
        set(value): Unit = set(72, value)
        get(): String? = get(72) as String?

    open var propertyAddressDetail: String?
        set(value): Unit = set(73, value)
        get(): String? = get(73) as String?

    open var onSiteVacantParkingSpots: Short?
        set(value): Unit = set(74, value)
        get(): Short? = get(74) as Short?

    open var flagReserve_1: Byte?
        set(value): Unit = set(75, value)
        get(): Byte? = get(75) as Byte?

    open var flagReserve_2: Byte?
        set(value): Unit = set(76, value)
        get(): Byte? = get(76) as Byte?

    open var flagReserve_3: Byte?
        set(value): Unit = set(77, value)
        get(): Byte? = get(77) as Byte?

    open var flagReserve_4: Byte?
        set(value): Unit = set(78, value)
        get(): Byte? = get(78) as Byte?

    open var flagReserve_5: Byte?
        set(value): Unit = set(79, value)
        get(): Byte? = get(79) as Byte?

    open var leasingStoreCd: String?
        set(value): Unit = set(80, value)
        get(): String? = get(80) as String?

    open var branchOfficeCd: String?
        set(value): Unit = set(81, value)
        get(): String? = get(81) as String?

    open var reviewBranchCd: String?
        set(value): Unit = set(82, value)
        get(): String? = get(82) as String?

    open var marketingBranchOfficeCd: String?
        set(value): Unit = set(83, value)
        get(): String? = get(83) as String?

    open var end_: String?
        set(value): Unit = set(84, value)
        get(): String? = get(84) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised BuildingInfoMasterRecord
     */
    constructor(buildingCode: String, customerBranchCode: String? = null, managementBranchCode: String? = null, prefectureCode: String? = null, cityCode: String? = null, lineCode: String? = null, stationCode: String? = null, lineName: String? = null, stationName: String? = null, busStopName: String? = null, busTime: Short? = null, walkingTime: Short? = null, distance: Short? = null, constructionYearMonth: Int? = null, location: String? = null, totalFloors: Short? = null, maxUnitsPerFloor: Short? = null, transportation: String? = null, notes: String? = null, propertyName: String? = null, structureName: String? = null, postalCode: String? = null, owner: String? = null, gasCompanyName: String? = null, gasCompanyContact: String? = null, waterCompanyName: String? = null, waterCompanyContact: String? = null, electricityCompanyName: String? = null, electricityCompanyContact: String? = null, buildingEquipment: String? = null, vacantRooms: Short? = null, vacantParkingSpots: Short? = null, minParkingFee: Int? = null, maxParkingFee: Int? = null, locationCity: String? = null, waterSupplyType: String? = null, waterMeterType: String? = null, powerSupplyCompanyCode: String? = null, electricityMeterType: String? = null, gasType: String? = null, gasMeterType: String? = null, toiletType: String? = null, powerPresence: Byte? = null, drainageType: String? = null, septicTankCalculation: Int? = null, telephoneType: String? = null, buildingStructureType: String? = null, roofType: String? = null, urbanPlanningAreaType: String? = null, landUseType: String? = null, fireProtectionAreaType: String? = null, plannedRoadSign: Byte? = null, projectPlanDecisionSign: Byte? = null, businessYear: Int? = null, buildingTypeCode: String? = null, contractBranch: String? = null, constructionBranch: String? = null, buildingConfirmationDate: Int? = null, exteriorConstructionStartDate: Int? = null, governmentInspectionDate: Int? = null, governmentInspectionCompletionDate: Int? = null, internalInspectionDate: Int? = null, internalInspectionCompletionDate: Int? = null, finalInspectionDate: Int? = null, finalInspectionCompletionDate: Int? = null, expectedCompletionDate: Int? = null, completionDate: Int? = null, moveInDate: Int? = null, startDate: Int? = null, publicLoanSign: Byte? = null, fletsSupportCd: Byte? = null, skyPerfectTvSupportCd: Byte? = null, propertyAddress: String? = null, propertyAddressDetail: String? = null, onSiteVacantParkingSpots: Short? = null, flagReserve_1: Byte? = null, flagReserve_2: Byte? = null, flagReserve_3: Byte? = null, flagReserve_4: Byte? = null, flagReserve_5: Byte? = null, leasingStoreCd: String? = null, branchOfficeCd: String? = null, reviewBranchCd: String? = null, marketingBranchOfficeCd: String? = null, end_: String? = null): this() {
        this.buildingCode = buildingCode
        this.customerBranchCode = customerBranchCode
        this.managementBranchCode = managementBranchCode
        this.prefectureCode = prefectureCode
        this.cityCode = cityCode
        this.lineCode = lineCode
        this.stationCode = stationCode
        this.lineName = lineName
        this.stationName = stationName
        this.busStopName = busStopName
        this.busTime = busTime
        this.walkingTime = walkingTime
        this.distance = distance
        this.constructionYearMonth = constructionYearMonth
        this.location = location
        this.totalFloors = totalFloors
        this.maxUnitsPerFloor = maxUnitsPerFloor
        this.transportation = transportation
        this.notes = notes
        this.propertyName = propertyName
        this.structureName = structureName
        this.postalCode = postalCode
        this.owner = owner
        this.gasCompanyName = gasCompanyName
        this.gasCompanyContact = gasCompanyContact
        this.waterCompanyName = waterCompanyName
        this.waterCompanyContact = waterCompanyContact
        this.electricityCompanyName = electricityCompanyName
        this.electricityCompanyContact = electricityCompanyContact
        this.buildingEquipment = buildingEquipment
        this.vacantRooms = vacantRooms
        this.vacantParkingSpots = vacantParkingSpots
        this.minParkingFee = minParkingFee
        this.maxParkingFee = maxParkingFee
        this.locationCity = locationCity
        this.waterSupplyType = waterSupplyType
        this.waterMeterType = waterMeterType
        this.powerSupplyCompanyCode = powerSupplyCompanyCode
        this.electricityMeterType = electricityMeterType
        this.gasType = gasType
        this.gasMeterType = gasMeterType
        this.toiletType = toiletType
        this.powerPresence = powerPresence
        this.drainageType = drainageType
        this.septicTankCalculation = septicTankCalculation
        this.telephoneType = telephoneType
        this.buildingStructureType = buildingStructureType
        this.roofType = roofType
        this.urbanPlanningAreaType = urbanPlanningAreaType
        this.landUseType = landUseType
        this.fireProtectionAreaType = fireProtectionAreaType
        this.plannedRoadSign = plannedRoadSign
        this.projectPlanDecisionSign = projectPlanDecisionSign
        this.businessYear = businessYear
        this.buildingTypeCode = buildingTypeCode
        this.contractBranch = contractBranch
        this.constructionBranch = constructionBranch
        this.buildingConfirmationDate = buildingConfirmationDate
        this.exteriorConstructionStartDate = exteriorConstructionStartDate
        this.governmentInspectionDate = governmentInspectionDate
        this.governmentInspectionCompletionDate = governmentInspectionCompletionDate
        this.internalInspectionDate = internalInspectionDate
        this.internalInspectionCompletionDate = internalInspectionCompletionDate
        this.finalInspectionDate = finalInspectionDate
        this.finalInspectionCompletionDate = finalInspectionCompletionDate
        this.expectedCompletionDate = expectedCompletionDate
        this.completionDate = completionDate
        this.moveInDate = moveInDate
        this.startDate = startDate
        this.publicLoanSign = publicLoanSign
        this.fletsSupportCd = fletsSupportCd
        this.skyPerfectTvSupportCd = skyPerfectTvSupportCd
        this.propertyAddress = propertyAddress
        this.propertyAddressDetail = propertyAddressDetail
        this.onSiteVacantParkingSpots = onSiteVacantParkingSpots
        this.flagReserve_1 = flagReserve_1
        this.flagReserve_2 = flagReserve_2
        this.flagReserve_3 = flagReserve_3
        this.flagReserve_4 = flagReserve_4
        this.flagReserve_5 = flagReserve_5
        this.leasingStoreCd = leasingStoreCd
        this.branchOfficeCd = branchOfficeCd
        this.reviewBranchCd = reviewBranchCd
        this.marketingBranchOfficeCd = marketingBranchOfficeCd
        this.end_ = end_
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BuildingInfoMasterRecord
     */
    constructor(value: BuildingInfoMasterPojo?): this() {
        if (value != null) {
            this.buildingCode = value.buildingCode
            this.customerBranchCode = value.customerBranchCode
            this.managementBranchCode = value.managementBranchCode
            this.prefectureCode = value.prefectureCode
            this.cityCode = value.cityCode
            this.lineCode = value.lineCode
            this.stationCode = value.stationCode
            this.lineName = value.lineName
            this.stationName = value.stationName
            this.busStopName = value.busStopName
            this.busTime = value.busTime
            this.walkingTime = value.walkingTime
            this.distance = value.distance
            this.constructionYearMonth = value.constructionYearMonth
            this.location = value.location
            this.totalFloors = value.totalFloors
            this.maxUnitsPerFloor = value.maxUnitsPerFloor
            this.transportation = value.transportation
            this.notes = value.notes
            this.propertyName = value.propertyName
            this.structureName = value.structureName
            this.postalCode = value.postalCode
            this.owner = value.owner
            this.gasCompanyName = value.gasCompanyName
            this.gasCompanyContact = value.gasCompanyContact
            this.waterCompanyName = value.waterCompanyName
            this.waterCompanyContact = value.waterCompanyContact
            this.electricityCompanyName = value.electricityCompanyName
            this.electricityCompanyContact = value.electricityCompanyContact
            this.buildingEquipment = value.buildingEquipment
            this.vacantRooms = value.vacantRooms
            this.vacantParkingSpots = value.vacantParkingSpots
            this.minParkingFee = value.minParkingFee
            this.maxParkingFee = value.maxParkingFee
            this.locationCity = value.locationCity
            this.waterSupplyType = value.waterSupplyType
            this.waterMeterType = value.waterMeterType
            this.powerSupplyCompanyCode = value.powerSupplyCompanyCode
            this.electricityMeterType = value.electricityMeterType
            this.gasType = value.gasType
            this.gasMeterType = value.gasMeterType
            this.toiletType = value.toiletType
            this.powerPresence = value.powerPresence
            this.drainageType = value.drainageType
            this.septicTankCalculation = value.septicTankCalculation
            this.telephoneType = value.telephoneType
            this.buildingStructureType = value.buildingStructureType
            this.roofType = value.roofType
            this.urbanPlanningAreaType = value.urbanPlanningAreaType
            this.landUseType = value.landUseType
            this.fireProtectionAreaType = value.fireProtectionAreaType
            this.plannedRoadSign = value.plannedRoadSign
            this.projectPlanDecisionSign = value.projectPlanDecisionSign
            this.businessYear = value.businessYear
            this.buildingTypeCode = value.buildingTypeCode
            this.contractBranch = value.contractBranch
            this.constructionBranch = value.constructionBranch
            this.buildingConfirmationDate = value.buildingConfirmationDate
            this.exteriorConstructionStartDate = value.exteriorConstructionStartDate
            this.governmentInspectionDate = value.governmentInspectionDate
            this.governmentInspectionCompletionDate = value.governmentInspectionCompletionDate
            this.internalInspectionDate = value.internalInspectionDate
            this.internalInspectionCompletionDate = value.internalInspectionCompletionDate
            this.finalInspectionDate = value.finalInspectionDate
            this.finalInspectionCompletionDate = value.finalInspectionCompletionDate
            this.expectedCompletionDate = value.expectedCompletionDate
            this.completionDate = value.completionDate
            this.moveInDate = value.moveInDate
            this.startDate = value.startDate
            this.publicLoanSign = value.publicLoanSign
            this.fletsSupportCd = value.fletsSupportCd
            this.skyPerfectTvSupportCd = value.skyPerfectTvSupportCd
            this.propertyAddress = value.propertyAddress
            this.propertyAddressDetail = value.propertyAddressDetail
            this.onSiteVacantParkingSpots = value.onSiteVacantParkingSpots
            this.flagReserve_1 = value.flagReserve_1
            this.flagReserve_2 = value.flagReserve_2
            this.flagReserve_3 = value.flagReserve_3
            this.flagReserve_4 = value.flagReserve_4
            this.flagReserve_5 = value.flagReserve_5
            this.leasingStoreCd = value.leasingStoreCd
            this.branchOfficeCd = value.branchOfficeCd
            this.reviewBranchCd = value.reviewBranchCd
            this.marketingBranchOfficeCd = value.marketingBranchOfficeCd
            this.end_ = value.end_
            resetChangedOnNotNull()
        }
    }
}
