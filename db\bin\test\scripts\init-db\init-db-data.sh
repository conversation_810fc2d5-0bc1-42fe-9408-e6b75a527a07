#!/bin/bash
#引数 $1:DB名, $2:ユーザ, $3:パスワード

DBNAME=$1
USER=$2
PASSWORD=$3

echo "/**************** ${DBNAME} 初期データ登録(Master) ****************/"
echo

# shellcheck disable=SC2164
cd "${DML_DIR}"
# shellcheck disable=SC2034
PGPASSWORD=${PASSWORD} # psql接続用
psql -U "${USER}" -h "${DBHOST}" -p "${DBPORT}" -d "${DBNAME}" -f insAllMaster.sql

echo "/**************** ${DBNAME} 初期データ登録(Initial) ****************/"
echo

psql -U "${USER}" -h "${DBHOST}" -p "${DBPORT}" -d "${DBNAME}" -f insAllInitial.sql

echo "/**************** ${DBNAME} 初期データ登録(Transaction) ****************/"
echo

psql -U "${USER}" -h "${DBHOST}" -p "${DBPORT}" -d "${DBNAME}" -f insAllTransaction.sql
