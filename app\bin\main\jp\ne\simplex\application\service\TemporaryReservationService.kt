package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.ForceUpdateTemporaryReservation.ForceCancelTemporaryReservation
import jp.ne.simplex.application.model.ForceUpdateTemporaryReservation.ForceRegisterTemporaryReservation
import jp.ne.simplex.application.repository.db.PropertyMaintenanceRepositoryInterface
import jp.ne.simplex.application.repository.db.PropertyRepositoryInterface
import jp.ne.simplex.application.repository.db.TemporaryReservationRepositoryInterface
import jp.ne.simplex.application.repository.external.eboard.EboardRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ServerValidationException
import org.jooq.DSLContext
import org.springframework.stereotype.Service

@Service
class TemporaryReservationService(
    private val context: DSLContext,
    private val repository: TemporaryReservationRepositoryInterface,
    private val eboardRepository: EboardRepositoryInterface,
    private val propertyRepository: PropertyRepositoryInterface,
    private val propertyMaintenanceRepository: PropertyMaintenanceRepositoryInterface
) {
    fun get(id: Property.Id): TemporaryReservationInfo? {
        return repository.findBy(id)
    }

    fun update(parameter: TemporaryReservation, requestUser: AuthInfo.RequestUser) {
        // 外部システムがデータの主管システムのため、外部からの強制更新リクエストの場合、物件の状態によらず更新を行う
        if (parameter is ForceUpdateTemporaryReservation) {
            return context.transaction { config ->
                when (parameter) {
                    is ForceRegisterTemporaryReservation -> {
                        repository.forceRegister(config, parameter)
                        updatePublishStatusWithStoreCurrentStatus(requestUser, parameter.getId())
                    }

                    is ForceCancelTemporaryReservation -> {
                        repository.forceCancel(config, parameter)
                        propertyMaintenanceRepository.updatePublishStatusFromOldStatus(
                            requestUser,
                            parameter.getId()
                        )
                    }
                }
            }
        }
        val propertyId = parameter.getId()
        val property = propertyRepository.list(listOf(propertyId)).firstOrNull()
            ?: throw ServerValidationException(
                ErrorMessage.PROPERTY_NOT_FOUND.format(
                    propertyId.buildingCode.value,
                    propertyId.roomCode.value
                )
            )

        // コメントのみ更新は、物件の状態によらず可能
        if (parameter is UpdateTemporaryReservationComment) {
            return context.transaction { config ->
                repository.updateComment(config, parameter).let {
                    eboardRepository.updateTemporaryReservationComment(parameter)
                }
            }
        }

        // コメントの更新以外の操作は、物件の状態が仮押さえ可能か確認する
        if (!property.isTemporaryReservationAllowed()) {
            throw ServerValidationException(ErrorMessage.TEMPORARY_RESERVATION_NOT_ALLOWED.format())
        }

        return context.transaction { config ->
            when (parameter) {
                is RegisterTemporaryReservation -> {
                    repository.register(config, parameter).let {
                        updatePublishStatusWithStoreCurrentStatus(requestUser, parameter.getId())
                        eboardRepository.registerTemporaryReservation(parameter)
                    }
                }

                is CancelTemporaryReservation -> {
                    // 解除対象が永続化されていない状態で、仮押さえ解除されることはあり得ないが、
                    // 万が一、解除対象が永続化されてない場合は、いい物件ボードへ同期する必要ない
                    repository.cancel(config, parameter)?.let {
                        propertyMaintenanceRepository.updatePublishStatusFromOldStatus(
                            requestUser,
                            parameter.getId()
                        )
                        eboardRepository.cancelTemporaryReservation(parameter, it)
                    }
                }
            }
        }
    }

    private fun updatePublishStatusWithStoreCurrentStatus(
        requestUser: AuthInfo.RequestUser,
        propertyId: Property.Id,
    ) {
        // 更新前の公開状態を保存する
        propertyMaintenanceRepository.updateOldPublishStatus(requestUser, propertyId)

        // 公開状態を非公開状態にする
        UpdatePropertyMaintenance.onTemporaryReservationRegister(propertyId).let {
            propertyMaintenanceRepository.updatePublishStatus(
                requestUser,
                listOf(it.getUpdatePublishStatus())
            )
        }
    }
}
