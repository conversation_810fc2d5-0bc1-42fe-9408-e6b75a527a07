package jp.ne.simplex.application.controller.client.exclusive.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.*
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd

class ClientExclusivePropertiesRegisterRequest(
    @JsonProperty("buildingCode")
    @field:Schema(description = "建物CD", example = "*********")
    val buildingCode: String,

    @JsonProperty("roomCd")
    @field:Schema(description = "部屋CD", example = "01010")
    val roomCd: String,

    @JsonProperty("exclusiveFrom")
    @field:Schema(description = "先行期間From", example = "20190116")
    val exclusiveFrom: String,

    @JsonProperty("exclusiveTo")
    @field:Schema(description = "先行期間To", example = "20191216")
    val exclusiveTo: String,

    @JsonProperty("companyTypeList")
    @field:Schema(description = "先行先種別リスト", type = "Array", example = "[0,1]")
    val companyTypeList: List<String>,

    @JsonProperty("ecodeList")
    @field:Schema(description = "Eコード", type = "Array", example = "[E12345678, E12345679]")
    val eCodeList: List<String>?,
) {

    companion object {
        private const val MAX_ECODE_COUNT = 50
    }

    // Service層の Interface に変換する
    fun toServiceInterface(): RegisterExclusiveProperty {
        if (companyTypeList.isEmpty()) {
            throw ClientValidationException(ErrorMessage.MISSING_REQUIRED_FIELDS.format("先行先"))
        }

        // 先行先に、不動産を指定している場合、Eコードが必須
        if (companyTypeList.contains(ExclusiveProperty.CompanyType.RealEstate.value.toString())) {
            if (eCodeList.isNullOrEmpty()) {
                throw ClientValidationException(ErrorMessage.MISSING_REQUIRED_FIELDS.format("Eコード"))
            }
        }

        if ((eCodeList?.size ?: 0) > MAX_ECODE_COUNT) {
            throw ClientValidationException(
                ErrorMessage.PROPERTY_MAX_COUNT.format("Eコード", MAX_ECODE_COUNT)
            )
        }

        try {
            return RegisterExclusiveProperty(
                propertyId = Property.Id(
                    buildingCode = Building.Code.of(buildingCode),
                    roomCode = Room.Code.of(roomCd)
                ),
                exclusiveRange = DateRange.of(
                    from = exclusiveFrom.yyyyMMdd(),
                    to = exclusiveTo.yyyyMMdd(),
                    allowSameDate = true,
                ),
                exclusiveTargetWithIds = ExclusiveProperty.ExclusiveTargetWithId.of(
                    companyTypes = companyTypeList,
                    eCodes = eCodeList,
                )
            )

        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        } catch (_: Exception) {
            throw ClientValidationException(ErrorMessage.INVALID_REQUEST_FORMAT.format())
        }
    }
}
