package jp.ne.simplex.application.controller.external.parking.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException
import org.springdoc.core.annotations.ParameterObject

@ParameterObject
@Schema(implementation = ExternalParkingDetailRequest::class)
data class ExternalParkingDetailRequest(
    @JsonProperty("orderCode")
    @field:Schema(description = "受注コード", example = "0001303")
    val orderCode: String,
) {
    // Service層の Interface に変換する
    fun toServiceInterface(): Building.OrderCode {
        try {
            return Building.OrderCode.of(orderCode)
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }
}
