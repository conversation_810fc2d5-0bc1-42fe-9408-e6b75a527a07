-- TABLE: WAON_POINT_AUTHORITY_MASTER(WAONポイント権限マスタ)

CREATE TABLE WAON_POINT_AUTHORITY_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    EMPLOYEE_NUMBER                              varchar(6)        NOT NULL    
,    EFFECTIVE_START_DATE                         numeric(8,0)                  
,    EFFECTIVE_END_DATE                           numeric(8,0)                  
,    APPLICATION_SCOPE                            varchar(2)                    
,    CONSTRAINT PK_WAON_POINT_AUTHORITY_MASTER PRIMARY KEY (EMPLOYEE_NUMBER)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE WAON_POINT_AUTHORITY_MASTER IS 'WAONポイント権限マスタ 既存システム物理名: EMWAMP';
COMMENT ON COLUMN WAON_POINT_AUTHORITY_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: EMWA1D';
COMMENT ON COLUMN WAON_POINT_AUTHORITY_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: EMWA2T';
COMMENT ON COLUMN WAON_POINT_AUTHORITY_MASTER.CREATOR IS '作成者 既存システム物理名: EMWA3C';
COMMENT ON COLUMN WAON_POINT_AUTHORITY_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: EMWA4D';
COMMENT ON COLUMN WAON_POINT_AUTHORITY_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: EMWA5T';
COMMENT ON COLUMN WAON_POINT_AUTHORITY_MASTER.UPDATER IS '更新者 既存システム物理名: EMWA6C';
COMMENT ON COLUMN WAON_POINT_AUTHORITY_MASTER.EMPLOYEE_NUMBER IS '社員番号 既存システム物理名: EMWA7C';
COMMENT ON COLUMN WAON_POINT_AUTHORITY_MASTER.EFFECTIVE_START_DATE IS '適用開始日 既存システム物理名: EMWA8D';
COMMENT ON COLUMN WAON_POINT_AUTHORITY_MASTER.EFFECTIVE_END_DATE IS '適用終了日 既存システム物理名: EMWA9D';
COMMENT ON COLUMN WAON_POINT_AUTHORITY_MASTER.APPLICATION_SCOPE IS '適用範囲 既存システム物理名: EMW10C';
