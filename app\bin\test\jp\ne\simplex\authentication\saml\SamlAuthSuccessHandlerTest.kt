package jp.ne.simplex.authentication.saml

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.controller.client.auth.ClientAuthController
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.repository.aws.SecretManagerRepository
import jp.ne.simplex.application.repository.db.AuthRepository
import jp.ne.simplex.application.service.AuthService
import jp.ne.simplex.application.service.LoginParkingService
import jp.ne.simplex.authentication.AccessKeyVerifier
import jp.ne.simplex.authentication.AuthConfig
import jp.ne.simplex.authentication.JwtAuthProcessor
import jp.ne.simplex.mock.MockBuildingMasterRepository
import jp.ne.simplex.mock.MockEmployeeRepository
import jp.ne.simplex.mock.MockSecretManagerClient
import jp.ne.simplex.stub.stubAuthConfig
import jp.ne.simplex.stub.stubEmployee
import jp.ne.simplex.stub.stubSsoConfig
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.mock.web.MockHttpServletRequest
import org.springframework.mock.web.MockHttpServletResponse
import org.springframework.security.authentication.TestingAuthenticationToken
import java.util.*
import kotlin.test.assertEquals

class SamlAuthSuccessHandlerTest : AbstractTestContainerTest() {

    companion object {
        private val LOGIN_USER = "123000011"
    }

    private val jwtSmClient = MockSecretManagerClient.of(
        config = stubAuthConfig().jwt,
        accessTokenSecretKey = """
                {
                    "jwt_key": "hL{&Vq\"@1}Z(fjQ4[1%G;=!_2P^oZfiT"
                }
            """.trimIndent(),
        refreshTokenSecretKey = """
                {
                    "jwt_key":"\"v\"Bp'&R!k*IeC{3\"j5^q4-H;FWt!Cu;"
                }
            """.trimIndent()

    )

    private val jwtAuthProcessor = JwtAuthProcessor(stubAuthConfig(), SecretManagerRepository(jwtSmClient))

    private val accessKeyVerifier = AccessKeyVerifier(stubSsoConfig(), SecretManagerRepository(MockSecretManagerClient()))

    lateinit var samlAuthSuccessHandler: SamlAuthSuccessHandler

    override fun beforeEach() {
        val authRepository =
            object : AuthRepository(dslContext, MockEmployeeRepository()) {
                override fun
                    getEmployee(employeeCode: Employee.Code): Employee? {
                    return if (employeeCode.value.isEmpty()) null else stubEmployee(LOGIN_USER.takeLast(6))
                }
            }
        val authService = AuthService(authRepository, jwtAuthProcessor, accessKeyVerifier)
        val parkingService = LoginParkingService(
            MockBuildingMasterRepository())
        val clientAuthController = ClientAuthController(stubAuthConfig(), authService, parkingService)
        samlAuthSuccessHandler = SamlAuthSuccessHandler(clientAuthController)
    }

    override fun targetTables(): List<TableImpl<*>> {
        return Collections.emptyList()
    }

    @Test
    @DisplayName("社員が取得できSSOに成功しクッキーがセットされていること")
    fun case01() {
        val req = MockHttpServletRequest()
        val res = MockHttpServletResponse()
        val auth = TestingAuthenticationToken(LOGIN_USER, null).apply {
            isAuthenticated = true
        }
        samlAuthSuccessHandler.onAuthenticationSuccess(req, res, auth)
        assertEquals(2, res.cookies.size)
        assertEquals(AuthConfig.Jwt.COOKIE_ACCESS_TOKEN_KEY, res.cookies[0].name)
        assertEquals(AuthConfig.Jwt.COOKIE_REFRESH_TOKEN_KEY, res.cookies[1].name)
    }

    @Test
    @DisplayName("社員が取得できずSSOに失敗しクッキーがセットされていないこと")
    fun case02() {
        val req = MockHttpServletRequest()
        val res = MockHttpServletResponse()
        val auth = TestingAuthenticationToken(null, null).apply {
            isAuthenticated = true
        }
        samlAuthSuccessHandler.onAuthenticationSuccess(req, res, auth)
        assertEquals(0, res.cookies.size)
    }
}
