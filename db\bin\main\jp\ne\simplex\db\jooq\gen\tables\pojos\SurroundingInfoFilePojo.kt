/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 周辺情報ファイル 既存システム物理名: ERDSJP
 */
@Suppress("UNCHECKED_CAST")
data class SurroundingInfoFilePojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updater: String? = null,
    var buildingCd: String? = null,
    var surroundingInfo_1: String? = null,
    var distance_1: String? = null,
    var surroundingInfo_2: String? = null,
    var distance_2: String? = null,
    var surroundingInfo_3: String? = null,
    var distance_3: String? = null,
    var surroundingInfo_4: String? = null,
    var distance_4: String? = null,
    var surroundingInfo_5: String? = null,
    var distance_5: String? = null,
    var surroundingInfo_6: String? = null,
    var distance_6: String? = null,
    var elementarySchool: String? = null,
    var middleSchool: String? = null,
    var burnableGarbage: String? = null,
    var nonBurnableGarbage: String? = null,
    var recyclableGarbage: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: SurroundingInfoFilePojo = other as SurroundingInfoFilePojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.buildingCd == null) {
            if (o.buildingCd != null)
                return false
        }
        else if (this.buildingCd != o.buildingCd)
            return false
        if (this.surroundingInfo_1 == null) {
            if (o.surroundingInfo_1 != null)
                return false
        }
        else if (this.surroundingInfo_1 != o.surroundingInfo_1)
            return false
        if (this.distance_1 == null) {
            if (o.distance_1 != null)
                return false
        }
        else if (this.distance_1 != o.distance_1)
            return false
        if (this.surroundingInfo_2 == null) {
            if (o.surroundingInfo_2 != null)
                return false
        }
        else if (this.surroundingInfo_2 != o.surroundingInfo_2)
            return false
        if (this.distance_2 == null) {
            if (o.distance_2 != null)
                return false
        }
        else if (this.distance_2 != o.distance_2)
            return false
        if (this.surroundingInfo_3 == null) {
            if (o.surroundingInfo_3 != null)
                return false
        }
        else if (this.surroundingInfo_3 != o.surroundingInfo_3)
            return false
        if (this.distance_3 == null) {
            if (o.distance_3 != null)
                return false
        }
        else if (this.distance_3 != o.distance_3)
            return false
        if (this.surroundingInfo_4 == null) {
            if (o.surroundingInfo_4 != null)
                return false
        }
        else if (this.surroundingInfo_4 != o.surroundingInfo_4)
            return false
        if (this.distance_4 == null) {
            if (o.distance_4 != null)
                return false
        }
        else if (this.distance_4 != o.distance_4)
            return false
        if (this.surroundingInfo_5 == null) {
            if (o.surroundingInfo_5 != null)
                return false
        }
        else if (this.surroundingInfo_5 != o.surroundingInfo_5)
            return false
        if (this.distance_5 == null) {
            if (o.distance_5 != null)
                return false
        }
        else if (this.distance_5 != o.distance_5)
            return false
        if (this.surroundingInfo_6 == null) {
            if (o.surroundingInfo_6 != null)
                return false
        }
        else if (this.surroundingInfo_6 != o.surroundingInfo_6)
            return false
        if (this.distance_6 == null) {
            if (o.distance_6 != null)
                return false
        }
        else if (this.distance_6 != o.distance_6)
            return false
        if (this.elementarySchool == null) {
            if (o.elementarySchool != null)
                return false
        }
        else if (this.elementarySchool != o.elementarySchool)
            return false
        if (this.middleSchool == null) {
            if (o.middleSchool != null)
                return false
        }
        else if (this.middleSchool != o.middleSchool)
            return false
        if (this.burnableGarbage == null) {
            if (o.burnableGarbage != null)
                return false
        }
        else if (this.burnableGarbage != o.burnableGarbage)
            return false
        if (this.nonBurnableGarbage == null) {
            if (o.nonBurnableGarbage != null)
                return false
        }
        else if (this.nonBurnableGarbage != o.nonBurnableGarbage)
            return false
        if (this.recyclableGarbage == null) {
            if (o.recyclableGarbage != null)
                return false
        }
        else if (this.recyclableGarbage != o.recyclableGarbage)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.buildingCd == null) 0 else this.buildingCd.hashCode())
        result = prime * result + (if (this.surroundingInfo_1 == null) 0 else this.surroundingInfo_1.hashCode())
        result = prime * result + (if (this.distance_1 == null) 0 else this.distance_1.hashCode())
        result = prime * result + (if (this.surroundingInfo_2 == null) 0 else this.surroundingInfo_2.hashCode())
        result = prime * result + (if (this.distance_2 == null) 0 else this.distance_2.hashCode())
        result = prime * result + (if (this.surroundingInfo_3 == null) 0 else this.surroundingInfo_3.hashCode())
        result = prime * result + (if (this.distance_3 == null) 0 else this.distance_3.hashCode())
        result = prime * result + (if (this.surroundingInfo_4 == null) 0 else this.surroundingInfo_4.hashCode())
        result = prime * result + (if (this.distance_4 == null) 0 else this.distance_4.hashCode())
        result = prime * result + (if (this.surroundingInfo_5 == null) 0 else this.surroundingInfo_5.hashCode())
        result = prime * result + (if (this.distance_5 == null) 0 else this.distance_5.hashCode())
        result = prime * result + (if (this.surroundingInfo_6 == null) 0 else this.surroundingInfo_6.hashCode())
        result = prime * result + (if (this.distance_6 == null) 0 else this.distance_6.hashCode())
        result = prime * result + (if (this.elementarySchool == null) 0 else this.elementarySchool.hashCode())
        result = prime * result + (if (this.middleSchool == null) 0 else this.middleSchool.hashCode())
        result = prime * result + (if (this.burnableGarbage == null) 0 else this.burnableGarbage.hashCode())
        result = prime * result + (if (this.nonBurnableGarbage == null) 0 else this.nonBurnableGarbage.hashCode())
        result = prime * result + (if (this.recyclableGarbage == null) 0 else this.recyclableGarbage.hashCode())
        return result
    }
}
