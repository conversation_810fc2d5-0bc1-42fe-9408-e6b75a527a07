package jp.ne.simplex.application.repository.db.extension

import jp.ne.simplex.application.model.*
import jp.ne.simplex.db.jooq.gen.tables.pojos.PropertyMaintenanceInfoPojo
import org.slf4j.LoggerFactory

class PropertyMaintenanceInfoEx {

    companion object {

        private val log = LoggerFactory.getLogger(PropertyMaintenanceInfoEx::class.java)

        fun PropertyMaintenanceInfoPojo.toPropertyMaintenanceInfo(): PropertyMaintenanceInfo? {
            return try {
                PropertyMaintenanceInfo(
                    propertyId = Property.Id(
                        buildingCode = Building.Code.of(this.buildingCd),
                        roomCode = Room.Code.of(this.roomCd)
                    ),
                    publishStatus = PropertyMaintenance.PublishStatus.fromValue(this.listingCategoryGoodRoomNet?.toInt()),
                    publishStatusBeforeTemporaryReserved = PropertyMaintenance.PublishStatus.fromValue(
                        this.homesPanoramaSendFlag?.toInt()
                    ),
                )
            } catch (_: Exception) {
                // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
                log.warn("Failed to deserialize PropertyMaintenanceInfo record. $this")
                null
            }
        }
    }
}
