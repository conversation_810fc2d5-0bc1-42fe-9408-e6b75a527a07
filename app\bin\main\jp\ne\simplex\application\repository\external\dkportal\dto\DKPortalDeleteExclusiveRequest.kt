package jp.ne.simplex.application.repository.external.dkportal.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.model.ExclusiveProperty
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalOperationName
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalRequest

abstract class DKPortalDeleteExclusiveRequest(
    @field:JsonProperty("dk_link_id")
    val dkLinkId: String,
) : DKPortalRequest {
    abstract override fun getDKPortalOperationName(): DKPortalOperationName

    companion object {

        fun of(
            property: Property,
            id: ExclusiveProperty.Id,
        ): DKPortalDeleteExclusiveRequest {
            return when (property.getType()) {
                Property.Type.RESIDENTIAL -> {
                    DKPortalDeleteExclusiveHousingRequest(id)
                }

                Property.Type.COMMERCIAL -> {
                    DKPortalDeleteExclusiveBusinessRequest(id)
                }
            }
        }

    }
}
