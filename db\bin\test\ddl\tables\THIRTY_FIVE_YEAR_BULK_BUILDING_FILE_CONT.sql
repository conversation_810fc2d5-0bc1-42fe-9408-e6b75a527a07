-- TABLE: THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT(35年一括建物ファイル(契約書))

CREATE TABLE THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATION_PROGRAM_ID                          varchar(10)                   
,    CREATION_TERMINAL_ID                         varchar(8)                    
,    CREATION_RESPONSIBLE_CD                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATE_TERMINAL_ID                           varchar(8)                    
,    UPDATE_RESPONSIBLE_CD                        varchar(6)                    
,    LOGICAL_DELETE_SIGN                          varchar(1)                    
,    BUILDING_CD                                  varchar(9)                    
,    EFFECTIVE_DATE                               numeric(8,0)                  
,    CONTRACT_OUTPUT_MANAGEMENT_NO                numeric(4,0)                  
,    BULK_LEASE_TYPE                              numeric(1,0)                  
,    CONSTRAINT UQ_THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT UNIQUE (BUILDING_CD, EFFECTIVE_DATE, LOGICAL_DELETE_SIGN)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT IS '35年一括建物ファイル(契約書) 既存システム物理名: HC360P';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.CREATION_DATE IS '作成年月日 既存システム物理名: HC301D';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.CREATION_TIME IS '作成時間 既存システム物理名: HC302H';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.CREATION_PROGRAM_ID IS '作成プログラムID 既存システム物理名: HC303M';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.CREATION_TERMINAL_ID IS '作成端末ID 既存システム物理名: HC304M';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.CREATION_RESPONSIBLE_CD IS '作成担当者CD 既存システム物理名: HC305M';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.UPDATE_DATE IS '更新年月日 既存システム物理名: HC306D';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.UPDATE_TIME IS '更新時間 既存システム物理名: HC307H';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: HC308M';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.UPDATE_TERMINAL_ID IS '更新端末ID 既存システム物理名: HC309M';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.UPDATE_RESPONSIBLE_CD IS '更新担当者CD 既存システム物理名: HC310M';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: HC311S';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.BUILDING_CD IS '建物CD 既存システム物理名: HC312C';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.EFFECTIVE_DATE IS '効力発生日 既存システム物理名: HC313D';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.CONTRACT_OUTPUT_MANAGEMENT_NO IS '契約書出力管理No 既存システム物理名: HC314N';
COMMENT ON COLUMN THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.BULK_LEASE_TYPE IS '一括借上タイプ 既存システム物理名: HC315B';
