/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable
import java.math.BigDecimal


/**
 * 空き家HP用 既存システム物理名: EMEH2P
 */
@Suppress("UNCHECKED_CAST")
data class VacantHouseHpPojo(
    var propertyCdDivision: String? = null,
    var propertyCdSeparator_1: String? = null,
    var propertyBuildingCd: String? = null,
    var propertyCdSeparator_2: String? = null,
    var propertyRoomCd: String? = null,
    var deleteFlag: String? = null,
    var customerCompanyCd: String? = null,
    var customerBranchCd: String? = null,
    var customerDepartmentCd: String? = null,
    var customerCompletionFlag: String? = null,
    var municipalityCd: String? = null,
    var lineCd: String? = null,
    var stationCd: String? = null,
    var rent: Int? = null,
    var layoutRooms: String? = null,
    var exclusiveArea: String? = null,
    var propertyType: String? = null,
    var feature_1: Byte? = null,
    var feature_2: Byte? = null,
    var feature_3: Byte? = null,
    var feature_4: Byte? = null,
    var feature_5: Byte? = null,
    var feature_6: Byte? = null,
    var feature_7: Byte? = null,
    var feature_8: Byte? = null,
    var feature_9: Byte? = null,
    var feature_10: Byte? = null,
    var feature_11: Byte? = null,
    var feature_12: Byte? = null,
    var feature_13: Byte? = null,
    var feature_14: Byte? = null,
    var feature_15: Byte? = null,
    var feature_16: Byte? = null,
    var feature_17: Byte? = null,
    var feature_18: Byte? = null,
    var feature_19: Byte? = null,
    var feature_20: Byte? = null,
    var feature_21: Byte? = null,
    var feature_22: Byte? = null,
    var feature_23: Byte? = null,
    var feature_24: Byte? = null,
    var feature_25: Byte? = null,
    var feature_26: Byte? = null,
    var feature_27: Byte? = null,
    var feature_28: Byte? = null,
    var feature_29: Byte? = null,
    var feature_30: Byte? = null,
    var feature_31: Byte? = null,
    var feature_32: Byte? = null,
    var feature_33: Byte? = null,
    var feature_34: Byte? = null,
    var feature_35: Byte? = null,
    var feature_36: Byte? = null,
    var feature_37: Byte? = null,
    var feature_38: Byte? = null,
    var feature_39: Byte? = null,
    var feature_40: Byte? = null,
    var feature_41: Byte? = null,
    var feature_42: Byte? = null,
    var feature_43: Byte? = null,
    var feature_44: Byte? = null,
    var feature_45: Byte? = null,
    var feature_46: Byte? = null,
    var feature_47: Byte? = null,
    var feature_48: Byte? = null,
    var feature_49: Byte? = null,
    var feature_50: Byte? = null,
    var feature_51: Byte? = null,
    var feature_52: Byte? = null,
    var feature_53: Byte? = null,
    var feature_54: Byte? = null,
    var feature_55: Byte? = null,
    var feature_56: Byte? = null,
    var feature_57: Byte? = null,
    var feature_58: Byte? = null,
    var feature_59: Byte? = null,
    var feature_60: Byte? = null,
    var feature_99: Byte? = null,
    var featureNewBuilding: Byte? = null,
    var featureCornerRoom: Byte? = null,
    var featureAboveSecondFloor: Byte? = null,
    var lineName: String? = null,
    var stationName: String? = null,
    var busStopName: String? = null,
    var busTime: Short? = null,
    var walkTime: Short? = null,
    var distance: Short? = null,
    var keyMoney: String? = null,
    var deposit: String? = null,
    var neighborhoodAssociationFee: String? = null,
    var maintenanceFee: String? = null,
    var roomTypeName: String? = null,
    var layout: String? = null,
    var layoutDetails: String? = null,
    var parkingDivision: String? = null,
    var parkingFee: String? = null,
    var buildYear: String? = null,
    var handlingStoreCompany: String? = null,
    var locationPublishAreaName: String? = null,
    var floorCount: String? = null,
    var direction: String? = null,
    var roomPosition: String? = null,
    var availableDate: String? = null,
    var transportation: String? = null,
    var equipment: String? = null,
    var remarks: String? = null,
    var contactBranchName: String? = null,
    var branchPhoneNumber: String? = null,
    var branchFaxNumber: String? = null,
    var transactionType: String? = null,
    var buildingName: String? = null,
    var structureName: String? = null,
    var agentAvailableDivision: String? = null,
    var subleaseDivision: String? = null,
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var creator: String? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updater: String? = null,
    var branchAddress: String? = null,
    var recommendationComment: String? = null,
    var completionYearMonth: Int? = null,
    var propertyPostalCode: String? = null,
    var recordSeparator: String? = null,
    var rentTax: Int? = null,
    var keyMoneyTax: Int? = null,
    var keyMoneyTotal: Int? = null,
    var maintenanceFeeTax: Int? = null,
    var parkingFeeTax: Int? = null,
    var changeDivision: String? = null,
    var updateFlag: String? = null,
    var feature_61: Byte? = null,
    var feature_62: Byte? = null,
    var feature_63: Byte? = null,
    var feature_64: Byte? = null,
    var feature_65: Byte? = null,
    var feature_66: Byte? = null,
    var feature_67: Byte? = null,
    var feature_68: Byte? = null,
    var feature_69: Byte? = null,
    var feature_70: Byte? = null,
    var feature_71: Byte? = null,
    var feature_72: Byte? = null,
    var feature_73: Byte? = null,
    var feature_74: Byte? = null,
    var feature_75: Byte? = null,
    var feature_76: Byte? = null,
    var feature_77: Byte? = null,
    var feature_78: Byte? = null,
    var feature_79: Byte? = null,
    var feature_80: Byte? = null,
    var feature_81: Byte? = null,
    var feature_82: Byte? = null,
    var feature_83: Byte? = null,
    var feature_84: Byte? = null,
    var feature_85: Byte? = null,
    var feature_86: Byte? = null,
    var feature_87: Byte? = null,
    var feature_88: Byte? = null,
    var feature_89: Byte? = null,
    var feature_90: Byte? = null,
    var feature_91: Byte? = null,
    var feature_92: Byte? = null,
    var feature_93: Byte? = null,
    var feature_94: Byte? = null,
    var feature_95: Byte? = null,
    var feature_96: Byte? = null,
    var feature_97: Byte? = null,
    var feature_98: Byte? = null,
    var equipmentFlag_1: Byte? = null,
    var equipmentFlag_2: Byte? = null,
    var equipmentFlag_3: Byte? = null,
    var equipmentFlag_4: Byte? = null,
    var equipmentFlag_5: Byte? = null,
    var equipmentFlag_6: Byte? = null,
    var equipmentFlag_7: Byte? = null,
    var equipmentFlag_8: Byte? = null,
    var equipmentFlag_9: Byte? = null,
    var equipmentFlag_10: Byte? = null,
    var rentalDivision: Byte? = null,
    var distance2: BigDecimal? = null,
    var prefectureCode: String? = null,
    var cityWardCode: String? = null,
    var townVillageAliasCode: String? = null,
    var productNameCode: Short? = null,
    var approvalDivision: Byte? = null,
    var propertyNameJudgmentSign: Byte? = null,
    var depositZeroFlag: Byte? = null,
    var fletsSupportCd: Byte? = null,
    var skyPerfectSupportCd: Byte? = null,
    var campaignTargetFlag: Byte? = null,
    var propertyAddressDetails: String? = null,
    var parking_2CarsAvailable: Byte? = null,
    var newInteriorMaterialUsed: Byte? = null,
    var serviceRoomSign: Byte? = null,
    var highVoltagePowerBulkReception: Byte? = null,
    var highCostRentSign: Byte? = null,
    var layoutDisplayOrder: String? = null,
    var prefectureKanjiName: String? = null,
    var prefectureKanaName: String? = null,
    var cityWardKanjiName: String? = null,
    var cityWardKanaName: String? = null,
    var townVillageKanjiName: String? = null,
    var townVillageKanaName: String? = null,
    var keyExchangeSpecialContractTarget: Byte? = null,
    var solarPowerDiscountTarget: Byte? = null,
    var cleaningCostFlatRate: Byte? = null,
    var petFlag: Byte? = null,
    var flagReserve: Byte? = null,
    var cleaningCostTotal: Int? = null,
    var flagReserve_6: Byte? = null,
    var flagReserve_7: Byte? = null,
    var flagReserve_8: Byte? = null,
    var flagReserve_9: Byte? = null,
    var flagReserve_10: Byte? = null,
    var dateReserve_6: Int? = null,
    var dateReserve_7: Int? = null,
    var dateReserve_8: Int? = null,
    var dateReserve_9: Int? = null,
    var dateReserve_10: Int? = null,
    var maintenanceFeeMain: Int? = null,
    var generalCableTvMain: Int? = null,
    var generalCableTvTax: Int? = null,
    var generalInternetMain: Int? = null,
    var generalInternetTax: Int? = null,
    var generalWaterQualityMaintenanceMain: Int? = null,
    var generalWaterQualityMaintenanceTax: Int? = null,
    var generalTenantWaterMain: Int? = null,
    var generalTenantWaterTax: Int? = null,
    var generalDrainageUseMain: Int? = null,
    var generalDrainageUseTax: Int? = null,
    var generalGarbageCollectionMain: Int? = null,
    var generalGarbageCollectionTax: Int? = null,
    var generalCommonAntennaMain: Int? = null,
    var generalCommonAntennaTax: Int? = null,
    var generalLandlordCleaningMain: Int? = null,
    var generalLandlordCleaningTax: Int? = null,
    var generalBuildingMaintenanceMain: Int? = null,
    var generalBuildingMaintenanceTax: Int? = null,
    var generalBuildingManagementMain: Int? = null,
    var generalBuildingManagementTax: Int? = null,
    var generalNeighborhoodAssociationMain: Int? = null,
    var generalNeighborhoodAssociationTax: Int? = null,
    var generalNeighborhoodAssocOtherMain: Int? = null,
    var generalNeighborhoodAssocOtherTax: Int? = null,
    var generalRepaymentAgencyMain: Int? = null,
    var generalRepaymentAgencyTax: Int? = null,
    var generalHlCommissionMain: Int? = null,
    var generalHlCommissionTax: Int? = null,
    var generalFurnitureIncludedMain: Int? = null,
    var generalFurnitureIncludedTax: Int? = null,
    var generalTenantDepositMain: Int? = null,
    var generalTenantDepositTax: Int? = null,
    var generalRentalMain: Int? = null,
    var generalRentalTax: Int? = null,
    var reserveAmount_1Main: Int? = null,
    var reserveAmount_1Tax: Int? = null,
    var reserveAmount_2Main: Int? = null,
    var reserveAmount_2Tax: Int? = null,
    var reserveAmount_3Main: Int? = null,
    var reserveAmount_3Tax: Int? = null,
    var flagReserve_11: Byte? = null,
    var flagReserve_12: Byte? = null,
    var flagReserve_13: Byte? = null,
    var flagReserve_14: Byte? = null,
    var flagReserve_15: Byte? = null,
    var divisionReserve_1: String? = null,
    var divisionReserve_2: String? = null,
    var divisionReserve_3: String? = null,
    var divisionReserve_4: String? = null,
    var divisionReserve_5: String? = null,
    var amountReserve_4: Int? = null,
    var amountReserve_5: Int? = null,
    var amountReserve_6: Int? = null,
    var amountReserve_7: Int? = null,
    var amountReserve_8: Int? = null,
    var dateReserve_14: Int? = null,
    var dateReserve_15: Int? = null,
    var dateReserve_16: Int? = null,
    var dateReserve_17: Int? = null,
    var dateReserve_18: Int? = null,
    var division_1DigitReserve_1: String? = null,
    var division_1DigitReserve_2: String? = null,
    var division_1DigitReserve_3: String? = null,
    var division_1DigitReserve_4: String? = null,
    var division_1DigitReserve_5: String? = null,
    var leasingStoreCd: String? = null,
    var managementBranchCd: String? = null,
    var officeCd: String? = null,
    var reviewBranchCd: String? = null,
    var feature_100: Byte? = null,
    var feature_101: Byte? = null,
    var feature_102: Byte? = null,
    var feature_103: Byte? = null,
    var feature_104: Byte? = null,
    var feature_105: Byte? = null,
    var feature_106: Byte? = null,
    var feature_107: Byte? = null,
    var feature_108: Byte? = null,
    var feature_109: Byte? = null,
    var feature_110: Byte? = null,
    var feature_111: Byte? = null,
    var feature_112: Byte? = null,
    var feature_113: Byte? = null,
    var feature_114: Byte? = null,
    var feature_115: Byte? = null,
    var feature_116: Byte? = null,
    var feature_117: Byte? = null,
    var feature_118: Byte? = null,
    var feature_119: Byte? = null,
    var feature_120: Byte? = null,
    var feature_121: Byte? = null,
    var feature_122: Byte? = null,
    var feature_123: Byte? = null,
    var feature_124: Byte? = null,
    var feature_125: Byte? = null,
    var feature_126: Byte? = null,
    var feature_127: Byte? = null,
    var feature_128: Byte? = null,
    var feature_129: Byte? = null,
    var feature_130: Byte? = null,
    var feature_131: Byte? = null,
    var feature_132: Byte? = null,
    var feature_133: Byte? = null,
    var feature_134: Byte? = null,
    var feature_135: Byte? = null,
    var feature_136: Byte? = null,
    var feature_137: Byte? = null,
    var feature_138: Byte? = null,
    var feature_139: Byte? = null,
    var feature_140: Byte? = null,
    var feature_141: Byte? = null,
    var feature_142: Byte? = null,
    var feature_143: Byte? = null,
    var feature_144: Byte? = null,
    var feature_145: Byte? = null,
    var feature_146: Byte? = null,
    var feature_147: Byte? = null,
    var feature_148: Byte? = null,
    var feature_149: Byte? = null,
    var feature_150: Byte? = null,
    var feature_151: Byte? = null,
    var feature_152: Byte? = null,
    var feature_153: Byte? = null,
    var feature_154: Byte? = null,
    var feature_155: Byte? = null,
    var feature_156: Byte? = null,
    var feature_157: Byte? = null,
    var feature_158: Byte? = null,
    var feature_159: Byte? = null,
    var feature_160: Byte? = null,
    var feature_161: Byte? = null,
    var feature_162: Byte? = null,
    var feature_163: Byte? = null,
    var feature_164: Byte? = null,
    var feature_165: Byte? = null,
    var feature_166: Byte? = null,
    var feature_167: Byte? = null,
    var feature_168: Byte? = null,
    var feature_169: Byte? = null,
    var feature_170: Byte? = null,
    var feature_171: Byte? = null,
    var feature_172: Byte? = null,
    var feature_173: Byte? = null,
    var feature_174: Byte? = null,
    var feature_175: Byte? = null,
    var feature_176: Byte? = null,
    var feature_177: Byte? = null,
    var feature_178: Byte? = null,
    var feature_179: Byte? = null,
    var feature_180: Byte? = null,
    var feature_181: Byte? = null,
    var feature_182: Byte? = null,
    var feature_183: Byte? = null,
    var feature_184: Byte? = null,
    var feature_185: Byte? = null,
    var feature_186: Byte? = null,
    var feature_187: Byte? = null,
    var feature_188: Byte? = null,
    var feature_189: Byte? = null,
    var feature_190: Byte? = null,
    var feature_191: Byte? = null,
    var feature_192: Byte? = null,
    var feature_193: Byte? = null,
    var feature_194: Byte? = null,
    var feature_195: Byte? = null,
    var feature_196: Byte? = null,
    var feature_197: Byte? = null,
    var feature_198: Byte? = null,
    var feature_199: Byte? = null,
    var marketingBranchOfficeCd: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: VacantHouseHpPojo = other as VacantHouseHpPojo
        if (this.propertyCdDivision == null) {
            if (o.propertyCdDivision != null)
                return false
        }
        else if (this.propertyCdDivision != o.propertyCdDivision)
            return false
        if (this.propertyCdSeparator_1 == null) {
            if (o.propertyCdSeparator_1 != null)
                return false
        }
        else if (this.propertyCdSeparator_1 != o.propertyCdSeparator_1)
            return false
        if (this.propertyBuildingCd == null) {
            if (o.propertyBuildingCd != null)
                return false
        }
        else if (this.propertyBuildingCd != o.propertyBuildingCd)
            return false
        if (this.propertyCdSeparator_2 == null) {
            if (o.propertyCdSeparator_2 != null)
                return false
        }
        else if (this.propertyCdSeparator_2 != o.propertyCdSeparator_2)
            return false
        if (this.propertyRoomCd == null) {
            if (o.propertyRoomCd != null)
                return false
        }
        else if (this.propertyRoomCd != o.propertyRoomCd)
            return false
        if (this.deleteFlag == null) {
            if (o.deleteFlag != null)
                return false
        }
        else if (this.deleteFlag != o.deleteFlag)
            return false
        if (this.customerCompanyCd == null) {
            if (o.customerCompanyCd != null)
                return false
        }
        else if (this.customerCompanyCd != o.customerCompanyCd)
            return false
        if (this.customerBranchCd == null) {
            if (o.customerBranchCd != null)
                return false
        }
        else if (this.customerBranchCd != o.customerBranchCd)
            return false
        if (this.customerDepartmentCd == null) {
            if (o.customerDepartmentCd != null)
                return false
        }
        else if (this.customerDepartmentCd != o.customerDepartmentCd)
            return false
        if (this.customerCompletionFlag == null) {
            if (o.customerCompletionFlag != null)
                return false
        }
        else if (this.customerCompletionFlag != o.customerCompletionFlag)
            return false
        if (this.municipalityCd == null) {
            if (o.municipalityCd != null)
                return false
        }
        else if (this.municipalityCd != o.municipalityCd)
            return false
        if (this.lineCd == null) {
            if (o.lineCd != null)
                return false
        }
        else if (this.lineCd != o.lineCd)
            return false
        if (this.stationCd == null) {
            if (o.stationCd != null)
                return false
        }
        else if (this.stationCd != o.stationCd)
            return false
        if (this.rent == null) {
            if (o.rent != null)
                return false
        }
        else if (this.rent != o.rent)
            return false
        if (this.layoutRooms == null) {
            if (o.layoutRooms != null)
                return false
        }
        else if (this.layoutRooms != o.layoutRooms)
            return false
        if (this.exclusiveArea == null) {
            if (o.exclusiveArea != null)
                return false
        }
        else if (this.exclusiveArea != o.exclusiveArea)
            return false
        if (this.propertyType == null) {
            if (o.propertyType != null)
                return false
        }
        else if (this.propertyType != o.propertyType)
            return false
        if (this.feature_1 == null) {
            if (o.feature_1 != null)
                return false
        }
        else if (this.feature_1 != o.feature_1)
            return false
        if (this.feature_2 == null) {
            if (o.feature_2 != null)
                return false
        }
        else if (this.feature_2 != o.feature_2)
            return false
        if (this.feature_3 == null) {
            if (o.feature_3 != null)
                return false
        }
        else if (this.feature_3 != o.feature_3)
            return false
        if (this.feature_4 == null) {
            if (o.feature_4 != null)
                return false
        }
        else if (this.feature_4 != o.feature_4)
            return false
        if (this.feature_5 == null) {
            if (o.feature_5 != null)
                return false
        }
        else if (this.feature_5 != o.feature_5)
            return false
        if (this.feature_6 == null) {
            if (o.feature_6 != null)
                return false
        }
        else if (this.feature_6 != o.feature_6)
            return false
        if (this.feature_7 == null) {
            if (o.feature_7 != null)
                return false
        }
        else if (this.feature_7 != o.feature_7)
            return false
        if (this.feature_8 == null) {
            if (o.feature_8 != null)
                return false
        }
        else if (this.feature_8 != o.feature_8)
            return false
        if (this.feature_9 == null) {
            if (o.feature_9 != null)
                return false
        }
        else if (this.feature_9 != o.feature_9)
            return false
        if (this.feature_10 == null) {
            if (o.feature_10 != null)
                return false
        }
        else if (this.feature_10 != o.feature_10)
            return false
        if (this.feature_11 == null) {
            if (o.feature_11 != null)
                return false
        }
        else if (this.feature_11 != o.feature_11)
            return false
        if (this.feature_12 == null) {
            if (o.feature_12 != null)
                return false
        }
        else if (this.feature_12 != o.feature_12)
            return false
        if (this.feature_13 == null) {
            if (o.feature_13 != null)
                return false
        }
        else if (this.feature_13 != o.feature_13)
            return false
        if (this.feature_14 == null) {
            if (o.feature_14 != null)
                return false
        }
        else if (this.feature_14 != o.feature_14)
            return false
        if (this.feature_15 == null) {
            if (o.feature_15 != null)
                return false
        }
        else if (this.feature_15 != o.feature_15)
            return false
        if (this.feature_16 == null) {
            if (o.feature_16 != null)
                return false
        }
        else if (this.feature_16 != o.feature_16)
            return false
        if (this.feature_17 == null) {
            if (o.feature_17 != null)
                return false
        }
        else if (this.feature_17 != o.feature_17)
            return false
        if (this.feature_18 == null) {
            if (o.feature_18 != null)
                return false
        }
        else if (this.feature_18 != o.feature_18)
            return false
        if (this.feature_19 == null) {
            if (o.feature_19 != null)
                return false
        }
        else if (this.feature_19 != o.feature_19)
            return false
        if (this.feature_20 == null) {
            if (o.feature_20 != null)
                return false
        }
        else if (this.feature_20 != o.feature_20)
            return false
        if (this.feature_21 == null) {
            if (o.feature_21 != null)
                return false
        }
        else if (this.feature_21 != o.feature_21)
            return false
        if (this.feature_22 == null) {
            if (o.feature_22 != null)
                return false
        }
        else if (this.feature_22 != o.feature_22)
            return false
        if (this.feature_23 == null) {
            if (o.feature_23 != null)
                return false
        }
        else if (this.feature_23 != o.feature_23)
            return false
        if (this.feature_24 == null) {
            if (o.feature_24 != null)
                return false
        }
        else if (this.feature_24 != o.feature_24)
            return false
        if (this.feature_25 == null) {
            if (o.feature_25 != null)
                return false
        }
        else if (this.feature_25 != o.feature_25)
            return false
        if (this.feature_26 == null) {
            if (o.feature_26 != null)
                return false
        }
        else if (this.feature_26 != o.feature_26)
            return false
        if (this.feature_27 == null) {
            if (o.feature_27 != null)
                return false
        }
        else if (this.feature_27 != o.feature_27)
            return false
        if (this.feature_28 == null) {
            if (o.feature_28 != null)
                return false
        }
        else if (this.feature_28 != o.feature_28)
            return false
        if (this.feature_29 == null) {
            if (o.feature_29 != null)
                return false
        }
        else if (this.feature_29 != o.feature_29)
            return false
        if (this.feature_30 == null) {
            if (o.feature_30 != null)
                return false
        }
        else if (this.feature_30 != o.feature_30)
            return false
        if (this.feature_31 == null) {
            if (o.feature_31 != null)
                return false
        }
        else if (this.feature_31 != o.feature_31)
            return false
        if (this.feature_32 == null) {
            if (o.feature_32 != null)
                return false
        }
        else if (this.feature_32 != o.feature_32)
            return false
        if (this.feature_33 == null) {
            if (o.feature_33 != null)
                return false
        }
        else if (this.feature_33 != o.feature_33)
            return false
        if (this.feature_34 == null) {
            if (o.feature_34 != null)
                return false
        }
        else if (this.feature_34 != o.feature_34)
            return false
        if (this.feature_35 == null) {
            if (o.feature_35 != null)
                return false
        }
        else if (this.feature_35 != o.feature_35)
            return false
        if (this.feature_36 == null) {
            if (o.feature_36 != null)
                return false
        }
        else if (this.feature_36 != o.feature_36)
            return false
        if (this.feature_37 == null) {
            if (o.feature_37 != null)
                return false
        }
        else if (this.feature_37 != o.feature_37)
            return false
        if (this.feature_38 == null) {
            if (o.feature_38 != null)
                return false
        }
        else if (this.feature_38 != o.feature_38)
            return false
        if (this.feature_39 == null) {
            if (o.feature_39 != null)
                return false
        }
        else if (this.feature_39 != o.feature_39)
            return false
        if (this.feature_40 == null) {
            if (o.feature_40 != null)
                return false
        }
        else if (this.feature_40 != o.feature_40)
            return false
        if (this.feature_41 == null) {
            if (o.feature_41 != null)
                return false
        }
        else if (this.feature_41 != o.feature_41)
            return false
        if (this.feature_42 == null) {
            if (o.feature_42 != null)
                return false
        }
        else if (this.feature_42 != o.feature_42)
            return false
        if (this.feature_43 == null) {
            if (o.feature_43 != null)
                return false
        }
        else if (this.feature_43 != o.feature_43)
            return false
        if (this.feature_44 == null) {
            if (o.feature_44 != null)
                return false
        }
        else if (this.feature_44 != o.feature_44)
            return false
        if (this.feature_45 == null) {
            if (o.feature_45 != null)
                return false
        }
        else if (this.feature_45 != o.feature_45)
            return false
        if (this.feature_46 == null) {
            if (o.feature_46 != null)
                return false
        }
        else if (this.feature_46 != o.feature_46)
            return false
        if (this.feature_47 == null) {
            if (o.feature_47 != null)
                return false
        }
        else if (this.feature_47 != o.feature_47)
            return false
        if (this.feature_48 == null) {
            if (o.feature_48 != null)
                return false
        }
        else if (this.feature_48 != o.feature_48)
            return false
        if (this.feature_49 == null) {
            if (o.feature_49 != null)
                return false
        }
        else if (this.feature_49 != o.feature_49)
            return false
        if (this.feature_50 == null) {
            if (o.feature_50 != null)
                return false
        }
        else if (this.feature_50 != o.feature_50)
            return false
        if (this.feature_51 == null) {
            if (o.feature_51 != null)
                return false
        }
        else if (this.feature_51 != o.feature_51)
            return false
        if (this.feature_52 == null) {
            if (o.feature_52 != null)
                return false
        }
        else if (this.feature_52 != o.feature_52)
            return false
        if (this.feature_53 == null) {
            if (o.feature_53 != null)
                return false
        }
        else if (this.feature_53 != o.feature_53)
            return false
        if (this.feature_54 == null) {
            if (o.feature_54 != null)
                return false
        }
        else if (this.feature_54 != o.feature_54)
            return false
        if (this.feature_55 == null) {
            if (o.feature_55 != null)
                return false
        }
        else if (this.feature_55 != o.feature_55)
            return false
        if (this.feature_56 == null) {
            if (o.feature_56 != null)
                return false
        }
        else if (this.feature_56 != o.feature_56)
            return false
        if (this.feature_57 == null) {
            if (o.feature_57 != null)
                return false
        }
        else if (this.feature_57 != o.feature_57)
            return false
        if (this.feature_58 == null) {
            if (o.feature_58 != null)
                return false
        }
        else if (this.feature_58 != o.feature_58)
            return false
        if (this.feature_59 == null) {
            if (o.feature_59 != null)
                return false
        }
        else if (this.feature_59 != o.feature_59)
            return false
        if (this.feature_60 == null) {
            if (o.feature_60 != null)
                return false
        }
        else if (this.feature_60 != o.feature_60)
            return false
        if (this.feature_99 == null) {
            if (o.feature_99 != null)
                return false
        }
        else if (this.feature_99 != o.feature_99)
            return false
        if (this.featureNewBuilding == null) {
            if (o.featureNewBuilding != null)
                return false
        }
        else if (this.featureNewBuilding != o.featureNewBuilding)
            return false
        if (this.featureCornerRoom == null) {
            if (o.featureCornerRoom != null)
                return false
        }
        else if (this.featureCornerRoom != o.featureCornerRoom)
            return false
        if (this.featureAboveSecondFloor == null) {
            if (o.featureAboveSecondFloor != null)
                return false
        }
        else if (this.featureAboveSecondFloor != o.featureAboveSecondFloor)
            return false
        if (this.lineName == null) {
            if (o.lineName != null)
                return false
        }
        else if (this.lineName != o.lineName)
            return false
        if (this.stationName == null) {
            if (o.stationName != null)
                return false
        }
        else if (this.stationName != o.stationName)
            return false
        if (this.busStopName == null) {
            if (o.busStopName != null)
                return false
        }
        else if (this.busStopName != o.busStopName)
            return false
        if (this.busTime == null) {
            if (o.busTime != null)
                return false
        }
        else if (this.busTime != o.busTime)
            return false
        if (this.walkTime == null) {
            if (o.walkTime != null)
                return false
        }
        else if (this.walkTime != o.walkTime)
            return false
        if (this.distance == null) {
            if (o.distance != null)
                return false
        }
        else if (this.distance != o.distance)
            return false
        if (this.keyMoney == null) {
            if (o.keyMoney != null)
                return false
        }
        else if (this.keyMoney != o.keyMoney)
            return false
        if (this.deposit == null) {
            if (o.deposit != null)
                return false
        }
        else if (this.deposit != o.deposit)
            return false
        if (this.neighborhoodAssociationFee == null) {
            if (o.neighborhoodAssociationFee != null)
                return false
        }
        else if (this.neighborhoodAssociationFee != o.neighborhoodAssociationFee)
            return false
        if (this.maintenanceFee == null) {
            if (o.maintenanceFee != null)
                return false
        }
        else if (this.maintenanceFee != o.maintenanceFee)
            return false
        if (this.roomTypeName == null) {
            if (o.roomTypeName != null)
                return false
        }
        else if (this.roomTypeName != o.roomTypeName)
            return false
        if (this.layout == null) {
            if (o.layout != null)
                return false
        }
        else if (this.layout != o.layout)
            return false
        if (this.layoutDetails == null) {
            if (o.layoutDetails != null)
                return false
        }
        else if (this.layoutDetails != o.layoutDetails)
            return false
        if (this.parkingDivision == null) {
            if (o.parkingDivision != null)
                return false
        }
        else if (this.parkingDivision != o.parkingDivision)
            return false
        if (this.parkingFee == null) {
            if (o.parkingFee != null)
                return false
        }
        else if (this.parkingFee != o.parkingFee)
            return false
        if (this.buildYear == null) {
            if (o.buildYear != null)
                return false
        }
        else if (this.buildYear != o.buildYear)
            return false
        if (this.handlingStoreCompany == null) {
            if (o.handlingStoreCompany != null)
                return false
        }
        else if (this.handlingStoreCompany != o.handlingStoreCompany)
            return false
        if (this.locationPublishAreaName == null) {
            if (o.locationPublishAreaName != null)
                return false
        }
        else if (this.locationPublishAreaName != o.locationPublishAreaName)
            return false
        if (this.floorCount == null) {
            if (o.floorCount != null)
                return false
        }
        else if (this.floorCount != o.floorCount)
            return false
        if (this.direction == null) {
            if (o.direction != null)
                return false
        }
        else if (this.direction != o.direction)
            return false
        if (this.roomPosition == null) {
            if (o.roomPosition != null)
                return false
        }
        else if (this.roomPosition != o.roomPosition)
            return false
        if (this.availableDate == null) {
            if (o.availableDate != null)
                return false
        }
        else if (this.availableDate != o.availableDate)
            return false
        if (this.transportation == null) {
            if (o.transportation != null)
                return false
        }
        else if (this.transportation != o.transportation)
            return false
        if (this.equipment == null) {
            if (o.equipment != null)
                return false
        }
        else if (this.equipment != o.equipment)
            return false
        if (this.remarks == null) {
            if (o.remarks != null)
                return false
        }
        else if (this.remarks != o.remarks)
            return false
        if (this.contactBranchName == null) {
            if (o.contactBranchName != null)
                return false
        }
        else if (this.contactBranchName != o.contactBranchName)
            return false
        if (this.branchPhoneNumber == null) {
            if (o.branchPhoneNumber != null)
                return false
        }
        else if (this.branchPhoneNumber != o.branchPhoneNumber)
            return false
        if (this.branchFaxNumber == null) {
            if (o.branchFaxNumber != null)
                return false
        }
        else if (this.branchFaxNumber != o.branchFaxNumber)
            return false
        if (this.transactionType == null) {
            if (o.transactionType != null)
                return false
        }
        else if (this.transactionType != o.transactionType)
            return false
        if (this.buildingName == null) {
            if (o.buildingName != null)
                return false
        }
        else if (this.buildingName != o.buildingName)
            return false
        if (this.structureName == null) {
            if (o.structureName != null)
                return false
        }
        else if (this.structureName != o.structureName)
            return false
        if (this.agentAvailableDivision == null) {
            if (o.agentAvailableDivision != null)
                return false
        }
        else if (this.agentAvailableDivision != o.agentAvailableDivision)
            return false
        if (this.subleaseDivision == null) {
            if (o.subleaseDivision != null)
                return false
        }
        else if (this.subleaseDivision != o.subleaseDivision)
            return false
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.creator == null) {
            if (o.creator != null)
                return false
        }
        else if (this.creator != o.creator)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.branchAddress == null) {
            if (o.branchAddress != null)
                return false
        }
        else if (this.branchAddress != o.branchAddress)
            return false
        if (this.recommendationComment == null) {
            if (o.recommendationComment != null)
                return false
        }
        else if (this.recommendationComment != o.recommendationComment)
            return false
        if (this.completionYearMonth == null) {
            if (o.completionYearMonth != null)
                return false
        }
        else if (this.completionYearMonth != o.completionYearMonth)
            return false
        if (this.propertyPostalCode == null) {
            if (o.propertyPostalCode != null)
                return false
        }
        else if (this.propertyPostalCode != o.propertyPostalCode)
            return false
        if (this.recordSeparator == null) {
            if (o.recordSeparator != null)
                return false
        }
        else if (this.recordSeparator != o.recordSeparator)
            return false
        if (this.rentTax == null) {
            if (o.rentTax != null)
                return false
        }
        else if (this.rentTax != o.rentTax)
            return false
        if (this.keyMoneyTax == null) {
            if (o.keyMoneyTax != null)
                return false
        }
        else if (this.keyMoneyTax != o.keyMoneyTax)
            return false
        if (this.keyMoneyTotal == null) {
            if (o.keyMoneyTotal != null)
                return false
        }
        else if (this.keyMoneyTotal != o.keyMoneyTotal)
            return false
        if (this.maintenanceFeeTax == null) {
            if (o.maintenanceFeeTax != null)
                return false
        }
        else if (this.maintenanceFeeTax != o.maintenanceFeeTax)
            return false
        if (this.parkingFeeTax == null) {
            if (o.parkingFeeTax != null)
                return false
        }
        else if (this.parkingFeeTax != o.parkingFeeTax)
            return false
        if (this.changeDivision == null) {
            if (o.changeDivision != null)
                return false
        }
        else if (this.changeDivision != o.changeDivision)
            return false
        if (this.updateFlag == null) {
            if (o.updateFlag != null)
                return false
        }
        else if (this.updateFlag != o.updateFlag)
            return false
        if (this.feature_61 == null) {
            if (o.feature_61 != null)
                return false
        }
        else if (this.feature_61 != o.feature_61)
            return false
        if (this.feature_62 == null) {
            if (o.feature_62 != null)
                return false
        }
        else if (this.feature_62 != o.feature_62)
            return false
        if (this.feature_63 == null) {
            if (o.feature_63 != null)
                return false
        }
        else if (this.feature_63 != o.feature_63)
            return false
        if (this.feature_64 == null) {
            if (o.feature_64 != null)
                return false
        }
        else if (this.feature_64 != o.feature_64)
            return false
        if (this.feature_65 == null) {
            if (o.feature_65 != null)
                return false
        }
        else if (this.feature_65 != o.feature_65)
            return false
        if (this.feature_66 == null) {
            if (o.feature_66 != null)
                return false
        }
        else if (this.feature_66 != o.feature_66)
            return false
        if (this.feature_67 == null) {
            if (o.feature_67 != null)
                return false
        }
        else if (this.feature_67 != o.feature_67)
            return false
        if (this.feature_68 == null) {
            if (o.feature_68 != null)
                return false
        }
        else if (this.feature_68 != o.feature_68)
            return false
        if (this.feature_69 == null) {
            if (o.feature_69 != null)
                return false
        }
        else if (this.feature_69 != o.feature_69)
            return false
        if (this.feature_70 == null) {
            if (o.feature_70 != null)
                return false
        }
        else if (this.feature_70 != o.feature_70)
            return false
        if (this.feature_71 == null) {
            if (o.feature_71 != null)
                return false
        }
        else if (this.feature_71 != o.feature_71)
            return false
        if (this.feature_72 == null) {
            if (o.feature_72 != null)
                return false
        }
        else if (this.feature_72 != o.feature_72)
            return false
        if (this.feature_73 == null) {
            if (o.feature_73 != null)
                return false
        }
        else if (this.feature_73 != o.feature_73)
            return false
        if (this.feature_74 == null) {
            if (o.feature_74 != null)
                return false
        }
        else if (this.feature_74 != o.feature_74)
            return false
        if (this.feature_75 == null) {
            if (o.feature_75 != null)
                return false
        }
        else if (this.feature_75 != o.feature_75)
            return false
        if (this.feature_76 == null) {
            if (o.feature_76 != null)
                return false
        }
        else if (this.feature_76 != o.feature_76)
            return false
        if (this.feature_77 == null) {
            if (o.feature_77 != null)
                return false
        }
        else if (this.feature_77 != o.feature_77)
            return false
        if (this.feature_78 == null) {
            if (o.feature_78 != null)
                return false
        }
        else if (this.feature_78 != o.feature_78)
            return false
        if (this.feature_79 == null) {
            if (o.feature_79 != null)
                return false
        }
        else if (this.feature_79 != o.feature_79)
            return false
        if (this.feature_80 == null) {
            if (o.feature_80 != null)
                return false
        }
        else if (this.feature_80 != o.feature_80)
            return false
        if (this.feature_81 == null) {
            if (o.feature_81 != null)
                return false
        }
        else if (this.feature_81 != o.feature_81)
            return false
        if (this.feature_82 == null) {
            if (o.feature_82 != null)
                return false
        }
        else if (this.feature_82 != o.feature_82)
            return false
        if (this.feature_83 == null) {
            if (o.feature_83 != null)
                return false
        }
        else if (this.feature_83 != o.feature_83)
            return false
        if (this.feature_84 == null) {
            if (o.feature_84 != null)
                return false
        }
        else if (this.feature_84 != o.feature_84)
            return false
        if (this.feature_85 == null) {
            if (o.feature_85 != null)
                return false
        }
        else if (this.feature_85 != o.feature_85)
            return false
        if (this.feature_86 == null) {
            if (o.feature_86 != null)
                return false
        }
        else if (this.feature_86 != o.feature_86)
            return false
        if (this.feature_87 == null) {
            if (o.feature_87 != null)
                return false
        }
        else if (this.feature_87 != o.feature_87)
            return false
        if (this.feature_88 == null) {
            if (o.feature_88 != null)
                return false
        }
        else if (this.feature_88 != o.feature_88)
            return false
        if (this.feature_89 == null) {
            if (o.feature_89 != null)
                return false
        }
        else if (this.feature_89 != o.feature_89)
            return false
        if (this.feature_90 == null) {
            if (o.feature_90 != null)
                return false
        }
        else if (this.feature_90 != o.feature_90)
            return false
        if (this.feature_91 == null) {
            if (o.feature_91 != null)
                return false
        }
        else if (this.feature_91 != o.feature_91)
            return false
        if (this.feature_92 == null) {
            if (o.feature_92 != null)
                return false
        }
        else if (this.feature_92 != o.feature_92)
            return false
        if (this.feature_93 == null) {
            if (o.feature_93 != null)
                return false
        }
        else if (this.feature_93 != o.feature_93)
            return false
        if (this.feature_94 == null) {
            if (o.feature_94 != null)
                return false
        }
        else if (this.feature_94 != o.feature_94)
            return false
        if (this.feature_95 == null) {
            if (o.feature_95 != null)
                return false
        }
        else if (this.feature_95 != o.feature_95)
            return false
        if (this.feature_96 == null) {
            if (o.feature_96 != null)
                return false
        }
        else if (this.feature_96 != o.feature_96)
            return false
        if (this.feature_97 == null) {
            if (o.feature_97 != null)
                return false
        }
        else if (this.feature_97 != o.feature_97)
            return false
        if (this.feature_98 == null) {
            if (o.feature_98 != null)
                return false
        }
        else if (this.feature_98 != o.feature_98)
            return false
        if (this.equipmentFlag_1 == null) {
            if (o.equipmentFlag_1 != null)
                return false
        }
        else if (this.equipmentFlag_1 != o.equipmentFlag_1)
            return false
        if (this.equipmentFlag_2 == null) {
            if (o.equipmentFlag_2 != null)
                return false
        }
        else if (this.equipmentFlag_2 != o.equipmentFlag_2)
            return false
        if (this.equipmentFlag_3 == null) {
            if (o.equipmentFlag_3 != null)
                return false
        }
        else if (this.equipmentFlag_3 != o.equipmentFlag_3)
            return false
        if (this.equipmentFlag_4 == null) {
            if (o.equipmentFlag_4 != null)
                return false
        }
        else if (this.equipmentFlag_4 != o.equipmentFlag_4)
            return false
        if (this.equipmentFlag_5 == null) {
            if (o.equipmentFlag_5 != null)
                return false
        }
        else if (this.equipmentFlag_5 != o.equipmentFlag_5)
            return false
        if (this.equipmentFlag_6 == null) {
            if (o.equipmentFlag_6 != null)
                return false
        }
        else if (this.equipmentFlag_6 != o.equipmentFlag_6)
            return false
        if (this.equipmentFlag_7 == null) {
            if (o.equipmentFlag_7 != null)
                return false
        }
        else if (this.equipmentFlag_7 != o.equipmentFlag_7)
            return false
        if (this.equipmentFlag_8 == null) {
            if (o.equipmentFlag_8 != null)
                return false
        }
        else if (this.equipmentFlag_8 != o.equipmentFlag_8)
            return false
        if (this.equipmentFlag_9 == null) {
            if (o.equipmentFlag_9 != null)
                return false
        }
        else if (this.equipmentFlag_9 != o.equipmentFlag_9)
            return false
        if (this.equipmentFlag_10 == null) {
            if (o.equipmentFlag_10 != null)
                return false
        }
        else if (this.equipmentFlag_10 != o.equipmentFlag_10)
            return false
        if (this.rentalDivision == null) {
            if (o.rentalDivision != null)
                return false
        }
        else if (this.rentalDivision != o.rentalDivision)
            return false
        if (this.distance2 == null) {
            if (o.distance2 != null)
                return false
        }
        else if (this.distance2 != o.distance2)
            return false
        if (this.prefectureCode == null) {
            if (o.prefectureCode != null)
                return false
        }
        else if (this.prefectureCode != o.prefectureCode)
            return false
        if (this.cityWardCode == null) {
            if (o.cityWardCode != null)
                return false
        }
        else if (this.cityWardCode != o.cityWardCode)
            return false
        if (this.townVillageAliasCode == null) {
            if (o.townVillageAliasCode != null)
                return false
        }
        else if (this.townVillageAliasCode != o.townVillageAliasCode)
            return false
        if (this.productNameCode == null) {
            if (o.productNameCode != null)
                return false
        }
        else if (this.productNameCode != o.productNameCode)
            return false
        if (this.approvalDivision == null) {
            if (o.approvalDivision != null)
                return false
        }
        else if (this.approvalDivision != o.approvalDivision)
            return false
        if (this.propertyNameJudgmentSign == null) {
            if (o.propertyNameJudgmentSign != null)
                return false
        }
        else if (this.propertyNameJudgmentSign != o.propertyNameJudgmentSign)
            return false
        if (this.depositZeroFlag == null) {
            if (o.depositZeroFlag != null)
                return false
        }
        else if (this.depositZeroFlag != o.depositZeroFlag)
            return false
        if (this.fletsSupportCd == null) {
            if (o.fletsSupportCd != null)
                return false
        }
        else if (this.fletsSupportCd != o.fletsSupportCd)
            return false
        if (this.skyPerfectSupportCd == null) {
            if (o.skyPerfectSupportCd != null)
                return false
        }
        else if (this.skyPerfectSupportCd != o.skyPerfectSupportCd)
            return false
        if (this.campaignTargetFlag == null) {
            if (o.campaignTargetFlag != null)
                return false
        }
        else if (this.campaignTargetFlag != o.campaignTargetFlag)
            return false
        if (this.propertyAddressDetails == null) {
            if (o.propertyAddressDetails != null)
                return false
        }
        else if (this.propertyAddressDetails != o.propertyAddressDetails)
            return false
        if (this.parking_2CarsAvailable == null) {
            if (o.parking_2CarsAvailable != null)
                return false
        }
        else if (this.parking_2CarsAvailable != o.parking_2CarsAvailable)
            return false
        if (this.newInteriorMaterialUsed == null) {
            if (o.newInteriorMaterialUsed != null)
                return false
        }
        else if (this.newInteriorMaterialUsed != o.newInteriorMaterialUsed)
            return false
        if (this.serviceRoomSign == null) {
            if (o.serviceRoomSign != null)
                return false
        }
        else if (this.serviceRoomSign != o.serviceRoomSign)
            return false
        if (this.highVoltagePowerBulkReception == null) {
            if (o.highVoltagePowerBulkReception != null)
                return false
        }
        else if (this.highVoltagePowerBulkReception != o.highVoltagePowerBulkReception)
            return false
        if (this.highCostRentSign == null) {
            if (o.highCostRentSign != null)
                return false
        }
        else if (this.highCostRentSign != o.highCostRentSign)
            return false
        if (this.layoutDisplayOrder == null) {
            if (o.layoutDisplayOrder != null)
                return false
        }
        else if (this.layoutDisplayOrder != o.layoutDisplayOrder)
            return false
        if (this.prefectureKanjiName == null) {
            if (o.prefectureKanjiName != null)
                return false
        }
        else if (this.prefectureKanjiName != o.prefectureKanjiName)
            return false
        if (this.prefectureKanaName == null) {
            if (o.prefectureKanaName != null)
                return false
        }
        else if (this.prefectureKanaName != o.prefectureKanaName)
            return false
        if (this.cityWardKanjiName == null) {
            if (o.cityWardKanjiName != null)
                return false
        }
        else if (this.cityWardKanjiName != o.cityWardKanjiName)
            return false
        if (this.cityWardKanaName == null) {
            if (o.cityWardKanaName != null)
                return false
        }
        else if (this.cityWardKanaName != o.cityWardKanaName)
            return false
        if (this.townVillageKanjiName == null) {
            if (o.townVillageKanjiName != null)
                return false
        }
        else if (this.townVillageKanjiName != o.townVillageKanjiName)
            return false
        if (this.townVillageKanaName == null) {
            if (o.townVillageKanaName != null)
                return false
        }
        else if (this.townVillageKanaName != o.townVillageKanaName)
            return false
        if (this.keyExchangeSpecialContractTarget == null) {
            if (o.keyExchangeSpecialContractTarget != null)
                return false
        }
        else if (this.keyExchangeSpecialContractTarget != o.keyExchangeSpecialContractTarget)
            return false
        if (this.solarPowerDiscountTarget == null) {
            if (o.solarPowerDiscountTarget != null)
                return false
        }
        else if (this.solarPowerDiscountTarget != o.solarPowerDiscountTarget)
            return false
        if (this.cleaningCostFlatRate == null) {
            if (o.cleaningCostFlatRate != null)
                return false
        }
        else if (this.cleaningCostFlatRate != o.cleaningCostFlatRate)
            return false
        if (this.petFlag == null) {
            if (o.petFlag != null)
                return false
        }
        else if (this.petFlag != o.petFlag)
            return false
        if (this.flagReserve == null) {
            if (o.flagReserve != null)
                return false
        }
        else if (this.flagReserve != o.flagReserve)
            return false
        if (this.cleaningCostTotal == null) {
            if (o.cleaningCostTotal != null)
                return false
        }
        else if (this.cleaningCostTotal != o.cleaningCostTotal)
            return false
        if (this.flagReserve_6 == null) {
            if (o.flagReserve_6 != null)
                return false
        }
        else if (this.flagReserve_6 != o.flagReserve_6)
            return false
        if (this.flagReserve_7 == null) {
            if (o.flagReserve_7 != null)
                return false
        }
        else if (this.flagReserve_7 != o.flagReserve_7)
            return false
        if (this.flagReserve_8 == null) {
            if (o.flagReserve_8 != null)
                return false
        }
        else if (this.flagReserve_8 != o.flagReserve_8)
            return false
        if (this.flagReserve_9 == null) {
            if (o.flagReserve_9 != null)
                return false
        }
        else if (this.flagReserve_9 != o.flagReserve_9)
            return false
        if (this.flagReserve_10 == null) {
            if (o.flagReserve_10 != null)
                return false
        }
        else if (this.flagReserve_10 != o.flagReserve_10)
            return false
        if (this.dateReserve_6 == null) {
            if (o.dateReserve_6 != null)
                return false
        }
        else if (this.dateReserve_6 != o.dateReserve_6)
            return false
        if (this.dateReserve_7 == null) {
            if (o.dateReserve_7 != null)
                return false
        }
        else if (this.dateReserve_7 != o.dateReserve_7)
            return false
        if (this.dateReserve_8 == null) {
            if (o.dateReserve_8 != null)
                return false
        }
        else if (this.dateReserve_8 != o.dateReserve_8)
            return false
        if (this.dateReserve_9 == null) {
            if (o.dateReserve_9 != null)
                return false
        }
        else if (this.dateReserve_9 != o.dateReserve_9)
            return false
        if (this.dateReserve_10 == null) {
            if (o.dateReserve_10 != null)
                return false
        }
        else if (this.dateReserve_10 != o.dateReserve_10)
            return false
        if (this.maintenanceFeeMain == null) {
            if (o.maintenanceFeeMain != null)
                return false
        }
        else if (this.maintenanceFeeMain != o.maintenanceFeeMain)
            return false
        if (this.generalCableTvMain == null) {
            if (o.generalCableTvMain != null)
                return false
        }
        else if (this.generalCableTvMain != o.generalCableTvMain)
            return false
        if (this.generalCableTvTax == null) {
            if (o.generalCableTvTax != null)
                return false
        }
        else if (this.generalCableTvTax != o.generalCableTvTax)
            return false
        if (this.generalInternetMain == null) {
            if (o.generalInternetMain != null)
                return false
        }
        else if (this.generalInternetMain != o.generalInternetMain)
            return false
        if (this.generalInternetTax == null) {
            if (o.generalInternetTax != null)
                return false
        }
        else if (this.generalInternetTax != o.generalInternetTax)
            return false
        if (this.generalWaterQualityMaintenanceMain == null) {
            if (o.generalWaterQualityMaintenanceMain != null)
                return false
        }
        else if (this.generalWaterQualityMaintenanceMain != o.generalWaterQualityMaintenanceMain)
            return false
        if (this.generalWaterQualityMaintenanceTax == null) {
            if (o.generalWaterQualityMaintenanceTax != null)
                return false
        }
        else if (this.generalWaterQualityMaintenanceTax != o.generalWaterQualityMaintenanceTax)
            return false
        if (this.generalTenantWaterMain == null) {
            if (o.generalTenantWaterMain != null)
                return false
        }
        else if (this.generalTenantWaterMain != o.generalTenantWaterMain)
            return false
        if (this.generalTenantWaterTax == null) {
            if (o.generalTenantWaterTax != null)
                return false
        }
        else if (this.generalTenantWaterTax != o.generalTenantWaterTax)
            return false
        if (this.generalDrainageUseMain == null) {
            if (o.generalDrainageUseMain != null)
                return false
        }
        else if (this.generalDrainageUseMain != o.generalDrainageUseMain)
            return false
        if (this.generalDrainageUseTax == null) {
            if (o.generalDrainageUseTax != null)
                return false
        }
        else if (this.generalDrainageUseTax != o.generalDrainageUseTax)
            return false
        if (this.generalGarbageCollectionMain == null) {
            if (o.generalGarbageCollectionMain != null)
                return false
        }
        else if (this.generalGarbageCollectionMain != o.generalGarbageCollectionMain)
            return false
        if (this.generalGarbageCollectionTax == null) {
            if (o.generalGarbageCollectionTax != null)
                return false
        }
        else if (this.generalGarbageCollectionTax != o.generalGarbageCollectionTax)
            return false
        if (this.generalCommonAntennaMain == null) {
            if (o.generalCommonAntennaMain != null)
                return false
        }
        else if (this.generalCommonAntennaMain != o.generalCommonAntennaMain)
            return false
        if (this.generalCommonAntennaTax == null) {
            if (o.generalCommonAntennaTax != null)
                return false
        }
        else if (this.generalCommonAntennaTax != o.generalCommonAntennaTax)
            return false
        if (this.generalLandlordCleaningMain == null) {
            if (o.generalLandlordCleaningMain != null)
                return false
        }
        else if (this.generalLandlordCleaningMain != o.generalLandlordCleaningMain)
            return false
        if (this.generalLandlordCleaningTax == null) {
            if (o.generalLandlordCleaningTax != null)
                return false
        }
        else if (this.generalLandlordCleaningTax != o.generalLandlordCleaningTax)
            return false
        if (this.generalBuildingMaintenanceMain == null) {
            if (o.generalBuildingMaintenanceMain != null)
                return false
        }
        else if (this.generalBuildingMaintenanceMain != o.generalBuildingMaintenanceMain)
            return false
        if (this.generalBuildingMaintenanceTax == null) {
            if (o.generalBuildingMaintenanceTax != null)
                return false
        }
        else if (this.generalBuildingMaintenanceTax != o.generalBuildingMaintenanceTax)
            return false
        if (this.generalBuildingManagementMain == null) {
            if (o.generalBuildingManagementMain != null)
                return false
        }
        else if (this.generalBuildingManagementMain != o.generalBuildingManagementMain)
            return false
        if (this.generalBuildingManagementTax == null) {
            if (o.generalBuildingManagementTax != null)
                return false
        }
        else if (this.generalBuildingManagementTax != o.generalBuildingManagementTax)
            return false
        if (this.generalNeighborhoodAssociationMain == null) {
            if (o.generalNeighborhoodAssociationMain != null)
                return false
        }
        else if (this.generalNeighborhoodAssociationMain != o.generalNeighborhoodAssociationMain)
            return false
        if (this.generalNeighborhoodAssociationTax == null) {
            if (o.generalNeighborhoodAssociationTax != null)
                return false
        }
        else if (this.generalNeighborhoodAssociationTax != o.generalNeighborhoodAssociationTax)
            return false
        if (this.generalNeighborhoodAssocOtherMain == null) {
            if (o.generalNeighborhoodAssocOtherMain != null)
                return false
        }
        else if (this.generalNeighborhoodAssocOtherMain != o.generalNeighborhoodAssocOtherMain)
            return false
        if (this.generalNeighborhoodAssocOtherTax == null) {
            if (o.generalNeighborhoodAssocOtherTax != null)
                return false
        }
        else if (this.generalNeighborhoodAssocOtherTax != o.generalNeighborhoodAssocOtherTax)
            return false
        if (this.generalRepaymentAgencyMain == null) {
            if (o.generalRepaymentAgencyMain != null)
                return false
        }
        else if (this.generalRepaymentAgencyMain != o.generalRepaymentAgencyMain)
            return false
        if (this.generalRepaymentAgencyTax == null) {
            if (o.generalRepaymentAgencyTax != null)
                return false
        }
        else if (this.generalRepaymentAgencyTax != o.generalRepaymentAgencyTax)
            return false
        if (this.generalHlCommissionMain == null) {
            if (o.generalHlCommissionMain != null)
                return false
        }
        else if (this.generalHlCommissionMain != o.generalHlCommissionMain)
            return false
        if (this.generalHlCommissionTax == null) {
            if (o.generalHlCommissionTax != null)
                return false
        }
        else if (this.generalHlCommissionTax != o.generalHlCommissionTax)
            return false
        if (this.generalFurnitureIncludedMain == null) {
            if (o.generalFurnitureIncludedMain != null)
                return false
        }
        else if (this.generalFurnitureIncludedMain != o.generalFurnitureIncludedMain)
            return false
        if (this.generalFurnitureIncludedTax == null) {
            if (o.generalFurnitureIncludedTax != null)
                return false
        }
        else if (this.generalFurnitureIncludedTax != o.generalFurnitureIncludedTax)
            return false
        if (this.generalTenantDepositMain == null) {
            if (o.generalTenantDepositMain != null)
                return false
        }
        else if (this.generalTenantDepositMain != o.generalTenantDepositMain)
            return false
        if (this.generalTenantDepositTax == null) {
            if (o.generalTenantDepositTax != null)
                return false
        }
        else if (this.generalTenantDepositTax != o.generalTenantDepositTax)
            return false
        if (this.generalRentalMain == null) {
            if (o.generalRentalMain != null)
                return false
        }
        else if (this.generalRentalMain != o.generalRentalMain)
            return false
        if (this.generalRentalTax == null) {
            if (o.generalRentalTax != null)
                return false
        }
        else if (this.generalRentalTax != o.generalRentalTax)
            return false
        if (this.reserveAmount_1Main == null) {
            if (o.reserveAmount_1Main != null)
                return false
        }
        else if (this.reserveAmount_1Main != o.reserveAmount_1Main)
            return false
        if (this.reserveAmount_1Tax == null) {
            if (o.reserveAmount_1Tax != null)
                return false
        }
        else if (this.reserveAmount_1Tax != o.reserveAmount_1Tax)
            return false
        if (this.reserveAmount_2Main == null) {
            if (o.reserveAmount_2Main != null)
                return false
        }
        else if (this.reserveAmount_2Main != o.reserveAmount_2Main)
            return false
        if (this.reserveAmount_2Tax == null) {
            if (o.reserveAmount_2Tax != null)
                return false
        }
        else if (this.reserveAmount_2Tax != o.reserveAmount_2Tax)
            return false
        if (this.reserveAmount_3Main == null) {
            if (o.reserveAmount_3Main != null)
                return false
        }
        else if (this.reserveAmount_3Main != o.reserveAmount_3Main)
            return false
        if (this.reserveAmount_3Tax == null) {
            if (o.reserveAmount_3Tax != null)
                return false
        }
        else if (this.reserveAmount_3Tax != o.reserveAmount_3Tax)
            return false
        if (this.flagReserve_11 == null) {
            if (o.flagReserve_11 != null)
                return false
        }
        else if (this.flagReserve_11 != o.flagReserve_11)
            return false
        if (this.flagReserve_12 == null) {
            if (o.flagReserve_12 != null)
                return false
        }
        else if (this.flagReserve_12 != o.flagReserve_12)
            return false
        if (this.flagReserve_13 == null) {
            if (o.flagReserve_13 != null)
                return false
        }
        else if (this.flagReserve_13 != o.flagReserve_13)
            return false
        if (this.flagReserve_14 == null) {
            if (o.flagReserve_14 != null)
                return false
        }
        else if (this.flagReserve_14 != o.flagReserve_14)
            return false
        if (this.flagReserve_15 == null) {
            if (o.flagReserve_15 != null)
                return false
        }
        else if (this.flagReserve_15 != o.flagReserve_15)
            return false
        if (this.divisionReserve_1 == null) {
            if (o.divisionReserve_1 != null)
                return false
        }
        else if (this.divisionReserve_1 != o.divisionReserve_1)
            return false
        if (this.divisionReserve_2 == null) {
            if (o.divisionReserve_2 != null)
                return false
        }
        else if (this.divisionReserve_2 != o.divisionReserve_2)
            return false
        if (this.divisionReserve_3 == null) {
            if (o.divisionReserve_3 != null)
                return false
        }
        else if (this.divisionReserve_3 != o.divisionReserve_3)
            return false
        if (this.divisionReserve_4 == null) {
            if (o.divisionReserve_4 != null)
                return false
        }
        else if (this.divisionReserve_4 != o.divisionReserve_4)
            return false
        if (this.divisionReserve_5 == null) {
            if (o.divisionReserve_5 != null)
                return false
        }
        else if (this.divisionReserve_5 != o.divisionReserve_5)
            return false
        if (this.amountReserve_4 == null) {
            if (o.amountReserve_4 != null)
                return false
        }
        else if (this.amountReserve_4 != o.amountReserve_4)
            return false
        if (this.amountReserve_5 == null) {
            if (o.amountReserve_5 != null)
                return false
        }
        else if (this.amountReserve_5 != o.amountReserve_5)
            return false
        if (this.amountReserve_6 == null) {
            if (o.amountReserve_6 != null)
                return false
        }
        else if (this.amountReserve_6 != o.amountReserve_6)
            return false
        if (this.amountReserve_7 == null) {
            if (o.amountReserve_7 != null)
                return false
        }
        else if (this.amountReserve_7 != o.amountReserve_7)
            return false
        if (this.amountReserve_8 == null) {
            if (o.amountReserve_8 != null)
                return false
        }
        else if (this.amountReserve_8 != o.amountReserve_8)
            return false
        if (this.dateReserve_14 == null) {
            if (o.dateReserve_14 != null)
                return false
        }
        else if (this.dateReserve_14 != o.dateReserve_14)
            return false
        if (this.dateReserve_15 == null) {
            if (o.dateReserve_15 != null)
                return false
        }
        else if (this.dateReserve_15 != o.dateReserve_15)
            return false
        if (this.dateReserve_16 == null) {
            if (o.dateReserve_16 != null)
                return false
        }
        else if (this.dateReserve_16 != o.dateReserve_16)
            return false
        if (this.dateReserve_17 == null) {
            if (o.dateReserve_17 != null)
                return false
        }
        else if (this.dateReserve_17 != o.dateReserve_17)
            return false
        if (this.dateReserve_18 == null) {
            if (o.dateReserve_18 != null)
                return false
        }
        else if (this.dateReserve_18 != o.dateReserve_18)
            return false
        if (this.division_1DigitReserve_1 == null) {
            if (o.division_1DigitReserve_1 != null)
                return false
        }
        else if (this.division_1DigitReserve_1 != o.division_1DigitReserve_1)
            return false
        if (this.division_1DigitReserve_2 == null) {
            if (o.division_1DigitReserve_2 != null)
                return false
        }
        else if (this.division_1DigitReserve_2 != o.division_1DigitReserve_2)
            return false
        if (this.division_1DigitReserve_3 == null) {
            if (o.division_1DigitReserve_3 != null)
                return false
        }
        else if (this.division_1DigitReserve_3 != o.division_1DigitReserve_3)
            return false
        if (this.division_1DigitReserve_4 == null) {
            if (o.division_1DigitReserve_4 != null)
                return false
        }
        else if (this.division_1DigitReserve_4 != o.division_1DigitReserve_4)
            return false
        if (this.division_1DigitReserve_5 == null) {
            if (o.division_1DigitReserve_5 != null)
                return false
        }
        else if (this.division_1DigitReserve_5 != o.division_1DigitReserve_5)
            return false
        if (this.leasingStoreCd == null) {
            if (o.leasingStoreCd != null)
                return false
        }
        else if (this.leasingStoreCd != o.leasingStoreCd)
            return false
        if (this.managementBranchCd == null) {
            if (o.managementBranchCd != null)
                return false
        }
        else if (this.managementBranchCd != o.managementBranchCd)
            return false
        if (this.officeCd == null) {
            if (o.officeCd != null)
                return false
        }
        else if (this.officeCd != o.officeCd)
            return false
        if (this.reviewBranchCd == null) {
            if (o.reviewBranchCd != null)
                return false
        }
        else if (this.reviewBranchCd != o.reviewBranchCd)
            return false
        if (this.feature_100 == null) {
            if (o.feature_100 != null)
                return false
        }
        else if (this.feature_100 != o.feature_100)
            return false
        if (this.feature_101 == null) {
            if (o.feature_101 != null)
                return false
        }
        else if (this.feature_101 != o.feature_101)
            return false
        if (this.feature_102 == null) {
            if (o.feature_102 != null)
                return false
        }
        else if (this.feature_102 != o.feature_102)
            return false
        if (this.feature_103 == null) {
            if (o.feature_103 != null)
                return false
        }
        else if (this.feature_103 != o.feature_103)
            return false
        if (this.feature_104 == null) {
            if (o.feature_104 != null)
                return false
        }
        else if (this.feature_104 != o.feature_104)
            return false
        if (this.feature_105 == null) {
            if (o.feature_105 != null)
                return false
        }
        else if (this.feature_105 != o.feature_105)
            return false
        if (this.feature_106 == null) {
            if (o.feature_106 != null)
                return false
        }
        else if (this.feature_106 != o.feature_106)
            return false
        if (this.feature_107 == null) {
            if (o.feature_107 != null)
                return false
        }
        else if (this.feature_107 != o.feature_107)
            return false
        if (this.feature_108 == null) {
            if (o.feature_108 != null)
                return false
        }
        else if (this.feature_108 != o.feature_108)
            return false
        if (this.feature_109 == null) {
            if (o.feature_109 != null)
                return false
        }
        else if (this.feature_109 != o.feature_109)
            return false
        if (this.feature_110 == null) {
            if (o.feature_110 != null)
                return false
        }
        else if (this.feature_110 != o.feature_110)
            return false
        if (this.feature_111 == null) {
            if (o.feature_111 != null)
                return false
        }
        else if (this.feature_111 != o.feature_111)
            return false
        if (this.feature_112 == null) {
            if (o.feature_112 != null)
                return false
        }
        else if (this.feature_112 != o.feature_112)
            return false
        if (this.feature_113 == null) {
            if (o.feature_113 != null)
                return false
        }
        else if (this.feature_113 != o.feature_113)
            return false
        if (this.feature_114 == null) {
            if (o.feature_114 != null)
                return false
        }
        else if (this.feature_114 != o.feature_114)
            return false
        if (this.feature_115 == null) {
            if (o.feature_115 != null)
                return false
        }
        else if (this.feature_115 != o.feature_115)
            return false
        if (this.feature_116 == null) {
            if (o.feature_116 != null)
                return false
        }
        else if (this.feature_116 != o.feature_116)
            return false
        if (this.feature_117 == null) {
            if (o.feature_117 != null)
                return false
        }
        else if (this.feature_117 != o.feature_117)
            return false
        if (this.feature_118 == null) {
            if (o.feature_118 != null)
                return false
        }
        else if (this.feature_118 != o.feature_118)
            return false
        if (this.feature_119 == null) {
            if (o.feature_119 != null)
                return false
        }
        else if (this.feature_119 != o.feature_119)
            return false
        if (this.feature_120 == null) {
            if (o.feature_120 != null)
                return false
        }
        else if (this.feature_120 != o.feature_120)
            return false
        if (this.feature_121 == null) {
            if (o.feature_121 != null)
                return false
        }
        else if (this.feature_121 != o.feature_121)
            return false
        if (this.feature_122 == null) {
            if (o.feature_122 != null)
                return false
        }
        else if (this.feature_122 != o.feature_122)
            return false
        if (this.feature_123 == null) {
            if (o.feature_123 != null)
                return false
        }
        else if (this.feature_123 != o.feature_123)
            return false
        if (this.feature_124 == null) {
            if (o.feature_124 != null)
                return false
        }
        else if (this.feature_124 != o.feature_124)
            return false
        if (this.feature_125 == null) {
            if (o.feature_125 != null)
                return false
        }
        else if (this.feature_125 != o.feature_125)
            return false
        if (this.feature_126 == null) {
            if (o.feature_126 != null)
                return false
        }
        else if (this.feature_126 != o.feature_126)
            return false
        if (this.feature_127 == null) {
            if (o.feature_127 != null)
                return false
        }
        else if (this.feature_127 != o.feature_127)
            return false
        if (this.feature_128 == null) {
            if (o.feature_128 != null)
                return false
        }
        else if (this.feature_128 != o.feature_128)
            return false
        if (this.feature_129 == null) {
            if (o.feature_129 != null)
                return false
        }
        else if (this.feature_129 != o.feature_129)
            return false
        if (this.feature_130 == null) {
            if (o.feature_130 != null)
                return false
        }
        else if (this.feature_130 != o.feature_130)
            return false
        if (this.feature_131 == null) {
            if (o.feature_131 != null)
                return false
        }
        else if (this.feature_131 != o.feature_131)
            return false
        if (this.feature_132 == null) {
            if (o.feature_132 != null)
                return false
        }
        else if (this.feature_132 != o.feature_132)
            return false
        if (this.feature_133 == null) {
            if (o.feature_133 != null)
                return false
        }
        else if (this.feature_133 != o.feature_133)
            return false
        if (this.feature_134 == null) {
            if (o.feature_134 != null)
                return false
        }
        else if (this.feature_134 != o.feature_134)
            return false
        if (this.feature_135 == null) {
            if (o.feature_135 != null)
                return false
        }
        else if (this.feature_135 != o.feature_135)
            return false
        if (this.feature_136 == null) {
            if (o.feature_136 != null)
                return false
        }
        else if (this.feature_136 != o.feature_136)
            return false
        if (this.feature_137 == null) {
            if (o.feature_137 != null)
                return false
        }
        else if (this.feature_137 != o.feature_137)
            return false
        if (this.feature_138 == null) {
            if (o.feature_138 != null)
                return false
        }
        else if (this.feature_138 != o.feature_138)
            return false
        if (this.feature_139 == null) {
            if (o.feature_139 != null)
                return false
        }
        else if (this.feature_139 != o.feature_139)
            return false
        if (this.feature_140 == null) {
            if (o.feature_140 != null)
                return false
        }
        else if (this.feature_140 != o.feature_140)
            return false
        if (this.feature_141 == null) {
            if (o.feature_141 != null)
                return false
        }
        else if (this.feature_141 != o.feature_141)
            return false
        if (this.feature_142 == null) {
            if (o.feature_142 != null)
                return false
        }
        else if (this.feature_142 != o.feature_142)
            return false
        if (this.feature_143 == null) {
            if (o.feature_143 != null)
                return false
        }
        else if (this.feature_143 != o.feature_143)
            return false
        if (this.feature_144 == null) {
            if (o.feature_144 != null)
                return false
        }
        else if (this.feature_144 != o.feature_144)
            return false
        if (this.feature_145 == null) {
            if (o.feature_145 != null)
                return false
        }
        else if (this.feature_145 != o.feature_145)
            return false
        if (this.feature_146 == null) {
            if (o.feature_146 != null)
                return false
        }
        else if (this.feature_146 != o.feature_146)
            return false
        if (this.feature_147 == null) {
            if (o.feature_147 != null)
                return false
        }
        else if (this.feature_147 != o.feature_147)
            return false
        if (this.feature_148 == null) {
            if (o.feature_148 != null)
                return false
        }
        else if (this.feature_148 != o.feature_148)
            return false
        if (this.feature_149 == null) {
            if (o.feature_149 != null)
                return false
        }
        else if (this.feature_149 != o.feature_149)
            return false
        if (this.feature_150 == null) {
            if (o.feature_150 != null)
                return false
        }
        else if (this.feature_150 != o.feature_150)
            return false
        if (this.feature_151 == null) {
            if (o.feature_151 != null)
                return false
        }
        else if (this.feature_151 != o.feature_151)
            return false
        if (this.feature_152 == null) {
            if (o.feature_152 != null)
                return false
        }
        else if (this.feature_152 != o.feature_152)
            return false
        if (this.feature_153 == null) {
            if (o.feature_153 != null)
                return false
        }
        else if (this.feature_153 != o.feature_153)
            return false
        if (this.feature_154 == null) {
            if (o.feature_154 != null)
                return false
        }
        else if (this.feature_154 != o.feature_154)
            return false
        if (this.feature_155 == null) {
            if (o.feature_155 != null)
                return false
        }
        else if (this.feature_155 != o.feature_155)
            return false
        if (this.feature_156 == null) {
            if (o.feature_156 != null)
                return false
        }
        else if (this.feature_156 != o.feature_156)
            return false
        if (this.feature_157 == null) {
            if (o.feature_157 != null)
                return false
        }
        else if (this.feature_157 != o.feature_157)
            return false
        if (this.feature_158 == null) {
            if (o.feature_158 != null)
                return false
        }
        else if (this.feature_158 != o.feature_158)
            return false
        if (this.feature_159 == null) {
            if (o.feature_159 != null)
                return false
        }
        else if (this.feature_159 != o.feature_159)
            return false
        if (this.feature_160 == null) {
            if (o.feature_160 != null)
                return false
        }
        else if (this.feature_160 != o.feature_160)
            return false
        if (this.feature_161 == null) {
            if (o.feature_161 != null)
                return false
        }
        else if (this.feature_161 != o.feature_161)
            return false
        if (this.feature_162 == null) {
            if (o.feature_162 != null)
                return false
        }
        else if (this.feature_162 != o.feature_162)
            return false
        if (this.feature_163 == null) {
            if (o.feature_163 != null)
                return false
        }
        else if (this.feature_163 != o.feature_163)
            return false
        if (this.feature_164 == null) {
            if (o.feature_164 != null)
                return false
        }
        else if (this.feature_164 != o.feature_164)
            return false
        if (this.feature_165 == null) {
            if (o.feature_165 != null)
                return false
        }
        else if (this.feature_165 != o.feature_165)
            return false
        if (this.feature_166 == null) {
            if (o.feature_166 != null)
                return false
        }
        else if (this.feature_166 != o.feature_166)
            return false
        if (this.feature_167 == null) {
            if (o.feature_167 != null)
                return false
        }
        else if (this.feature_167 != o.feature_167)
            return false
        if (this.feature_168 == null) {
            if (o.feature_168 != null)
                return false
        }
        else if (this.feature_168 != o.feature_168)
            return false
        if (this.feature_169 == null) {
            if (o.feature_169 != null)
                return false
        }
        else if (this.feature_169 != o.feature_169)
            return false
        if (this.feature_170 == null) {
            if (o.feature_170 != null)
                return false
        }
        else if (this.feature_170 != o.feature_170)
            return false
        if (this.feature_171 == null) {
            if (o.feature_171 != null)
                return false
        }
        else if (this.feature_171 != o.feature_171)
            return false
        if (this.feature_172 == null) {
            if (o.feature_172 != null)
                return false
        }
        else if (this.feature_172 != o.feature_172)
            return false
        if (this.feature_173 == null) {
            if (o.feature_173 != null)
                return false
        }
        else if (this.feature_173 != o.feature_173)
            return false
        if (this.feature_174 == null) {
            if (o.feature_174 != null)
                return false
        }
        else if (this.feature_174 != o.feature_174)
            return false
        if (this.feature_175 == null) {
            if (o.feature_175 != null)
                return false
        }
        else if (this.feature_175 != o.feature_175)
            return false
        if (this.feature_176 == null) {
            if (o.feature_176 != null)
                return false
        }
        else if (this.feature_176 != o.feature_176)
            return false
        if (this.feature_177 == null) {
            if (o.feature_177 != null)
                return false
        }
        else if (this.feature_177 != o.feature_177)
            return false
        if (this.feature_178 == null) {
            if (o.feature_178 != null)
                return false
        }
        else if (this.feature_178 != o.feature_178)
            return false
        if (this.feature_179 == null) {
            if (o.feature_179 != null)
                return false
        }
        else if (this.feature_179 != o.feature_179)
            return false
        if (this.feature_180 == null) {
            if (o.feature_180 != null)
                return false
        }
        else if (this.feature_180 != o.feature_180)
            return false
        if (this.feature_181 == null) {
            if (o.feature_181 != null)
                return false
        }
        else if (this.feature_181 != o.feature_181)
            return false
        if (this.feature_182 == null) {
            if (o.feature_182 != null)
                return false
        }
        else if (this.feature_182 != o.feature_182)
            return false
        if (this.feature_183 == null) {
            if (o.feature_183 != null)
                return false
        }
        else if (this.feature_183 != o.feature_183)
            return false
        if (this.feature_184 == null) {
            if (o.feature_184 != null)
                return false
        }
        else if (this.feature_184 != o.feature_184)
            return false
        if (this.feature_185 == null) {
            if (o.feature_185 != null)
                return false
        }
        else if (this.feature_185 != o.feature_185)
            return false
        if (this.feature_186 == null) {
            if (o.feature_186 != null)
                return false
        }
        else if (this.feature_186 != o.feature_186)
            return false
        if (this.feature_187 == null) {
            if (o.feature_187 != null)
                return false
        }
        else if (this.feature_187 != o.feature_187)
            return false
        if (this.feature_188 == null) {
            if (o.feature_188 != null)
                return false
        }
        else if (this.feature_188 != o.feature_188)
            return false
        if (this.feature_189 == null) {
            if (o.feature_189 != null)
                return false
        }
        else if (this.feature_189 != o.feature_189)
            return false
        if (this.feature_190 == null) {
            if (o.feature_190 != null)
                return false
        }
        else if (this.feature_190 != o.feature_190)
            return false
        if (this.feature_191 == null) {
            if (o.feature_191 != null)
                return false
        }
        else if (this.feature_191 != o.feature_191)
            return false
        if (this.feature_192 == null) {
            if (o.feature_192 != null)
                return false
        }
        else if (this.feature_192 != o.feature_192)
            return false
        if (this.feature_193 == null) {
            if (o.feature_193 != null)
                return false
        }
        else if (this.feature_193 != o.feature_193)
            return false
        if (this.feature_194 == null) {
            if (o.feature_194 != null)
                return false
        }
        else if (this.feature_194 != o.feature_194)
            return false
        if (this.feature_195 == null) {
            if (o.feature_195 != null)
                return false
        }
        else if (this.feature_195 != o.feature_195)
            return false
        if (this.feature_196 == null) {
            if (o.feature_196 != null)
                return false
        }
        else if (this.feature_196 != o.feature_196)
            return false
        if (this.feature_197 == null) {
            if (o.feature_197 != null)
                return false
        }
        else if (this.feature_197 != o.feature_197)
            return false
        if (this.feature_198 == null) {
            if (o.feature_198 != null)
                return false
        }
        else if (this.feature_198 != o.feature_198)
            return false
        if (this.feature_199 == null) {
            if (o.feature_199 != null)
                return false
        }
        else if (this.feature_199 != o.feature_199)
            return false
        if (this.marketingBranchOfficeCd == null) {
            if (o.marketingBranchOfficeCd != null)
                return false
        }
        else if (this.marketingBranchOfficeCd != o.marketingBranchOfficeCd)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.propertyCdDivision == null) 0 else this.propertyCdDivision.hashCode())
        result = prime * result + (if (this.propertyCdSeparator_1 == null) 0 else this.propertyCdSeparator_1.hashCode())
        result = prime * result + (if (this.propertyBuildingCd == null) 0 else this.propertyBuildingCd.hashCode())
        result = prime * result + (if (this.propertyCdSeparator_2 == null) 0 else this.propertyCdSeparator_2.hashCode())
        result = prime * result + (if (this.propertyRoomCd == null) 0 else this.propertyRoomCd.hashCode())
        result = prime * result + (if (this.deleteFlag == null) 0 else this.deleteFlag.hashCode())
        result = prime * result + (if (this.customerCompanyCd == null) 0 else this.customerCompanyCd.hashCode())
        result = prime * result + (if (this.customerBranchCd == null) 0 else this.customerBranchCd.hashCode())
        result = prime * result + (if (this.customerDepartmentCd == null) 0 else this.customerDepartmentCd.hashCode())
        result = prime * result + (if (this.customerCompletionFlag == null) 0 else this.customerCompletionFlag.hashCode())
        result = prime * result + (if (this.municipalityCd == null) 0 else this.municipalityCd.hashCode())
        result = prime * result + (if (this.lineCd == null) 0 else this.lineCd.hashCode())
        result = prime * result + (if (this.stationCd == null) 0 else this.stationCd.hashCode())
        result = prime * result + (if (this.rent == null) 0 else this.rent.hashCode())
        result = prime * result + (if (this.layoutRooms == null) 0 else this.layoutRooms.hashCode())
        result = prime * result + (if (this.exclusiveArea == null) 0 else this.exclusiveArea.hashCode())
        result = prime * result + (if (this.propertyType == null) 0 else this.propertyType.hashCode())
        result = prime * result + (if (this.feature_1 == null) 0 else this.feature_1.hashCode())
        result = prime * result + (if (this.feature_2 == null) 0 else this.feature_2.hashCode())
        result = prime * result + (if (this.feature_3 == null) 0 else this.feature_3.hashCode())
        result = prime * result + (if (this.feature_4 == null) 0 else this.feature_4.hashCode())
        result = prime * result + (if (this.feature_5 == null) 0 else this.feature_5.hashCode())
        result = prime * result + (if (this.feature_6 == null) 0 else this.feature_6.hashCode())
        result = prime * result + (if (this.feature_7 == null) 0 else this.feature_7.hashCode())
        result = prime * result + (if (this.feature_8 == null) 0 else this.feature_8.hashCode())
        result = prime * result + (if (this.feature_9 == null) 0 else this.feature_9.hashCode())
        result = prime * result + (if (this.feature_10 == null) 0 else this.feature_10.hashCode())
        result = prime * result + (if (this.feature_11 == null) 0 else this.feature_11.hashCode())
        result = prime * result + (if (this.feature_12 == null) 0 else this.feature_12.hashCode())
        result = prime * result + (if (this.feature_13 == null) 0 else this.feature_13.hashCode())
        result = prime * result + (if (this.feature_14 == null) 0 else this.feature_14.hashCode())
        result = prime * result + (if (this.feature_15 == null) 0 else this.feature_15.hashCode())
        result = prime * result + (if (this.feature_16 == null) 0 else this.feature_16.hashCode())
        result = prime * result + (if (this.feature_17 == null) 0 else this.feature_17.hashCode())
        result = prime * result + (if (this.feature_18 == null) 0 else this.feature_18.hashCode())
        result = prime * result + (if (this.feature_19 == null) 0 else this.feature_19.hashCode())
        result = prime * result + (if (this.feature_20 == null) 0 else this.feature_20.hashCode())
        result = prime * result + (if (this.feature_21 == null) 0 else this.feature_21.hashCode())
        result = prime * result + (if (this.feature_22 == null) 0 else this.feature_22.hashCode())
        result = prime * result + (if (this.feature_23 == null) 0 else this.feature_23.hashCode())
        result = prime * result + (if (this.feature_24 == null) 0 else this.feature_24.hashCode())
        result = prime * result + (if (this.feature_25 == null) 0 else this.feature_25.hashCode())
        result = prime * result + (if (this.feature_26 == null) 0 else this.feature_26.hashCode())
        result = prime * result + (if (this.feature_27 == null) 0 else this.feature_27.hashCode())
        result = prime * result + (if (this.feature_28 == null) 0 else this.feature_28.hashCode())
        result = prime * result + (if (this.feature_29 == null) 0 else this.feature_29.hashCode())
        result = prime * result + (if (this.feature_30 == null) 0 else this.feature_30.hashCode())
        result = prime * result + (if (this.feature_31 == null) 0 else this.feature_31.hashCode())
        result = prime * result + (if (this.feature_32 == null) 0 else this.feature_32.hashCode())
        result = prime * result + (if (this.feature_33 == null) 0 else this.feature_33.hashCode())
        result = prime * result + (if (this.feature_34 == null) 0 else this.feature_34.hashCode())
        result = prime * result + (if (this.feature_35 == null) 0 else this.feature_35.hashCode())
        result = prime * result + (if (this.feature_36 == null) 0 else this.feature_36.hashCode())
        result = prime * result + (if (this.feature_37 == null) 0 else this.feature_37.hashCode())
        result = prime * result + (if (this.feature_38 == null) 0 else this.feature_38.hashCode())
        result = prime * result + (if (this.feature_39 == null) 0 else this.feature_39.hashCode())
        result = prime * result + (if (this.feature_40 == null) 0 else this.feature_40.hashCode())
        result = prime * result + (if (this.feature_41 == null) 0 else this.feature_41.hashCode())
        result = prime * result + (if (this.feature_42 == null) 0 else this.feature_42.hashCode())
        result = prime * result + (if (this.feature_43 == null) 0 else this.feature_43.hashCode())
        result = prime * result + (if (this.feature_44 == null) 0 else this.feature_44.hashCode())
        result = prime * result + (if (this.feature_45 == null) 0 else this.feature_45.hashCode())
        result = prime * result + (if (this.feature_46 == null) 0 else this.feature_46.hashCode())
        result = prime * result + (if (this.feature_47 == null) 0 else this.feature_47.hashCode())
        result = prime * result + (if (this.feature_48 == null) 0 else this.feature_48.hashCode())
        result = prime * result + (if (this.feature_49 == null) 0 else this.feature_49.hashCode())
        result = prime * result + (if (this.feature_50 == null) 0 else this.feature_50.hashCode())
        result = prime * result + (if (this.feature_51 == null) 0 else this.feature_51.hashCode())
        result = prime * result + (if (this.feature_52 == null) 0 else this.feature_52.hashCode())
        result = prime * result + (if (this.feature_53 == null) 0 else this.feature_53.hashCode())
        result = prime * result + (if (this.feature_54 == null) 0 else this.feature_54.hashCode())
        result = prime * result + (if (this.feature_55 == null) 0 else this.feature_55.hashCode())
        result = prime * result + (if (this.feature_56 == null) 0 else this.feature_56.hashCode())
        result = prime * result + (if (this.feature_57 == null) 0 else this.feature_57.hashCode())
        result = prime * result + (if (this.feature_58 == null) 0 else this.feature_58.hashCode())
        result = prime * result + (if (this.feature_59 == null) 0 else this.feature_59.hashCode())
        result = prime * result + (if (this.feature_60 == null) 0 else this.feature_60.hashCode())
        result = prime * result + (if (this.feature_99 == null) 0 else this.feature_99.hashCode())
        result = prime * result + (if (this.featureNewBuilding == null) 0 else this.featureNewBuilding.hashCode())
        result = prime * result + (if (this.featureCornerRoom == null) 0 else this.featureCornerRoom.hashCode())
        result = prime * result + (if (this.featureAboveSecondFloor == null) 0 else this.featureAboveSecondFloor.hashCode())
        result = prime * result + (if (this.lineName == null) 0 else this.lineName.hashCode())
        result = prime * result + (if (this.stationName == null) 0 else this.stationName.hashCode())
        result = prime * result + (if (this.busStopName == null) 0 else this.busStopName.hashCode())
        result = prime * result + (if (this.busTime == null) 0 else this.busTime.hashCode())
        result = prime * result + (if (this.walkTime == null) 0 else this.walkTime.hashCode())
        result = prime * result + (if (this.distance == null) 0 else this.distance.hashCode())
        result = prime * result + (if (this.keyMoney == null) 0 else this.keyMoney.hashCode())
        result = prime * result + (if (this.deposit == null) 0 else this.deposit.hashCode())
        result = prime * result + (if (this.neighborhoodAssociationFee == null) 0 else this.neighborhoodAssociationFee.hashCode())
        result = prime * result + (if (this.maintenanceFee == null) 0 else this.maintenanceFee.hashCode())
        result = prime * result + (if (this.roomTypeName == null) 0 else this.roomTypeName.hashCode())
        result = prime * result + (if (this.layout == null) 0 else this.layout.hashCode())
        result = prime * result + (if (this.layoutDetails == null) 0 else this.layoutDetails.hashCode())
        result = prime * result + (if (this.parkingDivision == null) 0 else this.parkingDivision.hashCode())
        result = prime * result + (if (this.parkingFee == null) 0 else this.parkingFee.hashCode())
        result = prime * result + (if (this.buildYear == null) 0 else this.buildYear.hashCode())
        result = prime * result + (if (this.handlingStoreCompany == null) 0 else this.handlingStoreCompany.hashCode())
        result = prime * result + (if (this.locationPublishAreaName == null) 0 else this.locationPublishAreaName.hashCode())
        result = prime * result + (if (this.floorCount == null) 0 else this.floorCount.hashCode())
        result = prime * result + (if (this.direction == null) 0 else this.direction.hashCode())
        result = prime * result + (if (this.roomPosition == null) 0 else this.roomPosition.hashCode())
        result = prime * result + (if (this.availableDate == null) 0 else this.availableDate.hashCode())
        result = prime * result + (if (this.transportation == null) 0 else this.transportation.hashCode())
        result = prime * result + (if (this.equipment == null) 0 else this.equipment.hashCode())
        result = prime * result + (if (this.remarks == null) 0 else this.remarks.hashCode())
        result = prime * result + (if (this.contactBranchName == null) 0 else this.contactBranchName.hashCode())
        result = prime * result + (if (this.branchPhoneNumber == null) 0 else this.branchPhoneNumber.hashCode())
        result = prime * result + (if (this.branchFaxNumber == null) 0 else this.branchFaxNumber.hashCode())
        result = prime * result + (if (this.transactionType == null) 0 else this.transactionType.hashCode())
        result = prime * result + (if (this.buildingName == null) 0 else this.buildingName.hashCode())
        result = prime * result + (if (this.structureName == null) 0 else this.structureName.hashCode())
        result = prime * result + (if (this.agentAvailableDivision == null) 0 else this.agentAvailableDivision.hashCode())
        result = prime * result + (if (this.subleaseDivision == null) 0 else this.subleaseDivision.hashCode())
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.creator == null) 0 else this.creator.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.branchAddress == null) 0 else this.branchAddress.hashCode())
        result = prime * result + (if (this.recommendationComment == null) 0 else this.recommendationComment.hashCode())
        result = prime * result + (if (this.completionYearMonth == null) 0 else this.completionYearMonth.hashCode())
        result = prime * result + (if (this.propertyPostalCode == null) 0 else this.propertyPostalCode.hashCode())
        result = prime * result + (if (this.recordSeparator == null) 0 else this.recordSeparator.hashCode())
        result = prime * result + (if (this.rentTax == null) 0 else this.rentTax.hashCode())
        result = prime * result + (if (this.keyMoneyTax == null) 0 else this.keyMoneyTax.hashCode())
        result = prime * result + (if (this.keyMoneyTotal == null) 0 else this.keyMoneyTotal.hashCode())
        result = prime * result + (if (this.maintenanceFeeTax == null) 0 else this.maintenanceFeeTax.hashCode())
        result = prime * result + (if (this.parkingFeeTax == null) 0 else this.parkingFeeTax.hashCode())
        result = prime * result + (if (this.changeDivision == null) 0 else this.changeDivision.hashCode())
        result = prime * result + (if (this.updateFlag == null) 0 else this.updateFlag.hashCode())
        result = prime * result + (if (this.feature_61 == null) 0 else this.feature_61.hashCode())
        result = prime * result + (if (this.feature_62 == null) 0 else this.feature_62.hashCode())
        result = prime * result + (if (this.feature_63 == null) 0 else this.feature_63.hashCode())
        result = prime * result + (if (this.feature_64 == null) 0 else this.feature_64.hashCode())
        result = prime * result + (if (this.feature_65 == null) 0 else this.feature_65.hashCode())
        result = prime * result + (if (this.feature_66 == null) 0 else this.feature_66.hashCode())
        result = prime * result + (if (this.feature_67 == null) 0 else this.feature_67.hashCode())
        result = prime * result + (if (this.feature_68 == null) 0 else this.feature_68.hashCode())
        result = prime * result + (if (this.feature_69 == null) 0 else this.feature_69.hashCode())
        result = prime * result + (if (this.feature_70 == null) 0 else this.feature_70.hashCode())
        result = prime * result + (if (this.feature_71 == null) 0 else this.feature_71.hashCode())
        result = prime * result + (if (this.feature_72 == null) 0 else this.feature_72.hashCode())
        result = prime * result + (if (this.feature_73 == null) 0 else this.feature_73.hashCode())
        result = prime * result + (if (this.feature_74 == null) 0 else this.feature_74.hashCode())
        result = prime * result + (if (this.feature_75 == null) 0 else this.feature_75.hashCode())
        result = prime * result + (if (this.feature_76 == null) 0 else this.feature_76.hashCode())
        result = prime * result + (if (this.feature_77 == null) 0 else this.feature_77.hashCode())
        result = prime * result + (if (this.feature_78 == null) 0 else this.feature_78.hashCode())
        result = prime * result + (if (this.feature_79 == null) 0 else this.feature_79.hashCode())
        result = prime * result + (if (this.feature_80 == null) 0 else this.feature_80.hashCode())
        result = prime * result + (if (this.feature_81 == null) 0 else this.feature_81.hashCode())
        result = prime * result + (if (this.feature_82 == null) 0 else this.feature_82.hashCode())
        result = prime * result + (if (this.feature_83 == null) 0 else this.feature_83.hashCode())
        result = prime * result + (if (this.feature_84 == null) 0 else this.feature_84.hashCode())
        result = prime * result + (if (this.feature_85 == null) 0 else this.feature_85.hashCode())
        result = prime * result + (if (this.feature_86 == null) 0 else this.feature_86.hashCode())
        result = prime * result + (if (this.feature_87 == null) 0 else this.feature_87.hashCode())
        result = prime * result + (if (this.feature_88 == null) 0 else this.feature_88.hashCode())
        result = prime * result + (if (this.feature_89 == null) 0 else this.feature_89.hashCode())
        result = prime * result + (if (this.feature_90 == null) 0 else this.feature_90.hashCode())
        result = prime * result + (if (this.feature_91 == null) 0 else this.feature_91.hashCode())
        result = prime * result + (if (this.feature_92 == null) 0 else this.feature_92.hashCode())
        result = prime * result + (if (this.feature_93 == null) 0 else this.feature_93.hashCode())
        result = prime * result + (if (this.feature_94 == null) 0 else this.feature_94.hashCode())
        result = prime * result + (if (this.feature_95 == null) 0 else this.feature_95.hashCode())
        result = prime * result + (if (this.feature_96 == null) 0 else this.feature_96.hashCode())
        result = prime * result + (if (this.feature_97 == null) 0 else this.feature_97.hashCode())
        result = prime * result + (if (this.feature_98 == null) 0 else this.feature_98.hashCode())
        result = prime * result + (if (this.equipmentFlag_1 == null) 0 else this.equipmentFlag_1.hashCode())
        result = prime * result + (if (this.equipmentFlag_2 == null) 0 else this.equipmentFlag_2.hashCode())
        result = prime * result + (if (this.equipmentFlag_3 == null) 0 else this.equipmentFlag_3.hashCode())
        result = prime * result + (if (this.equipmentFlag_4 == null) 0 else this.equipmentFlag_4.hashCode())
        result = prime * result + (if (this.equipmentFlag_5 == null) 0 else this.equipmentFlag_5.hashCode())
        result = prime * result + (if (this.equipmentFlag_6 == null) 0 else this.equipmentFlag_6.hashCode())
        result = prime * result + (if (this.equipmentFlag_7 == null) 0 else this.equipmentFlag_7.hashCode())
        result = prime * result + (if (this.equipmentFlag_8 == null) 0 else this.equipmentFlag_8.hashCode())
        result = prime * result + (if (this.equipmentFlag_9 == null) 0 else this.equipmentFlag_9.hashCode())
        result = prime * result + (if (this.equipmentFlag_10 == null) 0 else this.equipmentFlag_10.hashCode())
        result = prime * result + (if (this.rentalDivision == null) 0 else this.rentalDivision.hashCode())
        result = prime * result + (if (this.distance2 == null) 0 else this.distance2.hashCode())
        result = prime * result + (if (this.prefectureCode == null) 0 else this.prefectureCode.hashCode())
        result = prime * result + (if (this.cityWardCode == null) 0 else this.cityWardCode.hashCode())
        result = prime * result + (if (this.townVillageAliasCode == null) 0 else this.townVillageAliasCode.hashCode())
        result = prime * result + (if (this.productNameCode == null) 0 else this.productNameCode.hashCode())
        result = prime * result + (if (this.approvalDivision == null) 0 else this.approvalDivision.hashCode())
        result = prime * result + (if (this.propertyNameJudgmentSign == null) 0 else this.propertyNameJudgmentSign.hashCode())
        result = prime * result + (if (this.depositZeroFlag == null) 0 else this.depositZeroFlag.hashCode())
        result = prime * result + (if (this.fletsSupportCd == null) 0 else this.fletsSupportCd.hashCode())
        result = prime * result + (if (this.skyPerfectSupportCd == null) 0 else this.skyPerfectSupportCd.hashCode())
        result = prime * result + (if (this.campaignTargetFlag == null) 0 else this.campaignTargetFlag.hashCode())
        result = prime * result + (if (this.propertyAddressDetails == null) 0 else this.propertyAddressDetails.hashCode())
        result = prime * result + (if (this.parking_2CarsAvailable == null) 0 else this.parking_2CarsAvailable.hashCode())
        result = prime * result + (if (this.newInteriorMaterialUsed == null) 0 else this.newInteriorMaterialUsed.hashCode())
        result = prime * result + (if (this.serviceRoomSign == null) 0 else this.serviceRoomSign.hashCode())
        result = prime * result + (if (this.highVoltagePowerBulkReception == null) 0 else this.highVoltagePowerBulkReception.hashCode())
        result = prime * result + (if (this.highCostRentSign == null) 0 else this.highCostRentSign.hashCode())
        result = prime * result + (if (this.layoutDisplayOrder == null) 0 else this.layoutDisplayOrder.hashCode())
        result = prime * result + (if (this.prefectureKanjiName == null) 0 else this.prefectureKanjiName.hashCode())
        result = prime * result + (if (this.prefectureKanaName == null) 0 else this.prefectureKanaName.hashCode())
        result = prime * result + (if (this.cityWardKanjiName == null) 0 else this.cityWardKanjiName.hashCode())
        result = prime * result + (if (this.cityWardKanaName == null) 0 else this.cityWardKanaName.hashCode())
        result = prime * result + (if (this.townVillageKanjiName == null) 0 else this.townVillageKanjiName.hashCode())
        result = prime * result + (if (this.townVillageKanaName == null) 0 else this.townVillageKanaName.hashCode())
        result = prime * result + (if (this.keyExchangeSpecialContractTarget == null) 0 else this.keyExchangeSpecialContractTarget.hashCode())
        result = prime * result + (if (this.solarPowerDiscountTarget == null) 0 else this.solarPowerDiscountTarget.hashCode())
        result = prime * result + (if (this.cleaningCostFlatRate == null) 0 else this.cleaningCostFlatRate.hashCode())
        result = prime * result + (if (this.petFlag == null) 0 else this.petFlag.hashCode())
        result = prime * result + (if (this.flagReserve == null) 0 else this.flagReserve.hashCode())
        result = prime * result + (if (this.cleaningCostTotal == null) 0 else this.cleaningCostTotal.hashCode())
        result = prime * result + (if (this.flagReserve_6 == null) 0 else this.flagReserve_6.hashCode())
        result = prime * result + (if (this.flagReserve_7 == null) 0 else this.flagReserve_7.hashCode())
        result = prime * result + (if (this.flagReserve_8 == null) 0 else this.flagReserve_8.hashCode())
        result = prime * result + (if (this.flagReserve_9 == null) 0 else this.flagReserve_9.hashCode())
        result = prime * result + (if (this.flagReserve_10 == null) 0 else this.flagReserve_10.hashCode())
        result = prime * result + (if (this.dateReserve_6 == null) 0 else this.dateReserve_6.hashCode())
        result = prime * result + (if (this.dateReserve_7 == null) 0 else this.dateReserve_7.hashCode())
        result = prime * result + (if (this.dateReserve_8 == null) 0 else this.dateReserve_8.hashCode())
        result = prime * result + (if (this.dateReserve_9 == null) 0 else this.dateReserve_9.hashCode())
        result = prime * result + (if (this.dateReserve_10 == null) 0 else this.dateReserve_10.hashCode())
        result = prime * result + (if (this.maintenanceFeeMain == null) 0 else this.maintenanceFeeMain.hashCode())
        result = prime * result + (if (this.generalCableTvMain == null) 0 else this.generalCableTvMain.hashCode())
        result = prime * result + (if (this.generalCableTvTax == null) 0 else this.generalCableTvTax.hashCode())
        result = prime * result + (if (this.generalInternetMain == null) 0 else this.generalInternetMain.hashCode())
        result = prime * result + (if (this.generalInternetTax == null) 0 else this.generalInternetTax.hashCode())
        result = prime * result + (if (this.generalWaterQualityMaintenanceMain == null) 0 else this.generalWaterQualityMaintenanceMain.hashCode())
        result = prime * result + (if (this.generalWaterQualityMaintenanceTax == null) 0 else this.generalWaterQualityMaintenanceTax.hashCode())
        result = prime * result + (if (this.generalTenantWaterMain == null) 0 else this.generalTenantWaterMain.hashCode())
        result = prime * result + (if (this.generalTenantWaterTax == null) 0 else this.generalTenantWaterTax.hashCode())
        result = prime * result + (if (this.generalDrainageUseMain == null) 0 else this.generalDrainageUseMain.hashCode())
        result = prime * result + (if (this.generalDrainageUseTax == null) 0 else this.generalDrainageUseTax.hashCode())
        result = prime * result + (if (this.generalGarbageCollectionMain == null) 0 else this.generalGarbageCollectionMain.hashCode())
        result = prime * result + (if (this.generalGarbageCollectionTax == null) 0 else this.generalGarbageCollectionTax.hashCode())
        result = prime * result + (if (this.generalCommonAntennaMain == null) 0 else this.generalCommonAntennaMain.hashCode())
        result = prime * result + (if (this.generalCommonAntennaTax == null) 0 else this.generalCommonAntennaTax.hashCode())
        result = prime * result + (if (this.generalLandlordCleaningMain == null) 0 else this.generalLandlordCleaningMain.hashCode())
        result = prime * result + (if (this.generalLandlordCleaningTax == null) 0 else this.generalLandlordCleaningTax.hashCode())
        result = prime * result + (if (this.generalBuildingMaintenanceMain == null) 0 else this.generalBuildingMaintenanceMain.hashCode())
        result = prime * result + (if (this.generalBuildingMaintenanceTax == null) 0 else this.generalBuildingMaintenanceTax.hashCode())
        result = prime * result + (if (this.generalBuildingManagementMain == null) 0 else this.generalBuildingManagementMain.hashCode())
        result = prime * result + (if (this.generalBuildingManagementTax == null) 0 else this.generalBuildingManagementTax.hashCode())
        result = prime * result + (if (this.generalNeighborhoodAssociationMain == null) 0 else this.generalNeighborhoodAssociationMain.hashCode())
        result = prime * result + (if (this.generalNeighborhoodAssociationTax == null) 0 else this.generalNeighborhoodAssociationTax.hashCode())
        result = prime * result + (if (this.generalNeighborhoodAssocOtherMain == null) 0 else this.generalNeighborhoodAssocOtherMain.hashCode())
        result = prime * result + (if (this.generalNeighborhoodAssocOtherTax == null) 0 else this.generalNeighborhoodAssocOtherTax.hashCode())
        result = prime * result + (if (this.generalRepaymentAgencyMain == null) 0 else this.generalRepaymentAgencyMain.hashCode())
        result = prime * result + (if (this.generalRepaymentAgencyTax == null) 0 else this.generalRepaymentAgencyTax.hashCode())
        result = prime * result + (if (this.generalHlCommissionMain == null) 0 else this.generalHlCommissionMain.hashCode())
        result = prime * result + (if (this.generalHlCommissionTax == null) 0 else this.generalHlCommissionTax.hashCode())
        result = prime * result + (if (this.generalFurnitureIncludedMain == null) 0 else this.generalFurnitureIncludedMain.hashCode())
        result = prime * result + (if (this.generalFurnitureIncludedTax == null) 0 else this.generalFurnitureIncludedTax.hashCode())
        result = prime * result + (if (this.generalTenantDepositMain == null) 0 else this.generalTenantDepositMain.hashCode())
        result = prime * result + (if (this.generalTenantDepositTax == null) 0 else this.generalTenantDepositTax.hashCode())
        result = prime * result + (if (this.generalRentalMain == null) 0 else this.generalRentalMain.hashCode())
        result = prime * result + (if (this.generalRentalTax == null) 0 else this.generalRentalTax.hashCode())
        result = prime * result + (if (this.reserveAmount_1Main == null) 0 else this.reserveAmount_1Main.hashCode())
        result = prime * result + (if (this.reserveAmount_1Tax == null) 0 else this.reserveAmount_1Tax.hashCode())
        result = prime * result + (if (this.reserveAmount_2Main == null) 0 else this.reserveAmount_2Main.hashCode())
        result = prime * result + (if (this.reserveAmount_2Tax == null) 0 else this.reserveAmount_2Tax.hashCode())
        result = prime * result + (if (this.reserveAmount_3Main == null) 0 else this.reserveAmount_3Main.hashCode())
        result = prime * result + (if (this.reserveAmount_3Tax == null) 0 else this.reserveAmount_3Tax.hashCode())
        result = prime * result + (if (this.flagReserve_11 == null) 0 else this.flagReserve_11.hashCode())
        result = prime * result + (if (this.flagReserve_12 == null) 0 else this.flagReserve_12.hashCode())
        result = prime * result + (if (this.flagReserve_13 == null) 0 else this.flagReserve_13.hashCode())
        result = prime * result + (if (this.flagReserve_14 == null) 0 else this.flagReserve_14.hashCode())
        result = prime * result + (if (this.flagReserve_15 == null) 0 else this.flagReserve_15.hashCode())
        result = prime * result + (if (this.divisionReserve_1 == null) 0 else this.divisionReserve_1.hashCode())
        result = prime * result + (if (this.divisionReserve_2 == null) 0 else this.divisionReserve_2.hashCode())
        result = prime * result + (if (this.divisionReserve_3 == null) 0 else this.divisionReserve_3.hashCode())
        result = prime * result + (if (this.divisionReserve_4 == null) 0 else this.divisionReserve_4.hashCode())
        result = prime * result + (if (this.divisionReserve_5 == null) 0 else this.divisionReserve_5.hashCode())
        result = prime * result + (if (this.amountReserve_4 == null) 0 else this.amountReserve_4.hashCode())
        result = prime * result + (if (this.amountReserve_5 == null) 0 else this.amountReserve_5.hashCode())
        result = prime * result + (if (this.amountReserve_6 == null) 0 else this.amountReserve_6.hashCode())
        result = prime * result + (if (this.amountReserve_7 == null) 0 else this.amountReserve_7.hashCode())
        result = prime * result + (if (this.amountReserve_8 == null) 0 else this.amountReserve_8.hashCode())
        result = prime * result + (if (this.dateReserve_14 == null) 0 else this.dateReserve_14.hashCode())
        result = prime * result + (if (this.dateReserve_15 == null) 0 else this.dateReserve_15.hashCode())
        result = prime * result + (if (this.dateReserve_16 == null) 0 else this.dateReserve_16.hashCode())
        result = prime * result + (if (this.dateReserve_17 == null) 0 else this.dateReserve_17.hashCode())
        result = prime * result + (if (this.dateReserve_18 == null) 0 else this.dateReserve_18.hashCode())
        result = prime * result + (if (this.division_1DigitReserve_1 == null) 0 else this.division_1DigitReserve_1.hashCode())
        result = prime * result + (if (this.division_1DigitReserve_2 == null) 0 else this.division_1DigitReserve_2.hashCode())
        result = prime * result + (if (this.division_1DigitReserve_3 == null) 0 else this.division_1DigitReserve_3.hashCode())
        result = prime * result + (if (this.division_1DigitReserve_4 == null) 0 else this.division_1DigitReserve_4.hashCode())
        result = prime * result + (if (this.division_1DigitReserve_5 == null) 0 else this.division_1DigitReserve_5.hashCode())
        result = prime * result + (if (this.leasingStoreCd == null) 0 else this.leasingStoreCd.hashCode())
        result = prime * result + (if (this.managementBranchCd == null) 0 else this.managementBranchCd.hashCode())
        result = prime * result + (if (this.officeCd == null) 0 else this.officeCd.hashCode())
        result = prime * result + (if (this.reviewBranchCd == null) 0 else this.reviewBranchCd.hashCode())
        result = prime * result + (if (this.feature_100 == null) 0 else this.feature_100.hashCode())
        result = prime * result + (if (this.feature_101 == null) 0 else this.feature_101.hashCode())
        result = prime * result + (if (this.feature_102 == null) 0 else this.feature_102.hashCode())
        result = prime * result + (if (this.feature_103 == null) 0 else this.feature_103.hashCode())
        result = prime * result + (if (this.feature_104 == null) 0 else this.feature_104.hashCode())
        result = prime * result + (if (this.feature_105 == null) 0 else this.feature_105.hashCode())
        result = prime * result + (if (this.feature_106 == null) 0 else this.feature_106.hashCode())
        result = prime * result + (if (this.feature_107 == null) 0 else this.feature_107.hashCode())
        result = prime * result + (if (this.feature_108 == null) 0 else this.feature_108.hashCode())
        result = prime * result + (if (this.feature_109 == null) 0 else this.feature_109.hashCode())
        result = prime * result + (if (this.feature_110 == null) 0 else this.feature_110.hashCode())
        result = prime * result + (if (this.feature_111 == null) 0 else this.feature_111.hashCode())
        result = prime * result + (if (this.feature_112 == null) 0 else this.feature_112.hashCode())
        result = prime * result + (if (this.feature_113 == null) 0 else this.feature_113.hashCode())
        result = prime * result + (if (this.feature_114 == null) 0 else this.feature_114.hashCode())
        result = prime * result + (if (this.feature_115 == null) 0 else this.feature_115.hashCode())
        result = prime * result + (if (this.feature_116 == null) 0 else this.feature_116.hashCode())
        result = prime * result + (if (this.feature_117 == null) 0 else this.feature_117.hashCode())
        result = prime * result + (if (this.feature_118 == null) 0 else this.feature_118.hashCode())
        result = prime * result + (if (this.feature_119 == null) 0 else this.feature_119.hashCode())
        result = prime * result + (if (this.feature_120 == null) 0 else this.feature_120.hashCode())
        result = prime * result + (if (this.feature_121 == null) 0 else this.feature_121.hashCode())
        result = prime * result + (if (this.feature_122 == null) 0 else this.feature_122.hashCode())
        result = prime * result + (if (this.feature_123 == null) 0 else this.feature_123.hashCode())
        result = prime * result + (if (this.feature_124 == null) 0 else this.feature_124.hashCode())
        result = prime * result + (if (this.feature_125 == null) 0 else this.feature_125.hashCode())
        result = prime * result + (if (this.feature_126 == null) 0 else this.feature_126.hashCode())
        result = prime * result + (if (this.feature_127 == null) 0 else this.feature_127.hashCode())
        result = prime * result + (if (this.feature_128 == null) 0 else this.feature_128.hashCode())
        result = prime * result + (if (this.feature_129 == null) 0 else this.feature_129.hashCode())
        result = prime * result + (if (this.feature_130 == null) 0 else this.feature_130.hashCode())
        result = prime * result + (if (this.feature_131 == null) 0 else this.feature_131.hashCode())
        result = prime * result + (if (this.feature_132 == null) 0 else this.feature_132.hashCode())
        result = prime * result + (if (this.feature_133 == null) 0 else this.feature_133.hashCode())
        result = prime * result + (if (this.feature_134 == null) 0 else this.feature_134.hashCode())
        result = prime * result + (if (this.feature_135 == null) 0 else this.feature_135.hashCode())
        result = prime * result + (if (this.feature_136 == null) 0 else this.feature_136.hashCode())
        result = prime * result + (if (this.feature_137 == null) 0 else this.feature_137.hashCode())
        result = prime * result + (if (this.feature_138 == null) 0 else this.feature_138.hashCode())
        result = prime * result + (if (this.feature_139 == null) 0 else this.feature_139.hashCode())
        result = prime * result + (if (this.feature_140 == null) 0 else this.feature_140.hashCode())
        result = prime * result + (if (this.feature_141 == null) 0 else this.feature_141.hashCode())
        result = prime * result + (if (this.feature_142 == null) 0 else this.feature_142.hashCode())
        result = prime * result + (if (this.feature_143 == null) 0 else this.feature_143.hashCode())
        result = prime * result + (if (this.feature_144 == null) 0 else this.feature_144.hashCode())
        result = prime * result + (if (this.feature_145 == null) 0 else this.feature_145.hashCode())
        result = prime * result + (if (this.feature_146 == null) 0 else this.feature_146.hashCode())
        result = prime * result + (if (this.feature_147 == null) 0 else this.feature_147.hashCode())
        result = prime * result + (if (this.feature_148 == null) 0 else this.feature_148.hashCode())
        result = prime * result + (if (this.feature_149 == null) 0 else this.feature_149.hashCode())
        result = prime * result + (if (this.feature_150 == null) 0 else this.feature_150.hashCode())
        result = prime * result + (if (this.feature_151 == null) 0 else this.feature_151.hashCode())
        result = prime * result + (if (this.feature_152 == null) 0 else this.feature_152.hashCode())
        result = prime * result + (if (this.feature_153 == null) 0 else this.feature_153.hashCode())
        result = prime * result + (if (this.feature_154 == null) 0 else this.feature_154.hashCode())
        result = prime * result + (if (this.feature_155 == null) 0 else this.feature_155.hashCode())
        result = prime * result + (if (this.feature_156 == null) 0 else this.feature_156.hashCode())
        result = prime * result + (if (this.feature_157 == null) 0 else this.feature_157.hashCode())
        result = prime * result + (if (this.feature_158 == null) 0 else this.feature_158.hashCode())
        result = prime * result + (if (this.feature_159 == null) 0 else this.feature_159.hashCode())
        result = prime * result + (if (this.feature_160 == null) 0 else this.feature_160.hashCode())
        result = prime * result + (if (this.feature_161 == null) 0 else this.feature_161.hashCode())
        result = prime * result + (if (this.feature_162 == null) 0 else this.feature_162.hashCode())
        result = prime * result + (if (this.feature_163 == null) 0 else this.feature_163.hashCode())
        result = prime * result + (if (this.feature_164 == null) 0 else this.feature_164.hashCode())
        result = prime * result + (if (this.feature_165 == null) 0 else this.feature_165.hashCode())
        result = prime * result + (if (this.feature_166 == null) 0 else this.feature_166.hashCode())
        result = prime * result + (if (this.feature_167 == null) 0 else this.feature_167.hashCode())
        result = prime * result + (if (this.feature_168 == null) 0 else this.feature_168.hashCode())
        result = prime * result + (if (this.feature_169 == null) 0 else this.feature_169.hashCode())
        result = prime * result + (if (this.feature_170 == null) 0 else this.feature_170.hashCode())
        result = prime * result + (if (this.feature_171 == null) 0 else this.feature_171.hashCode())
        result = prime * result + (if (this.feature_172 == null) 0 else this.feature_172.hashCode())
        result = prime * result + (if (this.feature_173 == null) 0 else this.feature_173.hashCode())
        result = prime * result + (if (this.feature_174 == null) 0 else this.feature_174.hashCode())
        result = prime * result + (if (this.feature_175 == null) 0 else this.feature_175.hashCode())
        result = prime * result + (if (this.feature_176 == null) 0 else this.feature_176.hashCode())
        result = prime * result + (if (this.feature_177 == null) 0 else this.feature_177.hashCode())
        result = prime * result + (if (this.feature_178 == null) 0 else this.feature_178.hashCode())
        result = prime * result + (if (this.feature_179 == null) 0 else this.feature_179.hashCode())
        result = prime * result + (if (this.feature_180 == null) 0 else this.feature_180.hashCode())
        result = prime * result + (if (this.feature_181 == null) 0 else this.feature_181.hashCode())
        result = prime * result + (if (this.feature_182 == null) 0 else this.feature_182.hashCode())
        result = prime * result + (if (this.feature_183 == null) 0 else this.feature_183.hashCode())
        result = prime * result + (if (this.feature_184 == null) 0 else this.feature_184.hashCode())
        result = prime * result + (if (this.feature_185 == null) 0 else this.feature_185.hashCode())
        result = prime * result + (if (this.feature_186 == null) 0 else this.feature_186.hashCode())
        result = prime * result + (if (this.feature_187 == null) 0 else this.feature_187.hashCode())
        result = prime * result + (if (this.feature_188 == null) 0 else this.feature_188.hashCode())
        result = prime * result + (if (this.feature_189 == null) 0 else this.feature_189.hashCode())
        result = prime * result + (if (this.feature_190 == null) 0 else this.feature_190.hashCode())
        result = prime * result + (if (this.feature_191 == null) 0 else this.feature_191.hashCode())
        result = prime * result + (if (this.feature_192 == null) 0 else this.feature_192.hashCode())
        result = prime * result + (if (this.feature_193 == null) 0 else this.feature_193.hashCode())
        result = prime * result + (if (this.feature_194 == null) 0 else this.feature_194.hashCode())
        result = prime * result + (if (this.feature_195 == null) 0 else this.feature_195.hashCode())
        result = prime * result + (if (this.feature_196 == null) 0 else this.feature_196.hashCode())
        result = prime * result + (if (this.feature_197 == null) 0 else this.feature_197.hashCode())
        result = prime * result + (if (this.feature_198 == null) 0 else this.feature_198.hashCode())
        result = prime * result + (if (this.feature_199 == null) 0 else this.feature_199.hashCode())
        result = prime * result + (if (this.marketingBranchOfficeCd == null) 0 else this.marketingBranchOfficeCd.hashCode())
        return result
    }
}
