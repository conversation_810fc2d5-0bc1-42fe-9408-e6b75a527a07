-- ************************************************************************** --
-- ファンクション名 : update_marketing_branch_office_cd
-- 処理概要         : 建物マスタのマーケティング営業所カラムを更新します
-- 引数             :
-- 戻り値           :
-- 備考             :
-- ************************************************************************** --
CREATE OR REPLACE FUNCTION update_marketing_branch_office_cd()
RETURNS TRIGGER AS $$
DECLARE
    office_code char(3);
BEGIN
    office_code := LEFT(NEW.daiken_branch_code, 3);
    NEW.marketing_branch_office_cd := CASE
        WHEN office_code = '639' THEN '822' -- 帯広 → 千歳
        WHEN office_code = '743' THEN '753' -- 能代 → 秋田
        WHEN office_code = '889' THEN '650' -- 会津若松 → 郡山
        WHEN office_code = '830' THEN '651' -- 北栃木 → 宇都宮
        WHEN office_code = '805' THEN '651' -- 宇都宮北 → 宇都宮
        WHEN office_code = '660' THEN '847' -- 太田 → 南栃木
        WHEN office_code = '923' THEN '728' -- 三郷 → 越谷
        WHEN office_code = '849' THEN '787' -- 埼玉南 → 川越
        WHEN office_code = '895' THEN '681' -- 狭山 → 所沢
        WHEN office_code = '899' THEN '705' -- 多摩 → 八王子
        WHEN office_code = '885' THEN '891' -- 川崎東 → 横浜
        WHEN office_code = '636' THEN '678' -- 浜松北 → 浜松
        WHEN office_code = '906' THEN '678' -- 浜松東 → 浜松
        WHEN office_code = '610' THEN '824' -- 新潟西 → 新潟
        WHEN office_code = '608' THEN '632' -- 金沢南 → 金沢
        WHEN office_code = '921' THEN '632' -- 小松 → 金沢
        WHEN office_code = '709' THEN '790' -- 小牧 → 一宮
        WHEN office_code = '876' THEN '713' -- 橋本 → 和歌山
        WHEN office_code = '770' THEN '827' -- 津山 → 岡山東
        WHEN office_code = '764' THEN '862' -- 米子 → 松江
        WHEN office_code = '912' THEN '802' -- 広島南 → 広島
        WHEN office_code = '877' THEN '701' -- 宇部 → 山口
        WHEN office_code = '913' THEN '881' -- 岩国 → 徳山
        WHEN office_code = '671' THEN '786' -- 鳴門 → 徳島
        WHEN office_code = '870' THEN '680' -- 松山北 → 松山
        WHEN office_code = '664' THEN '803' -- 延岡 → 宮崎
        WHEN office_code = '637' THEN '804' -- 鹿児島東 → 鹿児島
        ELSE office_code  -- それ以外はそのまま3桁を使用
    END;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_marketing_branch_office_cd
BEFORE INSERT OR UPDATE ON building_master
FOR EACH ROW
EXECUTE FUNCTION update_marketing_branch_office_cd();
