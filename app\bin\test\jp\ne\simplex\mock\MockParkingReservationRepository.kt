package jp.ne.simplex.mock

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.db.ParkingReservationRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo

class MockParkingReservationRepository(
    val registerFunc: (param: RegisterParkingReservation) -> Unit = { _ -> },
    val updateFunc: (param: UpdateParkingReservation) -> Unit = { _ -> },
    val cancelFunc: (param: CancelParkingReservation) -> Unit = { _ -> },
    val cancelApplicationFunc: (parkingReservationId: ParkingReservation.Id, param: CancelApplicationParkingReservation) -> Unit = { _, _ -> },
    val finishFunc: (param: FinishParkingReservation) -> Unit = { _ -> },
    val findByIdFunc: (id: ParkingReservation.Id) -> ParkingReservationInfo? = { _ -> null },
    val findTentativeFunc: (id: ParkingLot.Id) -> ParkingReservationInfo? = { _ -> null },
    val findActiveApplicationFunc: (id: ParkingLot.Id) -> ParkingReservationInfo? = { _ -> null },
    val findActiveReservationsFunc: (buildingCode: Building.Code, parkingLotCode: ParkingLot.Code?) -> List<ParkingReservationInfo> = { _, _ -> emptyList() },
    val findActiveReservationsForBatchFunc: () -> List<ParkingReservationInfo> = { emptyList() }
) : ParkingReservationRepositoryInterface {

    override fun register(
        requestUser: AuthInfo.RequestUser,
        param: RegisterParkingReservation
    ) {
        return registerFunc(param)
    }

    override fun update(
        requestUser: AuthInfo.RequestUser,
        param: UpdateParkingReservation,
        ovverideReservationType: ParkingReservation.Type?
    ) {
        return updateFunc(param)
    }

    override fun cancel(requestUser: AuthInfo.RequestUser, param: CancelParkingReservation) {
        return cancelFunc(param)
    }

    override fun cancelApplication(
        requestUser: AuthInfo.RequestUser,
        parkingReservationId: ParkingReservation.Id,
        param: CancelApplicationParkingReservation
    ) {
        return cancelApplicationFunc(parkingReservationId, param)
    }

    override fun finish(requestUser: AuthInfo.RequestUser, param: FinishParkingReservation) {
        return finishFunc(param)
    }

    override fun findById(id: ParkingReservation.Id): ParkingReservationInfo? {
        return findByIdFunc(id)
    }

    override fun findTentative(parkingLotId: ParkingLot.Id): ParkingReservationInfo? {
        return findTentativeFunc(parkingLotId)
    }

    override fun findActiveApplication(parkingLotId: ParkingLot.Id): ParkingReservationInfo? {
        return findActiveApplicationFunc(parkingLotId)
    }

    override fun findActiveReservations(
        buildingCode: Building.Code,
        parkingLotCode: ParkingLot.Code?
    ): List<ParkingReservationInfo> {
        return findActiveReservationsFunc(buildingCode, parkingLotCode)
    }

    override fun findActiveParkingReservationByOrderCode(orderCode: Building.OrderCode): List<ParkingReservationInfo> {
        return emptyList()
    }

    override fun findActiveReservationsForBatch(): List<ParkingReservationInfo> {
        return findActiveReservationsForBatchFunc()
    }
}
