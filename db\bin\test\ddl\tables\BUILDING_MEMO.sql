-- TABLE: BUILDING_MEMO(建物メモ)

CREATE TABLE BUILDING_MEMO(
     BUILDING_CODE                                varchar(9)        NOT NULL    
,    SEQ_NUMBER                                   numeric(4)        NOT NULL    
,    CONTENT                                      varchar(500)      NOT NULL    
,    BOOKMARK_FLAG                                varchar(1)        NOT NULL    
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    DELETE_FLAG                                  varchar(1)        NOT NULL    
,    CONSTRAINT PK_BUILDING_MEMO PRIMARY KEY (BUILDING_CODE, SEQ_NUMBER)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BUILDING_MEMO IS '建物メモ 既存システム物理名: -';
COMMENT ON COLUMN BUILDING_MEMO.BUILDING_CODE IS '建物コード 既存システム物理名: -';
COMMENT ON COLUMN BUILDING_MEMO.SEQ_NUMBER IS 'シーケンス番号 既存システム物理名: -';
COMMENT ON COLUMN BUILDING_MEMO.CONTENT IS '本文 既存システム物理名: -';
COMMENT ON COLUMN BUILDING_MEMO.BOOKMARK_FLAG IS 'ブックマークフラグ 既存システム物理名: -';
COMMENT ON COLUMN BUILDING_MEMO.CREATION_DATE IS '作成年月日 既存システム物理名: -';
COMMENT ON COLUMN BUILDING_MEMO.CREATION_TIME IS '作成時刻 既存システム物理名: -';
COMMENT ON COLUMN BUILDING_MEMO.CREATOR IS '作成者 既存システム物理名: -';
COMMENT ON COLUMN BUILDING_MEMO.UPDATE_DATE IS '更新年月日 既存システム物理名: -';
COMMENT ON COLUMN BUILDING_MEMO.UPDATE_TIME IS '更新時刻 既存システム物理名: -';
COMMENT ON COLUMN BUILDING_MEMO.UPDATER IS '更新者 既存システム物理名: -';
COMMENT ON COLUMN BUILDING_MEMO.DELETE_FLAG IS '削除フラグ 既存システム物理名: -';
