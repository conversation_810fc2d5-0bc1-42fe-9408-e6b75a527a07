package jp.ne.simplex.application.repository.aws

import jp.ne.simplex.application.repository.proxy.Proxy
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import software.amazon.awssdk.http.apache.ApacheHttpClient
import software.amazon.awssdk.http.apache.ProxyConfiguration
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.ses.SesClient
import java.net.URI

@Component
class SimpleEmailServiceConfig(
    @Value("\${aws.endpoint}")
    private val endpoint: String? = null,
    private val proxy: Proxy?
) {

    @Bean
    @Profile("!dev")
    fun prodSesClient(): SesClient {
        // SES APIを使用する場合は、NatGateway経由で接続する必要があるため、
        // 他のAWSサービスと違って、Proxy設定を行う
        if (proxy != null) {
            val proxyConfiguration = ProxyConfiguration.builder()
                .endpoint(URI.create("http://${proxy.host}:${proxy.port}"))
                .build()

            val httpClient = ApacheHttpClient.builder()
                .proxyConfiguration(proxyConfiguration)
                .build()

            return SesClient.builder() //
                .region(Region.AP_NORTHEAST_1) //
                .httpClient(httpClient) //
                .build()
        }
        return SesClient.builder() //
            .region(Region.AP_NORTHEAST_1) //
            .build()
    }

    @Bean
    @Profile("dev")
    fun devSesClient(): SesClient {
        if (endpoint == null) {
            throw RuntimeException("AWS endpoint is not configured for dev profile.")
        }

        val proxyConfiguration = ProxyConfiguration.builder()
            .nonProxyHosts(setOf("localhost"))
            .build()

        val httpClient = ApacheHttpClient.builder()
            .proxyConfiguration(proxyConfiguration)
            .build()

        return SesClient.builder() //
            .region(Region.AP_NORTHEAST_1) //
            .httpClient(httpClient) //
            .endpointOverride(URI.create(endpoint)) //
            .build()
    }
}
