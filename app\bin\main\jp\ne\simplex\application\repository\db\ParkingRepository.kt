package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.repository.db.pojos.ThirtyFiveYearBulkBuildingContractPojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingPojo
import jp.ne.simplex.db.jooq.gen.tables.references.*
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class ParkingRepository(private val context: DSLContext) : ParkingRepositoryInterface {

    companion object {
        private const val THIRTY_FIVE_YEAR_BULK_BUILDING_CONTRACT_SQL = "(SELECT " +
                "  c.building_cd AS contract_building_cd," +
                "  tc.building_cd AS temporary_contract_building_cd" +
                " FROM" +
                "  (" +
                "    SELECT" +
                "      *" +
                "    FROM" +
                "      (" +
                "        SELECT" +
                "          t1.building_cd," +
                "          row_number() over(" +
                "            PARTITION by t1.building_cd" +
                "            ORDER BY" +
                "              t1.data_management DESC," +
                "              t1.effective_start_date ASC," +
                "              t1.effective_end_date ASC," +
                "              t1.contract_type ASC" +
                "          ) AS seq" +
                "        FROM" +
                "          app.contract t1" +
                "        WHERE" +
                "          t1.building_cd LIKE  '%1${'$'}s'" +
                "          AND t1.effective_start_date <= to_char(CURRENT_TIMESTAMP, 'yyyymmdd') :: numeric" +
                "          AND t1.effective_end_date >= to_char(CURRENT_TIMESTAMP, 'yyyymmdd') :: numeric" +
                "          AND (t1.initial_setup_sign != '2' OR t1.initial_setup_sign is null)" +
                "          AND (t1.contract_type != '09000' OR t1.contract_type is null)" +
                "          AND coalesce(t1.logical_delete_sign, '0') != '1'" +
                "          AND EXISTS (" +
                "            SELECT" +
                "              1" +
                "            FROM" +
                "              app.thirty_five_year_bulk_building_file_cont t2" +
                "            WHERE" +
                "              t1.building_cd = t2.building_cd" +
                "              AND t1.effective_start_date = t2.effective_date" +
                "              AND coalesce(t2.logical_delete_sign, '0') != '1'" +
                "              AND t2.bulk_lease_type in ('3', '4')" +
                "          )" +
                "      ) as seq1" +
                "    WHERE" +
                "      seq = 1" +
                "  ) c FULL" +
                "  JOIN (" +
                "    SELECT" +
                "      *" +
                "    FROM" +
                "      (" +
                "        SELECT" +
                "          t1.building_cd," +
                "          row_number() over(" +
                "            PARTITION by t1.building_cd" +
                "            ORDER BY" +
                "              t1.data_management_no DESC," +
                "              t1.effective_start_date ASC," +
                "              t1.effective_end_date ASC," +
                "              t1.contract_type ASC" +
                "          ) AS seq" +
                "        FROM" +
                "          app.temporary_contract t1" +
                "        WHERE" +
                "          t1.building_cd LIKE '%1${'$'}s'" +
                "          AND t1.effective_start_date <= to_char(CURRENT_TIMESTAMP, 'yyyymmdd') :: numeric" +
                "          AND t1.effective_end_date >= to_char(CURRENT_TIMESTAMP, 'yyyymmdd') :: numeric" +
                "          AND (t1.initial_setup_flag != '2' OR t1.initial_setup_flag is null)" +
                "          AND (t1.contract_type != '09000' OR t1.contract_type is null)" +
                "          AND coalesce(t1.logical_delete_flag, '0') != '1'" +
                "          AND EXISTS (" +
                "            SELECT" +
                "              1" +
                "            FROM" +
                "              app.thirty_five_year_bulk_building_file_cont t2" +
                "            WHERE" +
                "              t1.building_cd = t2.building_cd" +
                "              AND t1.effective_start_date = t2.effective_date" +
                "              AND coalesce(t2.logical_delete_sign, '0') != '1'" +
                "              AND t2.bulk_lease_type in ('3', '4')" +
                "          )" +
                "      ) as seq2" +
                "    WHERE" +
                "      seq = 1" +
                "  ) tc ON c.building_cd = tc.building_cd) AS thirty_five_year_bulk_building_contract"
    }

    override fun isParkingExist(parkingLotId: ParkingLot.Id): Boolean {
        return context.selectFrom(PARKING)
            .where(PARKING.BUILDING_CODE.eq(parkingLotId.buildingCode.value))
            .and(PARKING.PARKING_LOT_CODE.eq(parkingLotId.parkingLotCode.value))
            .count() == 1
    }

    override fun getAllOrderCodes(): List<Building.OrderCode> {
        val orderCodeField = DSL.substring(PARKING.BUILDING_CODE, 1, 7).`as`("orderCode")
        return context.selectDistinct(orderCodeField)
            .from(PARKING)
            .orderBy(orderCodeField.asc())
            .fetch(orderCodeField).map { Building.OrderCode.of(it) }
    }

    override fun getRoomCountByOrderCode(orderCode: Building.OrderCode): Int {
        return context.selectCount().from(ROOM_MASTER)
            .where(ROOM_MASTER.BUILDING_CODE.like("${orderCode.value}%"))
            .and(ROOM_MASTER.LOGICAL_DELETE_SIGN.isNull().or(ROOM_MASTER.LOGICAL_DELETE_SIGN.eq(0)))
            .fetchOne(0, Int::class.java) ?: 0
    }

    override fun getVacancyRoomCountByOrderCode(orderCode: Building.OrderCode): Int {
        return context.select(BUILDING_INFO_MASTER.VACANT_ROOMS)
            .from(BUILDING_INFO_MASTER)
            .where(BUILDING_INFO_MASTER.BUILDING_CODE.like("${orderCode.value}%"))
            .fetch(0, Int::class.java).firstOrNull() ?: 0
    }

    override fun getThirtyFiveYearBulkBuildingContract(orderCode: Building.OrderCode): List<Building.Code> {
        return context.selectFrom(
            DSL.raw(
                String.format(
                    THIRTY_FIVE_YEAR_BULK_BUILDING_CONTRACT_SQL,
                    "${orderCode.value}%"
                )
            )
        )
            .fetchInto(ThirtyFiveYearBulkBuildingContractPojo::class.java).mapNotNull {
                when {
                    it.contractBuildingCd != null -> Building.Code.of(it.contractBuildingCd)
                    it.temporaryContractBuildingCd != null -> Building.Code.of(it.temporaryContractBuildingCd)
                    else -> null
                }
            }.distinct()
    }

    override fun findParkingLotIdMapForWelcomePark(): Map<Boolean, Set<ParkingLot.Id>> {

        val currentDateTime = LocalDateTime.now()
        val currentDate = currentDateTime.yyyyMMdd().toInt()

        // CONTRACTテーブルのサブクエリ
        val fContract = context.select(
            DSL.rowNumber().over()
                .partitionBy(CONTRACT.BUILDING_CD)
                .orderBy(
                    CONTRACT.DATA_MANAGEMENT.desc(),
                    CONTRACT.EFFECTIVE_START_DATE.asc(),
                    CONTRACT.EFFECTIVE_END_DATE.asc(),
                    CONTRACT.CONTRACT_TYPE.asc()
                )
                .`as`("SEQ"),
            CONTRACT.BUILDING_CD,
            CONTRACT.DATA_MANAGEMENT,
            CONTRACT.EFFECTIVE_START_DATE,
            CONTRACT.EFFECTIVE_END_DATE,
            CONTRACT.INITIAL_SETUP_SIGN,
            CONTRACT.CONTRACT_TYPE,
            THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.BULK_LEASE_TYPE,
            CONTRACT.CONTRACT_OUTPUT_MANAGEMENT,
            THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.CONTRACT_OUTPUT_MANAGEMENT_NO
        )
            .from(CONTRACT)
            .innerJoin(THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT)
            .on(CONTRACT.BUILDING_CD.eq(THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.BUILDING_CD))
            .and(CONTRACT.EFFECTIVE_START_DATE.eq(THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.EFFECTIVE_DATE))
            .and(CONTRACT.EFFECTIVE_START_DATE.le(currentDate))
            .and(CONTRACT.EFFECTIVE_END_DATE.ge(currentDate))
            .and(CONTRACT.INITIAL_SETUP_SIGN.ne("2").or(CONTRACT.INITIAL_SETUP_SIGN.isNull))
            .and(CONTRACT.CONTRACT_TYPE.ne("09000").or(CONTRACT.CONTRACT_TYPE.isNull))
            .and(CONTRACT.LOGICAL_DELETE_SIGN.ne("1").or(CONTRACT.LOGICAL_DELETE_SIGN.isNull))
            .and(
                THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.LOGICAL_DELETE_SIGN.ne("1")
                    .or(THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.LOGICAL_DELETE_SIGN.isNull)
            )

        // CONTRACTテーブルのサブクエリ
        val fTemporaryContract = context.select(
            DSL.rowNumber().over()
                .partitionBy(TEMPORARY_CONTRACT.BUILDING_CD)
                .orderBy(
                    TEMPORARY_CONTRACT.DATA_MANAGEMENT_NO.desc(),
                    TEMPORARY_CONTRACT.EFFECTIVE_START_DATE.asc(),
                    TEMPORARY_CONTRACT.EFFECTIVE_END_DATE.asc(),
                    TEMPORARY_CONTRACT.CONTRACT_TYPE.asc()
                ).`as`("SEQ"),
            TEMPORARY_CONTRACT.BUILDING_CD,
            TEMPORARY_CONTRACT.DATA_MANAGEMENT_NO,
            TEMPORARY_CONTRACT.EFFECTIVE_START_DATE,
            TEMPORARY_CONTRACT.EFFECTIVE_END_DATE,
            TEMPORARY_CONTRACT.INITIAL_SETUP_FLAG,
            TEMPORARY_CONTRACT.CONTRACT_TYPE,
            THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.BULK_LEASE_TYPE,
            TEMPORARY_CONTRACT.CONTRACT_OUTPUT_MANAGEMENT_NO,
            THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.CONTRACT_OUTPUT_MANAGEMENT_NO
        )
            .from(TEMPORARY_CONTRACT)
            .innerJoin(THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT)
            .on(TEMPORARY_CONTRACT.BUILDING_CD.eq(THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.BUILDING_CD))
            .and(TEMPORARY_CONTRACT.EFFECTIVE_START_DATE.eq(THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.EFFECTIVE_DATE))
            .and(TEMPORARY_CONTRACT.EFFECTIVE_START_DATE.le(currentDate))
            .and(TEMPORARY_CONTRACT.EFFECTIVE_END_DATE.ge(currentDate))
            .and(
                TEMPORARY_CONTRACT.INITIAL_SETUP_FLAG.ne("2")
                    .or(TEMPORARY_CONTRACT.INITIAL_SETUP_FLAG.isNull)
            )
            .and(
                TEMPORARY_CONTRACT.CONTRACT_TYPE.ne("09000")
                    .or(TEMPORARY_CONTRACT.CONTRACT_TYPE.isNull)
            )
            .and(
                TEMPORARY_CONTRACT.LOGICAL_DELETE_FLAG.ne("1")
                    .or(TEMPORARY_CONTRACT.LOGICAL_DELETE_FLAG.isNull)
            )
            .and(
                THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.LOGICAL_DELETE_SIGN.ne("1")
                    .or(THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.LOGICAL_DELETE_SIGN.isNull)
            )

        return context.selectDistinct(
            PARKING.BUILDING_CODE,
            PARKING.PARKING_LOT_CODE,
            PARKING.CONSOLIDATED_BUILDING_CODE,
            PARKING.CONSOLIDATED_PARKING_CODE
        )
            .from(PARKING)
            .innerJoin(BUILDING_MASTER)
            .on(PARKING.BUILDING_CODE.eq(BUILDING_MASTER.BUILDING_CODE))
            .innerJoin(PARKING_HOURLY_RENTAL_APPROVAL)
            .on(PARKING_HOURLY_RENTAL_APPROVAL.BUILDING_CODE.eq(PARKING.BUILDING_CODE))
            .and(PARKING_HOURLY_RENTAL_APPROVAL.PARKING_TIME_RENTAL_CONSENT_TYPE.eq("1"))
            .innerJoin(LATEST_RENT_EVALUATION)
            .on(LATEST_RENT_EVALUATION.BUILDING_CODE.eq(PARKING.BUILDING_CODE))
            .and(LATEST_RENT_EVALUATION.ROOM_PARKING_DIVISION.eq("2"))
            .and(LATEST_RENT_EVALUATION.PROPERTY_CODE.eq(PARKING.PARKING_LOT_CODE))
            .leftJoin(fContract)
            .on((PARKING.BUILDING_CODE).eq(fContract.field(CONTRACT.BUILDING_CD)))
            .and((fContract.field("SEQ", Int::class.java))?.eq(1) ?: DSL.falseCondition())
            .leftJoin(fTemporaryContract)
            .on(PARKING.BUILDING_CODE.eq(fTemporaryContract.field(TEMPORARY_CONTRACT.BUILDING_CD)))
            .and((fTemporaryContract.field("SEQ", Int::class.java))?.eq(1) ?: DSL.falseCondition())
            .where(
                PARKING.LOGICAL_DELETE_FLAG.ne(1)
                    .or(PARKING.LOGICAL_DELETE_FLAG.isNull)
                    .or(PARKING.CONSOLIDATED_BUILDING_CODE.isNotNull)
            )
            .and(
                BUILDING_MASTER.LOGICAL_DELETE_FLAG.ne(1)
                    .or(BUILDING_MASTER.LOGICAL_DELETE_FLAG.isNull)
            )
            .and(PARKING.PARKING_LOT_NUMBER.isNotNull())
            .and(LATEST_RENT_EVALUATION.PARKING_FEE.gt(0))
            .and(
                PARKING.BULK_LEASE_FLAG.notIn(0, 1, 5).or(PARKING.BULK_LEASE_FLAG.isNull)
                    .or(
                        PARKING.BULK_LEASE_FLAG.eq(5)
                            .and(
                                fContract.field(THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.BULK_LEASE_TYPE)
                                    ?.`in`(3, 4, 5)
                                    ?.or(
                                        fContract.field(THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.BULK_LEASE_TYPE)
                                            ?.isNull()
                                            ?.and(
                                                fTemporaryContract.field(
                                                    THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT.BULK_LEASE_TYPE
                                                )?.`in`(3, 4, 5)
                                            )
                                    )
                            )
                    ),
            )
            .and(PARKING.PARKING_CATEGORY.notIn("3", "4", "5").or(PARKING.PARKING_CATEGORY.isNull))
            .and(BUILDING_MASTER.BUILDING_TYPE_CODE.`in`("100", "110", "200", "210", "700"))
            .and(PARKING.TENANT_CATEGORY.eq("1"))
            .and(PARKING.RECRUITMENT_FLAG.eq(1))
            .and(
                PARKING.TRANSFERRED_BUILDING_CODE.isNull()
                    .or(DSL.trim(PARKING.TRANSFERRED_BUILDING_CODE).eq(""))
            )
            .orderBy(
                PARKING.BUILDING_CODE,
                PARKING.PARKING_LOT_CODE
            )
            .fetchInto(ParkingPojo::class.java)
            .mapNotNull { pojo ->
                val selfParkingLotId = ParkingLot.Id(
                    Building.Code.of(pojo.buildingCode),
                    ParkingLot.Code.of(pojo.parkingLotCode)
                )

                val consolidatedBuildingCode = pojo.consolidatedBuildingCode
                val consolidatedParkingCode = pojo.consolidatedParkingCode
                val consolidatedParkingLotId =
                    if (consolidatedBuildingCode != null && consolidatedParkingCode != null) {
                        ParkingLot.Id(
                            Building.Code.of(consolidatedBuildingCode),
                            ParkingLot.Code.of(consolidatedParkingCode)
                        )
                    } else {
                        null
                    }

                if (consolidatedParkingLotId != null) {
                    listOf(
                        Pair(false, consolidatedParkingLotId),
                        Pair(true, selfParkingLotId)
                    )
                } else {
                    listOf(Pair(true, selfParkingLotId))
                }
            }
            .flatMap { it }
            .groupBy(
                { it.first },
                { it.second }
            )
            .mapValues { it.value.toSet() }
    }

    override fun getLocalDisplayNumber(
        buildingCode: Building.Code,
        parkingLotCode: ParkingLot.Code?
    ): String? {
        if (parkingLotCode == null) {
            return null
        }

        return context.select(PARKING.PARKING_LOT_NUMBER).from(PARKING)
            .where(PARKING.BUILDING_CODE.eq(buildingCode.value))
            .and(PARKING.PARKING_LOT_CODE.eq(parkingLotCode.value))
            .fetchOneInto(String::class.java)
    }
}

interface ParkingRepositoryInterface {
    /** 駐車場存在フラグ(キー指定) */
    fun isParkingExist(parkingLotId: ParkingLot.Id): Boolean

    /** 駐車場テーブルに存在する全ての受注コード（建物コードの上7桁）を取得 */
    fun getAllOrderCodes(): List<Building.OrderCode>

    /** 部屋数取得（受注コード指定） */
    fun getRoomCountByOrderCode(orderCode: Building.OrderCode): Int

    /** 空き部屋数取得（受注コード指定） */
    fun getVacancyRoomCountByOrderCode(orderCode: Building.OrderCode): Int

    /** 一括借上タイプが3の契約が紐付く建物(受注コード指定) */
    fun getThirtyFiveYearBulkBuildingContract(orderCode: Building.OrderCode): List<Building.Code>

    /** 建物マスタ.建物CDチェック用SQL(sql1) */
    fun findParkingLotIdMapForWelcomePark(): Map<Boolean, Set<ParkingLot.Id>>

    /** 駐車場区画の現地表示番号を取得する */
    fun getLocalDisplayNumber(
        buildingCode: Building.Code,
        parkingLotCode: ParkingLot.Code?
    ): String?
}
