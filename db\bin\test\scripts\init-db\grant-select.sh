#!/bin/bash
#引数 $1:付与ユーザ名, $2:対象DB名, $3:対象スキーマ名

# shellcheck disable=SC2034
PGPASSWORD=${POSTGRES_PASSWORD} # psql接続用
GRANTEE_USER=$1
DBNAME=$2
SCHEMA_NAME=$3

echo "/**************** ${GRANTEE_USER}に${DBNAME}のSELECT権限付与 ****************/"
echo
psql -f grantSelect.sql -U "${POSTGRES_USER}" -h "${DBHOST}" -p "${DBPORT}" -d "${DBNAME}" -v USER_NAME="${GRANTEE_USER}" -v DB_NAME="${DBNAME}" -v SCHEMA_NAME="${SCHEMA_NAME}"
