-- TABLE: BUILDING_INFO_FILE(建物情報ファイル)

CREATE TABLE BUILDING_INFO_FILE(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM                               varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    DELETE_FLAG                                  varchar(1)                    
,    ORDER_CD                                     numeric(7)                    
,    ADDITIONAL_CD                                numeric(3)                    
,    RECORD_CATEGORY                              varchar(1)                    
,    CHANGE_HISTORY_NO                            numeric(3)                    
,    BUILDING_NO                                  numeric(2)                    
,    NUMBER_OF_BUILDINGS                          numeric(3)                    
,    SITE_AREA                                    numeric(7,2)                  
,    BUILDING_COVERAGE_RATIO                      numeric(5,2)                  
,    FLOOR_AREA_RATIO                             numeric(5,2)                  
,    USAGE_AREA_CATEGORY                          varchar(2)                    
,    FIRE_PROTECTION_AREA_CATEGORY                varchar(1)                    
,    Z<PERSON>E_CATEGORY                                varchar(1)                    
,    SNOW_ACCUMULATION_CATEGORY                   varchar(1)                    
,    TEMPERATURE_CATEGORY                         varchar(1)                    
,    BUILDING_CATEGORY                            varchar(1)                    
,    PRODUCT_NAME_CD                              numeric(3)                    
,    PRODUCT_CD_BRANCH                            numeric(2)                    
,    PRODUCT_SERIAL_NO                            numeric(3)                    
,    STANDARD_SPECIAL_ORDER_CATEGORY              varchar(1)                    
,    BUILDING_TYPE_CD_ST                          varchar(3)                    
,    BUILDING_AREA                                numeric(7,2)                  
,    TOTAL_FLOOR_AREA                             numeric(7,2)                  
,    EAVE_HEIGHT                                  numeric(5,2)                  
,    OFFICE_CATEGORY                              varchar(1)                    
,    OFFICE_AREA                                  numeric(7,2)                  
,    TOILET_CATEGORY                              varchar(1)                    
,    SEPTIC_TANK_CATEGORY                         varchar(1)                    
,    SEPTIC_TANK_CAPACITY                         numeric(3)                    
,    GAS_CATEGORY                                 varchar(1)                    
,    STANDARD_ROOF_SPEC_BASE                      varchar(2)                    
,    STANDARD_ROOF_SPEC_FINISH                    varchar(2)                    
,    STANDARD_WALL_SPEC_BASE                      varchar(2)                    
,    STANDARD_WALL_SPEC_FINISH                    varchar(2)                    
,    NUMBER_OF_ABOVE_GROUND_FLOORS                numeric(2)                    
,    NUMBER_OF_BASEMENT_FLOORS                    numeric(2)                    
,    HOUSE_ALIGNMENT                              numeric(2)                    
,    NUMBER_OF_COMMERCIAL_UNITS                   numeric(3)                    
,    NUMBER_OF_RESIDENTIAL_UNITS                  numeric(3)                    
,    STRUCTURE_CATEGORY                           varchar(2)                    
,    STAIRCASE_TYPE                               varchar(1)                    
,    ENTRANCE_TYPE                                varchar(1)                    
,    FLOOR_PLAN_CATEGORY                          varchar(2)                    
,    SHOP_RESIDENCE_CATEGORY                      varchar(1)                    
,    FRONTAGE                                     numeric(5,2)                  
,    DEPTH                                        numeric(5,2)                  
,    FOUNDATION_SHAPE                             varchar(1)                    
,    SPECIAL_PURPOSE_AREA_CATEGORY01              varchar(2)                    
,    SPECIAL_PURPOSE_AREA_CATEGORY02              varchar(2)                    
,    SPECIAL_PURPOSE_AREA_CATEGORY03              varchar(2)                    
,    SPECIAL_PURPOSE_AREA_CATEGORY04              varchar(2)                    
,    SPECIAL_PURPOSE_AREA_CATEGORY05              varchar(2)                    
,    CONTRACT_AMOUNT_TOTAL                        numeric(15)                   
,    LIST_PRICE                                   numeric(11)                   
,    TAC_NO                                       numeric(10)                   
,    CONTRACT_AMOUNT_MAIN                         numeric(15)                   
,    CONTRACT_MAIN_TAX_EXCL                       numeric(15)                   
,    CONTRACT_MAIN_TAX1                           numeric(15)                   
,    CONTRACT_MAIN_TAX2                           numeric(15)                   
,    CONTRACT_AMOUNT_ANCILLARY                    numeric(15)                   
,    CONTRACT_ANCILLARY_TAX_EXCL                  numeric(15)                   
,    CONTRACT_ANCILLARY_TAX1                      numeric(15)                   
,    CONTRACT_ANCILLARY_TAX2                      numeric(15)                   
,    CONTRACT_AMOUNT_EXTERNAL                     numeric(15)                   
,    CONTRACT_EXTERNAL_TAX_EXCL                   numeric(15)                   
,    CONTRACT_EXTERNAL_TAX1                       numeric(15)                   
,    CONTRACT_EXTERNAL_TAX2                       numeric(15)                   
,    CONTRACT_AMOUNT_OTHER                        numeric(15)                   
,    CONTRACT_OTHER_TAX_EXCL                      numeric(15)                   
,    CONTRACT_OTHER_TAX1                          numeric(15)                   
,    CONTRACT_OTHER_TAX2                          numeric(15)                   
,    BUILDING_CD                                  varchar(9)                    
,    ORDER_CD_ST                                  varchar(7)                    
,    ADDITIONAL_CD_ST                             varchar(3)                    
,    BUILDING_NO_ST                               varchar(2)                    
,    PET_FRIENDLY_FLAG                            varchar(1)                    
,    ELECTRICITY_TYPE                             varchar(1)                    
,    SOLAR_BUSINESS_TYPE                          varchar(1)                    
,    SOLAR_POWER_OUTPUT                           numeric(6,3)                  
,    ZEH_FLAG                                     varchar(1)                    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BUILDING_INFO_FILE IS '建物情報ファイル 既存システム物理名: BGZFDP';
COMMENT ON COLUMN BUILDING_INFO_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: BGZ01D @290';
COMMENT ON COLUMN BUILDING_INFO_FILE.CREATION_TIME IS '作成時刻 既存システム物理名: BGZ02H @290';
COMMENT ON COLUMN BUILDING_INFO_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: BGZ03D @290 削除フラグ " ":有効 "C":工事中止 "D":論理';
COMMENT ON COLUMN BUILDING_INFO_FILE.UPDATE_TIME IS '更新時刻 既存システム物理名: BGZ04H';
COMMENT ON COLUMN BUILDING_INFO_FILE.UPDATE_PROGRAM IS '更新プログラム 既存システム物理名: BGZ05P';
COMMENT ON COLUMN BUILDING_INFO_FILE.UPDATER IS '更新者 既存システム物理名: BGZ06P';
COMMENT ON COLUMN BUILDING_INFO_FILE.DELETE_FLAG IS '削除フラグ 既存システム物理名: BGZ08S';
COMMENT ON COLUMN BUILDING_INFO_FILE.ORDER_CD IS '受注コード 既存システム物理名: BGZJ04';
COMMENT ON COLUMN BUILDING_INFO_FILE.ADDITIONAL_CD IS '追加コード 既存システム物理名: BGZT14';
COMMENT ON COLUMN BUILDING_INFO_FILE.RECORD_CATEGORY IS 'レコード区分 既存システム物理名: BGZK80';
COMMENT ON COLUMN BUILDING_INFO_FILE.CHANGE_HISTORY_NO IS '変更履歴番号 既存システム物理名: BGZRNO';
COMMENT ON COLUMN BUILDING_INFO_FILE.BUILDING_NO IS '棟番号 既存システム物理名: BGZT12';
COMMENT ON COLUMN BUILDING_INFO_FILE.NUMBER_OF_BUILDINGS IS '棟数 既存システム物理名: BGZT15';
COMMENT ON COLUMN BUILDING_INFO_FILE.SITE_AREA IS '敷地面積 既存システム物理名: BGZS29';
COMMENT ON COLUMN BUILDING_INFO_FILE.BUILDING_COVERAGE_RATIO IS '建ぺい率 既存システム物理名: BGZK34';
COMMENT ON COLUMN BUILDING_INFO_FILE.FLOOR_AREA_RATIO IS '容積率 既存システム物理名: BGZY06';
COMMENT ON COLUMN BUILDING_INFO_FILE.USAGE_AREA_CATEGORY IS '用途地域区分 既存システム物理名: BGZK73';
COMMENT ON COLUMN BUILDING_INFO_FILE.FIRE_PROTECTION_AREA_CATEGORY IS '防火地域区分 既存システム物理名: BGZK81';
COMMENT ON COLUMN BUILDING_INFO_FILE.ZONE_CATEGORY IS '区域区分 既存システム物理名: BGZK82';
COMMENT ON COLUMN BUILDING_INFO_FILE.SNOW_ACCUMULATION_CATEGORY IS '積雪区分 既存システム物理名: BGZK67';
COMMENT ON COLUMN BUILDING_INFO_FILE.TEMPERATURE_CATEGORY IS '温度区分 既存システム物理名: BGZK68';
COMMENT ON COLUMN BUILDING_INFO_FILE.BUILDING_CATEGORY IS '建築区分 既存システム物理名: BGZK98';
COMMENT ON COLUMN BUILDING_INFO_FILE.PRODUCT_NAME_CD IS '商品名称コード 既存システム物理名: BGZS21';
COMMENT ON COLUMN BUILDING_INFO_FILE.PRODUCT_CD_BRANCH IS '商品コード枝番 既存システム物理名: BGZS24';
COMMENT ON COLUMN BUILDING_INFO_FILE.PRODUCT_SERIAL_NO IS '商品形式連番 既存システム物理名: BGZS27';
COMMENT ON COLUMN BUILDING_INFO_FILE.STANDARD_SPECIAL_ORDER_CATEGORY IS '標準・特注区分 既存システム物理名: BGZK74';
COMMENT ON COLUMN BUILDING_INFO_FILE.BUILDING_TYPE_CD_ST IS '建物種別コード-ST 既存システム物理名: BGZT03';
COMMENT ON COLUMN BUILDING_INFO_FILE.BUILDING_AREA IS '建築面積 既存システム物理名: BGZK33';
COMMENT ON COLUMN BUILDING_INFO_FILE.TOTAL_FLOOR_AREA IS '延床面積 既存システム物理名: BGZN03';
COMMENT ON COLUMN BUILDING_INFO_FILE.EAVE_HEIGHT IS '軒高 既存システム物理名: BGZN04';
COMMENT ON COLUMN BUILDING_INFO_FILE.OFFICE_CATEGORY IS '事務所区分 既存システム物理名: BGZK75';
COMMENT ON COLUMN BUILDING_INFO_FILE.OFFICE_AREA IS '事務所部分面積 既存システム物理名: BGZJ12';
COMMENT ON COLUMN BUILDING_INFO_FILE.TOILET_CATEGORY IS 'トイレ区分 既存システム物理名: BGZK76';
COMMENT ON COLUMN BUILDING_INFO_FILE.SEPTIC_TANK_CATEGORY IS '浄化槽区分 既存システム物理名: BGZK77';
COMMENT ON COLUMN BUILDING_INFO_FILE.SEPTIC_TANK_CAPACITY IS '浄化槽規模 既存システム物理名: BGZJ13';
COMMENT ON COLUMN BUILDING_INFO_FILE.GAS_CATEGORY IS 'ガス区分 既存システム物理名: BGZK78';
COMMENT ON COLUMN BUILDING_INFO_FILE.STANDARD_ROOF_SPEC_BASE IS '標準屋根仕様・下地 既存システム物理名: BGZH05';
COMMENT ON COLUMN BUILDING_INFO_FILE.STANDARD_ROOF_SPEC_FINISH IS '標準屋根仕様・仕上 既存システム物理名: BGZH06';
COMMENT ON COLUMN BUILDING_INFO_FILE.STANDARD_WALL_SPEC_BASE IS '標準外壁仕様・下地 既存システム物理名: BGZH07';
COMMENT ON COLUMN BUILDING_INFO_FILE.STANDARD_WALL_SPEC_FINISH IS '標準外壁仕様・仕上 既存システム物理名: BGZH08';
COMMENT ON COLUMN BUILDING_INFO_FILE.NUMBER_OF_ABOVE_GROUND_FLOORS IS '地上階数 既存システム物理名: BGZC09';
COMMENT ON COLUMN BUILDING_INFO_FILE.NUMBER_OF_BASEMENT_FLOORS IS '地下階数 既存システム物理名: BGZC10';
COMMENT ON COLUMN BUILDING_INFO_FILE.HOUSE_ALIGNMENT IS '戸並び 既存システム物理名: BGZK35';
COMMENT ON COLUMN BUILDING_INFO_FILE.NUMBER_OF_COMMERCIAL_UNITS IS '事業用戸数 既存システム物理名: BGZJ14';
COMMENT ON COLUMN BUILDING_INFO_FILE.NUMBER_OF_RESIDENTIAL_UNITS IS '住居用戸数 既存システム物理名: BGZJ15';
COMMENT ON COLUMN BUILDING_INFO_FILE.STRUCTURE_CATEGORY IS '構造区分 既存システム物理名: BGZK61';
COMMENT ON COLUMN BUILDING_INFO_FILE.STAIRCASE_TYPE IS '階段タイプ 既存システム物理名: BGZK32';
COMMENT ON COLUMN BUILDING_INFO_FILE.ENTRANCE_TYPE IS '入り口タイプ 既存システム物理名: BGZI01';
COMMENT ON COLUMN BUILDING_INFO_FILE.FLOOR_PLAN_CATEGORY IS '間取り区分 既存システム物理名: BGZK65';
COMMENT ON COLUMN BUILDING_INFO_FILE.SHOP_RESIDENCE_CATEGORY IS '店住区分 既存システム物理名: BGZK79';
COMMENT ON COLUMN BUILDING_INFO_FILE.FRONTAGE IS '間口 既存システム物理名: BGZM91';
COMMENT ON COLUMN BUILDING_INFO_FILE.DEPTH IS '奥行き 既存システム物理名: BGZO01';
COMMENT ON COLUMN BUILDING_INFO_FILE.FOUNDATION_SHAPE IS '基礎形状 既存システム物理名: BGZK36';
COMMENT ON COLUMN BUILDING_INFO_FILE.SPECIAL_PURPOSE_AREA_CATEGORY01 IS '特別用途地区区分01 既存システム物理名: BGZK89';
COMMENT ON COLUMN BUILDING_INFO_FILE.SPECIAL_PURPOSE_AREA_CATEGORY02 IS '特別用途地区区分02 既存システム物理名: BGZK90';
COMMENT ON COLUMN BUILDING_INFO_FILE.SPECIAL_PURPOSE_AREA_CATEGORY03 IS '特別用途地区区分03 既存システム物理名: BGZK91';
COMMENT ON COLUMN BUILDING_INFO_FILE.SPECIAL_PURPOSE_AREA_CATEGORY04 IS '特別用途地区区分04 既存システム物理名: BGZK92';
COMMENT ON COLUMN BUILDING_INFO_FILE.SPECIAL_PURPOSE_AREA_CATEGORY05 IS '特別用途地区区分05 既存システム物理名: BGZK93';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_AMOUNT_TOTAL IS '請負金額(総額) 既存システム物理名: BGZU06';
COMMENT ON COLUMN BUILDING_INFO_FILE.LIST_PRICE IS '定価 既存システム物理名: BGZT16';
COMMENT ON COLUMN BUILDING_INFO_FILE.TAC_NO IS 'TAC番号 既存システム物理名: BGZT18';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_AMOUNT_MAIN IS '請負金額(本体) 既存システム物理名: BGZU01';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_MAIN_TAX_EXCL IS '請負(本体：税抜) 既存システム物理名: BGZ51A';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_MAIN_TAX1 IS '請負(本体：税1) 既存システム物理名: BGZ52A';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_MAIN_TAX2 IS '請負(本体：税2) 既存システム物理名: BGZ53A';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_AMOUNT_ANCILLARY IS '請負金額(附帯) 既存システム物理名: BGZU02';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_ANCILLARY_TAX_EXCL IS '請負(附帯：税抜) 既存システム物理名: BGZ61A';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_ANCILLARY_TAX1 IS '請負(附帯：税1) 既存システム物理名: BGZ62A';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_ANCILLARY_TAX2 IS '請負(附帯：税2) 既存システム物理名: BGZ63A';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_AMOUNT_EXTERNAL IS '請負金額(外構) 既存システム物理名: BGZU03';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_EXTERNAL_TAX_EXCL IS '請負(外構：税抜) 既存システム物理名: BGZ71A';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_EXTERNAL_TAX1 IS '請負(外構：税1) 既存システム物理名: BGZ72A';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_EXTERNAL_TAX2 IS '請負(外構：税2) 既存システム物理名: BGZ73A';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_AMOUNT_OTHER IS '請負金額(その他) 既存システム物理名: BGZU04';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_OTHER_TAX_EXCL IS '請負(他：税抜) 既存システム物理名: BGZ81A';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_OTHER_TAX1 IS '請負(他：税1) 既存システム物理名: BGZ82A';
COMMENT ON COLUMN BUILDING_INFO_FILE.CONTRACT_OTHER_TAX2 IS '請負(他：税2) 既存システム物理名: BGZ83A';
COMMENT ON COLUMN BUILDING_INFO_FILE.BUILDING_CD IS '建物コード 既存システム物理名: BGZABC';
COMMENT ON COLUMN BUILDING_INFO_FILE.ORDER_CD_ST IS '受注コード-ST 既存システム物理名: BGZ0ST';
COMMENT ON COLUMN BUILDING_INFO_FILE.ADDITIONAL_CD_ST IS '追加コード-ST 既存システム物理名: BGZ1ST';
COMMENT ON COLUMN BUILDING_INFO_FILE.BUILDING_NO_ST IS '棟番号-ST 既存システム物理名: BGZ2ST';
COMMENT ON COLUMN BUILDING_INFO_FILE.PET_FRIENDLY_FLAG IS 'ペット共生区分 既存システム物理名: BGZPTB';
COMMENT ON COLUMN BUILDING_INFO_FILE.ELECTRICITY_TYPE IS '電力区分 既存システム物理名: BGZDEN';
COMMENT ON COLUMN BUILDING_INFO_FILE.SOLAR_BUSINESS_TYPE IS '太陽光事業区分 既存システム物理名: BGZTYB';
COMMENT ON COLUMN BUILDING_INFO_FILE.SOLAR_POWER_OUTPUT IS '太陽光電力量 既存システム物理名: BGZTYW';
COMMENT ON COLUMN BUILDING_INFO_FILE.ZEH_FLAG IS 'ＺＥＨ区分 既存システム物理名: BGZZEB';
