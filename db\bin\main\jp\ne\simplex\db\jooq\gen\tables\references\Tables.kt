/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.references


import jp.ne.simplex.db.jooq.gen.tables.AccidentPropertyManagementTable
import jp.ne.simplex.db.jooq.gen.tables.AccidentPropertyManagementVBTable
import jp.ne.simplex.db.jooq.gen.tables.AccidentPropertyManagementVRTable
import jp.ne.simplex.db.jooq.gen.tables.AddressMasterTable
import jp.ne.simplex.db.jooq.gen.tables.AffiliationMasterTable
import jp.ne.simplex.db.jooq.gen.tables.AgentTable
import jp.ne.simplex.db.jooq.gen.tables.AutoLockNoTable
import jp.ne.simplex.db.jooq.gen.tables.BatchExecuteHistoryTable
import jp.ne.simplex.db.jooq.gen.tables.BelsApplicationResultProgressFileTable
import jp.ne.simplex.db.jooq.gen.tables.BranchFileTable
import jp.ne.simplex.db.jooq.gen.tables.BuildingBasicInfoFileTable
import jp.ne.simplex.db.jooq.gen.tables.BuildingImageTable
import jp.ne.simplex.db.jooq.gen.tables.BuildingInfoFileTable
import jp.ne.simplex.db.jooq.gen.tables.BuildingInfoMasterTable
import jp.ne.simplex.db.jooq.gen.tables.BuildingLocationInfoTable
import jp.ne.simplex.db.jooq.gen.tables.BuildingMaintenanceInfoTable
import jp.ne.simplex.db.jooq.gen.tables.BuildingMasterTable
import jp.ne.simplex.db.jooq.gen.tables.BuildingMemoTable
import jp.ne.simplex.db.jooq.gen.tables.BuildingSpecificUtilityInfoTable
import jp.ne.simplex.db.jooq.gen.tables.BuildingStoreMasterTable
import jp.ne.simplex.db.jooq.gen.tables.BuildingTypeMasterTable
import jp.ne.simplex.db.jooq.gen.tables.Building_11DigitAddressTable
import jp.ne.simplex.db.jooq.gen.tables.BulkLeaseParkingTable
import jp.ne.simplex.db.jooq.gen.tables.CalendarMasterTable
import jp.ne.simplex.db.jooq.gen.tables.CatchCopyRegistrationTable
import jp.ne.simplex.db.jooq.gen.tables.CityGasRateAddressFileTable
import jp.ne.simplex.db.jooq.gen.tables.CodeMappingTable
import jp.ne.simplex.db.jooq.gen.tables.CommonCodeTable
import jp.ne.simplex.db.jooq.gen.tables.ConsumptionTaxRateMasterTable
import jp.ne.simplex.db.jooq.gen.tables.ContractFileTable
import jp.ne.simplex.db.jooq.gen.tables.ContractTable
import jp.ne.simplex.db.jooq.gen.tables.CoreProductLinkMasterTable
import jp.ne.simplex.db.jooq.gen.tables.CoreProductMasterTable
import jp.ne.simplex.db.jooq.gen.tables.CustomerTable
import jp.ne.simplex.db.jooq.gen.tables.DaikenOfficeMasterTable
import jp.ne.simplex.db.jooq.gen.tables.DaitoBuildingManagementTableTable
import jp.ne.simplex.db.jooq.gen.tables.DaitoBuildingManagementTableVTable
import jp.ne.simplex.db.jooq.gen.tables.DaitoBulkLeaseContractDetailsTable
import jp.ne.simplex.db.jooq.gen.tables.DaitoPartnersOfficeCdTableTable
import jp.ne.simplex.db.jooq.gen.tables.DkLinkControlTable
import jp.ne.simplex.db.jooq.gen.tables.ElectricityBusinessMasterTable
import jp.ne.simplex.db.jooq.gen.tables.EmailAddressMasterTable
import jp.ne.simplex.db.jooq.gen.tables.EmployeeMasterTable
import jp.ne.simplex.db.jooq.gen.tables.EnergySavingLabelInfoFileTable
import jp.ne.simplex.db.jooq.gen.tables.EquipmentMasterTable
import jp.ne.simplex.db.jooq.gen.tables.ExclusivePropertyECodeTable
import jp.ne.simplex.db.jooq.gen.tables.ExclusivePropertyTable
import jp.ne.simplex.db.jooq.gen.tables.FixedItemFileTable
import jp.ne.simplex.db.jooq.gen.tables.FixedTermRentalInfoMasterTable
import jp.ne.simplex.db.jooq.gen.tables.GasparBuildingInfoMasterTable
import jp.ne.simplex.db.jooq.gen.tables.GeographicSurveyAddressMasterTable
import jp.ne.simplex.db.jooq.gen.tables.HrCategoryTableBTable
import jp.ne.simplex.db.jooq.gen.tables.ImageCommentMasterTable
import jp.ne.simplex.db.jooq.gen.tables.ImageCopySupportFileTable
import jp.ne.simplex.db.jooq.gen.tables.ImportDbHistoryTable
import jp.ne.simplex.db.jooq.gen.tables.KtAllBranchTable
import jp.ne.simplex.db.jooq.gen.tables.LatestProductMasterTable
import jp.ne.simplex.db.jooq.gen.tables.LatestRentEvaluationTable
import jp.ne.simplex.db.jooq.gen.tables.LatestRoomEquipmentFileTable
import jp.ne.simplex.db.jooq.gen.tables.LayoutImageRegistrationTable
import jp.ne.simplex.db.jooq.gen.tables.LeasingStoreTableTable
import jp.ne.simplex.db.jooq.gen.tables.MailForwardMasterTable
import jp.ne.simplex.db.jooq.gen.tables.ManagedPropertyDataIbmLayoutTable
import jp.ne.simplex.db.jooq.gen.tables.MngOnlyUpdContFeeApprovalBuildingTable
import jp.ne.simplex.db.jooq.gen.tables.MngOnly_24hSupportBuildingTable
import jp.ne.simplex.db.jooq.gen.tables.NewOfficeMasterTable
import jp.ne.simplex.db.jooq.gen.tables.NewPropertyNearestStationTable
import jp.ne.simplex.db.jooq.gen.tables.NgWordCatchRegistrationResultTable
import jp.ne.simplex.db.jooq.gen.tables.NgWordCommentRegistrationResultTable
import jp.ne.simplex.db.jooq.gen.tables.OffSiteParkingTable
import jp.ne.simplex.db.jooq.gen.tables.OfficeMasterTable
import jp.ne.simplex.db.jooq.gen.tables.PanoramaAssociatedFileTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingAdditionalInfoMasterTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingContractPossibilityTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingEnableTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingHourlyRentalApprovalTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingImageRegistrationTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingInfoMasterTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingLayoutPinTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingLotMemoTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingMemoTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingReservationFileTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingReservationTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingReservationVTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingSpecialNotesFileTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingTable
import jp.ne.simplex.db.jooq.gen.tables.ParkingVehicleInfoFileTable
import jp.ne.simplex.db.jooq.gen.tables.PasswordMasterTable
import jp.ne.simplex.db.jooq.gen.tables.PermanentSignboardInfoTable
import jp.ne.simplex.db.jooq.gen.tables.PortalMemberIdLinkMasterTable
import jp.ne.simplex.db.jooq.gen.tables.PostKeyTable
import jp.ne.simplex.db.jooq.gen.tables.ProductMasterTable
import jp.ne.simplex.db.jooq.gen.tables.ProductMasterVTable
import jp.ne.simplex.db.jooq.gen.tables.ProductNameMasterTable
import jp.ne.simplex.db.jooq.gen.tables.ProductNameMasterVTable
import jp.ne.simplex.db.jooq.gen.tables.PropertyBasicFileTable
import jp.ne.simplex.db.jooq.gen.tables.PropertyBoardAddressMasterTable
import jp.ne.simplex.db.jooq.gen.tables.PropertyDetailFileExistingCommercialTable
import jp.ne.simplex.db.jooq.gen.tables.PropertyDetailFileExistingResidentialTable
import jp.ne.simplex.db.jooq.gen.tables.PropertyDetailFileNewCommercialTable
import jp.ne.simplex.db.jooq.gen.tables.PropertyDetailFileNewResidentialTable
import jp.ne.simplex.db.jooq.gen.tables.PropertyMaintenanceInfoTable
import jp.ne.simplex.db.jooq.gen.tables.PropertyMemoTable
import jp.ne.simplex.db.jooq.gen.tables.RailwayLineMasterTable
import jp.ne.simplex.db.jooq.gen.tables.RecommendCommentRegistrationTable
import jp.ne.simplex.db.jooq.gen.tables.RegionMasterTable
import jp.ne.simplex.db.jooq.gen.tables.RenewalContractFeeApprovalBuildingTable
import jp.ne.simplex.db.jooq.gen.tables.RentReviewCenterBranchTable
import jp.ne.simplex.db.jooq.gen.tables.RoomImageTable
import jp.ne.simplex.db.jooq.gen.tables.RoomInfoMasterTable
import jp.ne.simplex.db.jooq.gen.tables.RoomInfoMasterVTable
import jp.ne.simplex.db.jooq.gen.tables.RoomInfoMasterWorkCommercialTable
import jp.ne.simplex.db.jooq.gen.tables.RoomInfoMasterWorkExistingResidentialTable
import jp.ne.simplex.db.jooq.gen.tables.RoomInfoMasterWorkNewResidentialTable
import jp.ne.simplex.db.jooq.gen.tables.RoomMasterTable
import jp.ne.simplex.db.jooq.gen.tables.SiteCommentRegistrationTable
import jp.ne.simplex.db.jooq.gen.tables.SitePropertyKentakuForPTable
import jp.ne.simplex.db.jooq.gen.tables.SpecialBuildingMasterTable
import jp.ne.simplex.db.jooq.gen.tables.SpecialBuildingMasterVTable
import jp.ne.simplex.db.jooq.gen.tables.StationMasterTable
import jp.ne.simplex.db.jooq.gen.tables.SurroundingInfoFileTable
import jp.ne.simplex.db.jooq.gen.tables.TemporaryContractTable
import jp.ne.simplex.db.jooq.gen.tables.TemporaryReservationFileTable
import jp.ne.simplex.db.jooq.gen.tables.TenantContractBulkCollectionFileTable
import jp.ne.simplex.db.jooq.gen.tables.TenantContractTable
import jp.ne.simplex.db.jooq.gen.tables.TenantSalesMaintenanceInfoTable
import jp.ne.simplex.db.jooq.gen.tables.TenantTable
import jp.ne.simplex.db.jooq.gen.tables.ThirtyFiveYearBulkBuildingFileContTable
import jp.ne.simplex.db.jooq.gen.tables.UtilityCompanyMasterTable
import jp.ne.simplex.db.jooq.gen.tables.UtilityDepartmentMasterTable
import jp.ne.simplex.db.jooq.gen.tables.UtilityGuideMasterTable
import jp.ne.simplex.db.jooq.gen.tables.UtilityMasterTable
import jp.ne.simplex.db.jooq.gen.tables.VacantHouseHpTable
import jp.ne.simplex.db.jooq.gen.tables.VacantParkingListTable
import jp.ne.simplex.db.jooq.gen.tables.WaonPointAuthorityMasterTable



/**
 * 事故物件管理 既存システム物理名: FEJ1BP
 */
val ACCIDENT_PROPERTY_MANAGEMENT: AccidentPropertyManagementTable = AccidentPropertyManagementTable.ACCIDENT_PROPERTY_MANAGEMENT

/**
 * The table <code>app.accident_property_management_v_b</code>.
 */
val ACCIDENT_PROPERTY_MANAGEMENT_V_B: AccidentPropertyManagementVBTable = AccidentPropertyManagementVBTable.ACCIDENT_PROPERTY_MANAGEMENT_V_B

/**
 * The table <code>app.accident_property_management_v_r</code>.
 */
val ACCIDENT_PROPERTY_MANAGEMENT_V_R: AccidentPropertyManagementVRTable = AccidentPropertyManagementVRTable.ACCIDENT_PROPERTY_MANAGEMENT_V_R

/**
 * 住所マスタ 既存システム物理名: XXADRP
 */
val ADDRESS_MASTER: AddressMasterTable = AddressMasterTable.ADDRESS_MASTER

/**
 * 所属マスタ 既存システム物理名: JXB1MP
 */
val AFFILIATION_MASTER: AffiliationMasterTable = AffiliationMasterTable.AFFILIATION_MASTER

/**
 * 仲介業者 既存システム物理名: ELA10P
 */
val AGENT: AgentTable = AgentTable.AGENT

/**
 * オートロックNo 既存システム物理名: EMKEYP
 */
val AUTO_LOCK_NO: AutoLockNoTable = AutoLockNoTable.AUTO_LOCK_NO

/**
 * 定期バッチ実行履歴 既存システム物理名: -
 */
val BATCH_EXECUTE_HISTORY: BatchExecuteHistoryTable = BatchExecuteHistoryTable.BATCH_EXECUTE_HISTORY

/**
 * BELS申請結果進捗ファイル 既存システム物理名: BELSMP
 */
val BELS_APPLICATION_RESULT_PROGRESS_FILE: BelsApplicationResultProgressFileTable = BelsApplicationResultProgressFileTable.BELS_APPLICATION_RESULT_PROGRESS_FILE

/**
 * 支店ファイル 既存システム物理名: EMUSIP
 */
val BRANCH_FILE: BranchFileTable = BranchFileTable.BRANCH_FILE

/**
 * 建物11桁住所 既存システム物理名: EAD11P
 */
val BUILDING_11_DIGIT_ADDRESS: Building_11DigitAddressTable = Building_11DigitAddressTable.BUILDING_11_DIGIT_ADDRESS

/**
 * 建物基本情報ファイル 既存システム物理名: HAD40P
 */
val BUILDING_BASIC_INFO_FILE: BuildingBasicInfoFileTable = BuildingBasicInfoFileTable.BUILDING_BASIC_INFO_FILE

/**
 * 建物画像 既存システム物理名: ERATGP
 */
val BUILDING_IMAGE: BuildingImageTable = BuildingImageTable.BUILDING_IMAGE

/**
 * 建物情報ファイル 既存システム物理名: BGZFDP
 */
val BUILDING_INFO_FILE: BuildingInfoFileTable = BuildingInfoFileTable.BUILDING_INFO_FILE

/**
 * 建物情報マスタ 既存システム物理名: EMUR2P
 */
val BUILDING_INFO_MASTER: BuildingInfoMasterTable = BuildingInfoMasterTable.BUILDING_INFO_MASTER

/**
 * 建物位置情報 既存システム物理名: EMECMP
 */
val BUILDING_LOCATION_INFO: BuildingLocationInfoTable = BuildingLocationInfoTable.BUILDING_LOCATION_INFO

/**
 * 建物メンテナンス情報 既存システム物理名: EBLDMP
 */
val BUILDING_MAINTENANCE_INFO: BuildingMaintenanceInfoTable = BuildingMaintenanceInfoTable.BUILDING_MAINTENANCE_INFO

/**
 * 建物マスタ 既存システム物理名: ECMD0P
 */
val BUILDING_MASTER: BuildingMasterTable = BuildingMasterTable.BUILDING_MASTER

/**
 * 建物メモ 既存システム物理名: -
 */
val BUILDING_MEMO: BuildingMemoTable = BuildingMemoTable.BUILDING_MEMO

/**
 * 建物別 ライフライン情報 既存システム物理名: YITLLP
 */
val BUILDING_SPECIFIC_UTILITY_INFO: BuildingSpecificUtilityInfoTable = BuildingSpecificUtilityInfoTable.BUILDING_SPECIFIC_UTILITY_INFO

/**
 * 建物店舗マスタ 既存システム物理名: ECM10P
 */
val BUILDING_STORE_MASTER: BuildingStoreMasterTable = BuildingStoreMasterTable.BUILDING_STORE_MASTER

/**
 * 建物種別マスタ 既存システム物理名: XXHUSP
 */
val BUILDING_TYPE_MASTER: BuildingTypeMasterTable = BuildingTypeMasterTable.BUILDING_TYPE_MASTER

/**
 * 一括借上駐車場 既存システム物理名: EDD30P
 */
val BULK_LEASE_PARKING: BulkLeaseParkingTable = BulkLeaseParkingTable.BULK_LEASE_PARKING

/**
 * カレンダーマスタ 既存システム物理名: XXYMDP
 */
val CALENDAR_MASTER: CalendarMasterTable = CalendarMasterTable.CALENDAR_MASTER

/**
 * キャッチコピー登録 既存システム物理名: EMEKCP
 */
val CATCH_COPY_REGISTRATION: CatchCopyRegistrationTable = CatchCopyRegistrationTable.CATCH_COPY_REGISTRATION

/**
 * 都市ガス料金対象住所ファイル 既存システム物理名: FFF10P
 */
val CITY_GAS_RATE_ADDRESS_FILE: CityGasRateAddressFileTable = CityGasRateAddressFileTable.CITY_GAS_RATE_ADDRESS_FILE

/**
 * The table <code>app.code_mapping</code>.
 */
val CODE_MAPPING: CodeMappingTable = CodeMappingTable.CODE_MAPPING

/**
 * 共用コード 既存システム物理名: EZB20P
 */
val COMMON_CODE: CommonCodeTable = CommonCodeTable.COMMON_CODE

/**
 * 消費税率マスタ 既存システム物理名: EZJA0P
 */
val CONSUMPTION_TAX_RATE_MASTER: ConsumptionTaxRateMasterTable = ConsumptionTaxRateMasterTable.CONSUMPTION_TAX_RATE_MASTER

/**
 * 契約書 既存システム物理名: HCC30P
 */
val CONTRACT: ContractTable = ContractTable.CONTRACT

/**
 * 請負契約ファイル 既存システム物理名: AEUKYP
 */
val CONTRACT_FILE: ContractFileTable = ContractFileTable.CONTRACT_FILE

/**
 * 基幹商品紐付けマスタ 既存システム物理名: ERA20P
 */
val CORE_PRODUCT_LINK_MASTER: CoreProductLinkMasterTable = CoreProductLinkMasterTable.CORE_PRODUCT_LINK_MASTER

/**
 * 基幹商品マスタ 既存システム物理名: ERA16P
 */
val CORE_PRODUCT_MASTER: CoreProductMasterTable = CoreProductMasterTable.CORE_PRODUCT_MASTER

/**
 * 顧客 既存システム物理名: AXCIFP
 */
val CUSTOMER: CustomerTable = CustomerTable.CUSTOMER

/**
 * 大建営業所マスタ 既存システム物理名: HKA10P
 */
val DAIKEN_OFFICE_MASTER: DaikenOfficeMasterTable = DaikenOfficeMasterTable.DAIKEN_OFFICE_MASTER

/**
 * 大東建物管理対応表 既存システム物理名: FXX20P
 */
val DAITO_BUILDING_MANAGEMENT_TABLE: DaitoBuildingManagementTableTable = DaitoBuildingManagementTableTable.DAITO_BUILDING_MANAGEMENT_TABLE

/**
 * The table <code>app.daito_building_management_table_v</code>.
 */
val DAITO_BUILDING_MANAGEMENT_TABLE_V: DaitoBuildingManagementTableVTable = DaitoBuildingManagementTableVTable.DAITO_BUILDING_MANAGEMENT_TABLE_V

/**
 * 大東一括借上・契約内容 既存システム物理名: HUA10P
 */
val DAITO_BULK_LEASE_CONTRACT_DETAILS: DaitoBulkLeaseContractDetailsTable = DaitoBulkLeaseContractDetailsTable.DAITO_BULK_LEASE_CONTRACT_DETAILS

/**
 * 大東パートナーズ営業所CD表 既存システム物理名: EDP10P
 */
val DAITO_PARTNERS_OFFICE_CD_TABLE: DaitoPartnersOfficeCdTableTable = DaitoPartnersOfficeCdTableTable.DAITO_PARTNERS_OFFICE_CD_TABLE

/**
 * DKリンク制御 既存システム物理名: -
 */
val DK_LINK_CONTROL: DkLinkControlTable = DkLinkControlTable.DK_LINK_CONTROL

/**
 * 電力事業マスタ 既存システム物理名: BGDENP
 */
val ELECTRICITY_BUSINESS_MASTER: ElectricityBusinessMasterTable = ElectricityBusinessMasterTable.ELECTRICITY_BUSINESS_MASTER

/**
 * メールアドレスマスタ 既存システム物理名: XXMADP
 */
val EMAIL_ADDRESS_MASTER: EmailAddressMasterTable = EmailAddressMasterTable.EMAIL_ADDRESS_MASTER

/**
 * 社員マスタ 既存システム物理名: XXEMPP
 */
val EMPLOYEE_MASTER: EmployeeMasterTable = EmployeeMasterTable.EMPLOYEE_MASTER

/**
 * 省エネラベル情報ファイル 既存システム物理名: BESELP
 */
val ENERGY_SAVING_LABEL_INFO_FILE: EnergySavingLabelInfoFileTable = EnergySavingLabelInfoFileTable.ENERGY_SAVING_LABEL_INFO_FILE

/**
 * 設備マスタ 既存システム物理名: EJISMP
 */
val EQUIPMENT_MASTER: EquipmentMasterTable = EquipmentMasterTable.EQUIPMENT_MASTER

/**
 * 先行公開 既存システム物理名: -
 */
val EXCLUSIVE_PROPERTY: ExclusivePropertyTable = ExclusivePropertyTable.EXCLUSIVE_PROPERTY

/**
 * 先行公開Eコードテーブル 既存システム物理名: -
 */
val EXCLUSIVE_PROPERTY_E_CODE: ExclusivePropertyECodeTable = ExclusivePropertyECodeTable.EXCLUSIVE_PROPERTY_E_CODE

/**
 * 固定項目ファイル 既存システム物理名: HVX10P
 */
val FIXED_ITEM_FILE: FixedItemFileTable = FixedItemFileTable.FIXED_ITEM_FILE

/**
 * 定期借家情報マスタ 既存システム物理名: ECNE5P
 */
val FIXED_TERM_RENTAL_INFO_MASTER: FixedTermRentalInfoMasterTable = FixedTermRentalInfoMasterTable.FIXED_TERM_RENTAL_INFO_MASTER

/**
 * ガスパル建物情報マスタ 既存システム物理名: FFD10P
 */
val GASPAR_BUILDING_INFO_MASTER: GasparBuildingInfoMasterTable = GasparBuildingInfoMasterTable.GASPAR_BUILDING_INFO_MASTER

/**
 * 国土地理院住所マスタ 既存システム物理名: EDIADP
 */
val GEOGRAPHIC_SURVEY_ADDRESS_MASTER: GeographicSurveyAddressMasterTable = GeographicSurveyAddressMasterTable.GEOGRAPHIC_SURVEY_ADDRESS_MASTER

/**
 * 人事区分テーブル(Ｂ) 既存システム物理名: JXE1MP
 */
val HR_CATEGORY_TABLE_B: HrCategoryTableBTable = HrCategoryTableBTable.HR_CATEGORY_TABLE_B

/**
 * 画像コメントマスタ 既存システム物理名: ERCOMP
 */
val IMAGE_COMMENT_MASTER: ImageCommentMasterTable = ImageCommentMasterTable.IMAGE_COMMENT_MASTER

/**
 * 画像コピー対応ファイル 既存システム物理名: ERCPGP
 */
val IMAGE_COPY_SUPPORT_FILE: ImageCopySupportFileTable = ImageCopySupportFileTable.IMAGE_COPY_SUPPORT_FILE

/**
 * DB取込実行履歴 既存システム物理名: -
 */
val IMPORT_DB_HISTORY: ImportDbHistoryTable = ImportDbHistoryTable.IMPORT_DB_HISTORY

/**
 * 店舗・審査支店マスタ(VIEW) 既存システム物理名: KT_ALL_BRANCH
 */
val KT_ALL_BRANCH: KtAllBranchTable = KtAllBranchTable.KT_ALL_BRANCH

/**
 * 最新商品マスタ 既存システム物理名: EGJKCP
 */
val LATEST_PRODUCT_MASTER: LatestProductMasterTable = LatestProductMasterTable.LATEST_PRODUCT_MASTER

/**
 * 最新家賃査定 既存システム物理名: EAC30P
 */
val LATEST_RENT_EVALUATION: LatestRentEvaluationTable = LatestRentEvaluationTable.LATEST_RENT_EVALUATION

/**
 * 最新部屋設備ファイル 既存システム物理名: EJJHSP
 */
val LATEST_ROOM_EQUIPMENT_FILE: LatestRoomEquipmentFileTable = LatestRoomEquipmentFileTable.LATEST_ROOM_EQUIPMENT_FILE

/**
 * 配置図画像登録 既存システム物理名: EMEPHP
 */
val LAYOUT_IMAGE_REGISTRATION: LayoutImageRegistrationTable = LayoutImageRegistrationTable.LAYOUT_IMAGE_REGISTRATION

/**
 * リーシング店舗対応表 既存システム物理名: EMEBLP
 */
val LEASING_STORE_TABLE: LeasingStoreTableTable = LeasingStoreTableTable.LEASING_STORE_TABLE

/**
 * メール転送マスタ 既存システム物理名: ERAMSP
 */
val MAIL_FORWARD_MASTER: MailForwardMasterTable = MailForwardMasterTable.MAIL_FORWARD_MASTER

/**
 * 管理物件データ(IBMレイアウト) 既存システム物理名: FBWD0P
 */
val MANAGED_PROPERTY_DATA_IBM_LAYOUT: ManagedPropertyDataIbmLayoutTable = ManagedPropertyDataIbmLayoutTable.MANAGED_PROPERTY_DATA_IBM_LAYOUT

/**
 * 管理のみ24時間サポート建物 既存システム物理名: HLB31P
 */
val MNG_ONLY_24H_SUPPORT_BUILDING: MngOnly_24hSupportBuildingTable = MngOnly_24hSupportBuildingTable.MNG_ONLY_24H_SUPPORT_BUILDING

/**
 * 管理のみ更新契約手数料承諾建物 既存システム物理名: FVI91P
 */
val MNG_ONLY_UPD_CONT_FEE_APPROVAL_BUILDING: MngOnlyUpdContFeeApprovalBuildingTable = MngOnlyUpdContFeeApprovalBuildingTable.MNG_ONLY_UPD_CONT_FEE_APPROVAL_BUILDING

/**
 * 新営業所マスタ 既存システム物理名: -
 */
val NEW_OFFICE_MASTER: NewOfficeMasterTable = NewOfficeMasterTable.NEW_OFFICE_MASTER

/**
 * 新物件最寄駅 既存システム物理名: EMES1P
 */
val NEW_PROPERTY_NEAREST_STATION: NewPropertyNearestStationTable = NewPropertyNearestStationTable.NEW_PROPERTY_NEAREST_STATION

/**
 * NGワードキャッチ登録結果 既存システム物理名: EMETNP
 */
val NG_WORD_CATCH_REGISTRATION_RESULT: NgWordCatchRegistrationResultTable = NgWordCatchRegistrationResultTable.NG_WORD_CATCH_REGISTRATION_RESULT

/**
 * NGワードコメント登録結果 既存システム物理名: EMETKP
 */
val NG_WORD_COMMENT_REGISTRATION_RESULT: NgWordCommentRegistrationResultTable = NgWordCommentRegistrationResultTable.NG_WORD_COMMENT_REGISTRATION_RESULT

/**
 * 敷地外駐車場 既存システム物理名: ECC40P
 */
val OFF_SITE_PARKING: OffSiteParkingTable = OffSiteParkingTable.OFF_SITE_PARKING

/**
 * 事業所マスタ 既存システム物理名: XXJGYP
 */
val OFFICE_MASTER: OfficeMasterTable = OfficeMasterTable.OFFICE_MASTER

/**
 * パノラマ関連付けファイル 既存システム物理名: ERA10P
 */
val PANORAMA_ASSOCIATED_FILE: PanoramaAssociatedFileTable = PanoramaAssociatedFileTable.PANORAMA_ASSOCIATED_FILE

/**
 * 駐車場 既存システム物理名: ECC30P
 */
val PARKING: ParkingTable = ParkingTable.PARKING

/**
 * 駐車場付加情報DB 既存システム物理名: ECC90P
 */
val PARKING_ADDITIONAL_INFO_MASTER: ParkingAdditionalInfoMasterTable = ParkingAdditionalInfoMasterTable.PARKING_ADDITIONAL_INFO_MASTER

/**
 * 駐車場契約可否 既存システム物理名: -
 */
val PARKING_CONTRACT_POSSIBILITY: ParkingContractPossibilityTable = ParkingContractPossibilityTable.PARKING_CONTRACT_POSSIBILITY

/**
 * 駐車場利用停止 既存システム物理名: -
 */
val PARKING_ENABLE: ParkingEnableTable = ParkingEnableTable.PARKING_ENABLE

/**
 * 駐車場時間貸承諾 既存システム物理名: HVR10P
 */
val PARKING_HOURLY_RENTAL_APPROVAL: ParkingHourlyRentalApprovalTable = ParkingHourlyRentalApprovalTable.PARKING_HOURLY_RENTAL_APPROVAL

/**
 * 駐車場画像登録 既存システム物理名: EMEGOP
 */
val PARKING_IMAGE_REGISTRATION: ParkingImageRegistrationTable = ParkingImageRegistrationTable.PARKING_IMAGE_REGISTRATION

/**
 * 駐車場情報DB 既存システム物理名: ECC80P
 */
val PARKING_INFO_MASTER: ParkingInfoMasterTable = ParkingInfoMasterTable.PARKING_INFO_MASTER

/**
 * 駐車場配置図区画ピン 既存システム物理名: -
 */
val PARKING_LAYOUT_PIN: ParkingLayoutPinTable = ParkingLayoutPinTable.PARKING_LAYOUT_PIN

/**
 * 駐車場区画メモ 既存システム物理名: -
 */
val PARKING_LOT_MEMO: ParkingLotMemoTable = ParkingLotMemoTable.PARKING_LOT_MEMO

/**
 * 駐車場メモ 既存システム物理名: -
 */
val PARKING_MEMO: ParkingMemoTable = ParkingMemoTable.PARKING_MEMO

/**
 * 駐車場予約 既存システム物理名: -
 */
val PARKING_RESERVATION: ParkingReservationTable = ParkingReservationTable.PARKING_RESERVATION

/**
 * 駐車場予約ファイル 既存システム物理名: ERC30P
 */
val PARKING_RESERVATION_FILE: ParkingReservationFileTable = ParkingReservationFileTable.PARKING_RESERVATION_FILE

/**
 * The table <code>app.parking_reservation_v</code>.
 */
val PARKING_RESERVATION_V: ParkingReservationVTable = ParkingReservationVTable.PARKING_RESERVATION_V

/**
 * 駐車場特記事項ファイル 既存システム物理名: ERB30P
 */
val PARKING_SPECIAL_NOTES_FILE: ParkingSpecialNotesFileTable = ParkingSpecialNotesFileTable.PARKING_SPECIAL_NOTES_FILE

/**
 * 駐車場車種情報ファイル 既存システム物理名: ERA30P
 */
val PARKING_VEHICLE_INFO_FILE: ParkingVehicleInfoFileTable = ParkingVehicleInfoFileTable.PARKING_VEHICLE_INFO_FILE

/**
 * パスワードマスタ 既存システム物理名: XSPASP
 */
val PASSWORD_MASTER: PasswordMasterTable = PasswordMasterTable.PASSWORD_MASTER

/**
 * 常設看板情報 既存システム物理名: EPSK1P
 */
val PERMANENT_SIGNBOARD_INFO: PermanentSignboardInfoTable = PermanentSignboardInfoTable.PERMANENT_SIGNBOARD_INFO

/**
 * ポータル版会員ID紐付けマスタ 既存システム物理名: ERA15P
 */
val PORTAL_MEMBER_ID_LINK_MASTER: PortalMemberIdLinkMasterTable = PortalMemberIdLinkMasterTable.PORTAL_MEMBER_ID_LINK_MASTER

/**
 * ポスト鍵 既存システム物理名: EMPSTP
 */
val POST_KEY: PostKeyTable = PostKeyTable.POST_KEY

/**
 * 商品マスタ 既存システム物理名: BGKMBP
 */
val PRODUCT_MASTER: ProductMasterTable = ProductMasterTable.PRODUCT_MASTER

/**
 * The table <code>app.product_master_v</code>.
 */
val PRODUCT_MASTER_V: ProductMasterVTable = ProductMasterVTable.PRODUCT_MASTER_V

/**
 * 商品名称マスタ 既存システム物理名: BGJMAP
 */
val PRODUCT_NAME_MASTER: ProductNameMasterTable = ProductNameMasterTable.PRODUCT_NAME_MASTER

/**
 * The table <code>app.product_name_master_v</code>.
 */
val PRODUCT_NAME_MASTER_V: ProductNameMasterVTable = ProductNameMasterVTable.PRODUCT_NAME_MASTER_V

/**
 * 物件基本ファイル 既存システム物理名: BGNF1P
 */
val PROPERTY_BASIC_FILE: PropertyBasicFileTable = PropertyBasicFileTable.PROPERTY_BASIC_FILE

/**
 * 物件ボード用住所M 既存システム物理名: EMEADP
 */
val PROPERTY_BOARD_ADDRESS_MASTER: PropertyBoardAddressMasterTable = PropertyBoardAddressMasterTable.PROPERTY_BOARD_ADDRESS_MASTER

/**
 * 物件明細ファイル(既存事業用)商品管理Web 既存システム物理名: EMUU2P
 */
val PROPERTY_DETAIL_FILE_EXISTING_COMMERCIAL: PropertyDetailFileExistingCommercialTable = PropertyDetailFileExistingCommercialTable.PROPERTY_DETAIL_FILE_EXISTING_COMMERCIAL

/**
 * 物件明細ファイル(既存居住用)商品管理Web 既存システム物理名: EMUU1P
 */
val PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL: PropertyDetailFileExistingResidentialTable = PropertyDetailFileExistingResidentialTable.PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL

/**
 * 物件明細ファイル(新築事業用)商品管理Web 既存システム物理名: EMUU4P
 */
val PROPERTY_DETAIL_FILE_NEW_COMMERCIAL: PropertyDetailFileNewCommercialTable = PropertyDetailFileNewCommercialTable.PROPERTY_DETAIL_FILE_NEW_COMMERCIAL

/**
 * 物件明細ファイル(新築居住用)商品管理Web 既存システム物理名: EMUU3P
 */
val PROPERTY_DETAIL_FILE_NEW_RESIDENTIAL: PropertyDetailFileNewResidentialTable = PropertyDetailFileNewResidentialTable.PROPERTY_DETAIL_FILE_NEW_RESIDENTIAL

/**
 * 物件メンテナンス情報 既存システム物理名: EMEBMP
 */
val PROPERTY_MAINTENANCE_INFO: PropertyMaintenanceInfoTable = PropertyMaintenanceInfoTable.PROPERTY_MAINTENANCE_INFO

/**
 * 物件メモ 既存システム物理名: -
 */
val PROPERTY_MEMO: PropertyMemoTable = PropertyMemoTable.PROPERTY_MEMO

/**
 * 沿線マスタ 既存システム物理名: EZE51P
 */
val RAILWAY_LINE_MASTER: RailwayLineMasterTable = RailwayLineMasterTable.RAILWAY_LINE_MASTER

/**
 * おすすめコメント登録 既存システム物理名: EMEOSP
 */
val RECOMMEND_COMMENT_REGISTRATION: RecommendCommentRegistrationTable = RecommendCommentRegistrationTable.RECOMMEND_COMMENT_REGISTRATION

/**
 * 地域マスタ 既存システム物理名: JXH1MP
 */
val REGION_MASTER: RegionMasterTable = RegionMasterTable.REGION_MASTER

/**
 * 更新契約手数料承諾建物 既存システム物理名: FVI90P
 */
val RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING: RenewalContractFeeApprovalBuildingTable = RenewalContractFeeApprovalBuildingTable.RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING

/**
 * 家賃審査センター管轄支店 既存システム物理名: EJD10P
 */
val RENT_REVIEW_CENTER_BRANCH: RentReviewCenterBranchTable = RentReviewCenterBranchTable.RENT_REVIEW_CENTER_BRANCH

/**
 * 部屋画像 既存システム物理名: ERAHMP
 */
val ROOM_IMAGE: RoomImageTable = RoomImageTable.ROOM_IMAGE

/**
 * 部屋情報マスタ 既存システム物理名: EMUR1P
 */
val ROOM_INFO_MASTER: RoomInfoMasterTable = RoomInfoMasterTable.ROOM_INFO_MASTER

/**
 * The table <code>app.room_info_master_v</code>.
 */
val ROOM_INFO_MASTER_V: RoomInfoMasterVTable = RoomInfoMasterVTable.ROOM_INFO_MASTER_V

/**
 * 物件ボード用空き事業用物件ワーク 既存システム物理名: EMU33P
 */
val ROOM_INFO_MASTER_WORK_COMMERCIAL: RoomInfoMasterWorkCommercialTable = RoomInfoMasterWorkCommercialTable.ROOM_INFO_MASTER_WORK_COMMERCIAL

/**
 * 物件ボード用空き既存物件ワーク 既存システム物理名: EMU32P
 */
val ROOM_INFO_MASTER_WORK_EXISTING_RESIDENTIAL: RoomInfoMasterWorkExistingResidentialTable = RoomInfoMasterWorkExistingResidentialTable.ROOM_INFO_MASTER_WORK_EXISTING_RESIDENTIAL

/**
 * 物件ボード用空き新築物件ワーク 既存システム物理名: EMU31P
 */
val ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL: RoomInfoMasterWorkNewResidentialTable = RoomInfoMasterWorkNewResidentialTable.ROOM_INFO_MASTER_WORK_NEW_RESIDENTIAL

/**
 * 部屋マスタ 既存システム物理名: ECNE0P
 */
val ROOM_MASTER: RoomMasterTable = RoomMasterTable.ROOM_MASTER

/**
 * 各サイトコメント登録 既存システム物理名: EMEKKP
 */
val SITE_COMMENT_REGISTRATION: SiteCommentRegistrationTable = SiteCommentRegistrationTable.SITE_COMMENT_REGISTRATION

/**
 * DK-PORTAL用物件データファイル 既存システム物理名: SITE_PROPERTY_KENTAKU_FOR_P
 */
val SITE_PROPERTY_KENTAKU_FOR_P: SitePropertyKentakuForPTable = SitePropertyKentakuForPTable.SITE_PROPERTY_KENTAKU_FOR_P

/**
 * 特例建物マスタ 既存システム物理名: ECMD9P
 */
val SPECIAL_BUILDING_MASTER: SpecialBuildingMasterTable = SpecialBuildingMasterTable.SPECIAL_BUILDING_MASTER

/**
 * The table <code>app.special_building_master_v</code>.
 */
val SPECIAL_BUILDING_MASTER_V: SpecialBuildingMasterVTable = SpecialBuildingMasterVTable.SPECIAL_BUILDING_MASTER_V

/**
 * 駅マスタ 既存システム物理名: EZF61P
 */
val STATION_MASTER: StationMasterTable = StationMasterTable.STATION_MASTER

/**
 * 周辺情報ファイル 既存システム物理名: ERDSJP
 */
val SURROUNDING_INFO_FILE: SurroundingInfoFileTable = SurroundingInfoFileTable.SURROUNDING_INFO_FILE

/**
 * 仮契約書 既存システム物理名: HCC35P
 */
val TEMPORARY_CONTRACT: TemporaryContractTable = TemporaryContractTable.TEMPORARY_CONTRACT

/**
 * 仮押さえファイル 既存システム物理名: ERA03P
 */
val TEMPORARY_RESERVATION_FILE: TemporaryReservationFileTable = TemporaryReservationFileTable.TEMPORARY_RESERVATION_FILE

/**
 * テナント 既存システム物理名: EEA10P
 */
val TENANT: TenantTable = TenantTable.TENANT

/**
 * テナント契約 既存システム物理名: ECB20P
 */
val TENANT_CONTRACT: TenantContractTable = TenantContractTable.TENANT_CONTRACT

/**
 * テナント契約一括残集ファイル 既存システム物理名: EDCTNP
 */
val TENANT_CONTRACT_BULK_COLLECTION_FILE: TenantContractBulkCollectionFileTable = TenantContractBulkCollectionFileTable.TENANT_CONTRACT_BULK_COLLECTION_FILE

/**
 * テナント営業メンテナンス情報 既存システム物理名: EMETMP
 */
val TENANT_SALES_MAINTENANCE_INFO: TenantSalesMaintenanceInfoTable = TenantSalesMaintenanceInfoTable.TENANT_SALES_MAINTENANCE_INFO

/**
 * 35年一括建物ファイル(契約書) 既存システム物理名: HC360P
 */
val THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT: ThirtyFiveYearBulkBuildingFileContTable = ThirtyFiveYearBulkBuildingFileContTable.THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT

/**
 * ライフライン会社マスタ 既存システム物理名: YCALKP
 */
val UTILITY_COMPANY_MASTER: UtilityCompanyMasterTable = UtilityCompanyMasterTable.UTILITY_COMPANY_MASTER

/**
 * ライフライン部署マスタ 既存システム物理名: YCBLBP
 */
val UTILITY_DEPARTMENT_MASTER: UtilityDepartmentMasterTable = UtilityDepartmentMasterTable.UTILITY_DEPARTMENT_MASTER

/**
 * ライフラインご案内マスタ 既存システム物理名: EMLLGP
 */
val UTILITY_GUIDE_MASTER: UtilityGuideMasterTable = UtilityGuideMasterTable.UTILITY_GUIDE_MASTER

/**
 * ライフラインマスタ 既存システム物理名: HATK0P
 */
val UTILITY_MASTER: UtilityMasterTable = UtilityMasterTable.UTILITY_MASTER

/**
 * 空き家HP用 既存システム物理名: EMEH2P
 */
val VACANT_HOUSE_HP: VacantHouseHpTable = VacantHouseHpTable.VACANT_HOUSE_HP

/**
 * 空き駐車場一覧 既存システム物理名: EMPRKP
 */
val VACANT_PARKING_LIST: VacantParkingListTable = VacantParkingListTable.VACANT_PARKING_LIST

/**
 * WAONポイント権限マスタ 既存システム物理名: EMWAMP
 */
val WAON_POINT_AUTHORITY_MASTER: WaonPointAuthorityMasterTable = WaonPointAuthorityMasterTable.WAON_POINT_AUTHORITY_MASTER
