/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigInteger

import jp.ne.simplex.db.jooq.gen.tables.KtAllBranchTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.KtAllBranchPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 店舗・審査支店マスタ(VIEW) 既存システム物理名: KT_ALL_BRANCH
 */
@Suppress("UNCHECKED_CAST")
open class KtAllBranchRecord private constructor() : UpdatableRecordImpl<KtAllBranchRecord>(KtAllBranchTable.KT_ALL_BRANCH) {

    open var branchCode: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var branchName: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    open var zipCode: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var phone: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var fax: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var email: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var address: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var licenseno: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var prefcd: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var branchComment: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var latitude: Long?
        set(value): Unit = set(10, value)
        get(): Long? = get(10) as Long?

    open var longitude: Long?
        set(value): Unit = set(11, value)
        get(): Long? = get(11) as Long?

    open var openHour: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var webCallingNumber: BigInteger?
        set(value): Unit = set(13, value)
        get(): BigInteger? = get(13) as BigInteger?

    open var closedDay: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var otherComment: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var accessCar: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var accessTrain: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var parkingComment: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var callDaito: BigInteger?
        set(value): Unit = set(19, value)
        get(): BigInteger? = get(19) as BigInteger?

    open var openHourComment: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var companyCode: BigInteger?
        set(value): Unit = set(21, value)
        get(): BigInteger? = get(21) as BigInteger?

    open var eboardCompanyCode: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var addrPrefecture: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var addrCity: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var addrTown: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var addrTyoume: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var addrRestaddr: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var kokudoCodeText: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var jisCode: Int?
        set(value): Unit = set(29, value)
        get(): Int? = get(29) as Int?

    open var updateDate: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised KtAllBranchRecord
     */
    constructor(branchCode: String, branchName: String? = null, zipCode: String? = null, phone: String? = null, fax: String? = null, email: String? = null, address: String? = null, licenseno: String? = null, prefcd: String? = null, branchComment: String? = null, latitude: Long? = null, longitude: Long? = null, openHour: String? = null, webCallingNumber: BigInteger? = null, closedDay: String? = null, otherComment: String? = null, accessCar: String? = null, accessTrain: String? = null, parkingComment: String? = null, callDaito: BigInteger? = null, openHourComment: String? = null, companyCode: BigInteger? = null, eboardCompanyCode: String? = null, addrPrefecture: String? = null, addrCity: String? = null, addrTown: String? = null, addrTyoume: String? = null, addrRestaddr: String? = null, kokudoCodeText: String? = null, jisCode: Int? = null, updateDate: String? = null): this() {
        this.branchCode = branchCode
        this.branchName = branchName
        this.zipCode = zipCode
        this.phone = phone
        this.fax = fax
        this.email = email
        this.address = address
        this.licenseno = licenseno
        this.prefcd = prefcd
        this.branchComment = branchComment
        this.latitude = latitude
        this.longitude = longitude
        this.openHour = openHour
        this.webCallingNumber = webCallingNumber
        this.closedDay = closedDay
        this.otherComment = otherComment
        this.accessCar = accessCar
        this.accessTrain = accessTrain
        this.parkingComment = parkingComment
        this.callDaito = callDaito
        this.openHourComment = openHourComment
        this.companyCode = companyCode
        this.eboardCompanyCode = eboardCompanyCode
        this.addrPrefecture = addrPrefecture
        this.addrCity = addrCity
        this.addrTown = addrTown
        this.addrTyoume = addrTyoume
        this.addrRestaddr = addrRestaddr
        this.kokudoCodeText = kokudoCodeText
        this.jisCode = jisCode
        this.updateDate = updateDate
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised KtAllBranchRecord
     */
    constructor(value: KtAllBranchPojo?): this() {
        if (value != null) {
            this.branchCode = value.branchCode
            this.branchName = value.branchName
            this.zipCode = value.zipCode
            this.phone = value.phone
            this.fax = value.fax
            this.email = value.email
            this.address = value.address
            this.licenseno = value.licenseno
            this.prefcd = value.prefcd
            this.branchComment = value.branchComment
            this.latitude = value.latitude
            this.longitude = value.longitude
            this.openHour = value.openHour
            this.webCallingNumber = value.webCallingNumber
            this.closedDay = value.closedDay
            this.otherComment = value.otherComment
            this.accessCar = value.accessCar
            this.accessTrain = value.accessTrain
            this.parkingComment = value.parkingComment
            this.callDaito = value.callDaito
            this.openHourComment = value.openHourComment
            this.companyCode = value.companyCode
            this.eboardCompanyCode = value.eboardCompanyCode
            this.addrPrefecture = value.addrPrefecture
            this.addrCity = value.addrCity
            this.addrTown = value.addrTown
            this.addrTyoume = value.addrTyoume
            this.addrRestaddr = value.addrRestaddr
            this.kokudoCodeText = value.kokudoCodeText
            this.jisCode = value.jisCode
            this.updateDate = value.updateDate
            resetChangedOnNotNull()
        }
    }
}
