package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Company
import jp.ne.simplex.application.repository.db.extension.KtAllBranchEx.Companion.getBranch
import jp.ne.simplex.application.repository.db.extension.LeasingStoreTableEx.Companion.getBranchCode
import jp.ne.simplex.application.repository.db.extension.LeasingStoreTableEx.Companion.getLeasingBranchCode
import jp.ne.simplex.application.repository.db.pojos.ShinsaBranchPojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.KtAllBranchPojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.LeasingStoreTablePojo
import jp.ne.simplex.db.jooq.gen.tables.references.*
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import org.jooq.DSLContext
import org.jooq.impl.DSL.*
import org.springframework.stereotype.Repository
import java.math.BigInteger
import java.time.LocalDateTime

@Repository
class BranchRepository(private val context: DSLContext) : BranchRepositoryInterface {

    override fun getKtBranch(branchCode: Branch.Code?): Branch? {
        // 審査 ＞ リーシング/センター の順で検索する
        return getShinsaBranch(branchCode).firstOrNull()
            ?: getLeasingOrCenterBranch(branchCode).firstOrNull()
    }

    override fun getKtBranchList(): List<Branch> {
        // リーシング/センター支店 + 審査支店
        return getLeasingOrCenterBranch() + getShinsaBranch()
    }

    override fun getBranchRelatedToLeasing(branchCode: Branch.Code): Branch.Code? {
        val now = LocalDateTime.now().yyyyMMdd().toLong()

        return context.select().from(LEASING_STORE_TABLE)
            .where(LEASING_STORE_TABLE.LEASING_STORE_CD.eq(branchCode.getPrefix()))
            .and(LEASING_STORE_TABLE.START_DATE.lessOrEqual(now))
            .and(LEASING_STORE_TABLE.END_DATE.greaterOrEqual(now))
            .fetchOneInto(LeasingStoreTablePojo::class.java)
            ?.getBranchCode()
    }

    override fun getLeasingRelatedToBranch(branchCode: Branch.Code): Branch.Code? {
        val now = LocalDateTime.now().yyyyMMdd().toLong()

        return context.select().from(LEASING_STORE_TABLE)
            .where(LEASING_STORE_TABLE.BRANCH_CD.eq(branchCode.getPrefix()))
            .and(LEASING_STORE_TABLE.START_DATE.lessOrEqual(now))
            .and(LEASING_STORE_TABLE.END_DATE.greaterOrEqual(now))
            .orderBy(LEASING_STORE_TABLE.STORE_NORTH_ORDER.asc()) // 複数件紐づく場合があるため、ソートする
            .fetchInto(LeasingStoreTablePojo::class.java).firstOrNull()
            ?.getLeasingBranchCode()
    }

    /**
     * KT_BRANCHを取得する(審査支店)
     *
     * 引数に値を設定した場合は、その値で絞り込む。ただし、設定されていない場合は検索条件に含めない
     *
     *  @param branchCode Branch.Code?
     */
    private fun getShinsaBranch(branchCode: Branch.Code? = null): List<Branch> {
        val currentDate = LocalDateTime.now().yyyyMMdd().toInt()

        return context.select(
            AFFILIATION_MASTER.SHOZOKU_CODE,
            AFFILIATION_MASTER.SHOZOKU_ABBREV1,
        ).from(REGION_MASTER)
            .innerJoin(AFFILIATION_MASTER)
            .on(REGION_MASTER.DEPARTMENT_CODE.eq(AFFILIATION_MASTER.SHOZOKU_CODE))
            .leftJoin(BRANCH_FILE).on(
                BRANCH_FILE.BRANCH_CODE.like("%000")
                    .and(
                        AFFILIATION_MASTER.SHOZOKU_CODE.eq(
                            concat(left(BRANCH_FILE.BRANCH_CODE, 3), "800")
                        )
                    )
            )
            .where(REGION_MASTER.REGION_CODE_2.lessThan("50")) // JXH08C
            .and(REGION_MASTER.USE_START_DATE.lessOrEqual(currentDate)) // JXH03D
            .and(REGION_MASTER.USE_FINISH_DATE.greaterOrEqual(currentDate)) // JXH04D
            .and(AFFILIATION_MASTER.USAGE_START_DATE.lessOrEqual(currentDate)) // JXB02D
            .and(AFFILIATION_MASTER.USAGE_END_DATE.greaterOrEqual(currentDate)) // JXB03D
            .and(AFFILIATION_MASTER.HIERARCHY_DIVISION.eq("30")) // JXB09B
            .and(AFFILIATION_MASTER.COMPANY_CODE.eq(Company.DaitouKentaku.code)) // JXB10C
            .and(AFFILIATION_MASTER.SHOZOKU_CODE.like("%800")) // JXB10C
            .and(
                // @formatter:off 支店コードが指定されている場合は、それで絞り込み、指定されていない場合は、条件に含めない
                when (branchCode) {
                    null -> noCondition()
                    else -> AFFILIATION_MASTER.SHOZOKU_CODE.eq(branchCode.getPrefix() + "800")
                }
                // @formatter:on JXB01C
            )
            .orderBy(
                AFFILIATION_MASTER.HR_OUTPUT_ORDER0,
                AFFILIATION_MASTER.HR_OUTPUT_ORDER1,
                AFFILIATION_MASTER.HR_OUTPUT_ORDER2,
                AFFILIATION_MASTER.HR_OUTPUT_ORDER3,
                AFFILIATION_MASTER.HR_OUTPUT_ORDER4,
                AFFILIATION_MASTER.HR_OUTPUT_ORDER5,
                AFFILIATION_MASTER.HR_OUTPUT_ORDER6,
            )
            .fetchInto(ShinsaBranchPojo::class.java).mapNotNull { it.getBranch() }
    }

    /**
     * KT_BRANCHを取得する(リーシング、センター支店)
     *
     * 引数に値を設定した場合は、その値で絞り込む。ただし、設定されていない場合は検索条件に含めない
     *
     *  @param branchCode Branch.Code?
     */
    private fun getLeasingOrCenterBranch(branchCode: Branch.Code? = null): List<Branch> {
        return context.select().from(KT_ALL_BRANCH)
            // 0: リーシング、1:大東建託、3:パートナーズ
            .where(KT_ALL_BRANCH.COMPANY_CODE.`in`(BigInteger.ZERO, BigInteger.ONE))
            .and(
                // 支店コードが指定されている場合は、それで絞り込み、指定されていない場合は、条件に含めない
                when (branchCode) {
                    null -> noCondition()
                    else -> KT_ALL_BRANCH.BRANCH_CODE.eq(branchCode.getPrefix() + "000")
                }
            )
            .orderBy(KT_ALL_BRANCH.PREFCD, KT_ALL_BRANCH.ZIP_CODE)
            .fetchInto(KtAllBranchPojo::class.java).mapNotNull { it.getBranch() }
    }
}

interface BranchRepositoryInterface {
    /** KT_BRANCHを取得する */
    fun getKtBranch(branchCode: Branch.Code?): Branch?

    /** KT_BRANCH一覧を取得する */
    fun getKtBranchList(): List<Branch>

    /** リーシング店舗支店コードに紐づいている支店コード(Employee.affiliationCode)を取得する */
    fun getBranchRelatedToLeasing(branchCode: Branch.Code): Branch.Code?

    /** 支店コード(Employee.affiliationCode)に紐づいているリーシング店舗支店コードを取得する */
    fun getLeasingRelatedToBranch(branchCode: Branch.Code): Branch.Code?
}
