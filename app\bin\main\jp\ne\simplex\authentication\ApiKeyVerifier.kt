package jp.ne.simplex.authentication

import jakarta.servlet.http.HttpServletRequest
import jp.ne.simplex.exception.ErrorType
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class ApiKeyVerifier(private val apikeyAuthProcessor: ApikeyAuthProcessor) {

    companion object {
        private val log = LoggerFactory.getLogger(ApiKeyVerifier::class.java)
    }

    fun verify(request: HttpServletRequest) {
        val apiKey = request.getHeader("x-api-key") ?: run {
            request.setErrorAttribute(ErrorType.INVALID_API_KEY)
            return
        }
        try {
            val authInfo = apikeyAuthProcessor.verify(apiKey, request.servletPath)
            AuthSecurityContext.save(authInfo)
        } catch (ex: Exception) {
            AuthSecurityContext.clear()

            log.info(ex.message, ex)

            when (ex) {
                is InvalidApiKeyException -> request.setErrorAttribute(ErrorType.INVALID_API_KEY)
                else -> request.setErrorAttribute(ErrorType.UNEXPECTED_ERROR)
            }

        }
    }

    private fun HttpServletRequest.setErrorAttribute(errorType: ErrorType) {
        setAttribute("error_type", errorType)
    }
}
