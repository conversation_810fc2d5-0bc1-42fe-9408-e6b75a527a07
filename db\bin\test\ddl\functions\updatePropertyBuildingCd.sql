-- ************************************************************************** --
-- ファンクション名 : UPDATE_PROPERTY_BUILDING_CD
-- 処理概要         : 物件建物CDカラムを更新します
-- 引数             :
-- 戻り値           :
-- 備考             :
-- ************************************************************************** --
CREATE OR REPLACE FUNCTION update_property_building_cd()
RETURNS TRIGGER AS $$
BEGIN
    NEW.property_building_cd := RIGHT('0000000' || RTRIM(NEW.order_code::varchar), 7)
      || RIGHT('00' || RTRIM(NEW.building_number::varchar), 2);

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_property_building_cd
BEFORE INSERT OR UPDATE ON bels_application_result_progress_file
FOR EACH ROW
EXECUTE FUNCTION update_property_building_cd();
