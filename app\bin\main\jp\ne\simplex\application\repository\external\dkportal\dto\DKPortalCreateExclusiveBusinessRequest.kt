package jp.ne.simplex.application.repository.external.dkportal.dto

import jp.ne.simplex.application.model.ExclusivePropertyAction
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalOperationName
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd

class DKPortalCreateExclusiveBusinessRequest(
    property: Property,
    record: ExclusivePropertyAction.Record,
) : DKPortalCreateExclusiveRequest(
    dkLinkId = record.exclusiveTargetWithId.id.value.toString(),
    salesOfficeId = property.marketingBranchOfficeCode!!.value.toInt(),
    kentakuBuildingCode = record.propertyId.buildingCode.value,
    kentakuRoomCode = record.propertyId.roomCode.value,
    exclusiveFrom = record.exclusiveRange.from.yyyyMMdd(),
    exclusiveTo = record.exclusiveRange.to.yyyyMMdd(),
    eCode = record.exclusiveTargetWithId.target.eCode?.value,
    companyType = DKPortalExclusiveCompanyType.of(record.exclusiveTargetWithId.target.companyType).value,
) {
    override fun getDKPortalOperationName(): DKPortalOperationName {
        return DKPortalOperationName.CREATE_EXCLUSIVE_BUSINESS
    }
}
