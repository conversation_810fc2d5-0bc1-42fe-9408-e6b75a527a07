/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.BuildingMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingMasterPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 建物マスタ 既存システム物理名: ECMD0P
 */
@Suppress("UNCHECKED_CAST")
open class BuildingMasterRecord private constructor() : UpdatableRecordImpl<BuildingMasterRecord>(BuildingMasterTable.BUILDING_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteFlag: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var buildingCode: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var bulkLeaseFlag: Byte?
        set(value): Unit = set(8, value)
        get(): Byte? = get(8) as Byte?

    open var buildingName: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var buildingCategory: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var productNameCodeSt: Short?
        set(value): Unit = set(11, value)
        get(): Short? = get(11) as Short?

    open var productGradeCodeSt: Byte?
        set(value): Unit = set(12, value)
        get(): Byte? = get(12) as Byte?

    open var productTypeSerialSt: Short?
        set(value): Unit = set(13, value)
        get(): Short? = get(13) as Short?

    open var buildingTypeCode: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var buildingStructureCategory: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var foundationShapeCategory: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var prefectureCode: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var cityCode: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var townCode: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var addressDetail: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var buildingName2: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var orientationCategory: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var businessUseRoomCount: Short?
        set(value): Unit = set(23, value)
        get(): Short? = get(23) as Short?

    open var residentialUseRoomCount: Short?
        set(value): Unit = set(24, value)
        get(): Short? = get(24) as Short?

    open var floorsAboveGround: Short?
        set(value): Unit = set(25, value)
        get(): Short? = get(25) as Short?

    open var floorsBelowGround: Byte?
        set(value): Unit = set(26, value)
        get(): Byte? = get(26) as Byte?

    open var totalFloorAreaSqm: BigDecimal?
        set(value): Unit = set(27, value)
        get(): BigDecimal? = get(27) as BigDecimal?

    open var constructionFloorAreaSqm: BigDecimal?
        set(value): Unit = set(28, value)
        get(): BigDecimal? = get(28) as BigDecimal?

    open var warehouseFactoryAreaSqm: BigDecimal?
        set(value): Unit = set(29, value)
        get(): BigDecimal? = get(29) as BigDecimal?

    open var officeFlag: Byte?
        set(value): Unit = set(30, value)
        get(): Byte? = get(30) as Byte?

    open var buildingCoverageRatio: BigDecimal?
        set(value): Unit = set(31, value)
        get(): BigDecimal? = get(31) as BigDecimal?

    open var floorAreaRatio: BigDecimal?
        set(value): Unit = set(32, value)
        get(): BigDecimal? = get(32) as BigDecimal?

    open var residentialCategory: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var exteriorWallCategory: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var roofCategory: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var approachEntranceFlag: Byte?
        set(value): Unit = set(36, value)
        get(): Byte? = get(36) as Byte?

    open var fenceFlag: Byte?
        set(value): Unit = set(37, value)
        get(): Byte? = get(37) as Byte?

    open var approachLightSecurityLightFlag: Byte?
        set(value): Unit = set(38, value)
        get(): Byte? = get(38) as Byte?

    open var landscapingFlag: Byte?
        set(value): Unit = set(39, value)
        get(): Byte? = get(39) as Byte?

    open var waterTapFlag: Byte?
        set(value): Unit = set(40, value)
        get(): Byte? = get(40) as Byte?

    open var garbageStationFlag: Byte?
        set(value): Unit = set(41, value)
        get(): Byte? = get(41) as Byte?

    open var parkingSpacePerUnit: Byte?
        set(value): Unit = set(42, value)
        get(): Byte? = get(42) as Byte?

    open var totalParkingSpaces: Short?
        set(value): Unit = set(43, value)
        get(): Short? = get(43) as Short?

    open var leasedParkingSpaces: Short?
        set(value): Unit = set(44, value)
        get(): Short? = get(44) as Short?

    open var sLeasedParkingSpaces: Short?
        set(value): Unit = set(45, value)
        get(): Short? = get(45) as Short?

    open var bicycleParkingFlag: Byte?
        set(value): Unit = set(46, value)
        get(): Byte? = get(46) as Byte?

    open var waterSupplyCategory: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var sewageCategory: String?
        set(value): Unit = set(48, value)
        get(): String? = get(48) as String?

    open var gasCategory: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    open var toiletCategory: String?
        set(value): Unit = set(50, value)
        get(): String? = get(50) as String?

    open var telephoneCategory: String?
        set(value): Unit = set(51, value)
        get(): String? = get(51) as String?

    open var electricityFlag: Byte?
        set(value): Unit = set(52, value)
        get(): Byte? = get(52) as Byte?

    open var equipmentCode: String?
        set(value): Unit = set(53, value)
        get(): String? = get(53) as String?

    open var featureCode: String?
        set(value): Unit = set(54, value)
        get(): String? = get(54) as String?

    open var remarksCode: String?
        set(value): Unit = set(55, value)
        get(): String? = get(55) as String?

    open var septicTankAssessment: Int?
        set(value): Unit = set(56, value)
        get(): Int? = get(56) as Int?

    open var septicTankCategory: String?
        set(value): Unit = set(57, value)
        get(): String? = get(57) as String?

    open var landCode: String?
        set(value): Unit = set(58, value)
        get(): String? = get(58) as String?

    open var railwayLineCode: String?
        set(value): Unit = set(59, value)
        get(): String? = get(59) as String?

    open var stationCode: String?
        set(value): Unit = set(60, value)
        get(): String? = get(60) as String?

    open var nearestBusLineName: String?
        set(value): Unit = set(61, value)
        get(): String? = get(61) as String?

    open var nearestBusStopName: String?
        set(value): Unit = set(62, value)
        get(): String? = get(62) as String?

    open var busRideTime: Short?
        set(value): Unit = set(63, value)
        get(): Short? = get(63) as Short?

    open var walkingTime: Short?
        set(value): Unit = set(64, value)
        get(): Short? = get(64) as Short?

    open var railwayNearestStationDistance: BigDecimal?
        set(value): Unit = set(65, value)
        get(): BigDecimal? = get(65) as BigDecimal?

    open var surroundingFacilitiesDistance: Short?
        set(value): Unit = set(66, value)
        get(): Short? = get(66) as Short?

    open var surroundingFacilitiesTime: Short?
        set(value): Unit = set(67, value)
        get(): Short? = get(67) as Short?

    open var surroundingFacilitiesCode: String?
        set(value): Unit = set(68, value)
        get(): String? = get(68) as String?

    open var orderCode: String?
        set(value): Unit = set(69, value)
        get(): String? = get(69) as String?

    open var contractorConstructionFlag: Byte?
        set(value): Unit = set(70, value)
        get(): Byte? = get(70) as Byte?

    open var thirdPartyConstructionFlag: Byte?
        set(value): Unit = set(71, value)
        get(): Byte? = get(71) as Byte?

    open var landlordCode: String?
        set(value): Unit = set(72, value)
        get(): String? = get(72) as String?

    open var managementFlag: Byte?
        set(value): Unit = set(73, value)
        get(): Byte? = get(73) as Byte?

    open var consentFormCollectionFlag: Byte?
        set(value): Unit = set(74, value)
        get(): Byte? = get(74) as Byte?

    open var externalVacantCategory: String?
        set(value): Unit = set(75, value)
        get(): String? = get(75) as String?

    open var electricitySupplierCode: String?
        set(value): Unit = set(76, value)
        get(): String? = get(76) as String?

    open var electricityMeterCategory: String?
        set(value): Unit = set(77, value)
        get(): String? = get(77) as String?

    open var powerFlag: Byte?
        set(value): Unit = set(78, value)
        get(): Byte? = get(78) as Byte?

    open var gasMeterCategory: String?
        set(value): Unit = set(79, value)
        get(): String? = get(79) as String?

    open var waterSupplyCategoryForDisaster: String?
        set(value): Unit = set(80, value)
        get(): String? = get(80) as String?

    open var waterMeterCategory: String?
        set(value): Unit = set(81, value)
        get(): String? = get(81) as String?

    open var electricalEquipmentManagerCode: String?
        set(value): Unit = set(82, value)
        get(): String? = get(82) as String?

    open var gasEquipmentManagerCode: String?
        set(value): Unit = set(83, value)
        get(): String? = get(83) as String?

    open var sewageEquipmentManagerCode: String?
        set(value): Unit = set(84, value)
        get(): String? = get(84) as String?

    open var waterTankManagerCode: String?
        set(value): Unit = set(85, value)
        get(): String? = get(85) as String?

    open var septicTankManagerCode: String?
        set(value): Unit = set(86, value)
        get(): String? = get(86) as String?

    open var buildingSiteCleaningManagerCode: String?
        set(value): Unit = set(87, value)
        get(): String? = get(87) as String?

    open var fireEquipmentManagerCode: String?
        set(value): Unit = set(88, value)
        get(): String? = get(88) as String?

    open var contractHoldFlag: Byte?
        set(value): Unit = set(89, value)
        get(): Byte? = get(89) as Byte?

    open var applicationProcessHoldFlag: Byte?
        set(value): Unit = set(90, value)
        get(): Byte? = get(90) as Byte?

    open var constructionProcessHoldFlag: Byte?
        set(value): Unit = set(91, value)
        get(): Byte? = get(91) as Byte?

    open var constructionStopFlag: Byte?
        set(value): Unit = set(92, value)
        get(): Byte? = get(92) as Byte?

    open var inspectionFlag: Byte?
        set(value): Unit = set(93, value)
        get(): Byte? = get(93) as Byte?

    open var governmentInspectionEntryDate: Int?
        set(value): Unit = set(94, value)
        get(): Int? = get(94) as Int?

    open var governmentInspectionCompletionDate: Int?
        set(value): Unit = set(95, value)
        get(): Int? = get(95) as Int?

    open var temporaryUseInspectionEntryDate: Int?
        set(value): Unit = set(96, value)
        get(): Int? = get(96) as Int?

    open var temporaryUseApplicationApprovalDate: Int?
        set(value): Unit = set(97, value)
        get(): Int? = get(97) as Int?

    open var standardDeviationFlag: Byte?
        set(value): Unit = set(98, value)
        get(): Byte? = get(98) as Byte?

    open var vacantHouseAccountingDate: Int?
        set(value): Unit = set(99, value)
        get(): Int? = get(99) as Int?

    open var rentGuaranteeDate: Int?
        set(value): Unit = set(100, value)
        get(): Int? = get(100) as Int?

    open var completionExpectedDate: Int?
        set(value): Unit = set(101, value)
        get(): Int? = get(101) as Int?

    open var completionDeliveryDate: Int?
        set(value): Unit = set(102, value)
        get(): Int? = get(102) as Int?

    open var contractorConstructionCompletionDate: Int?
        set(value): Unit = set(103, value)
        get(): Int? = get(103) as Int?

    open var housingLoanCorporationLoanFlag: Byte?
        set(value): Unit = set(104, value)
        get(): Byte? = get(104) as Byte?

    open var loanCorporationApprovalStartDate: Int?
        set(value): Unit = set(105, value)
        get(): Int? = get(105) as Int?

    open var applicationAcceptanceDate: Int?
        set(value): Unit = set(106, value)
        get(): Int? = get(106) as Int?

    open var principalAdvanceFlag: Byte?
        set(value): Unit = set(107, value)
        get(): Byte? = get(107) as Byte?

    open var tenantRestrictionCode: String?
        set(value): Unit = set(108, value)
        get(): String? = get(108) as String?

    open var contractBranchCode: String?
        set(value): Unit = set(109, value)
        get(): String? = get(109) as String?

    open var tenantRecruitmentBranchCode: String?
        set(value): Unit = set(110, value)
        get(): String? = get(110) as String?

    open var managementBranchCode: String?
        set(value): Unit = set(111, value)
        get(): String? = get(111) as String?

    open var feeBearingLandlord: Short?
        set(value): Unit = set(112, value)
        get(): Short? = get(112) as Short?

    open var feeBearingTenant: Short?
        set(value): Unit = set(113, value)
        get(): Short? = get(113) as Short?

    open var feeDistributionOrigin: Short?
        set(value): Unit = set(114, value)
        get(): Short? = get(114) as Short?

    open var feeDistributionTenant: Short?
        set(value): Unit = set(115, value)
        get(): Short? = get(115) as Short?

    open var tenantRecruitmentNotes: String?
        set(value): Unit = set(116, value)
        get(): String? = get(116) as String?

    open var areaCategory_1: String?
        set(value): Unit = set(117, value)
        get(): String? = get(117) as String?

    open var areaBaseCode_1: String?
        set(value): Unit = set(118, value)
        get(): String? = get(118) as String?

    open var areaDetailCode_1: String?
        set(value): Unit = set(119, value)
        get(): String? = get(119) as String?

    open var areaCategory_2: String?
        set(value): Unit = set(120, value)
        get(): String? = get(120) as String?

    open var areaBaseCode_2: String?
        set(value): Unit = set(121, value)
        get(): String? = get(121) as String?

    open var areaDetailCode_2: String?
        set(value): Unit = set(122, value)
        get(): String? = get(122) as String?

    open var areaCategory_3: String?
        set(value): Unit = set(123, value)
        get(): String? = get(123) as String?

    open var areaBaseCode_3: String?
        set(value): Unit = set(124, value)
        get(): String? = get(124) as String?

    open var areaDetailCode_3: String?
        set(value): Unit = set(125, value)
        get(): String? = get(125) as String?

    open var areaCategory_4: String?
        set(value): Unit = set(126, value)
        get(): String? = get(126) as String?

    open var areaBaseCode_4: String?
        set(value): Unit = set(127, value)
        get(): String? = get(127) as String?

    open var areaDetailCode_4: String?
        set(value): Unit = set(128, value)
        get(): String? = get(128) as String?

    open var areaCategory_5: String?
        set(value): Unit = set(129, value)
        get(): String? = get(129) as String?

    open var areaBaseCode_5: String?
        set(value): Unit = set(130, value)
        get(): String? = get(130) as String?

    open var areaDetailCode_5: String?
        set(value): Unit = set(131, value)
        get(): String? = get(131) as String?

    open var areaCategory_6: String?
        set(value): Unit = set(132, value)
        get(): String? = get(132) as String?

    open var areaBaseCode_6: String?
        set(value): Unit = set(133, value)
        get(): String? = get(133) as String?

    open var areaDetailCode_6: String?
        set(value): Unit = set(134, value)
        get(): String? = get(134) as String?

    open var areaCategory_7: String?
        set(value): Unit = set(135, value)
        get(): String? = get(135) as String?

    open var areaBaseCode_7: String?
        set(value): Unit = set(136, value)
        get(): String? = get(136) as String?

    open var areaDetailCode_7: String?
        set(value): Unit = set(137, value)
        get(): String? = get(137) as String?

    open var buildingManagerCode: String?
        set(value): Unit = set(138, value)
        get(): String? = get(138) as String?

    open var rentManagerCode: String?
        set(value): Unit = set(139, value)
        get(): String? = get(139) as String?

    open var contractManagerCode: String?
        set(value): Unit = set(140, value)
        get(): String? = get(140) as String?

    open var houseComPurchaseFlag: Byte?
        set(value): Unit = set(141, value)
        get(): Byte? = get(141) as Byte?

    open var prefectureCode2: String?
        set(value): Unit = set(142, value)
        get(): String? = get(142) as String?

    open var cityCode2: String?
        set(value): Unit = set(143, value)
        get(): String? = get(143) as String?

    open var townCode2: String?
        set(value): Unit = set(144, value)
        get(): String? = get(144) as String?

    open var addressDetail2: String?
        set(value): Unit = set(145, value)
        get(): String? = get(145) as String?

    open var ownerCode_1: String?
        set(value): Unit = set(146, value)
        get(): String? = get(146) as String?

    open var ownerCode_2: String?
        set(value): Unit = set(147, value)
        get(): String? = get(147) as String?

    open var ownerCode_3: String?
        set(value): Unit = set(148, value)
        get(): String? = get(148) as String?

    open var ownershipDetail_1: String?
        set(value): Unit = set(149, value)
        get(): String? = get(149) as String?

    open var ownershipDetail_2: String?
        set(value): Unit = set(150, value)
        get(): String? = get(150) as String?

    open var ownershipDetail_3: String?
        set(value): Unit = set(151, value)
        get(): String? = get(151) as String?

    open var ownershipDetail_4: String?
        set(value): Unit = set(152, value)
        get(): String? = get(152) as String?

    open var ownershipDetail_5: String?
        set(value): Unit = set(153, value)
        get(): String? = get(153) as String?

    open var nonOwnershipDetail_1: String?
        set(value): Unit = set(154, value)
        get(): String? = get(154) as String?

    open var nonOwnershipDetail_2: String?
        set(value): Unit = set(155, value)
        get(): String? = get(155) as String?

    open var nonOwnershipDetail_3: String?
        set(value): Unit = set(156, value)
        get(): String? = get(156) as String?

    open var nonOwnershipDetail_4: String?
        set(value): Unit = set(157, value)
        get(): String? = get(157) as String?

    open var nonOwnershipDetail_5: String?
        set(value): Unit = set(158, value)
        get(): String? = get(158) as String?

    open var managementStartDate: Int?
        set(value): Unit = set(159, value)
        get(): Int? = get(159) as Int?

    open var managementEndDate: Int?
        set(value): Unit = set(160, value)
        get(): Int? = get(160) as Int?

    open var agreedTerminationDate: Int?
        set(value): Unit = set(161, value)
        get(): Int? = get(161) as Int?

    open var branchCode: String?
        set(value): Unit = set(162, value)
        get(): String? = get(162) as String?

    open var managementStaffCode: String?
        set(value): Unit = set(163, value)
        get(): String? = get(163) as String?

    open var managementStartDate2: Int?
        set(value): Unit = set(164, value)
        get(): Int? = get(164) as Int?

    open var interfaceFlag: Byte?
        set(value): Unit = set(165, value)
        get(): Byte? = get(165) as Byte?

    open var tenantExtractionFlag: Byte?
        set(value): Unit = set(166, value)
        get(): Byte? = get(166) as Byte?

    open var dataMigrationSourceKey_1: String?
        set(value): Unit = set(167, value)
        get(): String? = get(167) as String?

    open var dataMigrationSourceKey_2: String?
        set(value): Unit = set(168, value)
        get(): String? = get(168) as String?

    open var nearestBusStopDistance: BigDecimal?
        set(value): Unit = set(169, value)
        get(): BigDecimal? = get(169) as BigDecimal?

    open var catchcopyContent: String?
        set(value): Unit = set(170, value)
        get(): String? = get(170) as String?

    open var recruitmentInfoInputFlag: Byte?
        set(value): Unit = set(171, value)
        get(): Byte? = get(171) as Byte?

    open var listOutputFlag: Byte?
        set(value): Unit = set(172, value)
        get(): Byte? = get(172) as Byte?

    open var newBuildRentCategory: String?
        set(value): Unit = set(173, value)
        get(): String? = get(173) as String?

    open var companyCode: String?
        set(value): Unit = set(174, value)
        get(): String? = get(174) as String?

    open var depositCategory: String?
        set(value): Unit = set(175, value)
        get(): String? = get(175) as String?

    open var guaranteeDeductCategory: String?
        set(value): Unit = set(176, value)
        get(): String? = get(176) as String?

    open var newHouseGuarantee_10yPkCount: Short?
        set(value): Unit = set(177, value)
        get(): Short? = get(177) as Short?

    open var newGuaranteeRate: BigDecimal?
        set(value): Unit = set(178, value)
        get(): BigDecimal? = get(178) as BigDecimal?

    open var newGuaranteeManagementRate: BigDecimal?
        set(value): Unit = set(179, value)
        get(): BigDecimal? = get(179) as BigDecimal?

    open var contractMutualAidFeeRate: BigDecimal?
        set(value): Unit = set(180, value)
        get(): BigDecimal? = get(180) as BigDecimal?

    open var keyMoneyFlag: String?
        set(value): Unit = set(181, value)
        get(): String? = get(181) as String?

    open var managementFeeAccountingCategory: String?
        set(value): Unit = set(182, value)
        get(): String? = get(182) as String?

    open var screeningBranchCode: String?
        set(value): Unit = set(183, value)
        get(): String? = get(183) as String?

    open var residenceDisplayCategory: String?
        set(value): Unit = set(184, value)
        get(): String? = get(184) as String?

    open var landlordSignatureConsentCategory: String?
        set(value): Unit = set(185, value)
        get(): String? = get(185) as String?

    open var loanCorporationRestoreCategory: String?
        set(value): Unit = set(186, value)
        get(): String? = get(186) as String?

    open var leaseContractNumber: String?
        set(value): Unit = set(187, value)
        get(): String? = get(187) as String?

    open var guarantorNotRequiredCategory: Byte?
        set(value): Unit = set(188, value)
        get(): Byte? = get(188) as Byte?

    open var comPartnershipCategory: Byte?
        set(value): Unit = set(189, value)
        get(): Byte? = get(189) as Byte?

    open var contractorCompanyCategory: Byte?
        set(value): Unit = set(190, value)
        get(): Byte? = get(190) as Byte?

    open var specialRent: Byte?
        set(value): Unit = set(191, value)
        get(): Byte? = get(191) as Byte?

    open var realEstatePartnershipCategory: Byte?
        set(value): Unit = set(192, value)
        get(): Byte? = get(192) as Byte?

    open var supportMechanismCategory: Byte?
        set(value): Unit = set(193, value)
        get(): Byte? = get(193) as Byte?

    open var telephoneConsentCategory: Byte?
        set(value): Unit = set(194, value)
        get(): Byte? = get(194) as Byte?

    open var buildingNameFlag: Byte?
        set(value): Unit = set(195, value)
        get(): Byte? = get(195) as Byte?

    open var fireResistantStructureCategory: Byte?
        set(value): Unit = set(196, value)
        get(): Byte? = get(196) as Byte?

    open var managementInheritanceCategory: Byte?
        set(value): Unit = set(197, value)
        get(): Byte? = get(197) as Byte?

    open var unused_9: Byte?
        set(value): Unit = set(198, value)
        get(): Byte? = get(198) as Byte?

    open var managementStaffCodeSt: String?
        set(value): Unit = set(199, value)
        get(): String? = get(199) as String?

    open var daikenBranchCode: String?
        set(value): Unit = set(200, value)
        get(): String? = get(200) as String?

    open var houseNumber: String?
        set(value): Unit = set(201, value)
        get(): String? = get(201) as String?

    open var marketingBranchOfficeCd: String?
        set(value): Unit = set(202, value)
        get(): String? = get(202) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised BuildingMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteFlag: Byte? = null, buildingCode: String, bulkLeaseFlag: Byte? = null, buildingName: String? = null, buildingCategory: String? = null, productNameCodeSt: Short? = null, productGradeCodeSt: Byte? = null, productTypeSerialSt: Short? = null, buildingTypeCode: String? = null, buildingStructureCategory: String? = null, foundationShapeCategory: String? = null, prefectureCode: String? = null, cityCode: String? = null, townCode: String? = null, addressDetail: String? = null, buildingName2: String? = null, orientationCategory: String? = null, businessUseRoomCount: Short? = null, residentialUseRoomCount: Short? = null, floorsAboveGround: Short? = null, floorsBelowGround: Byte? = null, totalFloorAreaSqm: BigDecimal? = null, constructionFloorAreaSqm: BigDecimal? = null, warehouseFactoryAreaSqm: BigDecimal? = null, officeFlag: Byte? = null, buildingCoverageRatio: BigDecimal? = null, floorAreaRatio: BigDecimal? = null, residentialCategory: String? = null, exteriorWallCategory: String? = null, roofCategory: String? = null, approachEntranceFlag: Byte? = null, fenceFlag: Byte? = null, approachLightSecurityLightFlag: Byte? = null, landscapingFlag: Byte? = null, waterTapFlag: Byte? = null, garbageStationFlag: Byte? = null, parkingSpacePerUnit: Byte? = null, totalParkingSpaces: Short? = null, leasedParkingSpaces: Short? = null, sLeasedParkingSpaces: Short? = null, bicycleParkingFlag: Byte? = null, waterSupplyCategory: String? = null, sewageCategory: String? = null, gasCategory: String? = null, toiletCategory: String? = null, telephoneCategory: String? = null, electricityFlag: Byte? = null, equipmentCode: String? = null, featureCode: String? = null, remarksCode: String? = null, septicTankAssessment: Int? = null, septicTankCategory: String? = null, landCode: String? = null, railwayLineCode: String? = null, stationCode: String? = null, nearestBusLineName: String? = null, nearestBusStopName: String? = null, busRideTime: Short? = null, walkingTime: Short? = null, railwayNearestStationDistance: BigDecimal? = null, surroundingFacilitiesDistance: Short? = null, surroundingFacilitiesTime: Short? = null, surroundingFacilitiesCode: String? = null, orderCode: String? = null, contractorConstructionFlag: Byte? = null, thirdPartyConstructionFlag: Byte? = null, landlordCode: String? = null, managementFlag: Byte? = null, consentFormCollectionFlag: Byte? = null, externalVacantCategory: String? = null, electricitySupplierCode: String? = null, electricityMeterCategory: String? = null, powerFlag: Byte? = null, gasMeterCategory: String? = null, waterSupplyCategoryForDisaster: String? = null, waterMeterCategory: String? = null, electricalEquipmentManagerCode: String? = null, gasEquipmentManagerCode: String? = null, sewageEquipmentManagerCode: String? = null, waterTankManagerCode: String? = null, septicTankManagerCode: String? = null, buildingSiteCleaningManagerCode: String? = null, fireEquipmentManagerCode: String? = null, contractHoldFlag: Byte? = null, applicationProcessHoldFlag: Byte? = null, constructionProcessHoldFlag: Byte? = null, constructionStopFlag: Byte? = null, inspectionFlag: Byte? = null, governmentInspectionEntryDate: Int? = null, governmentInspectionCompletionDate: Int? = null, temporaryUseInspectionEntryDate: Int? = null, temporaryUseApplicationApprovalDate: Int? = null, standardDeviationFlag: Byte? = null, vacantHouseAccountingDate: Int? = null, rentGuaranteeDate: Int? = null, completionExpectedDate: Int? = null, completionDeliveryDate: Int? = null, contractorConstructionCompletionDate: Int? = null, housingLoanCorporationLoanFlag: Byte? = null, loanCorporationApprovalStartDate: Int? = null, applicationAcceptanceDate: Int? = null, principalAdvanceFlag: Byte? = null, tenantRestrictionCode: String? = null, contractBranchCode: String? = null, tenantRecruitmentBranchCode: String? = null, managementBranchCode: String? = null, feeBearingLandlord: Short? = null, feeBearingTenant: Short? = null, feeDistributionOrigin: Short? = null, feeDistributionTenant: Short? = null, tenantRecruitmentNotes: String? = null, areaCategory_1: String? = null, areaBaseCode_1: String? = null, areaDetailCode_1: String? = null, areaCategory_2: String? = null, areaBaseCode_2: String? = null, areaDetailCode_2: String? = null, areaCategory_3: String? = null, areaBaseCode_3: String? = null, areaDetailCode_3: String? = null, areaCategory_4: String? = null, areaBaseCode_4: String? = null, areaDetailCode_4: String? = null, areaCategory_5: String? = null, areaBaseCode_5: String? = null, areaDetailCode_5: String? = null, areaCategory_6: String? = null, areaBaseCode_6: String? = null, areaDetailCode_6: String? = null, areaCategory_7: String? = null, areaBaseCode_7: String? = null, areaDetailCode_7: String? = null, buildingManagerCode: String? = null, rentManagerCode: String? = null, contractManagerCode: String? = null, houseComPurchaseFlag: Byte? = null, prefectureCode2: String? = null, cityCode2: String? = null, townCode2: String? = null, addressDetail2: String? = null, ownerCode_1: String? = null, ownerCode_2: String? = null, ownerCode_3: String? = null, ownershipDetail_1: String? = null, ownershipDetail_2: String? = null, ownershipDetail_3: String? = null, ownershipDetail_4: String? = null, ownershipDetail_5: String? = null, nonOwnershipDetail_1: String? = null, nonOwnershipDetail_2: String? = null, nonOwnershipDetail_3: String? = null, nonOwnershipDetail_4: String? = null, nonOwnershipDetail_5: String? = null, managementStartDate: Int? = null, managementEndDate: Int? = null, agreedTerminationDate: Int? = null, branchCode: String? = null, managementStaffCode: String? = null, managementStartDate2: Int? = null, interfaceFlag: Byte? = null, tenantExtractionFlag: Byte? = null, dataMigrationSourceKey_1: String? = null, dataMigrationSourceKey_2: String? = null, nearestBusStopDistance: BigDecimal? = null, catchcopyContent: String? = null, recruitmentInfoInputFlag: Byte? = null, listOutputFlag: Byte? = null, newBuildRentCategory: String? = null, companyCode: String? = null, depositCategory: String? = null, guaranteeDeductCategory: String? = null, newHouseGuarantee_10yPkCount: Short? = null, newGuaranteeRate: BigDecimal? = null, newGuaranteeManagementRate: BigDecimal? = null, contractMutualAidFeeRate: BigDecimal? = null, keyMoneyFlag: String? = null, managementFeeAccountingCategory: String? = null, screeningBranchCode: String? = null, residenceDisplayCategory: String? = null, landlordSignatureConsentCategory: String? = null, loanCorporationRestoreCategory: String? = null, leaseContractNumber: String? = null, guarantorNotRequiredCategory: Byte? = null, comPartnershipCategory: Byte? = null, contractorCompanyCategory: Byte? = null, specialRent: Byte? = null, realEstatePartnershipCategory: Byte? = null, supportMechanismCategory: Byte? = null, telephoneConsentCategory: Byte? = null, buildingNameFlag: Byte? = null, fireResistantStructureCategory: Byte? = null, managementInheritanceCategory: Byte? = null, unused_9: Byte? = null, managementStaffCodeSt: String? = null, daikenBranchCode: String? = null, houseNumber: String? = null, marketingBranchOfficeCd: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteFlag = logicalDeleteFlag
        this.buildingCode = buildingCode
        this.bulkLeaseFlag = bulkLeaseFlag
        this.buildingName = buildingName
        this.buildingCategory = buildingCategory
        this.productNameCodeSt = productNameCodeSt
        this.productGradeCodeSt = productGradeCodeSt
        this.productTypeSerialSt = productTypeSerialSt
        this.buildingTypeCode = buildingTypeCode
        this.buildingStructureCategory = buildingStructureCategory
        this.foundationShapeCategory = foundationShapeCategory
        this.prefectureCode = prefectureCode
        this.cityCode = cityCode
        this.townCode = townCode
        this.addressDetail = addressDetail
        this.buildingName2 = buildingName2
        this.orientationCategory = orientationCategory
        this.businessUseRoomCount = businessUseRoomCount
        this.residentialUseRoomCount = residentialUseRoomCount
        this.floorsAboveGround = floorsAboveGround
        this.floorsBelowGround = floorsBelowGround
        this.totalFloorAreaSqm = totalFloorAreaSqm
        this.constructionFloorAreaSqm = constructionFloorAreaSqm
        this.warehouseFactoryAreaSqm = warehouseFactoryAreaSqm
        this.officeFlag = officeFlag
        this.buildingCoverageRatio = buildingCoverageRatio
        this.floorAreaRatio = floorAreaRatio
        this.residentialCategory = residentialCategory
        this.exteriorWallCategory = exteriorWallCategory
        this.roofCategory = roofCategory
        this.approachEntranceFlag = approachEntranceFlag
        this.fenceFlag = fenceFlag
        this.approachLightSecurityLightFlag = approachLightSecurityLightFlag
        this.landscapingFlag = landscapingFlag
        this.waterTapFlag = waterTapFlag
        this.garbageStationFlag = garbageStationFlag
        this.parkingSpacePerUnit = parkingSpacePerUnit
        this.totalParkingSpaces = totalParkingSpaces
        this.leasedParkingSpaces = leasedParkingSpaces
        this.sLeasedParkingSpaces = sLeasedParkingSpaces
        this.bicycleParkingFlag = bicycleParkingFlag
        this.waterSupplyCategory = waterSupplyCategory
        this.sewageCategory = sewageCategory
        this.gasCategory = gasCategory
        this.toiletCategory = toiletCategory
        this.telephoneCategory = telephoneCategory
        this.electricityFlag = electricityFlag
        this.equipmentCode = equipmentCode
        this.featureCode = featureCode
        this.remarksCode = remarksCode
        this.septicTankAssessment = septicTankAssessment
        this.septicTankCategory = septicTankCategory
        this.landCode = landCode
        this.railwayLineCode = railwayLineCode
        this.stationCode = stationCode
        this.nearestBusLineName = nearestBusLineName
        this.nearestBusStopName = nearestBusStopName
        this.busRideTime = busRideTime
        this.walkingTime = walkingTime
        this.railwayNearestStationDistance = railwayNearestStationDistance
        this.surroundingFacilitiesDistance = surroundingFacilitiesDistance
        this.surroundingFacilitiesTime = surroundingFacilitiesTime
        this.surroundingFacilitiesCode = surroundingFacilitiesCode
        this.orderCode = orderCode
        this.contractorConstructionFlag = contractorConstructionFlag
        this.thirdPartyConstructionFlag = thirdPartyConstructionFlag
        this.landlordCode = landlordCode
        this.managementFlag = managementFlag
        this.consentFormCollectionFlag = consentFormCollectionFlag
        this.externalVacantCategory = externalVacantCategory
        this.electricitySupplierCode = electricitySupplierCode
        this.electricityMeterCategory = electricityMeterCategory
        this.powerFlag = powerFlag
        this.gasMeterCategory = gasMeterCategory
        this.waterSupplyCategoryForDisaster = waterSupplyCategoryForDisaster
        this.waterMeterCategory = waterMeterCategory
        this.electricalEquipmentManagerCode = electricalEquipmentManagerCode
        this.gasEquipmentManagerCode = gasEquipmentManagerCode
        this.sewageEquipmentManagerCode = sewageEquipmentManagerCode
        this.waterTankManagerCode = waterTankManagerCode
        this.septicTankManagerCode = septicTankManagerCode
        this.buildingSiteCleaningManagerCode = buildingSiteCleaningManagerCode
        this.fireEquipmentManagerCode = fireEquipmentManagerCode
        this.contractHoldFlag = contractHoldFlag
        this.applicationProcessHoldFlag = applicationProcessHoldFlag
        this.constructionProcessHoldFlag = constructionProcessHoldFlag
        this.constructionStopFlag = constructionStopFlag
        this.inspectionFlag = inspectionFlag
        this.governmentInspectionEntryDate = governmentInspectionEntryDate
        this.governmentInspectionCompletionDate = governmentInspectionCompletionDate
        this.temporaryUseInspectionEntryDate = temporaryUseInspectionEntryDate
        this.temporaryUseApplicationApprovalDate = temporaryUseApplicationApprovalDate
        this.standardDeviationFlag = standardDeviationFlag
        this.vacantHouseAccountingDate = vacantHouseAccountingDate
        this.rentGuaranteeDate = rentGuaranteeDate
        this.completionExpectedDate = completionExpectedDate
        this.completionDeliveryDate = completionDeliveryDate
        this.contractorConstructionCompletionDate = contractorConstructionCompletionDate
        this.housingLoanCorporationLoanFlag = housingLoanCorporationLoanFlag
        this.loanCorporationApprovalStartDate = loanCorporationApprovalStartDate
        this.applicationAcceptanceDate = applicationAcceptanceDate
        this.principalAdvanceFlag = principalAdvanceFlag
        this.tenantRestrictionCode = tenantRestrictionCode
        this.contractBranchCode = contractBranchCode
        this.tenantRecruitmentBranchCode = tenantRecruitmentBranchCode
        this.managementBranchCode = managementBranchCode
        this.feeBearingLandlord = feeBearingLandlord
        this.feeBearingTenant = feeBearingTenant
        this.feeDistributionOrigin = feeDistributionOrigin
        this.feeDistributionTenant = feeDistributionTenant
        this.tenantRecruitmentNotes = tenantRecruitmentNotes
        this.areaCategory_1 = areaCategory_1
        this.areaBaseCode_1 = areaBaseCode_1
        this.areaDetailCode_1 = areaDetailCode_1
        this.areaCategory_2 = areaCategory_2
        this.areaBaseCode_2 = areaBaseCode_2
        this.areaDetailCode_2 = areaDetailCode_2
        this.areaCategory_3 = areaCategory_3
        this.areaBaseCode_3 = areaBaseCode_3
        this.areaDetailCode_3 = areaDetailCode_3
        this.areaCategory_4 = areaCategory_4
        this.areaBaseCode_4 = areaBaseCode_4
        this.areaDetailCode_4 = areaDetailCode_4
        this.areaCategory_5 = areaCategory_5
        this.areaBaseCode_5 = areaBaseCode_5
        this.areaDetailCode_5 = areaDetailCode_5
        this.areaCategory_6 = areaCategory_6
        this.areaBaseCode_6 = areaBaseCode_6
        this.areaDetailCode_6 = areaDetailCode_6
        this.areaCategory_7 = areaCategory_7
        this.areaBaseCode_7 = areaBaseCode_7
        this.areaDetailCode_7 = areaDetailCode_7
        this.buildingManagerCode = buildingManagerCode
        this.rentManagerCode = rentManagerCode
        this.contractManagerCode = contractManagerCode
        this.houseComPurchaseFlag = houseComPurchaseFlag
        this.prefectureCode2 = prefectureCode2
        this.cityCode2 = cityCode2
        this.townCode2 = townCode2
        this.addressDetail2 = addressDetail2
        this.ownerCode_1 = ownerCode_1
        this.ownerCode_2 = ownerCode_2
        this.ownerCode_3 = ownerCode_3
        this.ownershipDetail_1 = ownershipDetail_1
        this.ownershipDetail_2 = ownershipDetail_2
        this.ownershipDetail_3 = ownershipDetail_3
        this.ownershipDetail_4 = ownershipDetail_4
        this.ownershipDetail_5 = ownershipDetail_5
        this.nonOwnershipDetail_1 = nonOwnershipDetail_1
        this.nonOwnershipDetail_2 = nonOwnershipDetail_2
        this.nonOwnershipDetail_3 = nonOwnershipDetail_3
        this.nonOwnershipDetail_4 = nonOwnershipDetail_4
        this.nonOwnershipDetail_5 = nonOwnershipDetail_5
        this.managementStartDate = managementStartDate
        this.managementEndDate = managementEndDate
        this.agreedTerminationDate = agreedTerminationDate
        this.branchCode = branchCode
        this.managementStaffCode = managementStaffCode
        this.managementStartDate2 = managementStartDate2
        this.interfaceFlag = interfaceFlag
        this.tenantExtractionFlag = tenantExtractionFlag
        this.dataMigrationSourceKey_1 = dataMigrationSourceKey_1
        this.dataMigrationSourceKey_2 = dataMigrationSourceKey_2
        this.nearestBusStopDistance = nearestBusStopDistance
        this.catchcopyContent = catchcopyContent
        this.recruitmentInfoInputFlag = recruitmentInfoInputFlag
        this.listOutputFlag = listOutputFlag
        this.newBuildRentCategory = newBuildRentCategory
        this.companyCode = companyCode
        this.depositCategory = depositCategory
        this.guaranteeDeductCategory = guaranteeDeductCategory
        this.newHouseGuarantee_10yPkCount = newHouseGuarantee_10yPkCount
        this.newGuaranteeRate = newGuaranteeRate
        this.newGuaranteeManagementRate = newGuaranteeManagementRate
        this.contractMutualAidFeeRate = contractMutualAidFeeRate
        this.keyMoneyFlag = keyMoneyFlag
        this.managementFeeAccountingCategory = managementFeeAccountingCategory
        this.screeningBranchCode = screeningBranchCode
        this.residenceDisplayCategory = residenceDisplayCategory
        this.landlordSignatureConsentCategory = landlordSignatureConsentCategory
        this.loanCorporationRestoreCategory = loanCorporationRestoreCategory
        this.leaseContractNumber = leaseContractNumber
        this.guarantorNotRequiredCategory = guarantorNotRequiredCategory
        this.comPartnershipCategory = comPartnershipCategory
        this.contractorCompanyCategory = contractorCompanyCategory
        this.specialRent = specialRent
        this.realEstatePartnershipCategory = realEstatePartnershipCategory
        this.supportMechanismCategory = supportMechanismCategory
        this.telephoneConsentCategory = telephoneConsentCategory
        this.buildingNameFlag = buildingNameFlag
        this.fireResistantStructureCategory = fireResistantStructureCategory
        this.managementInheritanceCategory = managementInheritanceCategory
        this.unused_9 = unused_9
        this.managementStaffCodeSt = managementStaffCodeSt
        this.daikenBranchCode = daikenBranchCode
        this.houseNumber = houseNumber
        this.marketingBranchOfficeCd = marketingBranchOfficeCd
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BuildingMasterRecord
     */
    constructor(value: BuildingMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteFlag = value.logicalDeleteFlag
            this.buildingCode = value.buildingCode
            this.bulkLeaseFlag = value.bulkLeaseFlag
            this.buildingName = value.buildingName
            this.buildingCategory = value.buildingCategory
            this.productNameCodeSt = value.productNameCodeSt
            this.productGradeCodeSt = value.productGradeCodeSt
            this.productTypeSerialSt = value.productTypeSerialSt
            this.buildingTypeCode = value.buildingTypeCode
            this.buildingStructureCategory = value.buildingStructureCategory
            this.foundationShapeCategory = value.foundationShapeCategory
            this.prefectureCode = value.prefectureCode
            this.cityCode = value.cityCode
            this.townCode = value.townCode
            this.addressDetail = value.addressDetail
            this.buildingName2 = value.buildingName2
            this.orientationCategory = value.orientationCategory
            this.businessUseRoomCount = value.businessUseRoomCount
            this.residentialUseRoomCount = value.residentialUseRoomCount
            this.floorsAboveGround = value.floorsAboveGround
            this.floorsBelowGround = value.floorsBelowGround
            this.totalFloorAreaSqm = value.totalFloorAreaSqm
            this.constructionFloorAreaSqm = value.constructionFloorAreaSqm
            this.warehouseFactoryAreaSqm = value.warehouseFactoryAreaSqm
            this.officeFlag = value.officeFlag
            this.buildingCoverageRatio = value.buildingCoverageRatio
            this.floorAreaRatio = value.floorAreaRatio
            this.residentialCategory = value.residentialCategory
            this.exteriorWallCategory = value.exteriorWallCategory
            this.roofCategory = value.roofCategory
            this.approachEntranceFlag = value.approachEntranceFlag
            this.fenceFlag = value.fenceFlag
            this.approachLightSecurityLightFlag = value.approachLightSecurityLightFlag
            this.landscapingFlag = value.landscapingFlag
            this.waterTapFlag = value.waterTapFlag
            this.garbageStationFlag = value.garbageStationFlag
            this.parkingSpacePerUnit = value.parkingSpacePerUnit
            this.totalParkingSpaces = value.totalParkingSpaces
            this.leasedParkingSpaces = value.leasedParkingSpaces
            this.sLeasedParkingSpaces = value.sLeasedParkingSpaces
            this.bicycleParkingFlag = value.bicycleParkingFlag
            this.waterSupplyCategory = value.waterSupplyCategory
            this.sewageCategory = value.sewageCategory
            this.gasCategory = value.gasCategory
            this.toiletCategory = value.toiletCategory
            this.telephoneCategory = value.telephoneCategory
            this.electricityFlag = value.electricityFlag
            this.equipmentCode = value.equipmentCode
            this.featureCode = value.featureCode
            this.remarksCode = value.remarksCode
            this.septicTankAssessment = value.septicTankAssessment
            this.septicTankCategory = value.septicTankCategory
            this.landCode = value.landCode
            this.railwayLineCode = value.railwayLineCode
            this.stationCode = value.stationCode
            this.nearestBusLineName = value.nearestBusLineName
            this.nearestBusStopName = value.nearestBusStopName
            this.busRideTime = value.busRideTime
            this.walkingTime = value.walkingTime
            this.railwayNearestStationDistance = value.railwayNearestStationDistance
            this.surroundingFacilitiesDistance = value.surroundingFacilitiesDistance
            this.surroundingFacilitiesTime = value.surroundingFacilitiesTime
            this.surroundingFacilitiesCode = value.surroundingFacilitiesCode
            this.orderCode = value.orderCode
            this.contractorConstructionFlag = value.contractorConstructionFlag
            this.thirdPartyConstructionFlag = value.thirdPartyConstructionFlag
            this.landlordCode = value.landlordCode
            this.managementFlag = value.managementFlag
            this.consentFormCollectionFlag = value.consentFormCollectionFlag
            this.externalVacantCategory = value.externalVacantCategory
            this.electricitySupplierCode = value.electricitySupplierCode
            this.electricityMeterCategory = value.electricityMeterCategory
            this.powerFlag = value.powerFlag
            this.gasMeterCategory = value.gasMeterCategory
            this.waterSupplyCategoryForDisaster = value.waterSupplyCategoryForDisaster
            this.waterMeterCategory = value.waterMeterCategory
            this.electricalEquipmentManagerCode = value.electricalEquipmentManagerCode
            this.gasEquipmentManagerCode = value.gasEquipmentManagerCode
            this.sewageEquipmentManagerCode = value.sewageEquipmentManagerCode
            this.waterTankManagerCode = value.waterTankManagerCode
            this.septicTankManagerCode = value.septicTankManagerCode
            this.buildingSiteCleaningManagerCode = value.buildingSiteCleaningManagerCode
            this.fireEquipmentManagerCode = value.fireEquipmentManagerCode
            this.contractHoldFlag = value.contractHoldFlag
            this.applicationProcessHoldFlag = value.applicationProcessHoldFlag
            this.constructionProcessHoldFlag = value.constructionProcessHoldFlag
            this.constructionStopFlag = value.constructionStopFlag
            this.inspectionFlag = value.inspectionFlag
            this.governmentInspectionEntryDate = value.governmentInspectionEntryDate
            this.governmentInspectionCompletionDate = value.governmentInspectionCompletionDate
            this.temporaryUseInspectionEntryDate = value.temporaryUseInspectionEntryDate
            this.temporaryUseApplicationApprovalDate = value.temporaryUseApplicationApprovalDate
            this.standardDeviationFlag = value.standardDeviationFlag
            this.vacantHouseAccountingDate = value.vacantHouseAccountingDate
            this.rentGuaranteeDate = value.rentGuaranteeDate
            this.completionExpectedDate = value.completionExpectedDate
            this.completionDeliveryDate = value.completionDeliveryDate
            this.contractorConstructionCompletionDate = value.contractorConstructionCompletionDate
            this.housingLoanCorporationLoanFlag = value.housingLoanCorporationLoanFlag
            this.loanCorporationApprovalStartDate = value.loanCorporationApprovalStartDate
            this.applicationAcceptanceDate = value.applicationAcceptanceDate
            this.principalAdvanceFlag = value.principalAdvanceFlag
            this.tenantRestrictionCode = value.tenantRestrictionCode
            this.contractBranchCode = value.contractBranchCode
            this.tenantRecruitmentBranchCode = value.tenantRecruitmentBranchCode
            this.managementBranchCode = value.managementBranchCode
            this.feeBearingLandlord = value.feeBearingLandlord
            this.feeBearingTenant = value.feeBearingTenant
            this.feeDistributionOrigin = value.feeDistributionOrigin
            this.feeDistributionTenant = value.feeDistributionTenant
            this.tenantRecruitmentNotes = value.tenantRecruitmentNotes
            this.areaCategory_1 = value.areaCategory_1
            this.areaBaseCode_1 = value.areaBaseCode_1
            this.areaDetailCode_1 = value.areaDetailCode_1
            this.areaCategory_2 = value.areaCategory_2
            this.areaBaseCode_2 = value.areaBaseCode_2
            this.areaDetailCode_2 = value.areaDetailCode_2
            this.areaCategory_3 = value.areaCategory_3
            this.areaBaseCode_3 = value.areaBaseCode_3
            this.areaDetailCode_3 = value.areaDetailCode_3
            this.areaCategory_4 = value.areaCategory_4
            this.areaBaseCode_4 = value.areaBaseCode_4
            this.areaDetailCode_4 = value.areaDetailCode_4
            this.areaCategory_5 = value.areaCategory_5
            this.areaBaseCode_5 = value.areaBaseCode_5
            this.areaDetailCode_5 = value.areaDetailCode_5
            this.areaCategory_6 = value.areaCategory_6
            this.areaBaseCode_6 = value.areaBaseCode_6
            this.areaDetailCode_6 = value.areaDetailCode_6
            this.areaCategory_7 = value.areaCategory_7
            this.areaBaseCode_7 = value.areaBaseCode_7
            this.areaDetailCode_7 = value.areaDetailCode_7
            this.buildingManagerCode = value.buildingManagerCode
            this.rentManagerCode = value.rentManagerCode
            this.contractManagerCode = value.contractManagerCode
            this.houseComPurchaseFlag = value.houseComPurchaseFlag
            this.prefectureCode2 = value.prefectureCode2
            this.cityCode2 = value.cityCode2
            this.townCode2 = value.townCode2
            this.addressDetail2 = value.addressDetail2
            this.ownerCode_1 = value.ownerCode_1
            this.ownerCode_2 = value.ownerCode_2
            this.ownerCode_3 = value.ownerCode_3
            this.ownershipDetail_1 = value.ownershipDetail_1
            this.ownershipDetail_2 = value.ownershipDetail_2
            this.ownershipDetail_3 = value.ownershipDetail_3
            this.ownershipDetail_4 = value.ownershipDetail_4
            this.ownershipDetail_5 = value.ownershipDetail_5
            this.nonOwnershipDetail_1 = value.nonOwnershipDetail_1
            this.nonOwnershipDetail_2 = value.nonOwnershipDetail_2
            this.nonOwnershipDetail_3 = value.nonOwnershipDetail_3
            this.nonOwnershipDetail_4 = value.nonOwnershipDetail_4
            this.nonOwnershipDetail_5 = value.nonOwnershipDetail_5
            this.managementStartDate = value.managementStartDate
            this.managementEndDate = value.managementEndDate
            this.agreedTerminationDate = value.agreedTerminationDate
            this.branchCode = value.branchCode
            this.managementStaffCode = value.managementStaffCode
            this.managementStartDate2 = value.managementStartDate2
            this.interfaceFlag = value.interfaceFlag
            this.tenantExtractionFlag = value.tenantExtractionFlag
            this.dataMigrationSourceKey_1 = value.dataMigrationSourceKey_1
            this.dataMigrationSourceKey_2 = value.dataMigrationSourceKey_2
            this.nearestBusStopDistance = value.nearestBusStopDistance
            this.catchcopyContent = value.catchcopyContent
            this.recruitmentInfoInputFlag = value.recruitmentInfoInputFlag
            this.listOutputFlag = value.listOutputFlag
            this.newBuildRentCategory = value.newBuildRentCategory
            this.companyCode = value.companyCode
            this.depositCategory = value.depositCategory
            this.guaranteeDeductCategory = value.guaranteeDeductCategory
            this.newHouseGuarantee_10yPkCount = value.newHouseGuarantee_10yPkCount
            this.newGuaranteeRate = value.newGuaranteeRate
            this.newGuaranteeManagementRate = value.newGuaranteeManagementRate
            this.contractMutualAidFeeRate = value.contractMutualAidFeeRate
            this.keyMoneyFlag = value.keyMoneyFlag
            this.managementFeeAccountingCategory = value.managementFeeAccountingCategory
            this.screeningBranchCode = value.screeningBranchCode
            this.residenceDisplayCategory = value.residenceDisplayCategory
            this.landlordSignatureConsentCategory = value.landlordSignatureConsentCategory
            this.loanCorporationRestoreCategory = value.loanCorporationRestoreCategory
            this.leaseContractNumber = value.leaseContractNumber
            this.guarantorNotRequiredCategory = value.guarantorNotRequiredCategory
            this.comPartnershipCategory = value.comPartnershipCategory
            this.contractorCompanyCategory = value.contractorCompanyCategory
            this.specialRent = value.specialRent
            this.realEstatePartnershipCategory = value.realEstatePartnershipCategory
            this.supportMechanismCategory = value.supportMechanismCategory
            this.telephoneConsentCategory = value.telephoneConsentCategory
            this.buildingNameFlag = value.buildingNameFlag
            this.fireResistantStructureCategory = value.fireResistantStructureCategory
            this.managementInheritanceCategory = value.managementInheritanceCategory
            this.unused_9 = value.unused_9
            this.managementStaffCodeSt = value.managementStaffCodeSt
            this.daikenBranchCode = value.daikenBranchCode
            this.houseNumber = value.houseNumber
            this.marketingBranchOfficeCd = value.marketingBranchOfficeCd
            resetChangedOnNotNull()
        }
    }
}
