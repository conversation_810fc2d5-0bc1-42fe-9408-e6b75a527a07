/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.DaitoBuildingManagementTableTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.DaitoBuildingManagementTablePojo

import org.jooq.impl.TableRecordImpl


/**
 * 大東建物管理対応表 既存システム物理名: FXX20P
 */
@Suppress("UNCHECKED_CAST")
open class DaitoBuildingManagementTableRecord private constructor() : TableRecordImpl<DaitoBuildingManagementTableRecord>(DaitoBuildingManagementTableTable.DAITO_BUILDING_MANAGEMENT_TABLE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var updateDate: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var constructionTerminalInstallation: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var daikenTerminalInstallation: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var usageStartDate: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var usageEndDate: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var installationCategory: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var satelliteCategory: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    /**
     * Create a detached, initialised DaitoBuildingManagementTableRecord
     */
    constructor(creationDate: Int? = null, updateDate: Int? = null, constructionTerminalInstallation: String? = null, daikenTerminalInstallation: String? = null, usageStartDate: Int? = null, usageEndDate: Int? = null, installationCategory: String? = null, satelliteCategory: String? = null): this() {
        this.creationDate = creationDate
        this.updateDate = updateDate
        this.constructionTerminalInstallation = constructionTerminalInstallation
        this.daikenTerminalInstallation = daikenTerminalInstallation
        this.usageStartDate = usageStartDate
        this.usageEndDate = usageEndDate
        this.installationCategory = installationCategory
        this.satelliteCategory = satelliteCategory
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised DaitoBuildingManagementTableRecord
     */
    constructor(value: DaitoBuildingManagementTablePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.updateDate = value.updateDate
            this.constructionTerminalInstallation = value.constructionTerminalInstallation
            this.daikenTerminalInstallation = value.daikenTerminalInstallation
            this.usageStartDate = value.usageStartDate
            this.usageEndDate = value.usageEndDate
            this.installationCategory = value.installationCategory
            this.satelliteCategory = value.satelliteCategory
            resetChangedOnNotNull()
        }
    }
}
