package jp.ne.simplex.application.controller.client.parking.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.ParkingVehicleInfo
import jp.ne.simplex.application.model.Room
import jp.ne.simplex.application.model.TenantContract
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException

data class ClientParkingVehicleInfoUpdateRequest(

    @JsonProperty("tenantContractNumber")
    @field:Schema(description = "テ契番号", example = "00000000")
    val tenantContractNumber: String,

    @JsonProperty("roomCd")
    @field:Schema(description = "部屋コード", example = "00000")
    val roomCd: String?,

    @JsonProperty("parkingLotCategory")
    @field:Schema(description = "駐車場区分", example = "SINGLE")
    val parkingLotCategory: ParkingLot.Category?,

    @JsonProperty("landTransportName")
    @field:Schema(description = "陸事名", example = "仙台")
    val landTransportName: String?,

    @JsonProperty("type")
    @field:Schema(description = "種別", example = "000")
    val type: String?,

    @JsonProperty("businessCategory")
    @field:Schema(description = "業態", example = "た")
    val businessCategory: String?,

    @JsonProperty("leftNumber")
    @field:Schema(description = "左ナンバー", example = "00")
    val leftNumber: String?,

    @JsonProperty("rightNumber")
    @field:Schema(description = "右ナンバー", example = "00")
    val rightNumber: String?,

    @JsonProperty("manufacturerDivision")
    @field:Schema(description = "メーカー区分", example = "TOYOTA")
    val manufacturerDivision: ParkingVehicleInfo.Category.ManufacturerDivision?,

    @JsonProperty("carModelName")
    @field:Schema(description = "車種名", example = "シンプレクス")
    val carModelName: String?,

    @JsonProperty("parkingCertIssueSign")
    @field:Schema(description = "車庫証明発給サイン", example = "0")
    val parkingCertIssueSign: ParkingVehicleInfo.ParkingCertIssueSign?,

    @JsonProperty("parkingCertComment")
    @field:Schema(description = "車庫証明コメント", example = "コメント")
    val parkingCertComment: String?,

    ) {
    // Service層の Interface に変換する
    fun toServiceInterface(): ParkingVehicleInfo {
        try {
            return ParkingVehicleInfo(
                tenantContractNumber = TenantContract.Number.of(tenantContractNumber),
                roomCd = roomCd?.let { Room.Code.of(it) },
                parkingLotCategory = parkingLotCategory,
                vehicleNumber = ParkingVehicleInfo.Number(
                    landTransportName,
                    type,
                    businessCategory,
                    leftNumber,
                    rightNumber
                ),
                vehicleCategory = ParkingVehicleInfo.Category(manufacturerDivision, carModelName),
                parkingCertIssueSign = parkingCertIssueSign,
                parkingCertComment = parkingCertComment,
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }
}
