/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.RoomInfoMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.RoomInfoMasterPojo

import org.jooq.Record4
import org.jooq.impl.UpdatableRecordImpl


/**
 * 部屋情報マスタ 既存システム物理名: EMUR1P
 */
@Suppress("UNCHECKED_CAST")
open class RoomInfoMasterRecord private constructor() : UpdatableRecordImpl<RoomInfoMasterRecord>(RoomInfoMasterTable.ROOM_INFO_MASTER) {

    open var recordType: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var propertyCdType: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var propertyCdPart1: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var propertyBuildingCd: String
        set(value): Unit = set(3, value)
        get(): String = get(3) as String

    open var propertyCdPart2: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var propertyRoomCd: String
        set(value): Unit = set(5, value)
        get(): String = get(5) as String

    open var deleteFlag: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var customerCompanyCd: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var customerBranchCd: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var customerDepartmentCd: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var customerCompletionFlag: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var prefectureCd: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var cityCd: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var lineCd: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var stationCd: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var rent: Int?
        set(value): Unit = set(15, value)
        get(): Int? = get(15) as Int?

    open var layoutRoomCount: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var exclusiveArea: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var propertyType: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var preference_1: Byte?
        set(value): Unit = set(19, value)
        get(): Byte? = get(19) as Byte?

    open var preference_2: Byte?
        set(value): Unit = set(20, value)
        get(): Byte? = get(20) as Byte?

    open var preference_3: Byte?
        set(value): Unit = set(21, value)
        get(): Byte? = get(21) as Byte?

    open var preference_4: Byte?
        set(value): Unit = set(22, value)
        get(): Byte? = get(22) as Byte?

    open var preference_5: Byte?
        set(value): Unit = set(23, value)
        get(): Byte? = get(23) as Byte?

    open var preference_6: Byte?
        set(value): Unit = set(24, value)
        get(): Byte? = get(24) as Byte?

    open var preference_7: Byte?
        set(value): Unit = set(25, value)
        get(): Byte? = get(25) as Byte?

    open var preference_8: Byte?
        set(value): Unit = set(26, value)
        get(): Byte? = get(26) as Byte?

    open var preference_9: Byte?
        set(value): Unit = set(27, value)
        get(): Byte? = get(27) as Byte?

    open var preference_10: Byte?
        set(value): Unit = set(28, value)
        get(): Byte? = get(28) as Byte?

    open var preference_11: Byte?
        set(value): Unit = set(29, value)
        get(): Byte? = get(29) as Byte?

    open var preference_12: Byte?
        set(value): Unit = set(30, value)
        get(): Byte? = get(30) as Byte?

    open var preference_13: Byte?
        set(value): Unit = set(31, value)
        get(): Byte? = get(31) as Byte?

    open var preference_14: Byte?
        set(value): Unit = set(32, value)
        get(): Byte? = get(32) as Byte?

    open var preference_15: Byte?
        set(value): Unit = set(33, value)
        get(): Byte? = get(33) as Byte?

    open var preference_16: Byte?
        set(value): Unit = set(34, value)
        get(): Byte? = get(34) as Byte?

    open var preference_17: Byte?
        set(value): Unit = set(35, value)
        get(): Byte? = get(35) as Byte?

    open var preference_18: Byte?
        set(value): Unit = set(36, value)
        get(): Byte? = get(36) as Byte?

    open var preference_19: Byte?
        set(value): Unit = set(37, value)
        get(): Byte? = get(37) as Byte?

    open var preference_20: Byte?
        set(value): Unit = set(38, value)
        get(): Byte? = get(38) as Byte?

    open var preference_21: Byte?
        set(value): Unit = set(39, value)
        get(): Byte? = get(39) as Byte?

    open var preference_22: Byte?
        set(value): Unit = set(40, value)
        get(): Byte? = get(40) as Byte?

    open var preference_23: Byte?
        set(value): Unit = set(41, value)
        get(): Byte? = get(41) as Byte?

    open var preference_24: Byte?
        set(value): Unit = set(42, value)
        get(): Byte? = get(42) as Byte?

    open var preference_25: Byte?
        set(value): Unit = set(43, value)
        get(): Byte? = get(43) as Byte?

    open var preference_26: Byte?
        set(value): Unit = set(44, value)
        get(): Byte? = get(44) as Byte?

    open var preference_27: Byte?
        set(value): Unit = set(45, value)
        get(): Byte? = get(45) as Byte?

    open var preference_28: Byte?
        set(value): Unit = set(46, value)
        get(): Byte? = get(46) as Byte?

    open var preference_29: Byte?
        set(value): Unit = set(47, value)
        get(): Byte? = get(47) as Byte?

    open var preference_30: Byte?
        set(value): Unit = set(48, value)
        get(): Byte? = get(48) as Byte?

    open var preference_31: Byte?
        set(value): Unit = set(49, value)
        get(): Byte? = get(49) as Byte?

    open var preference_32: Byte?
        set(value): Unit = set(50, value)
        get(): Byte? = get(50) as Byte?

    open var preference_33: Byte?
        set(value): Unit = set(51, value)
        get(): Byte? = get(51) as Byte?

    open var preference_34: Byte?
        set(value): Unit = set(52, value)
        get(): Byte? = get(52) as Byte?

    open var preference_35: Byte?
        set(value): Unit = set(53, value)
        get(): Byte? = get(53) as Byte?

    open var preference_36: Byte?
        set(value): Unit = set(54, value)
        get(): Byte? = get(54) as Byte?

    open var preference_37: Byte?
        set(value): Unit = set(55, value)
        get(): Byte? = get(55) as Byte?

    open var preference_38: Byte?
        set(value): Unit = set(56, value)
        get(): Byte? = get(56) as Byte?

    open var preference_39: Byte?
        set(value): Unit = set(57, value)
        get(): Byte? = get(57) as Byte?

    open var preference_40: Byte?
        set(value): Unit = set(58, value)
        get(): Byte? = get(58) as Byte?

    open var preference_41: Byte?
        set(value): Unit = set(59, value)
        get(): Byte? = get(59) as Byte?

    open var preference_42: Byte?
        set(value): Unit = set(60, value)
        get(): Byte? = get(60) as Byte?

    open var preference_43: Byte?
        set(value): Unit = set(61, value)
        get(): Byte? = get(61) as Byte?

    open var preference_44: Byte?
        set(value): Unit = set(62, value)
        get(): Byte? = get(62) as Byte?

    open var preference_45: Byte?
        set(value): Unit = set(63, value)
        get(): Byte? = get(63) as Byte?

    open var preference_46: Byte?
        set(value): Unit = set(64, value)
        get(): Byte? = get(64) as Byte?

    open var preference_47: Byte?
        set(value): Unit = set(65, value)
        get(): Byte? = get(65) as Byte?

    open var preference_48: Byte?
        set(value): Unit = set(66, value)
        get(): Byte? = get(66) as Byte?

    open var preference_49: Byte?
        set(value): Unit = set(67, value)
        get(): Byte? = get(67) as Byte?

    open var preference_50: Byte?
        set(value): Unit = set(68, value)
        get(): Byte? = get(68) as Byte?

    open var preference_51: Byte?
        set(value): Unit = set(69, value)
        get(): Byte? = get(69) as Byte?

    open var preference_52: Byte?
        set(value): Unit = set(70, value)
        get(): Byte? = get(70) as Byte?

    open var preference_53: Byte?
        set(value): Unit = set(71, value)
        get(): Byte? = get(71) as Byte?

    open var preference_54: Byte?
        set(value): Unit = set(72, value)
        get(): Byte? = get(72) as Byte?

    open var preference_55: Byte?
        set(value): Unit = set(73, value)
        get(): Byte? = get(73) as Byte?

    open var preference_56: Byte?
        set(value): Unit = set(74, value)
        get(): Byte? = get(74) as Byte?

    open var preference_57: Byte?
        set(value): Unit = set(75, value)
        get(): Byte? = get(75) as Byte?

    open var preference_58: Byte?
        set(value): Unit = set(76, value)
        get(): Byte? = get(76) as Byte?

    open var preference_59: Byte?
        set(value): Unit = set(77, value)
        get(): Byte? = get(77) as Byte?

    open var preference_60: Byte?
        set(value): Unit = set(78, value)
        get(): Byte? = get(78) as Byte?

    open var preference_99: Byte?
        set(value): Unit = set(79, value)
        get(): Byte? = get(79) as Byte?

    open var preferenceNewBuild: Byte?
        set(value): Unit = set(80, value)
        get(): Byte? = get(80) as Byte?

    open var preferenceCornerRoom: Byte?
        set(value): Unit = set(81, value)
        get(): Byte? = get(81) as Byte?

    open var preferenceAbove_2ndFloor: Byte?
        set(value): Unit = set(82, value)
        get(): Byte? = get(82) as Byte?

    open var lineName: String?
        set(value): Unit = set(83, value)
        get(): String? = get(83) as String?

    open var stationName: String?
        set(value): Unit = set(84, value)
        get(): String? = get(84) as String?

    open var busStopName: String?
        set(value): Unit = set(85, value)
        get(): String? = get(85) as String?

    open var busTime: Short?
        set(value): Unit = set(86, value)
        get(): Short? = get(86) as Short?

    open var walkingTime: Short?
        set(value): Unit = set(87, value)
        get(): Short? = get(87) as Short?

    open var distance: Short?
        set(value): Unit = set(88, value)
        get(): Short? = get(88) as Short?

    open var keyMoney: String?
        set(value): Unit = set(89, value)
        get(): String? = get(89) as String?

    open var deposit: String?
        set(value): Unit = set(90, value)
        get(): String? = get(90) as String?

    open var neighborhoodAssociationFee: String?
        set(value): Unit = set(91, value)
        get(): String? = get(91) as String?

    open var commonServiceFee: String?
        set(value): Unit = set(92, value)
        get(): String? = get(92) as String?

    open var roomTypeName: String?
        set(value): Unit = set(93, value)
        get(): String? = get(93) as String?

    open var layoutType: String?
        set(value): Unit = set(94, value)
        get(): String? = get(94) as String?

    open var layout: String?
        set(value): Unit = set(95, value)
        get(): String? = get(95) as String?

    open var layoutDetails: String?
        set(value): Unit = set(96, value)
        get(): String? = get(96) as String?

    open var parkingType: String?
        set(value): Unit = set(97, value)
        get(): String? = get(97) as String?

    open var parkingFee: String?
        set(value): Unit = set(98, value)
        get(): String? = get(98) as String?

    open var constructionYearMonth: String?
        set(value): Unit = set(99, value)
        get(): String? = get(99) as String?

    open var handlingStoreCompany: String?
        set(value): Unit = set(100, value)
        get(): String? = get(100) as String?

    open var locationListingArea: String?
        set(value): Unit = set(101, value)
        get(): String? = get(101) as String?

    open var floorNumber: String?
        set(value): Unit = set(102, value)
        get(): String? = get(102) as String?

    open var direction: String?
        set(value): Unit = set(103, value)
        get(): String? = get(103) as String?

    open var roomPosition: String?
        set(value): Unit = set(104, value)
        get(): String? = get(104) as String?

    open var availableMoveInYearMonth: String?
        set(value): Unit = set(105, value)
        get(): String? = get(105) as String?

    open var transportation: String?
        set(value): Unit = set(106, value)
        get(): String? = get(106) as String?

    open var equipment: String?
        set(value): Unit = set(107, value)
        get(): String? = get(107) as String?

    open var notes: String?
        set(value): Unit = set(108, value)
        get(): String? = get(108) as String?

    open var inquiryBranchName: String?
        set(value): Unit = set(109, value)
        get(): String? = get(109) as String?

    open var branchPhoneNumber: String?
        set(value): Unit = set(110, value)
        get(): String? = get(110) as String?

    open var branchFaxNumber: String?
        set(value): Unit = set(111, value)
        get(): String? = get(111) as String?

    open var transactionType: String?
        set(value): Unit = set(112, value)
        get(): String? = get(112) as String?

    open var buildingName: String?
        set(value): Unit = set(113, value)
        get(): String? = get(113) as String?

    open var structureName: String?
        set(value): Unit = set(114, value)
        get(): String? = get(114) as String?

    open var agentAssignableType: String?
        set(value): Unit = set(115, value)
        get(): String? = get(115) as String?

    open var subleaseType: String?
        set(value): Unit = set(116, value)
        get(): String? = get(116) as String?

    open var creationDate: Int?
        set(value): Unit = set(117, value)
        get(): Int? = get(117) as Int?

    open var creationTime: Int?
        set(value): Unit = set(118, value)
        get(): Int? = get(118) as Int?

    open var creator: String?
        set(value): Unit = set(119, value)
        get(): String? = get(119) as String?

    open var updateDate: Int?
        set(value): Unit = set(120, value)
        get(): Int? = get(120) as Int?

    open var updateTime: Int?
        set(value): Unit = set(121, value)
        get(): Int? = get(121) as Int?

    open var updater: String?
        set(value): Unit = set(122, value)
        get(): String? = get(122) as String?

    open var branchAddress: String?
        set(value): Unit = set(123, value)
        get(): String? = get(123) as String?

    open var recommendationComment: String?
        set(value): Unit = set(124, value)
        get(): String? = get(124) as String?

    open var completionYearMonth: Int?
        set(value): Unit = set(125, value)
        get(): Int? = get(125) as Int?

    open var propertyPostalCode: String?
        set(value): Unit = set(126, value)
        get(): String? = get(126) as String?

    open var vacateNoticeDate: Int?
        set(value): Unit = set(127, value)
        get(): Int? = get(127) as Int?

    open var expectedMoveOutDate: Int?
        set(value): Unit = set(128, value)
        get(): Int? = get(128) as Int?

    open var moveOutDate: Int?
        set(value): Unit = set(129, value)
        get(): Int? = get(129) as Int?

    open var expectedCompletionDate: Int?
        set(value): Unit = set(130, value)
        get(): Int? = get(130) as Int?

    open var availableMoveInDate: Int?
        set(value): Unit = set(131, value)
        get(): Int? = get(131) as Int?

    open var moveInApplicationDate: Int?
        set(value): Unit = set(132, value)
        get(): Int? = get(132) as Int?

    open var depositDate: Int?
        set(value): Unit = set(133, value)
        get(): Int? = get(133) as Int?

    open var balanceCollectionDate: Int?
        set(value): Unit = set(134, value)
        get(): Int? = get(134) as Int?

    open var moveInDate: Int?
        set(value): Unit = set(135, value)
        get(): Int? = get(135) as Int?

    open var completionDate: Int?
        set(value): Unit = set(136, value)
        get(): Int? = get(136) as Int?

    open var tenantRecruitmentCollectionDate: Int?
        set(value): Unit = set(137, value)
        get(): Int? = get(137) as Int?

    open var tenant: String?
        set(value): Unit = set(138, value)
        get(): String? = get(138) as String?

    open var owner: String?
        set(value): Unit = set(139, value)
        get(): String? = get(139) as String?

    open var customerAgentBranchCd: String?
        set(value): Unit = set(140, value)
        get(): String? = get(140) as String?

    open var customerAgentDepartmentCd: String?
        set(value): Unit = set(141, value)
        get(): String? = get(141) as String?

    open var customerAgentEmployeeCd: String?
        set(value): Unit = set(142, value)
        get(): String? = get(142) as String?

    open var rentTax: Int?
        set(value): Unit = set(143, value)
        get(): Int? = get(143) as Int?

    open var keyMoneyTax: Int?
        set(value): Unit = set(144, value)
        get(): Int? = get(144) as Int?

    open var keyMoneyTotal: Int?
        set(value): Unit = set(145, value)
        get(): Int? = get(145) as Int?

    open var commonServiceFeeTax: Int?
        set(value): Unit = set(146, value)
        get(): Int? = get(146) as Int?

    open var parkingFeeTax: Int?
        set(value): Unit = set(147, value)
        get(): Int? = get(147) as Int?

    open var roomNumber: String?
        set(value): Unit = set(148, value)
        get(): String? = get(148) as String?

    open var newExistingFlag: String?
        set(value): Unit = set(149, value)
        get(): String? = get(149) as String?

    open var roomStatusType: String?
        set(value): Unit = set(150, value)
        get(): String? = get(150) as String?

    open var recordStatusType: String?
        set(value): Unit = set(151, value)
        get(): String? = get(151) as String?

    open var ffUsagePeriod: String?
        set(value): Unit = set(152, value)
        get(): String? = get(152) as String?

    open var adPayableAmount: String?
        set(value): Unit = set(153, value)
        get(): String? = get(153) as String?

    open var locationCity: String?
        set(value): Unit = set(154, value)
        get(): String? = get(154) as String?

    open var tenantContractNumber: String?
        set(value): Unit = set(155, value)
        get(): String? = get(155) as String?

    open var ownerContact: String?
        set(value): Unit = set(156, value)
        get(): String? = get(156) as String?

    open var vacantPeriod: Short?
        set(value): Unit = set(157, value)
        get(): Short? = get(157) as Short?

    open var floorArea_1f: BigDecimal?
        set(value): Unit = set(158, value)
        get(): BigDecimal? = get(158) as BigDecimal?

    open var floorArea_2f: BigDecimal?
        set(value): Unit = set(159, value)
        get(): BigDecimal? = get(159) as BigDecimal?

    open var floorArea_3f: BigDecimal?
        set(value): Unit = set(160, value)
        get(): BigDecimal? = get(160) as BigDecimal?

    open var recruitmentCreationDate: Int?
        set(value): Unit = set(161, value)
        get(): Int? = get(161) as Int?

    open var recruitmentApprovalDate: Int?
        set(value): Unit = set(162, value)
        get(): Int? = get(162) as Int?

    open var moveOutInspectionDate: Int?
        set(value): Unit = set(163, value)
        get(): Int? = get(163) as Int?

    open var restorationDate: Int?
        set(value): Unit = set(164, value)
        get(): Int? = get(164) as Int?

    open var restorationCompletionDate: Int?
        set(value): Unit = set(165, value)
        get(): Int? = get(165) as Int?

    open var vacantBookingDate: Int?
        set(value): Unit = set(166, value)
        get(): Int? = get(166) as Int?

    open var vacantBookingCompletionDate: Int?
        set(value): Unit = set(167, value)
        get(): Int? = get(167) as Int?

    open var additionalKeyMoney: Int?
        set(value): Unit = set(168, value)
        get(): Int? = get(168) as Int?

    open var mutualAidJoinSign: Byte?
        set(value): Unit = set(169, value)
        get(): Byte? = get(169) as Byte?

    open var rentalType: Byte?
        set(value): Unit = set(170, value)
        get(): Byte? = get(170) as Byte?

    open var specialRentalType: String?
        set(value): Unit = set(171, value)
        get(): String? = get(171) as String?

    open var distance2: BigDecimal?
        set(value): Unit = set(172, value)
        get(): BigDecimal? = get(172) as BigDecimal?

    open var approvalType: Byte?
        set(value): Unit = set(173, value)
        get(): Byte? = get(173) as Byte?

    open var recordSeparator: String?
        set(value): Unit = set(174, value)
        get(): String? = get(174) as String?

    open var changeType: String?
        set(value): Unit = set(175, value)
        get(): String? = get(175) as String?

    open var noDepositFlag: Byte?
        set(value): Unit = set(176, value)
        get(): Byte? = get(176) as Byte?

    open var campaignTargetFlag: Byte?
        set(value): Unit = set(177, value)
        get(): Byte? = get(177) as Byte?

    open var preference_61: Byte?
        set(value): Unit = set(178, value)
        get(): Byte? = get(178) as Byte?

    open var preference_62: Byte?
        set(value): Unit = set(179, value)
        get(): Byte? = get(179) as Byte?

    open var preference_63: Byte?
        set(value): Unit = set(180, value)
        get(): Byte? = get(180) as Byte?

    open var preference_64: Byte?
        set(value): Unit = set(181, value)
        get(): Byte? = get(181) as Byte?

    open var preference_65: Byte?
        set(value): Unit = set(182, value)
        get(): Byte? = get(182) as Byte?

    open var preference_66: Byte?
        set(value): Unit = set(183, value)
        get(): Byte? = get(183) as Byte?

    open var preference_67: Byte?
        set(value): Unit = set(184, value)
        get(): Byte? = get(184) as Byte?

    open var preference_68: Byte?
        set(value): Unit = set(185, value)
        get(): Byte? = get(185) as Byte?

    open var preference_69: Byte?
        set(value): Unit = set(186, value)
        get(): Byte? = get(186) as Byte?

    open var preference_70: Byte?
        set(value): Unit = set(187, value)
        get(): Byte? = get(187) as Byte?

    open var preference_71: Byte?
        set(value): Unit = set(188, value)
        get(): Byte? = get(188) as Byte?

    open var preference_72: Byte?
        set(value): Unit = set(189, value)
        get(): Byte? = get(189) as Byte?

    open var preference_73: Byte?
        set(value): Unit = set(190, value)
        get(): Byte? = get(190) as Byte?

    open var preference_74: Byte?
        set(value): Unit = set(191, value)
        get(): Byte? = get(191) as Byte?

    open var preference_75: Byte?
        set(value): Unit = set(192, value)
        get(): Byte? = get(192) as Byte?

    open var preference_76: Byte?
        set(value): Unit = set(193, value)
        get(): Byte? = get(193) as Byte?

    open var preference_77: Byte?
        set(value): Unit = set(194, value)
        get(): Byte? = get(194) as Byte?

    open var preference_78: Byte?
        set(value): Unit = set(195, value)
        get(): Byte? = get(195) as Byte?

    open var preference_79: Byte?
        set(value): Unit = set(196, value)
        get(): Byte? = get(196) as Byte?

    open var preference_80: Byte?
        set(value): Unit = set(197, value)
        get(): Byte? = get(197) as Byte?

    open var preference_81: Byte?
        set(value): Unit = set(198, value)
        get(): Byte? = get(198) as Byte?

    open var preference_82: Byte?
        set(value): Unit = set(199, value)
        get(): Byte? = get(199) as Byte?

    open var preference_83: Byte?
        set(value): Unit = set(200, value)
        get(): Byte? = get(200) as Byte?

    open var preference_84: Byte?
        set(value): Unit = set(201, value)
        get(): Byte? = get(201) as Byte?

    open var preference_85: Byte?
        set(value): Unit = set(202, value)
        get(): Byte? = get(202) as Byte?

    open var preference_86: Byte?
        set(value): Unit = set(203, value)
        get(): Byte? = get(203) as Byte?

    open var preference_87: Byte?
        set(value): Unit = set(204, value)
        get(): Byte? = get(204) as Byte?

    open var preference_88: Byte?
        set(value): Unit = set(205, value)
        get(): Byte? = get(205) as Byte?

    open var preference_89: Byte?
        set(value): Unit = set(206, value)
        get(): Byte? = get(206) as Byte?

    open var preference_90: Byte?
        set(value): Unit = set(207, value)
        get(): Byte? = get(207) as Byte?

    open var preference_91: Byte?
        set(value): Unit = set(208, value)
        get(): Byte? = get(208) as Byte?

    open var preference_92: Byte?
        set(value): Unit = set(209, value)
        get(): Byte? = get(209) as Byte?

    open var preference_93: Byte?
        set(value): Unit = set(210, value)
        get(): Byte? = get(210) as Byte?

    open var preference_94: Byte?
        set(value): Unit = set(211, value)
        get(): Byte? = get(211) as Byte?

    open var preference_95: Byte?
        set(value): Unit = set(212, value)
        get(): Byte? = get(212) as Byte?

    open var preference_96: Byte?
        set(value): Unit = set(213, value)
        get(): Byte? = get(213) as Byte?

    open var preference_97: Byte?
        set(value): Unit = set(214, value)
        get(): Byte? = get(214) as Byte?

    open var preference_98: Byte?
        set(value): Unit = set(215, value)
        get(): Byte? = get(215) as Byte?

    open var propertyAddress: String?
        set(value): Unit = set(216, value)
        get(): String? = get(216) as String?

    open var propertyAddressDetail: String?
        set(value): Unit = set(217, value)
        get(): String? = get(217) as String?

    open var serviceRoomSign: Byte?
        set(value): Unit = set(218, value)
        get(): Byte? = get(218) as Byte?

    open var highVoltageBulkReceipt: Byte?
        set(value): Unit = set(219, value)
        get(): Byte? = get(219) as Byte?

    open var highRentalSign: Byte?
        set(value): Unit = set(220, value)
        get(): Byte? = get(220) as Byte?

    open var solarDiscountTarget: Byte?
        set(value): Unit = set(221, value)
        get(): Byte? = get(221) as Byte?

    open var cleaningCostFixed: Byte?
        set(value): Unit = set(222, value)
        get(): Byte? = get(222) as Byte?

    open var previousRent: Int?
        set(value): Unit = set(223, value)
        get(): Int? = get(223) as Int?

    open var existingReviewUpdateDate: Int?
        set(value): Unit = set(224, value)
        get(): Int? = get(224) as Int?

    open var moveOutInspectionTime: Int?
        set(value): Unit = set(225, value)
        get(): Int? = get(225) as Int?

    open var recruitmentStartDate: Int?
        set(value): Unit = set(226, value)
        get(): Int? = get(226) as Int?

    open var cleaningCostTotal: Int?
        set(value): Unit = set(227, value)
        get(): Int? = get(227) as Int?

    open var discountInitialValueSign: Byte?
        set(value): Unit = set(228, value)
        get(): Byte? = get(228) as Byte?

    open var flagReserve_7: Byte?
        set(value): Unit = set(229, value)
        get(): Byte? = get(229) as Byte?

    open var petFlag: Byte?
        set(value): Unit = set(230, value)
        get(): Byte? = get(230) as Byte?

    open var flagReserve_9: Byte?
        set(value): Unit = set(231, value)
        get(): Byte? = get(231) as Byte?

    open var flagReserve_10: Byte?
        set(value): Unit = set(232, value)
        get(): Byte? = get(232) as Byte?

    open var challengeStartDate: Int?
        set(value): Unit = set(233, value)
        get(): Int? = get(233) as Int?

    open var challengeEndDate: Int?
        set(value): Unit = set(234, value)
        get(): Int? = get(234) as Int?

    open var applicationEndDate: Int?
        set(value): Unit = set(235, value)
        get(): Int? = get(235) as Int?

    open var moveInEndDate: Int?
        set(value): Unit = set(236, value)
        get(): Int? = get(236) as Int?

    open var additionalReleaseDate: Int?
        set(value): Unit = set(237, value)
        get(): Int? = get(237) as Int?

    open var recruitmentRent: Int?
        set(value): Unit = set(238, value)
        get(): Int? = get(238) as Int?

    open var challengeAdditionalAmount: Int?
        set(value): Unit = set(239, value)
        get(): Int? = get(239) as Int?

    open var reviewRent: Int?
        set(value): Unit = set(240, value)
        get(): Int? = get(240) as Int?

    open var dateReserve_11: Int?
        set(value): Unit = set(241, value)
        get(): Int? = get(241) as Int?

    open var dateReserve_12: Int?
        set(value): Unit = set(242, value)
        get(): Int? = get(242) as Int?

    open var dateReserve_13: Int?
        set(value): Unit = set(243, value)
        get(): Int? = get(243) as Int?

    open var amountReserve_1: Int?
        set(value): Unit = set(244, value)
        get(): Int? = get(244) as Int?

    open var amountReserve_2: Int?
        set(value): Unit = set(245, value)
        get(): Int? = get(245) as Int?

    open var amountReserve_3: Int?
        set(value): Unit = set(246, value)
        get(): Int? = get(246) as Int?

    open var commonServiceFeeBase: Int?
        set(value): Unit = set(247, value)
        get(): Int? = get(247) as Int?

    open var generalCableTvBase: Int?
        set(value): Unit = set(248, value)
        get(): Int? = get(248) as Int?

    open var generalCableTvTax: Int?
        set(value): Unit = set(249, value)
        get(): Int? = get(249) as Int?

    open var generalInternetBase: Int?
        set(value): Unit = set(250, value)
        get(): Int? = get(250) as Int?

    open var generalInternetTax: Int?
        set(value): Unit = set(251, value)
        get(): Int? = get(251) as Int?

    open var generalWaterQualityBase: Int?
        set(value): Unit = set(252, value)
        get(): Int? = get(252) as Int?

    open var generalWaterQualityTax: Int?
        set(value): Unit = set(253, value)
        get(): Int? = get(253) as Int?

    open var generalTenantWaterBase: Int?
        set(value): Unit = set(254, value)
        get(): Int? = get(254) as Int?

    open var generalTenantWaterTax: Int?
        set(value): Unit = set(255, value)
        get(): Int? = get(255) as Int?

    open var generalDrainUseBase: Int?
        set(value): Unit = set(256, value)
        get(): Int? = get(256) as Int?

    open var generalDrainUseTax: Int?
        set(value): Unit = set(257, value)
        get(): Int? = get(257) as Int?

    open var generalGarbageCollectionBase: Int?
        set(value): Unit = set(258, value)
        get(): Int? = get(258) as Int?

    open var generalGarbageCollectionTax: Int?
        set(value): Unit = set(259, value)
        get(): Int? = get(259) as Int?

    open var generalSharedAntennaBase: Int?
        set(value): Unit = set(260, value)
        get(): Int? = get(260) as Int?

    open var generalSharedAntennaTax: Int?
        set(value): Unit = set(261, value)
        get(): Int? = get(261) as Int?

    open var generalOwnerCleaningBase: Int?
        set(value): Unit = set(262, value)
        get(): Int? = get(262) as Int?

    open var generalOwnerCleaningTax: Int?
        set(value): Unit = set(263, value)
        get(): Int? = get(263) as Int?

    open var generalBuildingMaintenanceBase: Int?
        set(value): Unit = set(264, value)
        get(): Int? = get(264) as Int?

    open var generalBuildingMaintenanceTax: Int?
        set(value): Unit = set(265, value)
        get(): Int? = get(265) as Int?

    open var generalBuildingManagementBase: Int?
        set(value): Unit = set(266, value)
        get(): Int? = get(266) as Int?

    open var generalBuildingManagementTax: Int?
        set(value): Unit = set(267, value)
        get(): Int? = get(267) as Int?

    open var generalNeighborhoodAssocBase: Int?
        set(value): Unit = set(268, value)
        get(): Int? = get(268) as Int?

    open var generalNeighborhoodAssocTax: Int?
        set(value): Unit = set(269, value)
        get(): Int? = get(269) as Int?

    open var generalNeighborhoodOtherBase: Int?
        set(value): Unit = set(270, value)
        get(): Int? = get(270) as Int?

    open var generalNeighborhoodOtherTax: Int?
        set(value): Unit = set(271, value)
        get(): Int? = get(271) as Int?

    open var generalRepaymentAgentBase: Int?
        set(value): Unit = set(272, value)
        get(): Int? = get(272) as Int?

    open var generalRepaymentAgentTax: Int?
        set(value): Unit = set(273, value)
        get(): Int? = get(273) as Int?

    open var generalHlCommissionBase: Int?
        set(value): Unit = set(274, value)
        get(): Int? = get(274) as Int?

    open var generalHlCommissionTax: Int?
        set(value): Unit = set(275, value)
        get(): Int? = get(275) as Int?

    open var generalFurnishedBase: Int?
        set(value): Unit = set(276, value)
        get(): Int? = get(276) as Int?

    open var generalFurnishedTax: Int?
        set(value): Unit = set(277, value)
        get(): Int? = get(277) as Int?

    open var generalTenantDepositBase: Int?
        set(value): Unit = set(278, value)
        get(): Int? = get(278) as Int?

    open var generalTenantDepositTax: Int?
        set(value): Unit = set(279, value)
        get(): Int? = get(279) as Int?

    open var generalRentalBase: Int?
        set(value): Unit = set(280, value)
        get(): Int? = get(280) as Int?

    open var generalRentalTax: Int?
        set(value): Unit = set(281, value)
        get(): Int? = get(281) as Int?

    open var reserveAmount_1Base: Int?
        set(value): Unit = set(282, value)
        get(): Int? = get(282) as Int?

    open var reserveAmount_1Tax: Int?
        set(value): Unit = set(283, value)
        get(): Int? = get(283) as Int?

    open var reserveAmount_2Base: Int?
        set(value): Unit = set(284, value)
        get(): Int? = get(284) as Int?

    open var reserveAmount_2Tax: Int?
        set(value): Unit = set(285, value)
        get(): Int? = get(285) as Int?

    open var reserveAmount_3Base: Int?
        set(value): Unit = set(286, value)
        get(): Int? = get(286) as Int?

    open var reserveAmount_3Tax: Int?
        set(value): Unit = set(287, value)
        get(): Int? = get(287) as Int?

    open var flagReserve_11: Byte?
        set(value): Unit = set(288, value)
        get(): Byte? = get(288) as Byte?

    open var flagReserve_12: Byte?
        set(value): Unit = set(289, value)
        get(): Byte? = get(289) as Byte?

    open var bundleWater: Byte?
        set(value): Unit = set(290, value)
        get(): Byte? = get(290) as Byte?

    open var bundleElectricity: Byte?
        set(value): Unit = set(291, value)
        get(): Byte? = get(291) as Byte?

    open var bundleGas: Byte?
        set(value): Unit = set(292, value)
        get(): Byte? = get(292) as Byte?

    open var category_2digitReserve_1: String?
        set(value): Unit = set(293, value)
        get(): String? = get(293) as String?

    open var category_2digitReserve_2: String?
        set(value): Unit = set(294, value)
        get(): String? = get(294) as String?

    open var category_2digitReserve_3: String?
        set(value): Unit = set(295, value)
        get(): String? = get(295) as String?

    open var category_2digitReserve_4: String?
        set(value): Unit = set(296, value)
        get(): String? = get(296) as String?

    open var category_2digitReserve_5: String?
        set(value): Unit = set(297, value)
        get(): String? = get(297) as String?

    open var amountReserve_4: Int?
        set(value): Unit = set(298, value)
        get(): Int? = get(298) as Int?

    open var amountReserve_5: Int?
        set(value): Unit = set(299, value)
        get(): Int? = get(299) as Int?

    open var amountReserve_6: Int?
        set(value): Unit = set(300, value)
        get(): Int? = get(300) as Int?

    open var amountReserve_7: Int?
        set(value): Unit = set(301, value)
        get(): Int? = get(301) as Int?

    open var amountReserve_8: Int?
        set(value): Unit = set(302, value)
        get(): Int? = get(302) as Int?

    open var dateReserve_14: Int?
        set(value): Unit = set(303, value)
        get(): Int? = get(303) as Int?

    open var dateReserve_15: Int?
        set(value): Unit = set(304, value)
        get(): Int? = get(304) as Int?

    open var dateReserve_16: Int?
        set(value): Unit = set(305, value)
        get(): Int? = get(305) as Int?

    open var dateReserve_17: Int?
        set(value): Unit = set(306, value)
        get(): Int? = get(306) as Int?

    open var dateReserve_18: Int?
        set(value): Unit = set(307, value)
        get(): Int? = get(307) as Int?

    open var category_1digitReserve_1: String?
        set(value): Unit = set(308, value)
        get(): String? = get(308) as String?

    open var category_1digitReserve_2: String?
        set(value): Unit = set(309, value)
        get(): String? = get(309) as String?

    open var category_1digitReserve_3: String?
        set(value): Unit = set(310, value)
        get(): String? = get(310) as String?

    open var category_1digitReserve_4: String?
        set(value): Unit = set(311, value)
        get(): String? = get(311) as String?

    open var category_1digitReserve_5: String?
        set(value): Unit = set(312, value)
        get(): String? = get(312) as String?

    open var leasingStoreCd: String?
        set(value): Unit = set(313, value)
        get(): String? = get(313) as String?

    open var managementBranchCd: String?
        set(value): Unit = set(314, value)
        get(): String? = get(314) as String?

    open var salesOfficeCd: String?
        set(value): Unit = set(315, value)
        get(): String? = get(315) as String?

    open var screeningBranchCd: String?
        set(value): Unit = set(316, value)
        get(): String? = get(316) as String?

    open var preference_100: Byte?
        set(value): Unit = set(317, value)
        get(): Byte? = get(317) as Byte?

    open var preference_101: Byte?
        set(value): Unit = set(318, value)
        get(): Byte? = get(318) as Byte?

    open var preference_102: Byte?
        set(value): Unit = set(319, value)
        get(): Byte? = get(319) as Byte?

    open var preference_103: Byte?
        set(value): Unit = set(320, value)
        get(): Byte? = get(320) as Byte?

    open var preference_104: Byte?
        set(value): Unit = set(321, value)
        get(): Byte? = get(321) as Byte?

    open var preference_105: Byte?
        set(value): Unit = set(322, value)
        get(): Byte? = get(322) as Byte?

    open var preference_106: Byte?
        set(value): Unit = set(323, value)
        get(): Byte? = get(323) as Byte?

    open var preference_107: Byte?
        set(value): Unit = set(324, value)
        get(): Byte? = get(324) as Byte?

    open var preference_108: Byte?
        set(value): Unit = set(325, value)
        get(): Byte? = get(325) as Byte?

    open var preference_109: Byte?
        set(value): Unit = set(326, value)
        get(): Byte? = get(326) as Byte?

    open var preference_110: Byte?
        set(value): Unit = set(327, value)
        get(): Byte? = get(327) as Byte?

    open var preference_111: Byte?
        set(value): Unit = set(328, value)
        get(): Byte? = get(328) as Byte?

    open var preference_112: Byte?
        set(value): Unit = set(329, value)
        get(): Byte? = get(329) as Byte?

    open var preference_113: Byte?
        set(value): Unit = set(330, value)
        get(): Byte? = get(330) as Byte?

    open var preference_114: Byte?
        set(value): Unit = set(331, value)
        get(): Byte? = get(331) as Byte?

    open var preference_115: Byte?
        set(value): Unit = set(332, value)
        get(): Byte? = get(332) as Byte?

    open var preference_116: Byte?
        set(value): Unit = set(333, value)
        get(): Byte? = get(333) as Byte?

    open var preference_117: Byte?
        set(value): Unit = set(334, value)
        get(): Byte? = get(334) as Byte?

    open var preference_118: Byte?
        set(value): Unit = set(335, value)
        get(): Byte? = get(335) as Byte?

    open var preference_119: Byte?
        set(value): Unit = set(336, value)
        get(): Byte? = get(336) as Byte?

    open var preference_120: Byte?
        set(value): Unit = set(337, value)
        get(): Byte? = get(337) as Byte?

    open var preference_121: Byte?
        set(value): Unit = set(338, value)
        get(): Byte? = get(338) as Byte?

    open var preference_122: Byte?
        set(value): Unit = set(339, value)
        get(): Byte? = get(339) as Byte?

    open var preference_123: Byte?
        set(value): Unit = set(340, value)
        get(): Byte? = get(340) as Byte?

    open var preference_124: Byte?
        set(value): Unit = set(341, value)
        get(): Byte? = get(341) as Byte?

    open var preference_125: Byte?
        set(value): Unit = set(342, value)
        get(): Byte? = get(342) as Byte?

    open var preference_126: Byte?
        set(value): Unit = set(343, value)
        get(): Byte? = get(343) as Byte?

    open var preference_127: Byte?
        set(value): Unit = set(344, value)
        get(): Byte? = get(344) as Byte?

    open var preference_128: Byte?
        set(value): Unit = set(345, value)
        get(): Byte? = get(345) as Byte?

    open var preference_129: Byte?
        set(value): Unit = set(346, value)
        get(): Byte? = get(346) as Byte?

    open var preference_130: Byte?
        set(value): Unit = set(347, value)
        get(): Byte? = get(347) as Byte?

    open var preference_131: Byte?
        set(value): Unit = set(348, value)
        get(): Byte? = get(348) as Byte?

    open var preference_132: Byte?
        set(value): Unit = set(349, value)
        get(): Byte? = get(349) as Byte?

    open var preference_133: Byte?
        set(value): Unit = set(350, value)
        get(): Byte? = get(350) as Byte?

    open var preference_134: Byte?
        set(value): Unit = set(351, value)
        get(): Byte? = get(351) as Byte?

    open var preference_135: Byte?
        set(value): Unit = set(352, value)
        get(): Byte? = get(352) as Byte?

    open var preference_136: Byte?
        set(value): Unit = set(353, value)
        get(): Byte? = get(353) as Byte?

    open var preference_137: Byte?
        set(value): Unit = set(354, value)
        get(): Byte? = get(354) as Byte?

    open var preference_138: Byte?
        set(value): Unit = set(355, value)
        get(): Byte? = get(355) as Byte?

    open var preference_139: Byte?
        set(value): Unit = set(356, value)
        get(): Byte? = get(356) as Byte?

    open var preference_140: Byte?
        set(value): Unit = set(357, value)
        get(): Byte? = get(357) as Byte?

    open var preference_141: Byte?
        set(value): Unit = set(358, value)
        get(): Byte? = get(358) as Byte?

    open var preference_142: Byte?
        set(value): Unit = set(359, value)
        get(): Byte? = get(359) as Byte?

    open var preference_143: Byte?
        set(value): Unit = set(360, value)
        get(): Byte? = get(360) as Byte?

    open var preference_144: Byte?
        set(value): Unit = set(361, value)
        get(): Byte? = get(361) as Byte?

    open var preference_145: Byte?
        set(value): Unit = set(362, value)
        get(): Byte? = get(362) as Byte?

    open var preference_146: Byte?
        set(value): Unit = set(363, value)
        get(): Byte? = get(363) as Byte?

    open var preference_147: Byte?
        set(value): Unit = set(364, value)
        get(): Byte? = get(364) as Byte?

    open var preference_148: Byte?
        set(value): Unit = set(365, value)
        get(): Byte? = get(365) as Byte?

    open var preference_149: Byte?
        set(value): Unit = set(366, value)
        get(): Byte? = get(366) as Byte?

    open var preference_150: Byte?
        set(value): Unit = set(367, value)
        get(): Byte? = get(367) as Byte?

    open var preference_151: Byte?
        set(value): Unit = set(368, value)
        get(): Byte? = get(368) as Byte?

    open var preference_152: Byte?
        set(value): Unit = set(369, value)
        get(): Byte? = get(369) as Byte?

    open var preference_153: Byte?
        set(value): Unit = set(370, value)
        get(): Byte? = get(370) as Byte?

    open var preference_154: Byte?
        set(value): Unit = set(371, value)
        get(): Byte? = get(371) as Byte?

    open var preference_155: Byte?
        set(value): Unit = set(372, value)
        get(): Byte? = get(372) as Byte?

    open var preference_156: Byte?
        set(value): Unit = set(373, value)
        get(): Byte? = get(373) as Byte?

    open var preference_157: Byte?
        set(value): Unit = set(374, value)
        get(): Byte? = get(374) as Byte?

    open var preference_158: Byte?
        set(value): Unit = set(375, value)
        get(): Byte? = get(375) as Byte?

    open var preference_159: Byte?
        set(value): Unit = set(376, value)
        get(): Byte? = get(376) as Byte?

    open var preference_160: Byte?
        set(value): Unit = set(377, value)
        get(): Byte? = get(377) as Byte?

    open var preference_161: Byte?
        set(value): Unit = set(378, value)
        get(): Byte? = get(378) as Byte?

    open var preference_162: Byte?
        set(value): Unit = set(379, value)
        get(): Byte? = get(379) as Byte?

    open var preference_163: Byte?
        set(value): Unit = set(380, value)
        get(): Byte? = get(380) as Byte?

    open var preference_164: Byte?
        set(value): Unit = set(381, value)
        get(): Byte? = get(381) as Byte?

    open var preference_165: Byte?
        set(value): Unit = set(382, value)
        get(): Byte? = get(382) as Byte?

    open var preference_166: Byte?
        set(value): Unit = set(383, value)
        get(): Byte? = get(383) as Byte?

    open var preference_167: Byte?
        set(value): Unit = set(384, value)
        get(): Byte? = get(384) as Byte?

    open var preference_168: Byte?
        set(value): Unit = set(385, value)
        get(): Byte? = get(385) as Byte?

    open var preference_169: Byte?
        set(value): Unit = set(386, value)
        get(): Byte? = get(386) as Byte?

    open var preference_170: Byte?
        set(value): Unit = set(387, value)
        get(): Byte? = get(387) as Byte?

    open var preference_171: Byte?
        set(value): Unit = set(388, value)
        get(): Byte? = get(388) as Byte?

    open var preference_172: Byte?
        set(value): Unit = set(389, value)
        get(): Byte? = get(389) as Byte?

    open var preference_173: Byte?
        set(value): Unit = set(390, value)
        get(): Byte? = get(390) as Byte?

    open var preference_174: Byte?
        set(value): Unit = set(391, value)
        get(): Byte? = get(391) as Byte?

    open var preference_175: Byte?
        set(value): Unit = set(392, value)
        get(): Byte? = get(392) as Byte?

    open var preference_176: Byte?
        set(value): Unit = set(393, value)
        get(): Byte? = get(393) as Byte?

    open var preference_177: Byte?
        set(value): Unit = set(394, value)
        get(): Byte? = get(394) as Byte?

    open var preference_178: Byte?
        set(value): Unit = set(395, value)
        get(): Byte? = get(395) as Byte?

    open var preference_179: Byte?
        set(value): Unit = set(396, value)
        get(): Byte? = get(396) as Byte?

    open var preference_180: Byte?
        set(value): Unit = set(397, value)
        get(): Byte? = get(397) as Byte?

    open var preference_181: Byte?
        set(value): Unit = set(398, value)
        get(): Byte? = get(398) as Byte?

    open var preference_182: Byte?
        set(value): Unit = set(399, value)
        get(): Byte? = get(399) as Byte?

    open var preference_183: Byte?
        set(value): Unit = set(400, value)
        get(): Byte? = get(400) as Byte?

    open var preference_184: Byte?
        set(value): Unit = set(401, value)
        get(): Byte? = get(401) as Byte?

    open var preference_185: Byte?
        set(value): Unit = set(402, value)
        get(): Byte? = get(402) as Byte?

    open var preference_186: Byte?
        set(value): Unit = set(403, value)
        get(): Byte? = get(403) as Byte?

    open var preference_187: Byte?
        set(value): Unit = set(404, value)
        get(): Byte? = get(404) as Byte?

    open var preference_188: Byte?
        set(value): Unit = set(405, value)
        get(): Byte? = get(405) as Byte?

    open var preference_189: Byte?
        set(value): Unit = set(406, value)
        get(): Byte? = get(406) as Byte?

    open var preference_190: Byte?
        set(value): Unit = set(407, value)
        get(): Byte? = get(407) as Byte?

    open var preference_191: Byte?
        set(value): Unit = set(408, value)
        get(): Byte? = get(408) as Byte?

    open var preference_192: Byte?
        set(value): Unit = set(409, value)
        get(): Byte? = get(409) as Byte?

    open var preference_193: Byte?
        set(value): Unit = set(410, value)
        get(): Byte? = get(410) as Byte?

    open var preference_194: Byte?
        set(value): Unit = set(411, value)
        get(): Byte? = get(411) as Byte?

    open var preference_195: Byte?
        set(value): Unit = set(412, value)
        get(): Byte? = get(412) as Byte?

    open var preference_196: Byte?
        set(value): Unit = set(413, value)
        get(): Byte? = get(413) as Byte?

    open var preference_197: Byte?
        set(value): Unit = set(414, value)
        get(): Byte? = get(414) as Byte?

    open var preference_198: Byte?
        set(value): Unit = set(415, value)
        get(): Byte? = get(415) as Byte?

    open var preference_199: Byte?
        set(value): Unit = set(416, value)
        get(): Byte? = get(416) as Byte?

    open var marketingBranchOfficeCd: String?
        set(value): Unit = set(417, value)
        get(): String? = get(417) as String?

    open var propertySituation: String?
        set(value): Unit = set(418, value)
        get(): String? = get(418) as String?

    open var prefectureCityCd: String?
        set(value): Unit = set(419, value)
        get(): String? = get(419) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record4<String?, String?, String?, String?> = super.key() as Record4<String?, String?, String?, String?>

    /**
     * Create a detached, initialised RoomInfoMasterRecord
     */
    constructor(value: RoomInfoMasterPojo?): this() {
        if (value != null) {
            this.recordType = value.recordType
            this.propertyCdType = value.propertyCdType
            this.propertyCdPart1 = value.propertyCdPart1
            this.propertyBuildingCd = value.propertyBuildingCd
            this.propertyCdPart2 = value.propertyCdPart2
            this.propertyRoomCd = value.propertyRoomCd
            this.deleteFlag = value.deleteFlag
            this.customerCompanyCd = value.customerCompanyCd
            this.customerBranchCd = value.customerBranchCd
            this.customerDepartmentCd = value.customerDepartmentCd
            this.customerCompletionFlag = value.customerCompletionFlag
            this.prefectureCd = value.prefectureCd
            this.cityCd = value.cityCd
            this.lineCd = value.lineCd
            this.stationCd = value.stationCd
            this.rent = value.rent
            this.layoutRoomCount = value.layoutRoomCount
            this.exclusiveArea = value.exclusiveArea
            this.propertyType = value.propertyType
            this.preference_1 = value.preference_1
            this.preference_2 = value.preference_2
            this.preference_3 = value.preference_3
            this.preference_4 = value.preference_4
            this.preference_5 = value.preference_5
            this.preference_6 = value.preference_6
            this.preference_7 = value.preference_7
            this.preference_8 = value.preference_8
            this.preference_9 = value.preference_9
            this.preference_10 = value.preference_10
            this.preference_11 = value.preference_11
            this.preference_12 = value.preference_12
            this.preference_13 = value.preference_13
            this.preference_14 = value.preference_14
            this.preference_15 = value.preference_15
            this.preference_16 = value.preference_16
            this.preference_17 = value.preference_17
            this.preference_18 = value.preference_18
            this.preference_19 = value.preference_19
            this.preference_20 = value.preference_20
            this.preference_21 = value.preference_21
            this.preference_22 = value.preference_22
            this.preference_23 = value.preference_23
            this.preference_24 = value.preference_24
            this.preference_25 = value.preference_25
            this.preference_26 = value.preference_26
            this.preference_27 = value.preference_27
            this.preference_28 = value.preference_28
            this.preference_29 = value.preference_29
            this.preference_30 = value.preference_30
            this.preference_31 = value.preference_31
            this.preference_32 = value.preference_32
            this.preference_33 = value.preference_33
            this.preference_34 = value.preference_34
            this.preference_35 = value.preference_35
            this.preference_36 = value.preference_36
            this.preference_37 = value.preference_37
            this.preference_38 = value.preference_38
            this.preference_39 = value.preference_39
            this.preference_40 = value.preference_40
            this.preference_41 = value.preference_41
            this.preference_42 = value.preference_42
            this.preference_43 = value.preference_43
            this.preference_44 = value.preference_44
            this.preference_45 = value.preference_45
            this.preference_46 = value.preference_46
            this.preference_47 = value.preference_47
            this.preference_48 = value.preference_48
            this.preference_49 = value.preference_49
            this.preference_50 = value.preference_50
            this.preference_51 = value.preference_51
            this.preference_52 = value.preference_52
            this.preference_53 = value.preference_53
            this.preference_54 = value.preference_54
            this.preference_55 = value.preference_55
            this.preference_56 = value.preference_56
            this.preference_57 = value.preference_57
            this.preference_58 = value.preference_58
            this.preference_59 = value.preference_59
            this.preference_60 = value.preference_60
            this.preference_99 = value.preference_99
            this.preferenceNewBuild = value.preferenceNewBuild
            this.preferenceCornerRoom = value.preferenceCornerRoom
            this.preferenceAbove_2ndFloor = value.preferenceAbove_2ndFloor
            this.lineName = value.lineName
            this.stationName = value.stationName
            this.busStopName = value.busStopName
            this.busTime = value.busTime
            this.walkingTime = value.walkingTime
            this.distance = value.distance
            this.keyMoney = value.keyMoney
            this.deposit = value.deposit
            this.neighborhoodAssociationFee = value.neighborhoodAssociationFee
            this.commonServiceFee = value.commonServiceFee
            this.roomTypeName = value.roomTypeName
            this.layoutType = value.layoutType
            this.layout = value.layout
            this.layoutDetails = value.layoutDetails
            this.parkingType = value.parkingType
            this.parkingFee = value.parkingFee
            this.constructionYearMonth = value.constructionYearMonth
            this.handlingStoreCompany = value.handlingStoreCompany
            this.locationListingArea = value.locationListingArea
            this.floorNumber = value.floorNumber
            this.direction = value.direction
            this.roomPosition = value.roomPosition
            this.availableMoveInYearMonth = value.availableMoveInYearMonth
            this.transportation = value.transportation
            this.equipment = value.equipment
            this.notes = value.notes
            this.inquiryBranchName = value.inquiryBranchName
            this.branchPhoneNumber = value.branchPhoneNumber
            this.branchFaxNumber = value.branchFaxNumber
            this.transactionType = value.transactionType
            this.buildingName = value.buildingName
            this.structureName = value.structureName
            this.agentAssignableType = value.agentAssignableType
            this.subleaseType = value.subleaseType
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.branchAddress = value.branchAddress
            this.recommendationComment = value.recommendationComment
            this.completionYearMonth = value.completionYearMonth
            this.propertyPostalCode = value.propertyPostalCode
            this.vacateNoticeDate = value.vacateNoticeDate
            this.expectedMoveOutDate = value.expectedMoveOutDate
            this.moveOutDate = value.moveOutDate
            this.expectedCompletionDate = value.expectedCompletionDate
            this.availableMoveInDate = value.availableMoveInDate
            this.moveInApplicationDate = value.moveInApplicationDate
            this.depositDate = value.depositDate
            this.balanceCollectionDate = value.balanceCollectionDate
            this.moveInDate = value.moveInDate
            this.completionDate = value.completionDate
            this.tenantRecruitmentCollectionDate = value.tenantRecruitmentCollectionDate
            this.tenant = value.tenant
            this.owner = value.owner
            this.customerAgentBranchCd = value.customerAgentBranchCd
            this.customerAgentDepartmentCd = value.customerAgentDepartmentCd
            this.customerAgentEmployeeCd = value.customerAgentEmployeeCd
            this.rentTax = value.rentTax
            this.keyMoneyTax = value.keyMoneyTax
            this.keyMoneyTotal = value.keyMoneyTotal
            this.commonServiceFeeTax = value.commonServiceFeeTax
            this.parkingFeeTax = value.parkingFeeTax
            this.roomNumber = value.roomNumber
            this.newExistingFlag = value.newExistingFlag
            this.roomStatusType = value.roomStatusType
            this.recordStatusType = value.recordStatusType
            this.ffUsagePeriod = value.ffUsagePeriod
            this.adPayableAmount = value.adPayableAmount
            this.locationCity = value.locationCity
            this.tenantContractNumber = value.tenantContractNumber
            this.ownerContact = value.ownerContact
            this.vacantPeriod = value.vacantPeriod
            this.floorArea_1f = value.floorArea_1f
            this.floorArea_2f = value.floorArea_2f
            this.floorArea_3f = value.floorArea_3f
            this.recruitmentCreationDate = value.recruitmentCreationDate
            this.recruitmentApprovalDate = value.recruitmentApprovalDate
            this.moveOutInspectionDate = value.moveOutInspectionDate
            this.restorationDate = value.restorationDate
            this.restorationCompletionDate = value.restorationCompletionDate
            this.vacantBookingDate = value.vacantBookingDate
            this.vacantBookingCompletionDate = value.vacantBookingCompletionDate
            this.additionalKeyMoney = value.additionalKeyMoney
            this.mutualAidJoinSign = value.mutualAidJoinSign
            this.rentalType = value.rentalType
            this.specialRentalType = value.specialRentalType
            this.distance2 = value.distance2
            this.approvalType = value.approvalType
            this.recordSeparator = value.recordSeparator
            this.changeType = value.changeType
            this.noDepositFlag = value.noDepositFlag
            this.campaignTargetFlag = value.campaignTargetFlag
            this.preference_61 = value.preference_61
            this.preference_62 = value.preference_62
            this.preference_63 = value.preference_63
            this.preference_64 = value.preference_64
            this.preference_65 = value.preference_65
            this.preference_66 = value.preference_66
            this.preference_67 = value.preference_67
            this.preference_68 = value.preference_68
            this.preference_69 = value.preference_69
            this.preference_70 = value.preference_70
            this.preference_71 = value.preference_71
            this.preference_72 = value.preference_72
            this.preference_73 = value.preference_73
            this.preference_74 = value.preference_74
            this.preference_75 = value.preference_75
            this.preference_76 = value.preference_76
            this.preference_77 = value.preference_77
            this.preference_78 = value.preference_78
            this.preference_79 = value.preference_79
            this.preference_80 = value.preference_80
            this.preference_81 = value.preference_81
            this.preference_82 = value.preference_82
            this.preference_83 = value.preference_83
            this.preference_84 = value.preference_84
            this.preference_85 = value.preference_85
            this.preference_86 = value.preference_86
            this.preference_87 = value.preference_87
            this.preference_88 = value.preference_88
            this.preference_89 = value.preference_89
            this.preference_90 = value.preference_90
            this.preference_91 = value.preference_91
            this.preference_92 = value.preference_92
            this.preference_93 = value.preference_93
            this.preference_94 = value.preference_94
            this.preference_95 = value.preference_95
            this.preference_96 = value.preference_96
            this.preference_97 = value.preference_97
            this.preference_98 = value.preference_98
            this.propertyAddress = value.propertyAddress
            this.propertyAddressDetail = value.propertyAddressDetail
            this.serviceRoomSign = value.serviceRoomSign
            this.highVoltageBulkReceipt = value.highVoltageBulkReceipt
            this.highRentalSign = value.highRentalSign
            this.solarDiscountTarget = value.solarDiscountTarget
            this.cleaningCostFixed = value.cleaningCostFixed
            this.previousRent = value.previousRent
            this.existingReviewUpdateDate = value.existingReviewUpdateDate
            this.moveOutInspectionTime = value.moveOutInspectionTime
            this.recruitmentStartDate = value.recruitmentStartDate
            this.cleaningCostTotal = value.cleaningCostTotal
            this.discountInitialValueSign = value.discountInitialValueSign
            this.flagReserve_7 = value.flagReserve_7
            this.petFlag = value.petFlag
            this.flagReserve_9 = value.flagReserve_9
            this.flagReserve_10 = value.flagReserve_10
            this.challengeStartDate = value.challengeStartDate
            this.challengeEndDate = value.challengeEndDate
            this.applicationEndDate = value.applicationEndDate
            this.moveInEndDate = value.moveInEndDate
            this.additionalReleaseDate = value.additionalReleaseDate
            this.recruitmentRent = value.recruitmentRent
            this.challengeAdditionalAmount = value.challengeAdditionalAmount
            this.reviewRent = value.reviewRent
            this.dateReserve_11 = value.dateReserve_11
            this.dateReserve_12 = value.dateReserve_12
            this.dateReserve_13 = value.dateReserve_13
            this.amountReserve_1 = value.amountReserve_1
            this.amountReserve_2 = value.amountReserve_2
            this.amountReserve_3 = value.amountReserve_3
            this.commonServiceFeeBase = value.commonServiceFeeBase
            this.generalCableTvBase = value.generalCableTvBase
            this.generalCableTvTax = value.generalCableTvTax
            this.generalInternetBase = value.generalInternetBase
            this.generalInternetTax = value.generalInternetTax
            this.generalWaterQualityBase = value.generalWaterQualityBase
            this.generalWaterQualityTax = value.generalWaterQualityTax
            this.generalTenantWaterBase = value.generalTenantWaterBase
            this.generalTenantWaterTax = value.generalTenantWaterTax
            this.generalDrainUseBase = value.generalDrainUseBase
            this.generalDrainUseTax = value.generalDrainUseTax
            this.generalGarbageCollectionBase = value.generalGarbageCollectionBase
            this.generalGarbageCollectionTax = value.generalGarbageCollectionTax
            this.generalSharedAntennaBase = value.generalSharedAntennaBase
            this.generalSharedAntennaTax = value.generalSharedAntennaTax
            this.generalOwnerCleaningBase = value.generalOwnerCleaningBase
            this.generalOwnerCleaningTax = value.generalOwnerCleaningTax
            this.generalBuildingMaintenanceBase = value.generalBuildingMaintenanceBase
            this.generalBuildingMaintenanceTax = value.generalBuildingMaintenanceTax
            this.generalBuildingManagementBase = value.generalBuildingManagementBase
            this.generalBuildingManagementTax = value.generalBuildingManagementTax
            this.generalNeighborhoodAssocBase = value.generalNeighborhoodAssocBase
            this.generalNeighborhoodAssocTax = value.generalNeighborhoodAssocTax
            this.generalNeighborhoodOtherBase = value.generalNeighborhoodOtherBase
            this.generalNeighborhoodOtherTax = value.generalNeighborhoodOtherTax
            this.generalRepaymentAgentBase = value.generalRepaymentAgentBase
            this.generalRepaymentAgentTax = value.generalRepaymentAgentTax
            this.generalHlCommissionBase = value.generalHlCommissionBase
            this.generalHlCommissionTax = value.generalHlCommissionTax
            this.generalFurnishedBase = value.generalFurnishedBase
            this.generalFurnishedTax = value.generalFurnishedTax
            this.generalTenantDepositBase = value.generalTenantDepositBase
            this.generalTenantDepositTax = value.generalTenantDepositTax
            this.generalRentalBase = value.generalRentalBase
            this.generalRentalTax = value.generalRentalTax
            this.reserveAmount_1Base = value.reserveAmount_1Base
            this.reserveAmount_1Tax = value.reserveAmount_1Tax
            this.reserveAmount_2Base = value.reserveAmount_2Base
            this.reserveAmount_2Tax = value.reserveAmount_2Tax
            this.reserveAmount_3Base = value.reserveAmount_3Base
            this.reserveAmount_3Tax = value.reserveAmount_3Tax
            this.flagReserve_11 = value.flagReserve_11
            this.flagReserve_12 = value.flagReserve_12
            this.bundleWater = value.bundleWater
            this.bundleElectricity = value.bundleElectricity
            this.bundleGas = value.bundleGas
            this.category_2digitReserve_1 = value.category_2digitReserve_1
            this.category_2digitReserve_2 = value.category_2digitReserve_2
            this.category_2digitReserve_3 = value.category_2digitReserve_3
            this.category_2digitReserve_4 = value.category_2digitReserve_4
            this.category_2digitReserve_5 = value.category_2digitReserve_5
            this.amountReserve_4 = value.amountReserve_4
            this.amountReserve_5 = value.amountReserve_5
            this.amountReserve_6 = value.amountReserve_6
            this.amountReserve_7 = value.amountReserve_7
            this.amountReserve_8 = value.amountReserve_8
            this.dateReserve_14 = value.dateReserve_14
            this.dateReserve_15 = value.dateReserve_15
            this.dateReserve_16 = value.dateReserve_16
            this.dateReserve_17 = value.dateReserve_17
            this.dateReserve_18 = value.dateReserve_18
            this.category_1digitReserve_1 = value.category_1digitReserve_1
            this.category_1digitReserve_2 = value.category_1digitReserve_2
            this.category_1digitReserve_3 = value.category_1digitReserve_3
            this.category_1digitReserve_4 = value.category_1digitReserve_4
            this.category_1digitReserve_5 = value.category_1digitReserve_5
            this.leasingStoreCd = value.leasingStoreCd
            this.managementBranchCd = value.managementBranchCd
            this.salesOfficeCd = value.salesOfficeCd
            this.screeningBranchCd = value.screeningBranchCd
            this.preference_100 = value.preference_100
            this.preference_101 = value.preference_101
            this.preference_102 = value.preference_102
            this.preference_103 = value.preference_103
            this.preference_104 = value.preference_104
            this.preference_105 = value.preference_105
            this.preference_106 = value.preference_106
            this.preference_107 = value.preference_107
            this.preference_108 = value.preference_108
            this.preference_109 = value.preference_109
            this.preference_110 = value.preference_110
            this.preference_111 = value.preference_111
            this.preference_112 = value.preference_112
            this.preference_113 = value.preference_113
            this.preference_114 = value.preference_114
            this.preference_115 = value.preference_115
            this.preference_116 = value.preference_116
            this.preference_117 = value.preference_117
            this.preference_118 = value.preference_118
            this.preference_119 = value.preference_119
            this.preference_120 = value.preference_120
            this.preference_121 = value.preference_121
            this.preference_122 = value.preference_122
            this.preference_123 = value.preference_123
            this.preference_124 = value.preference_124
            this.preference_125 = value.preference_125
            this.preference_126 = value.preference_126
            this.preference_127 = value.preference_127
            this.preference_128 = value.preference_128
            this.preference_129 = value.preference_129
            this.preference_130 = value.preference_130
            this.preference_131 = value.preference_131
            this.preference_132 = value.preference_132
            this.preference_133 = value.preference_133
            this.preference_134 = value.preference_134
            this.preference_135 = value.preference_135
            this.preference_136 = value.preference_136
            this.preference_137 = value.preference_137
            this.preference_138 = value.preference_138
            this.preference_139 = value.preference_139
            this.preference_140 = value.preference_140
            this.preference_141 = value.preference_141
            this.preference_142 = value.preference_142
            this.preference_143 = value.preference_143
            this.preference_144 = value.preference_144
            this.preference_145 = value.preference_145
            this.preference_146 = value.preference_146
            this.preference_147 = value.preference_147
            this.preference_148 = value.preference_148
            this.preference_149 = value.preference_149
            this.preference_150 = value.preference_150
            this.preference_151 = value.preference_151
            this.preference_152 = value.preference_152
            this.preference_153 = value.preference_153
            this.preference_154 = value.preference_154
            this.preference_155 = value.preference_155
            this.preference_156 = value.preference_156
            this.preference_157 = value.preference_157
            this.preference_158 = value.preference_158
            this.preference_159 = value.preference_159
            this.preference_160 = value.preference_160
            this.preference_161 = value.preference_161
            this.preference_162 = value.preference_162
            this.preference_163 = value.preference_163
            this.preference_164 = value.preference_164
            this.preference_165 = value.preference_165
            this.preference_166 = value.preference_166
            this.preference_167 = value.preference_167
            this.preference_168 = value.preference_168
            this.preference_169 = value.preference_169
            this.preference_170 = value.preference_170
            this.preference_171 = value.preference_171
            this.preference_172 = value.preference_172
            this.preference_173 = value.preference_173
            this.preference_174 = value.preference_174
            this.preference_175 = value.preference_175
            this.preference_176 = value.preference_176
            this.preference_177 = value.preference_177
            this.preference_178 = value.preference_178
            this.preference_179 = value.preference_179
            this.preference_180 = value.preference_180
            this.preference_181 = value.preference_181
            this.preference_182 = value.preference_182
            this.preference_183 = value.preference_183
            this.preference_184 = value.preference_184
            this.preference_185 = value.preference_185
            this.preference_186 = value.preference_186
            this.preference_187 = value.preference_187
            this.preference_188 = value.preference_188
            this.preference_189 = value.preference_189
            this.preference_190 = value.preference_190
            this.preference_191 = value.preference_191
            this.preference_192 = value.preference_192
            this.preference_193 = value.preference_193
            this.preference_194 = value.preference_194
            this.preference_195 = value.preference_195
            this.preference_196 = value.preference_196
            this.preference_197 = value.preference_197
            this.preference_198 = value.preference_198
            this.preference_199 = value.preference_199
            this.marketingBranchOfficeCd = value.marketingBranchOfficeCd
            this.propertySituation = value.propertySituation
            this.prefectureCityCd = value.prefectureCityCd
            resetChangedOnNotNull()
        }
    }
}
