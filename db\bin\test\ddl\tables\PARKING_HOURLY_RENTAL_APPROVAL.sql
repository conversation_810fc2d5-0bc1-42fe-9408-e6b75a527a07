-- TABLE: PARKING_HOURLY_RENTAL_APPROVAL(駐車場時間貸承諾)

CREATE TABLE PARKING_HOURLY_RENTAL_APPROVAL(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1)                    
,    BUILDING_CODE                                varchar(9)                    
,    PARKING_TIME_RENTAL_CONSENT_TYPE             varchar(1)                    
,    BUILDING_NAME                                varchar(32)                   
,    R<PERSON><PERSON>TRATION_CONTRACT_TYPE_NAME              varchar(28)                   
,    REGISTRATION_MANAGEMENT_RESPONSIBLE          varchar(6)                    
,    REGISTRATION_OWNER_CD                        varchar(8)                    
,    REGISTRATION_OWNER_NAME                      varchar(42)                   
,    CONSENT_DATE                                 numeric(8)                    
,    CONSTRAINT UQ_PARKING_HOURLY_RENTAL_APPROVAL UNIQUE (BUILDING_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PARKING_HOURLY_RENTAL_APPROVAL IS '駐車場時間貸承諾 既存システム物理名: HVR10P';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.CREATION_DATE IS '作成年月日 既存システム物理名: HVR01D @290';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.CREATION_TIME IS '作成時刻 既存システム物理名: HVR02H @290';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.UPDATE_DATE IS '更新年月日 既存システム物理名: HVR03D';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.UPDATE_TIME IS '更新時刻 既存システム物理名: HVR04H';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: HVR05N';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.UPDATER IS '更新者 既存システム物理名: HVR06C';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: HVR07S';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.BUILDING_CODE IS '建物コード 既存システム物理名: HVR08C';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.PARKING_TIME_RENTAL_CONSENT_TYPE IS '駐車場時間貸 承諾区分 既存システム物理名: HVR09S';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.BUILDING_NAME IS '建物名 既存システム物理名: HVR10M';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.REGISTRATION_CONTRACT_TYPE_NAME IS '登録時点契約形態名 既存システム物理名: HVR11M';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.REGISTRATION_MANAGEMENT_RESPONSIBLE IS '登録時点管理担当者 既存システム物理名: HVR12C';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.REGISTRATION_OWNER_CD IS '登録時点オーナー 様CD 既存システム物理名: HVR13C';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.REGISTRATION_OWNER_NAME IS '登録時点オーナー 様名 既存システム物理名: HVR14M';
COMMENT ON COLUMN PARKING_HOURLY_RENTAL_APPROVAL.CONSENT_DATE IS '承諾日 既存システム物理名: HVR15D';
