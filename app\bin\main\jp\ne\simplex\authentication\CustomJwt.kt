package jp.ne.simplex.authentication

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTCreator
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.interfaces.Verification
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.model.Office
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*

class CustomJwt {

    class Builder {
        companion object {
            private lateinit var builder: JWTCreator.Builder

            fun create(): Builder {
                builder = JWT.create()
                return Builder()
            }
        }

        fun withClaim(employee: Employee): Builder {
            builder.run {
                withClaim(
                    Claim.EMPLOYEE_ID.key,
                    employee.code.value
                )
                employee.getOfficeCode()?.let {
                    withClaim(
                        Claim.BUSINESS_OFFICE_CODE.key,
                        it.value,
                    )
                }
                employee.company?.takeIf {
                    it.code.isNotEmpty()
                }?.let {
                    withClaim(
                        Claim.COMPANY_CODE.key,
                        it.code,
                    )
                }
            }

            return this
        }

        fun withMeta(currentDateTime: LocalDateTime, expireDateTime: LocalDateTime): Builder {
            builder
                .withIssuedAt(currentDateTime.toDate())
                .withExpiresAt(expireDateTime.toDate())

            return this
        }

        fun sign(secretKey: String): String {
            return builder.sign(resolveAlgorithm(secretKey))
        }

        private fun LocalDateTime.toDate() =
            Date.from(this.atZone(ZoneId.systemDefault()).toInstant())

    }

    class Verifier {
        companion object {

            private lateinit var verifier: Verification

            fun create(secretKey: String): Verifier {
                verifier = JWT.require(resolveAlgorithm(secretKey))
                return Verifier()
            }
        }

        fun withClaimPresence(): Verifier {
            verifier.withClaimPresence(Claim.EMPLOYEE_ID.key)
            return this
        }

        fun verify(tokenValue: String): AuthInfo.Jwt {
            val decoded = verifier.build().verify(tokenValue)

            return AuthInfo.Jwt(
                employeeCode = Employee.Code(decoded.getClaim(Claim.EMPLOYEE_ID.key).asString()),
                businessOfficeCode = decoded.getClaim(Claim.BUSINESS_OFFICE_CODE.key)?.asString()
                    ?.let { Office.Code.of(it) },
                companyCode = decoded.getClaim(Claim.COMPANY_CODE.key)?.asString(),
            )
        }
    }

    companion object {
        private enum class Claim(val key: String) {
            EMPLOYEE_ID("employeeId"),
            BUSINESS_OFFICE_CODE("businessOfficeCode"),
            COMPANY_CODE("companyCode")
        }

        fun builder(): Builder {
            return Builder.create()
        }

        fun verifier(secretKey: String): Verifier {
            return Verifier.create(secretKey)
        }

        private fun resolveAlgorithm(secretKey: String): Algorithm {
            return Algorithm.HMAC256(secretKey)
        }
    }
}
