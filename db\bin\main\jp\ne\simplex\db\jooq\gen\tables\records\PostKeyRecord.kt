/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.PostKeyTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.PostKeyPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * ポスト鍵 既存システム物理名: EMPSTP
 */
@Suppress("UNCHECKED_CAST")
open class PostKeyRecord private constructor() : UpdatableRecordImpl<PostKeyRecord>(PostKeyTable.POST_KEY) {

    open var creationDate: Long?
        set(value): Unit = set(0, value)
        get(): Long? = get(0) as Long?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Long?
        set(value): Unit = set(3, value)
        get(): Long? = get(3) as Long?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var buildingCd: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var roomCd: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var postKeyInfo: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var internetId: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var internetPassword: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised PostKeyRecord
     */
    constructor(creationDate: Long? = null, creationTime: Int? = null, creator: String? = null, updateDate: Long? = null, updateTime: Int? = null, updater: String? = null, buildingCd: String, roomCd: String, postKeyInfo: String? = null, internetId: String? = null, internetPassword: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.buildingCd = buildingCd
        this.roomCd = roomCd
        this.postKeyInfo = postKeyInfo
        this.internetId = internetId
        this.internetPassword = internetPassword
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised PostKeyRecord
     */
    constructor(value: PostKeyPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.buildingCd = value.buildingCd
            this.roomCd = value.roomCd
            this.postKeyInfo = value.postKeyInfo
            this.internetId = value.internetId
            this.internetPassword = value.internetPassword
            resetChangedOnNotNull()
        }
    }
}
