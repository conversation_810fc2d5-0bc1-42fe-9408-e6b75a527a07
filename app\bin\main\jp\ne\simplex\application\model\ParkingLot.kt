package jp.ne.simplex.application.model

import jp.ne.simplex.application.model.ParkingLot.*
import jp.ne.simplex.application.model.VacancyParkingLotTarget.BulkLeaseFlag
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import java.time.LocalDate

/** 駐車場区画 */
class ParkingLot(
    // 駐車場区画を特定するID
    val id: Id,
    // 現地表示番号
    val localDisplayNumber: String? = null,
    // 駐車場区分
    val parkingLotCategory: Category? = null,
    // 駐車場区画ステータス
    val parkingStatusDivision: StatusDivision,
    // 駐車場空き区画ステータス
    val vacancyParkingStatus: VacancyStatus,
    // 利用可否
    val isAvailable: Boolean,
    // 賃料
    val parkingFee: Int? = null,
    // 賃料税込みサイン
    val parkingFeeInTax: Int? = null,
    // 査定区分
    val assessmentDivision: AssessmentDivision? = null,
    // 特約有無サイン
    val specialContractFlag: SpecialContractFlag? = null,
    // 契約形態
    val contractForm: String? = null,
    // 一括借上サイン
    val bulkLeaseFlag: BulkLeaseFlag? = null,
    // 入居予定日
    val moveInScheduledDate: LocalDate? = null,
    // 退去予定日(yyyyMMdd)
    val expectedMoveOutDate: LocalDate? = null,
    // 斡旋可否
    val brokerApplicationPossibility: BrokerApplicationPossibility? = null,
    // 敷地外駐車場区分
    val offSiteParkingLotCategory: OffSiteCategory,
    // 敷地外駐車場との距離
    val offSiteParkingDistance: Int? = null,
    // 当該区画に紐づく（契約可能な）物件ID
    // 別建物でも、同一受注コードであれば、区画の契約は可能
    // そのため、Id属性のbuildingCodeと、当該属性のbuildingCodeは一致しない場合がある
    val linkedPropertyId: Property.Id? = null,
    // 当該区画に紐づく（契約可能な）物件の部屋番号
    val linkedRoomNumber: Room.Number? = null,
    // 予約リスト
    val reservationList: List<ParkingReservationInfo>,
    // 入居者情報
    val tenant: Tenant? = null,
) {

    data class Id(
        // 建物コード
        val buildingCode: Building.Code,
        // 駐車場コード
        val parkingLotCode: Code,
    ) : Comparable<Id> {
        override fun compareTo(other: Id): Int {
            return if (this.buildingCode == other.buildingCode) {
                this.parkingLotCode.value.compareTo(other.parkingLotCode.value)
            } else {
                this.buildingCode.value.compareTo(other.buildingCode.value)
            }
        }
    }

    data class Code private constructor(val value: String) {
        companion object {
            private const val LENGTH = 3
            fun of(value: String): Code {
                return if (value.length == LENGTH) Code(value)
                else throw ModelCreationFailedException(
                    ErrorMessage.STRING_LENGTH.format("駐車場コード", LENGTH)
                )
            }
        }
    }

    enum class Category(val value: String) {
        SINGLE("1"), // 単
        PARALLEL("2"), // 縦
        KEI("3"), // 軽
        MULTI_LEVEL("4"), // 立
        BIKE("5"); // バイク

        companion object {
            fun fromValue(value: String?): Category? {
                return value?.let { Category.entries.find { it.value == value } }
            }
        }
    }

    /** 敷地外駐車場区分 */
    enum class OffSiteCategory(val value: Int) {
        OUTSIDE(1), // 敷地外
        INSIDE(2); // 敷地内
    }

    /** 空き区画ステータス */
    enum class VacancyStatus(val value: Int) {
        POSSIBLE(0), // 可
        IMPOSSIBLE(1), // 不可
        REQUIRED_CONFIRM(2), // 要問合せ
    }

    /** 査定区分 */
    enum class AssessmentDivision(val value: Int) {
        ASSESSMENT(1), // 査定
        OUTSIDE_ASSESSMENT(2); // 査定外

        companion object {
            fun fromValue(value: Int?): AssessmentDivision? {
                return value?.let { AssessmentDivision.entries.find { it.value == value } }
            }
        }
    }

    /** 駐車場ステータス */
    enum class StatusDivision(val value: String) {
        VACANT("0"), // 空(なし-なし)
        CANCEL("1"), // ｷｬﾝｾﾙ(なし-申込)
        APPLIED("2"), // 申込(契約)
        CONFIRMED("3"), // 確定(契約)
        OCCUPIED("4"), // 入居中(契約)
        PLANNED_MOVE_OUT("5"), // 退去予定(契約)
        COLLECTING("6"), // 残集(契約)
        BEFORE_BROKER_COLLECTION("99"); // 斡旋回収前 ※ウェルカムパークバッチ専用一時ステータス
    }

    /** 特約有無 */
    enum class SpecialContractFlag(val value: Int) {
        NO(0), // なし
        YES(1); // あり

        companion object {
            fun fromValue(value: Int?): SpecialContractFlag? {
                return value?.let { SpecialContractFlag.entries.find { it.value == value } }
            }
        }
    }

    /** 斡旋可否 */
    enum class BrokerApplicationPossibility(val value: Int) {
        IMPOSSIBLE(0), // なし
        POSSIBLE(1); // あり
    }

    // ParkingLotに下記型のプロパティを追加しても良いが、
    // 現状は使用する場面はないため、YAGNIに従って実装はしない
    /** 区画利用可否 */
    enum class Status(val value: String) {
        ENABLE("1"), // 利用可能
        DISABLE("0"); // 利用不可

        companion object {
            fun fromValue(value: String): Status? {
                return Status.entries.find { it.value == value }
            }
        }
    }

    fun convertFirstParkingContractPossibility(
        vacancyStatus: VacancyStatus,
        autoJudge: ParkingContractPossibility.ContractPossibilityAutoJudge
    ): ParkingContractPossibility.ContractPossibility {
        return if (autoJudge == ParkingContractPossibility.ContractPossibilityAutoJudge.MANUAL) {
            ParkingContractPossibility.ContractPossibility.REQUIRED_CONFIRM
        } else if (vacancyStatus == VacancyStatus.POSSIBLE) {
            ParkingContractPossibility.ContractPossibility.POSSIBLE
        } else {
            ParkingContractPossibility.ContractPossibility.IMPOSSIBLE
        }

    }

    fun convertPossibleUseDate(
        expectedMoveOutDate: LocalDate?,
        status: StatusDivision,
        firstParkingContractPossibility: ParkingContractPossibility.ContractPossibility,
        reservationList: List<ParkingReservationInfo>,
        isAutoJudge: ParkingContractPossibility.ContractPossibilityAutoJudge
    ): LocalDate? {
        val dates = mutableListOf<LocalDate>()
        if (status == StatusDivision.PLANNED_MOVE_OUT && expectedMoveOutDate != null) {
            dates.add(expectedMoveOutDate.plusDays(1))
        }
        reservationList.forEach { reservation ->
            if (reservation.reservationType == ParkingReservation.Type.WORK && reservation.reserveEndDatetime != null) {
                dates.add(reservation.reserveEndDatetime.toLocalDate().plusDays(1))
            } else if (reservation.reservationType == ParkingReservation.Type.ONE_DAY) {
                dates.add(reservation.receptionDate.plusDays(2))
            }
        }
        return dates.maxOrNull()
    }

    // 入居者情報
    class Tenant(
        // テナント契約番号
        val tenantContractNumber: String,
        // 元テナント契約番号
        val originalContractNumber: String,
        // 部屋実入居者名
        val propertyTenantName: String? = null,
        // 部屋契約者名
        val propertyTenantContractName: String? = null,
        // 駐車場契約者名
        val tenantContractName: String? = null,
        // 陸事名
        val landTransportName: String? = null,
        // 車両種別
        val type: String? = null,
        // 業態
        val businessCategory: String? = null,
        // 左ナンバー
        val leftNumber: String? = null,
        // 右ナンバー
        val rightNumber: String? = null,
        // メーカー区分
        val manufacturerDivision: String? = null,
        // 車両名
        val carModelName: String? = null,
        // 車庫証明発給サイン
        val parkingCertIssueSign: String? = null,
        // 車庫証明コメント
        val parkingCertComment: String? = null,
    )
}

/** 空き駐車場対象情報 */
class VacancyParkingLotTarget(
    // 駐車場区画を特定するID
    val id: Id,
    // 駐車場番号
    val parkingLotNumber: String? = null,
    // 賃料
    val parkingFee: Int? = null,
    // 一括借上サイン
    val bulkLeaseFlag: BulkLeaseFlag? = null,
    // 査定区分
    val assessmentDivision: AssessmentDivision? = null,
    // 駐車場区分
    val parkingLotCategory: Category? = null,
) {
    /** 一括借上サイン */
    enum class BulkLeaseFlag(val value: Int) {
        KANRIKENTAKU(0), // 管理契約(建託)
        SUB(1), // サブリース
        TEN_Y(2), // 保証10年型
        THIRTY_Y(3), // 保証30年型
        IKKATSU(4), // 30年一括借上
        KANRIDAITATE(5), // 管理契約(大建)
        CHINRYORENDO(6), // 入居賃料連動
        CHOCHINHENMU(7); // 長期賃料変更無

        companion object {
            fun fromValue(value: Int?): BulkLeaseFlag? {
                return value?.let { BulkLeaseFlag.entries.find { it.value == value } }
            }
        }
    }
}
