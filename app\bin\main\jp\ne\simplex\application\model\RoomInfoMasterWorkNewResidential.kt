package jp.ne.simplex.application.model

import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmm
import java.time.LocalDate
import java.time.LocalDateTime

/** 入居中物件データ作成バッチ用の居住用新築物件ワークデータ */
data class RoomInfoMasterWorkNewResidential(
    /** 支店CD */
    val branchCode: String?,
    /** 状況 */
    val status: String?,
    /** 日付 */
    val date: Int?,
    /** 交渉 */
    val negotiation: String?,
    /** 建物CD */
    val buildingCode: String,
    /** 部屋CD */
    val roomCode: String,
    /** 家主名称 */
    val landlordName: String?,
    /** 建物名称 */
    val buildingName: String?,
    /** 間取り名 */
    val layoutName: String?,
    /** 間取り */
    val layout: String?,
    /** 所在地1 */
    val address1: String?,
    /** 所在地2 */
    val address2: String?,
    /** 家賃 */
    val rent: Int?,
    /** 駐車料 */
    val parkingFee: Int?,
    /** 共益費 */
    val commonFee: Int?,
    /** 礼金 */
    val keyMoney: Int?,
    /** 保証金（敷金） */
    val securityDeposit: Int?,
    /** 町内会費 */
    val neighborhoodAssociationFee: Int?,
    /** 備考1 */
    val remarks1: String?,
    /** 備考2 */
    val remarks2: String?,
    /** ロッキー区分 */
    val rockyCategory: String?,
    /** 区分A */
    val categoryA: String?,
    /** 区分B */
    val categoryB: String?,
    /** 都道府県CD */
    val prefectureCode: String?,
    /** 市区郡CD */
    val cityCode: String?,
    /** 町村字通称CD */
    val townCode: String?,
    /** 物件住所かな */
    val propertyAddressKana: String?,
    /** 町村かな */
    val townNameKana: String?,
    /** 部屋番号 */
    val roomNumber: String?,
    /** 入居年 */
    val moveInYear: Int?,
    /** 入居月 */
    val moveInMonth: Int?,
    /** 旬 */
    val season: String?,
    /** 時間 */
    val time: Int?,
    /** 抽出用支店CD */
    val extractionBranchCode: String?,
    /** 所在地3 */
    val address3: String?,
    /** 入居可能年月日 */
    val moveInAvailableDate: Int?
) {
    companion object {

        fun from(
            source: RoomInfoMasterWorkSource
        ): RoomInfoMasterWorkNewResidential {
            val moveInDate = getMoveInDate(source)
            return RoomInfoMasterWorkNewResidential(
                branchCode = "", // 空白固定
                status = getStatus(source),
                date = getDate(source),
                negotiation = source.negotiationEmployeeName ?: "",
                buildingCode = source.buildingCode,
                roomCode = source.roomCode,
                landlordName = source.landlordName ?: "",
                buildingName = source.buildingName ?: "",
                layoutName = source.layoutDetails,
                layout = "", // 間取区分マスタが連携されないため空白をセット
                address1 = source.townKanjiName ?: "",
                address2 = source.addressDetails ?: "",
                rent = source.rent?.let { it / 100 },
                parkingFee = 0, // 消費税計算処理が不明なため0固定
                commonFee = source.commonFee?.let { it / 1000 },
                keyMoney = source.keyMoneyAmount?.let { keyMoney ->
                    source.rent?.takeIf { it > 0 }?.let { rent -> keyMoney / rent }
                },
                securityDeposit = source.depositAmount?.let { deposit ->
                    source.rent?.takeIf { it > 0 }?.let { rent -> deposit / rent }
                },
                neighborhoodAssociationFee = source.neighborhoodAssociationFee,
                remarks1 = "", // 空白固定
                remarks2 = "", // 空白固定
                rockyCategory = null, // null固定
                categoryA = null, // null固定
                categoryB = null, // null固定
                prefectureCode = source.prefectureCode,
                cityCode = source.cityCode,
                townCode = source.townCode,
                propertyAddressKana = source.addressDetails,
                townNameKana = source.townKanaName,
                roomNumber = source.roomNumber,
                moveInYear = moveInDate?.year,
                moveInMonth = moveInDate?.month?.value,
                season = getSeason(moveInDate),
                time = LocalDateTime.now().HHmm().toInt(),
                extractionBranchCode = source.tenantRecruitmentBranchCode,
                address3 = (source.townKanjiName ?: "") + (source.addressDetails ?: ""),
                moveInAvailableDate = moveInDate?.yyyyMMdd()?.toInt(),
            )
        }

        private fun getStatus(source: RoomInfoMasterWorkSource): String {
            if (source.brokerApplicationCollectionDivision == "2") return "暫"
            // テナントキャンセルDBが連携されないため空白をセット
            return ""
        }

        private fun getDate(source: RoomInfoMasterWorkSource): Int? {
            return when (getStatus(source)) {
                "申" -> source.moveInApplicationDate?.yyyyMMdd()?.toInt()
                "手" -> source.depositChangeDate?.yyyyMMdd()?.toInt()
                "確" -> source.leaseContractDate?.yyyyMMdd()?.toInt()
                else -> 0
            }
        }

        private fun getMoveInDate(source: RoomInfoMasterWorkSource): LocalDate? {
            return source.completionDeliveryDate ?: source.completionExpectedDate
        }

        private fun getSeason(source: LocalDate?): String? {
            return when (source?.dayOfMonth) {
                in 1..10 -> "月／上旬"
                in 11..20 -> "月／中旬"
                in 21..31 -> "月／下旬"
                else -> null
            }
        }
    }
}
