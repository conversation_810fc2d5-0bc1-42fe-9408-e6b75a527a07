package jp.ne.simplex.authentication.saml

import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import jp.ne.simplex.application.controller.client.auth.ClientAuthController
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ServerValidationException
import org.slf4j.LoggerFactory
import org.springframework.security.core.Authentication
import org.springframework.security.saml2.core.Saml2Error
import org.springframework.security.saml2.provider.service.authentication.Saml2AuthenticationException
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler
import org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler
import org.springframework.stereotype.Component

@Component
class SamlAuthSuccessHandler(
    private val clientAuthController: ClientAuthController
) : SavedRequestAwareAuthenticationSuccessHandler() {

    companion object {
        private val log = LoggerFactory.getLogger(SamlAuthSuccessHandler::class.java)
    }

    private val failureHandler = SimpleUrlAuthenticationFailureHandler()

    override fun onAuthenticationSuccess(request: HttpServletRequest?, response: HttpServletResponse?, authentication: Authentication?) {
        try {
            if (authentication?.isAuthenticated!! && response != null) {
                val nameId = authentication.name
                clientAuthController.ssoLogin(Employee.Code.of(nameId.takeLast(6)), response)
                super.onAuthenticationSuccess(request, response, authentication)
            } else {
                // 社員マスタが見つからない場合
                throw ServerValidationException(ErrorMessage.AUTHENTICATION_FAILURE.format())
            }
        } catch (e: Exception) {
            log.info(e.stackTraceToString())
            failureHandler.onAuthenticationFailure(request, response, Saml2AuthenticationException(Saml2Error("success_handling_error", e.message), e))
        }
    }

    override fun setDefaultTargetUrl(rootUrl: String?) {
        super.setDefaultTargetUrl(rootUrl)
        super.setAlwaysUseDefaultTargetUrl(true)
        this.failureHandler.setDefaultFailureUrl("$rootUrl/#login")
    }
}
