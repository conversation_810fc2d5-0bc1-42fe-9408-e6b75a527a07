package jp.ne.simplex.application.repository.smtp

import jakarta.mail.Address
import jakarta.mail.Message
import jakarta.mail.Session
import jakarta.mail.Transport
import jakarta.mail.internet.MimeMessage
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository
import java.util.*

@Repository
class SmtpClient(private val config: SmtpConfig) {

    companion object {
        private const val ENCODE_UTF_8 = "UTF-8"

        private val log = LoggerFactory.getLogger(SmtpClient::class.java)
    }

    private val property = System.getProperties().apply {
        put("mail.smtp.host", config.host)
        put("mail.smtp.port", config.port)
        put("mail.host", config.host)
    }

    fun send(subject: String, text: String, from: Address, to: List<Address>, cc: List<Address>) {
        val session = Session.getInstance(property, null)

        val message = MimeMessage(session).apply {
            setHeader("Content-Type", "text/html")
            sentDate = Date()
            setFrom(from)
            setRecipients(Message.RecipientType.TO, to.toTypedArray())
            setRecipients(Message.RecipientType.CC, cc.toTypedArray())
            setSubject(subject, ENCODE_UTF_8)
            setText(text, ENCODE_UTF_8)
        }

        try {
            return Transport.send(message)
        } catch (e: Exception) {
            log.warn("メールの送信に失敗しました: ${e.message}")
            throw e
        }
    }
}
