/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ProductMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ProductMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 商品マスタ 既存システム物理名: BGKMBP
 */
@Suppress("UNCHECKED_CAST")
open class ProductMasterRecord private constructor() : TableRecordImpl<ProductMasterRecord>(ProductMasterTable.PRODUCT_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgram: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var deleteFlag: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var productNameCode: Short
        set(value): Unit = set(7, value)
        get(): Short = get(7) as Short

    open var productCodeBranch: Byte
        set(value): Unit = set(8, value)
        get(): Byte = get(8) as Byte

    open var effectiveStartDate: Int
        set(value): Unit = set(9, value)
        get(): Int = get(9) as Int

    open var effectiveEndDate: Int?
        set(value): Unit = set(10, value)
        get(): Int? = get(10) as Int?

    open var gradeName: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var gradeAbbreviation: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var buildingTypeCodeSt: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var minUnitAlignment: Short?
        set(value): Unit = set(14, value)
        get(): Short? = get(14) as Short?

    open var maxUnitAlignment: Short?
        set(value): Unit = set(15, value)
        get(): Short? = get(15) as Short?

    /**
     * Create a detached, initialised ProductMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgram: String? = null, updater: String? = null, deleteFlag: String? = null, productNameCode: Short, productCodeBranch: Byte, effectiveStartDate: Int, effectiveEndDate: Int? = null, gradeName: String? = null, gradeAbbreviation: String? = null, buildingTypeCodeSt: String? = null, minUnitAlignment: Short? = null, maxUnitAlignment: Short? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgram = updateProgram
        this.updater = updater
        this.deleteFlag = deleteFlag
        this.productNameCode = productNameCode
        this.productCodeBranch = productCodeBranch
        this.effectiveStartDate = effectiveStartDate
        this.effectiveEndDate = effectiveEndDate
        this.gradeName = gradeName
        this.gradeAbbreviation = gradeAbbreviation
        this.buildingTypeCodeSt = buildingTypeCodeSt
        this.minUnitAlignment = minUnitAlignment
        this.maxUnitAlignment = maxUnitAlignment
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ProductMasterRecord
     */
    constructor(value: ProductMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgram = value.updateProgram
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            this.productNameCode = value.productNameCode
            this.productCodeBranch = value.productCodeBranch
            this.effectiveStartDate = value.effectiveStartDate
            this.effectiveEndDate = value.effectiveEndDate
            this.gradeName = value.gradeName
            this.gradeAbbreviation = value.gradeAbbreviation
            this.buildingTypeCodeSt = value.buildingTypeCodeSt
            this.minUnitAlignment = value.minUnitAlignment
            this.maxUnitAlignment = value.maxUnitAlignment
            resetChangedOnNotNull()
        }
    }
}
