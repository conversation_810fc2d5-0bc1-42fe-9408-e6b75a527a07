/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.DaitoBulkLeaseContractDetailsTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.DaitoBulkLeaseContractDetailsPojo

import org.jooq.impl.TableRecordImpl


/**
 * 大東一括借上・契約内容 既存システム物理名: HUA10P
 */
@Suppress("UNCHECKED_CAST")
open class DaitoBulkLeaseContractDetailsRecord private constructor() : TableRecordImpl<DaitoBulkLeaseContractDetailsRecord>(DaitoBulkLeaseContractDetailsTable.DAITO_BULK_LEASE_CONTRACT_DETAILS) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creationProgramId: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var creationTerminalId: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var creationResponsibleCd: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateDate: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var updateTime: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var updateTerminalId: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var updateResponsibleCd: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var deleteSign: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var buildingCd: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var contractConclusionDate: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    open var contractStartDate: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var contractExpirationDate: Int?
        set(value): Unit = set(14, value)
        get(): Int? = get(14) as Int?

    open var paymentStartDateResidentialParking: Int?
        set(value): Unit = set(15, value)
        get(): Int? = get(15) as Int?

    open var paymentStartDateBusiness: Int?
        set(value): Unit = set(16, value)
        get(): Int? = get(16) as Int?

    open var managementTerminationEndDate: Int?
        set(value): Unit = set(17, value)
        get(): Int? = get(17) as Int?

    open var headOfficeApprovalDate: Int?
        set(value): Unit = set(18, value)
        get(): Int? = get(18) as Int?

    open var landlordCd: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var headOfficeApprovalCategory: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var contractTypeCategory: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var rentalRevisionDateResidentialParking: Int?
        set(value): Unit = set(22, value)
        get(): Int? = get(22) as Int?

    open var rentalRevisionDateBusiness: Int?
        set(value): Unit = set(23, value)
        get(): Int? = get(23) as Int?

    open var rentalRevisionStatusCategory: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var thirtyFiveYearLumpSumCategory: Byte?
        set(value): Unit = set(25, value)
        get(): Byte? = get(25) as Byte?

    /**
     * Create a detached, initialised DaitoBulkLeaseContractDetailsRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creationProgramId: String? = null, creationTerminalId: String? = null, creationResponsibleCd: String? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updateTerminalId: String? = null, updateResponsibleCd: String? = null, deleteSign: String? = null, buildingCd: String? = null, contractConclusionDate: Int? = null, contractStartDate: Int? = null, contractExpirationDate: Int? = null, paymentStartDateResidentialParking: Int? = null, paymentStartDateBusiness: Int? = null, managementTerminationEndDate: Int? = null, headOfficeApprovalDate: Int? = null, landlordCd: String? = null, headOfficeApprovalCategory: String? = null, contractTypeCategory: String? = null, rentalRevisionDateResidentialParking: Int? = null, rentalRevisionDateBusiness: Int? = null, rentalRevisionStatusCategory: String? = null, thirtyFiveYearLumpSumCategory: Byte? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creationProgramId = creationProgramId
        this.creationTerminalId = creationTerminalId
        this.creationResponsibleCd = creationResponsibleCd
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updateTerminalId = updateTerminalId
        this.updateResponsibleCd = updateResponsibleCd
        this.deleteSign = deleteSign
        this.buildingCd = buildingCd
        this.contractConclusionDate = contractConclusionDate
        this.contractStartDate = contractStartDate
        this.contractExpirationDate = contractExpirationDate
        this.paymentStartDateResidentialParking = paymentStartDateResidentialParking
        this.paymentStartDateBusiness = paymentStartDateBusiness
        this.managementTerminationEndDate = managementTerminationEndDate
        this.headOfficeApprovalDate = headOfficeApprovalDate
        this.landlordCd = landlordCd
        this.headOfficeApprovalCategory = headOfficeApprovalCategory
        this.contractTypeCategory = contractTypeCategory
        this.rentalRevisionDateResidentialParking = rentalRevisionDateResidentialParking
        this.rentalRevisionDateBusiness = rentalRevisionDateBusiness
        this.rentalRevisionStatusCategory = rentalRevisionStatusCategory
        this.thirtyFiveYearLumpSumCategory = thirtyFiveYearLumpSumCategory
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised DaitoBulkLeaseContractDetailsRecord
     */
    constructor(value: DaitoBulkLeaseContractDetailsPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creationProgramId = value.creationProgramId
            this.creationTerminalId = value.creationTerminalId
            this.creationResponsibleCd = value.creationResponsibleCd
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updateTerminalId = value.updateTerminalId
            this.updateResponsibleCd = value.updateResponsibleCd
            this.deleteSign = value.deleteSign
            this.buildingCd = value.buildingCd
            this.contractConclusionDate = value.contractConclusionDate
            this.contractStartDate = value.contractStartDate
            this.contractExpirationDate = value.contractExpirationDate
            this.paymentStartDateResidentialParking = value.paymentStartDateResidentialParking
            this.paymentStartDateBusiness = value.paymentStartDateBusiness
            this.managementTerminationEndDate = value.managementTerminationEndDate
            this.headOfficeApprovalDate = value.headOfficeApprovalDate
            this.landlordCd = value.landlordCd
            this.headOfficeApprovalCategory = value.headOfficeApprovalCategory
            this.contractTypeCategory = value.contractTypeCategory
            this.rentalRevisionDateResidentialParking = value.rentalRevisionDateResidentialParking
            this.rentalRevisionDateBusiness = value.rentalRevisionDateBusiness
            this.rentalRevisionStatusCategory = value.rentalRevisionStatusCategory
            this.thirtyFiveYearLumpSumCategory = value.thirtyFiveYearLumpSumCategory
            resetChangedOnNotNull()
        }
    }
}
