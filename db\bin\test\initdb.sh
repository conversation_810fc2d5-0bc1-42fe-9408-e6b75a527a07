#!/bin/bash
# shellcheck disable=SC2164
echo "$(date) /**************** 引数を確認 ****************/"
echo;
if [ -n "$SCRIPT_ARG" ]; then
  # ローカルの場合はDockerの環境設定で引数を渡す
  ENV=$SCRIPT_ARG
  export ROOT_DIR=/docker-entrypoint-initdb.d
else
  # ローカル以外の場合
  ENV=$1
  export ROOT_DIR=/tmp/postgres
fi
# 環境変数 ENV の値をチェック
case "$ENV" in
  "prd")
    echo "Running prd script..."
    ;;
  "dev")
    echo "Running dev script..."
    ;;
  "stg1")
    echo "Running stg1 script..."
    ;;
  "stg2")
    echo "Running stg2 script..."
    ;;
  "it")
    echo "Running it script..."
    ;;
  *)
    echo "Unknown environment: $ENV"
    echo "Please set ENV to 'dev', 'stg'..."
    exit 1
    ;;
esac

export ENV_DIR="${ROOT_DIR}/env"
export SCRIPTS_DIR="${ROOT_DIR}/scripts"
export DDL_DIR="${ROOT_DIR}/ddl"
export DML_DIR="${ROOT_DIR}/dml"
export LOGS_DIR="${ROOT_DIR}/logs"

echo "$(date) /**************** 環境設定をExport ****************/"
echo;
cd "${ENV_DIR}"
source "${ENV}".sh

echo "$(date) /**************** ユーザ・DB作成 ****************/"
echo;

cd "${SCRIPTS_DIR}"/create-db
sh main.sh

echo "$(date) /**************** ユーザ・DB作成処理完了 ****************/"

echo "$(date) /**************** DB初期化 ****************/"
echo;

cd "${SCRIPTS_DIR}"/init-db
sh main.sh

echo "$(date) /**************** DB初期化処理完了 ****************/"
