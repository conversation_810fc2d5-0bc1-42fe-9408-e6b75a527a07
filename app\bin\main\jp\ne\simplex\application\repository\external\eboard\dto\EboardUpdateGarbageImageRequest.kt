package jp.ne.simplex.application.repository.external.eboard.dto

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ImageFile
import jp.ne.simplex.application.repository.external.ExternalApiMultipartRequest
import jp.ne.simplex.application.repository.external.eboard.config.EboardApiPath
import jp.ne.simplex.application.repository.external.eboard.config.EboardRequest
import jp.ne.simplex.authentication.AuthInfo
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap

class EboardUpdateGarbageImageRequest private constructor(
    val tatemonoCd: String,

    val userId: String,

    val updateKbn: String,

    val image: ByteArrayResource? = null,

    ) : EboardRequest, ExternalApiMultipartRequest {
    override fun getApiPath(): EboardApiPath {
        return EboardApiPath.UPDATE_GARBAGE_IMAGE
    }

    override fun toMultipartMap(): MultiValueMap<String, Any> {
        return LinkedMultiValueMap<String, Any>().also {
            it.add("tatemonoCd", tatemonoCd)
            it.add("userId", userId)
            it.add("updateKbn", updateKbn)
            if (image != null) {
                it.add(
                    "InputStream",
                    HttpEntity<ByteArrayResource>(
                        image,
                        HttpHeaders().also {
                            it.contentType = MediaType.MULTIPART_FORM_DATA
                        }
                    ))
            }
        }
    }

    companion object {
        fun ofRegister(
            requestUser: AuthInfo.RequestUser,
            buildingCode: Building.Code,
            imageFile: ImageFile,
        ): EboardUpdateGarbageImageRequest {
            return EboardUpdateGarbageImageRequest(
                tatemonoCd = buildingCode.value,
                userId = requestUser.value,
                updateKbn = "1",
                image = object : ByteArrayResource(imageFile.imageData) {
                    override fun getFilename() = imageFile.fileName
                },
            )
        }

        fun ofDelete(
            requestUser: AuthInfo.RequestUser,
            buildingCode: Building.Code,
        ): EboardUpdateGarbageImageRequest {
            return EboardUpdateGarbageImageRequest(
                tatemonoCd = buildingCode.value,
                userId = requestUser.value,
                updateKbn = "2",
            )
        }
    }
}
