/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ThirtyFiveYearBulkBuildingFileContTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ThirtyFiveYearBulkBuildingFileContPojo

import org.jooq.impl.TableRecordImpl


/**
 * 35年一括建物ファイル(契約書) 既存システム物理名: HC360P
 */
@Suppress("UNCHECKED_CAST")
open class ThirtyFiveYearBulkBuildingFileContRecord private constructor() : TableRecordImpl<ThirtyFiveYearBulkBuildingFileContRecord>(ThirtyFiveYearBulkBuildingFileContTable.THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creationProgramId: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var creationTerminalId: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var creationResponsibleCd: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateDate: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var updateTime: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var updateTerminalId: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var updateResponsibleCd: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var logicalDeleteSign: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var buildingCd: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var effectiveDate: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    open var contractOutputManagementNo: Short?
        set(value): Unit = set(13, value)
        get(): Short? = get(13) as Short?

    open var bulkLeaseType: Byte?
        set(value): Unit = set(14, value)
        get(): Byte? = get(14) as Byte?

    /**
     * Create a detached, initialised ThirtyFiveYearBulkBuildingFileContRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creationProgramId: String? = null, creationTerminalId: String? = null, creationResponsibleCd: String? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updateTerminalId: String? = null, updateResponsibleCd: String? = null, logicalDeleteSign: String? = null, buildingCd: String? = null, effectiveDate: Int? = null, contractOutputManagementNo: Short? = null, bulkLeaseType: Byte? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creationProgramId = creationProgramId
        this.creationTerminalId = creationTerminalId
        this.creationResponsibleCd = creationResponsibleCd
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updateTerminalId = updateTerminalId
        this.updateResponsibleCd = updateResponsibleCd
        this.logicalDeleteSign = logicalDeleteSign
        this.buildingCd = buildingCd
        this.effectiveDate = effectiveDate
        this.contractOutputManagementNo = contractOutputManagementNo
        this.bulkLeaseType = bulkLeaseType
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ThirtyFiveYearBulkBuildingFileContRecord
     */
    constructor(value: ThirtyFiveYearBulkBuildingFileContPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creationProgramId = value.creationProgramId
            this.creationTerminalId = value.creationTerminalId
            this.creationResponsibleCd = value.creationResponsibleCd
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updateTerminalId = value.updateTerminalId
            this.updateResponsibleCd = value.updateResponsibleCd
            this.logicalDeleteSign = value.logicalDeleteSign
            this.buildingCd = value.buildingCd
            this.effectiveDate = value.effectiveDate
            this.contractOutputManagementNo = value.contractOutputManagementNo
            this.bulkLeaseType = value.bulkLeaseType
            resetChangedOnNotNull()
        }
    }
}
