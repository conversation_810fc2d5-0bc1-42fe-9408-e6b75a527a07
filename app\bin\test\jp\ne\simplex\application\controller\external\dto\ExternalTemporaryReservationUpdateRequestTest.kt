package jp.ne.simplex.application.controller.external.dto

import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import jp.ne.simplex.application.controller.external.property.dto.ExternalTemporaryReservationUpdateRequest
import jp.ne.simplex.application.model.ForceUpdateTemporaryReservation.ForceCancelTemporaryReservation
import jp.ne.simplex.application.model.ForceUpdateTemporaryReservation.ForceRegisterTemporaryReservation
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd

class ExternalTemporaryReservationUpdateRequestTest : FunSpec({

    context("外部公開している仮押さえ情報更新APIのリクエストを、仮押さえキャンセルフラグに応じて適切にデシリアライズできること") {

        context("仮押さえキャンセルフラグが「0」の場合、他社フラグに応じて、強制更新用のドメインに変換できること") {
            val base = ExternalTemporaryReservationUpdateRequest(
                buildingCd = "*********",
                roomCd = "01010",
                applicationScheduledDate = "20241219",
                temporaryReservationCancelFlag = "0",
                comment = "いい物件ボードからの仮押さえ登録リクエスト"
            )

            context("他社フラグが「0」の場合、担当支店及び担当者コードが必須で設定されること") {

                test("担当支店及び担当者コードが未設定の場合、ClientValidationExceptionがスローされること") {
                    shouldThrow<ClientValidationException> {
                        base.copy(
                            otherCompanyFlag = "0",
                            customerRepBranchCd = null,
                            applicationScheduledPersonCd = null,
                        ).toServiceInterface()
                    }
                }

                test("担当支店及び担当者コードが設定済みの場合、強制更新用のドメインに変換できること") {
                    val request = base.copy(
                        otherCompanyFlag = "0",
                        customerRepBranchCd = "843",
                        applicationScheduledPersonCd = "00011",
                    )
                    val actual = request.toServiceInterface()

                    actual.shouldBeInstanceOf<ForceRegisterTemporaryReservation>()
                    actual.getId().buildingCode.value.shouldBe(request.buildingCd)
                    actual.getId().roomCode.value.shouldBe(request.roomCd)
                    actual.comment.value.shouldBe(request.comment)
                    actual.scheduledMoveInDate.yyyyMMdd().shouldBe(request.applicationScheduledDate)
                    actual.assignedEmployeeCode!!.value.shouldBe(request.applicationScheduledPersonCd)
                    actual.assignedBranchCode!!.getValue().shouldBe(request.customerRepBranchCd)
                    actual.otherCompanyInfo.shouldBeNull()
                }
            }

            context("他社フラグが「1」の場合、他社情報が必須で設定されること") {

                test("他社情報が未設定の場合、ClientValidationExceptionがスローされること") {
                    shouldThrow<ClientValidationException> {
                        base.copy(
                            otherCompanyFlag = "1",
                            contractFormECode = null,
                            otherCompanyName = null,
                            otherCompanyStoreName = null,
                            otherCompanyRepName = null,
                        ).toServiceInterface()
                    }

                }

                test("他社情報が設定済みの場合、強制更新用のドメインに変換できること") {
                    val request = base.copy(
                        otherCompanyFlag = "1",
                        contractFormECode = "E230542",
                        otherCompanyName = "〇〇不動産",
                        otherCompanyStoreName = "渋谷支店",
                        otherCompanyRepName = "〇〇太郎",
                    )
                    val actual = request.toServiceInterface()

                    actual.shouldBeInstanceOf<ForceRegisterTemporaryReservation>()
                    actual.getId().buildingCode.value.shouldBe(request.buildingCd)
                    actual.getId().roomCode.value.shouldBe(request.roomCd)
                    actual.comment.value.shouldBe(request.comment)
                    actual.scheduledMoveInDate.yyyyMMdd().shouldBe(request.applicationScheduledDate)
                    actual.assignedEmployeeCode.shouldBeNull()
                    actual.assignedBranchCode.shouldBeNull()
                    actual.otherCompanyInfo!!.companyCode.shouldBe(request.contractFormECode)
                    actual.otherCompanyInfo!!.companyName.shouldBe(request.otherCompanyName)
                    actual.otherCompanyInfo!!.storeName.shouldBe(request.otherCompanyStoreName)
                    actual.otherCompanyInfo!!.staffName.shouldBe(request.otherCompanyRepName)
                }
            }

            context("他社フラグが「2」の場合、ClientValidationExceptionがスローされること") {
                shouldThrow<ClientValidationException> {
                    base.copy(
                        otherCompanyFlag = "2",
                        otherCompanyName = "〇〇不動産",
                        otherCompanyStoreName = "渋谷支店",
                        otherCompanyRepName = "〇〇太郎",
                    ).toServiceInterface()
                }
            }
        }


        test("仮押さえキャンセルフラグが「1」の場合、強制解除用の仮押さえドメインに変換できること") {
            val request = ExternalTemporaryReservationUpdateRequest(
                buildingCd = "*********",
                roomCd = "01010",
                temporaryReservationCancelFlag = "1",
                comment = "いい物件ボードからの仮押さえ登録リクエスト"
            )
            val actual = request.toServiceInterface()

            actual.shouldBeInstanceOf<ForceCancelTemporaryReservation>()
            actual.getId().buildingCode.value.shouldBe(request.buildingCd)
            actual.getId().roomCode.value.shouldBe(request.roomCd)
            actual.comment.value.shouldBe(request.comment)
        }

        test("仮押さえキャンセルフラグが「2」の場合、ClientValidationExceptionがスローされること") {
            shouldThrow<ClientValidationException> {
                ExternalTemporaryReservationUpdateRequest(
                    buildingCd = "*********",
                    roomCd = "01010",
                    temporaryReservationCancelFlag = "2",
                    comment = "いい物件ボードからの仮押さえ登録リクエスト"
                ).toServiceInterface()
            }
        }
    }

    context("外部公開している仮押さえ情報更新APIのリクエストの「コメント」は最大で257文字まで許容しないこと") {
        val base = ExternalTemporaryReservationUpdateRequest(
            buildingCd = "*********",
            roomCd = "01010",
            temporaryReservationCancelFlag = "1",
        )
        test("257文字のコメントが設定されている場合、適切にデシリアライズされること") {
            shouldNotThrow<ClientValidationException> {
                base.copy(comment = "◯".repeat(257)).toServiceInterface()
            }
        }

        test("258文字のコメントが設定されている場合、ClientValidationExceptionがスローされること") {
            shouldThrow<ClientValidationException> {
                base.copy(comment = "◯".repeat(258)).toServiceInterface()
            }
        }
    }
})
