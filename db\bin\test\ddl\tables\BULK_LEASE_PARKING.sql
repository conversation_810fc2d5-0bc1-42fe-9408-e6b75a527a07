-- TABLE: BULK_LEASE_PARKING(一括借上駐車場)

CREATE TABLE BULK_LEASE_PARKING(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1,0)                  
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    PARKING_CODE                                 varchar(3)        NOT NULL    
,    ASSESSMENT_DIVISION                          varchar(1)                    
,    CONSTRAINT PK_BULK_LEASE_PARKING PRIMARY KEY (BUILDING_CODE, PARKING_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE BULK_LEASE_PARKING IS '一括借上駐車場 既存システム物理名: EDD30P';
COMMENT ON COLUMN BULK_LEASE_PARKING.CREATION_DATE IS '作成年月日 既存システム物理名: EDD01D';
COMMENT ON COLUMN BULK_LEASE_PARKING.CREATION_TIME IS '作成時刻 既存システム物理名: EDD02H';
COMMENT ON COLUMN BULK_LEASE_PARKING.UPDATE_DATE IS '更新年月日 既存システム物理名: EDD03D';
COMMENT ON COLUMN BULK_LEASE_PARKING.UPDATE_TIME IS '更新時刻 既存システム物理名: EDD04H';
COMMENT ON COLUMN BULK_LEASE_PARKING.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EDD05N';
COMMENT ON COLUMN BULK_LEASE_PARKING.UPDATER IS '更新者 既存システム物理名: EDD06C';
COMMENT ON COLUMN BULK_LEASE_PARKING.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: EDD07S';
COMMENT ON COLUMN BULK_LEASE_PARKING.BUILDING_CODE IS '建物コード 既存システム物理名: EDDABC';
COMMENT ON COLUMN BULK_LEASE_PARKING.PARKING_CODE IS '駐車場コード 既存システム物理名: EDDBSC';
COMMENT ON COLUMN BULK_LEASE_PARKING.ASSESSMENT_DIVISION IS '査定区分 既存システム物理名: EDD08S';
