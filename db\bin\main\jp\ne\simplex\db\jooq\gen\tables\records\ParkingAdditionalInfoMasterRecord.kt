/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ParkingAdditionalInfoMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingAdditionalInfoMasterPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 駐車場付加情報DB 既存システム物理名: ECC90P
 */
@Suppress("UNCHECKED_CAST")
open class ParkingAdditionalInfoMasterRecord private constructor() : UpdatableRecordImpl<ParkingAdditionalInfoMasterRecord>(ParkingAdditionalInfoMasterTable.PARKING_ADDITIONAL_INFO_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteFlag: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var contractNumber: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var buildingCode: String
        set(value): Unit = set(8, value)
        get(): String = get(8) as String

    open var parkingCode: String
        set(value): Unit = set(9, value)
        get(): String = get(9) as String

    open var externalLendingCategory: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var roomBuildingCode: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var roomRoomCode: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var roomContractNumber: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised ParkingAdditionalInfoMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteFlag: Byte? = null, contractNumber: String? = null, buildingCode: String, parkingCode: String, externalLendingCategory: String? = null, roomBuildingCode: String? = null, roomRoomCode: String? = null, roomContractNumber: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteFlag = logicalDeleteFlag
        this.contractNumber = contractNumber
        this.buildingCode = buildingCode
        this.parkingCode = parkingCode
        this.externalLendingCategory = externalLendingCategory
        this.roomBuildingCode = roomBuildingCode
        this.roomRoomCode = roomRoomCode
        this.roomContractNumber = roomContractNumber
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ParkingAdditionalInfoMasterRecord
     */
    constructor(value: ParkingAdditionalInfoMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteFlag = value.logicalDeleteFlag
            this.contractNumber = value.contractNumber
            this.buildingCode = value.buildingCode
            this.parkingCode = value.parkingCode
            this.externalLendingCategory = value.externalLendingCategory
            this.roomBuildingCode = value.roomBuildingCode
            this.roomRoomCode = value.roomRoomCode
            this.roomContractNumber = value.roomContractNumber
            resetChangedOnNotNull()
        }
    }
}
