package jp.ne.simplex.mock

import jp.ne.simplex.authentication.AuthConfig
import jp.ne.simplex.authentication.saml.SingleSignOnConfig
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse

class MockSecretManagerClient(
    private var cache: Map<String, String> = mutableMapOf()
) : SecretsManagerClient {

    companion object {

        fun of(
            config: AuthConfig.Jwt,
            accessTokenSecretKey: String,
            refreshTokenSecretKey: String
        ): MockSecretManagerClient {
            return MockSecretManagerClient(
                cache = mapOf(
                    config.accessToken.secretId to accessTokenSecretKey,
                    config.refreshToken.secretId to refreshTokenSecretKey,
                )
            )

        }

        fun of(
            config: AuthConfig.Api<PERSON>ey,
            externalApiKeyList: String
        ): MockSecretManagerClient {
            return MockSecretManagerClient(
                cache = mapOf(
                    config.secretId
                        to externalApiKeyList
                )
            )
        }

        fun of(
            config: SingleSignOnConfig.AccessKeyConfig,
            hash: String
        ): MockSecretManagerClient {
            return MockSecretManagerClient(
                cache = mapOf(
                    config.secretId
                        to hash
                )
            )
        }
    }

    override fun getSecretValue(request: GetSecretValueRequest): GetSecretValueResponse? {
        val value = cache.getValue(request.secretId())
        return GetSecretValueResponse.builder().secretString(value).build()
    }

    override fun close() {
    }

    override fun serviceName(): String {
        return "secretsmanager"
    }

    fun add(key: String, value: String) {
        cache += key to value
    }
}
