#!/bin/bash

# 定数の設定
db_host="import-prd-20250421-rdsproxy.proxy-cfseiag02r46.ap-northeast-1.rds.amazonaws.com"
db_user="app"
db_name="gpb"
s3_bucket="s3://stg2-propetech-pg-dump-bucket"
secret_id="rds-db-credentials/cluster-LVWBFCUOC52ECKIVKS35FZOWB4/app/1747027152168"

export PGPASSWORD=$(aws secretsmanager get-secret-value --secret-id "$secret_id" --query SecretString --output text | jq -r .password)

# 最終更新日時が直近のファイルを取得
s3_import_path=$(aws s3api list-objects-v2 --bucket stg2-propetech-pg-dump-bucket --query "Contents[?contains(Key, '.dump.gz')].[Key, LastModified]" --output json | jq -r 'sort_by(.[1]) |last | .[0]')

# 最新がprodから連携されたファイルのみ処理を行う
if [[ "$s3_import_path" == */* ]]; then
        echo "Restore skipped"
        exit 0
fi

start_timestamp=$(TZ="Asia/Tokyo" date +"%Y%m%d_%H%M%S")

# pg_dump コマンドを実行し、念のためtruncate前の断面をバックアップする
echo "Backup started"
s3_backup_path="backup/${start_timestamp}.dump.gz"
pg_dump -h $db_host -U $db_user -d $db_name -F c -b -v --schema='app' | gzip | aws s3 cp - "${s3_bucket}/${s3_backup_path}"
echo "Backup completed and uploaded to ${s3_bucket}/${s3_backup_path}"

# PostgreSQLに接続して全テーブルをトランケート
echo "Truncating all tables in schema 'app' has started"
psql -U $db_user -h $db_host -d $db_name -c "
do \$\$
declare
    r record;
begin
    for r in
        select table_name from information_schema.tables
        where table_schema = 'app' and table_type = 'BASE TABLE'
    loop
        execute format('truncate table app.%I cascade;', r.table_name);
        raise notice '% has been truncated', r.table_name;
    end loop;
end \$\$;"
echo "Truncating all tables in schema 'app' has finished"

# S3からdumpを取得して復元
echo "Importing data from S3 startded"
aws s3 cp "${s3_bucket}/${s3_import_path}" - | gunzip | pg_restore -U $db_user -h $db_host -d $db_name --data-only -v
echo "Import complete."

# 取り込み後のテーブルの行数を確認とANALYZEの実行
psql -U $db_user -h $db_host -d $db_name -c "
do \$\$
declare
    r record;
    table_count integer;
begin
    raise notice 'Checking row counts for tables in schema ''app''';
    for r in
        select table_name from information_schema.tables
        where table_schema = 'app' and table_type = 'BASE TABLE'
        order by table_name
    loop
        -- 行数を確認
        execute format('select count(*) from app.%I', r.table_name) into table_count;
        raise notice '%: % rows', r.table_name, table_count;
        -- ANALYZEを実行
        execute format('analyze app.%I;', r.table_name);
    end loop;
end \$\$;"
