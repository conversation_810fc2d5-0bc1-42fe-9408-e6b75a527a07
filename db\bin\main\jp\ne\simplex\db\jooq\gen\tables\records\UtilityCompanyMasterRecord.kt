/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.UtilityCompanyMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.UtilityCompanyMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * ライフライン会社マスタ 既存システム物理名: YCALKP
 */
@Suppress("UNCHECKED_CAST")
open class UtilityCompanyMasterRecord private constructor() : TableRecordImpl<UtilityCompanyMasterRecord>(UtilityCompanyMasterTable.UTILITY_COMPANY_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updaterId: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var deleteFlag: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var utilityCategory: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var utilityCompanyCd: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var utilityCompanyFullName: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var utilityCompanyNameKana: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var utilityCompanyShortName: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    /**
     * Create a detached, initialised UtilityCompanyMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updaterId: String? = null, deleteFlag: Byte? = null, utilityCategory: String? = null, utilityCompanyCd: String? = null, utilityCompanyFullName: String? = null, utilityCompanyNameKana: String? = null, utilityCompanyShortName: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updaterId = updaterId
        this.deleteFlag = deleteFlag
        this.utilityCategory = utilityCategory
        this.utilityCompanyCd = utilityCompanyCd
        this.utilityCompanyFullName = utilityCompanyFullName
        this.utilityCompanyNameKana = utilityCompanyNameKana
        this.utilityCompanyShortName = utilityCompanyShortName
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised UtilityCompanyMasterRecord
     */
    constructor(value: UtilityCompanyMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updaterId = value.updaterId
            this.deleteFlag = value.deleteFlag
            this.utilityCategory = value.utilityCategory
            this.utilityCompanyCd = value.utilityCompanyCd
            this.utilityCompanyFullName = value.utilityCompanyFullName
            this.utilityCompanyNameKana = value.utilityCompanyNameKana
            this.utilityCompanyShortName = value.utilityCompanyShortName
            resetChangedOnNotNull()
        }
    }
}
