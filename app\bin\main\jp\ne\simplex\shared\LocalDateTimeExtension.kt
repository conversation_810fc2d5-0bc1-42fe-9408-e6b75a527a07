package jp.ne.simplex.shared

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class LocalDateTimeExtension {

    companion object {

        fun LocalDateTime.yyyyMMdd(): String {
            return this.format(DateTimeFormatter.ofPattern("yyyyMMdd"))
        }

        @Suppress("FunctionName")
        fun LocalDateTime.HHmmss(): String {
            return this.format(DateTimeFormatter.ofPattern("HHmmss"))
        }

        @Suppress("FunctionName")
        fun LocalDateTime.HHmm(): String {
            return this.format(DateTimeFormatter.ofPattern("HHmm"))
        }

        fun LocalDateTime.yyyyMMddHHmmss(): String {
            return this.format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))
        }

        fun LocalDateTime.yyyyMMddWithSlash(): String {
            return this.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"))
        }

        @Suppress("FunctionName")
        fun LocalDateTime.HHmmWithColon(): String {
            return this.format(DateTimeFormatter.ofPattern("HH:mm"))
        }
    }
}
