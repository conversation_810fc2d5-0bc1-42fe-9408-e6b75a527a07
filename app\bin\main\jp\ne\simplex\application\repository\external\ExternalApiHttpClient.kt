package jp.ne.simplex.application.repository.external

import jp.ne.simplex.application.repository.proxy.Proxy
import org.apache.hc.client5.http.classic.HttpClient
import org.apache.hc.client5.http.impl.classic.HttpClients
import org.apache.hc.core5.http.HttpHost
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile

@Configuration
class ExternalApiHttpClient(
    @Value("\${external.use-proxy}")
    private val useProxy: <PERSON>olean,
    private val proxy: Proxy?
) {

    @Bean
    @Profile("!dev")
    fun httpClient(): HttpClient? {
        if (proxy == null || !useProxy) {
            return null
        }
        return HttpClients.custom()
            .setProxy(HttpHost(proxy.host, proxy.port))
            .build()
    }
}
