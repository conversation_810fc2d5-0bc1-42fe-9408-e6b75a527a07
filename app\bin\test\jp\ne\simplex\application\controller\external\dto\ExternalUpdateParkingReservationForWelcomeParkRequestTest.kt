package jp.ne.simplex.application.controller.external.dto

import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import jp.ne.simplex.application.controller.external.parking.dto.ExternalUpdateParkingReservationForWelcomeParkRequest
import jp.ne.simplex.application.model.ExternalSystem
import jp.ne.simplex.application.model.ParkingReservation
import jp.ne.simplex.application.model.RegisterParkingReservation
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.mock.MockLocalDateTime
import jp.ne.simplex.stub.stubApiKeyAuthInfo
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

class ExternalUpdateParkingReservationForWelcomeParkRequestTest : FunSpec({

    val apiKeyAuthInfo = stubApiKeyAuthInfo(ExternalSystem.WELCOME_PARK)

    context("WelcomePark向けに外部公開している駐車場予約更新APIのリクエストを適切にデシリアライズできること") {

        val base = ExternalUpdateParkingReservationForWelcomeParkRequest(
            buildingCode = "000000001",
            parkingCode = "001",
            reservationStatus = 1,
            reservationType = 3,
            reserverName = null,
            reserverTel = null,
        )

        context("予約状態によって、適切なサービスのインスタンスを生成できること") {

            test("予約状態が1(受付)のとき駐車場予約(新規登録)のドメインに変換できること(~7:30)") {
                MockLocalDateTime.setNow(LocalDateTime.of(2025, 1, 1, 7, 29, 59))
                val request = base.copy(
                    reserverName = "simplex予約",
                    reserverTel = "090-1234-5678",
                )

                val actual = request.toServiceInterface(apiKeyAuthInfo)
                val now = LocalDate.now()

                actual.shouldBeInstanceOf<RegisterParkingReservation>()
                actual.parkingLotId.buildingCode.value.shouldBe(request.buildingCode)
                actual.parkingLotId.parkingLotCode.value.shouldBe(request.parkingCode)
                actual.parkingReservationStatus.value.shouldBe(request.reservationStatus.toString())
                actual.reservationType.value.shouldBe(ParkingReservation.Type.ONE_DAY.value)
                actual.reserveStartDatetime.shouldBe(now.plusDays(-1).atTime(LocalTime.of(8, 0, 0)))
                actual.reserveEndDatetime.shouldBe(now.atTime(LocalTime.of(7, 59, 59)))
                actual.reserverName.shouldBe(request.reserverName)
                actual.reserverTel!!.value.shouldBe(request.reserverTel)
                actual.requestSource.value.shouldBe(ParkingReservation.RequestSource.WELCOME_PARK.value)
                actual.linkedBuildingCode.shouldBeNull()
                actual.linkedRoomCode.shouldBeNull()

                MockLocalDateTime.close()
            }

            test("予約状態が1(受付)のとき駐車場予約(新規登録)のドメインに変換できること(7:30~)") {
                MockLocalDateTime.setNow(LocalDateTime.of(2025, 1, 1, 7, 30, 0))
                val request = base.copy(
                    reserverName = "simplex予約",
                    reserverTel = "090-1234-5678",
                )

                val actual = request.toServiceInterface(apiKeyAuthInfo)
                val now = LocalDate.now()

                actual.shouldBeInstanceOf<RegisterParkingReservation>()
                actual.parkingLotId.buildingCode.value.shouldBe(request.buildingCode)
                actual.parkingLotId.parkingLotCode.value.shouldBe(request.parkingCode)
                actual.parkingReservationStatus.value.shouldBe(request.reservationStatus.toString())
                actual.reservationType.value.shouldBe(ParkingReservation.Type.ONE_DAY.value)
                actual.reserveStartDatetime.shouldBe(now.atTime(LocalTime.of(8, 0, 0)))
                actual.reserveEndDatetime.shouldBe(now.plusDays(1).atTime(LocalTime.of(7, 59, 59)))
                actual.reserverName.shouldBe(request.reserverName)
                actual.reserverTel!!.value.shouldBe(request.reserverTel)
                actual.requestSource.value.shouldBe(ParkingReservation.RequestSource.WELCOME_PARK.value)
                actual.linkedBuildingCode.shouldBeNull()
                actual.linkedRoomCode.shouldBeNull()

                MockLocalDateTime.close()
            }

            test("予約状態が3(取消)のときバリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reservationStatus = 3,
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(ErrorMessage.PARKING_RESERVATION_INVALID_STATUS.format().message)
            }

            test("予約状態が0(仮申込)のときバリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reservationStatus = 0,
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(ErrorMessage.PARKING_RESERVATION_INVALID_STATUS.format().message)

            }

            test("予約状態が2(完了)のときバリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reservationStatus = 2,
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(ErrorMessage.PARKING_RESERVATION_INVALID_STATUS.format().message)
            }
        }

        context("予約種別は1日利用のみ受け付けること") {

            test("予約種別が1日利用のときサービスのドメインに変換できること") {

                shouldNotThrow<ClientValidationException> {
                    base.copy(
                        reservationType = 3,
                        reserverName = "simplex予約",
                        reserverTel = "090-1234-5678",
                    ).toServiceInterface(apiKeyAuthInfo)
                }
            }

            test("予約種別が0(申込)のときバリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reservationType = 0,
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(ErrorMessage.PARKING_RESERVATION_INVALID_TYPE.format().message)
            }

            test("予約種別が1(作業)のときバリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reservationType = 1,
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(ErrorMessage.PARKING_RESERVATION_INVALID_TYPE.format().message)
            }

            test("予約種別が2(場所変更)のときバリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reservationType = 2,
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(ErrorMessage.PARKING_RESERVATION_INVALID_TYPE.format().message)
            }
        }

        context("リクエスト受信時刻により前日/当日の予約状況の確認が切り替えられること") {
            val request = base.copy(
                reserverName = "simplex予約",
                reserverTel = "090-1234-5678",
            )
            test("リクエスト受信時刻が7:30より前の場合、前日の予約状況の確認リクエストに変換できること") {

                MockLocalDateTime.setNow(LocalDateTime.of(2025, 3, 24, 7, 29, 59))

                val actual = request.toServiceInterface(apiKeyAuthInfo)
                actual.shouldBeInstanceOf<RegisterParkingReservation>()
                actual.reserveStartDatetime.shouldBe(LocalDateTime.of(2025, 3, 23, 8, 0, 0))
                actual.reserveEndDatetime.shouldBe(LocalDateTime.of(2025, 3, 24, 7, 59, 59))

                MockLocalDateTime.close()
            }

            test("リクエスト受信時刻が7:30以降の場合、当日の予約状況の確認リクエストに変換できること") {
                MockLocalDateTime.setNow(LocalDateTime.of(2025, 3, 24, 7, 30, 0))
                val actual = request.toServiceInterface(apiKeyAuthInfo)

                actual.shouldBeInstanceOf<RegisterParkingReservation>()
                actual.reserveStartDatetime.shouldBe(LocalDateTime.of(2025, 3, 24, 8, 0, 0))
                actual.reserveEndDatetime.shouldBe(LocalDateTime.of(2025, 3, 25, 7, 59, 59))

                MockLocalDateTime.close()
            }

        }
    }
})
