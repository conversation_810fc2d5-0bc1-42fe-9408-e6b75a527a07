package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.repository.db.PropertyRepositoryInterface
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ServerValidationException
import org.springframework.stereotype.Service

@Service
class PropertyService(
    private val repository: PropertyRepositoryInterface,
) {

    fun get(propertyId: Property.Id): Property {
        return repository.list(listOf(propertyId)).firstOrNull()
            ?: throw ServerValidationException(
                ErrorMessage.PROPERTY_NOT_FOUND.format(
                    propertyId.buildingCode.value,
                    propertyId.roomCode.value
                )
            )
    }
}
