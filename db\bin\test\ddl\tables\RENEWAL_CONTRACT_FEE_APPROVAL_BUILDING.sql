-- TABLE: REN<PERSON>WAL_CONTRACT_FEE_APPROVAL_BUILDING(更新契約手数料承諾建物)

CREATE TABLE RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    CREATION_PROGRAM_ID                          varchar(10)                   
,    CREATION_TERMINAL_ID                         varchar(10)                   
,    CREATION_RESPONSIBLE_ID                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATE_TERMINAL_ID                           varchar(10)                   
,    UPDATE_RESPONSIBLE_ID                        varchar(6)                    
,    DELETE_FLAG                                  numeric(1)                    
,    BUILDING_CD                                  varchar(9)                    
,    APPROVAL_DATE                                numeric(8)                    
,    STATUS                                       numeric(1)                    
,    APPROVAL_REJECTION_INPUT_PERSON              varchar(6)                    
,    <PERSON><PERSON><PERSON><PERSON><PERSON>_FEE_CATEGORY                         numeric(3)                    
,    R<PERSON><PERSON>WAL_FEE_CATEGORY_REGISTRATION_DATE       numeric(8)                    
,    RENEWAL_FEE_CATEGORY_INPUT_PERSON            varchar(6)                    
,    CONSTRAINT UQ_RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING UNIQUE (BUILDING_CD, DELETE_FLAG)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING IS '更新契約手数料承諾建物 既存システム物理名: FVI90P';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.CREATION_DATE IS '作成年月日 既存システム物理名: FVI01D @290';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.CREATION_TIME IS '作成時刻 既存システム物理名: FVI02H @290';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.CREATION_PROGRAM_ID IS '作成プログラムID 既存システム物理名: FVI03M @290';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.CREATION_TERMINAL_ID IS '作成端末ID 既存システム物理名: FVI04M';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.CREATION_RESPONSIBLE_ID IS '作成担当者ID 既存システム物理名: FVI05C';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.UPDATE_DATE IS '更新年月日 既存システム物理名: FVI06D';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.UPDATE_TIME IS '更新時刻 既存システム物理名: FVI07H';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: FVI08M';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.UPDATE_TERMINAL_ID IS '更新端末ID 既存システム物理名: FVI09M';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.UPDATE_RESPONSIBLE_ID IS '更新担当者ID 既存システム物理名: FVI10C';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.DELETE_FLAG IS '削除フラグ 既存システム物理名: FVI11B';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.BUILDING_CD IS '建物CD 既存システム物理名: FVI12C';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.APPROVAL_DATE IS '承諾日 既存システム物理名: FVI13D';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.STATUS IS 'ステータス 既存システム物理名: FVI14B';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.APPROVAL_REJECTION_INPUT_PERSON IS '承諾・拒否等入力者 既存システム物理名: FVI15C';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.RENEWAL_FEE_CATEGORY IS '更新手数料区分 既存システム物理名: FVI16C';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.RENEWAL_FEE_CATEGORY_REGISTRATION_DATE IS '更新手数料区分 登録日 既存システム物理名: FVI17D';
COMMENT ON COLUMN RENEWAL_CONTRACT_FEE_APPROVAL_BUILDING.RENEWAL_FEE_CATEGORY_INPUT_PERSON IS '更新手数料区分 入力者 既存システム物理名: FVI18C';
