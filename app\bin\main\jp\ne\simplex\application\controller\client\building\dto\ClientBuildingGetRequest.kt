package jp.ne.simplex.application.controller.client.building.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Building
import org.springdoc.core.annotations.ParameterObject

@ParameterObject
@Schema(implementation = ClientBuildingGetRequest::class)
data class ClientBuildingGetRequest(
    @JsonProperty("buildingCode")
    @field:Schema(description = "建物コード")
    val buildingCode: String,
) {

    fun toServiceInterface(): Building.Code {
        return Building.Code.of(buildingCode)
    }
}
