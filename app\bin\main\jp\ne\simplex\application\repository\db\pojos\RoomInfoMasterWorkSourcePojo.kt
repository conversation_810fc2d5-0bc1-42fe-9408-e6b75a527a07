package jp.ne.simplex.application.repository.db.pojos

import jp.ne.simplex.application.model.RoomInfoMasterWorkSource
import org.slf4j.LoggerFactory
import java.math.BigDecimal

data class RoomInfoMasterWorkSourcePojo(
    // From ROOM_MASTER
    val buildingCode: String,
    val roomCode: String,
    val roomNumber: String?,
    val initialOccupancyDate: String?,
    val layoutDetails: String?,
    val privateGardenAvailabilitySign: Int?,
    val frontFreeRentSign: Int?,
    val roomType: String?,
    val storeExclusiveAreaSquareMeters: BigDecimal?,
    val officeFloorAreaSquareMeters: BigDecimal?,

    // From BUILDING_MASTER
    val buildingName: String?,
    val prefectureCode: String?,
    val cityCode: String?,
    val townCode: String?,
    val addressDetails: String?,
    val tenantRecruitmentBranchCode: String?,
    val completionDeliveryDate: Int?,
    val completionExpectedDate: Int?,

    // From TENANT_CONTRACT
    val moveOutDate: String?,
    val moveInApplicationDate: String?,
    val moveInScheduledDate: String?,
    val vacateNoticeDate: String?,
    val moveInStartProcessedSign: String?,
    val depositChangeDate: Int?,
    val leaseContractDate: Int?,
    val remainingDate: Int?,
    val taxDivision: String?,

    // From EMPLOYEE_MASTER
    val nameKanji: String?,

    // From LATEST_RENT_EVALUATION
    val recruitmentStartDate: Int?,
    val brokerApplicationFormNumber: String?,
    val brokerApplicationCollectionDate: Int?,
    val brokerApplicationCollectionDivision: String?,
    val rent: Int?,
    val commonFee: Int?,
    val keyMoneyAmount: Int?,
    val depositAmount: Int?,
    val neighborhoodAssociationFee: Int?,

    // From BUILDING_TYPE_MASTER
    val usageCategory: String?,
    val buildingTypeAbbreviation2: String?,

    // From CUSTOMER
    val clientNameKanji: String?,

    // From ADDRESS_MASTER
    val townKanjiName: String?,
    val townKanaName: String?,
) {
    companion object {
        private val log = LoggerFactory.getLogger(RoomInfoMasterWorkSourcePojo::class.java)
    }

    fun getRoomInfoMasterWorkSource(): RoomInfoMasterWorkSource? {
        return try {
            RoomInfoMasterWorkSource.of(
                buildingCode,
                roomCode,
                roomNumber,
                initialOccupancyDateStr = initialOccupancyDate,
                layoutDetails,
                privateGardenAvailabilitySign,
                frontFreeRentSign,
                roomType,
                storeExclusiveAreaSquareMeters,
                officeFloorAreaSquareMeters,
                buildingName,
                prefectureCode,
                cityCode,
                townCode,
                addressDetails,
                tenantRecruitmentBranchCode,
                completionDeliveryDateInt = completionDeliveryDate,
                completionExpectedDateInt = completionExpectedDate,
                moveOutDateStr = moveOutDate,
                moveInApplicationDateStr = moveInApplicationDate,
                moveInScheduledDateStr = moveInScheduledDate,
                vacateNoticeDateStr = vacateNoticeDate,
                moveInStartProcessedSign,
                depositChangeDateInt = depositChangeDate,
                leaseContractDateInt = leaseContractDate,
                remainingDateInt = remainingDate,
                taxDivision,
                negotiationEmployeeName = nameKanji,
                recruitmentStartDateInt = recruitmentStartDate,
                brokerApplicationFormNumber,
                brokerApplicationCollectionDateInt = brokerApplicationCollectionDate,
                brokerApplicationCollectionDivision,
                rent,
                commonFee,
                keyMoneyAmount,
                depositAmount,
                neighborhoodAssociationFee,
                usageCategory,
                buildingTypeAbbreviation2,
                landlordName = clientNameKanji,
                townKanjiName,
                townKanaName,
            )
        } catch (e: Exception) {
            // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
            log.warn("Failed to deserialize RoomInfoMasterWorkSource record. $this", e)
            return null
        }
    }
}
