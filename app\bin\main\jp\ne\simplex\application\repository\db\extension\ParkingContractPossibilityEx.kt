package jp.ne.simplex.application.repository.db.extension

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingContractPossibility
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingContractPossibilityPojo

class ParkingContractPossibilityEx {
    companion object {
        fun ParkingContractPossibilityPojo.toParkingContractPossibility():
                ParkingContractPossibility {
            return ParkingContractPossibility(
                orderCode = Building.OrderCode.of(this.orderCode),
                firstParkingContractPossibility = ParkingContractPossibility.ContractPossibility
                    .fromValue(this.isFirstParkingContractPossible),
                secondParkingContractPossibility = ParkingContractPossibility.ContractPossibility
                    .fromValue(this.isSecondParkingContractPossible),
                isAutoJudge = ParkingContractPossibility.ContractPossibilityAutoJudge
                    .fromValue(this.isAutoJudge),
            )
        }
    }
}
