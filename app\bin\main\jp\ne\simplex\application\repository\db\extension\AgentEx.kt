package jp.ne.simplex.application.repository.db.extension

import jp.ne.simplex.application.model.Agent
import jp.ne.simplex.db.jooq.gen.tables.pojos.AgentPojo
import org.slf4j.LoggerFactory

class AgentEx {

    companion object {

        private val log = LoggerFactory.getLogger(AgentEx::class.java)

        fun AgentPojo.getAgent(): Agent? {
            return try {
                Agent(
                    eCode = Agent.ECode.of(this.chukaiGyoshaCd),
                )
            } catch (_: Exception) {
                // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
                log.warn("Failed to deserialize Agent record. $this")
                null
            }
        }
    }
}
