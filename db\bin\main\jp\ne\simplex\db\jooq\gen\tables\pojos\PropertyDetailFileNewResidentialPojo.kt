/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable
import java.math.BigDecimal


/**
 * 物件明細ファイル(新築居住用)商品管理Web 既存システム物理名: EMUU3P
 */
@Suppress("UNCHECKED_CAST")
data class PropertyDetailFileNewResidentialPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updater: String? = null,
    var logicalDeleteSign: Byte? = null,
    var occurrenceMonthCategory: String? = null,
    var occurrenceMonth: Int? = null,
    var salesDepartmentCd: String? = null,
    var salesDepartmentName: String? = null,
    var salesDepartmentOutputOrderCd: String? = null,
    var branchCd: String? = null,
    var branchName: String? = null,
    var branchOutputOrderCd: String? = null,
    var processDate: Int? = null,
    var statusName: String? = null,
    var customerFlag: Byte? = null,
    var customerRepCd: String? = null,
    var customerRepName: String? = null,
    var occupancyFlag: Byte? = null,
    var buildingCode: String? = null,
    var buildingName: String? = null,
    var addressCd: String? = null,
    var location: String? = null,
    var expectedCompletionDate: Int? = null,
    var completionDate: Int? = null,
    var roomCode: String? = null,
    var roomNumber: String? = null,
    var landlordCd: String? = null,
    var landlordName: String? = null,
    var bulkLeasingSign: Byte? = null,
    var contractTypeName: String? = null,
    var roomTypeCd: String? = null,
    var roomTypeName: String? = null,
    var layoutCategory: String? = null,
    var layoutName: String? = null,
    var exclusiveArea: BigDecimal? = null,
    var tenantContractNumber: String? = null,
    var currentStatusCategory: String? = null,
    var modifiedStatusCategory: String? = null,
    var vacationNoticeDate: Int? = null,
    var reviewApprovalDate: Int? = null,
    var reviewApprovalDaysElapsed: Int? = null,
    var arrangementOutputDate: Int? = null,
    var arrangementApprovalDate: Int? = null,
    var arrangementCollectionDate: Int? = null,
    var arrangementCollectionDaysElapsed: Int? = null,
    var moveOutMeetingDate: Int? = null,
    var expectedMoveOutDate: Int? = null,
    var moveOutDate: Int? = null,
    var moveOutRent: Int? = null,
    var reviewRent: Int? = null,
    var currentRent: Int? = null,
    var differenceReviewMoveOut: Int? = null,
    var differenceCurrentMoveOut: Int? = null,
    var differenceCurrentReview: Int? = null,
    var discrepancyStatus: String? = null,
    var longestVacancyPeriodHistory: Int? = null,
    var residentialRoomCount: Short? = null,
    var residentialRoomCountVacant: Short? = null,
    var restorationCompletionExpectedDate: Int? = null,
    var restorationCompletionDate: Int? = null,
    var vacancyAccountingExpectedDate: Int? = null,
    var vacancyAccountingDate: Int? = null,
    var vacancyMonths: Short? = null,
    var vacancyPeriod: Int? = null,
    var occupancyApplicationDate: Int? = null,
    var contractDate: Int? = null,
    var remainingCollectionExpectedDate: Int? = null,
    var remainingCollectionDate: Int? = null,
    var expectedOccupancyDate: Int? = null,
    var occupancyDate: Int? = null,
    var ad: Byte? = null,
    var ff: Byte? = null,
    var supportMechanismCategory: String? = null,
    var preferredRentalCategory: String? = null,
    var financingCategory: String? = null,
    var hlApprovalCategory: String? = null,
    var hl: String? = null,
    var rentDiscountFlag: Byte? = null,
    var rentDiscount: String? = null,
    var reformType: String? = null,
    var reform: Byte? = null,
    var threeMonthFf: Byte? = null,
    var recruitmentStatusCategory: String? = null,
    var reserveCategory_2: String? = null,
    var reserveCategory_3: String? = null,
    var reserveCategory_4: String? = null,
    var reserveCategory_5: String? = null,
    var reserveCategory_6: String? = null,
    var reserveCategory_7: String? = null,
    var reserveCategory_8: String? = null,
    var reserveCategory_9: String? = null,
    var reserveCategory_10: String? = null,
    var constructionRepCd: String? = null,
    var constructionRepName: String? = null,
    var implementationRepCd: String? = null,
    var implementationRepName: String? = null,
    var adActualAmount: Int? = null,
    var ffActualAmount: Int? = null,
    var municipalityCd: String? = null,
    var municipalityName: String? = null,
    var businessRoomCountVacant: Short? = null,
    var longestVacancyPeriodBusiness: Int? = null,
    var businessRoomCount: Short? = null,
    var statusCode: String? = null,
    var exclusionFlag: Byte? = null,
    var collectionRepCd: String? = null,
    var collectionRepName: String? = null,
    var abcCategory: String? = null,
    var moveOutMeetingTime: Short? = null,
    var managementRepCd: String? = null,
    var managementRepName: String? = null,
    var supplyPlanAreaCode: String? = null,
    var supplyPlanAreaName: String? = null,
    var assessmentArea: String? = null,
    var storeSalesDepartmentCd: String? = null,
    var storeSalesDepartmentName: String? = null,
    var storeSalesDepartmentOutputOrderCd: String? = null,
    var storeCd: String? = null,
    var storeName: String? = null,
    var storeOutputOrderCd: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: PropertyDetailFileNewResidentialPojo = other as PropertyDetailFileNewResidentialPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.logicalDeleteSign == null) {
            if (o.logicalDeleteSign != null)
                return false
        }
        else if (this.logicalDeleteSign != o.logicalDeleteSign)
            return false
        if (this.occurrenceMonthCategory == null) {
            if (o.occurrenceMonthCategory != null)
                return false
        }
        else if (this.occurrenceMonthCategory != o.occurrenceMonthCategory)
            return false
        if (this.occurrenceMonth == null) {
            if (o.occurrenceMonth != null)
                return false
        }
        else if (this.occurrenceMonth != o.occurrenceMonth)
            return false
        if (this.salesDepartmentCd == null) {
            if (o.salesDepartmentCd != null)
                return false
        }
        else if (this.salesDepartmentCd != o.salesDepartmentCd)
            return false
        if (this.salesDepartmentName == null) {
            if (o.salesDepartmentName != null)
                return false
        }
        else if (this.salesDepartmentName != o.salesDepartmentName)
            return false
        if (this.salesDepartmentOutputOrderCd == null) {
            if (o.salesDepartmentOutputOrderCd != null)
                return false
        }
        else if (this.salesDepartmentOutputOrderCd != o.salesDepartmentOutputOrderCd)
            return false
        if (this.branchCd == null) {
            if (o.branchCd != null)
                return false
        }
        else if (this.branchCd != o.branchCd)
            return false
        if (this.branchName == null) {
            if (o.branchName != null)
                return false
        }
        else if (this.branchName != o.branchName)
            return false
        if (this.branchOutputOrderCd == null) {
            if (o.branchOutputOrderCd != null)
                return false
        }
        else if (this.branchOutputOrderCd != o.branchOutputOrderCd)
            return false
        if (this.processDate == null) {
            if (o.processDate != null)
                return false
        }
        else if (this.processDate != o.processDate)
            return false
        if (this.statusName == null) {
            if (o.statusName != null)
                return false
        }
        else if (this.statusName != o.statusName)
            return false
        if (this.customerFlag == null) {
            if (o.customerFlag != null)
                return false
        }
        else if (this.customerFlag != o.customerFlag)
            return false
        if (this.customerRepCd == null) {
            if (o.customerRepCd != null)
                return false
        }
        else if (this.customerRepCd != o.customerRepCd)
            return false
        if (this.customerRepName == null) {
            if (o.customerRepName != null)
                return false
        }
        else if (this.customerRepName != o.customerRepName)
            return false
        if (this.occupancyFlag == null) {
            if (o.occupancyFlag != null)
                return false
        }
        else if (this.occupancyFlag != o.occupancyFlag)
            return false
        if (this.buildingCode == null) {
            if (o.buildingCode != null)
                return false
        }
        else if (this.buildingCode != o.buildingCode)
            return false
        if (this.buildingName == null) {
            if (o.buildingName != null)
                return false
        }
        else if (this.buildingName != o.buildingName)
            return false
        if (this.addressCd == null) {
            if (o.addressCd != null)
                return false
        }
        else if (this.addressCd != o.addressCd)
            return false
        if (this.location == null) {
            if (o.location != null)
                return false
        }
        else if (this.location != o.location)
            return false
        if (this.expectedCompletionDate == null) {
            if (o.expectedCompletionDate != null)
                return false
        }
        else if (this.expectedCompletionDate != o.expectedCompletionDate)
            return false
        if (this.completionDate == null) {
            if (o.completionDate != null)
                return false
        }
        else if (this.completionDate != o.completionDate)
            return false
        if (this.roomCode == null) {
            if (o.roomCode != null)
                return false
        }
        else if (this.roomCode != o.roomCode)
            return false
        if (this.roomNumber == null) {
            if (o.roomNumber != null)
                return false
        }
        else if (this.roomNumber != o.roomNumber)
            return false
        if (this.landlordCd == null) {
            if (o.landlordCd != null)
                return false
        }
        else if (this.landlordCd != o.landlordCd)
            return false
        if (this.landlordName == null) {
            if (o.landlordName != null)
                return false
        }
        else if (this.landlordName != o.landlordName)
            return false
        if (this.bulkLeasingSign == null) {
            if (o.bulkLeasingSign != null)
                return false
        }
        else if (this.bulkLeasingSign != o.bulkLeasingSign)
            return false
        if (this.contractTypeName == null) {
            if (o.contractTypeName != null)
                return false
        }
        else if (this.contractTypeName != o.contractTypeName)
            return false
        if (this.roomTypeCd == null) {
            if (o.roomTypeCd != null)
                return false
        }
        else if (this.roomTypeCd != o.roomTypeCd)
            return false
        if (this.roomTypeName == null) {
            if (o.roomTypeName != null)
                return false
        }
        else if (this.roomTypeName != o.roomTypeName)
            return false
        if (this.layoutCategory == null) {
            if (o.layoutCategory != null)
                return false
        }
        else if (this.layoutCategory != o.layoutCategory)
            return false
        if (this.layoutName == null) {
            if (o.layoutName != null)
                return false
        }
        else if (this.layoutName != o.layoutName)
            return false
        if (this.exclusiveArea == null) {
            if (o.exclusiveArea != null)
                return false
        }
        else if (this.exclusiveArea != o.exclusiveArea)
            return false
        if (this.tenantContractNumber == null) {
            if (o.tenantContractNumber != null)
                return false
        }
        else if (this.tenantContractNumber != o.tenantContractNumber)
            return false
        if (this.currentStatusCategory == null) {
            if (o.currentStatusCategory != null)
                return false
        }
        else if (this.currentStatusCategory != o.currentStatusCategory)
            return false
        if (this.modifiedStatusCategory == null) {
            if (o.modifiedStatusCategory != null)
                return false
        }
        else if (this.modifiedStatusCategory != o.modifiedStatusCategory)
            return false
        if (this.vacationNoticeDate == null) {
            if (o.vacationNoticeDate != null)
                return false
        }
        else if (this.vacationNoticeDate != o.vacationNoticeDate)
            return false
        if (this.reviewApprovalDate == null) {
            if (o.reviewApprovalDate != null)
                return false
        }
        else if (this.reviewApprovalDate != o.reviewApprovalDate)
            return false
        if (this.reviewApprovalDaysElapsed == null) {
            if (o.reviewApprovalDaysElapsed != null)
                return false
        }
        else if (this.reviewApprovalDaysElapsed != o.reviewApprovalDaysElapsed)
            return false
        if (this.arrangementOutputDate == null) {
            if (o.arrangementOutputDate != null)
                return false
        }
        else if (this.arrangementOutputDate != o.arrangementOutputDate)
            return false
        if (this.arrangementApprovalDate == null) {
            if (o.arrangementApprovalDate != null)
                return false
        }
        else if (this.arrangementApprovalDate != o.arrangementApprovalDate)
            return false
        if (this.arrangementCollectionDate == null) {
            if (o.arrangementCollectionDate != null)
                return false
        }
        else if (this.arrangementCollectionDate != o.arrangementCollectionDate)
            return false
        if (this.arrangementCollectionDaysElapsed == null) {
            if (o.arrangementCollectionDaysElapsed != null)
                return false
        }
        else if (this.arrangementCollectionDaysElapsed != o.arrangementCollectionDaysElapsed)
            return false
        if (this.moveOutMeetingDate == null) {
            if (o.moveOutMeetingDate != null)
                return false
        }
        else if (this.moveOutMeetingDate != o.moveOutMeetingDate)
            return false
        if (this.expectedMoveOutDate == null) {
            if (o.expectedMoveOutDate != null)
                return false
        }
        else if (this.expectedMoveOutDate != o.expectedMoveOutDate)
            return false
        if (this.moveOutDate == null) {
            if (o.moveOutDate != null)
                return false
        }
        else if (this.moveOutDate != o.moveOutDate)
            return false
        if (this.moveOutRent == null) {
            if (o.moveOutRent != null)
                return false
        }
        else if (this.moveOutRent != o.moveOutRent)
            return false
        if (this.reviewRent == null) {
            if (o.reviewRent != null)
                return false
        }
        else if (this.reviewRent != o.reviewRent)
            return false
        if (this.currentRent == null) {
            if (o.currentRent != null)
                return false
        }
        else if (this.currentRent != o.currentRent)
            return false
        if (this.differenceReviewMoveOut == null) {
            if (o.differenceReviewMoveOut != null)
                return false
        }
        else if (this.differenceReviewMoveOut != o.differenceReviewMoveOut)
            return false
        if (this.differenceCurrentMoveOut == null) {
            if (o.differenceCurrentMoveOut != null)
                return false
        }
        else if (this.differenceCurrentMoveOut != o.differenceCurrentMoveOut)
            return false
        if (this.differenceCurrentReview == null) {
            if (o.differenceCurrentReview != null)
                return false
        }
        else if (this.differenceCurrentReview != o.differenceCurrentReview)
            return false
        if (this.discrepancyStatus == null) {
            if (o.discrepancyStatus != null)
                return false
        }
        else if (this.discrepancyStatus != o.discrepancyStatus)
            return false
        if (this.longestVacancyPeriodHistory == null) {
            if (o.longestVacancyPeriodHistory != null)
                return false
        }
        else if (this.longestVacancyPeriodHistory != o.longestVacancyPeriodHistory)
            return false
        if (this.residentialRoomCount == null) {
            if (o.residentialRoomCount != null)
                return false
        }
        else if (this.residentialRoomCount != o.residentialRoomCount)
            return false
        if (this.residentialRoomCountVacant == null) {
            if (o.residentialRoomCountVacant != null)
                return false
        }
        else if (this.residentialRoomCountVacant != o.residentialRoomCountVacant)
            return false
        if (this.restorationCompletionExpectedDate == null) {
            if (o.restorationCompletionExpectedDate != null)
                return false
        }
        else if (this.restorationCompletionExpectedDate != o.restorationCompletionExpectedDate)
            return false
        if (this.restorationCompletionDate == null) {
            if (o.restorationCompletionDate != null)
                return false
        }
        else if (this.restorationCompletionDate != o.restorationCompletionDate)
            return false
        if (this.vacancyAccountingExpectedDate == null) {
            if (o.vacancyAccountingExpectedDate != null)
                return false
        }
        else if (this.vacancyAccountingExpectedDate != o.vacancyAccountingExpectedDate)
            return false
        if (this.vacancyAccountingDate == null) {
            if (o.vacancyAccountingDate != null)
                return false
        }
        else if (this.vacancyAccountingDate != o.vacancyAccountingDate)
            return false
        if (this.vacancyMonths == null) {
            if (o.vacancyMonths != null)
                return false
        }
        else if (this.vacancyMonths != o.vacancyMonths)
            return false
        if (this.vacancyPeriod == null) {
            if (o.vacancyPeriod != null)
                return false
        }
        else if (this.vacancyPeriod != o.vacancyPeriod)
            return false
        if (this.occupancyApplicationDate == null) {
            if (o.occupancyApplicationDate != null)
                return false
        }
        else if (this.occupancyApplicationDate != o.occupancyApplicationDate)
            return false
        if (this.contractDate == null) {
            if (o.contractDate != null)
                return false
        }
        else if (this.contractDate != o.contractDate)
            return false
        if (this.remainingCollectionExpectedDate == null) {
            if (o.remainingCollectionExpectedDate != null)
                return false
        }
        else if (this.remainingCollectionExpectedDate != o.remainingCollectionExpectedDate)
            return false
        if (this.remainingCollectionDate == null) {
            if (o.remainingCollectionDate != null)
                return false
        }
        else if (this.remainingCollectionDate != o.remainingCollectionDate)
            return false
        if (this.expectedOccupancyDate == null) {
            if (o.expectedOccupancyDate != null)
                return false
        }
        else if (this.expectedOccupancyDate != o.expectedOccupancyDate)
            return false
        if (this.occupancyDate == null) {
            if (o.occupancyDate != null)
                return false
        }
        else if (this.occupancyDate != o.occupancyDate)
            return false
        if (this.ad == null) {
            if (o.ad != null)
                return false
        }
        else if (this.ad != o.ad)
            return false
        if (this.ff == null) {
            if (o.ff != null)
                return false
        }
        else if (this.ff != o.ff)
            return false
        if (this.supportMechanismCategory == null) {
            if (o.supportMechanismCategory != null)
                return false
        }
        else if (this.supportMechanismCategory != o.supportMechanismCategory)
            return false
        if (this.preferredRentalCategory == null) {
            if (o.preferredRentalCategory != null)
                return false
        }
        else if (this.preferredRentalCategory != o.preferredRentalCategory)
            return false
        if (this.financingCategory == null) {
            if (o.financingCategory != null)
                return false
        }
        else if (this.financingCategory != o.financingCategory)
            return false
        if (this.hlApprovalCategory == null) {
            if (o.hlApprovalCategory != null)
                return false
        }
        else if (this.hlApprovalCategory != o.hlApprovalCategory)
            return false
        if (this.hl == null) {
            if (o.hl != null)
                return false
        }
        else if (this.hl != o.hl)
            return false
        if (this.rentDiscountFlag == null) {
            if (o.rentDiscountFlag != null)
                return false
        }
        else if (this.rentDiscountFlag != o.rentDiscountFlag)
            return false
        if (this.rentDiscount == null) {
            if (o.rentDiscount != null)
                return false
        }
        else if (this.rentDiscount != o.rentDiscount)
            return false
        if (this.reformType == null) {
            if (o.reformType != null)
                return false
        }
        else if (this.reformType != o.reformType)
            return false
        if (this.reform == null) {
            if (o.reform != null)
                return false
        }
        else if (this.reform != o.reform)
            return false
        if (this.threeMonthFf == null) {
            if (o.threeMonthFf != null)
                return false
        }
        else if (this.threeMonthFf != o.threeMonthFf)
            return false
        if (this.recruitmentStatusCategory == null) {
            if (o.recruitmentStatusCategory != null)
                return false
        }
        else if (this.recruitmentStatusCategory != o.recruitmentStatusCategory)
            return false
        if (this.reserveCategory_2 == null) {
            if (o.reserveCategory_2 != null)
                return false
        }
        else if (this.reserveCategory_2 != o.reserveCategory_2)
            return false
        if (this.reserveCategory_3 == null) {
            if (o.reserveCategory_3 != null)
                return false
        }
        else if (this.reserveCategory_3 != o.reserveCategory_3)
            return false
        if (this.reserveCategory_4 == null) {
            if (o.reserveCategory_4 != null)
                return false
        }
        else if (this.reserveCategory_4 != o.reserveCategory_4)
            return false
        if (this.reserveCategory_5 == null) {
            if (o.reserveCategory_5 != null)
                return false
        }
        else if (this.reserveCategory_5 != o.reserveCategory_5)
            return false
        if (this.reserveCategory_6 == null) {
            if (o.reserveCategory_6 != null)
                return false
        }
        else if (this.reserveCategory_6 != o.reserveCategory_6)
            return false
        if (this.reserveCategory_7 == null) {
            if (o.reserveCategory_7 != null)
                return false
        }
        else if (this.reserveCategory_7 != o.reserveCategory_7)
            return false
        if (this.reserveCategory_8 == null) {
            if (o.reserveCategory_8 != null)
                return false
        }
        else if (this.reserveCategory_8 != o.reserveCategory_8)
            return false
        if (this.reserveCategory_9 == null) {
            if (o.reserveCategory_9 != null)
                return false
        }
        else if (this.reserveCategory_9 != o.reserveCategory_9)
            return false
        if (this.reserveCategory_10 == null) {
            if (o.reserveCategory_10 != null)
                return false
        }
        else if (this.reserveCategory_10 != o.reserveCategory_10)
            return false
        if (this.constructionRepCd == null) {
            if (o.constructionRepCd != null)
                return false
        }
        else if (this.constructionRepCd != o.constructionRepCd)
            return false
        if (this.constructionRepName == null) {
            if (o.constructionRepName != null)
                return false
        }
        else if (this.constructionRepName != o.constructionRepName)
            return false
        if (this.implementationRepCd == null) {
            if (o.implementationRepCd != null)
                return false
        }
        else if (this.implementationRepCd != o.implementationRepCd)
            return false
        if (this.implementationRepName == null) {
            if (o.implementationRepName != null)
                return false
        }
        else if (this.implementationRepName != o.implementationRepName)
            return false
        if (this.adActualAmount == null) {
            if (o.adActualAmount != null)
                return false
        }
        else if (this.adActualAmount != o.adActualAmount)
            return false
        if (this.ffActualAmount == null) {
            if (o.ffActualAmount != null)
                return false
        }
        else if (this.ffActualAmount != o.ffActualAmount)
            return false
        if (this.municipalityCd == null) {
            if (o.municipalityCd != null)
                return false
        }
        else if (this.municipalityCd != o.municipalityCd)
            return false
        if (this.municipalityName == null) {
            if (o.municipalityName != null)
                return false
        }
        else if (this.municipalityName != o.municipalityName)
            return false
        if (this.businessRoomCountVacant == null) {
            if (o.businessRoomCountVacant != null)
                return false
        }
        else if (this.businessRoomCountVacant != o.businessRoomCountVacant)
            return false
        if (this.longestVacancyPeriodBusiness == null) {
            if (o.longestVacancyPeriodBusiness != null)
                return false
        }
        else if (this.longestVacancyPeriodBusiness != o.longestVacancyPeriodBusiness)
            return false
        if (this.businessRoomCount == null) {
            if (o.businessRoomCount != null)
                return false
        }
        else if (this.businessRoomCount != o.businessRoomCount)
            return false
        if (this.statusCode == null) {
            if (o.statusCode != null)
                return false
        }
        else if (this.statusCode != o.statusCode)
            return false
        if (this.exclusionFlag == null) {
            if (o.exclusionFlag != null)
                return false
        }
        else if (this.exclusionFlag != o.exclusionFlag)
            return false
        if (this.collectionRepCd == null) {
            if (o.collectionRepCd != null)
                return false
        }
        else if (this.collectionRepCd != o.collectionRepCd)
            return false
        if (this.collectionRepName == null) {
            if (o.collectionRepName != null)
                return false
        }
        else if (this.collectionRepName != o.collectionRepName)
            return false
        if (this.abcCategory == null) {
            if (o.abcCategory != null)
                return false
        }
        else if (this.abcCategory != o.abcCategory)
            return false
        if (this.moveOutMeetingTime == null) {
            if (o.moveOutMeetingTime != null)
                return false
        }
        else if (this.moveOutMeetingTime != o.moveOutMeetingTime)
            return false
        if (this.managementRepCd == null) {
            if (o.managementRepCd != null)
                return false
        }
        else if (this.managementRepCd != o.managementRepCd)
            return false
        if (this.managementRepName == null) {
            if (o.managementRepName != null)
                return false
        }
        else if (this.managementRepName != o.managementRepName)
            return false
        if (this.supplyPlanAreaCode == null) {
            if (o.supplyPlanAreaCode != null)
                return false
        }
        else if (this.supplyPlanAreaCode != o.supplyPlanAreaCode)
            return false
        if (this.supplyPlanAreaName == null) {
            if (o.supplyPlanAreaName != null)
                return false
        }
        else if (this.supplyPlanAreaName != o.supplyPlanAreaName)
            return false
        if (this.assessmentArea == null) {
            if (o.assessmentArea != null)
                return false
        }
        else if (this.assessmentArea != o.assessmentArea)
            return false
        if (this.storeSalesDepartmentCd == null) {
            if (o.storeSalesDepartmentCd != null)
                return false
        }
        else if (this.storeSalesDepartmentCd != o.storeSalesDepartmentCd)
            return false
        if (this.storeSalesDepartmentName == null) {
            if (o.storeSalesDepartmentName != null)
                return false
        }
        else if (this.storeSalesDepartmentName != o.storeSalesDepartmentName)
            return false
        if (this.storeSalesDepartmentOutputOrderCd == null) {
            if (o.storeSalesDepartmentOutputOrderCd != null)
                return false
        }
        else if (this.storeSalesDepartmentOutputOrderCd != o.storeSalesDepartmentOutputOrderCd)
            return false
        if (this.storeCd == null) {
            if (o.storeCd != null)
                return false
        }
        else if (this.storeCd != o.storeCd)
            return false
        if (this.storeName == null) {
            if (o.storeName != null)
                return false
        }
        else if (this.storeName != o.storeName)
            return false
        if (this.storeOutputOrderCd == null) {
            if (o.storeOutputOrderCd != null)
                return false
        }
        else if (this.storeOutputOrderCd != o.storeOutputOrderCd)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.logicalDeleteSign == null) 0 else this.logicalDeleteSign.hashCode())
        result = prime * result + (if (this.occurrenceMonthCategory == null) 0 else this.occurrenceMonthCategory.hashCode())
        result = prime * result + (if (this.occurrenceMonth == null) 0 else this.occurrenceMonth.hashCode())
        result = prime * result + (if (this.salesDepartmentCd == null) 0 else this.salesDepartmentCd.hashCode())
        result = prime * result + (if (this.salesDepartmentName == null) 0 else this.salesDepartmentName.hashCode())
        result = prime * result + (if (this.salesDepartmentOutputOrderCd == null) 0 else this.salesDepartmentOutputOrderCd.hashCode())
        result = prime * result + (if (this.branchCd == null) 0 else this.branchCd.hashCode())
        result = prime * result + (if (this.branchName == null) 0 else this.branchName.hashCode())
        result = prime * result + (if (this.branchOutputOrderCd == null) 0 else this.branchOutputOrderCd.hashCode())
        result = prime * result + (if (this.processDate == null) 0 else this.processDate.hashCode())
        result = prime * result + (if (this.statusName == null) 0 else this.statusName.hashCode())
        result = prime * result + (if (this.customerFlag == null) 0 else this.customerFlag.hashCode())
        result = prime * result + (if (this.customerRepCd == null) 0 else this.customerRepCd.hashCode())
        result = prime * result + (if (this.customerRepName == null) 0 else this.customerRepName.hashCode())
        result = prime * result + (if (this.occupancyFlag == null) 0 else this.occupancyFlag.hashCode())
        result = prime * result + (if (this.buildingCode == null) 0 else this.buildingCode.hashCode())
        result = prime * result + (if (this.buildingName == null) 0 else this.buildingName.hashCode())
        result = prime * result + (if (this.addressCd == null) 0 else this.addressCd.hashCode())
        result = prime * result + (if (this.location == null) 0 else this.location.hashCode())
        result = prime * result + (if (this.expectedCompletionDate == null) 0 else this.expectedCompletionDate.hashCode())
        result = prime * result + (if (this.completionDate == null) 0 else this.completionDate.hashCode())
        result = prime * result + (if (this.roomCode == null) 0 else this.roomCode.hashCode())
        result = prime * result + (if (this.roomNumber == null) 0 else this.roomNumber.hashCode())
        result = prime * result + (if (this.landlordCd == null) 0 else this.landlordCd.hashCode())
        result = prime * result + (if (this.landlordName == null) 0 else this.landlordName.hashCode())
        result = prime * result + (if (this.bulkLeasingSign == null) 0 else this.bulkLeasingSign.hashCode())
        result = prime * result + (if (this.contractTypeName == null) 0 else this.contractTypeName.hashCode())
        result = prime * result + (if (this.roomTypeCd == null) 0 else this.roomTypeCd.hashCode())
        result = prime * result + (if (this.roomTypeName == null) 0 else this.roomTypeName.hashCode())
        result = prime * result + (if (this.layoutCategory == null) 0 else this.layoutCategory.hashCode())
        result = prime * result + (if (this.layoutName == null) 0 else this.layoutName.hashCode())
        result = prime * result + (if (this.exclusiveArea == null) 0 else this.exclusiveArea.hashCode())
        result = prime * result + (if (this.tenantContractNumber == null) 0 else this.tenantContractNumber.hashCode())
        result = prime * result + (if (this.currentStatusCategory == null) 0 else this.currentStatusCategory.hashCode())
        result = prime * result + (if (this.modifiedStatusCategory == null) 0 else this.modifiedStatusCategory.hashCode())
        result = prime * result + (if (this.vacationNoticeDate == null) 0 else this.vacationNoticeDate.hashCode())
        result = prime * result + (if (this.reviewApprovalDate == null) 0 else this.reviewApprovalDate.hashCode())
        result = prime * result + (if (this.reviewApprovalDaysElapsed == null) 0 else this.reviewApprovalDaysElapsed.hashCode())
        result = prime * result + (if (this.arrangementOutputDate == null) 0 else this.arrangementOutputDate.hashCode())
        result = prime * result + (if (this.arrangementApprovalDate == null) 0 else this.arrangementApprovalDate.hashCode())
        result = prime * result + (if (this.arrangementCollectionDate == null) 0 else this.arrangementCollectionDate.hashCode())
        result = prime * result + (if (this.arrangementCollectionDaysElapsed == null) 0 else this.arrangementCollectionDaysElapsed.hashCode())
        result = prime * result + (if (this.moveOutMeetingDate == null) 0 else this.moveOutMeetingDate.hashCode())
        result = prime * result + (if (this.expectedMoveOutDate == null) 0 else this.expectedMoveOutDate.hashCode())
        result = prime * result + (if (this.moveOutDate == null) 0 else this.moveOutDate.hashCode())
        result = prime * result + (if (this.moveOutRent == null) 0 else this.moveOutRent.hashCode())
        result = prime * result + (if (this.reviewRent == null) 0 else this.reviewRent.hashCode())
        result = prime * result + (if (this.currentRent == null) 0 else this.currentRent.hashCode())
        result = prime * result + (if (this.differenceReviewMoveOut == null) 0 else this.differenceReviewMoveOut.hashCode())
        result = prime * result + (if (this.differenceCurrentMoveOut == null) 0 else this.differenceCurrentMoveOut.hashCode())
        result = prime * result + (if (this.differenceCurrentReview == null) 0 else this.differenceCurrentReview.hashCode())
        result = prime * result + (if (this.discrepancyStatus == null) 0 else this.discrepancyStatus.hashCode())
        result = prime * result + (if (this.longestVacancyPeriodHistory == null) 0 else this.longestVacancyPeriodHistory.hashCode())
        result = prime * result + (if (this.residentialRoomCount == null) 0 else this.residentialRoomCount.hashCode())
        result = prime * result + (if (this.residentialRoomCountVacant == null) 0 else this.residentialRoomCountVacant.hashCode())
        result = prime * result + (if (this.restorationCompletionExpectedDate == null) 0 else this.restorationCompletionExpectedDate.hashCode())
        result = prime * result + (if (this.restorationCompletionDate == null) 0 else this.restorationCompletionDate.hashCode())
        result = prime * result + (if (this.vacancyAccountingExpectedDate == null) 0 else this.vacancyAccountingExpectedDate.hashCode())
        result = prime * result + (if (this.vacancyAccountingDate == null) 0 else this.vacancyAccountingDate.hashCode())
        result = prime * result + (if (this.vacancyMonths == null) 0 else this.vacancyMonths.hashCode())
        result = prime * result + (if (this.vacancyPeriod == null) 0 else this.vacancyPeriod.hashCode())
        result = prime * result + (if (this.occupancyApplicationDate == null) 0 else this.occupancyApplicationDate.hashCode())
        result = prime * result + (if (this.contractDate == null) 0 else this.contractDate.hashCode())
        result = prime * result + (if (this.remainingCollectionExpectedDate == null) 0 else this.remainingCollectionExpectedDate.hashCode())
        result = prime * result + (if (this.remainingCollectionDate == null) 0 else this.remainingCollectionDate.hashCode())
        result = prime * result + (if (this.expectedOccupancyDate == null) 0 else this.expectedOccupancyDate.hashCode())
        result = prime * result + (if (this.occupancyDate == null) 0 else this.occupancyDate.hashCode())
        result = prime * result + (if (this.ad == null) 0 else this.ad.hashCode())
        result = prime * result + (if (this.ff == null) 0 else this.ff.hashCode())
        result = prime * result + (if (this.supportMechanismCategory == null) 0 else this.supportMechanismCategory.hashCode())
        result = prime * result + (if (this.preferredRentalCategory == null) 0 else this.preferredRentalCategory.hashCode())
        result = prime * result + (if (this.financingCategory == null) 0 else this.financingCategory.hashCode())
        result = prime * result + (if (this.hlApprovalCategory == null) 0 else this.hlApprovalCategory.hashCode())
        result = prime * result + (if (this.hl == null) 0 else this.hl.hashCode())
        result = prime * result + (if (this.rentDiscountFlag == null) 0 else this.rentDiscountFlag.hashCode())
        result = prime * result + (if (this.rentDiscount == null) 0 else this.rentDiscount.hashCode())
        result = prime * result + (if (this.reformType == null) 0 else this.reformType.hashCode())
        result = prime * result + (if (this.reform == null) 0 else this.reform.hashCode())
        result = prime * result + (if (this.threeMonthFf == null) 0 else this.threeMonthFf.hashCode())
        result = prime * result + (if (this.recruitmentStatusCategory == null) 0 else this.recruitmentStatusCategory.hashCode())
        result = prime * result + (if (this.reserveCategory_2 == null) 0 else this.reserveCategory_2.hashCode())
        result = prime * result + (if (this.reserveCategory_3 == null) 0 else this.reserveCategory_3.hashCode())
        result = prime * result + (if (this.reserveCategory_4 == null) 0 else this.reserveCategory_4.hashCode())
        result = prime * result + (if (this.reserveCategory_5 == null) 0 else this.reserveCategory_5.hashCode())
        result = prime * result + (if (this.reserveCategory_6 == null) 0 else this.reserveCategory_6.hashCode())
        result = prime * result + (if (this.reserveCategory_7 == null) 0 else this.reserveCategory_7.hashCode())
        result = prime * result + (if (this.reserveCategory_8 == null) 0 else this.reserveCategory_8.hashCode())
        result = prime * result + (if (this.reserveCategory_9 == null) 0 else this.reserveCategory_9.hashCode())
        result = prime * result + (if (this.reserveCategory_10 == null) 0 else this.reserveCategory_10.hashCode())
        result = prime * result + (if (this.constructionRepCd == null) 0 else this.constructionRepCd.hashCode())
        result = prime * result + (if (this.constructionRepName == null) 0 else this.constructionRepName.hashCode())
        result = prime * result + (if (this.implementationRepCd == null) 0 else this.implementationRepCd.hashCode())
        result = prime * result + (if (this.implementationRepName == null) 0 else this.implementationRepName.hashCode())
        result = prime * result + (if (this.adActualAmount == null) 0 else this.adActualAmount.hashCode())
        result = prime * result + (if (this.ffActualAmount == null) 0 else this.ffActualAmount.hashCode())
        result = prime * result + (if (this.municipalityCd == null) 0 else this.municipalityCd.hashCode())
        result = prime * result + (if (this.municipalityName == null) 0 else this.municipalityName.hashCode())
        result = prime * result + (if (this.businessRoomCountVacant == null) 0 else this.businessRoomCountVacant.hashCode())
        result = prime * result + (if (this.longestVacancyPeriodBusiness == null) 0 else this.longestVacancyPeriodBusiness.hashCode())
        result = prime * result + (if (this.businessRoomCount == null) 0 else this.businessRoomCount.hashCode())
        result = prime * result + (if (this.statusCode == null) 0 else this.statusCode.hashCode())
        result = prime * result + (if (this.exclusionFlag == null) 0 else this.exclusionFlag.hashCode())
        result = prime * result + (if (this.collectionRepCd == null) 0 else this.collectionRepCd.hashCode())
        result = prime * result + (if (this.collectionRepName == null) 0 else this.collectionRepName.hashCode())
        result = prime * result + (if (this.abcCategory == null) 0 else this.abcCategory.hashCode())
        result = prime * result + (if (this.moveOutMeetingTime == null) 0 else this.moveOutMeetingTime.hashCode())
        result = prime * result + (if (this.managementRepCd == null) 0 else this.managementRepCd.hashCode())
        result = prime * result + (if (this.managementRepName == null) 0 else this.managementRepName.hashCode())
        result = prime * result + (if (this.supplyPlanAreaCode == null) 0 else this.supplyPlanAreaCode.hashCode())
        result = prime * result + (if (this.supplyPlanAreaName == null) 0 else this.supplyPlanAreaName.hashCode())
        result = prime * result + (if (this.assessmentArea == null) 0 else this.assessmentArea.hashCode())
        result = prime * result + (if (this.storeSalesDepartmentCd == null) 0 else this.storeSalesDepartmentCd.hashCode())
        result = prime * result + (if (this.storeSalesDepartmentName == null) 0 else this.storeSalesDepartmentName.hashCode())
        result = prime * result + (if (this.storeSalesDepartmentOutputOrderCd == null) 0 else this.storeSalesDepartmentOutputOrderCd.hashCode())
        result = prime * result + (if (this.storeCd == null) 0 else this.storeCd.hashCode())
        result = prime * result + (if (this.storeName == null) 0 else this.storeName.hashCode())
        result = prime * result + (if (this.storeOutputOrderCd == null) 0 else this.storeOutputOrderCd.hashCode())
        return result
    }
}
