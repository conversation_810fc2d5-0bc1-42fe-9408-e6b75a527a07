/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.MailForwardMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.MailForwardMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * メール転送マスタ 既存システム物理名: ERAMSP
 */
@Suppress("UNCHECKED_CAST")
open class MailForwardMasterRecord private constructor() : TableRecordImpl<MailForwardMasterRecord>(MailForwardMasterTable.MAIL_FORWARD_MASTER) {

    open var creationDate: Long?
        set(value): Unit = set(0, value)
        get(): Long? = get(0) as Long?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var branchCd: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var employeeNumber: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var address: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var recipientName: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    /**
     * Create a detached, initialised MailForwardMasterRecord
     */
    constructor(creationDate: Long? = null, creationTime: Int? = null, creator: String? = null, branchCd: String? = null, employeeNumber: String? = null, address: String? = null, recipientName: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.branchCd = branchCd
        this.employeeNumber = employeeNumber
        this.address = address
        this.recipientName = recipientName
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised MailForwardMasterRecord
     */
    constructor(value: MailForwardMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.branchCd = value.branchCd
            this.employeeNumber = value.employeeNumber
            this.address = value.address
            this.recipientName = value.recipientName
            resetChangedOnNotNull()
        }
    }
}
