-- VIEW: PARKING_RESERVATION_V(駐車場予約ビュー)
-- ステータスにRESERVE_END_DATETIMEをリアルタイムに反映

CREATE VIEW PARKING_RESERVATION_V AS
SELECT
  PARKING_RESERVATION_ID,
  BUILDING_CODE,
  PARKING_LOT_CODE,
  RESERVE_TYPE,
  CASE
    -- RESERVE_END_DATETIMEを超えた場合は完了と扱う
    WHEN RESERVE_STATUS in ('0', '1') 
      AND RESERVE_END_DATETIME IS NOT NULL 
      AND RESERVE_END_DATETIME <= current_timestamp
      THEN '2'
    ELSE RESERVE_STATUS
  END AS RESERVE_STATUS,
  RECEPTION_DATE,
  RECEPTION_STAFF,
  RESERVER_NAME,
  RESERVER_TEL,
  REMARKS,
  RESERVER_SYSTEM,
  RESERVE_START_DATETIME,
  RESERVE_END_DATETIME,
  LINKED_BUILDING_CODE,
  LINKED_ROOM_CODE,
  EBOARD_PARKING_RESERVATION_ID,
  CREATION_DATE,
  CREATION_TIME,
  CREATOR,
  UPDATE_DATE,
  UPDATE_TIME,
  UPDATER,
  DELETE_FLAG
FROM PARKING_RESERVATION;
