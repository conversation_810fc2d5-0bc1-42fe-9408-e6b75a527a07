package jp.ne.simplex.application.model

class Employee(
    val code: Code,
    val name: Name,
    val affiliationCode: String? = null,
    val company: Company? = null,
) {
    data class Code(val value: String) {
        companion object {
            fun of(value: String): Code {
                return Code(value)
            }
        }
    }

    data class Name(val kanji: String) {
        companion object {
            fun of(kanji: String?): Name {
                return Name(kanji = kanji ?: "")
            }
        }
    }

    fun getOfficeCode(): Office.Code? {
        return if (this.affiliationCode == null) {
            null
        } else {
            Office.Code.of(this.affiliationCode.substring(0, 3))
        }
    }

    companion object {
        // いい物件ボードに不正な状態で永続化されているデータを扱うため
        fun dummy(code: String? = null, name: String? = null): Employee {
            return Employee(
                code = Code.of(code ?: ""),
                name = Name.of(name ?: "")
            )
        }
    }
}
