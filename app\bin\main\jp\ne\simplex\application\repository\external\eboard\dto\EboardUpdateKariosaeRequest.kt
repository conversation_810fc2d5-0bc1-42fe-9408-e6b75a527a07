package jp.ne.simplex.application.repository.external.eboard.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.model.CancelTemporaryReservation
import jp.ne.simplex.application.model.Office
import jp.ne.simplex.application.model.RegisterTemporaryReservation.OtherCompanyRegisterTemporaryReservation
import jp.ne.simplex.application.model.RegisterTemporaryReservation.OwnCompanyRegisterTemporaryReservation
import jp.ne.simplex.application.model.TemporaryReservationInfo
import jp.ne.simplex.application.model.TemporaryReservationInfo.*
import jp.ne.simplex.application.model.UpdateTemporaryReservationComment
import jp.ne.simplex.application.repository.external.eboard.config.EboardApiPath
import jp.ne.simplex.application.repository.external.eboard.config.EboardRequest
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd

// ObjectMapperを用いてJSONデシリアライズする際に、
// 「nYoteiDate」を「nYoteiDate」と「nyoteiDate」の2つにデシリアライズしてしまうため、
// 明示的に「nyoteiDate」を除外するようにする
@JsonIgnoreProperties(value = ["nyoteiDate"])
class EboardUpdateKariosaeRequest private constructor(

    @field:JsonProperty("tatemonoCd")
    val tatemonoCd: String,

    @field:JsonProperty("heyaCd")
    val heyaCd: String,

    @field:JsonProperty("updKbn")
    val updKbn: String,

    @field:JsonProperty("nYoteiDate")
    val nYoteiDate: String?,

    @field:JsonProperty("firmId")
    val firmId: String?,

    @field:JsonProperty("firmName")
    val firmName: String?,

    @field:JsonProperty("branchName")
    val branchName: String?,

    @field:JsonProperty("regName")
    val regName: String?,

    @field:JsonProperty("staffCd")
    val staffCd: String?,

    @field:JsonProperty("comment")
    val comment: String?,
) : EboardRequest {

    override fun getApiPath(): EboardApiPath {
        return EboardApiPath.UPDATE_KARIOSAE
    }

    companion object {

        fun of(
            input: OwnCompanyRegisterTemporaryReservation,
            office: Office?
        ): EboardUpdateKariosaeRequest {
            return EboardUpdateKariosaeRequest(
                tatemonoCd = input.getId().buildingCode.value,
                heyaCd = input.getId().roomCode.value,
                updKbn = "1",
                nYoteiDate = input.scheduledMoveInDate.yyyyMMdd(),
                firmId = if (office == null) "DKL${input.assignedBranch.code.getPrefix()}" else "DKP${office.code.value}",
                firmName = input.assignedBranch.company.name,
                branchName = if (office == null) input.assignedBranch.name.value else "${office.name.value}営業所",
                regName = input.assignedEmployee.name.kanji,
                staffCd = input.assignedEmployee.code.value,
                comment = input.comment.value,
            )
        }

        fun of(input: OtherCompanyRegisterTemporaryReservation): EboardUpdateKariosaeRequest {
            return EboardUpdateKariosaeRequest(
                tatemonoCd = input.getId().buildingCode.value,
                heyaCd = input.getId().roomCode.value,
                updKbn = "1",
                nYoteiDate = input.scheduledMoveInDate.yyyyMMdd(),
                firmId = input.otherCompanyInfo.companyCode,
                firmName = input.otherCompanyInfo.companyName,
                branchName = input.otherCompanyInfo.storeName,
                regName = input.otherCompanyInfo.staffName,
                staffCd = null,
                comment = input.comment.value,
            )
        }

        fun of(
            input: CancelTemporaryReservation,
            canceled: TemporaryReservationInfo,
            office: Office?
        ): EboardUpdateKariosaeRequest? {
            // 他社情報が設定されている場合は、他社による仮押さえのため、 リクエストには、他社情報を設定する
            return when (canceled) {
                is OtherCompanyTemporaryReservationInfo -> {
                    EboardUpdateKariosaeRequest(
                        tatemonoCd = input.getId().buildingCode.value,
                        heyaCd = input.getId().roomCode.value,
                        updKbn = "0",
                        nYoteiDate = canceled.scheduledMoveInDate.yyyyMMdd(),
                        firmId = canceled.otherCompanyInfo.companyCode,
                        firmName = canceled.otherCompanyInfo.companyName,
                        branchName = canceled.otherCompanyInfo.storeName,
                        regName = canceled.otherCompanyInfo.staffName,
                        staffCd = null,
                        comment = input.comment.value,
                    )
                }

                is OwnCompanyTemporaryReservationInfo -> {
                    EboardUpdateKariosaeRequest(
                        tatemonoCd = input.getId().buildingCode.value,
                        heyaCd = input.getId().roomCode.value,
                        updKbn = "0",
                        nYoteiDate = canceled.scheduledMoveInDate.yyyyMMdd(),
                        firmId = if (office == null) "DKL${canceled.assignedBranch.code.getPrefix()}" else "DKP${office.code.value}",
                        firmName = canceled.assignedBranch.company.name,
                        branchName = if (office == null) canceled.assignedBranch.name.value else "${office.name.value}営業所",
                        regName = canceled.assignedEmployee.name.kanji,
                        staffCd = null,
                        comment = input.comment.value,
                    )
                }

                is CancelledTemporaryReservationInfo -> {
                    null
                }
            }
        }

        fun of(input: UpdateTemporaryReservationComment): EboardUpdateKariosaeRequest {
            return EboardUpdateKariosaeRequest(
                tatemonoCd = input.getId().buildingCode.value,
                heyaCd = input.getId().roomCode.value,
                updKbn = "0",
                nYoteiDate = null,
                firmId = null,
                firmName = null,
                branchName = null,
                regName = null,
                staffCd = null,
                comment = input.comment.value,
            )
        }
    }
}
