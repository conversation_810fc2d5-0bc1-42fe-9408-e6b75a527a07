-- TABLE: IMAGE_COPY_SUPPORT_FILE(画像コピー対応ファイル)

CREATE TABLE IMAGE_COPY_SUPPORT_FILE(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(10)                   
,    PROPERTY_BUILDING_CODE                       varchar(9)        NOT NULL    
,    PROPERTY_ROOM_CODE                           varchar(5)        NOT NULL    
,    EXTERIOR_IMAGE_COMMENT_CODE                  varchar(3)                    
,    LAYOUT_IMAGE_COMMENT_CODE                    varchar(3)                    
,    IMAGE_COMMENT_CODE_1                         varchar(3)                    
,    IMAGE_COMMENT_CODE_2                         varchar(3)                    
,    IMAGE_COMMENT_CODE_3                         varchar(3)                    
,    IMAGE_COMMENT_CODE_4                         varchar(3)                    
,    IMAGE_COMMENT_CODE_5                         varchar(3)                    
,    IMAGE_COMMENT_CODE_6                         varchar(3)                    
,    EXTERIOR_IMAGE_COMMENT                       varchar(404)                  
,    LAYOUT_IMAGE_COMMENT                         varchar(404)                  
,    IMAGE_COMMENT_1                              varchar(404)                  
,    IMAGE_COMMENT_2                              varchar(404)                  
,    IMAGE_COMMENT_3                              varchar(404)                  
,    IMAGE_COMMENT_4                              varchar(404)                  
,    IMAGE_COMMENT_5                              varchar(404)                  
,    IMAGE_COMMENT_6                              varchar(404)                  
,    COPY_EXECUTION_FLAG                          varchar(1)                    
,    CONFIRMATION_CATEGORY                        varchar(1)                    
,    IMAGE_COMMENT_CODE_7                         varchar(3)                    
,    IMAGE_COMMENT_CODE_8                         varchar(3)                    
,    IMAGE_COMMENT_CODE_9                         varchar(3)                    
,    IMAGE_COMMENT_CODE_10                        varchar(3)                    
,    IMAGE_COMMENT_CODE_11                        varchar(3)                    
,    IMAGE_COMMENT_CODE_12                        varchar(3)                    
,    SURROUNDING_IMAGE_COMMENT_CODE_1             varchar(3)                    
,    SURROUNDING_IMAGE_COMMENT_CODE_2             varchar(3)                    
,    SURROUNDING_IMAGE_COMMENT_CODE_3             varchar(3)                    
,    SURROUNDING_IMAGE_COMMENT_CODE_4             varchar(3)                    
,    SURROUNDING_IMAGE_COMMENT_CODE_5             varchar(3)                    
,    SURROUNDING_IMAGE_COMMENT_CODE_6             varchar(3)                    
,    IMAGE_COMMENT_7                              varchar(404)                  
,    IMAGE_COMMENT_8                              varchar(404)                  
,    IMAGE_COMMENT_9                              varchar(404)                  
,    IMAGE_COMMENT_10                             varchar(404)                  
,    IMAGE_COMMENT_11                             varchar(404)                  
,    IMAGE_COMMENT_12                             varchar(404)                  
,    SURROUNDING_IMAGE_COMMENT_1                  varchar(404)                  
,    SURROUNDING_IMAGE_COMMENT_2                  varchar(404)                  
,    SURROUNDING_IMAGE_COMMENT_3                  varchar(404)                  
,    SURROUNDING_IMAGE_COMMENT_4                  varchar(404)                  
,    SURROUNDING_IMAGE_COMMENT_5                  varchar(404)                  
,    SURROUNDING_IMAGE_COMMENT_6                  varchar(404)                  
,    CONSTRAINT PK_IMAGE_COPY_SUPPORT_FILE PRIMARY KEY (PROPERTY_BUILDING_CODE, PROPERTY_ROOM_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE IMAGE_COPY_SUPPORT_FILE IS '画像コピー対応ファイル 既存システム物理名: ERCPGP';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: ERC01D 402でVARLEN指定';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.CREATION_TIME IS '作成時刻 既存システム物理名: ERC02H 402でVARLEN指定';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.CREATOR IS '作成者 既存システム物理名: ERC03C 402でVARLEN指定';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: ERC04D 402でVARLEN指定';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.UPDATE_TIME IS '更新時刻 既存システム物理名: ERC05H 402でVARLEN指定';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.UPDATER IS '更新者 既存システム物理名: ERC06C 402でVARLEN指定';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.PROPERTY_BUILDING_CODE IS '物件建物コード 既存システム物理名: ERC08C 402でVARLEN指定';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.PROPERTY_ROOM_CODE IS '物件部屋コード 既存システム物理名: ERC09C 402でVARLEN指定';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.EXTERIOR_IMAGE_COMMENT_CODE IS '外観画像コメントコード 既存システム物理名: ERC10C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.LAYOUT_IMAGE_COMMENT_CODE IS '間取画像コメントコード 既存システム物理名: ERC11C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_CODE_1 IS '画像コメントコード1 既存システム物理名: ERC12C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_CODE_2 IS '画像コメントコード2 既存システム物理名: ERC13C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_CODE_3 IS '画像コメントコード3 既存システム物理名: ERC14C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_CODE_4 IS '画像コメントコード4 既存システム物理名: ERC15C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_CODE_5 IS '画像コメントコード5 既存システム物理名: ERC16C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_CODE_6 IS '画像コメントコード6 既存システム物理名: ERC17C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.EXTERIOR_IMAGE_COMMENT IS '外観画像コメント 既存システム物理名: ERC18X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.LAYOUT_IMAGE_COMMENT IS '間取画像コメント 既存システム物理名: ERC19X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_1 IS '画像コメント1 既存システム物理名: ERC20X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_2 IS '画像コメント2 既存システム物理名: ERC21X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_3 IS '画像コメント3 既存システム物理名: ERC22X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_4 IS '画像コメント4 既存システム物理名: ERC23X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_5 IS '画像コメント5 既存システム物理名: ERC24X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_6 IS '画像コメント6 既存システム物理名: ERC25X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.COPY_EXECUTION_FLAG IS 'コピー実施フラグ 既存システム物理名: ERC26F';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.CONFIRMATION_CATEGORY IS '確認区分 既存システム物理名: ERC27B';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_CODE_7 IS '画像コメントコード7 既存システム物理名: ERC18C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_CODE_8 IS '画像コメントコード8 既存システム物理名: ERC19C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_CODE_9 IS '画像コメントコード9 既存システム物理名: ERC20C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_CODE_10 IS '画像コメントコード10 既存システム物理名: ERC21C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_CODE_11 IS '画像コメントコード11 既存システム物理名: ERC22C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_CODE_12 IS '画像コメントコード12 既存システム物理名: ERC23C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.SURROUNDING_IMAGE_COMMENT_CODE_1 IS '周辺画像コメントコード1 既存システム物理名: ERC24C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.SURROUNDING_IMAGE_COMMENT_CODE_2 IS '周辺画像コメントコード2 既存システム物理名: ERC25C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.SURROUNDING_IMAGE_COMMENT_CODE_3 IS '周辺画像コメントコード3 既存システム物理名: ERC26C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.SURROUNDING_IMAGE_COMMENT_CODE_4 IS '周辺画像コメントコード4 既存システム物理名: ERC27C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.SURROUNDING_IMAGE_COMMENT_CODE_5 IS '周辺画像コメントコード5 既存システム物理名: ERC28C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.SURROUNDING_IMAGE_COMMENT_CODE_6 IS '周辺画像コメントコード6 既存システム物理名: ERC29C';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_7 IS '画像コメント7 既存システム物理名: ERC26X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_8 IS '画像コメント8 既存システム物理名: ERC27X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_9 IS '画像コメント9 既存システム物理名: ERC28X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_10 IS '画像コメント10 既存システム物理名: ERC29X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_11 IS '画像コメント11 既存システム物理名: ERC30X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.IMAGE_COMMENT_12 IS '画像コメント12 既存システム物理名: ERC31X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.SURROUNDING_IMAGE_COMMENT_1 IS '周辺画像コメント1 既存システム物理名: ERC32X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.SURROUNDING_IMAGE_COMMENT_2 IS '周辺画像コメント2 既存システム物理名: ERC33X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.SURROUNDING_IMAGE_COMMENT_3 IS '周辺画像コメント3 既存システム物理名: ERC34X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.SURROUNDING_IMAGE_COMMENT_4 IS '周辺画像コメント4 既存システム物理名: ERC35X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.SURROUNDING_IMAGE_COMMENT_5 IS '周辺画像コメント5 既存システム物理名: ERC36X';
COMMENT ON COLUMN IMAGE_COPY_SUPPORT_FILE.SURROUNDING_IMAGE_COMMENT_6 IS '周辺画像コメント6 既存システム物理名: ERC37X';
