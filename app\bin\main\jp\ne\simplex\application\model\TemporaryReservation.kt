package jp.ne.simplex.application.model

import java.time.LocalDate

/** 「仮押さえ」を操作する際の Interface */
interface TemporaryReservation {

    fun getId(): Property.Id

    data class Version private constructor(
        // 仮押さえ更新日
        val updateDate: String,

        // 仮押さえ更新日
        val updateTime: String,
    ) {
        companion object {
            fun of(updateDate: String, updateTime: String): Version {
                return Version(updateDate, updateTime)
            }
        }
    }

    data class Comment private constructor(val value: String) {

        companion object {
            private fun empty(): Comment {
                return Comment("") // 空文字を返却する
            }

            fun of(str: String?): Comment {
                val trimmed = str?.trim() ?: ""
                return if (trimmed.isEmpty()) empty() else Comment(trimmed)
            }
        }
    }

    data class OtherCompanyInfo(
        // 他社コード（Eコード）
        val companyCode: String,

        // 他社会社名
        val companyName: String,

        // 他社店舗名
        val storeName: String,

        // 他社担当者名
        val staffName: String
    )
}

/** 仮押さえ登録 */
sealed interface RegisterTemporaryReservation : TemporaryReservation {

    data class OwnCompanyRegisterTemporaryReservation(
        // 仮押さえ情報を一意に表すID
        private val id: Property.Id,

        // 担当支店
        val assignedBranch: Branch,

        // 担当従業員
        val assignedEmployee: Employee,

        // 入居予定日
        val scheduledMoveInDate: LocalDate,

        // 仮押さえコメント
        val comment: TemporaryReservation.Comment,

        // バージョン（ここではレコードの更新時刻をバージョンとして扱う）
        // 新規登録の場合、バージョン情報は null になりうる
        val version: TemporaryReservation.Version?,
    ) : RegisterTemporaryReservation {

        override fun getId(): Property.Id {
            return this.id
        }
    }

    data class OtherCompanyRegisterTemporaryReservation(
        // 仮押さえ情報を一意に表すID
        private val id: Property.Id,

        // 入居予定日
        val scheduledMoveInDate: LocalDate,

        // 他社情報
        val otherCompanyInfo: TemporaryReservation.OtherCompanyInfo,

        // 仮押さえコメント
        val comment: TemporaryReservation.Comment,

        // バージョン（ここではレコードの更新時刻をバージョンとして扱う）
        // 他社仮押さえの新規登録は、できないため必ずバージョン情報が存在する
        val version: TemporaryReservation.Version,
    ) : RegisterTemporaryReservation {

        override fun getId(): Property.Id {
            return this.id
        }
    }
}

/** 仮押さえ解除 */
class CancelTemporaryReservation(
    // 仮押さえ情報を一意に表すID
    private val id: Property.Id,

    // 仮押さえコメント
    val comment: TemporaryReservation.Comment,

    // バージョン（ここではレコードの更新時刻をバージョンとして扱う）
    val version: TemporaryReservation.Version?,
) : TemporaryReservation {

    override fun getId(): Property.Id {
        return this.id
    }
}

class UpdateTemporaryReservationComment(
    // 仮押さえ情報を一意に表すID
    private val id: Property.Id,

    // 仮押さえコメント
    val comment: TemporaryReservation.Comment,
) : TemporaryReservation {

    override fun getId(): Property.Id {
        return this.id
    }
}

/** 仮押さえ強制更新（データの主管サービスであるいい物件ボードから仮押さえ更新） **/
sealed interface ForceUpdateTemporaryReservation : TemporaryReservation {

    data class ForceRegisterTemporaryReservation(
        // 仮押さえ情報を一意に表すID
        private val id: Property.Id,

        // 入居予定日
        val scheduledMoveInDate: LocalDate,

        // 担当支店コード
        val assignedBranchCode: Branch.Code?,

        // 担当従業員コード
        val assignedEmployeeCode: Employee.Code?,

        // 他社による登録時の情報
        val otherCompanyInfo: TemporaryReservation.OtherCompanyInfo?,

        // 仮押さえコメント
        val comment: TemporaryReservation.Comment,
    ) : ForceUpdateTemporaryReservation {
        override fun getId(): Property.Id {
            return this.id
        }
    }

    data class ForceCancelTemporaryReservation(
        // 仮押さえ情報を一意に表すID
        private val id: Property.Id,

        // 仮押さえコメント
        val comment: TemporaryReservation.Comment,
    ) : ForceUpdateTemporaryReservation {

        override fun getId(): Property.Id {
            return this.id
        }
    }

}

/** 仮押さえ情報 */
sealed interface TemporaryReservationInfo : TemporaryReservation {

    data class CancelledTemporaryReservationInfo(
        // 仮押さえ情報を一意に表すID
        private val id: Property.Id,

        // 仮押さえコメント
        val comment: TemporaryReservation.Comment,
    ) : TemporaryReservationInfo {

        override fun getId(): Property.Id {
            return this.id
        }
    }

    /** 自社によって仮押さえされた仮押さえ登録情報 */
    data class OwnCompanyTemporaryReservationInfo(
        // 仮押さえ情報を一意に表すID
        private val id: Property.Id,

        // 担当支店コード
        val assignedBranch: Branch,

        // 担当従業員
        val assignedEmployee: Employee,

        // 入居予定日
        val scheduledMoveInDate: LocalDate,

        // 仮押さえコメント
        val comment: TemporaryReservation.Comment,

        // バージョン（ここではレコードの更新時刻をバージョンとして扱う）
        val version: TemporaryReservation.Version,
    ) : TemporaryReservationInfo {

        override fun getId(): Property.Id {
            return this.id
        }
    }

    /** 他社によって仮押さえされた仮押さえ登録情報 */
    data class OtherCompanyTemporaryReservationInfo(
        // 仮押さえ情報を一意に表すID
        private val id: Property.Id,

        // 入居予定日
        val scheduledMoveInDate: LocalDate,

        // 仮押さえコメント
        val comment: TemporaryReservation.Comment,

        // 他社による登録時の情報
        val otherCompanyInfo: TemporaryReservation.OtherCompanyInfo,

        // バージョン（ここではレコードの更新時刻をバージョンとして扱う）
        val version: TemporaryReservation.Version,
    ) : TemporaryReservationInfo {

        override fun getId(): Property.Id {
            return this.id
        }
    }
}
