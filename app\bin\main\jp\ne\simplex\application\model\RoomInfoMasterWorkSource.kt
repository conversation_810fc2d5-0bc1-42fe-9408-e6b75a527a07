package jp.ne.simplex.application.model

import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import java.math.BigDecimal
import java.time.LocalDate

/** 入居中物件データ作成バッチのワークテーブルのデータソースのクラス */
data class RoomInfoMasterWorkSource private constructor(
    /** 建物コード */
    val buildingCode: String,
    /** 部屋コード */
    val roomCode: String,
    /** 部屋番号 */
    val roomNumber: String?,
    /** 初回入居日 */
    val initialOccupancyDate: LocalDate?,
    /** 間取内容 */
    val layoutDetails: String?,
    /** 専用庭有無 */
    val hasPrivateGardenAvailability: Boolean,
    /** フロントフリーレントサイン */
    val frontFreeRentSign: Int?,
    /** 部屋種別 */
    val roomType: String?,
    /** 店舗専有面積 */
    val storeExclusiveAreaSquareMeters: BigDecimal?,
    /** 事務所床面積 */
    val officeFloorAreaSquareMeters: BigDecimal?,
    /** 建物名称 */
    val buildingName: String?,
    /** 都道府県コード */
    val prefectureCode: String?,
    /** 市区郡コード */
    val cityCode: String?,
    /** 町村字通称コード */
    val townCode: String?,
    /** 住所詳細 */
    val addressDetails: String?,
    /** 客付責任視点コード */
    val tenantRecruitmentBranchCode: String?,
    /** 完成引渡日 */
    val completionDeliveryDate: LocalDate?,
    /** 完成予定日 */
    val completionExpectedDate: LocalDate?,
    /** 物件区分 */
    val category: RoomInfoMasterWorkCategory,
    /** 退居日 */
    val moveOutDate: LocalDate?,
    /** 入居申込日 */
    val moveInApplicationDate: LocalDate?,
    /** 入居予定日 */
    val moveInScheduledDate: LocalDate?,
    /** 明渡し通知日 */
    val vacateNoticeDate: LocalDate?,
    /** 入居開始処理済サイン */
    val moveInStartProcessedSign: Boolean,
    /** 手付変更日 */
    val depositChangeDate: LocalDate?,
    /** 賃貸借契約日 */
    val leaseContractDate: LocalDate?,
    /** 残集日 */
    val remainingDate: LocalDate?,
    /** 課税区分 */
    val taxDivision: String?,
    /** 交渉 */
    val negotiationEmployeeName: String?,
    /** 募集開始日 */
    val recruitmentStartDate: LocalDate?,
    /** 斡旋申込書帳票番号 */
    val brokerApplicationFormNumber: String?,
    /** 斡旋申込書回収日 */
    val brokerApplicationCollectionDate: LocalDate?,
    /** 斡旋申込書回収区分 */
    val brokerApplicationCollectionDivision: String?,
    /** 家賃 */
    val rent: Int?,
    /** 共益費 */
    val commonFee: Int?,
    /** 礼金(権利金)金額 */
    val keyMoneyAmount: Int?,
    /** 保証金(敷金)金額 */
    val depositAmount: Int?,
    /** 町内会費(回収) */
    val neighborhoodAssociationFee: Int?,
    /** 家主名 */
    val landlordName: String?,
    /** 建物種別略称2 */
    val buildingTypeAbbreviation2: String?,
    /** 町村字通称漢字名 */
    val townKanjiName: String?,
    /** 町村字通称カナ名 */
    val townKanaName: String?,
) {
    companion object {
        fun of(
            buildingCode: String,
            roomCode: String,
            roomNumber: String?,
            initialOccupancyDateStr: String?,
            layoutDetails: String?,
            privateGardenAvailabilitySign: Int?,
            frontFreeRentSign: Int?,
            roomType: String?,
            storeExclusiveAreaSquareMeters: BigDecimal?,
            officeFloorAreaSquareMeters: BigDecimal?,
            buildingName: String?,
            prefectureCode: String?,
            cityCode: String?,
            townCode: String?,
            addressDetails: String?,
            tenantRecruitmentBranchCode: String?,
            completionDeliveryDateInt: Int?,
            completionExpectedDateInt: Int?,
            moveOutDateStr: String?,
            moveInApplicationDateStr: String?,
            moveInScheduledDateStr: String?,
            vacateNoticeDateStr: String?,
            moveInStartProcessedSign: String?,
            depositChangeDateInt: Int?,
            leaseContractDateInt: Int?,
            remainingDateInt: Int?,
            taxDivision: String?,
            negotiationEmployeeName: String?,
            recruitmentStartDateInt: Int?,
            brokerApplicationFormNumber: String?,
            brokerApplicationCollectionDateInt: Int?,
            brokerApplicationCollectionDivision: String?,
            rent: Int?,
            commonFee: Int?,
            keyMoneyAmount: Int?,
            depositAmount: Int?,
            neighborhoodAssociationFee: Int?,
            usageCategory: String?,
            buildingTypeAbbreviation2: String?,
            landlordName: String?,
            townKanjiName: String?,
            townKanaName: String?,
        ): RoomInfoMasterWorkSource {
            val initialOccupancyDate =
                runCatching { initialOccupancyDateStr?.yyyyMMdd() }.getOrNull()
            val category = when (usageCategory) {
                "1" -> if (initialOccupancyDate == null || initialOccupancyDate.isAfter(LocalDate.now())) {
                    RoomInfoMasterWorkCategory.NEW_RESIDENTIAL
                } else {
                    RoomInfoMasterWorkCategory.EXISTING_RESIDENTIAL
                }

                "2" -> RoomInfoMasterWorkCategory.COMMERCIAL
                else -> null
            }
            return RoomInfoMasterWorkSource(
                buildingCode,
                roomCode,
                roomNumber,
                initialOccupancyDate,
                layoutDetails,
                hasPrivateGardenAvailability = privateGardenAvailabilitySign != null && privateGardenAvailabilitySign == 1,
                frontFreeRentSign,
                roomType,
                storeExclusiveAreaSquareMeters,
                officeFloorAreaSquareMeters,
                buildingName,
                prefectureCode,
                cityCode,
                townCode,
                addressDetails,
                tenantRecruitmentBranchCode,
                completionDeliveryDate = runCatching {
                    completionDeliveryDateInt?.toString()?.yyyyMMdd()
                }.getOrNull(),
                completionExpectedDate = runCatching {
                    completionExpectedDateInt?.toString()?.yyyyMMdd()
                }.getOrNull(),
                category = category!!,
                moveOutDate = runCatching { moveOutDateStr?.yyyyMMdd() }.getOrNull(),
                moveInApplicationDate = runCatching { moveInApplicationDateStr?.yyyyMMdd() }.getOrNull(),
                moveInScheduledDate = runCatching { moveInScheduledDateStr?.yyyyMMdd() }.getOrNull(),
                vacateNoticeDate = runCatching { vacateNoticeDateStr?.yyyyMMdd() }.getOrNull(),
                moveInStartProcessedSign = moveInStartProcessedSign != null && moveInStartProcessedSign != "0",
                depositChangeDate = runCatching {
                    depositChangeDateInt?.toString()?.yyyyMMdd()
                }.getOrNull(),
                leaseContractDate = runCatching {
                    leaseContractDateInt?.toString()?.yyyyMMdd()
                }.getOrNull(),
                remainingDate = runCatching {
                    remainingDateInt?.toString()?.yyyyMMdd()
                }.getOrNull(),
                taxDivision,
                negotiationEmployeeName,
                recruitmentStartDate = runCatching {
                    recruitmentStartDateInt?.toString()?.yyyyMMdd()
                }.getOrNull(),
                brokerApplicationFormNumber,
                brokerApplicationCollectionDate = runCatching {
                    brokerApplicationCollectionDateInt?.toString()?.yyyyMMdd()
                }
                    .getOrNull(),
                brokerApplicationCollectionDivision,
                rent,
                commonFee,
                keyMoneyAmount,
                depositAmount,
                neighborhoodAssociationFee,
                landlordName,
                buildingTypeAbbreviation2,
                townKanjiName,
                townKanaName,
            )
        }
    }

    enum class RoomInfoMasterWorkCategory {
        NEW_RESIDENTIAL, // 居住用新築
        EXISTING_RESIDENTIAL, // 居住用既存
        COMMERCIAL // 事業用
    }
}
