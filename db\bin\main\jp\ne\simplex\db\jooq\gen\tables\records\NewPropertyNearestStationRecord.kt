/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.NewPropertyNearestStationTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.NewPropertyNearestStationPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 新物件最寄駅 既存システム物理名: EMES1P
 */
@Suppress("UNCHECKED_CAST")
open class NewPropertyNearestStationRecord private constructor() : UpdatableRecordImpl<NewPropertyNearestStationRecord>(NewPropertyNearestStationTable.NEW_PROPERTY_NEAREST_STATION) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updater: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var buildingCode: String
        set(value): Unit = set(5, value)
        get(): String = get(5) as String

    open var nearestStationBranch: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var lineCode: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var stationCode: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var busStopName: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var railwayNearestStationDistance: Int?
        set(value): Unit = set(10, value)
        get(): Int? = get(10) as Int?

    open var nearestBusStopDistance: Int?
        set(value): Unit = set(11, value)
        get(): Int? = get(11) as Int?

    open var busRideTime: Byte?
        set(value): Unit = set(12, value)
        get(): Byte? = get(12) as Byte?

    open var latitudeStation: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var longitudeStation: Int?
        set(value): Unit = set(14, value)
        get(): Int? = get(14) as Int?

    open var latitudeBusStop: Int?
        set(value): Unit = set(15, value)
        get(): Int? = get(15) as Int?

    open var longitudeBusStop: Int?
        set(value): Unit = set(16, value)
        get(): Int? = get(16) as Int?

    open var mapScale: Short?
        set(value): Unit = set(17, value)
        get(): Short? = get(17) as Short?

    open var deleteFlag: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised NewPropertyNearestStationRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, buildingCode: String, nearestStationBranch: String, lineCode: String? = null, stationCode: String? = null, busStopName: String? = null, railwayNearestStationDistance: Int? = null, nearestBusStopDistance: Int? = null, busRideTime: Byte? = null, latitudeStation: Int? = null, longitudeStation: Int? = null, latitudeBusStop: Int? = null, longitudeBusStop: Int? = null, mapScale: Short? = null, deleteFlag: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.buildingCode = buildingCode
        this.nearestStationBranch = nearestStationBranch
        this.lineCode = lineCode
        this.stationCode = stationCode
        this.busStopName = busStopName
        this.railwayNearestStationDistance = railwayNearestStationDistance
        this.nearestBusStopDistance = nearestBusStopDistance
        this.busRideTime = busRideTime
        this.latitudeStation = latitudeStation
        this.longitudeStation = longitudeStation
        this.latitudeBusStop = latitudeBusStop
        this.longitudeBusStop = longitudeBusStop
        this.mapScale = mapScale
        this.deleteFlag = deleteFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised NewPropertyNearestStationRecord
     */
    constructor(value: NewPropertyNearestStationPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.buildingCode = value.buildingCode
            this.nearestStationBranch = value.nearestStationBranch
            this.lineCode = value.lineCode
            this.stationCode = value.stationCode
            this.busStopName = value.busStopName
            this.railwayNearestStationDistance = value.railwayNearestStationDistance
            this.nearestBusStopDistance = value.nearestBusStopDistance
            this.busRideTime = value.busRideTime
            this.latitudeStation = value.latitudeStation
            this.longitudeStation = value.longitudeStation
            this.latitudeBusStop = value.latitudeBusStop
            this.longitudeBusStop = value.longitudeBusStop
            this.mapScale = value.mapScale
            this.deleteFlag = value.deleteFlag
            resetChangedOnNotNull()
        }
    }
}
