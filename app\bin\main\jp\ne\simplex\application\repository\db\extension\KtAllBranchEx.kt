package jp.ne.simplex.application.repository.db.extension

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Company
import jp.ne.simplex.db.jooq.gen.tables.pojos.KtAllBranchPojo

class KtAllBranchEx {
    companion object {

        fun KtAllBranchPojo.getBranch(): Branch? {
            return try {
                Branch(
                    code = Branch.Code.of(this.branchCode),
                    name = Branch.Name.of(this.branchName!!),
                    company = Company.of(this.eboardCompanyCode),
                )
            } catch (_: Exception) {
                null
            }
        }
    }
}
