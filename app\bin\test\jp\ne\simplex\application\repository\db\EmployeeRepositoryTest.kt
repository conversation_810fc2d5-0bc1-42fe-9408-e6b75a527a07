package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Company
import jp.ne.simplex.application.repository.db.extension.EmployeeMasterEx.Companion.getEmployee
import jp.ne.simplex.db.jooq.gen.tables.references.EMPLOYEE_MASTER
import jp.ne.simplex.db.jooq.gen.tables.references.HR_CATEGORY_TABLE_B
import jp.ne.simplex.mock.MockBranchRepository
import jp.ne.simplex.mock.MockOfficeBranchMappingRepository
import jp.ne.simplex.stub.stubBranch
import jp.ne.simplex.stub.stubEmployeeMasterPojo
import jp.ne.simplex.stub.stubHrCategoryTableBPojo
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class EmployeeRepositoryTest : AbstractTestContainerTest() {

    private lateinit var repository: EmployeeRepository

    companion object {
        private const val LEASING_BRANCH_CD = "060"
        private const val LEASING_STORE_CD = "373"
        private const val OFFICE_CODE = "757"
        private const val AFFILIATION_BRANCH_CODE = "067"
        private const val KT_ALL_BRANCH_CODE = "475000"
    }

    override fun beforeEach() {
        repository = EmployeeRepository(
            context = dslContext,
            branchRepository = MockBranchRepository(
                getLeasingRelatedToBranchFunc = { branchCode ->
                    if (branchCode.getValue() == LEASING_BRANCH_CD) {
                        return@MockBranchRepository Branch.Code.of(LEASING_STORE_CD)
                    }
                    return@MockBranchRepository null
                },
            ),
            officeBranchMappingRepository = MockOfficeBranchMappingRepository(
                getByOfficeCodeFunc = { officeCode ->
                    if (officeCode?.value == OFFICE_CODE) {
                        return@MockOfficeBranchMappingRepository Branch.Code.of(
                            AFFILIATION_BRANCH_CODE
                        )
                    }
                    throw IllegalArgumentException("Invalid office code")
                },
            )
        )
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(EMPLOYEE_MASTER, HR_CATEGORY_TABLE_B)
    }

    @Nested
    @DisplayName("支店に所属する従業員の一覧を取得できること")
    inner class Scenario1 {

        val targetBranch = stubBranch(
            code = "843000",
            name = "〇〇支店",
            company = Company.DaitouKentakuPartners,
        )

        // 検索条件を全て満たすデータ
        val employee = stubEmployeeMasterPojo(
            affiliationCode = "${targetBranch.code.getPrefix()}800",
            positionCode = "50",
            jobTypeCode = "050",
            resignationDate = 0,
        )

        val hrCategory = stubHrCategoryTableBPojo(
            typeCategory = "13",
            code = employee.positionCode!!
        )

        @Test
        @DisplayName("全ての条件に合致した従業員レコードの一覧が取得できること")
        fun case1() {
            // setup
            dslContext.saveEmployeeMasterPojo(employee)
            dslContext.saveHrCategoryTableBPojo(hrCategory)

            // execute
            val actual = repository.findBy(targetBranch)

            // verify
            assertEquals(1, actual.size)
        }

        @Nested
        @DisplayName("従業員の所属コード（AffiliationCode）の先頭3文字が、指定された支店コードと一致していない場合")
        inner class Scenario1x1 {

            @Test
            @DisplayName("従業員は取得できないこと")
            fun case1() {
                // setup
                dslContext.saveEmployeeMasterPojo(
                    employee.copy(affiliationCode = "7${targetBranch.code.getPrefix()}00")
                )
                dslContext.saveHrCategoryTableBPojo(hrCategory)

                // execute
                val actual = repository.findBy(targetBranch)

                // verify
                assertEquals(0, actual.size)
            }
        }

        @Nested
        @DisplayName("従業員の所属コード（AffiliationCode）の先頭3文字が、指定された支店コードと一致している場合")
        inner class Scenario1x2 {

            @Test
            @DisplayName("従業員の役職CD（PositionCode）が80より大きいレコードは取得できないこと")
            fun case21() {
                // setup
                dslContext.saveEmployeeMasterPojo(
                    employee.copy(positionCode = "80")
                )
                dslContext.saveHrCategoryTableBPojo(hrCategory)

                // execute
                val actual = repository.findBy(targetBranch)

                // verify
                assertEquals(0, actual.size)
            }

            @Test
            @DisplayName("従業員の退職年月日（ResignationDate）が0でないレコードは取得できないこと")
            fun case3() {
                // setup
                dslContext.saveEmployeeMasterPojo(
                    employee.copy(resignationDate = 20231210)
                )
                dslContext.saveHrCategoryTableBPojo(hrCategory)

                // execute
                val actual = repository.findBy(targetBranch)

                // verify
                assertEquals(0, actual.size)
            }

            @Test
            @DisplayName("従業員の職種CD（JobTypeCode）が特定の値でないかつ、所属コード(AffiliationCode)の先頭3文字が、特定の値でないレコードは取得できないこと")
            fun case4() {
                // setup
                val branch = stubBranch(
                    code = "100000",
                    name = "テスト",
                    company = Company.DaitouKentakuPartners,
                )

                dslContext.saveEmployeeMasterPojo(
                    employee.copy(
                        jobTypeCode = "100",
                        affiliationCode = "${branch.code.getPrefix()}800"
                    )
                )
                dslContext.saveHrCategoryTableBPojo(hrCategory)

                // execute
                val actual = repository.findBy(branch)

                // verify
                assertEquals(0, actual.size)
            }

            @Test
            @DisplayName("従業員の職種CD（JobTypeCode）が特定の値でないが、所属コード(AffiliationCode)の先頭3文字が、特定の値のレコードは取得できること")
            fun case5() {
                // setup
                val branch = stubBranch(
                    code = "579000",
                    name = "テスト",
                    company = Company.DaitouKentakuPartners,
                )

                dslContext.saveEmployeeMasterPojo(
                    employee.copy(
                        jobTypeCode = "100",
                        affiliationCode = "${branch.code.getPrefix()}800"
                    )
                )
                dslContext.saveHrCategoryTableBPojo(hrCategory)

                // execute
                val actual = repository.findBy(branch)

                // verify
                assertEquals(1, actual.size)
            }
        }

        @Nested
        @DisplayName("人事区分テーブルB（HrCategoryTableB）と結合できた従業員のレコードのみが取得できること")
        inner class Scenario1x3 {

            @Test
            @DisplayName("従業員マスタの役職CDに合致する人事区分テーブルBのレコードが存在しない場合、その他条件に合致しても検索対象にならないこと")
            fun case1() {
                // setup
                dslContext.saveEmployeeMasterPojo(
                    employee.copy(positionCode = "60")
                )
                dslContext.saveHrCategoryTableBPojo(
                    hrCategory.copy(code = "70")
                )

                // execute
                val actual = repository.findBy(targetBranch)

                // verify
                assertEquals(0, actual.size)
            }

            @Test
            @DisplayName("従業員マスタの役職CDに合致する人事区分テーブルBの種類区分が「13」でない場合、その他条件に合致しても検索対象にならないこと")
            fun case2() {
                // setup
                dslContext.saveEmployeeMasterPojo()
                dslContext.saveHrCategoryTableBPojo(
                    hrCategory.copy(typeCategory = "26")
                )

                // execute
                val actual = repository.findBy(targetBranch)

                // verify
                assertEquals(0, actual.size)
            }
        }
    }

    @Nested
    @DisplayName("指定された従業員コードに紐づく所属支店コードを取得できること")
    inner class Scenario2 {

        private val employee = stubEmployeeMasterPojo()

        @Test
        @DisplayName("従業員の会社が大東建託の場合、所属CDと所属リーシング店舗対応表から所属支店コードを取得できること")
        fun case1() {
            // setup
            dslContext.saveEmployeeMasterPojo(
                employee.copy(
                    affiliationCode = "${LEASING_BRANCH_CD}800",
                    companyCode = Company.DaitouKentaku.code
                )
            )

            // execute
            val actual = repository.getAffiliationBranchCode(
                employee.getEmployee()!!.code
            )

            // verify
            assertEquals(LEASING_STORE_CD, actual?.getValue())
        }

        @Test
        @DisplayName("従業員の会社が大東建託パートナーズの場合、所属CD=営業所コードと大東建物管理対応表から所属支店コードを取得できること")
        fun case2() {
            // setup
            dslContext.saveEmployeeMasterPojo(
                employee.copy(
                    affiliationCode = "${OFFICE_CODE}931",
                    companyCode = Company.DaitouKentakuPartners.code
                )
            )

            // execute
            val actual = repository.getAffiliationBranchCode(
                employee.getEmployee()!!.code
            )

            // verify
            assertEquals(AFFILIATION_BRANCH_CODE, actual?.getValue())
        }

        @Test
        @DisplayName("従業員の会社が大東建託パートナーズだが、所属CD=営業所コードと大東建物管理対応表から所属支店コードを取得できない場合、営業所コードが返却されること")
        fun case3() {
            // setup
            dslContext.saveEmployeeMasterPojo(
                employee.copy(
                    affiliationCode = "111931",
                    companyCode = Company.DaitouKentakuPartners.code
                )
            )

            // execute
            val actual = repository.getAffiliationBranchCode(
                employee.getEmployee()!!.code
            )

            // verify
            assertEquals("111", actual?.getValue())
        }

        @Test
        @DisplayName("従業員の会社が大東建託リーシングの場合、所属CDからそのまま支店コードを返却すること")
        fun case4() {
            // setup
            dslContext.saveEmployeeMasterPojo(
                employee.copy(
                    affiliationCode = KT_ALL_BRANCH_CODE.take(3),
                    companyCode = Company.DaitouKentakuLeasing.code
                )
            )

            // execute
            val actual = repository.getAffiliationBranchCode(
                employee.getEmployee()!!.code
            )

            // verify
            assertEquals(KT_ALL_BRANCH_CODE.take(3), actual?.getValue())
        }
    }
}
