/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * 駅マスタ 既存システム物理名: EZF61P
 */
@Suppress("UNCHECKED_CAST")
data class StationMasterPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updater: String? = null,
    var lineCode: String,
    var stationCode: String,
    var lineName: String? = null,
    var lineShortName: String? = null,
    var stationName: String? = null,
    var stationShortName: String? = null,
    var transferStationBlockCode: String? = null,
    var transferStationCode: String? = null,
    var startYearMonth: Int? = null,
    var endYearMonth: Int? = null,
    var blockCode: String? = null,
    var lineCode_3Digit: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: StationMasterPojo = other as StationMasterPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.lineCode != o.lineCode)
            return false
        if (this.stationCode != o.stationCode)
            return false
        if (this.lineName == null) {
            if (o.lineName != null)
                return false
        }
        else if (this.lineName != o.lineName)
            return false
        if (this.lineShortName == null) {
            if (o.lineShortName != null)
                return false
        }
        else if (this.lineShortName != o.lineShortName)
            return false
        if (this.stationName == null) {
            if (o.stationName != null)
                return false
        }
        else if (this.stationName != o.stationName)
            return false
        if (this.stationShortName == null) {
            if (o.stationShortName != null)
                return false
        }
        else if (this.stationShortName != o.stationShortName)
            return false
        if (this.transferStationBlockCode == null) {
            if (o.transferStationBlockCode != null)
                return false
        }
        else if (this.transferStationBlockCode != o.transferStationBlockCode)
            return false
        if (this.transferStationCode == null) {
            if (o.transferStationCode != null)
                return false
        }
        else if (this.transferStationCode != o.transferStationCode)
            return false
        if (this.startYearMonth == null) {
            if (o.startYearMonth != null)
                return false
        }
        else if (this.startYearMonth != o.startYearMonth)
            return false
        if (this.endYearMonth == null) {
            if (o.endYearMonth != null)
                return false
        }
        else if (this.endYearMonth != o.endYearMonth)
            return false
        if (this.blockCode == null) {
            if (o.blockCode != null)
                return false
        }
        else if (this.blockCode != o.blockCode)
            return false
        if (this.lineCode_3Digit == null) {
            if (o.lineCode_3Digit != null)
                return false
        }
        else if (this.lineCode_3Digit != o.lineCode_3Digit)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + this.lineCode.hashCode()
        result = prime * result + this.stationCode.hashCode()
        result = prime * result + (if (this.lineName == null) 0 else this.lineName.hashCode())
        result = prime * result + (if (this.lineShortName == null) 0 else this.lineShortName.hashCode())
        result = prime * result + (if (this.stationName == null) 0 else this.stationName.hashCode())
        result = prime * result + (if (this.stationShortName == null) 0 else this.stationShortName.hashCode())
        result = prime * result + (if (this.transferStationBlockCode == null) 0 else this.transferStationBlockCode.hashCode())
        result = prime * result + (if (this.transferStationCode == null) 0 else this.transferStationCode.hashCode())
        result = prime * result + (if (this.startYearMonth == null) 0 else this.startYearMonth.hashCode())
        result = prime * result + (if (this.endYearMonth == null) 0 else this.endYearMonth.hashCode())
        result = prime * result + (if (this.blockCode == null) 0 else this.blockCode.hashCode())
        result = prime * result + (if (this.lineCode_3Digit == null) 0 else this.lineCode_3Digit.hashCode())
        return result
    }
}
