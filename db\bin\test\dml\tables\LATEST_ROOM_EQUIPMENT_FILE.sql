truncate table LATEST_ROOM_EQUIPMENT_FILE;
insert into LATEST_ROOM_EQUIPMENT_FILE (CREATION_DATE, CREATION_TIME, UPDATE_DATE, UPDATE_TIME, UPDATE_PROGRAM_ID, UPDATER, LOGICAL_DELETE_SIGN, BUILDING_CODE, ROOM_CODE, EQUIPMENT_CODE) values
 (20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000000101', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000000201', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000000401', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000001101', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000001301', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000001401', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000001501', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000001502', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000001801', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000003101', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000003501', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000003901', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000004201', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000006401', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000006501', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000007601', '01010', '001')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000007701', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000009101', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000009701', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000009801', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010001', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010001', '01020', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010201', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010401', '01010', '060')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010401', '01010', '032')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010401', '01010', '015')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010401', '01020', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010401', '01030', '032')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010401', '01040', '060')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010401', '01040', '032')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010401', '01050', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010601', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010701', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000010801', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000011001', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000011201', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000011701', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000011702', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000011901', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000012201', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000012301', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000013401', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000013402', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000013501', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000014501', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000014502', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000015001', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000016101', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000016901', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017201', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017201', '01020', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017201', '01030', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017201', '01040', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017201', '01050', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017201', '01060', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017401', '01030', '060')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017401', '01030', '032')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017401', '01010', '001')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017401', '01020', '001')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017401', '01030', '001')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017401', '01040', '001')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017401', '01050', '001')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017501', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017501', '01020', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017501', '01030', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017501', '01040', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017501', '01050', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017501', '01060', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017501', '01070', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017501', '01080', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017901', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017901', '01020', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017901', '01030', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017901', '01040', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017901', '01050', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017902', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017902', '01020', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017902', '01030', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017902', '01040', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000017902', '01050', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000018201', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000019601', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000019901', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000020801', '01010', '099')
,(20090519, 110008, 20160610, 132754, 'EBC100R', '059361', 0, '000020901', '01010', '060')
,(20090519, 110008, 20160610, 132754, 'EBC100R', '059361', 0, '000020901', '01010', '032')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000020901', '01020', '060')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000020901', '01020', '032')
,(20090519, 110008, 20210517, 125801, 'EBC100R', '054463', 0, '000020901', '01030', '060')
,(20090519, 110008, 20210517, 125801, 'EBC100R', '054463', 0, '000020901', '01030', '032')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000020901', '01050', '060')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000020901', '01050', '032')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000020901', '01050', '001')
,(20090519, 110008, 20160610, 132754, 'EBC100R', '059361', 0, '000020901', '01010', '015')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000020901', '01020', '015')
,(20090519, 110008, 20210517, 125801, 'EBC100R', '054463', 0, '000020901', '01030', '015')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000020901', '01040', '015')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000020901', '01050', '015')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000021101', '01010', '099')
,(20090519, 110008, 20090519, 110008, 'ETB240R', 'ｾｯﾄｱｯﾌﾟ', 0, '000021201', '01010', '032')
;
