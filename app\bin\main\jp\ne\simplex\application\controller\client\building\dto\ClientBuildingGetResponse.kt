package jp.ne.simplex.application.controller.client.building.dto

import jp.ne.simplex.application.model.Building

data class ClientBuildingGetResponse private constructor(
    val buildingCode: String,
    val buildingName: String,
) {

    companion object {
        fun of(building: Building): ClientBuildingGetResponse {
            return ClientBuildingGetResponse(
                buildingCode = building.code.value,
                buildingName = building.name.value,
            )
        }
    }
}
