/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * This class is generated by jOOQ.
 */
@Suppress("UNCHECKED_CAST")
data class SpecialBuildingMasterVPojo(
    var buildingCd: String? = null,
    var identificationCategory: String? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: SpecialBuildingMasterVPojo = other as SpecialBuildingMasterVPojo
        if (this.buildingCd == null) {
            if (o.buildingCd != null)
                return false
        }
        else if (this.buildingCd != o.buildingCd)
            return false
        if (this.identificationCategory == null) {
            if (o.identificationCategory != null)
                return false
        }
        else if (this.identificationCategory != o.identificationCategory)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.buildingCd == null) 0 else this.buildingCd.hashCode())
        result = prime * result + (if (this.identificationCategory == null) 0 else this.identificationCategory.hashCode())
        return result
    }
}
