package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.batch.BatchType
import jp.ne.simplex.db.jooq.gen.tables.references.BATCH_EXECUTE_HISTORY
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import org.jooq.DSLContext
import org.jooq.Field
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class BatchExecuteHistoryRepository(
    private val dslContext: DSLContext
) : BatchExecuteHistoryRepositoryInterface {

    override fun register(
        batchType: BatchType,
        executeDateTime: LocalDateTime,
    ) {
        dslContext.insertInto(BATCH_EXECUTE_HISTORY)
            .set(BATCH_EXECUTE_HISTORY.BATCH_TYPE, batchType.code)
            .set(BATCH_EXECUTE_HISTORY.EXECUTION_DATETIME, executeDateTime)
            .set(
                BATCH_EXECUTE_HISTORY.EXECUTION_DATE,
                executeDateTime.yyyyMMdd()
            )
            .execute()

    }

    override fun upsert(
        batchType: BatchType,
        executeDateTime: LocalDateTime,
    ) {

        val insertTargets = mapOf<Field<*>, Any?>(
            BATCH_EXECUTE_HISTORY.BATCH_TYPE to batchType.code,
            BATCH_EXECUTE_HISTORY.EXECUTION_DATETIME to executeDateTime,
            BATCH_EXECUTE_HISTORY.EXECUTION_DATE to executeDateTime.yyyyMMdd(),
        )

        val updateTargets = mapOf<Field<*>, Any?>(
            BATCH_EXECUTE_HISTORY.EXECUTION_DATETIME to executeDateTime,
        )

        dslContext.insertInto(BATCH_EXECUTE_HISTORY)
            .set(insertTargets)
            .onDuplicateKeyUpdate()
            .set(updateTargets)
            .execute()

    }
}

interface BatchExecuteHistoryRepositoryInterface {
    /**
     * バッチ実行履歴を登録する
     * 一意制約違反（バッチ名と開始日時の組み合わせが重複）の場合は例外をスロー
     *
     * @param batchType バッチ名
     * @param executeDateTime 開始日時
     */
    fun register(
        batchType: BatchType,
        executeDateTime: LocalDateTime,
    )

    fun upsert(
        batchType: BatchType,
        executeDateTime: LocalDateTime,
    )
}
