/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.time.LocalDateTime

import jp.ne.simplex.db.jooq.gen.tables.ParkingReservationVTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingReservationVPojo

import org.jooq.impl.TableRecordImpl


/**
 * This class is generated by jOOQ.
 */
@Suppress("UNCHECKED_CAST")
open class ParkingReservationVRecord private constructor() : TableRecordImpl<ParkingReservationVRecord>(ParkingReservationVTable.PARKING_RESERVATION_V) {

    open var parkingReservationId: String?
        set(value): Unit = set(0, value)
        get(): String? = get(0) as String?

    open var buildingCode: String?
        set(value): Unit = set(1, value)
        get(): String? = get(1) as String?

    open var parkingLotCode: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var reserveType: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var reserveStatus: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var receptionDate: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var receptionStaff: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var reserverName: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var reserverTel: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var remarks: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var reserverSystem: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var reserveStartDatetime: LocalDateTime?
        set(value): Unit = set(11, value)
        get(): LocalDateTime? = get(11) as LocalDateTime?

    open var reserveEndDatetime: LocalDateTime?
        set(value): Unit = set(12, value)
        get(): LocalDateTime? = get(12) as LocalDateTime?

    open var linkedBuildingCode: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var linkedRoomCode: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var eboardParkingReservationId: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var creationDate: Int?
        set(value): Unit = set(16, value)
        get(): Int? = get(16) as Int?

    open var creationTime: Int?
        set(value): Unit = set(17, value)
        get(): Int? = get(17) as Int?

    open var creator: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var updateDate: Int?
        set(value): Unit = set(19, value)
        get(): Int? = get(19) as Int?

    open var updateTime: Int?
        set(value): Unit = set(20, value)
        get(): Int? = get(20) as Int?

    open var updater: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var deleteFlag: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    /**
     * Create a detached, initialised ParkingReservationVRecord
     */
    constructor(parkingReservationId: String? = null, buildingCode: String? = null, parkingLotCode: String? = null, reserveType: String? = null, reserveStatus: String? = null, receptionDate: Int? = null, receptionStaff: String? = null, reserverName: String? = null, reserverTel: String? = null, remarks: String? = null, reserverSystem: String? = null, reserveStartDatetime: LocalDateTime? = null, reserveEndDatetime: LocalDateTime? = null, linkedBuildingCode: String? = null, linkedRoomCode: String? = null, eboardParkingReservationId: String? = null, creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, deleteFlag: String? = null): this() {
        this.parkingReservationId = parkingReservationId
        this.buildingCode = buildingCode
        this.parkingLotCode = parkingLotCode
        this.reserveType = reserveType
        this.reserveStatus = reserveStatus
        this.receptionDate = receptionDate
        this.receptionStaff = receptionStaff
        this.reserverName = reserverName
        this.reserverTel = reserverTel
        this.remarks = remarks
        this.reserverSystem = reserverSystem
        this.reserveStartDatetime = reserveStartDatetime
        this.reserveEndDatetime = reserveEndDatetime
        this.linkedBuildingCode = linkedBuildingCode
        this.linkedRoomCode = linkedRoomCode
        this.eboardParkingReservationId = eboardParkingReservationId
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.deleteFlag = deleteFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ParkingReservationVRecord
     */
    constructor(value: ParkingReservationVPojo?): this() {
        if (value != null) {
            this.parkingReservationId = value.parkingReservationId
            this.buildingCode = value.buildingCode
            this.parkingLotCode = value.parkingLotCode
            this.reserveType = value.reserveType
            this.reserveStatus = value.reserveStatus
            this.receptionDate = value.receptionDate
            this.receptionStaff = value.receptionStaff
            this.reserverName = value.reserverName
            this.reserverTel = value.reserverTel
            this.remarks = value.remarks
            this.reserverSystem = value.reserverSystem
            this.reserveStartDatetime = value.reserveStartDatetime
            this.reserveEndDatetime = value.reserveEndDatetime
            this.linkedBuildingCode = value.linkedBuildingCode
            this.linkedRoomCode = value.linkedRoomCode
            this.eboardParkingReservationId = value.eboardParkingReservationId
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            resetChangedOnNotNull()
        }
    }
}
