package jp.ne.simplex.configuration

import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import jp.ne.simplex.authentication.ApiKeyVerifier
import jp.ne.simplex.authentication.AuthFilter
import jp.ne.simplex.authentication.JwtVerifier
import jp.ne.simplex.authentication.saml.SamlAuthSuccessHandler
import jp.ne.simplex.authentication.saml.SingleSignOnConfig
import jp.ne.simplex.exception.ErrorResponse
import jp.ne.simplex.exception.ErrorType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.web.servlet.FilterRegistrationBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.access.intercept.AuthorizationFilter
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.cors.UrlBasedCorsConfigurationSource

@Configuration
@EnableWebSecurity
class WebApiSecurityConfig(
    private val ssoConfig: SingleSignOnConfig
) {

    @Value("\${swagger.enabled}")
    private val useSwagger = false

    @Value("\${management.endpoints.web.exposure.include}")
    private val healthCheckPath = "/health"

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var samlAuthSuccessHandler: SamlAuthSuccessHandler

    private val swaggerApiList = arrayOf(
        "/swagger-ui/**",
        "/api-docs/**",
        "/api-docs.yaml"
    )

    private val publicApiList = arrayOf(
        healthCheckPath,
        "/auth/login",
        "/auth/login/parking-details",
    )

    companion object {
        private const val SAML_LOGIN_PATH = "/auth/acs"
        private const val SAML_METADATA_PATH = "/auth/saml/metadata"

        val eBoardApiList = arrayOf(
            "/login/parking-details",
            "/update-temporary-reservation"
        )
        val kimaRoomSignApiList = arrayOf(
            "/parking-detail",
            "/update-parking-reservation"
        )
        val dkPortalApiList = arrayOf(
            "/parking-detail"
        )
        val welcomeParkApiList = arrayOf(
            "/check-parking-reservable-for-welcomepark",
            "/update-parking-reservation-for-welcomepark"
        )
    }

    @Bean
    fun securityFilterChain(
        http: HttpSecurity,
        jwtVerificationFilter: JwtVerifier,
        apiKeyVerificationFilter: ApiKeyVerifier
    ): SecurityFilterChain {
        http
            .cors { }
            .csrf { cf -> cf.disable() }
            .formLogin { fl -> fl.disable() }

        // Swagger利用時のみ、SwaggerUI表示等に必要なエンドポイントへのアクセスを全て許可する
        if (useSwagger) {
            http.authorizeHttpRequests { request ->
                request.requestMatchers(*swaggerApiList).permitAll()
            }
        }

        //saml sso setting
        if (ssoConfig.enabled) {
            http.authorizeHttpRequests { request ->
                request.requestMatchers(SAML_LOGIN_PATH, SAML_METADATA_PATH).permitAll()
            }.saml2Login { saml2Login ->
                saml2Login
                    .loginProcessingUrl(SAML_LOGIN_PATH) // POST endpoint for SAML response from idp
                    .successHandler(samlAuthSuccessHandler.apply {
                        setDefaultTargetUrl(ssoConfig.saml.dkLinkUrl)
                    }) // issue jwt token then redirect to homepage
                    .failureUrl(ssoConfig.saml.dkLinkUrl + "/#login") // redirect to log in without jwt token
            }.saml2Metadata { saml2Metadata ->
                saml2Metadata.metadataUrl(SAML_METADATA_PATH)
            }
        }

        http
            .authorizeHttpRequests { request ->
                request
                    .requestMatchers(*publicApiList).permitAll()
                    .anyRequest().authenticated()
            }
            // デフォルトのAuthorizationManagerで、SecurityContext内の認証情報を元に、認証の成否を判断するため、
            // AuthorizationFilter の 直前にフィルタを注入している
            // 詳細は、https://spring.pleiades.io/spring-security/reference/servlet/authorization/authorize-http-requests.html
            .addFilterBefore(
                AuthFilter(apiKeyVerificationFilter, jwtVerificationFilter),
                AuthorizationFilter::class.java
            )
            .exceptionHandling { exceptionConfig ->
                exceptionConfig
                    .authenticationEntryPoint { request, response, _ ->
                        response.wrapUnauthorizedResponse(request)
                    }
                    .accessDeniedHandler { request, response, _ ->
                        response.wrapUnauthorizedResponse(request)
                    }
            }


        return http.build()
    }

    @Bean
    fun loggingFilter(): FilterRegistrationBean<WebApiLoggingFilter> {
        return FilterRegistrationBean(WebApiLoggingFilter()).apply { this.order = Int.MIN_VALUE }
    }

    @Bean
    // TODO AWS環境で、Webとサーバーで同一ドメインを割利当てたら以下のコメントアウトを外し、ローカルのみCORSを適用しないようにする
//    @Profile("dev")
    fun corsConfigurationSource(): CorsConfigurationSource {
        val configuration = CorsConfiguration()
        configuration.allowedOriginPatterns = listOf("*")
        configuration.allowedHeaders = listOf("*")
        configuration.allowedMethods = listOf("*")
        configuration.allowCredentials = true

        val source = UrlBasedCorsConfigurationSource()
        source.registerCorsConfiguration("/**", configuration)

        return source
    }

    private fun HttpServletResponse.wrapUnauthorizedResponse(request: HttpServletRequest): HttpServletResponse {
        val errorType = request.getAttribute("error_type")
        val type = errorType as? ErrorType ?: ErrorType.UNEXPECTED_AUTHENTICATION_ERROR

        this.status = HttpStatus.UNAUTHORIZED.value()
        this.contentType = MediaType.APPLICATION_JSON_VALUE
        this.characterEncoding = Charsets.UTF_8.name()

        if (errorType == ErrorType.ACCESS_TOKEN_EXPIRED) {
            // トークンの有効期限切れ時は、以下の値をクライアントで参照して、トークンリフレッシュしているため、
            // ErrorType.ACCESS_TOKEN_EXPIRED の場合は、特別に以下をレスポンスとして返却する
            this.writer.write("""{"error":"expired_access_token"}""")
        } else {
            this.writer.write(objectMapper.writeValueAsString(ErrorResponse.of(type, request)))
        }
        return this
    }
}
