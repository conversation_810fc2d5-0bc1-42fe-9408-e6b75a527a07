package jp.ne.simplex.application.controller.client.parking

import jp.ne.simplex.application.controller.client.parking.dto.*
import jp.ne.simplex.application.service.*
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.openapi.ApiDefinition
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.runBlocking
import org.springframework.http.MediaType
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/parking")
class ClientParkingController(
    private val parkingDetailsService: ParkingDetailsService,
    private val parkingVehicleInfoService: ParkingVehicleInfoService,
    private val parkingReservationService: ParkingReservationService,
    private val parkingContractService: ParkingContractService,
    private val parkingLotAvailabilityService: ParkingLotAvailabilityService,
    private val buildingService: BuildingService,
) {

    @GetMapping("/parking-detail")
    @ApiDefinition(summary = "駐車場詳細取得API")
    fun getParkingDetail(
        @ModelAttribute request: ClientParkingDetailRequest,
    ): ClientParkingDetailResponse = runBlocking {
        val orderCode = request.toServiceInterface()
        coroutineScope {
            // 並列処理
            val parkingListDeferred = async { suspend { parkingDetailsService.getParkingList(orderCode, true) }() }
            val roomCountsDeferred = async { suspend { parkingDetailsService.getRoomCounts(orderCode) }() }
            val vacantRoomsDeferred = async { suspend { parkingDetailsService.getVacantRooms(orderCode) }() }

            // parkingListを待つ
            val parkingList = parkingListDeferred.await()
            val contractPossibilityDeferred = async { suspend { parkingContractService.getParkingContractPossibility(orderCode, parkingList) }() }

            // その他の結果を待つ
            val contractPossibility = contractPossibilityDeferred.await()
            val roomCounts = roomCountsDeferred.await()
            val vacantRooms = vacantRoomsDeferred.await()

            return@coroutineScope ClientParkingDetailResponse.from(
                parkingList,
                contractPossibility,
                roomCounts,
                vacantRooms,
            )
        }
    }

    @PutMapping("/vehicle-info")
    @ApiDefinition(summary = "駐車場車種情報更新API")
    fun updateVehicleInfo(
        @RequestBody request: ClientParkingVehicleInfoUpdateRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        return parkingVehicleInfoService.update(
            authInfo.getRequestUser(),
            request.toServiceInterface()
        )
    }

    @PostMapping("/reservation")
    @ApiDefinition(summary = "駐車場予約情報登録API")
    fun registerReservation(
        @RequestBody request: ClientParkingReservationRegisterRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        return parkingReservationService.updateReservation(
            authInfo, request.toServiceInterface()
        )
    }

    @DeleteMapping("/reservation")
    @ApiDefinition(summary = "駐車場予約情報取消API")
    fun cancelReservation(
        @RequestBody request: ClientParkingReservationCancelRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        return parkingReservationService.updateReservation(
            authInfo, request.toServiceInterface()
        )
    }

    @PutMapping("/reservations")
    @ApiDefinition(summary = "駐車場予約情報更新API")
    fun updateReservations(
        @RequestBody request: ClientParkingReservationsUpdateRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ): ClientParkingReservationsUpdateResponse {
        val res = parkingReservationService.updateReservations(
            authInfo, request.toServiceInterface()
        )
        return ClientParkingReservationsUpdateResponse.fromServiceInterface(res)
    }

    @PostMapping("/is-auto-judge")
    @ApiDefinition(summary = "駐車場要問合せ設定更新API")
    fun updateIsAutoJudge(
        @RequestBody request: ClientParkingIsAutoJudgeUpdateRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        return parkingContractService.updateIsAutoJudge(
            authInfo.getRequestUser(),
            request.toServiceInterface()
        )
    }

    @PutMapping("/parking-lot-availability")
    @ApiDefinition(summary = "駐車場区画利用可否更新API")
    fun updateParkingLotAvailability(
        @RequestBody request: ClientParkingLotAvailabilityUpdateRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        return parkingLotAvailabilityService.update(
            authInfo.getRequestUser(),
            request.toServiceInterface(),
        )
    }

    @PostMapping("/register-parking-image", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @ApiDefinition(summary = "駐車場配置図画像登録API")
    fun registerParkingImage(
        @ModelAttribute request: ClientParkingImageRegisterRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        val param = request.toServiceInterface()
        buildingService.registerParkingImage(authInfo.getRequestUser(), param)
    }

    @DeleteMapping("/delete-parking-image")
    @ApiDefinition(summary = "駐車場配置図画像削除API")
    fun deleteParkingImage(
        @RequestBody request: ClientParkingImageDeleteRequest,
        @AuthenticationPrincipal authInfo: AuthInfo,
    ) {
        val param = request.toServiceInterface()
        buildingService.deleteParkingImage(authInfo.getRequestUser(), param)
    }
}
