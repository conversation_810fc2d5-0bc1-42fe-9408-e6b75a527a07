package jp.ne.simplex.application.model

import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmm
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

/** 入居中物件データ作成バッチ用の事業用物件ワークデータ */
data class RoomInfoMasterWorkCommercial(
    /** 支店CD */
    val branchCode: String?,
    /** 状況 */
    val status: String?,
    /** 日付 */
    val date: Int?,
    /** 交渉 */
    val negotiation: String?,
    /** 建物CD */
    val buildingCode: String,
    /** 部屋CD */
    val roomCode: String,
    /** 家主名称 */
    val landlordName: String?,
    /** 所在地1 */
    val address1: String?,
    /** 所在地2 */
    val address2: String?,
    /** 家賃 */
    val rent: Int?,
    /** 共益費 */
    val commonFee: Int?,
    /** 礼金 */
    val keyMoney: Int?,
    /** 保証金（敷金） */
    val securityDeposit: Int?,
    /** 退居日 */
    val moveOutDate: Int?,
    /** 備考1 */
    val remarks1: String?,
    /** 備考2 */
    val remarks2: String?,
    /** ロッキー区分 */
    val rockyCategory: String?,
    /** 区分A */
    val categoryA: String?,
    /** 区分B */
    val categoryB: String?,
    /** FF区分 */
    val ffCategory: String?,
    /** AD区分 */
    val adCategory: String?,
    /** 新規既存区分 */
    val newExistingType: String?,
    /** 種別 */
    val category: String?,
    /** 面積 */
    val area: BigDecimal?,
    /** 都道府県CD */
    val prefectureCode: String?,
    /** 市区郡CD */
    val cityCode: String?,
    /** 町村字通称CD */
    val townCode: String?,
    /** 物件住所かな */
    val propertyAddressKana: String?,
    /** 町村かな */
    val townNameKana: String?,
    /** 部屋番号 */
    val roomNumber: String?,
    /** 課税 */
    val tax: String?,
    /** 完工日 */
    val constructionDate: Int?,
    /** 市区郡かな */
    val cityKana: String?,
    /** 用途別ソート */
    val usageSort: String?,
    /** 時間 */
    val time: Int?,
    /** 抽出用支店CD */
    val extractionBranchCode: String?,
    /** 所在地3 */
    val address3: String?,
) {
    companion object {

        fun from(
            source: RoomInfoMasterWorkSource
        ): RoomInfoMasterWorkCommercial {
            return RoomInfoMasterWorkCommercial(
                branchCode = "", // 空白固定
                status = getStatus(),
                date = getDate(source),
                negotiation = source.negotiationEmployeeName ?: "",
                buildingCode = source.buildingCode,
                roomCode = source.roomCode,
                landlordName = source.landlordName ?: "",
                address1 = source.townKanjiName ?: "",
                address2 = source.addressDetails ?: "",
                rent = source.rent?.let { it / 100 },
                commonFee = source.commonFee?.let { it / 1000 },
                keyMoney = source.keyMoneyAmount?.let { keyMoney ->
                    source.rent?.takeIf { it > 0 }?.let { rent -> keyMoney / rent }
                },
                securityDeposit = source.depositAmount?.let { deposit ->
                    source.rent?.takeIf { it > 0 }?.let { rent -> deposit / rent }
                },
                moveOutDate = source.moveOutDate?.yyyyMMdd()?.toInt(),
                remarks1 = "", // 空白固定
                remarks2 = "", // 空白固定
                rockyCategory = null, // null固定
                categoryA = null, // null固定
                categoryB = null, // null固定
                ffCategory = source.frontFreeRentSign?.toString(),
                adCategory = "", // 空白固定
                newExistingType = getNewExistingType(source),
                category = source.buildingTypeAbbreviation2,
                area = getArea(source),
                prefectureCode = source.prefectureCode,
                cityCode = source.cityCode,
                townCode = source.townCode,
                propertyAddressKana = source.addressDetails,
                townNameKana = source.townKanaName,
                roomNumber = source.roomNumber,
                tax = getTax(source),
                constructionDate = getConstructionDate(source),
                cityKana = source.townKanaName,
                usageSort = getUsageSort(source),
                time = LocalDateTime.now().HHmm().toInt(),
                extractionBranchCode = source.tenantRecruitmentBranchCode,
                address3 = (source.townKanjiName ?: "") + (source.addressDetails ?: ""),
            )
        }

        private fun getStatus(): String {
            // テナントキャンセルDBが連携されないため空白をセット
            return ""
        }

        private fun getDate(source: RoomInfoMasterWorkSource): Int? {
            return when (getStatus()) {
                "申" -> source.moveInApplicationDate?.yyyyMMdd()?.toInt()
                "手" -> source.depositChangeDate?.yyyyMMdd()?.toInt()
                "確" -> source.leaseContractDate?.yyyyMMdd()?.toInt()
                else -> 0
            }
        }

        private fun getNewExistingType(source: RoomInfoMasterWorkSource): String? {
            return if (source.initialOccupancyDate == null || source.initialOccupancyDate.isAfter(
                    LocalDate.now()
                )
            ) "新築" else "既存"
        }

        private fun getArea(source: RoomInfoMasterWorkSource): BigDecimal? {
            return when (source.roomType) {
                "500", "510", "520" -> source.storeExclusiveAreaSquareMeters
                else -> source.officeFloorAreaSquareMeters
            }
        }

        private fun getTax(source: RoomInfoMasterWorkSource): String? {
            return if (source.taxDivision == "1") "課税" else null
        }

        private fun getConstructionDate(source: RoomInfoMasterWorkSource): Int? {
            return source.completionDeliveryDate?.yyyyMMdd()?.toInt()
                ?: source.completionExpectedDate?.yyyyMMdd()?.toInt()
        }

        private fun getUsageSort(source: RoomInfoMasterWorkSource): String? {
            return when (source.roomType) {
                "350" -> "01"
                "300" -> "04"
                "410" -> "05"
                "500" -> "07"
                "210" -> "08"
                "110" -> "09"
                "320" -> "14"
                "310" -> "06"
                "360" -> "02"
                "400" -> "12"
                "490" -> "13"
                "510" -> "10"
                "520" -> "11"
                "420" -> "03"
                else -> null
            }
        }
    }
}
