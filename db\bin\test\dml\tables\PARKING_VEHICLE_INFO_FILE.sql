truncate table PARKING_VEHICLE_INFO_FILE;
insert into PARKING_VEHICLE_INFO_FILE (CREATION_DATE, CREATION_TIME, CREATOR, CREATION_PROGRAM_ID, UPDATE_DATE, UPDATE_TIME, UPDATER, UPDATE_PROGRAM_ID, TENANT_CONTRACT_NUMBER, ROOM_CODE, TANDEM_SIGN, LAND_TRANSPORT_NAME_1, TYPE_1, BUSINESS_CATEGORY_1, LEFT_NUMBER_1, RIGHT_NUMBER_1, MANUFACTURER_DIVISION_1, CAR_MODEL_NAME_1, LIGHT_VEHICLE_SIGN_1, LAND_TRANSPORT_NAME_2, TYPE_2, BUSINESS_CATEGORY_2, LEFT_NUMBER_2, RIGHT_NUMBER_2, MANUFACTURER_DIVISION_2, CAR_<PERSON>ODEL_NAME_2, LIGHT_VEHICLE_SIGN_2, LAND_TRANSPORT_NAME_3, TYPE_3, BUSINESS_CATEGORY_3, LEFT_NUMBER_3, RIGHT_NUMBER_3, MANUFACTURER_DIVISION_3, CAR_MODEL_NAME_3, LIGHT_VEHICLE_SIGN_3, LAND_TRANSPORT_NAME_4, TYPE_4, BUSINESS_CATEGORY_4, LEFT_NUMBER_4, RIGHT_NUMBER_4, MANUFACTURER_DIVISION_4, CAR_MODEL_NAME_4, LIGHT_VEHICLE_SIGN_4, LAND_TRANSPORT_NAME_5, TYPE_5, BUSINESS_CATEGORY_5, LEFT_NUMBER_5, RIGHT_NUMBER_5, MANUFACTURER_DIVISION_5, CAR_MODEL_NAME_5, LIGHT_VEHICLE_SIGN_5, PARKING_CERT_ISSUE_SIGN_1, PARKING_CERT_COMMENT_1, PARKING_CERT_ISSUE_SIGN_2, PARKING_CERT_COMMENT_2, PARKING_CERT_ISSUE_SIGN_3, PARKING_CERT_COMMENT_3, PARKING_CERT_ISSUE_SIGN_4, PARKING_CERT_COMMENT_4, PARKING_CERT_ISSUE_SIGN_5, PARKING_CERT_COMMENT_5) values
 (20070206, 140057, '091884', 'pnEB800', 20070206, 140057, '091884', 'pnEB800', '00005539', '01040', '0', '浜松', '330', 'ろ', '12', '06', 6, 'レガシー', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090718, 110039, '092246', 'pnEB800', 20090718, 110039, '092246', 'pnEB800', '00017327', '02030', '0', '仙台', '500', 'つ', '34', '04', 3, 'フィット', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090618, 184144, '092246', 'pnEB800', 20090618, 184144, '092246', 'pnEB800', '00022874', '02010', '0', '宮城', '501', 'ゆ', '76', '63', 2, 'マーチ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090620, 174235, '092246', 'pnEB800', 20090620, 174235, '092246', 'pnEB800', '00023575', '01040', '0', '仙台', '300', 'た', '16', '09', 1, 'クラウン', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110204, 92402, '022951', 'pnEB800', 20110204, 174025, '022951', 'pnEB800', '00025177', '03040', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '現地７番？', 0, null, 0, null, 0, null, 0, null)
,(20120430, 91659, '059643', 'pnEB800', 20120430, 91659, '059643', 'pnEB800', '00026598', '02030', '0', '宮城', '502', 'た', '25', '48', 2, 'セレナ　黒', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110204, 92343, '022951', 'pnEB800', 20110204, 174001, '022951', 'pnEB800', '00031992', '03020', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '現地９番
現地９番？', 0, null, 0, null, 0, null, 0, null)
,(20070830, 111531, '092959', 'pnEB800', 20070830, 111531, '092959', 'pnEB800', '00032944', '01030', '0', '浜松', '501', 'て', '68', '35', 3, 'フィット', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090718, 113346, '092246', 'pnEB800', 20090718, 113346, '092246', 'pnEB800', '00043022', '01020', '0', '宮城', '500', 'の', '34', '76', 2, 'アベニール', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110202, 174450, '094384', 'pnEB800', 20110202, 174450, '094384', 'pnEB800', '00046910', '01040', '0', null, null, null, '88', '23', 1, 'ハイエース', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090618, 144050, '092024', 'pnEB800', 20090618, 144050, '092024', 'pnEB800', '00050284', '02030', '0', '宮城', '50', 'ひ', '53', '98', 5, 'パジェロミニ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100929, 144252, '092423', 'pnEB800', 20110126, 93135, '048166', 'pnEB800', '00051273', '02010', '0', '八王子', '580', null, '22', '67', 8, 'ワゴンＲ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20120430, 91656, '059643', 'pnEB800', 20120430, 91656, '059643', 'pnEB800', '00058440', '02020', '0', '仙台', '580', 'い', '63', '19', 8, 'ワゴンR　シルバー', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20061016, 164413, '020634', 'pnEB800', 20061225, 120214, '091884', 'pnEB800', '00058894', '01020', '0', '浜松', '530', 'す', '50', '31', 1, 'カローラ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090312, 114340, '092959', 'pnEB800', 20090312, 114340, '092959', 'pnEB800', '00060590', '01030', '0', '浜松', '59', 'ほ', '77', '75', 2, 'ローレル', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090711, 145936, '092246', 'pnEB800', 20090711, 145936, '092246', 'pnEB800', '00060921', '02020', '0', '宮城', '50', 'に', '50', '30', 7, 'ムーブ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090812, 172340, '093781', 'pnEB800', 20090812, 172340, '093781', 'pnEB800', '00065072', '02010', '0', '宮城', '501', 'ゆ', '14', '00', 3, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20120430, 93347, '059643', 'pnEB800', 20120430, 93405, '059643', 'pnEB800', '00070178', '01040', '0', '仙台', '580', 'え', '27', '79', 3, 'ライフ　シルバー', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20101005, 144533, '050440', 'pnEB800', 20101005, 144533, '050440', 'pnEB800', '00073558', '01010', '0', '多摩', '502', 'の', '63', '73', 0, 'ヴィッツ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '10/10/05確認済', 0, null, 0, null, 0, null, 0, null)
,(20071205, 115024, '092959', 'pnEB800', 20071205, 115024, '092959', 'pnEB800', '00075639', '01030', '0', '浜松', '50', null, '63', '15', 0, 'トゥディ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090825, 203209, '092895', 'pnEB800', 20090825, 203209, '092895', 'pnEB800', '00075996', '01020', '0', '宮城', '501', 'に', '73', '67', 4, 'クラシック', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100929, 153559, '050440', 'pnEB800', 20100929, 153559, '050440', 'pnEB800', '00078756', '01020', '0', null, null, null, null, null, 0, '軽バン・軽トラック', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '10/9/29確認済', 0, null, 0, null, 0, null, 0, null)
,(20061106, 105308, '029880', 'pnEB800', 20061106, 105308, '029880', 'pnEB800', '00083027', '02030', '0', '多摩', '79', 'む', '54', '22', 2, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090526, 155026, '016995', 'pnEB800', 20090526, 155026, '016995', 'pnEB800', '00090563', '02020', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20080131, 104231, '092959', 'pnEB800', 20080131, 104231, '092959', 'pnEB800', '00097947', '01010', '0', '浜松', '50', 'る', '12', '82', 8, 'Ｋｅｉ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110204, 92322, '022951', 'pnEB800', 20110204, 173938, '022951', 'pnEB800', '00098837', '03010', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '敷地外？', 0, null, 0, null, 0, null, 0, null)
,(20100823, 153525, '091555', 'pnEB800', 20100823, 153525, '091555', 'pnEB800', '00098979', '01030', '0', '八王子', '500', 'ひ', '62', '11', 1, 'カローラスパシオ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20061201, 163805, '091884', 'pnEB800', 20110111, 152447, '094157', 'pnEB800', '00105012', '02050', '0', '浜松', '580', 'あ', '22', '16', 8, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090917, 162612, '021474', 'pnEB800', 20090917, 162612, '021474', 'pnEB800', '00105502', '03040', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 1, null, 0, null, 0, null, 0, null, 0, null)
,(20101228, 131933, '094475', 'pnEB800', 20101228, 131933, '094475', 'pnEB800', '00105603', '01020', '0', '所沢', '501', null, '81', '45', 2, 'ティーダ　ベージュ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100824, 131313, '050440', 'pnEB800', 20100824, 131457, '050440', 'pnEB800', '00106571', '01040', '0', '多摩', '502', 'そ', '22', '03', 2, 'セレナ（グレー）', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '10/8/24確認済', 0, null, 0, null, 0, null, 0, null)
,(20081217, 142056, '029227', 'pnEB800', 20081217, 142056, '029227', 'pnEB800', '00109858', '02020', '0', '浜松', '300', 'ふ', '17', '46', 4, 'ＲＸ-8', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20081217, 142120, '029227', 'pnEB800', 20081217, 142120, '029227', 'pnEB800', '00109860', '02040', '0', '浜松', '300', 'ほ', '71', '80', 3, 'アコード', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20060725, 201710, '036690', 'pnEB800', 20061213, 143416, '029227', 'pnEB800', '00110749', '01020', '0', '静岡', '300', 'の', '38', '03', 1, 'プリウス', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090720, 135647, '028889', 'pnEB800', 20090720, 135647, '028889', 'pnEB800', '00123162', '01010', '0', '宮城', '300', 'て', '63', '09', 0, 'ﾏｰｸⅡ ﾎﾜｲﾄ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110526, 95010, '092423', 'pnEB800', 20110526, 95010, '092423', 'pnEB800', '00126294', '01010', '0', '八王子', '531', 'の', '11', '30', 5, 'コルト　黒', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20070526, 113546, '029227', 'pnEB800', 20070526, 113546, '029227', 'pnEB800', '00127203', '01040', '0', '浜松', '300', 'ち', '68', '16', 2, 'セフィーロ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110725, 174255, '034813', 'pnEB800', 20110725, 174307, '034813', 'pnEB800', '00128245', '02010', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '４番も無料で契約中', 0, null, 0, null, 0, null, 0, null)
,(20110124, 174008, '092710', 'pnEB800', 20110124, 174008, '092710', 'pnEB800', '00129313', '01020', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '縦列で無断駐車あり、', 0, null, 0, null, 0, null, 0, null)
,(20090622, 121111, '092024', 'pnEB800', 20090622, 121111, '092024', 'pnEB800', '00130235', '01010', '0', '宮城', '502', 'さ', '34', '88', 1, 'ウィッシュ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110725, 105040, '092732', 'pnEB800', 20110725, 105040, '092732', 'pnEB800', '00132849', '02020', '0', '熊本', '52', 'は', '47', '34', 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090716, 173125, '092024', 'pnEB800', 20090716, 173125, '092024', 'pnEB800', '00134635', '01040', '0', '仙台', '500', 'つ', '87', '13', 2, 'キューブ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110417, 91935, '025509', 'pnEB800', 20110417, 91935, '025509', 'pnEB800', '00145292', '01010', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '実際は2台駐車3番', 0, null, 0, null, 0, null, 0, null)
,(20091125, 155353, '094024', 'pnEB800', 20111125, 92716, '092423', 'pnEB800', '00146199', '01010', '0', '八王子', null, null, '96', '65', 7, 'ミゼット　緑', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090814, 140611, '037265', 'pnEB800', 20090814, 140618, '037265', 'pnEB800', '00149959', '01010', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100107, 110422, '094024', 'pnEB800', 20100107, 110422, '094024', 'pnEB800', '00150195', '01030', '0', null, null, null, '97', '10', 0, 'インスパイア　シルバー', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090716, 155121, '092246', 'pnEB800', 20090716, 155121, '092246', 'pnEB800', '00150734', '02010', '0', '宮城', '580', 'け', '50', '54', 3, 'ライフ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110308, 152246, '093342', 'pnEB800', 20110308, 152246, '093342', 'pnEB800', '00152601', '02040', '0', null, null, null, null, null, 7, 'ミラ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110117, 100145, '025509', 'pnEB800', 20110417, 91943, '025509', 'pnEB800', '00153044', '01040', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '実際は2台駐車2番', 0, null, 0, null, 0, null, 0, null)
,(20110502, 153049, '093342', 'pnEB800', 20110502, 153049, '093342', 'pnEB800', '00153614', '02020', '0', null, null, '茶', null, null, 4, 'ビアンテ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100212, 100042, '094024', 'pnEB800', 20120106, 124807, '094475', 'pnEB800', '00156916', '02040', '0', null, null, null, '55', '25', 2, 'スカイライン　黒', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 1, '12/01/06', 0, null, 0, null, 0, null, 0, null)
,(20090618, 141104, '092024', 'pnEB800', 20090618, 141234, '092024', 'pnEB800', '00157492', '02010', '0', '仙台', '500', 'つ', '92', '21', 2, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110131, 145102, '094475', 'pnEB800', 20110131, 145102, '094475', 'pnEB800', '00159241', '02010', '0', null, null, null, null, null, 2, 'フリード', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100616, 160503, '092423', 'pnEB800', 20100616, 160503, '092423', 'pnEB800', '00161551', '02060', '0', '八王子', '580', 'す', '17', '94', 7, 'アトレーワゴン　白', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100929, 150124, '050440', 'pnEB800', 20100929, 150124, '050440', 'pnEB800', '00164912', '01010', '0', '多摩', '530', 'ち', '8', '14', 2, 'キューブ（シルバー）', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '10/9/29確認済', 0, null, 0, null, 0, null, 0, null)
,(20100929, 153130, '050440', 'pnEB800', 20100929, 153130, '050440', 'pnEB800', '00165223', '02060', '0', '多摩', '501', 'ひ', '37', '07', 2, 'キューブ水色', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '10/9/29確認済', 0, null, 0, null, 0, null, 0, null)
,(20101022, 141923, '050440', 'pnEB800', 20101022, 141935, '050440', 'pnEB800', '00166663', '01010', '0', '多摩', '480', 'か', '90', '77', 8, 'エブリィ　白', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '10/10/22確認済', 0, null, 0, null, 0, null, 0, null)
,(20100716, 152951, '021821', 'pnEB800', 20100716, 152951, '021821', 'pnEB800', '00170920', '02020', '0', '多摩', '580', 'せ', '16', '55', 7, 'ムーヴ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100929, 142630, '092423', 'pnEB800', 20100929, 142630, '092423', 'pnEB800', '00178535', '02010', '0', '八王子', '333', 'や', null, '1', 1, 'ヴォクシー　黒', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20061118, 114233, '091884', 'pnEB800', 20110202, 155340, '092959', 'pnEB800', '00192453', '01030', '0', '浜松', null, null, '49', '60', 3, 'オデッセイ', 0, '浜松', '501', null, '32', '68', 3, 'モビリオ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20061109, 161019, '091884', 'pnEB800', 20081222, 144030, '092959', 'pnEB800', '00192595', '01020', '0', '浜松', '33', 'の', '63', '94', 1, 'プラド', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20061106, 163211, '029227', 'pnEB800', 20061106, 163211, '029227', 'pnEB800', '00194540', '02010', '0', '浜松', '500', 'ま', '37', '87', 3, 'ＳＭーＸ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20061106, 163051, '029227', 'pnEB800', 20090421, 150523, '029227', 'pnEB800', '00195497', '01010', '1', '浜松', '500', 'は', '81', '30', 3, 'ストリーム', 0, '浜松', '500', 'は', '81', '30', 3, 'ライフ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20061128, 140107, '091884', 'pnEB800', 20061128, 140107, '091884', 'pnEB800', '00196749', '02030', '0', '山口', '500', 'せ', '30', '94', 1, 'ビスタ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110217, 160138, '094475', 'pnEB800', 20110217, 160138, '094475', 'pnEB800', '00199500', '01020', '0', null, '480', 'え', '59', '73', 4, '白', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090814, 110525, '092246', 'pnEB800', 20090814, 110525, '092246', 'pnEB800', '00201729', '01030', '0', '宮城', '50', 'や', '9', '56', 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20120321, 95156, '094384', 'pnEB800', 20120321, 95156, '094384', 'pnEB800', '00204474', '02010', '0', '仙台', '500', 'は', '38', '46', 2, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100714, 165547, '021821', 'pnEB800', 20100714, 165547, '021821', 'pnEB800', '00209954', '01020', '0', '多摩', '502', 'ね', '57', '41', 3, 'ステップワゴン', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100917, 181208, '042727', 'pnEB800', 20100917, 181208, '042727', 'pnEB800', '00210704', '01050', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090917, 165906, '093815', 'pnEB800', 20090917, 165906, '093815', 'pnEB800', '00216896', '03020', '0', '八王子', '500', 'に', '88', '61', 4, 'ファミリア', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110722, 161019, '092732', 'pnEB800', 20110722, 161019, '092732', 'pnEB800', '00216930', '01050', '0', '熊本', '501', 'ち', '59', '22', 8, 'スイフト', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20110727, 162043, '094475', 'pnEB800', 20110727, 162043, '094475', 'pnEB800', '00217794', '02010', '0', null, null, 'さ', '98', '75', 1, 'ｂＢ　黒', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20120411, 161813, '094319', 'pnEB800', 20120411, 161813, '094319', 'pnEB800', '00218365', '01040', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 1, '2012/04/11', 0, null, 0, null, 0, null, 0, null)
,(20110311, 114526, '093191', 'pnEB800', 20110311, 114526, '093191', 'pnEB800', '00218443', '02030', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090725, 131215, '093815', 'pnEB800', 20090725, 131215, '093815', 'pnEB800', '00219738', '01010', '0', '八王子', '583', 'え', '3', '30', 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090723, 133348, '028889', 'pnEB800', 20090723, 133348, '028889', 'pnEB800', '00220677', '01020', '0', '宮城', '502', 'た', '3', '66', 0, 'MR･S ｼﾙﾊﾞｰ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100929, 145824, '092423', 'pnEB800', 20110920, 171655, '094475', 'pnEB800', '00221324', '01020', '0', null, null, null, '94', '15', 0, 'イプサム　黒', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 1, '10/9/29', 0, null, 0, null, 0, null, 0, null)
,(20090724, 154600, '091555', 'pnEB800', 20090724, 154600, '091555', 'pnEB800', '00221921', '03020', '0', '八王子', '580', 'か', '69', '12', 8, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090626, 161850, '092024', 'pnEB800', 20090626, 161850, '092024', 'pnEB800', '00230378', '01020', '0', '宮城', '300', 'ね', '26', '52', 0, 'ＡＬＴＥＺＺＡ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20111108, 113239, '026761', 'pnEB800', 20120508, 121536, '031418', 'pnEB800', '00232189', '01020', '0', null, null, null, '82', '00', 8, 'スズキ・ＳＶ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100929, 152907, '050440', 'pnEB800', 20100929, 152907, '050440', 'pnEB800', '00233298', '02040', '0', '多摩', '502', 'ま', '75', '08', 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, '10/9/29確認済', 0, null, 0, null, 0, null, 0, null)
,(20111227, 161230, '092732', 'pnEB800', 20111227, 161230, '092732', 'pnEB800', '00235260', '01030', '0', '熊本', '500', null, '23', '68', 5, 'コルト　白', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090814, 110029, '092246', 'pnEB800', 20090814, 110029, '092246', 'pnEB800', '00235461', '02020', '0', '福島', '50', 'む', '38', '89', 7, 'ムーブ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100120, 105756, '093815', 'pnEB800', 20100120, 105756, '093815', 'pnEB800', '00241268', '02010', '0', '八王子', '501', 'さ', '28', '32', 0, 'インプレッサ　ブルー', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20111216, 101737, '094057', 'pnEB800', 20111216, 101737, '094057', 'pnEB800', '00241299', '01020', '0', null, null, null, null, null, 1, 'カローラ　シルバー', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090618, 142403, '092024', 'pnEB800', 20090618, 142403, '092024', 'pnEB800', '00242442', '02010', '0', '宮城', '580', 'か', '96', '14', 7, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20120130, 165434, '092412', 'pnEB800', 20120130, 165434, '092412', 'pnEB800', '00244973', '02020', '0', '奈良', '501', 'て', '83', '34', 2, 'ｳｲﾝｸﾞﾛｰﾄﾞ（ｸﾞﾚｰ）', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 1, null, 0, null, 0, null, 0, null, 0, null)
,(20100128, 132511, '093815', 'pnEB800', 20100128, 132511, '093815', 'pnEB800', '00245018', '01030', '0', '八王子', '300', 'は', '50', '81', 2, 'テラノ　白', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20111124, 112928, '093376', 'pnEB800', 20111124, 112928, '093376', 'pnEB800', '00245764', '01020', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100119, 120327, '093815', 'pnEB800', 20100119, 120327, '093815', 'pnEB800', '00249171', '01010', '0', '八王子', '300', 'ま', '52', '85', 0, 'レガシー　グレー', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090814, 125118, '017622', 'pnEB800', 20090814, 125118, '017622', 'pnEB800', '00249792', '02040', '0', '宮城', '300', 'つ', '21', '93', 3, 'オデッセイ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20080408, 112127, '092959', 'pnEB800', 20080408, 112127, '092959', 'pnEB800', '00255767', '01050', '0', '浜松', '501', 'と', '21', '25', 1, 'ランドクルーザープラド', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20120326, 120209, '094321', 'pnEB800', 20120326, 120209, '094321', 'pnEB800', '00257500', '01020', '0', null, null, null, null, null, 2, 'モコ（茶）', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100225, 103610, '094024', 'pnEB800', 20100225, 103610, '094024', 'pnEB800', '00258732', '02020', '0', '八王子', '580', 'せ', '15', '48', 7, 'エッセＸ　ブルー', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090718, 125209, '028889', 'pnEB800', 20090718, 125209, '028889', 'pnEB800', '00260063', '01030', '0', '宮城', '580', 'つ', '10', '62', 0, 'ﾜｺﾞﾝR', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100607, 162339, '092423', 'pnEB800', 20100607, 162339, '092423', 'pnEB800', '00261379', '02010', '0', null, null, null, '55', '12', 0, 'ミラ', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20090702, 114904, '028302', 'pnEB800', 20090702, 114904, '028302', 'pnEB800', '00261903', '01030', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100511, 113927, '092423', 'pnEB800', 20100511, 113927, '092423', 'pnEB800', '00277832', '01020', '0', '八王子', '580', 'せ', '85', '54', 7, 'ミラココア　アイボリー', 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 1, '車庫証明コメント', 0, null, 0, null, 0, null, 0, null)
,(20111121, 112751, '094356', 'pnEB800', 20111121, 112751, '094356', 'pnEB800', '00264249', '02040', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
,(20100325, 120052, '093726', 'pnEB800', 20100325, 120052, '093726', 'pnEB800', '00265141', '01020', '0', null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, null, null, null, null, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null)
;
