/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.MngOnly_24hSupportBuildingTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.MngOnly_24hSupportBuildingPojo

import org.jooq.impl.TableRecordImpl


/**
 * 管理のみ24時間サポート建物 既存システム物理名: HLB31P
 */
@Suppress("UNCHECKED_CAST")
open class MngOnly_24hSupportBuildingRecord private constructor() : TableRecordImpl<MngOnly_24hSupportBuildingRecord>(MngOnly_24hSupportBuildingTable.MNG_ONLY_24H_SUPPORT_BUILDING) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creationProgramId: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var creationTerminalId: String?
        set(value): Unit = set(3, value)
        get(): String? = get(3) as String?

    open var creationResponsibleId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updateDate: Int?
        set(value): Unit = set(5, value)
        get(): Int? = get(5) as Int?

    open var updateTime: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var updateTerminalId: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var updateResponsibleId: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var deleteFlag: Byte?
        set(value): Unit = set(10, value)
        get(): Byte? = get(10) as Byte?

    open var buildingCd: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var approvalDate: Int?
        set(value): Unit = set(12, value)
        get(): Int? = get(12) as Int?

    open var status: Byte?
        set(value): Unit = set(13, value)
        get(): Byte? = get(13) as Byte?

    open var approvalRejectionInputPerson: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    /**
     * Create a detached, initialised MngOnly_24hSupportBuildingRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creationProgramId: String? = null, creationTerminalId: String? = null, creationResponsibleId: String? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updateTerminalId: String? = null, updateResponsibleId: String? = null, deleteFlag: Byte? = null, buildingCd: String? = null, approvalDate: Int? = null, status: Byte? = null, approvalRejectionInputPerson: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creationProgramId = creationProgramId
        this.creationTerminalId = creationTerminalId
        this.creationResponsibleId = creationResponsibleId
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updateTerminalId = updateTerminalId
        this.updateResponsibleId = updateResponsibleId
        this.deleteFlag = deleteFlag
        this.buildingCd = buildingCd
        this.approvalDate = approvalDate
        this.status = status
        this.approvalRejectionInputPerson = approvalRejectionInputPerson
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised MngOnly_24hSupportBuildingRecord
     */
    constructor(value: MngOnly_24hSupportBuildingPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creationProgramId = value.creationProgramId
            this.creationTerminalId = value.creationTerminalId
            this.creationResponsibleId = value.creationResponsibleId
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updateTerminalId = value.updateTerminalId
            this.updateResponsibleId = value.updateResponsibleId
            this.deleteFlag = value.deleteFlag
            this.buildingCd = value.buildingCd
            this.approvalDate = value.approvalDate
            this.status = value.status
            this.approvalRejectionInputPerson = value.approvalRejectionInputPerson
            resetChangedOnNotNull()
        }
    }
}
