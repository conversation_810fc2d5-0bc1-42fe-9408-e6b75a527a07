-- TABLE: OFFICE_MASTER(事業所マスタ)

CREATE TABLE OFFICE_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    REFLECTION_DATE                              numeric(8,0)                  
,    DELETE_FLAG                                  varchar(1)                    
,    OFFICE_CODE                                  varchar(3)                    
,    OFFICE_KANA                                  varchar(16)                   
,    OFFICE_NAME                                  varchar(18)                   
,    OFFICE_ABBREVIATION_2                        varchar(6)                    
,    OFFICE_ABBREVIATION_3                        varchar(8)                    
,    OFFICE_ABBREVIATION_4                        varchar(10)                   
,    REPRESENTATIVE_LOCATION_CODE                 varchar(3)                    
,    PARENT_CHILD_CATEGORY                        varchar(1)                    
,    PARENT_OFFICE_CODE                           varchar(3)                    
,    BRANCH_OPENING_DATE                          numeric(8,0)                  
,    BRANCH_ESTABLISHMENT_DATE                    numeric(8,0)                  
,    REGION_CODE                                  varchar(1)                    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE OFFICE_MASTER IS '事業所マスタ 既存システム物理名: XXJGYP';
COMMENT ON COLUMN OFFICE_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: XXJ01D';
COMMENT ON COLUMN OFFICE_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: XXJ02H';
COMMENT ON COLUMN OFFICE_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: XXJ03D';
COMMENT ON COLUMN OFFICE_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: XXJ04H';
COMMENT ON COLUMN OFFICE_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: XXJ05P';
COMMENT ON COLUMN OFFICE_MASTER.UPDATER IS '更新者 既存システム物理名: XXJ06P';
COMMENT ON COLUMN OFFICE_MASTER.REFLECTION_DATE IS '反映日付 既存システム物理名: XXJ07D';
COMMENT ON COLUMN OFFICE_MASTER.DELETE_FLAG IS '削除フラグ 既存システム物理名: XXJ08S';
COMMENT ON COLUMN OFFICE_MASTER.OFFICE_CODE IS '事業所コード 既存システム物理名: XXJ10K';
COMMENT ON COLUMN OFFICE_MASTER.OFFICE_KANA IS '事業所カナ 既存システム物理名: XXJ11M';
COMMENT ON COLUMN OFFICE_MASTER.OFFICE_NAME IS '事業所名 既存システム物理名: XXJ12M';
COMMENT ON COLUMN OFFICE_MASTER.OFFICE_ABBREVIATION_2 IS '事業所略称2桁 既存システム物理名: XXJ13M';
COMMENT ON COLUMN OFFICE_MASTER.OFFICE_ABBREVIATION_3 IS '事業所略称3桁 既存システム物理名: XXJ14M';
COMMENT ON COLUMN OFFICE_MASTER.OFFICE_ABBREVIATION_4 IS '事業所略称4桁 既存システム物理名: XXJ15M';
COMMENT ON COLUMN OFFICE_MASTER.REPRESENTATIVE_LOCATION_CODE IS '代表所在地コード 既存システム物理名: XXJ16C';
COMMENT ON COLUMN OFFICE_MASTER.PARENT_CHILD_CATEGORY IS '親子区分 既存システム物理名: XXJ17B';
COMMENT ON COLUMN OFFICE_MASTER.PARENT_OFFICE_CODE IS '親事業所コード 既存システム物理名: XXJ18C';
COMMENT ON COLUMN OFFICE_MASTER.BRANCH_OPENING_DATE IS '営業所開設年月日 既存システム物理名: XXJ19D';
COMMENT ON COLUMN OFFICE_MASTER.BRANCH_ESTABLISHMENT_DATE IS '支店開設年月日 既存システム物理名: XXJ20D';
COMMENT ON COLUMN OFFICE_MASTER.REGION_CODE IS '地域コード 既存システム物理名: XXJ21C';
