-- TABLE: CONSUMPTION_TAX_RATE_MASTER(消費税率マスタ)

CREATE TABLE CONSUMPTION_TAX_RATE_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    CONSUMPTION_TAX_MANAGEMENT_CODE              varchar(2)                    
,    EFFECTIVE_START_DATE                         numeric(8,0)                  
,    EFFECTIVE_END_DATE                           numeric(8,0)                  
,    NATIONAL_TAX_CONSUMPTION_PERCENT             numeric(5,2)                  
,    LOCAL_TAX_CONSUMPTION_PERCENT                numeric(5,2)                  
,    CONSTRAINT UQ_CONSUMPTION_TAX_RATE_MASTER UNIQUE (CONSUMPTION_TAX_MANAGEMENT_CODE, EFFECTIVE_START_DATE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE CONSUMPTION_TAX_RATE_MASTER IS '消費税率マスタ 既存システム物理名: EZJA0P';
COMMENT ON COLUMN CONSUMPTION_TAX_RATE_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: EZJ01D';
COMMENT ON COLUMN CONSUMPTION_TAX_RATE_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: EZJ02H';
COMMENT ON COLUMN CONSUMPTION_TAX_RATE_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: EZJ03D';
COMMENT ON COLUMN CONSUMPTION_TAX_RATE_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: EZJ04H';
COMMENT ON COLUMN CONSUMPTION_TAX_RATE_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EZJ05N';
COMMENT ON COLUMN CONSUMPTION_TAX_RATE_MASTER.UPDATER IS '更新者 既存システム物理名: EZJ06C';
COMMENT ON COLUMN CONSUMPTION_TAX_RATE_MASTER.CONSUMPTION_TAX_MANAGEMENT_CODE IS '消費税管理コード 既存システム物理名: EZJCMC';
COMMENT ON COLUMN CONSUMPTION_TAX_RATE_MASTER.EFFECTIVE_START_DATE IS '有効開始年月日 既存システム物理名: EZJ07D';
COMMENT ON COLUMN CONSUMPTION_TAX_RATE_MASTER.EFFECTIVE_END_DATE IS '有効終了年月日 既存システム物理名: EZJ08D';
COMMENT ON COLUMN CONSUMPTION_TAX_RATE_MASTER.NATIONAL_TAX_CONSUMPTION_PERCENT IS '国税消費税％ 既存システム物理名: EZJ09R';
COMMENT ON COLUMN CONSUMPTION_TAX_RATE_MASTER.LOCAL_TAX_CONSUMPTION_PERCENT IS '地方税消費税％ 既存システム物理名: EZJ10R';
