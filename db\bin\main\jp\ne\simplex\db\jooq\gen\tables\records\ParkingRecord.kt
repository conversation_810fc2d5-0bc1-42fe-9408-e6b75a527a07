/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.ParkingTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingPojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 駐車場 既存システム物理名: ECC30P
 */
@Suppress("UNCHECKED_CAST")
open class ParkingRecord private constructor() : UpdatableRecordImpl<ParkingRecord>(ParkingTable.PARKING) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteFlag: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var buildingCode: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var actualBuildingCode: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var parkingLotCode: String
        set(value): Unit = set(9, value)
        get(): String = get(9) as String

    open var parkingLotNumber: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var consolidatedBuildingCode: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var consolidatedParkingCode: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var consolidatedParkingCount: Short?
        set(value): Unit = set(13, value)
        get(): Short? = get(13) as Short?

    open var transferredBuildingCode: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var preConsolidationAvailable: Int?
        set(value): Unit = set(15, value)
        get(): Int? = get(15) as Int?

    open var bulkLeaseFlag: Byte?
        set(value): Unit = set(16, value)
        get(): Byte? = get(16) as Byte?

    open var depositZeroConsentCategory: Byte?
        set(value): Unit = set(17, value)
        get(): Byte? = get(17) as Byte?

    open var landlordCode_10: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var tenantCategory: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var roofFlag: Byte?
        set(value): Unit = set(20, value)
        get(): Byte? = get(20) as Byte?

    open var recruitmentFlag: Byte?
        set(value): Unit = set(21, value)
        get(): Byte? = get(21) as Byte?

    open var availableDate: Int?
        set(value): Unit = set(22, value)
        get(): Int? = get(22) as Int?

    open var managementFlag: Byte?
        set(value): Unit = set(23, value)
        get(): Byte? = get(23) as Byte?

    open var managementUnitCategory: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var tenantRecruitmentCount: Byte?
        set(value): Unit = set(25, value)
        get(): Byte? = get(25) as Byte?

    open var managementContractCashReceived: Int?
        set(value): Unit = set(26, value)
        get(): Int? = get(26) as Int?

    open var assessmentReviewDocumentNumber: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var interfaceFlag: Byte?
        set(value): Unit = set(28, value)
        get(): Byte? = get(28) as Byte?

    open var dataMigrationSourceKey_1: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var dataMigrationSourceKey_2: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var additionalAdFeeMonths: BigDecimal?
        set(value): Unit = set(31, value)
        get(): BigDecimal? = get(31) as BigDecimal?

    open var newGuaranteeRate: BigDecimal?
        set(value): Unit = set(32, value)
        get(): BigDecimal? = get(32) as BigDecimal?

    open var newGuaranteeManagementRate: BigDecimal?
        set(value): Unit = set(33, value)
        get(): BigDecimal? = get(33) as BigDecimal?

    open var contractMutualAidFeeRate: BigDecimal?
        set(value): Unit = set(34, value)
        get(): BigDecimal? = get(34) as BigDecimal?

    open var nonStandardCategory: Byte?
        set(value): Unit = set(35, value)
        get(): Byte? = get(35) as Byte?

    open var parkingCategory: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var offSiteParkingCategory: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised ParkingRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteFlag: Byte? = null, buildingCode: String, actualBuildingCode: String? = null, parkingLotCode: String, parkingLotNumber: String? = null, consolidatedBuildingCode: String? = null, consolidatedParkingCode: String? = null, consolidatedParkingCount: Short? = null, transferredBuildingCode: String? = null, preConsolidationAvailable: Int? = null, bulkLeaseFlag: Byte? = null, depositZeroConsentCategory: Byte? = null, landlordCode_10: String? = null, tenantCategory: String? = null, roofFlag: Byte? = null, recruitmentFlag: Byte? = null, availableDate: Int? = null, managementFlag: Byte? = null, managementUnitCategory: String? = null, tenantRecruitmentCount: Byte? = null, managementContractCashReceived: Int? = null, assessmentReviewDocumentNumber: String? = null, interfaceFlag: Byte? = null, dataMigrationSourceKey_1: String? = null, dataMigrationSourceKey_2: String? = null, additionalAdFeeMonths: BigDecimal? = null, newGuaranteeRate: BigDecimal? = null, newGuaranteeManagementRate: BigDecimal? = null, contractMutualAidFeeRate: BigDecimal? = null, nonStandardCategory: Byte? = null, parkingCategory: String? = null, offSiteParkingCategory: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteFlag = logicalDeleteFlag
        this.buildingCode = buildingCode
        this.actualBuildingCode = actualBuildingCode
        this.parkingLotCode = parkingLotCode
        this.parkingLotNumber = parkingLotNumber
        this.consolidatedBuildingCode = consolidatedBuildingCode
        this.consolidatedParkingCode = consolidatedParkingCode
        this.consolidatedParkingCount = consolidatedParkingCount
        this.transferredBuildingCode = transferredBuildingCode
        this.preConsolidationAvailable = preConsolidationAvailable
        this.bulkLeaseFlag = bulkLeaseFlag
        this.depositZeroConsentCategory = depositZeroConsentCategory
        this.landlordCode_10 = landlordCode_10
        this.tenantCategory = tenantCategory
        this.roofFlag = roofFlag
        this.recruitmentFlag = recruitmentFlag
        this.availableDate = availableDate
        this.managementFlag = managementFlag
        this.managementUnitCategory = managementUnitCategory
        this.tenantRecruitmentCount = tenantRecruitmentCount
        this.managementContractCashReceived = managementContractCashReceived
        this.assessmentReviewDocumentNumber = assessmentReviewDocumentNumber
        this.interfaceFlag = interfaceFlag
        this.dataMigrationSourceKey_1 = dataMigrationSourceKey_1
        this.dataMigrationSourceKey_2 = dataMigrationSourceKey_2
        this.additionalAdFeeMonths = additionalAdFeeMonths
        this.newGuaranteeRate = newGuaranteeRate
        this.newGuaranteeManagementRate = newGuaranteeManagementRate
        this.contractMutualAidFeeRate = contractMutualAidFeeRate
        this.nonStandardCategory = nonStandardCategory
        this.parkingCategory = parkingCategory
        this.offSiteParkingCategory = offSiteParkingCategory
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ParkingRecord
     */
    constructor(value: ParkingPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteFlag = value.logicalDeleteFlag
            this.buildingCode = value.buildingCode
            this.actualBuildingCode = value.actualBuildingCode
            this.parkingLotCode = value.parkingLotCode
            this.parkingLotNumber = value.parkingLotNumber
            this.consolidatedBuildingCode = value.consolidatedBuildingCode
            this.consolidatedParkingCode = value.consolidatedParkingCode
            this.consolidatedParkingCount = value.consolidatedParkingCount
            this.transferredBuildingCode = value.transferredBuildingCode
            this.preConsolidationAvailable = value.preConsolidationAvailable
            this.bulkLeaseFlag = value.bulkLeaseFlag
            this.depositZeroConsentCategory = value.depositZeroConsentCategory
            this.landlordCode_10 = value.landlordCode_10
            this.tenantCategory = value.tenantCategory
            this.roofFlag = value.roofFlag
            this.recruitmentFlag = value.recruitmentFlag
            this.availableDate = value.availableDate
            this.managementFlag = value.managementFlag
            this.managementUnitCategory = value.managementUnitCategory
            this.tenantRecruitmentCount = value.tenantRecruitmentCount
            this.managementContractCashReceived = value.managementContractCashReceived
            this.assessmentReviewDocumentNumber = value.assessmentReviewDocumentNumber
            this.interfaceFlag = value.interfaceFlag
            this.dataMigrationSourceKey_1 = value.dataMigrationSourceKey_1
            this.dataMigrationSourceKey_2 = value.dataMigrationSourceKey_2
            this.additionalAdFeeMonths = value.additionalAdFeeMonths
            this.newGuaranteeRate = value.newGuaranteeRate
            this.newGuaranteeManagementRate = value.newGuaranteeManagementRate
            this.contractMutualAidFeeRate = value.contractMutualAidFeeRate
            this.nonStandardCategory = value.nonStandardCategory
            this.parkingCategory = value.parkingCategory
            this.offSiteParkingCategory = value.offSiteParkingCategory
            resetChangedOnNotNull()
        }
    }
}
