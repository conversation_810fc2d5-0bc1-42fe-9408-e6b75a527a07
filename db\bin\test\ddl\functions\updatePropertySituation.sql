-- ************************************************************************** --
-- ファンクション名 : UPDATE_PROPERTY_SITUATION
-- 処理概要         : 物件状況カラムを更新します
-- 引数             :
-- 戻り値           :
-- 備考             :
-- ************************************************************************** --
CREATE OR REPLACE FUNCTION update_property_situation()
RETURNS TRIGGER AS $$
BEGIN
    NEW.property_situation := CASE
        WHEN NEW.record_status_type IN ('20', '30', '40', '50') THEN '120'
        WHEN NEW.record_status_type = '85' THEN '160'
        WHEN NEW.room_status_type = '00' THEN '100'
        WHEN NEW.room_status_type = '10' THEN '130'
        WHEN NEW.room_status_type = '20' THEN '140'
        WHEN NEW.room_status_type = '30' AND NEW.preference_new_build = 1 THEN '110'
        WHEN NEW.room_status_type = '30' AND NEW.preference_new_build != 1 THEN '150'
        ELSE NULL  -- どの条件にも当てはまらない場合は NULL を設定
    END;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_property_situation
BEFORE INSERT OR UPDATE ON room_info_master
FOR EACH ROW
EXECUTE FUNCTION update_property_situation();
