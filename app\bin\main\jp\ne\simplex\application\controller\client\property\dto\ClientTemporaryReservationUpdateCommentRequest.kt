package jp.ne.simplex.application.controller.client.property.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.*
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException

data class ClientTemporaryReservationUpdateCommentRequest(
    @JsonProperty("buildingCd")
    @field:Schema(description = "建物コード", example = "000130305")
    val buildingCd: String,

    @JsonProperty("roomCd")
    @field:Schema(description = "部屋コード", example = "01010")
    val roomCd: String,

    @JsonProperty("comment")
    @field:Schema(description = "コメント", example = "コメントが入ります")
    val comment: String?,
) {

    // Service層の Interface に変換する
    fun toServiceInterface(): TemporaryReservation {
        try {
            return UpdateTemporaryReservationComment(
                id = Property.Id(Building.Code.of(buildingCd), Room.Code.of(roomCd)),
                comment = TemporaryReservation.Comment.of(comment),
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }

}
