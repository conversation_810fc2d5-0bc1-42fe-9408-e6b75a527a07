package jp.ne.simplex.application.batch

import jp.ne.simplex.application.repository.aws.S3RepositoryInterface
import jp.ne.simplex.application.repository.db.BatchExecuteHistoryRepositoryInterface
import jp.ne.simplex.application.repository.file.ParkingFileRepositoryInterface
import jp.ne.simplex.application.repository.sftp.SftpRepositoryInterface
import jp.ne.simplex.application.service.ParkingContractService
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component

/** 駐車場バッチ(DK-PORTAL) */
@Profile("batch")
@Component("ParkingSummaryForDkPortalBatch")
class ParkingSummaryForDkPortalBatch(
    private val parkingContractService: ParkingContractService,
    private val parkingFileRepository: ParkingFileRepositoryInterface,
    private val s3Repository: S3RepositoryInterface,
    private val sftpRepository: SftpRepositoryInterface,
    @Value("\${batch.s3.parking-summary-batch-bucket}")
    private val bucketName: String,
    override val batchExecuteHistoryRepository: BatchExecuteHistoryRepositoryInterface,
) : BatchInterface {
    override val batchType: BatchType = BatchType.PARKING_SUMMARY_FOR_DK_PORTAL

    override fun executeInternal(executeOption: ExecuteOptionType?) {
        if (executeOption == ExecuteOptionType.RERUN_ONLY_SEND_FILE) {
            executeOnlySendFile()
            return
        }
        val parkingContractPossibilityList =
            parkingContractService.updateAllParkingContractPossibility(BatchInterface.BATCH_USER)
        val localFilePath =
            parkingFileRepository.saveParkingSummaryForDkPortal(parkingContractPossibilityList)

        s3Repository.uploadFile(
            localFilePath,
            localFilePath.fileName.toString(),
            bucketName
        )
        sftpRepository.sendFile(
            localFilePath,
            localFilePath.fileName.toString()
        )
    }

    override fun isAcceptableOption(executeOption: ExecuteOptionType): Boolean {
        return executeOption == ExecuteOptionType.RERUN_ONLY_SEND_FILE || executeOption == ExecuteOptionType.RERUN
    }

    private fun executeOnlySendFile() {
        val latestCsvContent = s3Repository.getLatestFile(bucketName)
        val localFilePath = parkingFileRepository.saveFromS3ForDkPortal(
            latestCsvContent
        )
        sftpRepository.sendFile(
            localFilePath,
            localFilePath.fileName.toString()
        )
    }
}
