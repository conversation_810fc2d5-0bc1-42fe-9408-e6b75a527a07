-- INDEX: IDX_REGION_MASTER_01
-- 既存システム物理名: JXH1ML1

CREATE INDEX IDX_REGION_MASTER_01 ON REGION_MASTER(GROUP_CODE, REGION_CODE_2, DEPARTMENT_CODE, USE_START_DATE)
TABLESPACE :TS_IDX;

-- INDEX: IDX_REGION_MASTER_02
-- 既存システム物理名: JXH1ML2

CREATE INDEX IDX_REGION_MASTER_02 ON REGION_MASTER(GROUP_CODE, REGION_CODE_1, REGION_ORDER_1, REGION_CODE_2, REGION_ORDER_2)
TABLESPACE :TS_IDX;

-- INDEX: IDX_REGION_MASTER_03
-- 既存システム物理名: JXH1ML3

CREATE INDEX IDX_REGION_MASTER_03 ON REGION_MASTER(GROUP_CODE, REGION_NAME_2, USE_START_DATE)
TABLESPACE :TS_IDX;

-- INDEX: IDX_R<PERSON>ION_MASTER_04
-- 既存システム物理名: JX<PERSON>ML<PERSON>

CREATE INDEX IDX_REGION_MASTER_04 ON REGION_MASTER(GROUP_CODE, REGION_CODE_2, USE_START_DATE, USE_FINISH_DATE, DEPARTMENT_CODE)
TABLESPACE :TS_IDX;

-- INDEX: IDX_REGION_MASTER_05
-- 既存システム物理名: JXH1ML5

CREATE INDEX IDX_REGION_MASTER_05 ON REGION_MASTER(SALES_DEPARTMENT_SUPERIOR, USE_START_DATE)
TABLESPACE :TS_IDX;

-- INDEX: IDX_REGION_MASTER_06
-- 既存システム物理名: JXH1ML6

CREATE INDEX IDX_REGION_MASTER_06 ON REGION_MASTER(REGION_CODE_2, DEPARTMENT_CODE, USE_START_DATE)
TABLESPACE :TS_IDX;

-- INDEX: IDX_REGION_MASTER_07
-- 既存システム物理名: JXH1ML7

CREATE INDEX IDX_REGION_MASTER_07 ON REGION_MASTER(DEPARTMENT_CODE, USE_START_DATE, USE_FINISH_DATE)
TABLESPACE :TS_IDX;

-- INDEX: IDX_REGION_MASTER_08
-- 既存システム物理名: JXH1ML8

CREATE INDEX IDX_REGION_MASTER_08 ON REGION_MASTER(DEPARTMENT_CODE, SALES_DEPARTMENT_SUPERIOR, USE_FINISH_DATE)
TABLESPACE :TS_IDX;
