package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.PropertyMaintenanceInfo
import jp.ne.simplex.application.model.UpdatePropertyMaintenance
import jp.ne.simplex.application.repository.db.extension.PropertyMaintenanceInfoEx.Companion.toPropertyMaintenanceInfo
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.db.jooq.gen.tables.pojos.PropertyMaintenanceInfoPojo
import jp.ne.simplex.db.jooq.gen.tables.references.PROPERTY_MAINTENANCE_INFO
import jp.ne.simplex.db.jooq.gen.tables.references.VACANT_HOUSE_HP
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmmss
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import org.jooq.DSLContext
import org.jooq.InsertOnDuplicateSetMoreStep
import org.jooq.InsertOnDuplicateSetStep
import org.jooq.InsertSetMoreStep
import org.jooq.impl.DSL.row
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class PropertyMaintenanceRepository(
    private val context: DSLContext
) : PropertyMaintenanceRepositoryInterface {
    override fun updatePublishStatus(
        requestUser: AuthInfo.RequestUser,
        parameter: List<UpdatePropertyMaintenance.PublishStatus>
    ) {
        // @formatter:off
        parameter.forEach { param ->
            context.insertInto(PROPERTY_MAINTENANCE_INFO)
                .set(PROPERTY_MAINTENANCE_INFO.BUILDING_CD, param.id.buildingCode.value)
                .set(PROPERTY_MAINTENANCE_INFO.ROOM_CD, param.id.roomCode.value)
                .set(PROPERTY_MAINTENANCE_INFO.LISTING_CATEGORY_GOOD_ROOM_NET, param.publishStatus.value.toByte())
                .set(PROPERTY_MAINTENANCE_INFO.UPDATER, requestUser.value)
                .setCreationDateTime()
                .setUpdateDateTime().onDuplicateKeyUpdate()
                .set(PROPERTY_MAINTENANCE_INFO.LISTING_CATEGORY_GOOD_ROOM_NET, param.publishStatus.value.toByte())
                .set(PROPERTY_MAINTENANCE_INFO.UPDATER, requestUser.value)
                .setUpdateDateTime()
                .execute()
            // @formatter:on
        }
    }

    override fun updateAdFf(
        requestUser: AuthInfo.RequestUser,
        parameter: List<UpdatePropertyMaintenance.AdFf>
    ) {
        /** いい物件ボードに登録したレコードについてDKリンクのDBを更新するので常に後勝ちUPSERTする **/
        // @formatter:off
        parameter.forEach { param ->
            context.insertInto(PROPERTY_MAINTENANCE_INFO)
                .set(PROPERTY_MAINTENANCE_INFO.BUILDING_CD, param.id.buildingCode.value)
                .set(PROPERTY_MAINTENANCE_INFO.ROOM_CD, param.id.roomCode.value)
                .set(PROPERTY_MAINTENANCE_INFO.AD_AMOUNT, param.adFf.advertisementFee)
                .set(PROPERTY_MAINTENANCE_INFO.FF_AMOUNT, param.adFf.frontFreerentPeriod?.toBigDecimal())
                .set(PROPERTY_MAINTENANCE_INFO.UPDATER, requestUser.value)
                .setCreationDateTime()
                .setUpdateDateTime()
                .onDuplicateKeyUpdate()
                .set(PROPERTY_MAINTENANCE_INFO.AD_AMOUNT, param.adFf.advertisementFee)
                .set(PROPERTY_MAINTENANCE_INFO.FF_AMOUNT, param.adFf.frontFreerentPeriod?.toBigDecimal())
                .set(PROPERTY_MAINTENANCE_INFO.UPDATER, requestUser.value)
                .setUpdateDateTime()
                .execute()
            // @formatter:on
        }
    }

    override fun findBy(
        id: Property.Id
    ): PropertyMaintenanceInfo? {
        return context.selectFrom(PROPERTY_MAINTENANCE_INFO)
            .where(PROPERTY_MAINTENANCE_INFO.BUILDING_CD.eq(id.buildingCode.value))
            .and(PROPERTY_MAINTENANCE_INFO.ROOM_CD.eq(id.roomCode.value))
            .fetchOneInto(PropertyMaintenanceInfoPojo::class.java)
            ?.toPropertyMaintenanceInfo()
    }

    override fun listBy(
        ids: List<Property.Id>
    ): List<PropertyMaintenanceInfo> {
        return context.selectFrom(PROPERTY_MAINTENANCE_INFO)
            .where(
                row(
                    PROPERTY_MAINTENANCE_INFO.BUILDING_CD,
                    PROPERTY_MAINTENANCE_INFO.ROOM_CD
                ).`in`(ids.map { row(it.buildingCode.value, it.roomCode.value) })
            )
            .fetchInto(PropertyMaintenanceInfoPojo::class.java)
            .mapNotNull { it.toPropertyMaintenanceInfo() }
    }

    override fun updateOldPublishStatus(
        requestUser: AuthInfo.RequestUser,
        propertyId: Property.Id
    ) {
        // @formatter:off
            context.update(PROPERTY_MAINTENANCE_INFO)
                .set(PROPERTY_MAINTENANCE_INFO.HOMES_PANORAMA_SEND_FLAG, PROPERTY_MAINTENANCE_INFO.LISTING_CATEGORY_GOOD_ROOM_NET)
                .set(PROPERTY_MAINTENANCE_INFO.UPDATER, requestUser.value)
                .where(PROPERTY_MAINTENANCE_INFO.BUILDING_CD.eq(propertyId.buildingCode.value))
                .and(PROPERTY_MAINTENANCE_INFO.ROOM_CD.eq(propertyId.roomCode.value))
                .execute()
            // @formatter:on
    }

    override fun updatePublishStatusFromOldStatus(
        requestUser: AuthInfo.RequestUser,
        propertyId: Property.Id
    ) {
        val customerCompletionFlag = context.select(
            VACANT_HOUSE_HP.CUSTOMER_COMPLETION_FLAG
        ).from(VACANT_HOUSE_HP)
            .where(VACANT_HOUSE_HP.PROPERTY_BUILDING_CD.eq(propertyId.buildingCode.value))
            .and(VACANT_HOUSE_HP.PROPERTY_ROOM_CD.eq(propertyId.roomCode.value))
            .fetchOneInto(String::class.java)

        if (customerCompletionFlag == null || customerCompletionFlag == "1") {
            // 物件が公開不可の場合は、公開指示を更新しない
            return
        }

        context.update(PROPERTY_MAINTENANCE_INFO)
            .set(
                PROPERTY_MAINTENANCE_INFO.LISTING_CATEGORY_GOOD_ROOM_NET,
                PROPERTY_MAINTENANCE_INFO.HOMES_PANORAMA_SEND_FLAG
            )
            .set(PROPERTY_MAINTENANCE_INFO.HOMES_PANORAMA_SEND_FLAG, 0)
            .set(PROPERTY_MAINTENANCE_INFO.UPDATER, requestUser.value)
            .where(PROPERTY_MAINTENANCE_INFO.BUILDING_CD.eq(propertyId.buildingCode.value))
            .and(PROPERTY_MAINTENANCE_INFO.ROOM_CD.eq(propertyId.roomCode.value))
            .and(PROPERTY_MAINTENANCE_INFO.LISTING_CATEGORY.eq(1))
            .execute()
    }
}

/** 新規登録時のメタ情報(CREATION_DATE, CREATION_TIME)の更新処理 **/
private fun InsertSetMoreStep<*>.setCreationDateTime(): InsertSetMoreStep<*> {
    // 現在の日時を取得
    val currentDateTime = LocalDateTime.now()

    return this.set(PROPERTY_MAINTENANCE_INFO.CREATION_DATE, currentDateTime.yyyyMMdd().toInt())
        .set(PROPERTY_MAINTENANCE_INFO.CREATION_TIME, currentDateTime.HHmmss().toInt())
}

/** 新規登録時のメタ情報(UPDATE_DATE, UPDATE_TIME)の更新処理 **/
private fun InsertSetMoreStep<*>.setUpdateDateTime(): InsertSetMoreStep<*> {
    // 現在の日時を取得
    val currentDateTime = LocalDateTime.now()

    return this.set(PROPERTY_MAINTENANCE_INFO.UPDATE_DATE, currentDateTime.yyyyMMdd().toInt())
        .set(PROPERTY_MAINTENANCE_INFO.UPDATE_TIME, currentDateTime.HHmmss().toInt())
}

/** 更新時のメタ情報(UPDATE_DATE, UPDATE_TIME)の更新処理 **/
private fun InsertOnDuplicateSetStep<*>.setUpdateDateTime(): InsertOnDuplicateSetMoreStep<*> {
    // 現在の日時を取得
    val currentDateTime = LocalDateTime.now()

    return this.set(PROPERTY_MAINTENANCE_INFO.UPDATE_DATE, currentDateTime.yyyyMMdd().toInt())
        .set(PROPERTY_MAINTENANCE_INFO.UPDATE_TIME, currentDateTime.HHmmss().toInt())
}

interface PropertyMaintenanceRepositoryInterface {
    /** 公開指示情報を更新 **/
    fun updatePublishStatus(
        requestUser: AuthInfo.RequestUser,
        parameter: List<UpdatePropertyMaintenance.PublishStatus>
    )

    /** AD・FFを更新 **/
    fun updateAdFf(
        requestUser: AuthInfo.RequestUser,
        parameter: List<UpdatePropertyMaintenance.AdFf>
    )

    /** 物件メンテナンスリスト取得(ID指定) **/
    fun findBy(
        id: Property.Id
    ): PropertyMaintenanceInfo?

    /** 物件メンテナンスリスト取得(複数ID指定) **/
    fun listBy(
        ids: List<Property.Id>
    ): List<PropertyMaintenanceInfo>

    fun updateOldPublishStatus(
        requestUser: AuthInfo.RequestUser,
        propertyId: Property.Id
    )

    fun updatePublishStatusFromOldStatus(
        requestUser: AuthInfo.RequestUser,
        propertyId: Property.Id,
    )
}
