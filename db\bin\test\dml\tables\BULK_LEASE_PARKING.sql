truncate table BULK_LEASE_PARKING;
insert into BULK_LEASE_PARKING (CREATION_DATE, CREATION_TIME, UPDATE_DATE, UPDATE_TIME, UPDATE_PROGRAM_ID, UPDATER, LOGICAL_DELETE_SIGN, BUILDING_CODE, PARKING_CODE, ASSESSMENT_DIVISION) values
 (20060923, 160127, 20140805, 135050, 'EBD010R', '043922', 0, '026373501', '001', '1')
,(20060923, 160127, 20190924, 174126, 'EBD010R', '096402', 0, '014061301', '008', '1')
,(20060923, 160127, 20190718, 170944, 'EBD010R', '096402', 0, '014061301', '009', '1')
,(20060925, 94742, 20200713, 114831, 'EBD010R', '094678', 0, '014061301', '001', '1')
,(20060925, 94742, 20190718, 170924, 'EBD010R', '096402', 0, '014061301', '002', '1')
,(20060925, 94743, 20211105, 153719, 'EBD010R', '094754', 0, '014061301', '003', '1')
,(20060925, 94743, 20170608, 125812, 'EBD010R', '093909', 0, '014061301', '004', '1')
,(20060925, 94744, 20160330, 173152, 'EBD010R', '030556', 0, '014061301', '005', '1')
,(20060925, 94744, 20181116, 111711, 'EBD010R', '096402', 0, '014061301', '006', '1')
,(20060925, 95121, 20201008, 175805, 'EBD010R', '301628', 0, '013890701', '001', '1')
,(20060925, 95121, 20200310, 181327, 'EBD010R', '025678', 0, '013890701', '002', '1')
,(20060925, 95239, 20211014, 145439, 'EBD010R', '301626', 0, '013890701', '003', '1')
,(20060925, 95240, 20110510, 155234, 'EBD010R', '046610', 0, '013890701', '004', '1')
,(20060925, 95240, 20231006, 154926, 'EBD010R', '063168', 0, '013890701', '005', '1')
,(20060925, 95240, 20130118, 105747, 'EBD010R', '010814', 0, '013890701', '006', '1')
,(20060925, 95240, 20200310, 181328, 'EBD010R', '025678', 0, '013890701', '007', '1')
,(20060925, 105706, 20230926, 114043, 'EBD010R', '063168', 0, '013950801', '001', '1')
,(20060925, 105707, 20230926, 114044, 'EBD010R', '063168', 0, '013950801', '002', '1')
,(20060925, 105707, 20210715, 180541, 'EBD010R', '025322', 0, '013950801', '003', '1')
,(20060925, 105708, 20230809, 102859, 'EBD010R', '063168', 0, '013950801', '004', '1')
,(20060925, 105748, 20171225, 175947, 'EBD010R', '300150', 0, '013950801', '005', '1')
,(20060925, 105749, 20221202, 153251, 'EBD010R', '063168', 0, '013950801', '006', '1')
,(20060925, 105750, 20201007, 100953, 'EBD010R', '094111', 0, '013950801', '007', '1')
,(20060925, 111908, 20210319, 194750, 'EBD010R', '094482', 0, '013963001', '001', '1')
,(20060925, 162344, 20161104, 130427, 'EBD010R', '061684', 0, '013963001', '002', '1')
,(20060925, 162441, 20061223, 105731, 'EBD010R', '015119', 0, '013963001', '003', '1')
,(20060925, 162505, 20220809, 140316, 'EBD010R', '033639', 0, '013963001', '004', '1')
,(20060927, 131746, 20230712, 144412, 'EBD010R', '025322', 0, '014026901', '001', '1')
,(20060927, 131747, 20230712, 144413, 'EBD010R', '025322', 0, '014026901', '002', '1')
,(20060927, 131747, 20081023, 193057, 'EBD010R', '025322', 0, '014026901', '003', '1')
,(20060927, 131747, 20170711, 175619, 'EBD010R', '025678', 0, '014026901', '004', '1')
,(20060927, 131747, 20221202, 132633, 'EBD010R', '096499', 0, '014026901', '005', '1')
,(20060927, 131748, 20220302, 173404, 'EBD010R', '063168', 0, '014026901', '006', '1')
,(20060927, 131748, 20220428, 162054, 'EBD010R', '063168', 0, '014026901', '007', '1')
,(20060927, 131749, 20220622, 92927, 'EBD010R', '094111', 0, '014026901', '008', '1')
,(20060927, 131749, 20221202, 132654, 'EBD010R', '096499', 0, '014026901', '009', '1')
,(20060927, 131749, 20141218, 74755, 'EBD010R', '010814', 0, '014026901', '010', '1')
,(20060927, 131821, 20220930, 163315, 'EBD010R', '063168', 0, '014026901', '011', '1')
,(20060927, 131821, 20061223, 102449, 'EBD010R', '025322', 0, '014026901', '012', '1')
,(20060927, 131821, 20230207, 164659, 'EBD010R', '063168', 0, '014026901', '013', '1')
,(20060927, 131821, 20230207, 164659, 'EBD010R', '063168', 0, '014026901', '014', '1')
,(20060927, 131847, 20210806, 103718, 'EBD010R', '094111', 0, '014026901', '015', '2')
,(20060927, 131847, 20210806, 103743, 'EBD010R', '094111', 0, '014026901', '016', '2')
,(20060928, 151720, 20191031, 153609, 'EBD010R', '301952', 0, '013861601', '001', '1')
,(20060928, 151721, 20150829, 183404, 'EBD010R', '018538', 0, '013861601', '002', '1')
,(20060928, 151721, 20171013, 170620, 'EBD010R', '300150', 0, '013861601', '003', '1')
,(20060928, 151721, 20161228, 110518, 'EBD010R', '018538', 0, '013861601', '004', '1')
,(20060928, 151721, 20110329, 134707, 'EBD010R', '046610', 0, '013861601', '005', '1')
,(20060928, 151721, 20200221, 182846, 'EBD010R', '096998', 0, '013861601', '006', '1')
,(20060928, 153122, 20191129, 155321, 'EBD010R', '301952', 0, '014010001', '001', '1')
,(20060928, 153123, 20190311, 174529, 'EBD010R', '300150', 0, '014010001', '002', '1')
,(20060928, 153215, 20200904, 95951, 'EBD010R', '301952', 0, '014010001', '003', '1')
,(20060928, 153216, 20231006, 155018, 'EBD010R', '063168', 0, '014010001', '004', '1')
,(20060928, 153216, 20110127, 110508, 'EBD010R', '046610', 0, '014010001', '005', '1')
,(20060928, 153217, 20130527, 95941, 'EBD010R', '010814', 0, '014010001', '006', '1')
,(20060928, 153217, 20200326, 172525, 'EBD010R', '301952', 0, '014010001', '007', '1')
,(20060928, 154658, 20230703, 182536, 'EBD010R', '301626', 0, '014115901', '001', '1')
,(20060928, 154659, 20230703, 182537, 'EBD010R', '301626', 0, '014115901', '002', '1')
,(20060928, 154700, 20061223, 102821, 'EBD010R', '025322', 0, '014115901', '003', '1')
,(20060928, 154700, 20061223, 102825, 'EBD010R', '025322', 0, '014115901', '004', '1')
,(20060928, 154701, 20220702, 175125, 'EBD010R', '096499', 0, '014115901', '005', '1')
,(20060928, 154702, 20230207, 164744, 'EBD010R', '063168', 0, '014115901', '006', '1')
,(20060928, 154702, 20220729, 141509, 'EBD010R', '022787', 0, '014115901', '007', '1')
,(20060928, 154702, 20210907, 93736, 'EBD010R', '025322', 0, '014115901', '008', '1')
,(20060928, 154703, 20220702, 175159, 'EBD010R', '096499', 0, '014115901', '009', '1')
,(20060928, 154703, 20230207, 164744, 'EBD010R', '063168', 0, '014115901', '010', '1')
,(20060928, 154742, 20210806, 113731, 'EBD010R', '094111', 0, '014115901', '011', '2')
,(20060928, 154742, 20210806, 113754, 'EBD010R', '094111', 0, '014115901', '012', '2')
,(20060928, 160444, 20110817, 170930, 'EBD010R', '046610', 0, '014059301', '001', '1')
,(20060928, 160445, 20200214, 144934, 'EBD010R', '025678', 0, '014059301', '002', '1')
,(20060928, 160446, 20190116, 170031, 'EBD010R', '300150', 0, '014059301', '003', '1')
,(20060928, 160446, 20191115, 120727, 'EBD010R', '025678', 0, '014059301', '004', '1')
,(20060928, 160447, 20180608, 113956, 'EBD010R', '300150', 0, '014059301', '005', '1')
,(20060928, 160447, 20200728, 141144, 'EBD010R', '301952', 0, '014059301', '007', '1')
,(20060928, 160517, 20170303, 92258, 'EBD010R', '018538', 0, '014059301', '006', '1')
,(20060928, 160518, 20210827, 133222, 'EBD010R', '094111', 0, '014059301', '008', '1')
,(20060928, 160519, 20121126, 94231, 'EBD010R', '010814', 0, '014059301', '009', '1')
,(20060928, 161924, 20200904, 143332, 'EBD010R', '058276', 0, '014094001', '001', '1')
,(20060928, 162003, 20220411, 180812, 'EBD010R', '025322', 0, '014094001', '002', '1')
,(20060928, 162003, 20210629, 165657, 'EBD010R', '057526', 0, '014094001', '003', '1')
,(20060928, 162004, 20211203, 124506, 'EBD010R', '057526', 0, '014094001', '004', '1')
,(20060928, 162004, 20211203, 124507, 'EBD010R', '057526', 0, '014094001', '005', '1')
,(20060928, 162004, 20230302, 140722, 'EBD010R', '025322', 0, '014094001', '006', '1')
,(20060928, 162005, 20230919, 180212, 'EBD010R', '301626', 0, '014094001', '007', '1')
,(20060928, 162005, 20200904, 143333, 'EBD010R', '058276', 0, '014094001', '008', '1')
,(20060928, 162006, 20220905, 185230, 'EBD010R', '094111', 0, '014094001', '009', '1')
,(20060928, 162006, 20190226, 180205, 'EBD010R', '300150', 0, '014094001', '010', '1')
,(20060928, 162007, 20190226, 181539, 'EBD010R', '300150', 0, '014094001', '011', '1')
,(20060928, 162059, 20160311, 204949, 'EBD010R', '018538', 0, '014094001', '012', '1')
,(20060928, 162100, 20210728, 172600, 'EBD010R', '301626', 0, '014094001', '013', '1')
,(20060928, 162100, 20210728, 172620, 'EBD010R', '301626', 0, '014094001', '014', '1')
,(20060928, 162101, 20190227, 170130, 'EBD010R', '300150', 0, '014094001', '015', '1')
,(20060928, 162101, 20220905, 185231, 'EBD010R', '094111', 0, '014094001', '016', '1')
,(20060928, 162101, 20161219, 143629, 'EBD010R', '018538', 0, '014094001', '017', '1')
,(20060928, 162102, 20160324, 152114, 'EBD010R', '018538', 0, '014094001', '018', '1')
,(20060928, 163905, 20180307, 122125, 'EBD010R', '300150', 0, '013847001', '001', '1')
,(20060928, 163905, 20201208, 101535, 'EBD010R', '094111', 0, '013847001', '002', '1')
,(20060928, 163905, 20200303, 142515, 'EBD010R', '301628', 0, '013847001', '003', '1')
,(20060928, 163905, 20210119, 172934, 'EBD010R', '094111', 0, '013847001', '004', '1')
,(20060928, 163906, 20160609, 132912, 'EBD010R', '018538', 0, '013847001', '005', '1')
;
