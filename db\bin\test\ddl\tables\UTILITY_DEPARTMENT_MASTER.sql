-- TABLE: UTILITY_DEPARTMENT_MASTER(ライフライン部署マスタ)

CREATE TABLE UTILITY_DEPARTMENT_MASTER(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER_ID                                   varchar(10)                   
,    DELETE_FLAG                                  numeric(1)                    
,    UTILITY_CATEGORY                             varchar(2)                    
,    UTILITY_COMPANY_CD                           varchar(6)                    
,    UTILITY_DEPARTMENT_CD                        varchar(3)                    
,    UTILITY_DEPARTMENT_FULL_NAME                 varchar(52)                   
,    UTILITY_DEPARTMENT_NAME_KANA                 varchar(52)                   
,    PHONE_AREA_CODE                              varchar(6)                    
,    PHONE_LOCAL_CODE                             varchar(6)                    
,    PHONE_SUBSCRIBER_NUMBER                      varchar(6)                    
,    DETAIL_CATEGORY                              varchar(2)                    
,    COMMENT                                      varchar(42)                   
) TABLESPACE :TS_TBL;

COMMENT ON TABLE UTILITY_DEPARTMENT_MASTER IS 'ライフライン部署マスタ 既存システム物理名: YCBLBP';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: SAKUSEI_DT @290';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: SAKUSEI_TM @290';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: KOSHIN_DT';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: KOSHIN_TM';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: KOSHIN_PGM_ID';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.UPDATER_ID IS '更新者ID 既存システム物理名: KOSHINSHA_ID';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.DELETE_FLAG IS '削除フラグ 既存システム物理名: SAKUJO_FLG';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.UTILITY_CATEGORY IS 'ライフライン区分 既存システム物理名: LIFELINE_KBN';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.UTILITY_COMPANY_CD IS 'ライフライン 会社CD 既存システム物理名: LIFELINE_KAISHA_CD';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.UTILITY_DEPARTMENT_CD IS 'ライフライン 部署CD 既存システム物理名: LIFELINE_BUSHO_CD';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.UTILITY_DEPARTMENT_FULL_NAME IS 'ライフライン 部署正式名称 既存システム物理名: LIFELINE_BUSHO_SEISHIKIMEISHO';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.UTILITY_DEPARTMENT_NAME_KANA IS 'ライフライン 部署名称(カナ) 既存システム物理名: LIFELINE_BUSHO_MEISHO_KANA';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.PHONE_AREA_CODE IS '電話番号 (市外局番) 既存システム物理名: DENWA_BANGO_SHIGAIKYOKUBAN';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.PHONE_LOCAL_CODE IS '電話番号 (市内局番) 既存システム物理名: DENWA_BANGO_SHINAIKYOKUBAN';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.PHONE_SUBSCRIBER_NUMBER IS '電話番号 (加入者番号) 既存システム物理名: DENWA_BANGO_KANYUSHABANGO';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.DETAIL_CATEGORY IS '詳細区分 既存システム物理名: SHOSAI_KBN';
COMMENT ON COLUMN UTILITY_DEPARTMENT_MASTER.COMMENT IS 'コメント 既存システム物理名: COMENT';
