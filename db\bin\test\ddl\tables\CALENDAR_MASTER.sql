-- TABLE: <PERSON><PERSON><PERSON><PERSON>_MASTER(カレンダーマスタ)

CREATE TABLE CALENDAR_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    REFLECTION_DATE                              numeric(8,0)                  
,    DELETE_FLAG                                  varchar(1)                    
,    YEAR_MONTH                                   numeric(6,0)                  
,    DAYS_IN_MONTH                                numeric(2,0)                  
,    COMPANY_WORK_DAYS                            numeric(2,0)                  
,    CUMULATIVE_DAYS_PREV_MONTH_END               numeric(3,0)                  
,    WEEKDAY_PREV_MONTH_END                       varchar(1)                    
,    WEEKDAY_01                                   varchar(1)                    
,    HOLIDAY_01                                   varchar(1)                    
,    COMPANY_HOLIDAY_01                           varchar(1)                    
,    BANK_HOLIDAY_01                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_01                    numeric(3,0)                  
,    WEEKDAY_02                                   varchar(1)                    
,    H<PERSON>IDAY_02                                   varchar(1)                    
,    COMPANY_HOLIDAY_02                           varchar(1)                    
,    BANK_HOLIDAY_02                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_02                    numeric(3,0)                  
,    WEEKDAY_03                                   varchar(1)                    
,    HOLIDAY_03                                   varchar(1)                    
,    COMPANY_HOLIDAY_03                           varchar(1)                    
,    BANK_HOLIDAY_03                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_03                    numeric(3,0)                  
,    WEEKDAY_04                                   varchar(1)                    
,    HOLIDAY_04                                   varchar(1)                    
,    COMPANY_HOLIDAY_04                           varchar(1)                    
,    BANK_HOLIDAY_04                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_04                    numeric(3,0)                  
,    WEEKDAY_05                                   varchar(1)                    
,    HOLIDAY_05                                   varchar(1)                    
,    COMPANY_HOLIDAY_05                           varchar(1)                    
,    BANK_HOLIDAY_05                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_05                    numeric(3,0)                  
,    WEEKDAY_06                                   varchar(1)                    
,    HOLIDAY_06                                   varchar(1)                    
,    COMPANY_HOLIDAY_06                           varchar(1)                    
,    BANK_HOLIDAY_06                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_06                    numeric(3,0)                  
,    WEEKDAY_07                                   varchar(1)                    
,    HOLIDAY_07                                   varchar(1)                    
,    COMPANY_HOLIDAY_07                           varchar(1)                    
,    BANK_HOLIDAY_07                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_07                    numeric(3,0)                  
,    WEEKDAY_08                                   varchar(1)                    
,    HOLIDAY_08                                   varchar(1)                    
,    COMPANY_HOLIDAY_08                           varchar(1)                    
,    BANK_HOLIDAY_08                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_08                    numeric(3,0)                  
,    WEEKDAY_09                                   varchar(1)                    
,    HOLIDAY_09                                   varchar(1)                    
,    COMPANY_HOLIDAY_09                           varchar(1)                    
,    BANK_HOLIDAY_09                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_09                    numeric(3,0)                  
,    WEEKDAY_10                                   varchar(1)                    
,    HOLIDAY_10                                   varchar(1)                    
,    COMPANY_HOLIDAY_10                           varchar(1)                    
,    BANK_HOLIDAY_10                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_10                    numeric(3,0)                  
,    WEEKDAY_11                                   varchar(1)                    
,    HOLIDAY_11                                   varchar(1)                    
,    COMPANY_HOLIDAY_11                           varchar(1)                    
,    BANK_HOLIDAY_11                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_11                    numeric(3,0)                  
,    WEEKDAY_12                                   varchar(1)                    
,    HOLIDAY_12                                   varchar(1)                    
,    COMPANY_HOLIDAY_12                           varchar(1)                    
,    BANK_HOLIDAY_12                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_12                    numeric(3,0)                  
,    WEEKDAY_13                                   varchar(1)                    
,    HOLIDAY_13                                   varchar(1)                    
,    COMPANY_HOLIDAY_13                           varchar(1)                    
,    BANK_HOLIDAY_13                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_13                    numeric(3,0)                  
,    WEEKDAY_14                                   varchar(1)                    
,    HOLIDAY_14                                   varchar(1)                    
,    COMPANY_HOLIDAY_14                           varchar(1)                    
,    BANK_HOLIDAY_14                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_14                    numeric(3,0)                  
,    WEEKDAY_15                                   varchar(1)                    
,    HOLIDAY_15                                   varchar(1)                    
,    COMPANY_HOLIDAY_15                           varchar(1)                    
,    BANK_HOLIDAY_15                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_15                    numeric(3,0)                  
,    WEEKDAY_16                                   varchar(1)                    
,    HOLIDAY_16                                   varchar(1)                    
,    COMPANY_HOLIDAY_16                           varchar(1)                    
,    BANK_HOLIDAY_16                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_16                    numeric(3,0)                  
,    WEEKDAY_17                                   varchar(1)                    
,    HOLIDAY_17                                   varchar(1)                    
,    COMPANY_HOLIDAY_17                           varchar(1)                    
,    BANK_HOLIDAY_17                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_17                    numeric(3,0)                  
,    WEEKDAY_18                                   varchar(1)                    
,    HOLIDAY_18                                   varchar(1)                    
,    COMPANY_HOLIDAY_18                           varchar(1)                    
,    BANK_HOLIDAY_18                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_18                    numeric(3,0)                  
,    WEEKDAY_19                                   varchar(1)                    
,    HOLIDAY_19                                   varchar(1)                    
,    COMPANY_HOLIDAY_19                           varchar(1)                    
,    BANK_HOLIDAY_19                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_19                    numeric(3,0)                  
,    WEEKDAY_20                                   varchar(1)                    
,    HOLIDAY_20                                   varchar(1)                    
,    COMPANY_HOLIDAY_20                           varchar(1)                    
,    BANK_HOLIDAY_20                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_20                    numeric(3,0)                  
,    WEEKDAY_21                                   varchar(1)                    
,    HOLIDAY_21                                   varchar(1)                    
,    COMPANY_HOLIDAY_21                           varchar(1)                    
,    BANK_HOLIDAY_21                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_21                    numeric(3,0)                  
,    WEEKDAY_22                                   varchar(1)                    
,    HOLIDAY_22                                   varchar(1)                    
,    COMPANY_HOLIDAY_22                           varchar(1)                    
,    BANK_HOLIDAY_22                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_22                    numeric(3,0)                  
,    WEEKDAY_23                                   varchar(1)                    
,    HOLIDAY_23                                   varchar(1)                    
,    COMPANY_HOLIDAY_23                           varchar(1)                    
,    BANK_HOLIDAY_23                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_23                    numeric(3,0)                  
,    WEEKDAY_24                                   varchar(1)                    
,    HOLIDAY_24                                   varchar(1)                    
,    COMPANY_HOLIDAY_24                           varchar(1)                    
,    BANK_HOLIDAY_24                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_24                    numeric(3,0)                  
,    WEEKDAY_25                                   varchar(1)                    
,    HOLIDAY_25                                   varchar(1)                    
,    COMPANY_HOLIDAY_25                           varchar(1)                    
,    BANK_HOLIDAY_25                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_25                    numeric(3,0)                  
,    WEEKDAY_26                                   varchar(1)                    
,    HOLIDAY_26                                   varchar(1)                    
,    COMPANY_HOLIDAY_26                           varchar(1)                    
,    BANK_HOLIDAY_26                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_26                    numeric(3,0)                  
,    WEEKDAY_27                                   varchar(1)                    
,    HOLIDAY_27                                   varchar(1)                    
,    COMPANY_HOLIDAY_27                           varchar(1)                    
,    BANK_HOLIDAY_27                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_27                    numeric(3,0)                  
,    WEEKDAY_28                                   varchar(1)                    
,    HOLIDAY_28                                   varchar(1)                    
,    COMPANY_HOLIDAY_28                           varchar(1)                    
,    BANK_HOLIDAY_28                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_28                    numeric(3,0)                  
,    WEEKDAY_29                                   varchar(1)                    
,    HOLIDAY_29                                   varchar(1)                    
,    COMPANY_HOLIDAY_29                           varchar(1)                    
,    BANK_HOLIDAY_29                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_29                    numeric(3,0)                  
,    WEEKDAY_30                                   varchar(1)                    
,    HOLIDAY_30                                   varchar(1)                    
,    COMPANY_HOLIDAY_30                           varchar(1)                    
,    BANK_HOLIDAY_30                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_30                    numeric(3,0)                  
,    WEEKDAY_31                                   varchar(1)                    
,    HOLIDAY_31                                   varchar(1)                    
,    COMPANY_HOLIDAY_31                           varchar(1)                    
,    BANK_HOLIDAY_31                              varchar(1)                    
,    CUMULATIVE_ANNUAL_DAYS_31                    numeric(3,0)                  
,    SALES_COMPANY_HOLIDAY_01                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_02                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_03                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_04                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_05                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_06                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_07                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_08                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_09                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_10                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_11                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_12                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_13                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_14                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_15                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_16                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_17                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_18                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_19                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_20                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_21                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_22                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_23                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_24                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_25                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_26                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_27                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_28                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_29                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_30                     varchar(1)                    
,    SALES_COMPANY_HOLIDAY_31                     varchar(1)                    
,    XXXX_01                                      varchar(1)                    
,    XXXX_02                                      varchar(1)                    
,    XXXX_03                                      varchar(1)                    
,    XXXX_04                                      varchar(1)                    
,    XXXX_05                                      varchar(1)                    
,    XXXX_06                                      varchar(1)                    
,    XXXX_07                                      varchar(1)                    
,    XXXX_08                                      varchar(1)                    
,    XXXX_09                                      varchar(1)                    
,    XXXX_10                                      varchar(1)                    
,    XXXX_11                                      varchar(1)                    
,    XXXX_12                                      varchar(1)                    
,    XXXX_13                                      varchar(1)                    
,    XXXX_14                                      varchar(1)                    
,    XXXX_15                                      varchar(1)                    
,    XXXX_16                                      varchar(1)                    
,    XXXX_17                                      varchar(1)                    
,    XXXX_18                                      varchar(1)                    
,    XXXX_19                                      varchar(1)                    
,    XXXX_20                                      varchar(1)                    
,    XXXX_21                                      varchar(1)                    
,    XXXX_22                                      varchar(1)                    
,    XXXX_23                                      varchar(1)                    
,    XXXX_24                                      varchar(1)                    
,    XXXX_25                                      varchar(1)                    
,    XXXX_26                                      varchar(1)                    
,    XXXX_27                                      varchar(1)                    
,    XXXX_28                                      varchar(1)                    
,    XXXX_29                                      varchar(1)                    
,    XXXX_30                                      varchar(1)                    
,    XXXX_31                                      varchar(1)                    
,    XXXX_ALT_01                                  varchar(1)                    
,    XXXX_ALT_02                                  varchar(1)                    
,    XXXX_ALT_03                                  varchar(1)                    
,    XXXX_ALT_04                                  varchar(1)                    
,    XXXX_ALT_05                                  varchar(1)                    
,    XXXX_ALT_06                                  varchar(1)                    
,    XXXX_ALT_07                                  varchar(1)                    
,    XXXX_ALT_08                                  varchar(1)                    
,    XXXX_ALT_09                                  varchar(1)                    
,    XXXX_ALT_10                                  varchar(1)                    
,    XXXX_ALT_11                                  varchar(1)                    
,    XXXX_ALT_12                                  varchar(1)                    
,    XXXX_ALT_13                                  varchar(1)                    
,    XXXX_ALT_14                                  varchar(1)                    
,    XXXX_ALT_15                                  varchar(1)                    
,    XXXX_ALT_16                                  varchar(1)                    
,    XXXX_ALT_17                                  varchar(1)                    
,    XXXX_ALT_18                                  varchar(1)                    
,    XXXX_ALT_19                                  varchar(1)                    
,    XXXX_ALT_20                                  varchar(1)                    
,    XXXX_ALT_21                                  varchar(1)                    
,    XXXX_ALT_22                                  varchar(1)                    
,    XXXX_ALT_23                                  varchar(1)                    
,    XXXX_ALT_24                                  varchar(1)                    
,    XXXX_ALT_25                                  varchar(1)                    
,    XXXX_ALT_26                                  varchar(1)                    
,    XXXX_ALT_27                                  varchar(1)                    
,    XXXX_ALT_28                                  varchar(1)                    
,    XXXX_ALT_29                                  varchar(1)                    
,    XXXX_ALT_30                                  varchar(1)                    
,    XXXX_ALT_31                                  varchar(1)                    
,    XXXX_ALT2_01                                 varchar(1)                    
,    XXXX_ALT2_02                                 varchar(1)                    
,    XXXX_ALT2_03                                 varchar(1)                    
,    XXXX_ALT2_04                                 varchar(1)                    
,    XXXX_ALT2_05                                 varchar(1)                    
,    XXXX_ALT2_06                                 varchar(1)                    
,    XXXX_ALT2_07                                 varchar(1)                    
,    XXXX_ALT2_08                                 varchar(1)                    
,    XXXX_ALT2_09                                 varchar(1)                    
,    XXXX_ALT2_10                                 varchar(1)                    
,    XXXX_ALT2_11                                 varchar(1)                    
,    XXXX_ALT2_12                                 varchar(1)                    
,    XXXX_ALT2_13                                 varchar(1)                    
,    XXXX_ALT2_14                                 varchar(1)                    
,    XXXX_ALT2_15                                 varchar(1)                    
,    XXXX_ALT2_16                                 varchar(1)                    
,    XXXX_ALT2_17                                 varchar(1)                    
,    XXXX_ALT2_18                                 varchar(1)                    
,    XXXX_ALT2_19                                 varchar(1)                    
,    XXXX_ALT2_20                                 varchar(1)                    
,    XXXX_ALT2_21                                 varchar(1)                    
,    XXXX_ALT2_22                                 varchar(1)                    
,    XXXX_ALT2_23                                 varchar(1)                    
,    XXXX_ALT2_24                                 varchar(1)                    
,    XXXX_ALT2_25                                 varchar(1)                    
,    XXXX_ALT2_26                                 varchar(1)                    
,    XXXX_ALT2_27                                 varchar(1)                    
,    XXXX_ALT2_28                                 varchar(1)                    
,    XXXX_ALT2_29                                 varchar(1)                    
,    XXXX_ALT2_30                                 varchar(1)                    
,    XXXX_ALT2_31                                 varchar(1)                    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE CALENDAR_MASTER IS 'カレンダーマスタ 既存システム物理名: XXYMDP';
COMMENT ON COLUMN CALENDAR_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: XXY01D';
COMMENT ON COLUMN CALENDAR_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: XXY02H';
COMMENT ON COLUMN CALENDAR_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: XXY03D';
COMMENT ON COLUMN CALENDAR_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: XXY04H';
COMMENT ON COLUMN CALENDAR_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: XXY05P';
COMMENT ON COLUMN CALENDAR_MASTER.UPDATER IS '更新者 既存システム物理名: XXY06P';
COMMENT ON COLUMN CALENDAR_MASTER.REFLECTION_DATE IS '反映日付 既存システム物理名: XXY07D';
COMMENT ON COLUMN CALENDAR_MASTER.DELETE_FLAG IS '削除フラグ 既存システム物理名: XXY08S';
COMMENT ON COLUMN CALENDAR_MASTER.YEAR_MONTH IS '年月 既存システム物理名: XXYA1K';
COMMENT ON COLUMN CALENDAR_MASTER.DAYS_IN_MONTH IS '月日数 既存システム物理名: XXYA2L';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_WORK_DAYS IS '当社出勤数 既存システム物理名: XXYA3L';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_DAYS_PREV_MONTH_END IS '前月末通算日数 既存システム物理名: XXYA4L';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_PREV_MONTH_END IS '前月末曜日 既存システム物理名: XXYA5B';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_01 IS '曜日01 既存システム物理名: XXY011';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_01 IS '日祝日01 既存システム物理名: XXY012';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_01 IS '当社休日01 既存システム物理名: XXY013';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_01 IS '銀行休日01 既存システム物理名: XXY014';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_01 IS '年間通算日数01 既存システム物理名: XXY015';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_02 IS '曜日02 既存システム物理名: XXY021';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_02 IS '日祝日02 既存システム物理名: XXY022';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_02 IS '当社休日02 既存システム物理名: XXY023';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_02 IS '銀行休日02 既存システム物理名: XXY024';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_02 IS '年間通算日数02 既存システム物理名: XXY025';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_03 IS '曜日03 既存システム物理名: XXY031';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_03 IS '日祝日03 既存システム物理名: XXY032';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_03 IS '当社休日03 既存システム物理名: XXY033';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_03 IS '銀行休日03 既存システム物理名: XXY034';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_03 IS '年間通算日数03 既存システム物理名: XXY035';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_04 IS '曜日04 既存システム物理名: XXY041';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_04 IS '日祝日04 既存システム物理名: XXY042';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_04 IS '当社休日04 既存システム物理名: XXY043';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_04 IS '銀行休日04 既存システム物理名: XXY044';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_04 IS '年間通算日数04 既存システム物理名: XXY045';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_05 IS '曜日05 既存システム物理名: XXY051';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_05 IS '日祝日05 既存システム物理名: XXY052';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_05 IS '当社休日05 既存システム物理名: XXY053';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_05 IS '銀行休日05 既存システム物理名: XXY054';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_05 IS '年間通算日数05 既存システム物理名: XXY055';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_06 IS '曜日06 既存システム物理名: XXY061';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_06 IS '日祝日06 既存システム物理名: XXY062';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_06 IS '当社休日06 既存システム物理名: XXY063';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_06 IS '銀行休日06 既存システム物理名: XXY064';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_06 IS '年間通算日数06 既存システム物理名: XXY065';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_07 IS '曜日07 既存システム物理名: XXY071';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_07 IS '日祝日07 既存システム物理名: XXY072';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_07 IS '当社休日07 既存システム物理名: XXY073';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_07 IS '銀行休日07 既存システム物理名: XXY074';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_07 IS '年間通算日数07 既存システム物理名: XXY075';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_08 IS '曜日08 既存システム物理名: XXY081';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_08 IS '日祝日08 既存システム物理名: XXY082';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_08 IS '当社休日08 既存システム物理名: XXY083';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_08 IS '銀行休日08 既存システム物理名: XXY084';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_08 IS '年間通算日数08 既存システム物理名: XXY085';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_09 IS '曜日09 既存システム物理名: XXY091';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_09 IS '日祝日09 既存システム物理名: XXY092';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_09 IS '当社休日09 既存システム物理名: XXY093';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_09 IS '銀行休日09 既存システム物理名: XXY094';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_09 IS '年間通算日数09 既存システム物理名: XXY095';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_10 IS '曜日10 既存システム物理名: XXY101';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_10 IS '日祝日10 既存システム物理名: XXY102';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_10 IS '当社休日10 既存システム物理名: XXY103';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_10 IS '銀行休日10 既存システム物理名: XXY104';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_10 IS '年間通算日数10 既存システム物理名: XXY105';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_11 IS '曜日11 既存システム物理名: XXY111';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_11 IS '日祝日11 既存システム物理名: XXY112';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_11 IS '当社休日11 既存システム物理名: XXY113';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_11 IS '銀行休日11 既存システム物理名: XXY114';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_11 IS '年間通算日数11 既存システム物理名: XXY115';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_12 IS '曜日12 既存システム物理名: XXY121';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_12 IS '日祝日12 既存システム物理名: XXY122';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_12 IS '当社休日12 既存システム物理名: XXY123';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_12 IS '銀行休日12 既存システム物理名: XXY124';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_12 IS '年間通算日数12 既存システム物理名: XXY125';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_13 IS '曜日13 既存システム物理名: XXY131';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_13 IS '日祝日13 既存システム物理名: XXY132';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_13 IS '当社休日13 既存システム物理名: XXY133';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_13 IS '銀行休日13 既存システム物理名: XXY134';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_13 IS '年間通算日数13 既存システム物理名: XXY135';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_14 IS '曜日14 既存システム物理名: XXY141';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_14 IS '日祝日14 既存システム物理名: XXY142';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_14 IS '当社休日14 既存システム物理名: XXY143';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_14 IS '銀行休日14 既存システム物理名: XXY144';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_14 IS '年間通算日数14 既存システム物理名: XXY145';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_15 IS '曜日15 既存システム物理名: XXY151';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_15 IS '日祝日15 既存システム物理名: XXY152';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_15 IS '当社休日15 既存システム物理名: XXY153';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_15 IS '銀行休日15 既存システム物理名: XXY154';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_15 IS '年間通算日数15 既存システム物理名: XXY155';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_16 IS '曜日16 既存システム物理名: XXY161';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_16 IS '日祝日16 既存システム物理名: XXY162';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_16 IS '当社休日16 既存システム物理名: XXY163';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_16 IS '銀行休日16 既存システム物理名: XXY164';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_16 IS '年間通算日数16 既存システム物理名: XXY165';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_17 IS '曜日17 既存システム物理名: XXY171';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_17 IS '日祝日17 既存システム物理名: XXY172';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_17 IS '当社休日17 既存システム物理名: XXY173';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_17 IS '銀行休日17 既存システム物理名: XXY174';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_17 IS '年間通算日数17 既存システム物理名: XXY175';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_18 IS '曜日18 既存システム物理名: XXY181';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_18 IS '日祝日18 既存システム物理名: XXY182';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_18 IS '当社休日18 既存システム物理名: XXY183';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_18 IS '銀行休日18 既存システム物理名: XXY184';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_18 IS '年間通算日数18 既存システム物理名: XXY185';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_19 IS '曜日19 既存システム物理名: XXY191';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_19 IS '日祝日19 既存システム物理名: XXY192';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_19 IS '当社休日19 既存システム物理名: XXY193';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_19 IS '銀行休日19 既存システム物理名: XXY194';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_19 IS '年間通算日数19 既存システム物理名: XXY195';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_20 IS '曜日20 既存システム物理名: XXY201';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_20 IS '日祝日20 既存システム物理名: XXY202';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_20 IS '当社休日20 既存システム物理名: XXY203';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_20 IS '銀行休日20 既存システム物理名: XXY204';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_20 IS '年間通算日数20 既存システム物理名: XXY205';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_21 IS '曜日21 既存システム物理名: XXY211';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_21 IS '日祝日21 既存システム物理名: XXY212';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_21 IS '当社休日21 既存システム物理名: XXY213';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_21 IS '銀行休日21 既存システム物理名: XXY214';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_21 IS '年間通算日数21 既存システム物理名: XXY215';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_22 IS '曜日22 既存システム物理名: XXY221';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_22 IS '日祝日22 既存システム物理名: XXY222';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_22 IS '当社休日22 既存システム物理名: XXY223';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_22 IS '銀行休日22 既存システム物理名: XXY224';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_22 IS '年間通算日数22 既存システム物理名: XXY225';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_23 IS '曜日23 既存システム物理名: XXY231';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_23 IS '日祝日23 既存システム物理名: XXY232';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_23 IS '当社休日23 既存システム物理名: XXY233';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_23 IS '銀行休日23 既存システム物理名: XXY234';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_23 IS '年間通算日数23 既存システム物理名: XXY235';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_24 IS '曜日24 既存システム物理名: XXY241';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_24 IS '日祝日24 既存システム物理名: XXY242';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_24 IS '当社休日24 既存システム物理名: XXY243';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_24 IS '銀行休日24 既存システム物理名: XXY244';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_24 IS '年間通算日数24 既存システム物理名: XXY245';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_25 IS '曜日25 既存システム物理名: XXY251';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_25 IS '日祝日25 既存システム物理名: XXY252';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_25 IS '当社休日25 既存システム物理名: XXY253';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_25 IS '銀行休日25 既存システム物理名: XXY254';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_25 IS '年間通算日数25 既存システム物理名: XXY255';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_26 IS '曜日26 既存システム物理名: XXY261';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_26 IS '日祝日26 既存システム物理名: XXY262';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_26 IS '当社休日26 既存システム物理名: XXY263';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_26 IS '銀行休日26 既存システム物理名: XXY264';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_26 IS '年間通算日数26 既存システム物理名: XXY265';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_27 IS '曜日27 既存システム物理名: XXY271';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_27 IS '日祝日27 既存システム物理名: XXY272';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_27 IS '当社休日27 既存システム物理名: XXY273';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_27 IS '銀行休日27 既存システム物理名: XXY274';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_27 IS '年間通算日数27 既存システム物理名: XXY275';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_28 IS '曜日28 既存システム物理名: XXY281';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_28 IS '日祝日28 既存システム物理名: XXY282';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_28 IS '当社休日28 既存システム物理名: XXY283';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_28 IS '銀行休日28 既存システム物理名: XXY284';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_28 IS '年間通算日数28 既存システム物理名: XXY285';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_29 IS '曜日29 既存システム物理名: XXY291';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_29 IS '日祝日29 既存システム物理名: XXY292';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_29 IS '当社休日29 既存システム物理名: XXY293';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_29 IS '銀行休日29 既存システム物理名: XXY294';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_29 IS '年間通算日数29 既存システム物理名: XXY295';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_30 IS '曜日30 既存システム物理名: XXY301';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_30 IS '日祝日30 既存システム物理名: XXY302';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_30 IS '当社休日30 既存システム物理名: XXY303';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_30 IS '銀行休日30 既存システム物理名: XXY304';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_30 IS '年間通算日数30 既存システム物理名: XXY305';
COMMENT ON COLUMN CALENDAR_MASTER.WEEKDAY_31 IS '曜日31 既存システム物理名: XXY311';
COMMENT ON COLUMN CALENDAR_MASTER.HOLIDAY_31 IS '日祝日31 既存システム物理名: XXY312';
COMMENT ON COLUMN CALENDAR_MASTER.COMPANY_HOLIDAY_31 IS '当社休日31 既存システム物理名: XXY313';
COMMENT ON COLUMN CALENDAR_MASTER.BANK_HOLIDAY_31 IS '銀行休日31 既存システム物理名: XXY314';
COMMENT ON COLUMN CALENDAR_MASTER.CUMULATIVE_ANNUAL_DAYS_31 IS '年間通算日数31 既存システム物理名: XXY315';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_01 IS '販社休日01 既存システム物理名: XXY016';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_02 IS '販社休日02 既存システム物理名: XXY026';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_03 IS '販社休日03 既存システム物理名: XXY036';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_04 IS '販社休日04 既存システム物理名: XXY046';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_05 IS '販社休日05 既存システム物理名: XXY056';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_06 IS '販社休日06 既存システム物理名: XXY066';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_07 IS '販社休日07 既存システム物理名: XXY076';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_08 IS '販社休日08 既存システム物理名: XXY086';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_09 IS '販社休日09 既存システム物理名: XXY096';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_10 IS '販社休日10 既存システム物理名: XXY106';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_11 IS '販社休日11 既存システム物理名: XXY116';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_12 IS '販社休日12 既存システム物理名: XXY126';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_13 IS '販社休日13 既存システム物理名: XXY136';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_14 IS '販社休日14 既存システム物理名: XXY146';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_15 IS '販社休日15 既存システム物理名: XXY156';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_16 IS '販社休日16 既存システム物理名: XXY166';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_17 IS '販社休日17 既存システム物理名: XXY176';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_18 IS '販社休日18 既存システム物理名: XXY186';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_19 IS '販社休日19 既存システム物理名: XXY196';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_20 IS '販社休日20 既存システム物理名: XXY206';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_21 IS '販社休日21 既存システム物理名: XXY216';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_22 IS '販社休日22 既存システム物理名: XXY226';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_23 IS '販社休日23 既存システム物理名: XXY236';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_24 IS '販社休日24 既存システム物理名: XXY246';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_25 IS '販社休日25 既存システム物理名: XXY256';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_26 IS '販社休日26 既存システム物理名: XXY266';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_27 IS '販社休日27 既存システム物理名: XXY276';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_28 IS '販社休日28 既存システム物理名: XXY286';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_29 IS '販社休日29 既存システム物理名: XXY296';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_30 IS '販社休日30 既存システム物理名: XXY306';
COMMENT ON COLUMN CALENDAR_MASTER.SALES_COMPANY_HOLIDAY_31 IS '販社休日31 既存システム物理名: XXY316';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_01 IS 'ＸＸＸＸ01 既存システム物理名: XXY017';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_02 IS 'ＸＸＸＸ02 既存システム物理名: XXY027';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_03 IS 'ＸＸＸＸ03 既存システム物理名: XXY037';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_04 IS 'ＸＸＸＸ04 既存システム物理名: XXY047';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_05 IS 'ＸＸＸＸ05 既存システム物理名: XXY057';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_06 IS 'ＸＸＸＸ06 既存システム物理名: XXY067';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_07 IS 'ＸＸＸＸ07 既存システム物理名: XXY077';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_08 IS 'ＸＸＸＸ08 既存システム物理名: XXY087';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_09 IS 'ＸＸＸＸ09 既存システム物理名: XXY097';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_10 IS 'ＸＸＸＸ10 既存システム物理名: XXY107';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_11 IS 'ＸＸＸＸ11 既存システム物理名: XXY117';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_12 IS 'ＸＸＸＸ12 既存システム物理名: XXY127';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_13 IS 'ＸＸＸＸ13 既存システム物理名: XXY137';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_14 IS 'ＸＸＸＸ14 既存システム物理名: XXY147';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_15 IS 'ＸＸＸＸ15 既存システム物理名: XXY157';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_16 IS 'ＸＸＸＸ16 既存システム物理名: XXY167';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_17 IS 'ＸＸＸＸ17 既存システム物理名: XXY177';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_18 IS 'ＸＸＸＸ18 既存システム物理名: XXY187';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_19 IS 'ＸＸＸＸ19 既存システム物理名: XXY197';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_20 IS 'ＸＸＸＸ20 既存システム物理名: XXY207';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_21 IS 'ＸＸＸＸ21 既存システム物理名: XXY217';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_22 IS 'ＸＸＸＸ22 既存システム物理名: XXY227';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_23 IS 'ＸＸＸＸ23 既存システム物理名: XXY237';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_24 IS 'ＸＸＸＸ24 既存システム物理名: XXY247';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_25 IS 'ＸＸＸＸ25 既存システム物理名: XXY257';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_26 IS 'ＸＸＸＸ26 既存システム物理名: XXY267';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_27 IS 'ＸＸＸＸ27 既存システム物理名: XXY277';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_28 IS 'ＸＸＸＸ28 既存システム物理名: XXY287';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_29 IS 'ＸＸＸＸ29 既存システム物理名: XXY297';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_30 IS 'ＸＸＸＸ30 既存システム物理名: XXY307';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_31 IS 'ＸＸＸＸ31 既存システム物理名: XXY317';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_01 IS 'ＸＸＸＸ01 既存システム物理名: XXY018';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_02 IS 'ＸＸＸＸ02 既存システム物理名: XXY028';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_03 IS 'ＸＸＸＸ03 既存システム物理名: XXY038';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_04 IS 'ＸＸＸＸ04 既存システム物理名: XXY048';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_05 IS 'ＸＸＸＸ05 既存システム物理名: XXY058';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_06 IS 'ＸＸＸＸ06 既存システム物理名: XXY068';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_07 IS 'ＸＸＸＸ07 既存システム物理名: XXY078';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_08 IS 'ＸＸＸＸ08 既存システム物理名: XXY088';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_09 IS 'ＸＸＸＸ09 既存システム物理名: XXY098';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_10 IS 'ＸＸＸＸ10 既存システム物理名: XXY108';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_11 IS 'ＸＸＸＸ11 既存システム物理名: XXY118';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_12 IS 'ＸＸＸＸ12 既存システム物理名: XXY128';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_13 IS 'ＸＸＸＸ13 既存システム物理名: XXY138';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_14 IS 'ＸＸＸＸ14 既存システム物理名: XXY148';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_15 IS 'ＸＸＸＸ15 既存システム物理名: XXY158';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_16 IS 'ＸＸＸＸ16 既存システム物理名: XXY168';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_17 IS 'ＸＸＸＸ17 既存システム物理名: XXY178';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_18 IS 'ＸＸＸＸ18 既存システム物理名: XXY188';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_19 IS 'ＸＸＸＸ19 既存システム物理名: XXY198';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_20 IS 'ＸＸＸＸ20 既存システム物理名: XXY208';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_21 IS 'ＸＸＸＸ21 既存システム物理名: XXY218';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_22 IS 'ＸＸＸＸ22 既存システム物理名: XXY228';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_23 IS 'ＸＸＸＸ23 既存システム物理名: XXY238';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_24 IS 'ＸＸＸＸ24 既存システム物理名: XXY248';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_25 IS 'ＸＸＸＸ25 既存システム物理名: XXY258';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_26 IS 'ＸＸＸＸ26 既存システム物理名: XXY268';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_27 IS 'ＸＸＸＸ27 既存システム物理名: XXY278';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_28 IS 'ＸＸＸＸ28 既存システム物理名: XXY288';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_29 IS 'ＸＸＸＸ29 既存システム物理名: XXY298';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_30 IS 'ＸＸＸＸ30 既存システム物理名: XXY308';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT_31 IS 'ＸＸＸＸ31 既存システム物理名: XXY318';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_01 IS 'ＸＸＸＸ01 既存システム物理名: XXY019';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_02 IS 'ＸＸＸＸ02 既存システム物理名: XXY029';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_03 IS 'ＸＸＸＸ03 既存システム物理名: XXY039';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_04 IS 'ＸＸＸＸ04 既存システム物理名: XXY049';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_05 IS 'ＸＸＸＸ05 既存システム物理名: XXY059';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_06 IS 'ＸＸＸＸ06 既存システム物理名: XXY069';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_07 IS 'ＸＸＸＸ07 既存システム物理名: XXY079';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_08 IS 'ＸＸＸＸ08 既存システム物理名: XXY089';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_09 IS 'ＸＸＸＸ09 既存システム物理名: XXY099';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_10 IS 'ＸＸＸＸ10 既存システム物理名: XXY109';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_11 IS 'ＸＸＸＸ11 既存システム物理名: XXY119';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_12 IS 'ＸＸＸＸ12 既存システム物理名: XXY129';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_13 IS 'ＸＸＸＸ13 既存システム物理名: XXY139';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_14 IS 'ＸＸＸＸ14 既存システム物理名: XXY149';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_15 IS 'ＸＸＸＸ15 既存システム物理名: XXY159';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_16 IS 'ＸＸＸＸ16 既存システム物理名: XXY169';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_17 IS 'ＸＸＸＸ17 既存システム物理名: XXY179';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_18 IS 'ＸＸＸＸ18 既存システム物理名: XXY189';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_19 IS 'ＸＸＸＸ19 既存システム物理名: XXY199';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_20 IS 'ＸＸＸＸ20 既存システム物理名: XXY209';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_21 IS 'ＸＸＸＸ21 既存システム物理名: XXY219';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_22 IS 'ＸＸＸＸ22 既存システム物理名: XXY229';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_23 IS 'ＸＸＸＸ23 既存システム物理名: XXY239';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_24 IS 'ＸＸＸＸ24 既存システム物理名: XXY249';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_25 IS 'ＸＸＸＸ25 既存システム物理名: XXY259';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_26 IS 'ＸＸＸＸ26 既存システム物理名: XXY269';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_27 IS 'ＸＸＸＸ27 既存システム物理名: XXY279';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_28 IS 'ＸＸＸＸ28 既存システム物理名: XXY289';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_29 IS 'ＸＸＸＸ29 既存システム物理名: XXY299';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_30 IS 'ＸＸＸＸ30 既存システム物理名: XXY309';
COMMENT ON COLUMN CALENDAR_MASTER.XXXX_ALT2_31 IS 'ＸＸＸＸ31 既存システム物理名: XXY319';
