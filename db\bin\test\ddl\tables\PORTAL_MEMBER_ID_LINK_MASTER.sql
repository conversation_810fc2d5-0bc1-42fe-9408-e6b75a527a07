-- TABLE: PORTAL_MEMBER_ID_LINK_MASTER(ポータル版会員ID紐付けマスタ)

CREATE TABLE PORTAL_MEMBER_ID_LINK_MASTER(
     PORTAL_MEMBER_ID                             numeric(8,0)                  
,    BRANCH_ID                                    varchar(3)        NOT NULL    
,    APPLICATION_START_DATE                       numeric(8,0)      NOT NULL    
,    APPLICATION_END_DATE                         numeric(8,0)      NOT NULL    
,    LOGICAL_DELETE_SIGN                          varchar(1)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATER_ID                                   varchar(10)                   
,    CONSTRAINT PK_PORTAL_MEMBER_ID_LINK_MASTE PRIMARY KEY (BRANCH_ID, APPLICATION_START_DATE, APPLICATION_END_DATE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PORTAL_MEMBER_ID_LINK_MASTER IS 'ポータル版会員ID紐付けマスタ 既存システム物理名: ERA15P';
COMMENT ON COLUMN PORTAL_MEMBER_ID_LINK_MASTER.PORTAL_MEMBER_ID IS 'ポータル版 いい部屋会員ID 既存システム物理名: A1501C';
COMMENT ON COLUMN PORTAL_MEMBER_ID_LINK_MASTER.BRANCH_ID IS '支店ID 既存システム物理名: A1502C';
COMMENT ON COLUMN PORTAL_MEMBER_ID_LINK_MASTER.APPLICATION_START_DATE IS '適用開始年月日 既存システム物理名: A1503D';
COMMENT ON COLUMN PORTAL_MEMBER_ID_LINK_MASTER.APPLICATION_END_DATE IS '適用終了年月日 既存システム物理名: A1504D';
COMMENT ON COLUMN PORTAL_MEMBER_ID_LINK_MASTER.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: A1505S';
COMMENT ON COLUMN PORTAL_MEMBER_ID_LINK_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: A1506D';
COMMENT ON COLUMN PORTAL_MEMBER_ID_LINK_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: A1507T';
COMMENT ON COLUMN PORTAL_MEMBER_ID_LINK_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: A1508D';
COMMENT ON COLUMN PORTAL_MEMBER_ID_LINK_MASTER.CREATION_TIME IS '更新時刻 既存システム物理名: A1509T';
COMMENT ON COLUMN PORTAL_MEMBER_ID_LINK_MASTER.UPDATER_ID IS '更新者ID 既存システム物理名: A1510C';
