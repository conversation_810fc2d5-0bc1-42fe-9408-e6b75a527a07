package jp.ne.simplex.application.model

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import jp.ne.simplex.shared.TestCase

class PropertyTest : FunSpec({

    context("物件の建物種別と物件種別の紐付けの確認") {

        listOf(
            // @formatter:off
            TestCase(input = Building.Type.APARTMENT, expected = Property.Type.RESIDENTIAL),
            TestCase(input = Building.Type.MANSION, expected = Property.Type.RESIDENTIAL),
            TestCase(input = Building.Type.DETACHED_HOUSE, expected = Property.Type.RESIDENTIAL),
            TestCase(input = Building.Type.COMMERCIAL_APARTMENT, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.STORE_APARTMENT, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.COMMERCIAL_MANSION, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.STORE_MANSION, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.WAREHOUSE, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.OFFICE_WAREHOUSE, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.TRUNK_ROOM, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.FACTORY, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.OFFICE_FACTORY, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.OFFICE, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.WAREHOUSE_OFFICE, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.FACTORY_OFFICE, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.DEDICATED_OFFICE, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.STORE, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.APARTMENT_STORE, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.MANSION_STORE, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.BUILDING, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.COMMERCIAL_DETACHED_HOUSE, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.PARKING, expected = Property.Type.COMMERCIAL),
            TestCase(input = Building.Type.OTHER, expected = Property.Type.COMMERCIAL),
            // @formatter:on
        ).forEach {
            test("建物種別=${it.input}の時、物件種別は${it.expected}であること") {
                Property.Type.of(it.input).shouldBe(it.expected)
            }
        }
    }
})
