package jp.ne.simplex.application.repository.db.pojos

import jp.ne.simplex.application.model.*
import jp.ne.simplex.shared.BooleanExtension.Companion.toInt
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import org.slf4j.LoggerFactory
import kotlin.String

data class ExclusivePropertiesSearchPojo(
    var id: Long,
    var buildingCode: String,
    var roomCode: String,
    var salesOfficeCode: String?,
    var exclusiveFrom: Int,
    var exclusiveTo: Int,
    var creationDate: Int,
    var earlyClosureFlag: String,
    var creator: String? = null,
    var updateDate: Int,
    var updater: String? = null,
    var companyType: Int,
    var exclusiveTarget: String? = null,
    var propertyName: String? = null,
    var roomNumber: String? = null,
    var eCode: String? = null,
    var totalCount: Int,
    var deleteFlag:String,
) {
    companion object {
        private val log = LoggerFactory.getLogger(ExclusivePropertiesSearchPojo::class.java)
    }

    fun toExclusivePropertyInfo(): ExclusivePropertyInfo? {
        return try {
            val companyType = ExclusiveProperty.CompanyType.fromValue(companyType)!!

            ExclusivePropertyInfo(
                id = ExclusiveProperty.Id.of(id),
                propertyId = Property.Id(Building.Code.of(buildingCode), Room.Code.of(roomCode)),
                buildingName = propertyName,
                roomNumber = roomNumber?.let { Room.Number.of(it) },
                exclusiveRange = DateRange.of(
                    from = exclusiveFrom.toString().yyyyMMdd(),
                    to = exclusiveTo.toString().yyyyMMdd(),
                    allowSameDate = true,
                ),
                exclusiveTarget = when (companyType) {
                    ExclusiveProperty.CompanyType.RealEstate ->
                        ExclusiveProperty.ExclusiveTarget(companyType, Agent.ECode.of(eCode!!))

                    else ->
                        ExclusiveProperty.ExclusiveTarget(companyType, null)

                },

                exclusiveTargetName = when (companyType) {
                    ExclusiveProperty.CompanyType.RealEstate ->
                        exclusiveTarget

                    ExclusiveProperty.CompanyType.Leasing ->
                        "リーシング"

                    ExclusiveProperty.CompanyType.HouseCom ->
                        "ハウスコム"
                },

                //exclusiveTargetName = exclusiveTarget,
                earlyClosureFlag = earlyClosureFlag.toInt() == true.toInt(),
                deleteFlag = deleteFlag.toInt() == true.toInt(),
                createDate = creationDate.toString().yyyyMMdd(),
                creator = creator.let { Employee.Name.of(it) },
                creatorAffiliationOfficeCode = salesOfficeCode?.let { Office.Code.of(it) },
                updateDate = updateDate.toString().yyyyMMdd(),
                updater = updater.let { Employee.Name.of(it) },
            )
        } catch (_: Exception) {
            // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
            log.warn("Failed to deserialize exclusive_properties_search record. $this")
            return null
        }
    }

}
