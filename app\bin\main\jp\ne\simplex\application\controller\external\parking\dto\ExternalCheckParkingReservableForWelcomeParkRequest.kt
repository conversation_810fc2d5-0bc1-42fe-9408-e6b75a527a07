package jp.ne.simplex.application.controller.external.parking.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.controller.external.shared.ReserveDateCreatorForWelcomePark
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.ParkingReservation
import jp.ne.simplex.application.model.RegisterParkingReservation
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException
import org.springdoc.core.annotations.ParameterObject
import java.time.LocalTime

@ParameterObject
@Schema(implementation = ExternalCheckParkingReservableForWelcomeParkRequest::class)
class ExternalCheckParkingReservableForWelcomeParkRequest(
    @JsonProperty("buildingCode")
    @field:Schema(description = "建物コード", example = "000000001")
    val buildingCode: String,

    @JsonProperty("parkingCode")
    @field:Schema(description = "駐車場コード", example = "001")
    val parkingCode: String,
) {

    // Service層の Interface に変換する
    fun toServiceInterface(): RegisterParkingReservation {

        try {
            val reserveStartDate = ReserveDateCreatorForWelcomePark.reserveStartDate()
            return RegisterParkingReservation.of(
                parkingLotId = ParkingLot.Id(
                    Building.Code.of(buildingCode),
                    ParkingLot.Code.of(parkingCode)
                ),
                parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                reservationType = ParkingReservation.Type.ONE_DAY,
                reserveStartDatetime = reserveStartDate.atTime(LocalTime.of(8, 0, 0)),
                reserveEndDatetime = reserveStartDate.plusDays(1).atTime(LocalTime.of(7, 59, 59)),
                requestSource = ParkingReservation.RequestSource.WELCOME_PARK,
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }
}
