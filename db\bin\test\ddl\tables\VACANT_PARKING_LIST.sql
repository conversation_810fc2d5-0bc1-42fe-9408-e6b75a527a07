-- TABLE: VACANT_PARKING_LIST(空き駐車場一覧)

CREATE TABLE VACANT_PARKING_LIST(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATER                                      varchar(10)                   
,    BUILDING_CD                                  varchar(9)                    
,    PARKING_CD                                   varchar(3)                    
,    PARKING_NO                                   varchar(4)                    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE VACANT_PARKING_LIST IS '空き駐車場一覧 既存システム物理名: EMPRKP';
COMMENT ON COLUMN VACANT_PARKING_LIST.CREATION_DATE IS '作成年月日 既存システム物理名: EMP01D @290';
COMMENT ON COLUMN VACANT_PARKING_LIST.CREATION_TIME IS '作成時刻 既存システム物理名: EMP02H';
COMMENT ON COLUMN VACANT_PARKING_LIST.CREATOR IS '作成者 既存システム物理名: EMP03C';
COMMENT ON COLUMN VACANT_PARKING_LIST.UPDATE_DATE IS '更新年月日 既存システム物理名: EMP04D';
COMMENT ON COLUMN VACANT_PARKING_LIST.UPDATE_TIME IS '更新時刻 既存システム物理名: EMP05H';
COMMENT ON COLUMN VACANT_PARKING_LIST.UPDATER IS '更新者 既存システム物理名: EMP06C';
COMMENT ON COLUMN VACANT_PARKING_LIST.BUILDING_CD IS '建物コード 既存システム物理名: EMP07C';
COMMENT ON COLUMN VACANT_PARKING_LIST.PARKING_CD IS '駐車場コード 既存システム物理名: EMP08C';
COMMENT ON COLUMN VACANT_PARKING_LIST.PARKING_NO IS '駐車場番号 既存システム物理名: EMP09N';
