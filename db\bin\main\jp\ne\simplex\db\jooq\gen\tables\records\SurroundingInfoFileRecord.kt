/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.SurroundingInfoFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.SurroundingInfoFilePojo

import org.jooq.impl.TableRecordImpl


/**
 * 周辺情報ファイル 既存システム物理名: ERDSJP
 */
@Suppress("UNCHECKED_CAST")
open class SurroundingInfoFileRecord private constructor() : TableRecordImpl<SurroundingInfoFileRecord>(SurroundingInfoFileTable.SURROUNDING_INFO_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updater: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var buildingCd: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var surroundingInfo_1: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var distance_1: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var surroundingInfo_2: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var distance_2: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var surroundingInfo_3: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var distance_3: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var surroundingInfo_4: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var distance_4: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var surroundingInfo_5: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var distance_5: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var surroundingInfo_6: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var distance_6: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var elementarySchool: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var middleSchool: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var burnableGarbage: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var nonBurnableGarbage: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var recyclableGarbage: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    /**
     * Create a detached, initialised SurroundingInfoFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, buildingCd: String? = null, surroundingInfo_1: String? = null, distance_1: String? = null, surroundingInfo_2: String? = null, distance_2: String? = null, surroundingInfo_3: String? = null, distance_3: String? = null, surroundingInfo_4: String? = null, distance_4: String? = null, surroundingInfo_5: String? = null, distance_5: String? = null, surroundingInfo_6: String? = null, distance_6: String? = null, elementarySchool: String? = null, middleSchool: String? = null, burnableGarbage: String? = null, nonBurnableGarbage: String? = null, recyclableGarbage: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.buildingCd = buildingCd
        this.surroundingInfo_1 = surroundingInfo_1
        this.distance_1 = distance_1
        this.surroundingInfo_2 = surroundingInfo_2
        this.distance_2 = distance_2
        this.surroundingInfo_3 = surroundingInfo_3
        this.distance_3 = distance_3
        this.surroundingInfo_4 = surroundingInfo_4
        this.distance_4 = distance_4
        this.surroundingInfo_5 = surroundingInfo_5
        this.distance_5 = distance_5
        this.surroundingInfo_6 = surroundingInfo_6
        this.distance_6 = distance_6
        this.elementarySchool = elementarySchool
        this.middleSchool = middleSchool
        this.burnableGarbage = burnableGarbage
        this.nonBurnableGarbage = nonBurnableGarbage
        this.recyclableGarbage = recyclableGarbage
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised SurroundingInfoFileRecord
     */
    constructor(value: SurroundingInfoFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.buildingCd = value.buildingCd
            this.surroundingInfo_1 = value.surroundingInfo_1
            this.distance_1 = value.distance_1
            this.surroundingInfo_2 = value.surroundingInfo_2
            this.distance_2 = value.distance_2
            this.surroundingInfo_3 = value.surroundingInfo_3
            this.distance_3 = value.distance_3
            this.surroundingInfo_4 = value.surroundingInfo_4
            this.distance_4 = value.distance_4
            this.surroundingInfo_5 = value.surroundingInfo_5
            this.distance_5 = value.distance_5
            this.surroundingInfo_6 = value.surroundingInfo_6
            this.distance_6 = value.distance_6
            this.elementarySchool = value.elementarySchool
            this.middleSchool = value.middleSchool
            this.burnableGarbage = value.burnableGarbage
            this.nonBurnableGarbage = value.nonBurnableGarbage
            this.recyclableGarbage = value.recyclableGarbage
            resetChangedOnNotNull()
        }
    }
}
