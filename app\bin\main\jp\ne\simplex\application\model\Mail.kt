package jp.ne.simplex.application.model

import jakarta.mail.internet.InternetAddress
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.shared.StringExtension.Companion.isValidEmail

/** メールアドレス */
class EmailAddress private constructor(val value: String) {
    companion object {
        fun of(value: String): EmailAddress {
            return if (value.isValidEmail()) EmailAddress(value)
            else throw ModelCreationFailedException(ErrorMessage.INVALID_EMAIL_ADDRESS.format())
        }
    }
}

class MailProperty(
    // メールテンプレートタイプ
    val mailTemplateType: MailTemplateType,
    // 送信元アドレス
    val fromAddress: EmailAddress,
    // 送信者
    val fromName: String,
    // 件名
    val subject: String,
    // url
    val url: String,
    // 送信先
    val toList: List<EmailAddress> = emptyList(),
    // CC先
    val ccList: List<EmailAddress> = emptyList(),
    // テンプレートのパラメータ
    val messageParam: Map<String, String> = emptyMap(),
) {
    fun getFrom(): InternetAddress {
        return if (this.fromName.isEmpty()) {
            InternetAddress(this.fromAddress.value)
        } else {
            InternetAddress(this.fromAddress.value, this.fromName, "UTF-8")
        }
    }

    /** メールテンプレートタイプ */
    enum class MailTemplateType(val fileName: String) {
        PARKING_YOYAKU_INFO("FE_PARKING_YOYAKU_INFO"),
        PARKING_YOYAKU_INFO_WELCOME_PARK("FE_PARKING_YOYAKU_INFO_WELCOME_PARK"),
        PARKING_YOYAKU_UKETUKE_INFO("FE_PARKING_YOYAKU_UKETUKE_INFO"),
        PARKING_YOYAKU_KEIYAKU_INFO("FE_PARKING_YOYAKU_KEIYAKU_INFO"),
        PARKING_YOYAKU_CANCEL_INFO("FE_PARKING_YOYAKU_CANCEL_INFO"),
    }
}
