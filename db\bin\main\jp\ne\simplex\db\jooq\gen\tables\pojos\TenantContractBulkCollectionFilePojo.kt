/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * テナント契約一括残集ファイル 既存システム物理名: EDCTNP
 */
@Suppress("UNCHECKED_CAST")
data class TenantContractBulkCollectionFilePojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updateProgramId: String? = null,
    var updater: String? = null,
    var tenantContractNumber: String? = null,
    var parkingTenantContract: String? = null,
    var deletionDate: Int? = null,
    var remainingCollectionApprovalDate: Int? = null,
    var cancellationApprovalDate: Int? = null,
    var moveInApplicationFee: Int? = null,
    var brokerageFee: Int? = null,
    var brokerageFeeTax: Int? = null,
    var agentFee: Int? = null,
    var agentFeeTax: Int? = null,
    var advertisingFee: Int? = null,
    var postMoveAttachmentDivision: Byte? = null,
    var agencyAutoSumDivision: Byte? = null,
    var transmissionDivision: Byte? = null,
    var transmissionDate: Int? = null,
    var transmissionTime: Int? = null,
    var parentTenantBuildingCd: String? = null,
    var parentTenantRoomCd: String? = null,
    var parentTenantParkingCd: String? = null,
    var parentTenantSurrenderPlannedDate: Int? = null,
    var pkBuildingCd: String? = null,
    var pkParkingCd: String? = null,
    var pkTenantSurrenderPlannedDate: Int? = null,
    var parkingContractFee: Int? = null,
    var parkingContractFeeTax: Int? = null,
    var agentParkingContractFee: Int? = null,
    var agentParkingContractFeeTax: Int? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: TenantContractBulkCollectionFilePojo = other as TenantContractBulkCollectionFilePojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updateProgramId == null) {
            if (o.updateProgramId != null)
                return false
        }
        else if (this.updateProgramId != o.updateProgramId)
            return false
        if (this.updater == null) {
            if (o.updater != null)
                return false
        }
        else if (this.updater != o.updater)
            return false
        if (this.tenantContractNumber == null) {
            if (o.tenantContractNumber != null)
                return false
        }
        else if (this.tenantContractNumber != o.tenantContractNumber)
            return false
        if (this.parkingTenantContract == null) {
            if (o.parkingTenantContract != null)
                return false
        }
        else if (this.parkingTenantContract != o.parkingTenantContract)
            return false
        if (this.deletionDate == null) {
            if (o.deletionDate != null)
                return false
        }
        else if (this.deletionDate != o.deletionDate)
            return false
        if (this.remainingCollectionApprovalDate == null) {
            if (o.remainingCollectionApprovalDate != null)
                return false
        }
        else if (this.remainingCollectionApprovalDate != o.remainingCollectionApprovalDate)
            return false
        if (this.cancellationApprovalDate == null) {
            if (o.cancellationApprovalDate != null)
                return false
        }
        else if (this.cancellationApprovalDate != o.cancellationApprovalDate)
            return false
        if (this.moveInApplicationFee == null) {
            if (o.moveInApplicationFee != null)
                return false
        }
        else if (this.moveInApplicationFee != o.moveInApplicationFee)
            return false
        if (this.brokerageFee == null) {
            if (o.brokerageFee != null)
                return false
        }
        else if (this.brokerageFee != o.brokerageFee)
            return false
        if (this.brokerageFeeTax == null) {
            if (o.brokerageFeeTax != null)
                return false
        }
        else if (this.brokerageFeeTax != o.brokerageFeeTax)
            return false
        if (this.agentFee == null) {
            if (o.agentFee != null)
                return false
        }
        else if (this.agentFee != o.agentFee)
            return false
        if (this.agentFeeTax == null) {
            if (o.agentFeeTax != null)
                return false
        }
        else if (this.agentFeeTax != o.agentFeeTax)
            return false
        if (this.advertisingFee == null) {
            if (o.advertisingFee != null)
                return false
        }
        else if (this.advertisingFee != o.advertisingFee)
            return false
        if (this.postMoveAttachmentDivision == null) {
            if (o.postMoveAttachmentDivision != null)
                return false
        }
        else if (this.postMoveAttachmentDivision != o.postMoveAttachmentDivision)
            return false
        if (this.agencyAutoSumDivision == null) {
            if (o.agencyAutoSumDivision != null)
                return false
        }
        else if (this.agencyAutoSumDivision != o.agencyAutoSumDivision)
            return false
        if (this.transmissionDivision == null) {
            if (o.transmissionDivision != null)
                return false
        }
        else if (this.transmissionDivision != o.transmissionDivision)
            return false
        if (this.transmissionDate == null) {
            if (o.transmissionDate != null)
                return false
        }
        else if (this.transmissionDate != o.transmissionDate)
            return false
        if (this.transmissionTime == null) {
            if (o.transmissionTime != null)
                return false
        }
        else if (this.transmissionTime != o.transmissionTime)
            return false
        if (this.parentTenantBuildingCd == null) {
            if (o.parentTenantBuildingCd != null)
                return false
        }
        else if (this.parentTenantBuildingCd != o.parentTenantBuildingCd)
            return false
        if (this.parentTenantRoomCd == null) {
            if (o.parentTenantRoomCd != null)
                return false
        }
        else if (this.parentTenantRoomCd != o.parentTenantRoomCd)
            return false
        if (this.parentTenantParkingCd == null) {
            if (o.parentTenantParkingCd != null)
                return false
        }
        else if (this.parentTenantParkingCd != o.parentTenantParkingCd)
            return false
        if (this.parentTenantSurrenderPlannedDate == null) {
            if (o.parentTenantSurrenderPlannedDate != null)
                return false
        }
        else if (this.parentTenantSurrenderPlannedDate != o.parentTenantSurrenderPlannedDate)
            return false
        if (this.pkBuildingCd == null) {
            if (o.pkBuildingCd != null)
                return false
        }
        else if (this.pkBuildingCd != o.pkBuildingCd)
            return false
        if (this.pkParkingCd == null) {
            if (o.pkParkingCd != null)
                return false
        }
        else if (this.pkParkingCd != o.pkParkingCd)
            return false
        if (this.pkTenantSurrenderPlannedDate == null) {
            if (o.pkTenantSurrenderPlannedDate != null)
                return false
        }
        else if (this.pkTenantSurrenderPlannedDate != o.pkTenantSurrenderPlannedDate)
            return false
        if (this.parkingContractFee == null) {
            if (o.parkingContractFee != null)
                return false
        }
        else if (this.parkingContractFee != o.parkingContractFee)
            return false
        if (this.parkingContractFeeTax == null) {
            if (o.parkingContractFeeTax != null)
                return false
        }
        else if (this.parkingContractFeeTax != o.parkingContractFeeTax)
            return false
        if (this.agentParkingContractFee == null) {
            if (o.agentParkingContractFee != null)
                return false
        }
        else if (this.agentParkingContractFee != o.agentParkingContractFee)
            return false
        if (this.agentParkingContractFeeTax == null) {
            if (o.agentParkingContractFeeTax != null)
                return false
        }
        else if (this.agentParkingContractFeeTax != o.agentParkingContractFeeTax)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updateProgramId == null) 0 else this.updateProgramId.hashCode())
        result = prime * result + (if (this.updater == null) 0 else this.updater.hashCode())
        result = prime * result + (if (this.tenantContractNumber == null) 0 else this.tenantContractNumber.hashCode())
        result = prime * result + (if (this.parkingTenantContract == null) 0 else this.parkingTenantContract.hashCode())
        result = prime * result + (if (this.deletionDate == null) 0 else this.deletionDate.hashCode())
        result = prime * result + (if (this.remainingCollectionApprovalDate == null) 0 else this.remainingCollectionApprovalDate.hashCode())
        result = prime * result + (if (this.cancellationApprovalDate == null) 0 else this.cancellationApprovalDate.hashCode())
        result = prime * result + (if (this.moveInApplicationFee == null) 0 else this.moveInApplicationFee.hashCode())
        result = prime * result + (if (this.brokerageFee == null) 0 else this.brokerageFee.hashCode())
        result = prime * result + (if (this.brokerageFeeTax == null) 0 else this.brokerageFeeTax.hashCode())
        result = prime * result + (if (this.agentFee == null) 0 else this.agentFee.hashCode())
        result = prime * result + (if (this.agentFeeTax == null) 0 else this.agentFeeTax.hashCode())
        result = prime * result + (if (this.advertisingFee == null) 0 else this.advertisingFee.hashCode())
        result = prime * result + (if (this.postMoveAttachmentDivision == null) 0 else this.postMoveAttachmentDivision.hashCode())
        result = prime * result + (if (this.agencyAutoSumDivision == null) 0 else this.agencyAutoSumDivision.hashCode())
        result = prime * result + (if (this.transmissionDivision == null) 0 else this.transmissionDivision.hashCode())
        result = prime * result + (if (this.transmissionDate == null) 0 else this.transmissionDate.hashCode())
        result = prime * result + (if (this.transmissionTime == null) 0 else this.transmissionTime.hashCode())
        result = prime * result + (if (this.parentTenantBuildingCd == null) 0 else this.parentTenantBuildingCd.hashCode())
        result = prime * result + (if (this.parentTenantRoomCd == null) 0 else this.parentTenantRoomCd.hashCode())
        result = prime * result + (if (this.parentTenantParkingCd == null) 0 else this.parentTenantParkingCd.hashCode())
        result = prime * result + (if (this.parentTenantSurrenderPlannedDate == null) 0 else this.parentTenantSurrenderPlannedDate.hashCode())
        result = prime * result + (if (this.pkBuildingCd == null) 0 else this.pkBuildingCd.hashCode())
        result = prime * result + (if (this.pkParkingCd == null) 0 else this.pkParkingCd.hashCode())
        result = prime * result + (if (this.pkTenantSurrenderPlannedDate == null) 0 else this.pkTenantSurrenderPlannedDate.hashCode())
        result = prime * result + (if (this.parkingContractFee == null) 0 else this.parkingContractFee.hashCode())
        result = prime * result + (if (this.parkingContractFeeTax == null) 0 else this.parkingContractFeeTax.hashCode())
        result = prime * result + (if (this.agentParkingContractFee == null) 0 else this.agentParkingContractFee.hashCode())
        result = prime * result + (if (this.agentParkingContractFeeTax == null) 0 else this.agentParkingContractFeeTax.hashCode())
        return result
    }
}
