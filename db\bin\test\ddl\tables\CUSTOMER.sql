-- TABLE: CUSTOMER(顧客)

CREATE TABLE CUSTOMER(
     CLIENT_CATEGORY                              varchar(1)        NOT NULL    
,    CLIENT_CODE                                  varchar(7)        NOT NULL    
,    CLIENT_BRANCH_CODE                           numeric(3,0)      NOT NULL    
,    INTEGRATED_CLIENT_CODE                       varchar(10)                   
,    CLIENT_NAME_KANJI                            varchar(42)                   
,    CLIENT_NAME_KANA                             varchar(40)                   
,    SEARCH_CLIENT_NAME_KANA                      varchar(25)                   
,    TRANSFER_MATCHING_NAME_1                     varchar(25)                   
,    TRANSFER_MATCHING_NAME_2                     varchar(25)                   
,    ADDRESS_CODE                                 varchar(10)                   
,    ADDRESS_DETAIL                               varchar(62)                   
,    BUILDING_NAME                                varchar(32)                   
,    POSTAL_CODE                                  varchar(8)                    
,    CLIENT_PHONE_NUMBER                          varchar(15)                   
,    BANK_CODE                                    numeric(4,0)                  
,    BANK_BRANCH_CODE                             numeric(3,0)                  
,    ACCOUNT_TYPE                                 varchar(1)                    
,    ACCOUNT_NUMBER                               numeric(10,0)                 
,    ACCOUNT_NAME                                 varchar(40)                   
,    <PERSON>LIENT_CONTRACTOR                            numeric(1,0)                  
,    CLIENT_LANDLORD                              numeric(1,0)                  
,    CLIENT_PAYEE                                 numeric(1,0)                  
,    CLIENT_REGISTRATION_DATE                     numeric(8,0)                  
,    CLIENT_MODIFICATION_DATE                     numeric(8,0)                  
,    CLIENT_DELETION_DATE                         numeric(8,0)                  
,    CREATOR_CODE                                 numeric(5,0)                  
,    CLIENT_PARENT_CHILD_CODE                     varchar(8)                    
,    CLIENT_PARENT_CHILD_BRANCH                   numeric(3,0)                  
,    CONSOLIDATION_CODE                           varchar(8)                    
,    CONSOLIDATION_BRANCH                         numeric(3,0)                  
,    INDUSTRY_CODE                                numeric(4,0)                  
,    CAREER_CODE_1                                numeric(4,0)                  
,    CAREER_CODE_2                                numeric(4,0)                  
,    CAREER_CODE_3                                numeric(4,0)                  
,    CLIENT_APPLICATION_CATEGORY                  numeric(1,0)                  
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM                               varchar(10)                   
,    USER_ID                                      varchar(10)                   
,    DELETE_FLAG                                  varchar(1)                    
,    LANDLORD_TAX_CATEGORY                        varchar(1)                    
,    DATE_OF_BIRTH                                numeric(8,0)                  
,    SURVIVAL_CATEGORY                            numeric(1,0)                  
,    DEATH_YEAR_MONTH                             numeric(6,0)                  
,    CORPORATION_CATEGORY                         numeric(1,0)                  
,    OCCUPATION_1                                 numeric(4,0)                  
,    OCCUPATION_2                                 numeric(4,0)                  
,    OCCUPATION_3                                 numeric(4,0)                  
,    OCCUPATION_4                                 numeric(4,0)                  
,    REMOTE_LANDLORD_CATEGORY                     varchar(1)                    
,    CREATOR_CODE_ST                              varchar(6)                    
,    DATE_OF_DEATH                                numeric(8,0)                  
,    CLIENT_OCCUPATION_CODE                       varchar(2)                    
,    CLIENT_FAX_NUMBER                            varchar(15)                   
,    LANDLORD_TENANT_CATEGORY                     varchar(1)                    
,    CLIENT_GENDER                                varchar(1)                    
,    CONSTRAINT PK_CUSTOMER PRIMARY KEY (CLIENT_CATEGORY, CLIENT_CODE, CLIENT_BRANCH_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE CUSTOMER IS '顧客 既存システム物理名: AXCIFP';
COMMENT ON COLUMN CUSTOMER.CLIENT_CATEGORY IS '取引先区分 既存システム物理名: AXC0C1';
COMMENT ON COLUMN CUSTOMER.CLIENT_CODE IS '取引先コード 既存システム物理名: AXC01C';
COMMENT ON COLUMN CUSTOMER.CLIENT_BRANCH_CODE IS '取引先枝番 既存システム物理名: AXC1C1';
COMMENT ON COLUMN CUSTOMER.INTEGRATED_CLIENT_CODE IS '統合取引先コード 既存システム物理名: AXCBVC';
COMMENT ON COLUMN CUSTOMER.CLIENT_NAME_KANJI IS '顧客名称・漢字 既存システム物理名: AXC02M';
COMMENT ON COLUMN CUSTOMER.CLIENT_NAME_KANA IS '顧客名称(カナ) 既存システム物理名: AXC03M';
COMMENT ON COLUMN CUSTOMER.SEARCH_CLIENT_NAME_KANA IS '検索用顧客カナ名称 既存システム物理名: AXC34M';
COMMENT ON COLUMN CUSTOMER.TRANSFER_MATCHING_NAME_1 IS '振込照合用(仮名1) 既存システム物理名: AXC35M';
COMMENT ON COLUMN CUSTOMER.TRANSFER_MATCHING_NAME_2 IS '振込照合用(仮名2) 既存システム物理名: AXC36M';
COMMENT ON COLUMN CUSTOMER.ADDRESS_CODE IS '住所コード 既存システム物理名: AXC04C';
COMMENT ON COLUMN CUSTOMER.ADDRESS_DETAIL IS '住所詳細 既存システム物理名: AXC05J';
COMMENT ON COLUMN CUSTOMER.BUILDING_NAME IS 'ビル名称 既存システム物理名: AXC37M';
COMMENT ON COLUMN CUSTOMER.POSTAL_CODE IS '郵便番号 既存システム物理名: AXC06N';
COMMENT ON COLUMN CUSTOMER.CLIENT_PHONE_NUMBER IS '顧客電話番号 既存システム物理名: AXC07N';
COMMENT ON COLUMN CUSTOMER.BANK_CODE IS '金融機関コード 既存システム物理名: AXC08C';
COMMENT ON COLUMN CUSTOMER.BANK_BRANCH_CODE IS '金融機関支店コード 既存システム物理名: AXC09C';
COMMENT ON COLUMN CUSTOMER.ACCOUNT_TYPE IS '口座種別 既存システム物理名: AXC10C';
COMMENT ON COLUMN CUSTOMER.ACCOUNT_NUMBER IS '口座番号 既存システム物理名: AXC11N';
COMMENT ON COLUMN CUSTOMER.ACCOUNT_NAME IS '口座名 既存システム物理名: AXC12M';
COMMENT ON COLUMN CUSTOMER.CLIENT_CONTRACTOR IS '顧客(施主) 既存システム物理名: AXC13B';
COMMENT ON COLUMN CUSTOMER.CLIENT_LANDLORD IS '顧客(家主) 既存システム物理名: AXC14B';
COMMENT ON COLUMN CUSTOMER.CLIENT_PAYEE IS '顧客(支払先) 既存システム物理名: AXC15B';
COMMENT ON COLUMN CUSTOMER.CLIENT_REGISTRATION_DATE IS '顧客登録日 既存システム物理名: AXC16D';
COMMENT ON COLUMN CUSTOMER.CLIENT_MODIFICATION_DATE IS '顧客修正日 既存システム物理名: AXC17D';
COMMENT ON COLUMN CUSTOMER.CLIENT_DELETION_DATE IS '顧客削除日 既存システム物理名: AXC18D';
COMMENT ON COLUMN CUSTOMER.CREATOR_CODE IS '起票者コード 既存システム物理名: AXC19C';
COMMENT ON COLUMN CUSTOMER.CLIENT_PARENT_CHILD_CODE IS '顧客親子関係コード 既存システム物理名: AXC20C';
COMMENT ON COLUMN CUSTOMER.CLIENT_PARENT_CHILD_BRANCH IS '顧客親子関係枝番 既存システム物理名: AXC34C';
COMMENT ON COLUMN CUSTOMER.CONSOLIDATION_CODE IS '名寄コード 既存システム物理名: AXC21C';
COMMENT ON COLUMN CUSTOMER.CONSOLIDATION_BRANCH IS '名寄コード枝番 既存システム物理名: AXC35C';
COMMENT ON COLUMN CUSTOMER.INDUSTRY_CODE IS '業種コード 既存システム物理名: AXC22C';
COMMENT ON COLUMN CUSTOMER.CAREER_CODE_1 IS '経歴コード1 既存システム物理名: AXC23C';
COMMENT ON COLUMN CUSTOMER.CAREER_CODE_2 IS '経歴コード2 既存システム物理名: AXC24C';
COMMENT ON COLUMN CUSTOMER.CAREER_CODE_3 IS '経歴コード3 既存システム物理名: AXC25C';
COMMENT ON COLUMN CUSTOMER.CLIENT_APPLICATION_CATEGORY IS '顧客申請区分 既存システム物理名: AXC26B';
COMMENT ON COLUMN CUSTOMER.CREATION_DATE IS '作成日付 既存システム物理名: AXC27D';
COMMENT ON COLUMN CUSTOMER.CREATION_TIME IS '作成時刻 既存システム物理名: AXC28H';
COMMENT ON COLUMN CUSTOMER.UPDATE_DATE IS '更新日付 既存システム物理名: AXC29D';
COMMENT ON COLUMN CUSTOMER.UPDATE_TIME IS '更新時刻 既存システム物理名: AXC30H';
COMMENT ON COLUMN CUSTOMER.UPDATE_PROGRAM IS '更新プログラム 既存システム物理名: AXC31P';
COMMENT ON COLUMN CUSTOMER.USER_ID IS 'ユーザーID 既存システム物理名: AXC32P';
COMMENT ON COLUMN CUSTOMER.DELETE_FLAG IS '削除フラグ 既存システム物理名: AXC33P';
COMMENT ON COLUMN CUSTOMER.LANDLORD_TAX_CATEGORY IS '家主課税者区分 既存システム物理名: AXC38B';
COMMENT ON COLUMN CUSTOMER.DATE_OF_BIRTH IS '生年月日 既存システム物理名: AXC39D';
COMMENT ON COLUMN CUSTOMER.SURVIVAL_CATEGORY IS '生存区分 既存システム物理名: AXC40B';
COMMENT ON COLUMN CUSTOMER.DEATH_YEAR_MONTH IS '死亡年月 既存システム物理名: AXC41D';
COMMENT ON COLUMN CUSTOMER.CORPORATION_CATEGORY IS '法人区分 既存システム物理名: AXC42B';
COMMENT ON COLUMN CUSTOMER.OCCUPATION_1 IS '職種1 既存システム物理名: AXC43C';
COMMENT ON COLUMN CUSTOMER.OCCUPATION_2 IS '職種2 既存システム物理名: AXC44C';
COMMENT ON COLUMN CUSTOMER.OCCUPATION_3 IS '職種3 既存システム物理名: AXC45C';
COMMENT ON COLUMN CUSTOMER.OCCUPATION_4 IS '職種4 既存システム物理名: AXC46C';
COMMENT ON COLUMN CUSTOMER.REMOTE_LANDLORD_CATEGORY IS '家主遠隔地区分 既存システム物理名: AXC47B';
COMMENT ON COLUMN CUSTOMER.CREATOR_CODE_ST IS '起票者コード－ST 既存システム物理名: AXC0ST';
COMMENT ON COLUMN CUSTOMER.DATE_OF_DEATH IS '死亡年月日 既存システム物理名: AXC48D';
COMMENT ON COLUMN CUSTOMER.CLIENT_OCCUPATION_CODE IS '顧客職業コード 既存システム物理名: AXC49C';
COMMENT ON COLUMN CUSTOMER.CLIENT_FAX_NUMBER IS '顧客FAX番号 既存システム物理名: AXC50N';
COMMENT ON COLUMN CUSTOMER.LANDLORD_TENANT_CATEGORY IS '家主／テナント区分 既存システム物理名: AXC51C';
COMMENT ON COLUMN CUSTOMER.CLIENT_GENDER IS '顧客性別 既存システム物理名: AXC52C';
