package jp.ne.simplex.shared

import com.fasterxml.jackson.annotation.JsonProperty
import java.lang.reflect.ParameterizedType

class ClassExtension {

    companion object {

        fun Class<*>.getJsonPropertyNames(): List<String> {
            val propertyNames = mutableListOf<String>()
            this.collectPropertyNames(propertyNames)
            return propertyNames
        }

        // 再帰的にプロパティ名を取得する内部関数
        private fun Class<*>.collectPropertyNames(propertyNames: MutableList<String>) {
            for (field in this.declaredFields) {
                // JsonProperty アノテーションを取得
                field.getAnnotation(JsonProperty::class.java)?.let { jsonProperty ->
                    propertyNames.add(jsonProperty.value)
                }
                // フィールドの型がリストである場合、再帰的に呼び出す
                if (List::class.java.isAssignableFrom(field.type)) {
                    val genericType = (field.genericType as? ParameterizedType)?.actualTypeArguments?.first()
                    if (genericType is Class<*>) {
                        genericType.collectPropertyNames(propertyNames) // リストの要素型で再帰呼び出し
                    }
                }
            }
        }
    }
}
