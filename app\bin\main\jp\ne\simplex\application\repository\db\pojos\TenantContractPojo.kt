package jp.ne.simplex.application.repository.db.pojos


// 引数超過対策
// Insert用
data class TenantContractPojo(
    val tenantContractNumber: String,
    val tenantContractChangeSeq: String,
    val buildingCode: String? = null,
    val parkingCode: String? = null,
    val roomCode: String? = null,
    val contractEffectiveStartDate: Int? = 0,
    override val tenantName: String? = null,
    val tenantCode: String? = null,
    override val moveInScheduledDate: Int? = null,
    override val tenantNameKanji: String? = null,
    override val contractExpiryDate: Int? = null,
    override val contractEffectiveEndDate: Int? = null,
    override val currentStateDivision: String? = null,
    override val modificationStateDivision: String? = null,
    override val moveInStartProcessedSign: Int? = null,
    override val moveOutDate: Int? = null,
    override val cancellationSign: Int? = null,
    override val vacateScheduledDate: Int? = null,
    override val vacateNoticeDate: Int? = null,
    val aggregateContractNumber: String? = null,
    val logicalDeleteSign: Int? = 0,
) : TenantContractPojoInterface

// 駐車場詳細テナント契約合算先用
data class AggregateTenantContractPojo(
    override val tenantName: String? = null,
    override val moveInScheduledDate: Int? = null,
    override val tenantNameKanji: String? = null,
    override val contractExpiryDate: Int? = null,
    override val contractEffectiveEndDate: Int? = null,
    override val currentStateDivision: String? = null,
    override val modificationStateDivision: String? = null,
    override val moveInStartProcessedSign: Int? = null,
    override val moveOutDate: Int? = null,
    override val cancellationSign: Int? = null,
    override val vacateScheduledDate: Int? = null,
    override val vacateNoticeDate: Int? = null,
) : TenantContractPojoInterface

interface TenantContractPojoInterface {
    val tenantName: String?
    val moveInScheduledDate: Int?
    val tenantNameKanji: String?
    val contractExpiryDate: Int?
    val contractEffectiveEndDate: Int?
    val currentStateDivision: String?
    val modificationStateDivision: String?
    val moveInStartProcessedSign: Int?
    val moveOutDate: Int?
    val cancellationSign: Int?
    val vacateScheduledDate: Int?
    val vacateNoticeDate: Int?
}
