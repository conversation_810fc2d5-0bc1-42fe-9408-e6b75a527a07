/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ParkingEnableTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingEnablePojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 駐車場利用停止 既存システム物理名: -
 */
@Suppress("UNCHECKED_CAST")
open class ParkingEnableRecord private constructor() : UpdatableRecordImpl<ParkingEnableRecord>(ParkingEnableTable.PARKING_ENABLE) {

    open var buildingCode: String
        set(value): Unit = set(0, value)
        get(): String = get(0) as String

    open var parkingLotCode: String
        set(value): Unit = set(1, value)
        get(): String = get(1) as String

    open var parkingLotEnable: String
        set(value): Unit = set(2, value)
        get(): String = get(2) as String

    open var creationDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var creationTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var creator: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var updateDate: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var updateTime: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var updater: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var deleteFlag: String
        set(value): Unit = set(9, value)
        get(): String = get(9) as String

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised ParkingEnableRecord
     */
    constructor(buildingCode: String, parkingLotCode: String, parkingLotEnable: String, creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, deleteFlag: String): this() {
        this.buildingCode = buildingCode
        this.parkingLotCode = parkingLotCode
        this.parkingLotEnable = parkingLotEnable
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.deleteFlag = deleteFlag
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ParkingEnableRecord
     */
    constructor(value: ParkingEnablePojo?): this() {
        if (value != null) {
            this.buildingCode = value.buildingCode
            this.parkingLotCode = value.parkingLotCode
            this.parkingLotEnable = value.parkingLotEnable
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.deleteFlag = value.deleteFlag
            resetChangedOnNotNull()
        }
    }
}
