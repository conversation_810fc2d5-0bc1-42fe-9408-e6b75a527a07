package jp.ne.simplex.application.repository.db.pojos

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Company
import org.slf4j.LoggerFactory

data class ShinsaBranchPojo(
    val shozokuCode: String? = null,
    val shozokuAbbrev1: String? = null,
) {

    companion object {
        private val log = LoggerFactory.getLogger(ShinsaBranchPojo::class.java)
    }

    fun getBranch(): Branch? {
        try {
            return Branch(
                code = Branch.Code.of(shozokuCode!!.substring(0, 3) + "000"),
                name = Branch.Name.of(shozokuAbbrev1!! + "支店"),
                company = Company.DaitouKentakuPartners,
            )
        } catch (_: Exception) {
            // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
            log.warn("Failed to deserialize AffiliationMaster record. $this")
            return null
        }
    }
}
