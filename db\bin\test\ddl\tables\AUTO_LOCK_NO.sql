-- TABLE: AUTO_LOCK_NO(オートロックNo)

CREATE TABLE AUTO_LOCK_NO(
     CREATION_DATE                                numeric(10,0)                 
,    CREATION_TIME                                numeric(8,0)                  
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(10,0)                 
,    UPDATE_TIME                                  numeric(8,0)                  
,    UPDATER                                      varchar(10)                   
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1,0)                  
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    AUTOLOCK_RELEASE_NO                          varchar(30)                   
,    RENT_FIXED_FLAG                              numeric(1)                    
,    HOME_SECURITY                                numeric(1)                    
,    RESERVE_03                                   numeric(1)                    
,    RESERVE_04                                   numeric(1)                    
,    RESERVE_05                                   numeric(1)                    
,    RESERVE_06                                   numeric(1)                    
,    RESERVE_07                                   numeric(1)                    
,    RESERVE_08                                   numeric(1)                    
,    RESERVE_09                                   numeric(1)                    
,    RESERVE_10                                   numeric(1)                    
,    CONSTRAINT PK_AUTO_LOCK_NO PRIMARY KEY (BUILDING_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE AUTO_LOCK_NO IS 'オートロックNo 既存システム物理名: EMKEYP';
COMMENT ON COLUMN AUTO_LOCK_NO.CREATION_DATE IS '作成年月日 既存システム物理名: EMK01D';
COMMENT ON COLUMN AUTO_LOCK_NO.CREATION_TIME IS '作成時刻 既存システム物理名: EMK02H';
COMMENT ON COLUMN AUTO_LOCK_NO.CREATOR IS '作成者 既存システム物理名: EMK03C';
COMMENT ON COLUMN AUTO_LOCK_NO.UPDATE_DATE IS '更新年月日 既存システム物理名: EMK04D';
COMMENT ON COLUMN AUTO_LOCK_NO.UPDATE_TIME IS '更新時刻 既存システム物理名: EMK05H';
COMMENT ON COLUMN AUTO_LOCK_NO.UPDATER IS '更新者 既存システム物理名: EMK06C';
COMMENT ON COLUMN AUTO_LOCK_NO.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EMK07N';
COMMENT ON COLUMN AUTO_LOCK_NO.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: EMK08S';
COMMENT ON COLUMN AUTO_LOCK_NO.BUILDING_CODE IS '建物コード 既存システム物理名: EMK09C';
COMMENT ON COLUMN AUTO_LOCK_NO.AUTOLOCK_RELEASE_NO IS 'オートロック解除No 既存システム物理名: EMK10X';
COMMENT ON COLUMN AUTO_LOCK_NO.RENT_FIXED_FLAG IS '賃料固定フラグ 既存システム物理名: EMKF01';
COMMENT ON COLUMN AUTO_LOCK_NO.HOME_SECURITY IS 'ホームセキュリティ 既存システム物理名: EMKF02';
COMMENT ON COLUMN AUTO_LOCK_NO.RESERVE_03 IS '予備03 既存システム物理名: EMKF03';
COMMENT ON COLUMN AUTO_LOCK_NO.RESERVE_04 IS '予備04 既存システム物理名: EMKF04';
COMMENT ON COLUMN AUTO_LOCK_NO.RESERVE_05 IS '予備05 既存システム物理名: EMKF05';
COMMENT ON COLUMN AUTO_LOCK_NO.RESERVE_06 IS '予備06 既存システム物理名: EMKF06';
COMMENT ON COLUMN AUTO_LOCK_NO.RESERVE_07 IS '予備07 既存システム物理名: EMKF07';
COMMENT ON COLUMN AUTO_LOCK_NO.RESERVE_08 IS '予備08 既存システム物理名: EMKF08';
COMMENT ON COLUMN AUTO_LOCK_NO.RESERVE_09 IS '予備09 既存システム物理名: EMKF09';
COMMENT ON COLUMN AUTO_LOCK_NO.RESERVE_10 IS '予備10 既存システム物理名: EMKF10';
