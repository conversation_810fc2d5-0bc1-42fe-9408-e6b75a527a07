-- TABLE: PROPERTY_MAINTENANCE_INFO(物件メンテナンス情報)

CREATE TABLE PROPERTY_MAINTENANCE_INFO(
     BUILDING_CD                                  varchar(9)        NOT NULL    
,    ROOM_CD                                      varchar(5)        NOT NULL    
,    RENTAL_PRICE                                 numeric(9,0)                  
,    SECURITY_DEPOSIT                             numeric(9,0)                  
,    KEY_MONEY                                    numeric(9,0)                  
,    LISTING_CATEGORY                             numeric(1,0)                  
,    LISTING_CATEGORY_GOOD_ROOM_NET               numeric(1,0)                  
,    LOW_REPAIR_COST_SPECIFICATION                numeric(1,0)                  
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(10)                   
,    AD_AMOUNT                                    numeric(9,0)                  
,    COMMENT                                      varchar(100)                  
,    HOMES_PANORAMA_SEND_FLAG                     numeric(1,0)                  
,    AD_UNIT                                      numeric(1,0)                  
,    FF_AMOUNT                                    numeric(100,1)                
,    CONSTRAINT PK_PROPERTY_MAINTENANCE_INFO PRIMARY KEY (BUILDING_CD, ROOM_CD)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PROPERTY_MAINTENANCE_INFO IS '物件メンテナンス情報 既存システム物理名: EMEBMP';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.BUILDING_CD IS '建物CD 既存システム物理名: EMETCD 0…非掲載、1…掲載';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.ROOM_CD IS '部屋CD 既存システム物理名: EMEHCD';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.RENTAL_PRICE IS '募集家賃 既存システム物理名: EMEBYN';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.SECURITY_DEPOSIT IS '募集敷金 既存システム物理名: EMEBSN';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.KEY_MONEY IS '募集礼金 既存システム物理名: EMEBRN';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.LISTING_CATEGORY IS '掲載区分(いい物件・仲介) 既存システム物理名: EMEKSK';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.LISTING_CATEGORY_GOOD_ROOM_NET IS '掲載区分いい部屋ネット 既存システム物理名: EMEKS2';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.LOW_REPAIR_COST_SPECIFICATION IS '原状回復費用負担少仕様 既存システム物理名: EMEGSF';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.CREATION_DATE IS '作成年月日 既存システム物理名: EME01D';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.CREATION_TIME IS '作成時刻 既存システム物理名: EME02H';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.UPDATE_DATE IS '更新年月日 既存システム物理名: EME03D';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.UPDATE_TIME IS '更新時刻 既存システム物理名: EME04H';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.UPDATER IS '更新者 既存システム物理名: EME05C';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.AD_AMOUNT IS 'AD金額 既存システム物理名: -';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.COMMENT IS 'コメント 既存システム物理名: EMEFFN';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.HOMES_PANORAMA_SEND_FLAG IS 'HOME''Sパノラマ送信フラグ 既存システム物理名: EMEPN1';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.AD_UNIT IS 'AD単位 既存システム物理名: -';
COMMENT ON COLUMN PROPERTY_MAINTENANCE_INFO.FF_AMOUNT IS 'FF金額 既存システム物理名: -';
