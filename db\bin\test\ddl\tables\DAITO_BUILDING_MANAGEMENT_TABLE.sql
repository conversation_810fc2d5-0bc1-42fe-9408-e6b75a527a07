-- TABLE: DAITO_BUILDING_MANAGEMENT_TABLE(大東建物管理対応表)

CREATE TABLE DAITO_BUILDING_MANAGEMENT_TABLE(
     CREATION_DATE                                numeric(8,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    CONSTRUCTION_TERMINAL_INSTALLATION           varchar(3)                    
,    DAIKEN_TERMINAL_INSTALLATION                 varchar(3)                    
,    USAGE_START_DATE                             numeric(8,0)                  
,    USAGE_END_DATE                               numeric(8,0)                  
,    INSTALLATION_CATEGORY                        varchar(1)                    
,    SATELLITE_CATEGORY                           varchar(1)                    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE DAITO_BUILDING_MANAGEMENT_TABLE IS '大東建物管理対応表 既存システム物理名: FXX20P';
COMMENT ON COLUMN DAITO_BUILDING_MANAGEMENT_TABLE.CREATION_DATE IS '作成日 既存システム物理名: FXX21D 0:大建営業所1:サテライト';
COMMENT ON COLUMN DAITO_BUILDING_MANAGEMENT_TABLE.UPDATE_DATE IS '更新日 既存システム物理名: FXX22D';
COMMENT ON COLUMN DAITO_BUILDING_MANAGEMENT_TABLE.CONSTRUCTION_TERMINAL_INSTALLATION IS '建託端末設置 既存システム物理名: FXX23C';
COMMENT ON COLUMN DAITO_BUILDING_MANAGEMENT_TABLE.DAIKEN_TERMINAL_INSTALLATION IS '大建端末設置 既存システム物理名: FXX24C';
COMMENT ON COLUMN DAITO_BUILDING_MANAGEMENT_TABLE.USAGE_START_DATE IS '使用開始日 既存システム物理名: FXX25D';
COMMENT ON COLUMN DAITO_BUILDING_MANAGEMENT_TABLE.USAGE_END_DATE IS '使用終了日 既存システム物理名: FXX26D';
COMMENT ON COLUMN DAITO_BUILDING_MANAGEMENT_TABLE.INSTALLATION_CATEGORY IS '設置区分 既存システム物理名: FXX27S';
COMMENT ON COLUMN DAITO_BUILDING_MANAGEMENT_TABLE.SATELLITE_CATEGORY IS 'サテライト区分 既存システム物理名: FXX28B';
