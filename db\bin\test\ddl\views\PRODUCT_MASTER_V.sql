-- VIEW: PRODUCT_MASTER_V(商品マスタビュー)

CREATE VIEW PRODUCT_MASTER_V AS
SELECT
	PRODUCT_NAME_CODE,
	PRODUCT_CODE_BRANCH,
	GRADE_NAME
FROM
	PRODUCT_MASTER PM
WHERE
	EXISTS (
	SELECT
		1
	FROM
		(
		SELECT
			PRODUCT_NAME_CODE,
			PRODUCT_CODE_BRANCH,
			MAX(EFFECTIVE_START_DATE) AS EFFECTIVE_START_DATE
		FROM
			PRODUCT_MASTER
		WHERE
		    EFFECTIVE_START_DATE <= TO_CHAR(CURRENT_TIMESTAMP, 'yyyymmdd')::numeric
		GROUP BY
			PRODUCT_NAME_CODE,
			PRODUCT_CODE_<PERSON>ANCH) PMM
	WHERE
		PM.PRODUCT_NAME_CODE = PMM.PRODUCT_NAME_CODE
		AND PM.PRODUCT_CODE_BRANCH = PMM.PRODUCT_CODE_<PERSON>ANCH
		AND PM.EFFECTIVE_START_DATE = PMM.EFFECTIVE_START_DATE
	)
	AND PM.DELETE_FLAG IS NULL;
