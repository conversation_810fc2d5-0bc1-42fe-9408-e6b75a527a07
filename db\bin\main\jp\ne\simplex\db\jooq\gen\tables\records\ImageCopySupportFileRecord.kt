/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ImageCopySupportFileTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ImageCopySupportFilePojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 画像コピー対応ファイル 既存システム物理名: ERCPGP
 */
@Suppress("UNCHECKED_CAST")
open class ImageCopySupportFileRecord private constructor() : UpdatableRecordImpl<ImageCopySupportFileRecord>(ImageCopySupportFileTable.IMAGE_COPY_SUPPORT_FILE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var propertyBuildingCode: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var propertyRoomCode: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var exteriorImageCommentCode: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var layoutImageCommentCode: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var imageCommentCode_1: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var imageCommentCode_2: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var imageCommentCode_3: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var imageCommentCode_4: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var imageCommentCode_5: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var imageCommentCode_6: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var exteriorImageComment: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var layoutImageComment: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var imageComment_1: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var imageComment_2: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var imageComment_3: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var imageComment_4: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var imageComment_5: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var imageComment_6: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var copyExecutionFlag: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var confirmationCategory: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var imageCommentCode_7: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var imageCommentCode_8: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var imageCommentCode_9: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var imageCommentCode_10: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var imageCommentCode_11: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var imageCommentCode_12: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var surroundingImageCommentCode_1: String?
        set(value): Unit = set(32, value)
        get(): String? = get(32) as String?

    open var surroundingImageCommentCode_2: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var surroundingImageCommentCode_3: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var surroundingImageCommentCode_4: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var surroundingImageCommentCode_5: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var surroundingImageCommentCode_6: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var imageComment_7: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var imageComment_8: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var imageComment_9: String?
        set(value): Unit = set(40, value)
        get(): String? = get(40) as String?

    open var imageComment_10: String?
        set(value): Unit = set(41, value)
        get(): String? = get(41) as String?

    open var imageComment_11: String?
        set(value): Unit = set(42, value)
        get(): String? = get(42) as String?

    open var imageComment_12: String?
        set(value): Unit = set(43, value)
        get(): String? = get(43) as String?

    open var surroundingImageComment_1: String?
        set(value): Unit = set(44, value)
        get(): String? = get(44) as String?

    open var surroundingImageComment_2: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var surroundingImageComment_3: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var surroundingImageComment_4: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var surroundingImageComment_5: String?
        set(value): Unit = set(48, value)
        get(): String? = get(48) as String?

    open var surroundingImageComment_6: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised ImageCopySupportFileRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, propertyBuildingCode: String, propertyRoomCode: String, exteriorImageCommentCode: String? = null, layoutImageCommentCode: String? = null, imageCommentCode_1: String? = null, imageCommentCode_2: String? = null, imageCommentCode_3: String? = null, imageCommentCode_4: String? = null, imageCommentCode_5: String? = null, imageCommentCode_6: String? = null, exteriorImageComment: String? = null, layoutImageComment: String? = null, imageComment_1: String? = null, imageComment_2: String? = null, imageComment_3: String? = null, imageComment_4: String? = null, imageComment_5: String? = null, imageComment_6: String? = null, copyExecutionFlag: String? = null, confirmationCategory: String? = null, imageCommentCode_7: String? = null, imageCommentCode_8: String? = null, imageCommentCode_9: String? = null, imageCommentCode_10: String? = null, imageCommentCode_11: String? = null, imageCommentCode_12: String? = null, surroundingImageCommentCode_1: String? = null, surroundingImageCommentCode_2: String? = null, surroundingImageCommentCode_3: String? = null, surroundingImageCommentCode_4: String? = null, surroundingImageCommentCode_5: String? = null, surroundingImageCommentCode_6: String? = null, imageComment_7: String? = null, imageComment_8: String? = null, imageComment_9: String? = null, imageComment_10: String? = null, imageComment_11: String? = null, imageComment_12: String? = null, surroundingImageComment_1: String? = null, surroundingImageComment_2: String? = null, surroundingImageComment_3: String? = null, surroundingImageComment_4: String? = null, surroundingImageComment_5: String? = null, surroundingImageComment_6: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.propertyBuildingCode = propertyBuildingCode
        this.propertyRoomCode = propertyRoomCode
        this.exteriorImageCommentCode = exteriorImageCommentCode
        this.layoutImageCommentCode = layoutImageCommentCode
        this.imageCommentCode_1 = imageCommentCode_1
        this.imageCommentCode_2 = imageCommentCode_2
        this.imageCommentCode_3 = imageCommentCode_3
        this.imageCommentCode_4 = imageCommentCode_4
        this.imageCommentCode_5 = imageCommentCode_5
        this.imageCommentCode_6 = imageCommentCode_6
        this.exteriorImageComment = exteriorImageComment
        this.layoutImageComment = layoutImageComment
        this.imageComment_1 = imageComment_1
        this.imageComment_2 = imageComment_2
        this.imageComment_3 = imageComment_3
        this.imageComment_4 = imageComment_4
        this.imageComment_5 = imageComment_5
        this.imageComment_6 = imageComment_6
        this.copyExecutionFlag = copyExecutionFlag
        this.confirmationCategory = confirmationCategory
        this.imageCommentCode_7 = imageCommentCode_7
        this.imageCommentCode_8 = imageCommentCode_8
        this.imageCommentCode_9 = imageCommentCode_9
        this.imageCommentCode_10 = imageCommentCode_10
        this.imageCommentCode_11 = imageCommentCode_11
        this.imageCommentCode_12 = imageCommentCode_12
        this.surroundingImageCommentCode_1 = surroundingImageCommentCode_1
        this.surroundingImageCommentCode_2 = surroundingImageCommentCode_2
        this.surroundingImageCommentCode_3 = surroundingImageCommentCode_3
        this.surroundingImageCommentCode_4 = surroundingImageCommentCode_4
        this.surroundingImageCommentCode_5 = surroundingImageCommentCode_5
        this.surroundingImageCommentCode_6 = surroundingImageCommentCode_6
        this.imageComment_7 = imageComment_7
        this.imageComment_8 = imageComment_8
        this.imageComment_9 = imageComment_9
        this.imageComment_10 = imageComment_10
        this.imageComment_11 = imageComment_11
        this.imageComment_12 = imageComment_12
        this.surroundingImageComment_1 = surroundingImageComment_1
        this.surroundingImageComment_2 = surroundingImageComment_2
        this.surroundingImageComment_3 = surroundingImageComment_3
        this.surroundingImageComment_4 = surroundingImageComment_4
        this.surroundingImageComment_5 = surroundingImageComment_5
        this.surroundingImageComment_6 = surroundingImageComment_6
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ImageCopySupportFileRecord
     */
    constructor(value: ImageCopySupportFilePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.propertyBuildingCode = value.propertyBuildingCode
            this.propertyRoomCode = value.propertyRoomCode
            this.exteriorImageCommentCode = value.exteriorImageCommentCode
            this.layoutImageCommentCode = value.layoutImageCommentCode
            this.imageCommentCode_1 = value.imageCommentCode_1
            this.imageCommentCode_2 = value.imageCommentCode_2
            this.imageCommentCode_3 = value.imageCommentCode_3
            this.imageCommentCode_4 = value.imageCommentCode_4
            this.imageCommentCode_5 = value.imageCommentCode_5
            this.imageCommentCode_6 = value.imageCommentCode_6
            this.exteriorImageComment = value.exteriorImageComment
            this.layoutImageComment = value.layoutImageComment
            this.imageComment_1 = value.imageComment_1
            this.imageComment_2 = value.imageComment_2
            this.imageComment_3 = value.imageComment_3
            this.imageComment_4 = value.imageComment_4
            this.imageComment_5 = value.imageComment_5
            this.imageComment_6 = value.imageComment_6
            this.copyExecutionFlag = value.copyExecutionFlag
            this.confirmationCategory = value.confirmationCategory
            this.imageCommentCode_7 = value.imageCommentCode_7
            this.imageCommentCode_8 = value.imageCommentCode_8
            this.imageCommentCode_9 = value.imageCommentCode_9
            this.imageCommentCode_10 = value.imageCommentCode_10
            this.imageCommentCode_11 = value.imageCommentCode_11
            this.imageCommentCode_12 = value.imageCommentCode_12
            this.surroundingImageCommentCode_1 = value.surroundingImageCommentCode_1
            this.surroundingImageCommentCode_2 = value.surroundingImageCommentCode_2
            this.surroundingImageCommentCode_3 = value.surroundingImageCommentCode_3
            this.surroundingImageCommentCode_4 = value.surroundingImageCommentCode_4
            this.surroundingImageCommentCode_5 = value.surroundingImageCommentCode_5
            this.surroundingImageCommentCode_6 = value.surroundingImageCommentCode_6
            this.imageComment_7 = value.imageComment_7
            this.imageComment_8 = value.imageComment_8
            this.imageComment_9 = value.imageComment_9
            this.imageComment_10 = value.imageComment_10
            this.imageComment_11 = value.imageComment_11
            this.imageComment_12 = value.imageComment_12
            this.surroundingImageComment_1 = value.surroundingImageComment_1
            this.surroundingImageComment_2 = value.surroundingImageComment_2
            this.surroundingImageComment_3 = value.surroundingImageComment_3
            this.surroundingImageComment_4 = value.surroundingImageComment_4
            this.surroundingImageComment_5 = value.surroundingImageComment_5
            this.surroundingImageComment_6 = value.surroundingImageComment_6
            resetChangedOnNotNull()
        }
    }
}
