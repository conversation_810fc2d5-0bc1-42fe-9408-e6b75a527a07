/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.RoomImageTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.RoomImagePojo

import org.jooq.Record2
import org.jooq.impl.UpdatableRecordImpl


/**
 * 部屋画像 既存システム物理名: ERAHMP
 */
@Suppress("UNCHECKED_CAST")
open class RoomImageRecord private constructor() : UpdatableRecordImpl<RoomImageRecord>(RoomImageTable.ROOM_IMAGE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateUser: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var propertyBuildingCd: String
        set(value): Unit = set(5, value)
        get(): String = get(5) as String

    open var propertyRoomCd: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var imageRegistrationCount: Byte?
        set(value): Unit = set(7, value)
        get(): Byte? = get(7) as Byte?

    open var latestImageRegistrationDate: Int?
        set(value): Unit = set(8, value)
        get(): Int? = get(8) as Int?

    open var floorPlanImageFileName: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var floorPlanImageRegistrationDate: Int?
        set(value): Unit = set(10, value)
        get(): Int? = get(10) as Int?

    open var roomImageFileName_1: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var roomImageType_1: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var imageRegistrationDate_1: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var roomImageFileName_2: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var roomImageType_2: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var imageRegistrationDate_2: Int?
        set(value): Unit = set(16, value)
        get(): Int? = get(16) as Int?

    open var roomImageFileName_3: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var roomImageType_3: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var imageRegistrationDate_3: Int?
        set(value): Unit = set(19, value)
        get(): Int? = get(19) as Int?

    open var roomImageFileName_4: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var roomImageType_4: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var imageRegistrationDate_4: Int?
        set(value): Unit = set(22, value)
        get(): Int? = get(22) as Int?

    open var roomImageFileName_5: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var roomImageType_5: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var imageRegistrationDate_5: Int?
        set(value): Unit = set(25, value)
        get(): Int? = get(25) as Int?

    open var roomImageFileName_6: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var roomImageType_6: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var imageRegistrationDate_6: Int?
        set(value): Unit = set(28, value)
        get(): Int? = get(28) as Int?

    open var roomImageFileName_7: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var roomImageType_7: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var imageRegistrationDate_7: Int?
        set(value): Unit = set(31, value)
        get(): Int? = get(31) as Int?

    open var roomImageFileName_8: String?
        set(value): Unit = set(32, value)
        get(): String? = get(32) as String?

    open var roomImageType_8: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var imageRegistrationDate_8: Int?
        set(value): Unit = set(34, value)
        get(): Int? = get(34) as Int?

    open var roomImageFileName_9: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var roomImageType_9: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var imageRegistrationDate_9: Int?
        set(value): Unit = set(37, value)
        get(): Int? = get(37) as Int?

    open var searchBranchCd: Int?
        set(value): Unit = set(38, value)
        get(): Int? = get(38) as Int?

    open var roomImageFileName_10: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var roomImageType_10: String?
        set(value): Unit = set(40, value)
        get(): String? = get(40) as String?

    open var imageRegistrationDate_10: Int?
        set(value): Unit = set(41, value)
        get(): Int? = get(41) as Int?

    open var roomImageFileName_11: String?
        set(value): Unit = set(42, value)
        get(): String? = get(42) as String?

    open var roomImageType_11: String?
        set(value): Unit = set(43, value)
        get(): String? = get(43) as String?

    open var imageRegistrationDate_11: Int?
        set(value): Unit = set(44, value)
        get(): Int? = get(44) as Int?

    open var roomImageFileName_12: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var roomImageType_12: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var imageRegistrationDate_12: Int?
        set(value): Unit = set(47, value)
        get(): Int? = get(47) as Int?

    open var floorPlanImageRegistrationTime: Int?
        set(value): Unit = set(48, value)
        get(): Int? = get(48) as Int?

    open var imageRegistrationTime_1: Int?
        set(value): Unit = set(49, value)
        get(): Int? = get(49) as Int?

    open var imageRegistrationTime_2: Int?
        set(value): Unit = set(50, value)
        get(): Int? = get(50) as Int?

    open var imageRegistrationTime_3: Int?
        set(value): Unit = set(51, value)
        get(): Int? = get(51) as Int?

    open var imageRegistrationTime_4: Int?
        set(value): Unit = set(52, value)
        get(): Int? = get(52) as Int?

    open var imageRegistrationTime_5: Int?
        set(value): Unit = set(53, value)
        get(): Int? = get(53) as Int?

    open var imageRegistrationTime_6: Int?
        set(value): Unit = set(54, value)
        get(): Int? = get(54) as Int?

    open var imageRegistrationTime_7: Int?
        set(value): Unit = set(55, value)
        get(): Int? = get(55) as Int?

    open var imageRegistrationTime_8: Int?
        set(value): Unit = set(56, value)
        get(): Int? = get(56) as Int?

    open var imageRegistrationTime_9: Int?
        set(value): Unit = set(57, value)
        get(): Int? = get(57) as Int?

    open var imageRegistrationTime_10: Int?
        set(value): Unit = set(58, value)
        get(): Int? = get(58) as Int?

    open var imageRegistrationTime_11: Int?
        set(value): Unit = set(59, value)
        get(): Int? = get(59) as Int?

    open var imageRegistrationTime_12: Int?
        set(value): Unit = set(60, value)
        get(): Int? = get(60) as Int?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record2<String?, String?> = super.key() as Record2<String?, String?>

    /**
     * Create a detached, initialised RoomImageRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateUser: String? = null, propertyBuildingCd: String, propertyRoomCd: String, imageRegistrationCount: Byte? = null, latestImageRegistrationDate: Int? = null, floorPlanImageFileName: String? = null, floorPlanImageRegistrationDate: Int? = null, roomImageFileName_1: String? = null, roomImageType_1: String? = null, imageRegistrationDate_1: Int? = null, roomImageFileName_2: String? = null, roomImageType_2: String? = null, imageRegistrationDate_2: Int? = null, roomImageFileName_3: String? = null, roomImageType_3: String? = null, imageRegistrationDate_3: Int? = null, roomImageFileName_4: String? = null, roomImageType_4: String? = null, imageRegistrationDate_4: Int? = null, roomImageFileName_5: String? = null, roomImageType_5: String? = null, imageRegistrationDate_5: Int? = null, roomImageFileName_6: String? = null, roomImageType_6: String? = null, imageRegistrationDate_6: Int? = null, roomImageFileName_7: String? = null, roomImageType_7: String? = null, imageRegistrationDate_7: Int? = null, roomImageFileName_8: String? = null, roomImageType_8: String? = null, imageRegistrationDate_8: Int? = null, roomImageFileName_9: String? = null, roomImageType_9: String? = null, imageRegistrationDate_9: Int? = null, searchBranchCd: Int? = null, roomImageFileName_10: String? = null, roomImageType_10: String? = null, imageRegistrationDate_10: Int? = null, roomImageFileName_11: String? = null, roomImageType_11: String? = null, imageRegistrationDate_11: Int? = null, roomImageFileName_12: String? = null, roomImageType_12: String? = null, imageRegistrationDate_12: Int? = null, floorPlanImageRegistrationTime: Int? = null, imageRegistrationTime_1: Int? = null, imageRegistrationTime_2: Int? = null, imageRegistrationTime_3: Int? = null, imageRegistrationTime_4: Int? = null, imageRegistrationTime_5: Int? = null, imageRegistrationTime_6: Int? = null, imageRegistrationTime_7: Int? = null, imageRegistrationTime_8: Int? = null, imageRegistrationTime_9: Int? = null, imageRegistrationTime_10: Int? = null, imageRegistrationTime_11: Int? = null, imageRegistrationTime_12: Int? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateUser = updateUser
        this.propertyBuildingCd = propertyBuildingCd
        this.propertyRoomCd = propertyRoomCd
        this.imageRegistrationCount = imageRegistrationCount
        this.latestImageRegistrationDate = latestImageRegistrationDate
        this.floorPlanImageFileName = floorPlanImageFileName
        this.floorPlanImageRegistrationDate = floorPlanImageRegistrationDate
        this.roomImageFileName_1 = roomImageFileName_1
        this.roomImageType_1 = roomImageType_1
        this.imageRegistrationDate_1 = imageRegistrationDate_1
        this.roomImageFileName_2 = roomImageFileName_2
        this.roomImageType_2 = roomImageType_2
        this.imageRegistrationDate_2 = imageRegistrationDate_2
        this.roomImageFileName_3 = roomImageFileName_3
        this.roomImageType_3 = roomImageType_3
        this.imageRegistrationDate_3 = imageRegistrationDate_3
        this.roomImageFileName_4 = roomImageFileName_4
        this.roomImageType_4 = roomImageType_4
        this.imageRegistrationDate_4 = imageRegistrationDate_4
        this.roomImageFileName_5 = roomImageFileName_5
        this.roomImageType_5 = roomImageType_5
        this.imageRegistrationDate_5 = imageRegistrationDate_5
        this.roomImageFileName_6 = roomImageFileName_6
        this.roomImageType_6 = roomImageType_6
        this.imageRegistrationDate_6 = imageRegistrationDate_6
        this.roomImageFileName_7 = roomImageFileName_7
        this.roomImageType_7 = roomImageType_7
        this.imageRegistrationDate_7 = imageRegistrationDate_7
        this.roomImageFileName_8 = roomImageFileName_8
        this.roomImageType_8 = roomImageType_8
        this.imageRegistrationDate_8 = imageRegistrationDate_8
        this.roomImageFileName_9 = roomImageFileName_9
        this.roomImageType_9 = roomImageType_9
        this.imageRegistrationDate_9 = imageRegistrationDate_9
        this.searchBranchCd = searchBranchCd
        this.roomImageFileName_10 = roomImageFileName_10
        this.roomImageType_10 = roomImageType_10
        this.imageRegistrationDate_10 = imageRegistrationDate_10
        this.roomImageFileName_11 = roomImageFileName_11
        this.roomImageType_11 = roomImageType_11
        this.imageRegistrationDate_11 = imageRegistrationDate_11
        this.roomImageFileName_12 = roomImageFileName_12
        this.roomImageType_12 = roomImageType_12
        this.imageRegistrationDate_12 = imageRegistrationDate_12
        this.floorPlanImageRegistrationTime = floorPlanImageRegistrationTime
        this.imageRegistrationTime_1 = imageRegistrationTime_1
        this.imageRegistrationTime_2 = imageRegistrationTime_2
        this.imageRegistrationTime_3 = imageRegistrationTime_3
        this.imageRegistrationTime_4 = imageRegistrationTime_4
        this.imageRegistrationTime_5 = imageRegistrationTime_5
        this.imageRegistrationTime_6 = imageRegistrationTime_6
        this.imageRegistrationTime_7 = imageRegistrationTime_7
        this.imageRegistrationTime_8 = imageRegistrationTime_8
        this.imageRegistrationTime_9 = imageRegistrationTime_9
        this.imageRegistrationTime_10 = imageRegistrationTime_10
        this.imageRegistrationTime_11 = imageRegistrationTime_11
        this.imageRegistrationTime_12 = imageRegistrationTime_12
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised RoomImageRecord
     */
    constructor(value: RoomImagePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateUser = value.updateUser
            this.propertyBuildingCd = value.propertyBuildingCd
            this.propertyRoomCd = value.propertyRoomCd
            this.imageRegistrationCount = value.imageRegistrationCount
            this.latestImageRegistrationDate = value.latestImageRegistrationDate
            this.floorPlanImageFileName = value.floorPlanImageFileName
            this.floorPlanImageRegistrationDate = value.floorPlanImageRegistrationDate
            this.roomImageFileName_1 = value.roomImageFileName_1
            this.roomImageType_1 = value.roomImageType_1
            this.imageRegistrationDate_1 = value.imageRegistrationDate_1
            this.roomImageFileName_2 = value.roomImageFileName_2
            this.roomImageType_2 = value.roomImageType_2
            this.imageRegistrationDate_2 = value.imageRegistrationDate_2
            this.roomImageFileName_3 = value.roomImageFileName_3
            this.roomImageType_3 = value.roomImageType_3
            this.imageRegistrationDate_3 = value.imageRegistrationDate_3
            this.roomImageFileName_4 = value.roomImageFileName_4
            this.roomImageType_4 = value.roomImageType_4
            this.imageRegistrationDate_4 = value.imageRegistrationDate_4
            this.roomImageFileName_5 = value.roomImageFileName_5
            this.roomImageType_5 = value.roomImageType_5
            this.imageRegistrationDate_5 = value.imageRegistrationDate_5
            this.roomImageFileName_6 = value.roomImageFileName_6
            this.roomImageType_6 = value.roomImageType_6
            this.imageRegistrationDate_6 = value.imageRegistrationDate_6
            this.roomImageFileName_7 = value.roomImageFileName_7
            this.roomImageType_7 = value.roomImageType_7
            this.imageRegistrationDate_7 = value.imageRegistrationDate_7
            this.roomImageFileName_8 = value.roomImageFileName_8
            this.roomImageType_8 = value.roomImageType_8
            this.imageRegistrationDate_8 = value.imageRegistrationDate_8
            this.roomImageFileName_9 = value.roomImageFileName_9
            this.roomImageType_9 = value.roomImageType_9
            this.imageRegistrationDate_9 = value.imageRegistrationDate_9
            this.searchBranchCd = value.searchBranchCd
            this.roomImageFileName_10 = value.roomImageFileName_10
            this.roomImageType_10 = value.roomImageType_10
            this.imageRegistrationDate_10 = value.imageRegistrationDate_10
            this.roomImageFileName_11 = value.roomImageFileName_11
            this.roomImageType_11 = value.roomImageType_11
            this.imageRegistrationDate_11 = value.imageRegistrationDate_11
            this.roomImageFileName_12 = value.roomImageFileName_12
            this.roomImageType_12 = value.roomImageType_12
            this.imageRegistrationDate_12 = value.imageRegistrationDate_12
            this.floorPlanImageRegistrationTime = value.floorPlanImageRegistrationTime
            this.imageRegistrationTime_1 = value.imageRegistrationTime_1
            this.imageRegistrationTime_2 = value.imageRegistrationTime_2
            this.imageRegistrationTime_3 = value.imageRegistrationTime_3
            this.imageRegistrationTime_4 = value.imageRegistrationTime_4
            this.imageRegistrationTime_5 = value.imageRegistrationTime_5
            this.imageRegistrationTime_6 = value.imageRegistrationTime_6
            this.imageRegistrationTime_7 = value.imageRegistrationTime_7
            this.imageRegistrationTime_8 = value.imageRegistrationTime_8
            this.imageRegistrationTime_9 = value.imageRegistrationTime_9
            this.imageRegistrationTime_10 = value.imageRegistrationTime_10
            this.imageRegistrationTime_11 = value.imageRegistrationTime_11
            this.imageRegistrationTime_12 = value.imageRegistrationTime_12
            resetChangedOnNotNull()
        }
    }
}
