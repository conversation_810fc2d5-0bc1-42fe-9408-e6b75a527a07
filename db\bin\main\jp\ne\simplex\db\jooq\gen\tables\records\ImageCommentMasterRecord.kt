/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ImageCommentMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ImageCommentMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 画像コメントマスタ 既存システム物理名: ERCOMP
 */
@Suppress("UNCHECKED_CAST")
open class ImageCommentMasterRecord private constructor() : TableRecordImpl<ImageCommentMasterRecord>(ImageCommentMasterTable.IMAGE_COMMENT_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteSign: Int?
        set(value): Unit = set(6, value)
        get(): Int? = get(6) as Int?

    open var imageCommentCode: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var imageComment: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    /**
     * Create a detached, initialised ImageCommentMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, logicalDeleteSign: Int? = null, imageCommentCode: String? = null, imageComment: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.logicalDeleteSign = logicalDeleteSign
        this.imageCommentCode = imageCommentCode
        this.imageComment = imageComment
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ImageCommentMasterRecord
     */
    constructor(value: ImageCommentMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.logicalDeleteSign = value.logicalDeleteSign
            this.imageCommentCode = value.imageCommentCode
            this.imageComment = value.imageComment
            resetChangedOnNotNull()
        }
    }
}
