package jp.ne.simplex.application.repository.mail

import com.icegreen.greenmail.util.GreenMail
import com.icegreen.greenmail.util.ServerSetup
import jakarta.mail.internet.MimeMessage
import jp.ne.simplex.application.model.EmailAddress
import jp.ne.simplex.application.model.MailProperty
import jp.ne.simplex.application.repository.smtp.SmtpClient
import jp.ne.simplex.application.repository.smtp.SmtpConfig
import org.junit.jupiter.api.*
import kotlin.test.assertEquals

class SmtpMailRepositoryTest {
    companion object {
        private const val HOST = "localhost"
        private const val PORT = 1026
        private const val APP_URL = "http://example.com"
    }

    private val repository = SmtpMailRepository(APP_URL, SmtpClient(SmtpConfig(HOST, PORT)))

    private lateinit var greenMail: GreenMail

    @BeforeEach
    fun setUp() {
        // SMTPサーバを立ち上げる
        val smtpSetup = ServerSetup(PORT, HOST, "smtp")
        greenMail = GreenMail(smtpSetup)
        greenMail.start()
    }

    @AfterEach
    fun tearDown() {
        // サーバを停止する
        greenMail.stop()
    }

    @Nested
    @DisplayName("メール送信の検証")
    inner class Scenario1 {

        @Test
        @DisplayName("受信したメールの数・件名・送信元・送信先・本文が正しいこと")
        fun case1() {

            val body =
                """
                    建物ＣＤ：123456789　名称 ：　テスト建物
                    駐車場ＣＤ：101　現地表示：1
                    リーシング店舗：987
                    賃マ営業所　　：654

                    にて駐車場予約の登録がされました。

                    備考：テスト備考
                """.trimIndent()

            // メール本文パラメータ
            val paramMap: HashMap<String, String> = hashMapOf(
                "TateCD" to "123456789",
                "ParkingCD" to "101",
                "LocalDisplayNumber" to "1",
                "Remarks" to "テスト備考",
                "TateNM" to "テスト建物",
                "LeaseShitenName" to "987",
                "ShinsaShitenName" to "654"
            )

            val prop = MailProperty(
                mailTemplateType = MailProperty.MailTemplateType.PARKING_YOYAKU_INFO,
                fromAddress = EmailAddress.of("<EMAIL>"),
                fromName = "テストサーバー",
                subject = "メール通知テスト",
                url = "",
                toList = listOf(EmailAddress.of("<EMAIL>")),
                ccList = listOf(EmailAddress.of("<EMAIL>")),
                messageParam = paramMap
            )

            // メールを送信
            repository.sendMailUP(prop)

            // 受信したメールを取得
            val receivedMessages: Array<MimeMessage> = greenMail.receivedMessages

            // 受信したメールの数が2通であること
            assertEquals(2, receivedMessages.size)
            // 件名が期待通りであること
            assertEquals(prop.subject, receivedMessages[0].subject)
            // 送信元が期待通りであること
            assertEquals(prop.getFrom().toString(), receivedMessages[0].from.first().toString())
            // 送信先が期待通りであること
            assertEquals(
                prop.toList[0].value,
                receivedMessages[0].getRecipients(MimeMessage.RecipientType.TO)[0].toString()
            )
            // 本文が期待通りであること
            assertEquals(body, receivedMessages[0].content.toString().trim())
        }
    }
}
