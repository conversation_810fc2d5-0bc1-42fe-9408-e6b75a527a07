/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.BuildingImageTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingImagePojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 建物画像 既存システム物理名: ERATGP
 */
@Suppress("UNCHECKED_CAST")
open class BuildingImageRecord private constructor() : UpdatableRecordImpl<BuildingImageRecord>(BuildingImageTable.BUILDING_IMAGE) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateUser: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var propertyBuildingCd: String
        set(value): Unit = set(5, value)
        get(): String = get(5) as String

    open var imageRegistrationCount: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var imageRegistrationDate: Int?
        set(value): Unit = set(7, value)
        get(): Int? = get(7) as Int?

    open var imageFileName_1: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var imageRegistrationDate_1: Int?
        set(value): Unit = set(9, value)
        get(): Int? = get(9) as Int?

    open var imageFileName_2: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var imageRegistrationDate_2: Int?
        set(value): Unit = set(11, value)
        get(): Int? = get(11) as Int?

    open var imageFileName_3: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var imageRegistrationDate_3: Int?
        set(value): Unit = set(13, value)
        get(): Int? = get(13) as Int?

    open var surroundingImageFileName_1: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var surroundingImageRegistrationDate_1: Int?
        set(value): Unit = set(15, value)
        get(): Int? = get(15) as Int?

    open var imageType_1: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var facilityName_1: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var distance_1: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var surroundingImageFileName_2: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var surroundingImageRegistrationDate_2: Int?
        set(value): Unit = set(20, value)
        get(): Int? = get(20) as Int?

    open var imageType_2: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var facilityName_2: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var distance_2: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var surroundingImageFileName_3: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var surroundingImageRegistrationDate_3: Int?
        set(value): Unit = set(25, value)
        get(): Int? = get(25) as Int?

    open var imageType_3: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var facilityName_3: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var distance_3: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var surroundingImageFileName_4: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var surroundingImageRegistrationDate_4: Int?
        set(value): Unit = set(30, value)
        get(): Int? = get(30) as Int?

    open var imageType_4: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var facilityName_4: String?
        set(value): Unit = set(32, value)
        get(): String? = get(32) as String?

    open var distance_4: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var surroundingImageFileName_5: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var surroundingImageRegistrationDate_5: Int?
        set(value): Unit = set(35, value)
        get(): Int? = get(35) as Int?

    open var imageType_5: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var facilityName_5: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var distance_5: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var surroundingImageFileName_6: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var surroundingImageRegistrationDate_6: Int?
        set(value): Unit = set(40, value)
        get(): Int? = get(40) as Int?

    open var imageType_6: String?
        set(value): Unit = set(41, value)
        get(): String? = get(41) as String?

    open var facilityName_6: String?
        set(value): Unit = set(42, value)
        get(): String? = get(42) as String?

    open var distance_6: String?
        set(value): Unit = set(43, value)
        get(): String? = get(43) as String?

    open var imageRegistrationTime_1: Int?
        set(value): Unit = set(44, value)
        get(): Int? = get(44) as Int?

    open var imageRegistrationTime_2: Int?
        set(value): Unit = set(45, value)
        get(): Int? = get(45) as Int?

    open var imageRegistrationTime_3: Int?
        set(value): Unit = set(46, value)
        get(): Int? = get(46) as Int?

    open var surroundingImageRegistrationTime_1: Int?
        set(value): Unit = set(47, value)
        get(): Int? = get(47) as Int?

    open var surroundingImageRegistrationTime_2: Int?
        set(value): Unit = set(48, value)
        get(): Int? = get(48) as Int?

    open var surroundingImageRegistrationTime_3: Int?
        set(value): Unit = set(49, value)
        get(): Int? = get(49) as Int?

    open var surroundingImageRegistrationTime_4: Int?
        set(value): Unit = set(50, value)
        get(): Int? = get(50) as Int?

    open var surroundingImageRegistrationTime_5: Int?
        set(value): Unit = set(51, value)
        get(): Int? = get(51) as Int?

    open var surroundingImageRegistrationTime_6: Int?
        set(value): Unit = set(52, value)
        get(): Int? = get(52) as Int?

    open var nextUpdateDate_2: Int?
        set(value): Unit = set(53, value)
        get(): Int? = get(53) as Int?

    open var nextUpdateDate_3: Int?
        set(value): Unit = set(54, value)
        get(): Int? = get(54) as Int?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised BuildingImageRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateUser: String? = null, propertyBuildingCd: String, imageRegistrationCount: Byte? = null, imageRegistrationDate: Int? = null, imageFileName_1: String? = null, imageRegistrationDate_1: Int? = null, imageFileName_2: String? = null, imageRegistrationDate_2: Int? = null, imageFileName_3: String? = null, imageRegistrationDate_3: Int? = null, surroundingImageFileName_1: String? = null, surroundingImageRegistrationDate_1: Int? = null, imageType_1: String? = null, facilityName_1: String? = null, distance_1: String? = null, surroundingImageFileName_2: String? = null, surroundingImageRegistrationDate_2: Int? = null, imageType_2: String? = null, facilityName_2: String? = null, distance_2: String? = null, surroundingImageFileName_3: String? = null, surroundingImageRegistrationDate_3: Int? = null, imageType_3: String? = null, facilityName_3: String? = null, distance_3: String? = null, surroundingImageFileName_4: String? = null, surroundingImageRegistrationDate_4: Int? = null, imageType_4: String? = null, facilityName_4: String? = null, distance_4: String? = null, surroundingImageFileName_5: String? = null, surroundingImageRegistrationDate_5: Int? = null, imageType_5: String? = null, facilityName_5: String? = null, distance_5: String? = null, surroundingImageFileName_6: String? = null, surroundingImageRegistrationDate_6: Int? = null, imageType_6: String? = null, facilityName_6: String? = null, distance_6: String? = null, imageRegistrationTime_1: Int? = null, imageRegistrationTime_2: Int? = null, imageRegistrationTime_3: Int? = null, surroundingImageRegistrationTime_1: Int? = null, surroundingImageRegistrationTime_2: Int? = null, surroundingImageRegistrationTime_3: Int? = null, surroundingImageRegistrationTime_4: Int? = null, surroundingImageRegistrationTime_5: Int? = null, surroundingImageRegistrationTime_6: Int? = null, nextUpdateDate_2: Int? = null, nextUpdateDate_3: Int? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateUser = updateUser
        this.propertyBuildingCd = propertyBuildingCd
        this.imageRegistrationCount = imageRegistrationCount
        this.imageRegistrationDate = imageRegistrationDate
        this.imageFileName_1 = imageFileName_1
        this.imageRegistrationDate_1 = imageRegistrationDate_1
        this.imageFileName_2 = imageFileName_2
        this.imageRegistrationDate_2 = imageRegistrationDate_2
        this.imageFileName_3 = imageFileName_3
        this.imageRegistrationDate_3 = imageRegistrationDate_3
        this.surroundingImageFileName_1 = surroundingImageFileName_1
        this.surroundingImageRegistrationDate_1 = surroundingImageRegistrationDate_1
        this.imageType_1 = imageType_1
        this.facilityName_1 = facilityName_1
        this.distance_1 = distance_1
        this.surroundingImageFileName_2 = surroundingImageFileName_2
        this.surroundingImageRegistrationDate_2 = surroundingImageRegistrationDate_2
        this.imageType_2 = imageType_2
        this.facilityName_2 = facilityName_2
        this.distance_2 = distance_2
        this.surroundingImageFileName_3 = surroundingImageFileName_3
        this.surroundingImageRegistrationDate_3 = surroundingImageRegistrationDate_3
        this.imageType_3 = imageType_3
        this.facilityName_3 = facilityName_3
        this.distance_3 = distance_3
        this.surroundingImageFileName_4 = surroundingImageFileName_4
        this.surroundingImageRegistrationDate_4 = surroundingImageRegistrationDate_4
        this.imageType_4 = imageType_4
        this.facilityName_4 = facilityName_4
        this.distance_4 = distance_4
        this.surroundingImageFileName_5 = surroundingImageFileName_5
        this.surroundingImageRegistrationDate_5 = surroundingImageRegistrationDate_5
        this.imageType_5 = imageType_5
        this.facilityName_5 = facilityName_5
        this.distance_5 = distance_5
        this.surroundingImageFileName_6 = surroundingImageFileName_6
        this.surroundingImageRegistrationDate_6 = surroundingImageRegistrationDate_6
        this.imageType_6 = imageType_6
        this.facilityName_6 = facilityName_6
        this.distance_6 = distance_6
        this.imageRegistrationTime_1 = imageRegistrationTime_1
        this.imageRegistrationTime_2 = imageRegistrationTime_2
        this.imageRegistrationTime_3 = imageRegistrationTime_3
        this.surroundingImageRegistrationTime_1 = surroundingImageRegistrationTime_1
        this.surroundingImageRegistrationTime_2 = surroundingImageRegistrationTime_2
        this.surroundingImageRegistrationTime_3 = surroundingImageRegistrationTime_3
        this.surroundingImageRegistrationTime_4 = surroundingImageRegistrationTime_4
        this.surroundingImageRegistrationTime_5 = surroundingImageRegistrationTime_5
        this.surroundingImageRegistrationTime_6 = surroundingImageRegistrationTime_6
        this.nextUpdateDate_2 = nextUpdateDate_2
        this.nextUpdateDate_3 = nextUpdateDate_3
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised BuildingImageRecord
     */
    constructor(value: BuildingImagePojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateUser = value.updateUser
            this.propertyBuildingCd = value.propertyBuildingCd
            this.imageRegistrationCount = value.imageRegistrationCount
            this.imageRegistrationDate = value.imageRegistrationDate
            this.imageFileName_1 = value.imageFileName_1
            this.imageRegistrationDate_1 = value.imageRegistrationDate_1
            this.imageFileName_2 = value.imageFileName_2
            this.imageRegistrationDate_2 = value.imageRegistrationDate_2
            this.imageFileName_3 = value.imageFileName_3
            this.imageRegistrationDate_3 = value.imageRegistrationDate_3
            this.surroundingImageFileName_1 = value.surroundingImageFileName_1
            this.surroundingImageRegistrationDate_1 = value.surroundingImageRegistrationDate_1
            this.imageType_1 = value.imageType_1
            this.facilityName_1 = value.facilityName_1
            this.distance_1 = value.distance_1
            this.surroundingImageFileName_2 = value.surroundingImageFileName_2
            this.surroundingImageRegistrationDate_2 = value.surroundingImageRegistrationDate_2
            this.imageType_2 = value.imageType_2
            this.facilityName_2 = value.facilityName_2
            this.distance_2 = value.distance_2
            this.surroundingImageFileName_3 = value.surroundingImageFileName_3
            this.surroundingImageRegistrationDate_3 = value.surroundingImageRegistrationDate_3
            this.imageType_3 = value.imageType_3
            this.facilityName_3 = value.facilityName_3
            this.distance_3 = value.distance_3
            this.surroundingImageFileName_4 = value.surroundingImageFileName_4
            this.surroundingImageRegistrationDate_4 = value.surroundingImageRegistrationDate_4
            this.imageType_4 = value.imageType_4
            this.facilityName_4 = value.facilityName_4
            this.distance_4 = value.distance_4
            this.surroundingImageFileName_5 = value.surroundingImageFileName_5
            this.surroundingImageRegistrationDate_5 = value.surroundingImageRegistrationDate_5
            this.imageType_5 = value.imageType_5
            this.facilityName_5 = value.facilityName_5
            this.distance_5 = value.distance_5
            this.surroundingImageFileName_6 = value.surroundingImageFileName_6
            this.surroundingImageRegistrationDate_6 = value.surroundingImageRegistrationDate_6
            this.imageType_6 = value.imageType_6
            this.facilityName_6 = value.facilityName_6
            this.distance_6 = value.distance_6
            this.imageRegistrationTime_1 = value.imageRegistrationTime_1
            this.imageRegistrationTime_2 = value.imageRegistrationTime_2
            this.imageRegistrationTime_3 = value.imageRegistrationTime_3
            this.surroundingImageRegistrationTime_1 = value.surroundingImageRegistrationTime_1
            this.surroundingImageRegistrationTime_2 = value.surroundingImageRegistrationTime_2
            this.surroundingImageRegistrationTime_3 = value.surroundingImageRegistrationTime_3
            this.surroundingImageRegistrationTime_4 = value.surroundingImageRegistrationTime_4
            this.surroundingImageRegistrationTime_5 = value.surroundingImageRegistrationTime_5
            this.surroundingImageRegistrationTime_6 = value.surroundingImageRegistrationTime_6
            this.nextUpdateDate_2 = value.nextUpdateDate_2
            this.nextUpdateDate_3 = value.nextUpdateDate_3
            resetChangedOnNotNull()
        }
    }
}
