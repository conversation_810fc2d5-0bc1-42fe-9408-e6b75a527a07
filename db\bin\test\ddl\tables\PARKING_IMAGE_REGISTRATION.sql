-- TABLE: PARKING_IMAGE_REGISTRATION(駐車場画像登録)

CREATE TABLE PARKING_IMAGE_REGISTRATION(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    CONSTRAINT PK_PARKING_IMAGE_REGISTRATION PRIMARY KEY (BUILDING_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PARKING_IMAGE_REGISTRATION IS '駐車場画像登録 既存システム物理名: EMEGOP';
COMMENT ON COLUMN PARKING_IMAGE_REGISTRATION.CREATION_DATE IS '作成年月日 既存システム物理名: EMEG1D';
COMMENT ON COLUMN PARKING_IMAGE_REGISTRATION.CREATION_TIME IS '作成時刻 既存システム物理名: EMEG2T';
COMMENT ON COLUMN PARKING_IMAGE_REGISTRATION.CREATOR IS '作成者 既存システム物理名: EMEG3C';
COMMENT ON COLUMN PARKING_IMAGE_REGISTRATION.UPDATE_DATE IS '更新年月日 既存システム物理名: EMEG4D';
COMMENT ON COLUMN PARKING_IMAGE_REGISTRATION.UPDATE_TIME IS '更新時刻 既存システム物理名: EMEG5T';
COMMENT ON COLUMN PARKING_IMAGE_REGISTRATION.UPDATER IS '更新者 既存システム物理名: EMEG6C';
COMMENT ON COLUMN PARKING_IMAGE_REGISTRATION.BUILDING_CODE IS '建物コード 既存システム物理名: EMEG7C';
