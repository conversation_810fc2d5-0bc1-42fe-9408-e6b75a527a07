/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.ProductMasterVTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.ProductMasterVPojo

import org.jooq.impl.TableRecordImpl


/**
 * This class is generated by jOOQ.
 */
@Suppress("UNCHECKED_CAST")
open class ProductMasterVRecord private constructor() : TableRecordImpl<ProductMasterVRecord>(ProductMasterVTable.PRODUCT_MASTER_V) {

    open var productNameCode: Short?
        set(value): Unit = set(0, value)
        get(): Short? = get(0) as Short?

    open var productCodeBranch: Byte?
        set(value): Unit = set(1, value)
        get(): Byte? = get(1) as Byte?

    open var gradeName: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    /**
     * Create a detached, initialised ProductMasterVRecord
     */
    constructor(productNameCode: Short? = null, productCodeBranch: Byte? = null, gradeName: String? = null): this() {
        this.productNameCode = productNameCode
        this.productCodeBranch = productCodeBranch
        this.gradeName = gradeName
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised ProductMasterVRecord
     */
    constructor(value: ProductMasterVPojo?): this() {
        if (value != null) {
            this.productNameCode = value.productNameCode
            this.productCodeBranch = value.productCodeBranch
            this.gradeName = value.gradeName
            resetChangedOnNotNull()
        }
    }
}
