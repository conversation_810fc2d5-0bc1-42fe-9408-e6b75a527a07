package jp.ne.simplex.shared

import com.fasterxml.jackson.databind.ObjectMapper
import com.ibm.icu.text.Transliterator
import jakarta.mail.internet.InternetAddress
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class StringExtension {

    companion object {

        private val objectMapper: ObjectMapper = ObjectMapper()
        private val halfWidthToFullWidthTransliterator =
            Transliterator.getInstance("Halfwidth-Fullwidth")

        private val kataOfMap = objectMapper.typeFactory.constructMapLikeType(
            Map::class.java,
            String::class.java,
            Any::class.java
        )

        fun String.toMap(): Map<String, Any?> {
            return objectMapper.readValue(this, kataOfMap)
        }

        fun String.yyyyMMdd(): LocalDate {
            return LocalDate.parse(this, DateTimeFormatter.ofPattern("yyyyMMdd"))
        }

        fun String.yyyyMMddHHmmss(): LocalDateTime {
            return LocalDateTime.parse(this, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
        }

        fun String?.toBoolean(): Boolean? {
            return when (this) {
                "0" -> false
                "1" -> true
                else -> null
            }
        }

        fun String.isValidEmail(): Boolean {
            return try {
                InternetAddress(this).apply { validate() }
                true
            } catch (_: Exception) {
                false
            }
        }

        /** 文字列が全角文字のみで構成されているかを判定する */
        fun String.isOnlyFullWidth(): Boolean {
            return this.all { it.toString().toByteArray(Charsets.UTF_8).size > 1 }
        }

        /** 半角文字列を全角文字列に変換する */
        fun String.toFullWidth(): String {
            return halfWidthToFullWidthTransliterator.transliterate(this)
        }

        /** 全角変換処理を行った文字列が全角文字のみで構成されているかを判定する */
        fun String.isAllCharsFullWidthConvertible(): Boolean {
            return this.toFullWidth().isOnlyFullWidth()
        }
    }
}
