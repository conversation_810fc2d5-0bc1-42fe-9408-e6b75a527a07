package jp.ne.simplex.application.repository.external.dkportal.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.model.ParkingContractPossibility
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibility
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibilityAutoJudge
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalOperationName
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalRequest
import jp.ne.simplex.exception.ErrorType

class DKPortalUpdateParkingLotRequest private constructor(
    @field:JsonProperty("order_code")
    val orderCode: String,

    @field:JsonProperty("available_parking_flag")
    val availableParkingFlag: Int,

    @field:JsonProperty("second_parking_contract_possible_flag")
    val secondParkingContractPossibleFlag: Int,
) : DKPortalRequest {
    
    override fun getDKPortalOperationName(): DKPortalOperationName {
        return DKPortalOperationName.UPDATE_PARKING_LOT
    }

    companion object {

        fun equals(
            oldStatus: ParkingContractPossibility,
            newStatus: ParkingContractPossibility
        ): Boolean {
            val oldRequest = of(oldStatus)
            val newRequest = of(newStatus)
            return (oldRequest.orderCode == newRequest.orderCode)
                    && (oldRequest.availableParkingFlag == newRequest.availableParkingFlag)
                    && (oldRequest.secondParkingContractPossibleFlag == newRequest.secondParkingContractPossibleFlag)
        }

        fun of(status: ParkingContractPossibility): DKPortalUpdateParkingLotRequest {
            if (status.isAutoJudge == ContractPossibilityAutoJudge.MANUAL) {
                return DKPortalUpdateParkingLotRequest(
                    orderCode = status.orderCode.value,
                    availableParkingFlag = 1,
                    secondParkingContractPossibleFlag = 0
                )
            } else {
                when (status.firstParkingContractPossibility) {
                    ContractPossibility.POSSIBLE ->
                        return when (status.secondParkingContractPossibility) {
                            ContractPossibility.POSSIBLE -> DKPortalUpdateParkingLotRequest(
                                orderCode = status.orderCode.value,
                                availableParkingFlag = 1,
                                secondParkingContractPossibleFlag = 1
                            )

                            ContractPossibility.IMPOSSIBLE,
                            ContractPossibility.REQUIRED_CONFIRM -> DKPortalUpdateParkingLotRequest(
                                orderCode = status.orderCode.value,
                                availableParkingFlag = 1,
                                secondParkingContractPossibleFlag = 0
                            )
                        }

                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibility.REQUIRED_CONFIRM -> return DKPortalUpdateParkingLotRequest(
                        orderCode = status.orderCode.value,
                        availableParkingFlag = 0,
                        secondParkingContractPossibleFlag = 0
                    )
                }
            }
        }
    }
}
