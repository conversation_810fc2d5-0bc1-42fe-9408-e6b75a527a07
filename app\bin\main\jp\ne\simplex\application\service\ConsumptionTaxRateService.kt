package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.ConsumptionTaxRate
import jp.ne.simplex.application.repository.db.ConsumptionTaxRateRepositoryInterface
import org.springframework.stereotype.Service

@Service
class ConsumptionTaxRateService(private val consumptionTaxRateRepository: ConsumptionTaxRateRepositoryInterface) {
    fun getConsumptionTaxRate(): ConsumptionTaxRate {
        return consumptionTaxRateRepository.findActive()
            ?: throw IllegalStateException("消費税率が設定されていません。")
    }

}
