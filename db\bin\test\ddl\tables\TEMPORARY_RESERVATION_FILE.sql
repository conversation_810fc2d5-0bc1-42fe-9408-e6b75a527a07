-- TABLE: TEMPORARY_RESERVATION_FILE(仮押さえファイル)

CREATE TABLE TEMPORARY_RESERVATION_FILE(
     BUILDING_CD                                  varchar(9)                    
,    ROOM_CD                                      varchar(5)                    
,    STATUS                                       varchar(2)                    
,    APPLICATION_SCHEDULED_DATE                   varchar(8)                    
,    STATE                                        varchar(2)                    
,    APPLICATION_SCHEDULED_PERSON_CD              varchar(6)                    
,    CUSTOMER_REP_CD                              varchar(6)                    
,    CUSTOMER_REP_BRANCH_CD                       varchar(3)                    
,    CUSTOMER_REP_SHOZOKU_CD                      varchar(3)                    
,    COMMENT                                      varchar(257)                  
,    CONTRACT_FORM_E_CODE                         varchar(16)                   
,    LIST_COMMENT                                 varchar(62)                   
,    REGISTRATION_DATE                            varchar(8)                    
,    LINK_CD_REGISTRATION_TIME                    varchar(6)                    
,    OTHER_COMPANY_FLAG                           varchar(1)                    
,    OTHER_COMPANY_MEMBER_ID                      varchar(8)                    
,    OTHER_COMPANY_NAME                           varchar(102)                  
,    <PERSON><PERSON>ER_COMPANY_STORE_NAME                     varchar(102)                  
,    OTHER_COMPANY_REP_NAME                       varchar(102)                  
) TABLESPACE :TS_TBL;

COMMENT ON TABLE TEMPORARY_RESERVATION_FILE IS '仮押さえファイル 既存システム物理名: ERA03P';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.BUILDING_CD IS '建物CD 既存システム物理名: E03P01 WEB:0 AS400:1？＞Webは1を設定。その後0に書き換え？';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.ROOM_CD IS '部屋CD 既存システム物理名: E03P02 仮押さえONはこの値あり。';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.STATUS IS 'ステータス 既存システム物理名: E03P03 未使用';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_DATE IS '申込予定日 既存システム物理名: E03P04 自社：社員番号';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.STATE IS '状態 既存システム物理名: E03P05 未使用';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_PERSON_CD IS '申込予定担当者CD 既存システム物理名: E03P06 自社：社員所属支店';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.CUSTOMER_REP_CD IS '客付け担当者CD 既存システム物理名: E03P07 未使用';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.CUSTOMER_REP_BRANCH_CD IS '客付け担当支店CD 既存システム物理名: E03P08';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.CUSTOMER_REP_SHOZOKU_CD IS '客付け担当所属CD 既存システム物理名: E03P09';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.COMMENT IS 'コメント 既存システム物理名: E03P10';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.CONTRACT_FORM_E_CODE IS '契約形態→Eコード 既存システム物理名: E03P11';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.LIST_COMMENT IS '一覧用コメント 既存システム物理名: E03P13 ユニークID';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.REGISTRATION_DATE IS '登録日 既存システム物理名: E03P14';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.LINK_CD_REGISTRATION_TIME IS 'ひも付けCD→登録時刻 既存システム物理名: E03P15';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_FLAG IS '他社フラグ 既存システム物理名: E03P16 1:他社 ブランク:自社';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_MEMBER_ID IS '他社会員ID 既存システム物理名: E03P17';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_NAME IS '他社会社名 既存システム物理名: E03P18';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_STORE_NAME IS '他社店舗名 既存システム物理名: E03P19';
COMMENT ON COLUMN TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_REP_NAME IS '他社担当者名 既存システム物理名: E03P20';
