truncate table SITE_PROPERTY_KENTAKU_FOR_P;
insert into SITE_PROPERTY_KENTAKU_FOR_P (PROPERTY_FULL_ID, BUILDING_ID, RENEW_DATE, DISTANCE_FROM_STATION_1, WALK_FROM_STATION_1, BUS_FROM_STATION_1, BUS_STOP_NAME_1, FROM_BUS_STOP_1, DISTANCE_FROM_BUSSTOP_1, NEAREST_ROUTE_1, NEAREST_STATION_1, KINDAIKA_CODE_TEXT_1, WAY_TO_CODE_1, DISTANCE_FROM_STATION_2, WALK_FROM_STATION_2, BUS_FROM_STATION_2, BUS_STOP_NAME_2, FROM_BUS_STOP_2, DISTANCE_FROM_BUSSTOP_2, NEAREST_ROUTE_2, NEAREST_STATION_2, KINDAIKA_CODE_TEXT_2, WAY_TO_CODE_2, DISTANCE_FROM_STATION_3, WALK_FROM_STATION_3, BUS_FROM_STATION_3, BUS_STOP_NAME_3, FROM_BUS_STOP_3, DISTANCE_FROM_BUSSTOP_3, NEAREST_ROUTE_3, NEAREST_STATION_3, KINDAIKA_CODE_TEXT_3, WAY_TO_CODE_3, ZIP_CODE_TEXT, PREFECTURE, CITY, TOWN, TYOUME, KOKUDO_CODE_TEXT, JIS_CODE_VALUE, TOWN_CODE_VALUE, TYOUME_CODE_VALUE, RESTADDR1, LATITUDE, LONGITUDE, BUILDING_NAME, DISP_NAME_CODE, BUILDING_FURIGANA, KIND_CODE, KIND_DISP_NAME, SALE_BLOCK_NUM, EMPTY_HOUSES_NUM, SELLING_COMPANY, COMPLETION_DATE, AREA_WAYS1_CODE, STRUCTURE_CODE, STRUCTURE_DISP_NAME, BUILDING_TYPE_CODE, ALL_FLOOR_NUM, UNDER_FLOOR_NUM, NEW_USED_CODE, MANAGER_STYLE_CODE, MANAGER_COMMENT, QUIET_CODE, GAS_CODE, WATER_SUPPLY_CODE, WASTE_WATER_CODE, ELEC_POWER_CODE, TWO_BY_FOUR_CODE, SELL_TYPE_CODE, AVOID_QUAKE_CODE, BARRIER_FREE_CODE, FULLTIME_MANAGEMENT_CODE, LIFT_CODE, LIFT_NUM_CODE, WALL_TYPE_CODE, DELIVERY_MAILBOX_CODE, LAUNDERETTE_CODE, ROOM_NUMBER_TEXT, DISP_ROOM_NUMBER_CODE, SALES_POINT, REMARK1, REMARK2, SPECIAL_REMARK, NOTE, PRICE, PRICE_TAX_CODE, CONSUMPTION_TAX, QUERY_PERSON, FIRM_SIDE_CODE, INTO_CODE, INTO_DATE, LEAVE_DATE, OTHER_COMPANY_CODE, MESSAGE_TO_OTHER_COMPANY, REGIST_DATE, REGIST_TIME, RENT_EXCHANGE_STYLE_CODE, HOUSE_PLAN_CODE, ROOM_NUM, HOUSE_PLAN_EQUIV, WINDOW_DIRECTION_CODE, FLOOR_NUM, NON_MOVEINTO_CODE, MANAGED_PROPERTY_CODE, PET_CODE, OFFICE_CODE, MUSICAL_CODE, HOUSE_PLAN_DISP_NAME, USE_PART_AREA, KEY_MONEY, KEY_MONEY_UNIT_CODE, KEY_MONEY_TAX_CODE, DEPOSIT, DEPOSIT_UNIT_CODE, REPAIR_COST, REPAIR_COST_UNIT_CODE, GUARANTY, GUARANTY_UNIT_CODE, SYOKYAKU_CLASS_CODE, SYOKYAKU, SYOKYAKU_UNIT_CODE, PREMIUM, PREMIUM_UNIT_CODE, PREMIUM_TAX_CODE, MANAGE_COST, MANAGE_COST_TAX_CODE, SERVICE_FEE, SERVICE_FEE_FREE_CODE, SERVICE_FEE_TAX_CODE, ZAPPI, ZAPPI_TAX_CODE, OTHER_COST_COMMENT, OTHER_COST_1, OTHER_COST_ITEM_1, OTHER_COST_TAX_CODE_1, OTHER_COST_2, OTHER_COST_ITEM_2, OTHER_COST_TAX_CODE_2, OTHER_COST_3, OTHER_COST_ITEM_3, OTHER_COST_TAX_CODE_3, OTHER_COST_4, OTHER_COST_ITEM_4, OTHER_COST_TAX_CODE_4, OTHER_COST_5, OTHER_COST_ITEM_5, OTHER_COST_TAX_CODE_5, OUTER_FACILITY_CODE_1, OUTER_FACILITY_CODE_2, OUTER_AREA_2, RENEWAL_FEE, RENEWAL_FEE_UNIT_CODE, RENEWAL_FEE_CLASS_CODE, HOUSE_RENT_LIMIT_DATE, INSURANCE_CODE, SPECIAL_RENTAL_LOWER_COST, SPECIAL_RENTAL_UPPER_COST, ADDITIONAL_DEPOSIT_UNIT_CODE, ADDITIONAL_DEPOSIT_REASON_CODE, BROKERAGE, BROKERAGE_UNIT_CODE, RENEWAL_CHARGE, RENEWAL_CHARGE_UNIT_CODE, STUDENT_ONLY_CODE, SEX_CONDITION_CODE, KIDS_CODE, ALONE_CODE, TWO_PEOPLE_CODE, ELDER_CODE, CORPORATION_ONLY_CODE, RESIDENCE_HOUSE_RENT_CODE, ROOM_STYLE_CODE_1, ROOM_AREA_1, ROOM_UNIT_CODE_1, ROOM_STYLE_CODE_2, ROOM_AREA_2, ROOM_UNIT_CODE_2, ROOM_STYLE_CODE_3, ROOM_AREA_3, ROOM_UNIT_CODE_3, ROOM_STYLE_CODE_4, ROOM_AREA_4, ROOM_UNIT_CODE_4, ROOM_STYLE_CODE_5, ROOM_AREA_5, ROOM_UNIT_CODE_5, ROOM_STYLE_CODE_6, ROOM_AREA_6, ROOM_UNIT_CODE_6, ROOM_STYLE_CODE_7, ROOM_AREA_7, ROOM_UNIT_CODE_7, ROOM_STYLE_CODE_8, ROOM_AREA_8, ROOM_UNIT_CODE_8, ROOM_STYLE_CODE_9, ROOM_AREA_9, ROOM_UNIT_CODE_9, ROOM_STYLE_CODE_10, ROOM_AREA_10, ROOM_UNIT_CODE_10, PARKING_CODE, FROM_NEAR_PARKING, PARKING_NUM, PARKING_TYPE_CODE, PARKING_SHUTTER_CODE, PARKING_LOWER_COST, PARKING_TAX_CODE, PARKABLE_NUM_CODE, PARKING_FREE_CODE, BIKE_PARK_CODE, BIKE_PARK_COST, MOTORBIKE_PARK_CODE, MOTORBIKE_COST, AIRCON_CODE, COOLER_CODE, HEATING_CODE, LOAD_HEATER_CODE, STOVE_CODE, FLOOR_HEATING_CODE, CATV_CODE, COMMUNITY_BROADCAST_CODE, BS_CODE, CS_CODE, INTERNET_CODE, CLOSET_CODE, WALKIN_WARDROBE_CODE, CLOSET_UNDER_FLOOR_CODE, TRUNK_ROOM_CODE, OSHIIRE_CODE, GARRET_CLOSET_CODE, SHOE_CUPBOARD_CODE, STOREROOM_CODE, BATH_TOILET_CODE, BATH_CODE, SHOWER_CODE, AUTO_BATH_CODE, DRESSING_ROOM_CODE, REBOIL_BATH_CODE, TOILET_CODE, BATH_DRIER_CODE, SHAMPOO_DRESSER_CODE, WASHLET_CODE, BATH_OVER_1TSUBO_CODE, WARMLET_CODE, COOKING_STOVE_CODE, KITCHEN_CODE, MICROWAVE_OVEN_CODE, IH_COOKING_HEATER_CODE, COLD_STORAGE_CODE, GRILL_CODE, DISPOSER_CODE, DISH_WASHER_CODE, WATER_CLEANER_CODE, WOODEN_FLOOR_CODE, LOFT_CODE, CUSHION_FLOOR_CODE, HIGHEST_FLOOR_CODE, MAISONETTE_CODE, OVER_SECOND_FLOOR_CODE, CAVE_CODE, SOUNDPROOF_CODE, CORNER_HOUSE_CODE, SUNROOM_CODE, BASEMENT_CODE, SOUTH_ROOM_CODE, PATIO_CODE, CRIME_PREV_SHUTTER_CODE, CRIME_PREV_CAMERA_CODE, AUTOLOCK_CODE, DOUBLE_LOCK_CODE, WASHING_MACHINE_CODE, DRIER_CODE, WASHING_MACHINE_PLACE_CODE, CARD_KEY_CODE, BOW_WINDOW_CODE, LIGHT_CODE, ALL_ELECTRIC_CODE, HOT_WATER_SUPPLY_CODE, INTERPHONE_CODE, FULLTIME_FUN_CODE, ECOCUTE_CODE, DOUBLE_SIDE_BALCONY_CODE, BALCONY_SIDE_NUM_CODE, BATH_TV_CODE, PORCH_CODE, UP_START_DATE, UP_END_DATE, DRESSING_TABLE_CODE, PRIVATE_DUST_BOX_CODE, PIANO_CODE, LARGE_SHOES_BOX_CODE, CLOSET_UNDER_TATAMI_CODE, INDOORS_BICYCLE_PARKING_CODE, SECURITY_KEY_CODE, SHUTTER_CODE, FOR_SOUTH_CODE, CLOSET_UNDERSTAIR_CODE, NEARBY_CONVENIENCE_STORE_CODE, NEARBY_BANK_CODE, NEARBY_RENTAL_VIDEO_CODE, LARGE_SCALE_RENEWAL_CODE, RECOVERY_COST_CODE, GUARANTOR_CODE, GUARANTOR_PROXY_CODE, GUARANTOR_PROXY_COM_CODE, GUARANTOR_PROXY_COMMENT, DISP_MAP_CODE, LATITUDE_WORLD, LONGITUDE_WORLD, GARDEN_CODE, BALCONY_CODE, PANORAMA_ID, LARGE_SCALE_RENEWAL_DATE, SHATAKU_KANOU_CODE, NO_DEPOSIT_PLAN_CODE, KENTAKU_KIND_CODE, PRICE_SALE_FLAG, REFOME_FLAG, PRODUCT_CODE, OWNER_SHIP_BRANCH_CODE, KENTAKU_BUILDING_CODE, KENTAKU_ROOM_CODE, KEY_EXCHANGE_FREE_CODE, AD_PRICE, FF_PRICE, LEAVE_DATE_TP, LEAVE_FINISH_DATE, LOW_PARKING_PRICE, HIGH_PARKING_PRICE, STRUCTURE_DISP_NAME_TP, DISPLACE_CODE, FINANCE_CORPORATION_CODE, WATER_COMPANY_NAME, WATER_COMPANY_TEL, ELECTRIC_COMPANY_NAME, ELECTRIC_COMPANY_TEL, GAS_COMPANY_NAME, GAS_COMPANY_TEL, COLLECT_DATE, KOUENTIN_CODE, INTO_DATE_TXT, ROOM_SITUATION_CODE, RECORD_SITUATION_CODE, ELECTRIC_DISCOUNT_FLAG, FLETS_HIKARI_CODE, AKIYA_TERM, CLEANING_FEE_CODE, CLEANING_FEE, POWER_CODE, FIRE_ZONE_CODE, DISCOUNT_RATE, DISCOUNT_TERM, PET_FLAG, INTERNET_FREE_CODE, ALL_ROOM_CLOSET, WALK_THROUGH_CLOSET, FREE_WASH_ROOM, AUTO_BATH, INDOOR_CLOTHES_DRYING, MOTION_SENSOR_LIGHTING, OPEN_KITCHEN, ISLAND_KITCHEN, GAS_COOKER_ATTACHED, THREE_OVER_GAS, DOUBLE_GLAZING, SECURITY_GLAZING, VIBRATION_CONTROL_FLOOR, SNOW_VANISHING_FACILITY, KEROSENE_HEATER, BATH_WINDOW, JAPANESE_STYLE_ROOM, EARTHQUAKE_RESIST_CONST, ALLINONE_SERVICE_WATER, ALLINONE_SERVICE_ELECTRICITY, ALLINONE_SERVICE_GAS, PRICE_AND_COST, VAL_CODE_1, VAL_CODE_2, VAL_CODE_3, PANORAMA_TYPE, SERVICE_FEE_DETAILS, SHINSA_BRANCH_CODE, CONTRACT_CONFIRM_CODE, PREFECTURE_EN, SHIKUGUNCHOUSON_EN, OOAZA_TSUUSYOU_EN, AZA_CHOUME_EN, RESTADDR_ALPHABET, UP_STATE, AD_PRICE_UNIT_CODE, DELETE_DATE, REALTIME_UP_TIME, REALTIME_UP_TYPE, PRODUCT_TYPE_CD, MONEY_UPDATE_TIME, KODAWARI100_199, FLOOR_MAX_ROOM, RENEWAL_FEE_FLG, FULLTIME_SUPPORT_FLG, MEMBERSHIP_FEE_EXEMPTION_KBN, MEMBERSHIP_FEE_EXEMPTION_DAYS, EBOARD_COMMENT, MANAGEMENT_PARKING_KBN, NET_SERVICE_JCOM, NET_SERVICE_STARCAT, ZEH_ORIENTED, ZEH_DK_SOLEIL, ZEH_DK_ALPHA, KEY_SET_COST_FLAG, ELECTRIC_INTRODUCTION, ELECTRIC_TYPE, EMERGENCY_E_COMPANY_NAME, EMERGENCY_E_COMPANY_TEL, GAS_INTRODUCTION, EMERGENCY_GAS_COMPANY_NAME, EMERGENCY_GAS_COMPANY_TEL, WATER_INTRODUCTION, WATER_METER_TYPE, INTERNET_TYPE, INTERNET_NAME, INTERNET_TEL, INTERNET_INTRODUCTION, WATER_SERVER, LIFELINE_GUIDANCE_TYPE, ROOM_SAVE_ENERGY_CERT_DATE, ROOM_THIRD_PARTY_EVAL_FLG, ROOM_SAVE_ENERGY_LEVEL, ROOM_ENERGY_COST, ROOM_ENERGY_COST_SUN, ROOM_RENEW_ENERGY_FLG, ROOM_INSULATION_LEVEL, ROOM_EASY_UTILITY_COSTS, ROOM_ZEH_LEVEL_FLG, ROOM_NET_ZERO_ENERGY_FLG, BUILDING_SAVE_ENERGY_CERT_DATE, BUILDING_THIRD_PARTY_EVAL_FLG, BUILDING_SAVE_ENERGY_LEVEL, BUILDING_ENERGY_COST, BUILDING_ENERGY_COST_SUN, BUILDING_RENEW_ENERGY_FLG, BUILDING_INSULATION_LEVEL, BUILDING_EASY_UTILITY_COSTS, BUILDING_ZEH_LEVEL_FLG, BUILDING_NET_ZERO_ENERGY_FLG, SHINSA_BUSINESS_OFFICE_CODE, CHALLENGE_START, CHALLENGE_END, CHALLENGE_DISCOUNT_PRICE) values
 (123456789012345678901234567, 123456, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)
;
