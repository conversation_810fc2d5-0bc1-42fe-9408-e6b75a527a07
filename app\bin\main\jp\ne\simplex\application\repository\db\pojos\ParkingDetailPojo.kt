package jp.ne.simplex.application.repository.db.pojos

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.ParkingLot.AssessmentDivision.ASSESSMENT
import jp.ne.simplex.application.model.ParkingLot.AssessmentDivision.OUTSIDE_ASSESSMENT
import jp.ne.simplex.application.model.ParkingLot.BrokerApplicationPossibility.IMPOSSIBLE
import jp.ne.simplex.application.model.ParkingLot.BrokerApplicationPossibility.POSSIBLE
import jp.ne.simplex.application.model.ParkingLot.OffSiteCategory.INSIDE
import jp.ne.simplex.application.model.ParkingLot.OffSiteCategory.OUTSIDE
import jp.ne.simplex.application.model.ParkingLot.StatusDivision.*
import jp.ne.simplex.application.model.VacancyParkingLotTarget.BulkLeaseFlag
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.shared.StringExtension.Companion.toBoolean
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import java.time.LocalDate

// 駐車場詳細主データ保持用
data class ParkingDetailPojo(
    val buildingCode: String,
    val parkingLotCode: String? = null,
    val buildingName: String? = null,
    val buildingBulkLeaseFlag: Int? = null,
    val addressDetail: String? = null,
    val completionDeliveryDate: Int? = null,
    val buildingThirtyFiveYearLumpSumCategory: String? = null,
    override val tenantContractNumber: String? = null,
    override val roomCode: String? = null,
    val parkingLotNumber: String? = null,
    override val landTransportName: String? = null,
    override val type: String? = null,
    override val businessCategory: String? = null,
    override val leftNumber: String? = null,
    override val rightNumber: String? = null,
    override val manufacturerDivision: String? = null,
    override val carModelName: String? = null,
    override val lightVehicleSign: String? = null,
    override val tenantName: String? = null, // 未使用
    override val moveInScheduledDate: Int? = null,
    override val tenantNameKanji: String? = null, // 駐車場契約者名
    override val contractExpiryDate: Int? = null,// 未使用
    override val contractEffectiveEndDate: Int? = null,// 未使用
    override val currentStateDivision: String? = null,
    override val modificationStateDivision: String? = null,
    override val moveInStartProcessedSign: Int? = null,
    override val moveOutDate: Int? = null,
    var expectedMoveOutDate: Int? = null,
    override val vacateScheduledDate: Int? = null,
    val tou: String? = null, //未使用
    override val tandemSign: String? = null, //未使用
    override val cancellationSign: Int? = null,
    val logicalDeleteFlag: Int? = null,
    val consolidatedBuildingCode: String? = null,
    val consolidatedParkingCode: String? = null,
    val transferredBuildingCode: String? = null,
    override val logicalDeleteSign: Int? = null,
    override val vacateNoticeDate: Int? = null,//未使用
    val assessmentDivision: String? = null,
    override val parkingCertIssueSign: String? = null,
    override val parkingCertComment: String? = null,
    val bulkLeaseFlag: Int? = null,
    val parkingCategory: String? = null,
    override val aggregateContractNumber: String? = null,
    val thirtyFiveYearLumpSumCategory: String? = null,
    val offSiteParkingCategory: String? = null,
    val distance: Int? = null,
    val postalCode: String? = null,
    val prefectureKanjiName: String? = null,
    val cityKanjiName: String? = null,
    val townKanjiName: String? = null,
    val clientNameKanji: String? = null,
    val businessOfficeCode: String? = null,
    val businessOfficeCode2: String? = null,
    val specialContractFlag: Int? = null,
    val brokerApplicationPossibility: String? = null,
    val parkingLotEnable: String? = null,
    val allowAllParkingLotAvailabilityEdit: String? = null,
    //査定DB
    override val keyMoneyAmount: Int? = null, //未使用
    override val depositAmount: Int? = null, //未使用
    override val parkingFee: Int? = null,
    override val brokerApplicationCollectionDivision: String? = null, //未使用
    override val parkingFeeInTax: String? = null,
    override val keyMoneyInTax: String? = null, //未使用
    override val standardRentForCoop: Int? = null, //未使用
    // 外部API向け部屋テナント契約情報
    val roomBuildingCode: String? = null,
    val roomRoomCode: String? = null,
    var roomRoomNumber: String? = null,
    // ウェルカムパークバッチ専用 斡旋回収前フラグ
    val brokerApplicationCollectionFlg: Int? = null,
    // 合算時の元情報保持
    var orgParkingVehiclePojo: OrgParkingVehiclePojo? = null,
    // 部屋テナント契約情報
    var propertyTenant: PropertyTenantContractPojo? = null,
    // 空き部屋フラグ
    var beFlg: String? = null,
    // 駐車場区画ステータス
    var parkingStatusDivision: ParkingLot.StatusDivision? = VACANT,
) : ParkingTenantContractPojoInterface, ParkingLatestRentEvaluationInterface {
    fun toParking(
        parkingLotList: List<ParkingLot>,
        thirtyFiveYearBulkBuildings: List<Building.Code>
    ): Parking {
        return Parking(
            building = Building(
                code = Building.Code.of(this.buildingCode),
                name = Building.Name.of(this.buildingName ?: ""),
                postalCode = this.postalCode,
                location = convertToLocation(
                    this.prefectureKanjiName,
                    this.cityKanjiName,
                    this.townKanjiName,
                    this.addressDetail
                ),
                businessOfficeCode = (this.businessOfficeCode ?: this.businessOfficeCode2)
                    ?.let { Office.Code.of(it) },
                buildingContractForm = convertToBuildingContractForm(
                    this.buildingBulkLeaseFlag,
                    this.buildingCode,
                    this.buildingThirtyFiveYearLumpSumCategory,
                    thirtyFiveYearBulkBuildings
                ),
                landlordName = this.clientNameKanji,
                completionDeliveryDate = this.completionDeliveryDate?.toString()?.LocalDate(),
            ),
            parkingLotList = parkingLotList,
            allowAllParkingLotAvailabilityEdit = this.allowAllParkingLotAvailabilityEdit?.toBoolean()
                ?: false
        )
    }

    fun toParkingLot(
        reservationListMap: Map<ParkingLot.Id, List<ParkingReservationInfo>>,
        thirtyFiveYearBulkBuildings: List<Building.Code>,
        authInfo: AuthInfo? = null,
    ): ParkingLot {
        val buildingCode = Building.Code.of(buildingCode)

        val id = ParkingLot.Id(buildingCode, ParkingLot.Code.of(parkingLotCode!!))
        val propertyTenant = this.propertyTenant
        val tenantBuildingCode = propertyTenant?.tenantBuildingCode
        val tenantRoomCode = propertyTenant?.tenantRoomCode
        val linkedPropertyId =
            if (tenantBuildingCode != null && tenantRoomCode != null) {
                // PARKING_ADDITIONAL_INFO_MASTER の ROOM_ROOM_CODEが5桁でない場合があり、その場合は物件と直接紐づかないのでnullを返す
                if (tenantRoomCode.length != Room.Code.LENGTH) {
                    null
                } else {
                    Property.Id(Building.Code.of(tenantBuildingCode), Room.Code.of(tenantRoomCode))
                }
            } else {
                null
            }
        val reservationList = reservationListMap[id] ?: emptyList()
        val isAvailable = parkingLotEnable?.toBoolean() != false

        // キマルームサインからAPIの場合、仮申し込みを無視する
        val shouldIgnoreTentative = when (authInfo) {
            is AuthInfo.ApiKey -> authInfo.externalSystem == ExternalSystem.KIMAROOM_SIGN
            else -> false
        }
        val vacancyStatus = getVacancyParkingStatus(
            isAvailable,
            parkingStatusDivision,
            reservationList,
            brokerApplicationPossibility,
            shouldIgnoreTentative
        )
        return ParkingLot(
            id = id,
            localDisplayNumber = parkingLotNumber,
            parkingLotCategory = ParkingLot.Category.fromValue(parkingCategory),
            parkingStatusDivision = parkingStatusDivision!!,
            vacancyParkingStatus = vacancyStatus,
            isAvailable = isAvailable,
            parkingFee = parkingFee,
            parkingFeeInTax = parkingFeeInTax?.toInt(),
            assessmentDivision = convertToAssessmentDivision(assessmentDivision, bulkLeaseFlag),
            specialContractFlag = ParkingLot.SpecialContractFlag.fromValue(specialContractFlag),
            contractForm = convertToContractForm(
                bulkLeaseFlag,
                buildingCode.value,
                thirtyFiveYearLumpSumCategory,
                thirtyFiveYearBulkBuildings
            ),
            bulkLeaseFlag = BulkLeaseFlag.fromValue(this.bulkLeaseFlag),
            moveInScheduledDate = moveInScheduledDate?.toString()?.LocalDate(),
            expectedMoveOutDate = expectedMoveOutDate?.toString()
                ?.LocalDate(), // 「退去予定」の場合にのみ明渡し予定日がセットされている
            brokerApplicationPossibility = convertToBrokerApplicationPossibility(
                brokerApplicationPossibility
            ),
            offSiteParkingLotCategory = convertToOffSiteParkingCategory(offSiteParkingCategory),
            offSiteParkingDistance = distance,
            linkedPropertyId = linkedPropertyId,
            linkedRoomNumber = if (linkedPropertyId != null) propertyTenant?.tenantRoomNumber?.let {
                Room.Number.of(
                    it
                )
            } else null,
            reservationList = reservationList,
            tenant = if (tenantContractNumber.isNullOrBlank()
                || orgParkingVehiclePojo?.tenantContractNumber.isNullOrBlank()
            ) {
                null
            } else {
                ParkingLot.Tenant(
                    tenantContractNumber = tenantContractNumber,
                    originalContractNumber = orgParkingVehiclePojo?.tenantContractNumber!!,
                    tenantContractName = tenantNameKanji,
                    propertyTenantContractName = propertyTenant?.tenantNameKanji,
                    propertyTenantName = propertyTenant?.tenantName,
                    landTransportName = landTransportName,
                    type = type,
                    businessCategory = businessCategory,
                    leftNumber = leftNumber,
                    rightNumber = rightNumber,
                    manufacturerDivision = manufacturerDivision,
                    carModelName = carModelName,
                    parkingCertIssueSign = parkingCertIssueSign,
                    parkingCertComment = parkingCertComment,
                )
            }
        )
    }

    /** 駐車場区画ステータスenumを決定する */
    fun computeParkingStatusDivision(isWelcomePark: Boolean = false): ParkingDetailPojo {
        val moveOutDate = this.moveOutDate?.toString()?.LocalDate()
        var convStatus = toEboardParkingStatusDivision(
            currentStateDivision,
            modificationStateDivision,
            cancellationSign?.toString(),
            moveInStartProcessedSign?.toString(),
            moveOutDate,
        )
        if (moveOutDate != null) convStatus =
            VACANT // 退居日がある場合は空き区画とする JavaSource/jp/co/daito/eboard/Parking/EB800ParkingComBean.javaL2289
        if (beFlg == "3") {
            //以前のテ契を取得していて、有効な情報が存在した場合
            //退去日に入力がない
            if (moveOutDate == null) convStatus = PLANNED_MOVE_OUT
        } else if (beFlg == "2") {
            //以前のテ契を取得していたが、状態が20以上の有効な情報が存在しない、または以前のが存在しない
            convStatus = VACANT
        }
        // WelcomeParkバッチ処理の場合のときのみ斡旋回収前を判断
        convStatus = if (isWelcomePark && brokerApplicationCollectionFlg == 1) {
            BEFORE_BROKER_COLLECTION
        } else {
            convStatus
        }
        this.parkingStatusDivision = convStatus // 駐車場区画ステータスをセット
        //後の通常合算処理のために車両情報は毎回退避しておく
        this.orgParkingVehiclePojo = OrgParkingVehiclePojo(
            tenantContractNumber = this.tenantContractNumber, // 駐車場車種情報ファイルの更新時にも利用する
            landTransportName = this.landTransportName,
            type = this.type,
            businessCategory = this.businessCategory,
            leftNumber = this.leftNumber,
            rightNumber = this.rightNumber,
            manufacturerDivision = this.manufacturerDivision,
            carModelName = this.carModelName,
        )
        return handleApplyParkingStatusDivision()
    }

    // 確定した駐車場区画ステータスを元に、フィールドの値を再セットする
    private fun handleApplyParkingStatusDivision(): ParkingDetailPojo {
        var ret = this
        if (parkingStatusDivision == VACANT) {
            // 状態「空」の場合のクリア処理
            ret = ret.copy(
                tenantName = null, //実入居者名
                moveInScheduledDate = null, // 入居予定日
                expectedMoveOutDate = null, // 退去予定日
                tenantNameKanji = null, // 契約者名
                contractExpiryDate = null, // 契約満了日
                contractEffectiveEndDate = null, // 契約終了日
                tenantContractNumber = null, // テナント契約番号
                propertyTenant = null, // 部屋テナント契約情報
                roomBuildingCode = null, // 部屋テナント契約情報
                roomRoomCode = null, // 部屋テナント契約情報
                roomRoomNumber = null, // 部屋テナント契約情報
                landTransportName = null, // 陸事名
                type = null, // 車両種別
                businessCategory = null, // 業態
                leftNumber = null, // 左ナンバー
                rightNumber = null, // 右ナンバー
                manufacturerDivision = null, // メーカー区分
                carModelName = null, // 車両名
                parkingCertIssueSign = null, // 車庫証明発給サイン
                parkingCertComment = null, // 車庫証明コメント
            )
        } else if (ret.parkingStatusDivision == CANCEL || ret.cancellationSign == 1) {
            // 状態「ｷｬﾝｾﾙ」の場合のクリア処理, キャンセルサインが１の場合は入居日をセットしない
            ret = ret.copy(
                moveInScheduledDate = null, // 入居予定日をクリア
            )
        }
        if (ret.parkingStatusDivision == PLANNED_MOVE_OUT) {
            // 状態「退去予定」の場合にのみ明渡し予定日を退去予定日にセット
            ret = ret.copy(
                expectedMoveOutDate = ret.vacateScheduledDate,
            )
        }
        return ret
    }

    companion object {

        /** 駐車場区画Pojoリストを区画情報を持たない建物リストに変換する */
        fun List<ParkingDetailPojo>.toParkingOnlyBuildingList(thirtyFiveYearBulkBuildings: List<Building.Code>): List<Parking> {
            return this.groupBy { it.buildingCode }
                .map { entry ->
                    entry.value.first()
                        .toParking(emptyList(), thirtyFiveYearBulkBuildings) // 建物情報は区画で共通なため1つで十分
                }
        }

        /** 駐車場区画Pojoリストを建物毎の駐車場区画リストに変換する */
        fun List<ParkingDetailPojo>.toParkingList(
            reservationListMap: Map<ParkingLot.Id, List<ParkingReservationInfo>>,
            thirtyFiveYearBulkBuildings: List<Building.Code>,
            authInfo: AuthInfo? = null,
        ): List<Parking> {
            val buildingMap = this.groupBy { it.buildingCode }
            return buildingMap.values.mapNotNull {
                it.toParking(
                    reservationListMap,
                    thirtyFiveYearBulkBuildings,
                    authInfo
                )
            }
        }

        fun List<ParkingDetailPojo>.toParking(
            reservationListMap: Map<ParkingLot.Id, List<ParkingReservationInfo>>,
            thirtyFiveYearBulkBuildings: List<Building.Code>,
            authInfo: AuthInfo? = null,
        ): Parking? {
            // 空でくることはないが一応
            if (this.isEmpty()) return null
            val parkingLotList =
                this.filter { !it.parkingLotCode.isNullOrEmpty() }
                    .map {
                        it.toParkingLot(
                            reservationListMap,
                            thirtyFiveYearBulkBuildings,
                            authInfo
                        )
                    }
            return this[0].toParking(parkingLotList, thirtyFiveYearBulkBuildings)
        }

        // EboardのconvStatusの移植
        fun toEboardParkingStatusDivision(
            currentParkingStatus: String?,
            fixedParkingStatus: String?,
            cancellationSign: String?,
            moveInStartProcessedSign: String?,
            moveOutDate: LocalDate?
        ): ParkingLot.StatusDivision {
            // 駐車場区画ステータスの判定
            if (currentParkingStatus != "27" && cancellationSign == "1") {
                return CANCEL
            }
            if (currentParkingStatus in listOf("20", "29")) {
                return APPLIED
            }
            if (currentParkingStatus in listOf("25", "80")) {
                return CONFIRMED
            }
            if (currentParkingStatus == "30" || (currentParkingStatus == "35" && moveInStartProcessedSign == "0")) {
                return COLLECTING
            }
            if (currentParkingStatus == "35" && fixedParkingStatus in listOf("35", "36", "37")) {
                return OCCUPIED
            }

            // 退去済みかどうかの判定(DB退去日付が本日以前なら退去している)
            // ※但しこの後の処理で退去日付があれば全て空きとなるため、デッドコードと思われる
            val isLeave =
                moveOutDate?.isBefore(LocalDate.now()) == true || moveOutDate?.isEqual(LocalDate.now()) == true
            if ((currentParkingStatus == "35" && fixedParkingStatus == "40") ||
                currentParkingStatus in listOf("45", "47") ||
                (currentParkingStatus == "90" && isLeave)
            ) {
                return PLANNED_MOVE_OUT
            }
            return VACANT
        }

        fun String.LocalDate(): LocalDate? {
            if (this.length != 8) {
                return null
            }
            val date = try {
                this.yyyyMMdd()
            } catch (_: Exception) {
                null
            }
            return date
        }

        /** 査定区分enumに変換する */
        fun convertToAssessmentDivision(
            assessmentDivision: String?,
            bulkLeaseFlag: Int?
        ): ParkingLot.AssessmentDivision? {
            if (bulkLeaseFlag == 4 || bulkLeaseFlag == 7) {
                return when (assessmentDivision) {
                    "1" -> ASSESSMENT
                    "2" -> OUTSIDE_ASSESSMENT
                    else -> null
                }
            }
            return null
        }

        /** 斡旋可否enumに変換する */
        fun convertToBrokerApplicationPossibility(dbValue: String?): ParkingLot.BrokerApplicationPossibility? {
            return when (dbValue) {
                "0" -> IMPOSSIBLE
                "1" -> POSSIBLE
                else -> null
            }
        }

        /** 敷地外駐車場区分enumに変換する */
        fun convertToOffSiteParkingCategory(dbValue: String?): ParkingLot.OffSiteCategory {
            return when (dbValue) {
                "1" -> OUTSIDE
                else -> INSIDE // いい物件の仕様にあわせる
            }
        }

        /** 住所に変換する */
        fun convertToLocation(
            prefectureKanjiName: String?,
            cityKanjiName: String?,
            townKanjiName: String?,
            addressDetail: String?
        ): String? {
            return prefectureKanjiName?.let { prefecture ->
                cityKanjiName?.let { city ->
                    townKanjiName?.let { town ->
                        "$prefecture $city $town ${addressDetail ?: ""}"
                    }
                }
            }
        }

        /** 建物の契約形態を返す */
        fun convertToBuildingContractForm(
            buildingBulkLeaseFlag: Int?,
            buildingCode: String,
            buildingThirtyFiveYearLumpSumCategory: String?,
            thirtyFiveYearBulkBuildings: List<Building.Code>
        ): String? {
            return when (buildingBulkLeaseFlag?.toString()) {
                //一括契約タイプが4:30年一括借上の場合
                Building.SubleaseType.IKKATSU.value ->
                    // 一括借上タイプが3の場合は新35年一括借上（駐車管）
                    if (thirtyFiveYearBulkBuildings.contains(Building.Code.of(buildingCode))) {
                        Building.SubleaseType.NEW_IKKATSU_PARKING.value
                    }
                    // 35年一括区分が2の場合は新35年一括借上
                    else if (buildingThirtyFiveYearLumpSumCategory == "2") {
                        Building.SubleaseType.NEW_IKKATSU.value
                    }
                    // それ以外は30年一括借上
                    else {
                        buildingBulkLeaseFlag.toString()
                    }
                // それ以外はそのまま返却する
                else -> buildingBulkLeaseFlag?.toString()
            }
        }

        /** 駐車場区画の契約形態を返す */
        fun convertToContractForm(
            bulkLeaseFlag: Int?,
            buildingCode: String,
            thirtyFiveYearLumpSumCategory: String?,
            thirtyFiveYearBulkBuildings: List<Building.Code>
        ): String? {
            return when (bulkLeaseFlag?.toString()) {
                //一括契約タイプが4:30年一括借上の場合
                Building.SubleaseType.IKKATSU.value ->
                    // 35年一括区分が2の場合は新35年一括借上
                    if (thirtyFiveYearLumpSumCategory == "2") {
                        Building.SubleaseType.NEW_IKKATSU.value
                    }
                    // それ以外は30年一括借上
                    else bulkLeaseFlag.toString()
                //一括契約タイプが5:管理契約(大パ)の場合
                Building.SubleaseType.KANRIDAITATE.value ->
                    // 一括借上タイプが3の駐車場建物契約が紐付く場合は管理契約(新35年一括)
                    if (thirtyFiveYearBulkBuildings.contains(Building.Code.of(buildingCode))) {
                        Building.SubleaseType.NEW_KANRIDAITATE_PARKING.value
                    }
                    // それ以外は管理契約(大パ)
                    else bulkLeaseFlag.toString()
                // それ以外はそのまま返却する
                else -> bulkLeaseFlag?.toString()
            }
        }

        // 駐車場空き区画ステータス取得
        fun getVacancyParkingStatus(
            isAvailable: Boolean,
            parkingStatusDivision: ParkingLot.StatusDivision?,
            reservationList: List<ParkingReservationInfo>,
            brokerApplicationPossibility: String?,
            shouldIgnoreTentative: Boolean,
        ): ParkingLot.VacancyStatus {
            if (!isAvailable) {
                return ParkingLot.VacancyStatus.IMPOSSIBLE
            }
            if (parkingStatusDivision !in listOf(VACANT, CANCEL, PLANNED_MOVE_OUT)) {
                return ParkingLot.VacancyStatus.IMPOSSIBLE
            }
            // 退去予定で斡旋区分が募集不可の場合は不可
            if (parkingStatusDivision == PLANNED_MOVE_OUT && brokerApplicationPossibility == "0") {
                return ParkingLot.VacancyStatus.IMPOSSIBLE
            }
            // 以下以外の予約が存在する場合、「不可」と判定する（予約状態が「仮申込」/「受付」のみ取得している）
            // ・予約システムが「ウェルカムパーク」
            // ・予約種別が「作業」
            // ・shouldIgnoreTentative=trueの時の「仮申し込み」
            if (reservationList.isNotEmpty()) {
                if (reservationList.stream().anyMatch {
                        (it.requestSource != ParkingReservation.RequestSource.WELCOME_PARK && it.reservationType != ParkingReservation.Type.WORK)
                                && !(shouldIgnoreTentative && it.status == ParkingReservation.Status.TENTATIVE)
                    }
                ) {
                    return ParkingLot.VacancyStatus.IMPOSSIBLE
                }
            }
            return ParkingLot.VacancyStatus.POSSIBLE
        }
    }
}

// 前テナント契約用
data class PreviousParkingTenantContractPojo(
    val contractEffectiveStartDate: Int? = null,
    override val tenantContractNumber: String? = null,
    override val roomCode: String? = null,
    override val landTransportName: String? = null,
    override val type: String? = null,
    override val businessCategory: String? = null,
    override val leftNumber: String? = null,
    override val rightNumber: String? = null,
    override val manufacturerDivision: String? = null,
    override val carModelName: String? = null,
    override val lightVehicleSign: String? = null,
    override val tenantName: String? = null,
    override val moveInScheduledDate: Int? = null,
    override val tenantNameKanji: String? = null,
    override val contractExpiryDate: Int? = null,
    override val contractEffectiveEndDate: Int? = null,
    override val currentStateDivision: String? = null,
    override val modificationStateDivision: String? = null,
    override val moveInStartProcessedSign: Int? = null,
    override val moveOutDate: Int? = null,
    override val vacateScheduledDate: Int? = null,
    override val tandemSign: String? = null,
    override val cancellationSign: Int? = null,
    override val logicalDeleteSign: Int? = null,
    override val vacateNoticeDate: Int? = null,
    override val parkingCertIssueSign: String? = null,
    override val parkingCertComment: String? = null,
    override val aggregateContractNumber: String? = null,
) : ParkingTenantContractPojoInterface

// テナント部屋契約情報専用
data class PropertyTenantContractPojo(
    override val tenantBuildingCode: String? = null,
    override val tenantRoomCode: String? = null,
    override val tenantRoomNumber: String? = null,
    override val tenantNameKanji: String? = null, // 契約者名
    override val tenantName: String? = null, // 実入居者名
) : PropertyTenantContractPojoInterface

// 合算時の元情報保持用
data class OrgParkingVehiclePojo(
    override val tenantContractNumber: String? = null,
    override val landTransportName: String? = null,
    override val type: String? = null,
    override val businessCategory: String? = null,
    override val leftNumber: String? = null,
    override val rightNumber: String? = null,
    override val manufacturerDivision: String? = null,
    override val carModelName: String? = null,
) : ParkingVehiclePojoInterface

interface ParkingTenantContractPojoInterface : ParkingVehiclePojoInterface,
    TenantContractPojoInterface {
    val roomCode: String?
    val lightVehicleSign: String?
    val tandemSign: String?
    val parkingCertIssueSign: String?
    val parkingCertComment: String?
    val aggregateContractNumber: String?
    val logicalDeleteSign: Int?
}

interface PropertyTenantContractPojoInterface {
    val tenantBuildingCode: String?
    val tenantRoomCode: String?
    val tenantRoomNumber: String?
    val tenantNameKanji: String?
    val tenantName: String?
}

interface ParkingVehiclePojoInterface {
    val tenantContractNumber: String?
    val landTransportName: String?
    val type: String?
    val businessCategory: String?
    val leftNumber: String?
    val rightNumber: String?
    val manufacturerDivision: String?
    val carModelName: String?
}
