/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.pojos


import java.io.Serializable


/**
 * ライフラインマスタ 既存システム物理名: HATK0P
 */
@Suppress("UNCHECKED_CAST")
data class UtilityMasterPojo(
    var creationDate: Int? = null,
    var creationTime: Int? = null,
    var updateDate: Int? = null,
    var updateTime: Int? = null,
    var updatePgm: String? = null,
    var updateResponsibleCd: String? = null,
    var utilityType: String? = null,
    var utilityCompanyCd: String? = null,
    var utilityBranchCd: String? = null,
    var utilityCompanyKana: String? = null,
    var utilityCompanyKanji: String? = null,
    var utilityBranchKana: String? = null,
    var utilityBranchKanji: String? = null,
    var phoneNumber: String? = null,
    var deleteDate: Int? = null
): Serializable {


    override fun equals(other: Any?): Boolean {
        if (this === other)
            return true
        if (other == null)
            return false
        if (this::class != other::class)
            return false
        val o: UtilityMasterPojo = other as UtilityMasterPojo
        if (this.creationDate == null) {
            if (o.creationDate != null)
                return false
        }
        else if (this.creationDate != o.creationDate)
            return false
        if (this.creationTime == null) {
            if (o.creationTime != null)
                return false
        }
        else if (this.creationTime != o.creationTime)
            return false
        if (this.updateDate == null) {
            if (o.updateDate != null)
                return false
        }
        else if (this.updateDate != o.updateDate)
            return false
        if (this.updateTime == null) {
            if (o.updateTime != null)
                return false
        }
        else if (this.updateTime != o.updateTime)
            return false
        if (this.updatePgm == null) {
            if (o.updatePgm != null)
                return false
        }
        else if (this.updatePgm != o.updatePgm)
            return false
        if (this.updateResponsibleCd == null) {
            if (o.updateResponsibleCd != null)
                return false
        }
        else if (this.updateResponsibleCd != o.updateResponsibleCd)
            return false
        if (this.utilityType == null) {
            if (o.utilityType != null)
                return false
        }
        else if (this.utilityType != o.utilityType)
            return false
        if (this.utilityCompanyCd == null) {
            if (o.utilityCompanyCd != null)
                return false
        }
        else if (this.utilityCompanyCd != o.utilityCompanyCd)
            return false
        if (this.utilityBranchCd == null) {
            if (o.utilityBranchCd != null)
                return false
        }
        else if (this.utilityBranchCd != o.utilityBranchCd)
            return false
        if (this.utilityCompanyKana == null) {
            if (o.utilityCompanyKana != null)
                return false
        }
        else if (this.utilityCompanyKana != o.utilityCompanyKana)
            return false
        if (this.utilityCompanyKanji == null) {
            if (o.utilityCompanyKanji != null)
                return false
        }
        else if (this.utilityCompanyKanji != o.utilityCompanyKanji)
            return false
        if (this.utilityBranchKana == null) {
            if (o.utilityBranchKana != null)
                return false
        }
        else if (this.utilityBranchKana != o.utilityBranchKana)
            return false
        if (this.utilityBranchKanji == null) {
            if (o.utilityBranchKanji != null)
                return false
        }
        else if (this.utilityBranchKanji != o.utilityBranchKanji)
            return false
        if (this.phoneNumber == null) {
            if (o.phoneNumber != null)
                return false
        }
        else if (this.phoneNumber != o.phoneNumber)
            return false
        if (this.deleteDate == null) {
            if (o.deleteDate != null)
                return false
        }
        else if (this.deleteDate != o.deleteDate)
            return false
        return true
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        result = prime * result + (if (this.creationDate == null) 0 else this.creationDate.hashCode())
        result = prime * result + (if (this.creationTime == null) 0 else this.creationTime.hashCode())
        result = prime * result + (if (this.updateDate == null) 0 else this.updateDate.hashCode())
        result = prime * result + (if (this.updateTime == null) 0 else this.updateTime.hashCode())
        result = prime * result + (if (this.updatePgm == null) 0 else this.updatePgm.hashCode())
        result = prime * result + (if (this.updateResponsibleCd == null) 0 else this.updateResponsibleCd.hashCode())
        result = prime * result + (if (this.utilityType == null) 0 else this.utilityType.hashCode())
        result = prime * result + (if (this.utilityCompanyCd == null) 0 else this.utilityCompanyCd.hashCode())
        result = prime * result + (if (this.utilityBranchCd == null) 0 else this.utilityBranchCd.hashCode())
        result = prime * result + (if (this.utilityCompanyKana == null) 0 else this.utilityCompanyKana.hashCode())
        result = prime * result + (if (this.utilityCompanyKanji == null) 0 else this.utilityCompanyKanji.hashCode())
        result = prime * result + (if (this.utilityBranchKana == null) 0 else this.utilityBranchKana.hashCode())
        result = prime * result + (if (this.utilityBranchKanji == null) 0 else this.utilityBranchKanji.hashCode())
        result = prime * result + (if (this.phoneNumber == null) 0 else this.phoneNumber.hashCode())
        result = prime * result + (if (this.deleteDate == null) 0 else this.deleteDate.hashCode())
        return result
    }
}
