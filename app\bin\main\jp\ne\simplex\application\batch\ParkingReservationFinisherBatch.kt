package jp.ne.simplex.application.batch

import jp.ne.simplex.application.repository.db.BatchExecuteHistoryRepositoryInterface
import jp.ne.simplex.application.service.ParkingReservationService
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component

/**
 * 駐車場予約完了バッチ
 * 駐車場予約対象；受付かつ申込かつ利用開始日時
 * 駐車場区画対象；確定または入居中
 * */
@Profile("batch")
@Component("ParkingReservationFinisherBatch")
class ParkingReservationFinisherBatch(
    private val parkingReservationService: ParkingReservationService,
    override val batchExecuteHistoryRepository: BatchExecuteHistoryRepositoryInterface,
) : BatchInterface {
    override val batchType: BatchType = BatchType.PARKING_RESERVATION_FINISHER

    companion object {
        private val log = LoggerFactory.getLogger(ParkingReservationFinisherBatch::class.java)
    }

    override fun executeInternal(executeOption: ExecuteOptionType?) {
        log.info("ParkingReservationFinisherBatch start.")
        // 処理対象となる予約一覧を取得
        val targetReservations = parkingReservationService.getActiveReservationsShouldFinish()
        log.info("ParkingReservationFinisherBatch target count = {}", targetReservations.size)
        // 予約を完了へ更新し、後処理の外部連携を実施
        val updated = parkingReservationService.finishReservations(targetReservations)
        log.info("ParkingReservationFinisherBatch updated count = {}", updated)

        log.info("ParkingReservationFinisherBatch finish.")
    }

    override fun isAcceptableOption(executeOption: ExecuteOptionType): Boolean {
        return executeOption == ExecuteOptionType.RERUN
    }

}
