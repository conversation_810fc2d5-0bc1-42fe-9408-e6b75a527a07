/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.LatestProductMasterTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.LatestProductMasterPojo

import org.jooq.impl.TableRecordImpl


/**
 * 最新商品マスタ 既存システム物理名: EGJKCP
 */
@Suppress("UNCHECKED_CAST")
open class LatestProductMasterRecord private constructor() : TableRecordImpl<LatestProductMasterRecord>(LatestProductMasterTable.LATEST_PRODUCT_MASTER) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var productNameCd: Short?
        set(value): Unit = set(6, value)
        get(): Short? = get(6) as Short?

    open var productCdBranch: Byte?
        set(value): Unit = set(7, value)
        get(): Byte? = get(7) as Byte?

    open var productName: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var productGradeName: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    /**
     * Create a detached, initialised LatestProductMasterRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, productNameCd: Short? = null, productCdBranch: Byte? = null, productName: String? = null, productGradeName: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.productNameCd = productNameCd
        this.productCdBranch = productCdBranch
        this.productName = productName
        this.productGradeName = productGradeName
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised LatestProductMasterRecord
     */
    constructor(value: LatestProductMasterPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.productNameCd = value.productNameCd
            this.productCdBranch = value.productCdBranch
            this.productName = value.productName
            this.productGradeName = value.productGradeName
            resetChangedOnNotNull()
        }
    }
}
