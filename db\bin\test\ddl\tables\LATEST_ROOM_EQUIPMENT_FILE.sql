-- TABLE: LATEST_ROOM_EQUIPMENT_FILE(最新部屋設備ファイル)

CREATE TABLE LATEST_ROOM_EQUIPMENT_FILE(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1,0)                  
,    BUILDING_CODE                                varchar(9)                    
,    ROOM_CODE                                    varchar(5)                    
,    EQUIPMENT_CODE                               varchar(3)                    
,    CONSTRAINT UQ_LATEST_ROOM_EQUIPMENT_FILE UNIQUE (BUILDING_CODE, ROOM_CODE, EQUIPMENT_CODE, LOGICAL_DELETE_SIGN)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE LATEST_ROOM_EQUIPMENT_FILE IS '最新部屋設備ファイル 既存システム物理名: EJJHSP';
COMMENT ON COLUMN LATEST_ROOM_EQUIPMENT_FILE.CREATION_DATE IS '作成年月日 既存システム物理名: EJJ01D 1:削除';
COMMENT ON COLUMN LATEST_ROOM_EQUIPMENT_FILE.CREATION_TIME IS '作成時刻 既存システム物理名: EJJ02H';
COMMENT ON COLUMN LATEST_ROOM_EQUIPMENT_FILE.UPDATE_DATE IS '更新年月日 既存システム物理名: EJJ03D';
COMMENT ON COLUMN LATEST_ROOM_EQUIPMENT_FILE.UPDATE_TIME IS '更新時刻 既存システム物理名: EJJ04H';
COMMENT ON COLUMN LATEST_ROOM_EQUIPMENT_FILE.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EJJ05N';
COMMENT ON COLUMN LATEST_ROOM_EQUIPMENT_FILE.UPDATER IS '更新者 既存システム物理名: EJJ06C';
COMMENT ON COLUMN LATEST_ROOM_EQUIPMENT_FILE.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: EJJ07S';
COMMENT ON COLUMN LATEST_ROOM_EQUIPMENT_FILE.BUILDING_CODE IS '建物コード 既存システム物理名: EJJABC';
COMMENT ON COLUMN LATEST_ROOM_EQUIPMENT_FILE.ROOM_CODE IS '部屋コード 既存システム物理名: EJJACC';
COMMENT ON COLUMN LATEST_ROOM_EQUIPMENT_FILE.EQUIPMENT_CODE IS '設備コード 既存システム物理名: EJJSTC';
