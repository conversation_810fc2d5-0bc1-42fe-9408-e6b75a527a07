-- TABLE: AFFILIATION_MASTER(所属マスタ)

CREATE TABLE AFFILIATION_MASTER(
     SHO<PERSON>OKU_CODE                                 varchar(6)                    
,    USAGE_START_DATE                             numeric(8,0)                  
,    USAGE_END_DATE                               numeric(8,0)                  
,    <PERSON><PERSON><PERSON>OKU_NAME                                 varchar(42)                   
,    <PERSON><PERSON><PERSON>OKU_ABBREV1                              varchar(14)                   
,    SH<PERSON><PERSON>OKU_ABBREV2                              varchar(10)                   
,    SHOZOKU_ABBREV3                              varchar(8)                    
,    SHOZOKU_ABBREV4                              varchar(6)                    
,    HIERARCHY_DIVISION                           varchar(2)                    
,    COMPANY_CODE                                 varchar(3)                    
,    LOCATION_CODE                                varchar(3)                    
,    UPPER_SHOZOKU_CODE                           varchar(6)                    
,    HQ_BRANCH_DIVISION                           varchar(1)                    
,    AGGREGATION_DETAIL_DIVISION                  varchar(1)                    
,    ACCOUNTING_SPECIFIC_DIVISION                 varchar(1)                    
,    COST_DEPARTMENT_DIVISION                     varchar(1)                    
,    HR_SHOZOKU_OUTPUT_ORDER                      varchar(5)                    
,    ACCOUNTING_SHOZOKU_OUTPUT_ORDER              varchar(8)                    
,    ACCOUNTING_SHOZOKU_CODE                      varchar(6)                    
,    PERSONNEL_COST_CODE                          varchar(6)                    
,    JOB_TYPE_CODE                                varchar(3)                    
,    HR_OUTPUT_ORDER0                             varchar(5)                    
,    HR_OUTPUT_ORDER1                             varchar(5)                    
,    HR_OUTPUT_ORDER2                             varchar(5)                    
,    HR_OUTPUT_ORDER3                             varchar(5)                    
,    HR_OUTPUT_ORDER4                             varchar(5)                    
,    HR_OUTPUT_ORDER5                             varchar(5)                    
,    HR_OUTPUT_ORDER6                             varchar(5)                    
,    JURISDICTION_CODE                            varchar(3)                    
,    DELETE_DIVISION                              varchar(1)                    
,    CREATOR                                      varchar(6)                    
,    CREATION_PROGRAM                             varchar(10)                   
,    CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    UPDATE_PROGRAM                               varchar(10)                   
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    TERMINAL_ID                                  varchar(10)                   
) TABLESPACE :TS_TBL;

COMMENT ON TABLE AFFILIATION_MASTER IS '所属マスタ 既存システム物理名: JXB1MP';
COMMENT ON COLUMN AFFILIATION_MASTER.SHOZOKU_CODE IS '所属コード 既存システム物理名: JXB01C';
COMMENT ON COLUMN AFFILIATION_MASTER.USAGE_START_DATE IS '使用開始年月日 既存システム物理名: JXB02D';
COMMENT ON COLUMN AFFILIATION_MASTER.USAGE_END_DATE IS '使用終了年月日 既存システム物理名: JXB03D';
COMMENT ON COLUMN AFFILIATION_MASTER.SHOZOKU_NAME IS '所属名称 既存システム物理名: JXB04M';
COMMENT ON COLUMN AFFILIATION_MASTER.SHOZOKU_ABBREV1 IS '所属略称1 既存システム物理名: JXB05M';
COMMENT ON COLUMN AFFILIATION_MASTER.SHOZOKU_ABBREV2 IS '所属略称2 既存システム物理名: JXB06M';
COMMENT ON COLUMN AFFILIATION_MASTER.SHOZOKU_ABBREV3 IS '所属略称3 既存システム物理名: JXB07M';
COMMENT ON COLUMN AFFILIATION_MASTER.SHOZOKU_ABBREV4 IS '所属略称4 既存システム物理名: JXB08M';
COMMENT ON COLUMN AFFILIATION_MASTER.HIERARCHY_DIVISION IS '階層区分 既存システム物理名: JXB09B';
COMMENT ON COLUMN AFFILIATION_MASTER.COMPANY_CODE IS '会社コード 既存システム物理名: JXB10C';
COMMENT ON COLUMN AFFILIATION_MASTER.LOCATION_CODE IS '所在地コード 既存システム物理名: JXB11C';
COMMENT ON COLUMN AFFILIATION_MASTER.UPPER_SHOZOKU_CODE IS '上位所属コード 既存システム物理名: JXB12C';
COMMENT ON COLUMN AFFILIATION_MASTER.HQ_BRANCH_DIVISION IS '本社支店区分 既存システム物理名: JXB13B';
COMMENT ON COLUMN AFFILIATION_MASTER.AGGREGATION_DETAIL_DIVISION IS '集計明細区分 既存システム物理名: JXB14B';
COMMENT ON COLUMN AFFILIATION_MASTER.ACCOUNTING_SPECIFIC_DIVISION IS '会計専用区分 既存システム物理名: JXB15B';
COMMENT ON COLUMN AFFILIATION_MASTER.COST_DEPARTMENT_DIVISION IS '原価部門区分 既存システム物理名: JXB16B';
COMMENT ON COLUMN AFFILIATION_MASTER.HR_SHOZOKU_OUTPUT_ORDER IS '人事所属出力順 既存システム物理名: JXB17C';
COMMENT ON COLUMN AFFILIATION_MASTER.ACCOUNTING_SHOZOKU_OUTPUT_ORDER IS '会計所属出力順 既存システム物理名: JXB18C';
COMMENT ON COLUMN AFFILIATION_MASTER.ACCOUNTING_SHOZOKU_CODE IS '計上所属コード 既存システム物理名: JXB19C';
COMMENT ON COLUMN AFFILIATION_MASTER.PERSONNEL_COST_CODE IS '人件費計上コード 既存システム物理名: JXB20C';
COMMENT ON COLUMN AFFILIATION_MASTER.JOB_TYPE_CODE IS '職種コード 既存システム物理名: JXB21C';
COMMENT ON COLUMN AFFILIATION_MASTER.HR_OUTPUT_ORDER0 IS '人事出力順0 既存システム物理名: JXB22C';
COMMENT ON COLUMN AFFILIATION_MASTER.HR_OUTPUT_ORDER1 IS '人事出力順1 既存システム物理名: JXB23C';
COMMENT ON COLUMN AFFILIATION_MASTER.HR_OUTPUT_ORDER2 IS '人事出力順2 既存システム物理名: JXB24C';
COMMENT ON COLUMN AFFILIATION_MASTER.HR_OUTPUT_ORDER3 IS '人事出力順3 既存システム物理名: JXB25C';
COMMENT ON COLUMN AFFILIATION_MASTER.HR_OUTPUT_ORDER4 IS '人事出力順4 既存システム物理名: JXB26C';
COMMENT ON COLUMN AFFILIATION_MASTER.HR_OUTPUT_ORDER5 IS '人事出力順5 既存システム物理名: JXB27C';
COMMENT ON COLUMN AFFILIATION_MASTER.HR_OUTPUT_ORDER6 IS '人事出力順6 既存システム物理名: JXB28C';
COMMENT ON COLUMN AFFILIATION_MASTER.JURISDICTION_CODE IS '管轄コード 既存システム物理名: JXB29C';
COMMENT ON COLUMN AFFILIATION_MASTER.DELETE_DIVISION IS '削除区分 既存システム物理名: JXBZ0B';
COMMENT ON COLUMN AFFILIATION_MASTER.CREATOR IS '作成者 既存システム物理名: JXBZ1C';
COMMENT ON COLUMN AFFILIATION_MASTER.CREATION_PROGRAM IS '作成プログラム 既存システム物理名: JXBZ2C';
COMMENT ON COLUMN AFFILIATION_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: JXBZ3D';
COMMENT ON COLUMN AFFILIATION_MASTER.CREATION_TIME IS '作成時間 既存システム物理名: JXBZ4H';
COMMENT ON COLUMN AFFILIATION_MASTER.UPDATER IS '更新者 既存システム物理名: JXBZ5C';
COMMENT ON COLUMN AFFILIATION_MASTER.UPDATE_PROGRAM IS '更新プログラム 既存システム物理名: JXBZ6C';
COMMENT ON COLUMN AFFILIATION_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: JXBZ7D';
COMMENT ON COLUMN AFFILIATION_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: JXBZ8H';
COMMENT ON COLUMN AFFILIATION_MASTER.TERMINAL_ID IS '端末ID 既存システム物理名: JXBZ9C';
