package jp.ne.simplex.application.repository.file

import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibility
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibilityAutoJudge
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.VacancyParkingLotTarget.BulkLeaseFlag
import jp.ne.simplex.stub.stubParkingContractPossibility
import jp.ne.simplex.stub.stubVacancyParkingLotTarget
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.io.BufferedWriter
import java.io.StringWriter
import kotlin.test.assertEquals

class ParkingFileRepositoryTest {
    private val repository = ParkingFileRepository()

    @Nested
    @DisplayName("DK-PORTAL向け駐車場サマリCSV出力の検証")
    inner class Scenario1 {

        @Test
        @DisplayName("全パターンのCSV出力内容の確認")
        fun case1() {
            val list = listOf(
                stubParkingContractPossibility(
                    orderCode = "1000001",
                    firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                    secondParkingContractPossibility = ContractPossibility.POSSIBLE,
                    isAutoJudge = ContractPossibilityAutoJudge.MANUAL,
                ),
                stubParkingContractPossibility(
                    orderCode = "1000002",
                    firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                    secondParkingContractPossibility = ContractPossibility.POSSIBLE,
                    isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                ),
                stubParkingContractPossibility(
                    orderCode = "1000003",
                    firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                    secondParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                    isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                ),
                stubParkingContractPossibility(
                    orderCode = "1000004",
                    firstParkingContractPossibility = ContractPossibility.REQUIRED_CONFIRM,
                    secondParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                    isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                ),
                stubParkingContractPossibility(
                    orderCode = "1000005",
                    firstParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                    secondParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                    isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                ),
                stubParkingContractPossibility(
                    orderCode = "1000006",
                    firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                    secondParkingContractPossibility = ContractPossibility.REQUIRED_CONFIRM,
                    isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                ),
            )

            val writer = StringWriter()
            BufferedWriter(writer).use { w ->
                repository.writeParkingSummaryForDkPortal(w, list)
            }

            // 想定出力内容
            val expected = "orderCode,availableParkingFlag,secondParkingContractPossibleFlag\n" +
                    "1000001,1,0\n" +
                    "1000002,1,1\n" +
                    "1000003,1,0\n" +
                    "1000004,0,0\n" +
                    "1000005,0,0\n" +
                    "1000006,1,0\n"
            assertEquals(expected, writer.toString())
        }
    }

    @Nested
    @DisplayName("WelcomePark向け空き駐車場CSV出力の検証")
    inner class Scenario2 {

        @Test
        @DisplayName("全パターンのCSV出力内容の確認")
        fun case1() {
            val list = listOf(
                stubVacancyParkingLotTarget(
                    buildingCode = "100000001",
                    parkingLotCode = "001",
                    parkingLotNumber = "1",
                    parkingFee = 25000,
                    bulkLeaseFlag = BulkLeaseFlag.TEN_Y,
                    assessmentDivision = ParkingLot.AssessmentDivision.ASSESSMENT,
                    parkingLotCategory = ParkingLot.Category.SINGLE,
                ),
                stubVacancyParkingLotTarget(
                    buildingCode = "100000002",
                    parkingLotCode = "002",
                    parkingLotNumber = "2",
                    parkingFee = 30000,
                    bulkLeaseFlag = BulkLeaseFlag.THIRTY_Y,
                    assessmentDivision = ParkingLot.AssessmentDivision.OUTSIDE_ASSESSMENT,
                    parkingLotCategory = ParkingLot.Category.PARALLEL,
                ),
                stubVacancyParkingLotTarget(
                    buildingCode = "100000003",
                    parkingLotCode = "003",
                    parkingLotNumber = "3",
                    parkingFee = 27000,
                    bulkLeaseFlag = BulkLeaseFlag.CHOCHINHENMU,
                    assessmentDivision = null,
                    parkingLotCategory = ParkingLot.Category.SINGLE,
                ),
            )

            val writer = StringWriter()
            BufferedWriter(writer).use { w ->
                repository.writeVacancyParkingDataForWelcomePark(w, list, "20250325", "123456")
            }

            // 想定出力内容
            val expected = "100000001,001,1,25000,2,1,1,20250325,123456\n" +
                    "100000002,002,2,30000,3,2,2,20250325,123456\n" +
                    "100000003,003,3,27000,7,,1,20250325,123456\n"
            assertEquals(expected, writer.toString())
        }
    }
}
