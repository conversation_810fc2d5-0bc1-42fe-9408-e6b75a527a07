/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import java.math.BigDecimal

import jp.ne.simplex.db.jooq.gen.tables.TenantTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.TenantPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * テナント 既存システム物理名: EEA10P
 */
@Suppress("UNCHECKED_CAST")
open class TenantRecord private constructor() : UpdatableRecordImpl<TenantRecord>(TenantTable.TENANT) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var updateDate: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var updateTime: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateProgramId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var logicalDeleteSign: Byte?
        set(value): Unit = set(6, value)
        get(): Byte? = get(6) as Byte?

    open var tenantCode: String
        set(value): Unit = set(7, value)
        get(): String = get(7) as String

    open var integratedClientCode: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var tenantNameKanji: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var tenantNameKanji_2: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var tenantNameKana: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var transferVerificationKana1: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var transferVerificationKana2: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var headquartersNameKanji: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var headquartersNameKana: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var searchKana: String?
        set(value): Unit = set(16, value)
        get(): String? = get(16) as String?

    open var personalCorporateDivision: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var corporateStatusDivision: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var corporatePositionDivision: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var representativeNameKanji: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var representativeNameKana: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var representativeSearchKana: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var tenantDepartment: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var tenantContactName: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var postalCode: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var prefectureCode: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var cityCode: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var townCode: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var addressDetail: String?
        set(value): Unit = set(29, value)
        get(): String? = get(29) as String?

    open var buildingName: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var phoneNumber: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var faxNumber: String?
        set(value): Unit = set(32, value)
        get(): String? = get(32) as String?

    open var postalCode_2: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var prefectureCode_2: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var cityCode_2: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var townCode_2: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var addressDetail_2: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var buildingName_2: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var phoneNumber_2: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var faxNumber_2: String?
        set(value): Unit = set(40, value)
        get(): String? = get(40) as String?

    open var industryCode: String?
        set(value): Unit = set(41, value)
        get(): String? = get(41) as String?

    open var birthDate: Int?
        set(value): Unit = set(42, value)
        get(): Int? = get(42) as Int?

    open var workplaceName: String?
        set(value): Unit = set(43, value)
        get(): String? = get(43) as String?

    open var workplacePrefectureCode: String?
        set(value): Unit = set(44, value)
        get(): String? = get(44) as String?

    open var workplaceCityCode: String?
        set(value): Unit = set(45, value)
        get(): String? = get(45) as String?

    open var workplaceTownCode: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var workplaceAddressDetail: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var workplaceBuildingName: String?
        set(value): Unit = set(48, value)
        get(): String? = get(48) as String?

    open var workplaceIndustryCode: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    open var workplacePhoneNumber: String?
        set(value): Unit = set(50, value)
        get(): String? = get(50) as String?

    open var violationHistorySign: Byte?
        set(value): Unit = set(51, value)
        get(): Byte? = get(51) as Byte?

    open var nonManagedPropertySign: Byte?
        set(value): Unit = set(52, value)
        get(): Byte? = get(52) as Byte?

    open var interfaceSign: Byte?
        set(value): Unit = set(53, value)
        get(): Byte? = get(53) as Byte?

    open var corporateHousingAgency: Byte?
        set(value): Unit = set(54, value)
        get(): Byte? = get(54) as Byte?

    open var yearsOfService: BigDecimal?
        set(value): Unit = set(55, value)
        get(): BigDecimal? = get(55) as BigDecimal?

    open var industryPersonal: String?
        set(value): Unit = set(56, value)
        get(): String? = get(56) as String?

    open var personalAnnualIncome: Int?
        set(value): Unit = set(57, value)
        get(): Int? = get(57) as Int?

    open var cohabitantAnnualIncome: Int?
        set(value): Unit = set(58, value)
        get(): Int? = get(58) as Int?

    open var gender: String?
        set(value): Unit = set(59, value)
        get(): String? = get(59) as String?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised TenantRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, updateDate: Int? = null, updateTime: Int? = null, updateProgramId: String? = null, updater: String? = null, logicalDeleteSign: Byte? = null, tenantCode: String, integratedClientCode: String? = null, tenantNameKanji: String? = null, tenantNameKanji_2: String? = null, tenantNameKana: String? = null, transferVerificationKana1: String? = null, transferVerificationKana2: String? = null, headquartersNameKanji: String? = null, headquartersNameKana: String? = null, searchKana: String? = null, personalCorporateDivision: String? = null, corporateStatusDivision: String? = null, corporatePositionDivision: String? = null, representativeNameKanji: String? = null, representativeNameKana: String? = null, representativeSearchKana: String? = null, tenantDepartment: String? = null, tenantContactName: String? = null, postalCode: String? = null, prefectureCode: String? = null, cityCode: String? = null, townCode: String? = null, addressDetail: String? = null, buildingName: String? = null, phoneNumber: String? = null, faxNumber: String? = null, postalCode_2: String? = null, prefectureCode_2: String? = null, cityCode_2: String? = null, townCode_2: String? = null, addressDetail_2: String? = null, buildingName_2: String? = null, phoneNumber_2: String? = null, faxNumber_2: String? = null, industryCode: String? = null, birthDate: Int? = null, workplaceName: String? = null, workplacePrefectureCode: String? = null, workplaceCityCode: String? = null, workplaceTownCode: String? = null, workplaceAddressDetail: String? = null, workplaceBuildingName: String? = null, workplaceIndustryCode: String? = null, workplacePhoneNumber: String? = null, violationHistorySign: Byte? = null, nonManagedPropertySign: Byte? = null, interfaceSign: Byte? = null, corporateHousingAgency: Byte? = null, yearsOfService: BigDecimal? = null, industryPersonal: String? = null, personalAnnualIncome: Int? = null, cohabitantAnnualIncome: Int? = null, gender: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updateProgramId = updateProgramId
        this.updater = updater
        this.logicalDeleteSign = logicalDeleteSign
        this.tenantCode = tenantCode
        this.integratedClientCode = integratedClientCode
        this.tenantNameKanji = tenantNameKanji
        this.tenantNameKanji_2 = tenantNameKanji_2
        this.tenantNameKana = tenantNameKana
        this.transferVerificationKana1 = transferVerificationKana1
        this.transferVerificationKana2 = transferVerificationKana2
        this.headquartersNameKanji = headquartersNameKanji
        this.headquartersNameKana = headquartersNameKana
        this.searchKana = searchKana
        this.personalCorporateDivision = personalCorporateDivision
        this.corporateStatusDivision = corporateStatusDivision
        this.corporatePositionDivision = corporatePositionDivision
        this.representativeNameKanji = representativeNameKanji
        this.representativeNameKana = representativeNameKana
        this.representativeSearchKana = representativeSearchKana
        this.tenantDepartment = tenantDepartment
        this.tenantContactName = tenantContactName
        this.postalCode = postalCode
        this.prefectureCode = prefectureCode
        this.cityCode = cityCode
        this.townCode = townCode
        this.addressDetail = addressDetail
        this.buildingName = buildingName
        this.phoneNumber = phoneNumber
        this.faxNumber = faxNumber
        this.postalCode_2 = postalCode_2
        this.prefectureCode_2 = prefectureCode_2
        this.cityCode_2 = cityCode_2
        this.townCode_2 = townCode_2
        this.addressDetail_2 = addressDetail_2
        this.buildingName_2 = buildingName_2
        this.phoneNumber_2 = phoneNumber_2
        this.faxNumber_2 = faxNumber_2
        this.industryCode = industryCode
        this.birthDate = birthDate
        this.workplaceName = workplaceName
        this.workplacePrefectureCode = workplacePrefectureCode
        this.workplaceCityCode = workplaceCityCode
        this.workplaceTownCode = workplaceTownCode
        this.workplaceAddressDetail = workplaceAddressDetail
        this.workplaceBuildingName = workplaceBuildingName
        this.workplaceIndustryCode = workplaceIndustryCode
        this.workplacePhoneNumber = workplacePhoneNumber
        this.violationHistorySign = violationHistorySign
        this.nonManagedPropertySign = nonManagedPropertySign
        this.interfaceSign = interfaceSign
        this.corporateHousingAgency = corporateHousingAgency
        this.yearsOfService = yearsOfService
        this.industryPersonal = industryPersonal
        this.personalAnnualIncome = personalAnnualIncome
        this.cohabitantAnnualIncome = cohabitantAnnualIncome
        this.gender = gender
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised TenantRecord
     */
    constructor(value: TenantPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updateProgramId = value.updateProgramId
            this.updater = value.updater
            this.logicalDeleteSign = value.logicalDeleteSign
            this.tenantCode = value.tenantCode
            this.integratedClientCode = value.integratedClientCode
            this.tenantNameKanji = value.tenantNameKanji
            this.tenantNameKanji_2 = value.tenantNameKanji_2
            this.tenantNameKana = value.tenantNameKana
            this.transferVerificationKana1 = value.transferVerificationKana1
            this.transferVerificationKana2 = value.transferVerificationKana2
            this.headquartersNameKanji = value.headquartersNameKanji
            this.headquartersNameKana = value.headquartersNameKana
            this.searchKana = value.searchKana
            this.personalCorporateDivision = value.personalCorporateDivision
            this.corporateStatusDivision = value.corporateStatusDivision
            this.corporatePositionDivision = value.corporatePositionDivision
            this.representativeNameKanji = value.representativeNameKanji
            this.representativeNameKana = value.representativeNameKana
            this.representativeSearchKana = value.representativeSearchKana
            this.tenantDepartment = value.tenantDepartment
            this.tenantContactName = value.tenantContactName
            this.postalCode = value.postalCode
            this.prefectureCode = value.prefectureCode
            this.cityCode = value.cityCode
            this.townCode = value.townCode
            this.addressDetail = value.addressDetail
            this.buildingName = value.buildingName
            this.phoneNumber = value.phoneNumber
            this.faxNumber = value.faxNumber
            this.postalCode_2 = value.postalCode_2
            this.prefectureCode_2 = value.prefectureCode_2
            this.cityCode_2 = value.cityCode_2
            this.townCode_2 = value.townCode_2
            this.addressDetail_2 = value.addressDetail_2
            this.buildingName_2 = value.buildingName_2
            this.phoneNumber_2 = value.phoneNumber_2
            this.faxNumber_2 = value.faxNumber_2
            this.industryCode = value.industryCode
            this.birthDate = value.birthDate
            this.workplaceName = value.workplaceName
            this.workplacePrefectureCode = value.workplacePrefectureCode
            this.workplaceCityCode = value.workplaceCityCode
            this.workplaceTownCode = value.workplaceTownCode
            this.workplaceAddressDetail = value.workplaceAddressDetail
            this.workplaceBuildingName = value.workplaceBuildingName
            this.workplaceIndustryCode = value.workplaceIndustryCode
            this.workplacePhoneNumber = value.workplacePhoneNumber
            this.violationHistorySign = value.violationHistorySign
            this.nonManagedPropertySign = value.nonManagedPropertySign
            this.interfaceSign = value.interfaceSign
            this.corporateHousingAgency = value.corporateHousingAgency
            this.yearsOfService = value.yearsOfService
            this.industryPersonal = value.industryPersonal
            this.personalAnnualIncome = value.personalAnnualIncome
            this.cohabitantAnnualIncome = value.cohabitantAnnualIncome
            this.gender = value.gender
            resetChangedOnNotNull()
        }
    }
}
