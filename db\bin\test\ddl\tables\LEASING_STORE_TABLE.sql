-- TABLE: LEASING_STORE_TABLE(リーシング店舗対応表)

CREATE TABLE LEASING_STORE_TABLE(
     CREATION_DATE                                numeric(10,0)                 
,    CREATION_TIME                                numeric(8,0)                  
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(10,0)                 
,    UPDATE_TIME                                  numeric(8,0)                  
,    UPDATER                                      varchar(10)                   
,    BRANCH_CD                                    varchar(3)                    
,    BRANCH_NAME                                  varchar(42)                   
,    LEASING_STORE_CD                             varchar(3)                    
,    LEASING_STORE_NAME                           varchar(42)                   
,    START_DATE                                   numeric(10,0)                 
,    END_DATE                                     numeric(10,0)                 
,    LEASING_STORE_COMPANY_NAME                   varchar(42)                   
,    LEASING_STORE_PHONE_NUMBER                   varchar(15)                   
,    STORE_NORTH_ORDER                            numeric(3)                    
) TABLESPACE :TS_TBL;

COMMENT ON TABLE LEASING_STORE_TABLE IS 'リーシング店舗対応表 既存システム物理名: EMEBLP';
COMMENT ON COLUMN LEASING_STORE_TABLE.CREATION_DATE IS '作成年月日 既存システム物理名: EME01D "支店"を含まない';
COMMENT ON COLUMN LEASING_STORE_TABLE.CREATION_TIME IS '作成時刻 既存システム物理名: EME02H';
COMMENT ON COLUMN LEASING_STORE_TABLE.CREATOR IS '作成者 既存システム物理名: EME03C';
COMMENT ON COLUMN LEASING_STORE_TABLE.UPDATE_DATE IS '更新年月日 既存システム物理名: EME04D';
COMMENT ON COLUMN LEASING_STORE_TABLE.UPDATE_TIME IS '更新時刻 既存システム物理名: EME05H';
COMMENT ON COLUMN LEASING_STORE_TABLE.UPDATER IS '更新者 既存システム物理名: EME06C';
COMMENT ON COLUMN LEASING_STORE_TABLE.BRANCH_CD IS '支店CD 既存システム物理名: EME07C';
COMMENT ON COLUMN LEASING_STORE_TABLE.BRANCH_NAME IS '支店名 既存システム物理名: EME08X';
COMMENT ON COLUMN LEASING_STORE_TABLE.LEASING_STORE_CD IS 'リーシング店舗CD 既存システム物理名: EME09C';
COMMENT ON COLUMN LEASING_STORE_TABLE.LEASING_STORE_NAME IS 'リーシング店舗名 既存システム物理名: EME10X';
COMMENT ON COLUMN LEASING_STORE_TABLE.START_DATE IS '使用開始日 既存システム物理名: EME11D';
COMMENT ON COLUMN LEASING_STORE_TABLE.END_DATE IS '使用終了日 既存システム物理名: EME12D';
COMMENT ON COLUMN LEASING_STORE_TABLE.LEASING_STORE_COMPANY_NAME IS 'リーシング店舗会社名 既存システム物理名: EME13X';
COMMENT ON COLUMN LEASING_STORE_TABLE.LEASING_STORE_PHONE_NUMBER IS 'リーシング店舗電話番号 既存システム物理名: EME14N';
COMMENT ON COLUMN LEASING_STORE_TABLE.STORE_NORTH_ORDER IS '店舗北順 既存システム物理名: EME15S';
