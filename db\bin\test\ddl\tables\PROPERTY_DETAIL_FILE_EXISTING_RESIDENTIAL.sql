-- TABLE: PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL(物件明細ファイル(既存居住用)商品管理Web)

CREATE TABLE PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LOGICAL_DELETE_SIGN                          numeric(1,0)                  
,    OCCURRENCE_MONTH_DIVISION                    varchar(1)                    
,    OCCURRENCE_MONTH                             numeric(6,0)                  
,    SALES_DEPT_CD                                varchar(2)                    
,    SALES_DEPT_NAME                              varchar(22)                   
,    SALES_DEPT_OUTPUT_ORDER_CD                   varchar(6)                    
,    BRANCH_CD                                    varchar(3)                    
,    BRANCH_NAME                                  varchar(14)                   
,    <PERSON><PERSON>CH_OUTPUT_ORDER_CD                       varchar(5)                    
,    PROCESS_DATE                                 numeric(8,0)                  
,    STATUS_NAME                                  varchar(6)                    
,    CUSTOMER_FLAG                                numeric(1,0)                  
,    CUSTOMER_REP_CD                              varchar(6)                    
,    CUSTOMER_REP_NAME                            varchar(26)                   
,    OCCUPANCY_FLAG                               numeric(1,0)                  
,    BUILDING_CODE                                varchar(9)                    
,    BUILDING_NAME                                varchar(84)                   
,    ADDRESS_CD                                   varchar(10)                   
,    LOCATION                                     varchar(258)                  
,    EXPECTED_COMPLETION_DATE                     numeric(8,0)                  
,    COMPLETION_DATE                              numeric(8,0)                  
,    ROOM_CODE                                    varchar(5)                    
,    ROOM_NUMBER                                  varchar(4)                    
,    LANDLORD_CD                                  varchar(10)                   
,    LANDLORD_NAME                                varchar(42)                   
,    BULK_LEASING_SIGN                            numeric(1,0)                  
,    CONTRACT_TYPE_NAME                           varchar(8)                    
,    ROOM_TYPE_CD                                 varchar(3)                    
,    ROOM_TYPE_NAME                               varchar(8)                    
,    LAYOUT_DIVISION                              varchar(2)                    
,    LAYOUT_NAME                                  varchar(6)                    
,    EXCLUSIVE_AREA                               numeric(7,2)                  
,    TENANT_CONTRACT_NUMBER                       varchar(8)                    
,    CURRENT_STATUS_DIVISION                      varchar(2)                    
,    MODIFIED_STATUS_DIVISION                     varchar(2)                    
,    VACATION_NOTICE_DATE                         numeric(8,0)                  
,    REVIEW_APPROVAL_DATE                         numeric(8,0)                  
,    REVIEW_APPROVAL_DAYS_ELAPSED                 numeric(5,0)                  
,    ARRANGEMENT_OUTPUT_DATE                      numeric(8,0)                  
,    ARRANGEMENT_APPROVAL_DATE                    numeric(8,0)                  
,    ARRANGEMENT_COLLECTION_DATE                  numeric(8,0)                  
,    ARRANGEMENT_COLLECTION_DAYS_ELAPSED          numeric(5,0)                  
,    MOVE_OUT_MEETING_DATE                        numeric(8,0)                  
,    EXPECTED_MOVE_OUT_DATE                       numeric(8,0)                  
,    MOVE_OUT_DATE                                numeric(8,0)                  
,    MOVE_OUT_RENT                                numeric(9,0)                  
,    REVIEW_RENT                                  numeric(9,0)                  
,    CURRENT_RENT                                 numeric(9,0)                  
,    DIFFERENCE_REVIEW_MOVE_OUT                   numeric(9,0)                  
,    DIFFERENCE_CURRENT_MOVE_OUT                  numeric(9,0)                  
,    DIFFERENCE_CURRENT_REVIEW                    numeric(9,0)                  
,    DISCREPANCY_STATUS                           varchar(4)                    
,    LONGEST_VACANCY_PERIOD_HISTORY               numeric(5,0)                  
,    RESIDENTIAL_ROOM_COUNT                       numeric(3,0)                  
,    RESIDENTIAL_ROOM_COUNT_VACANT                numeric(3,0)                  
,    RESTORATION_COMPLETION_EXPECTED_DATE         numeric(8,0)                  
,    RESTORATION_COMPLETION_DATE                  numeric(8,0)                  
,    VACANCY_ACCOUNTING_EXPECTED_DATE             numeric(8,0)                  
,    VACANCY_ACCOUNTING_DATE                      numeric(8,0)                  
,    VACANCY_MONTHS                               numeric(4,0)                  
,    VACANCY_PERIOD                               numeric(5,0)                  
,    OCCUPANCY_APPLICATION_DATE                   numeric(8,0)                  
,    CONTRACT_DATE                                numeric(8,0)                  
,    REMAINING_COLLECTION_EXPECTED_DATE           numeric(8,0)                  
,    REMAINING_COLLECTION_DATE                    numeric(8,0)                  
,    EXPECTED_OCCUPANCY_DATE                      numeric(8,0)                  
,    OCCUPANCY_DATE                               numeric(8,0)                  
,    AD                                           numeric(1,0)                  
,    FF                                           numeric(1,0)                  
,    SUPPORT_MECHANISM_DIVISION                   varchar(1)                    
,    PREFERRED_RENTAL_DIVISION                    varchar(1)                    
,    FINANCING_DIVISION                           varchar(18)                   
,    HL_APPROVAL_DIVISION                         varchar(2)                    
,    HL                                           varchar(8)                    
,    RENT_DISCOUNT_FLAG                           numeric(1,0)                  
,    RENT_DISCOUNT                                varchar(10)                   
,    REFORM_TYPE                                  varchar(2)                    
,    REFORM                                       numeric(1,0)                  
,    THREE_MONTH_FF                               numeric(1,0)                  
,    RECRUITMENT_STATUS_DIVISION                  varchar(2)                    
,    RESERVE_DIVISION_2                           varchar(2)                    
,    RESERVE_DIVISION_3                           varchar(2)                    
,    RESERVE_DIVISION_4                           varchar(2)                    
,    RESERVE_DIVISION_5                           varchar(2)                    
,    RESERVE_DIVISION_6                           varchar(2)                    
,    RESERVE_DIVISION_7                           varchar(2)                    
,    RESERVE_DIVISION_8                           varchar(2)                    
,    RESERVE_DIVISION_9                           varchar(2)                    
,    RESERVE_DIVISION_10                          varchar(2)                    
,    CONSTRUCTION_REP_CD                          varchar(6)                    
,    CONSTRUCTION_REP_NAME                        varchar(26)                   
,    IMPLEMENTATION_REP_CD                        varchar(6)                    
,    IMPLEMENTATION_REP_NAME                      varchar(26)                   
,    AD_ACTUAL_AMOUNT                             numeric(9,0)                  
,    FF_ACTUAL_AMOUNT                             numeric(9,0)                  
,    MUNICIPALITY_CD                              varchar(5)                    
,    MUNICIPALITY_NAME                            varchar(32)                   
,    BUSINESS_ROOM_COUNT_VACANT                   numeric(3,0)                  
,    LONGEST_VACANCY_PERIOD_BUSINESS              numeric(5,0)                  
,    BUSINESS_ROOM_COUNT                          numeric(3,0)                  
,    STATUS_CODE                                  varchar(2)                    
,    EXCLUSION_FLAG                               numeric(1,0)                  
,    COLLECTION_REP_CD                            varchar(6)                    
,    COLLECTION_REP_NAME                          varchar(26)                   
,    ABC_DIVISION                                 varchar(1)                    
,    MOVE_OUT_MEETING_TIME                        numeric(4,0)                  
,    MANAGEMENT_REP_CD                            varchar(6)                    
,    MANAGEMENT_REP_NAME                          varchar(26)                   
,    SUPPLY_PLAN_AREA_CODE                        varchar(4)                    
,    SUPPLY_PLAN_AREA_NAME                        varchar(32)                   
,    ASSESSMENT_AREA                              varchar(5)                    
,    STORE_SALES_DEPT_CD                          varchar(2)                    
,    STORE_SALES_DEPT_NAME                        varchar(22)                   
,    STORE_SALES_DEPT_OUTPUT_ORDER_CD             varchar(6)                    
,    STORE_CD                                     varchar(3)                    
,    STORE_NAME                                   varchar(18)                   
,    STORE_OUTPUT_ORDER_CD                        varchar(5)                    
,    CONSTRAINT UQ_PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL UNIQUE (BUILDING_CODE, ROOM_CODE, LOGICAL_DELETE_SIGN)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL IS '物件明細ファイル(既存居住用)商品管理Web 既存システム物理名: EMUU1P';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.CREATION_DATE IS '作成年月日 既存システム物理名: EMU01D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.CREATION_TIME IS '作成時刻 既存システム物理名: EMU02H';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.UPDATE_DATE IS '更新年月日 既存システム物理名: EMU03D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.UPDATE_TIME IS '更新時刻 既存システム物理名: EMU04H';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EMU05N';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.UPDATER IS '更新者 既存システム物理名: EMU06C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: EMU07S';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.OCCURRENCE_MONTH_DIVISION IS '発生月区分 既存システム物理名: EMU08B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.OCCURRENCE_MONTH IS '発生月 既存システム物理名: EMU09D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.SALES_DEPT_CD IS '営業部CD 既存システム物理名: EMU10C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.SALES_DEPT_NAME IS '営業部名称 既存システム物理名: EMU11M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.SALES_DEPT_OUTPUT_ORDER_CD IS '営業部出力順CD 既存システム物理名: EMU12C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.BRANCH_CD IS '支店CD 既存システム物理名: EMU13C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.BRANCH_NAME IS '支店名称 既存システム物理名: EMU14M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.BRANCH_OUTPUT_ORDER_CD IS '支店出力順CD 既存システム物理名: EMU15C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.PROCESS_DATE IS '処理日 既存システム物理名: EMU16D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.STATUS_NAME IS '状態名称 既存システム物理名: EMU17M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.CUSTOMER_FLAG IS '客付フラグ 既存システム物理名: EMU18S';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.CUSTOMER_REP_CD IS '客付担当者CD 既存システム物理名: EMU19C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.CUSTOMER_REP_NAME IS '客付担当者名 既存システム物理名: EMU20M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.OCCUPANCY_FLAG IS '入居フラグ 既存システム物理名: EMU21S';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.BUILDING_CODE IS '建物コード 既存システム物理名: EMUABC';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.BUILDING_NAME IS '建物名称 既存システム物理名: EMU22M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.ADDRESS_CD IS '住所CD 既存システム物理名: EMU23C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.LOCATION IS '所在地 既存システム物理名: EMU24J';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.EXPECTED_COMPLETION_DATE IS '完工予定日 既存システム物理名: EMU25D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.COMPLETION_DATE IS '完工日 既存システム物理名: EMU26D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.ROOM_CODE IS '部屋コード 既存システム物理名: EMUACC';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.ROOM_NUMBER IS '部屋番号 既存システム物理名: EMU27N';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.LANDLORD_CD IS '家主CD 既存システム物理名: EMU28C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.LANDLORD_NAME IS '家主名 既存システム物理名: EMU29M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.BULK_LEASING_SIGN IS '一括借上サイン 既存システム物理名: EMU30S';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.CONTRACT_TYPE_NAME IS '契約形態名称 既存システム物理名: EMU31M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.ROOM_TYPE_CD IS '部屋種別CD 既存システム物理名: EMU32C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.ROOM_TYPE_NAME IS '部屋種別名称 既存システム物理名: EMU33M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.LAYOUT_DIVISION IS '間取区分 既存システム物理名: EMU34B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.LAYOUT_NAME IS '間取名称 既存システム物理名: EMU35M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.EXCLUSIVE_AREA IS '専有面積 既存システム物理名: EMU36Q';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.TENANT_CONTRACT_NUMBER IS 'テナント契約番号 既存システム物理名: EMUAKN';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.CURRENT_STATUS_DIVISION IS '現在状態区分 既存システム物理名: EMU37B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.MODIFIED_STATUS_DIVISION IS '修正状態区分 既存システム物理名: EMU38B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.VACATION_NOTICE_DATE IS '明渡通告日 既存システム物理名: EMU39D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.REVIEW_APPROVAL_DATE IS '審査決裁日 既存システム物理名: EMU40D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.REVIEW_APPROVAL_DAYS_ELAPSED IS '審査決裁経過日数 既存システム物理名: EMU41Q';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.ARRANGEMENT_OUTPUT_DATE IS '斡旋出力日 既存システム物理名: EMU42D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.ARRANGEMENT_APPROVAL_DATE IS '斡旋承認日 既存システム物理名: EMU43D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.ARRANGEMENT_COLLECTION_DATE IS '斡旋回収日 既存システム物理名: EMU44D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.ARRANGEMENT_COLLECTION_DAYS_ELAPSED IS '斡旋回収経過日数 既存システム物理名: EMU45Q';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.MOVE_OUT_MEETING_DATE IS '退居立会予定日 既存システム物理名: EMU46D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.EXPECTED_MOVE_OUT_DATE IS '退居予定日 既存システム物理名: EMU47D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.MOVE_OUT_DATE IS '退居日 既存システム物理名: EMU48D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.MOVE_OUT_RENT IS '退居時家賃 既存システム物理名: EMU49A';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.REVIEW_RENT IS '審査家賃 既存システム物理名: EMU50A';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.CURRENT_RENT IS '現募集家賃 既存システム物理名: EMU51A';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.DIFFERENCE_REVIEW_MOVE_OUT IS '差額(審査－退居) 既存システム物理名: EMU52A';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.DIFFERENCE_CURRENT_MOVE_OUT IS '差額(募集－退居) 既存システム物理名: EMU53A';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.DIFFERENCE_CURRENT_REVIEW IS '差額(募集－審査) 既存システム物理名: EMU54A';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.DISCREPANCY_STATUS IS '乖離状況 既存システム物理名: EMU55X';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.LONGEST_VACANCY_PERIOD_HISTORY IS '最長空家期間履歴 既存システム物理名: EMU56L';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESIDENTIAL_ROOM_COUNT IS '居住用部屋数 既存システム物理名: EMU57Q';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESIDENTIAL_ROOM_COUNT_VACANT IS '居住用部屋数(空) 既存システム物理名: EMU58Q';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESTORATION_COMPLETION_EXPECTED_DATE IS '原状回復完了予定日 既存システム物理名: EMU59D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESTORATION_COMPLETION_DATE IS '原状回復完了日 既存システム物理名: EMU60D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.VACANCY_ACCOUNTING_EXPECTED_DATE IS '空家計上予定日 既存システム物理名: EMU61D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.VACANCY_ACCOUNTING_DATE IS '空家計上日 既存システム物理名: EMU62D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.VACANCY_MONTHS IS '空家月数 既存システム物理名: EMU63Q';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.VACANCY_PERIOD IS '空家期間 既存システム物理名: EMU64L';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.OCCUPANCY_APPLICATION_DATE IS '入居申込日 既存システム物理名: EMU65D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.CONTRACT_DATE IS '契約日 既存システム物理名: EMU66D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.REMAINING_COLLECTION_EXPECTED_DATE IS '残集予定日 既存システム物理名: EMU67D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.REMAINING_COLLECTION_DATE IS '残集日 既存システム物理名: EMU68D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.EXPECTED_OCCUPANCY_DATE IS '入居予定日 既存システム物理名: EMU69D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.OCCUPANCY_DATE IS '入居日 既存システム物理名: EMU70D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.AD IS 'AD 既存システム物理名: EMU71S';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.FF IS 'FF 既存システム物理名: EMU72S';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.SUPPORT_MECHANISM_DIVISION IS '支援機構区分 既存システム物理名: EMU73B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.PREFERRED_RENTAL_DIVISION IS '特優賃区分 既存システム物理名: EMU74B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.FINANCING_DIVISION IS '融資区分 既存システム物理名: EMU75X';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.HL_APPROVAL_DIVISION IS 'HL承諾区分 既存システム物理名: EMU76B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.HL IS 'HL 既存システム物理名: EMU77X';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RENT_DISCOUNT_FLAG IS '家賃割引フラグ 既存システム物理名: EMU78S';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RENT_DISCOUNT IS '家賃割引 既存システム物理名: EMU79X';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.REFORM_TYPE IS 'リフォーム種別 既存システム物理名: EMU80B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.REFORM IS 'リフォーム 既存システム物理名: EMU81S';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.THREE_MONTH_FF IS '3ヶ月FF 既存システム物理名: EMU82S';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RECRUITMENT_STATUS_DIVISION IS '募集状態区分 既存システム物理名: EMU91B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESERVE_DIVISION_2 IS '予備区分2 既存システム物理名: EMU92B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESERVE_DIVISION_3 IS '予備区分3 既存システム物理名: EMU93B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESERVE_DIVISION_4 IS '予備区分4 既存システム物理名: EMU94B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESERVE_DIVISION_5 IS '予備区分5 既存システム物理名: EMU95B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESERVE_DIVISION_6 IS '予備区分6 既存システム物理名: EMU96B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESERVE_DIVISION_7 IS '予備区分7 既存システム物理名: EMU97B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESERVE_DIVISION_8 IS '予備区分8 既存システム物理名: EMU98B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESERVE_DIVISION_9 IS '予備区分9 既存システム物理名: EMU99B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.RESERVE_DIVISION_10 IS '予備区分10 既存システム物理名: EMUA0B';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.CONSTRUCTION_REP_CD IS '工事担当者CD 既存システム物理名: EMUA1C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.CONSTRUCTION_REP_NAME IS '工事担当者名 既存システム物理名: EMUA2M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.IMPLEMENTATION_REP_CD IS '施工担当者CD 既存システム物理名: EMUA3C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.IMPLEMENTATION_REP_NAME IS '施工担当者名 既存システム物理名: EMUA4M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.AD_ACTUAL_AMOUNT IS 'AD実績額 既存システム物理名: EMUA5A';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.FF_ACTUAL_AMOUNT IS 'FF実績額 既存システム物理名: EMUA6A';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.MUNICIPALITY_CD IS '自治体CD 既存システム物理名: EMUA7C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.MUNICIPALITY_NAME IS '自治体名称 既存システム物理名: EMUA8M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.BUSINESS_ROOM_COUNT_VACANT IS '事業用部屋数(空) 既存システム物理名: EMUA9Q';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.LONGEST_VACANCY_PERIOD_BUSINESS IS '最長空家期間(事業 既存システム物理名: EMUB0L';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.BUSINESS_ROOM_COUNT IS '事業用部屋数 既存システム物理名: EMUB1Q';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.STATUS_CODE IS '状態コード 既存システム物理名: EMUJYC';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.EXCLUSION_FLAG IS '除外フラグ 既存システム物理名: EMUJGF';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.COLLECTION_REP_CD IS '回収担当者CD 既存システム物理名: EMUB2C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.COLLECTION_REP_NAME IS '回収担当者名 既存システム物理名: EMUB3M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.ABC_DIVISION IS 'ABC区分 既存システム物理名: EMUABB';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.MOVE_OUT_MEETING_TIME IS '退居立会予定時間 既存システム物理名: EMUB8D';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.MANAGEMENT_REP_CD IS '管理担当者CD 既存システム物理名: EMUB9C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.MANAGEMENT_REP_NAME IS '管理担当者名 既存システム物理名: EMUB9M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.SUPPLY_PLAN_AREA_CODE IS '供給計画地域コード 既存システム物理名: EMUC0C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.SUPPLY_PLAN_AREA_NAME IS '供給計画地域名称 既存システム物理名: EMUC0M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.ASSESSMENT_AREA IS '査定エリア 既存システム物理名: EMUC1C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.STORE_SALES_DEPT_CD IS '店舗営業部CD 既存システム物理名: EMUC2C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.STORE_SALES_DEPT_NAME IS '店舗営業部名称 既存システム物理名: EMUC3M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.STORE_SALES_DEPT_OUTPUT_ORDER_CD IS '店舗営業部出力順CD 既存システム物理名: EMUC4C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.STORE_CD IS '店舗CD 既存システム物理名: EMUC5C';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.STORE_NAME IS '店舗名称 既存システム物理名: EMUC6M';
COMMENT ON COLUMN PROPERTY_DETAIL_FILE_EXISTING_RESIDENTIAL.STORE_OUTPUT_ORDER_CD IS '店舗出力順CD 既存システム物理名: EMUC7C';
