package jp.ne.simplex.mock

import jp.ne.simplex.application.batch.BatchType
import jp.ne.simplex.application.repository.db.BatchExecuteHistoryRepositoryInterface
import java.time.LocalDateTime

class MockBatchExecuteHistoryRepository(private val registerFunc: (batchType: BatchType, executeDateTime: LocalDateTime) -> Unit = { _, _ -> }) :
    BatchExecuteHistoryRepositoryInterface {
    override fun register(batchType: BatchType, executeDateTime: LocalDateTime) {
        registerFunc(batchType, executeDateTime)
    }

    override fun upsert(batchType: BatchType, executeDateTime: LocalDateTime) {
        
    }
}
