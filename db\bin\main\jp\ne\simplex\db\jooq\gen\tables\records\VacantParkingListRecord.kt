/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.VacantParkingListTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.VacantParkingListPojo

import org.jooq.impl.TableRecordImpl


/**
 * 空き駐車場一覧 既存システム物理名: EMPRKP
 */
@Suppress("UNCHECKED_CAST")
open class VacantParkingListRecord private constructor() : TableRecordImpl<VacantParkingListRecord>(VacantParkingListTable.VACANT_PARKING_LIST) {

    open var creationDate: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var creationTime: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var creator: String?
        set(value): Unit = set(2, value)
        get(): String? = get(2) as String?

    open var updateDate: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var updateTime: Int?
        set(value): Unit = set(4, value)
        get(): Int? = get(4) as Int?

    open var updater: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var buildingCd: String?
        set(value): Unit = set(6, value)
        get(): String? = get(6) as String?

    open var parkingCd: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var parkingNo: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    /**
     * Create a detached, initialised VacantParkingListRecord
     */
    constructor(creationDate: Int? = null, creationTime: Int? = null, creator: String? = null, updateDate: Int? = null, updateTime: Int? = null, updater: String? = null, buildingCd: String? = null, parkingCd: String? = null, parkingNo: String? = null): this() {
        this.creationDate = creationDate
        this.creationTime = creationTime
        this.creator = creator
        this.updateDate = updateDate
        this.updateTime = updateTime
        this.updater = updater
        this.buildingCd = buildingCd
        this.parkingCd = parkingCd
        this.parkingNo = parkingNo
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised VacantParkingListRecord
     */
    constructor(value: VacantParkingListPojo?): this() {
        if (value != null) {
            this.creationDate = value.creationDate
            this.creationTime = value.creationTime
            this.creator = value.creator
            this.updateDate = value.updateDate
            this.updateTime = value.updateTime
            this.updater = value.updater
            this.buildingCd = value.buildingCd
            this.parkingCd = value.parkingCd
            this.parkingNo = value.parkingNo
            resetChangedOnNotNull()
        }
    }
}
