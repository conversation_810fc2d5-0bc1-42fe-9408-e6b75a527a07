-- TABLE: CORE_PRODUCT_LINK_MASTER(基幹商品紐付けマスタ)

CREATE TABLE CORE_PRODUCT_LINK_MASTER(
     CREATION_DATE                                numeric(8)                    
,    CREATION_TIME                                numeric(6)                    
,    CREATOR                                      varchar(10)                   
,    UPDATE_DATE                                  numeric(8)                    
,    UPDATE_TIME                                  numeric(6)                    
,    UPDATER                                      varchar(10)                   
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    LO<PERSON>CAL_DELETE_SIGN                          numeric(1)                    
,    BUILDING_CD                                  varchar(9)                    
,    ROOM_CD                                      varchar(5)                    
,    CORE_PRODUCT_ID                              numeric(8)                    
,    LAYER1_TYPE_ID                               numeric(8)                    
,    LAYER2_TYPE_ID                               numeric(8)                    
,    LAYER3_TYPE_ID                               numeric(8)                    
,    LAYER4_TYPE_ID                               numeric(8)                    
,    LAYER5_TYPE_ID                               numeric(8)                    
,    LAYER6_TYPE_ID                               numeric(8)                    
,    LAYER7_TYPE_ID                               numeric(8)                    
,    LAYER8_TYPE_ID                               numeric(8)                    
,    LAYER9_TYPE_ID                               numeric(8)                    
,    EXTERIOR_IMAGE_ID                            numeric(8)                    
,    FLOOR_PLAN_IMAGE_ID                          numeric(8)                    
,    INTERIOR_IMAGE_ID1                           numeric(8)                    
,    INTERIOR_IMAGE_ID2                           numeric(8)                    
,    INTERIOR_IMAGE_ID3                           numeric(8)                    
,    INTERIOR_IMAGE_ID4                           numeric(8)                    
,    INTERIOR_IMAGE_ID5                           numeric(8)                    
,    INTERIOR_IMAGE_ID6                           numeric(8)                    
,    INTERIOR_IMAGE_ID7                           numeric(8)                    
,    INTERIOR_IMAGE_ID8                           numeric(8)                    
,    INTERIOR_IMAGE_ID9                           numeric(8)                    
,    INTERIOR_IMAGE_ID10                          numeric(8)                    
,    INTERIOR_IMAGE_ID11                          numeric(8)                    
,    INTERIOR_IMAGE_ID12                          numeric(8)                    
,    CORE_PRODUCT_NAME                            varchar(202)                  
) TABLESPACE :TS_TBL;

COMMENT ON TABLE CORE_PRODUCT_LINK_MASTER IS '基幹商品紐付けマスタ 既存システム物理名: ERA20P';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: A2001D @290';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: A2002H';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.CREATOR IS '作成者 既存システム物理名: A2003C';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: A2004D';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: A2005H';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.UPDATER IS '更新者 既存システム物理名: A2006C';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: A2007C';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.LOGICAL_DELETE_SIGN IS '論理削除サイン 既存システム物理名: A2008S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.BUILDING_CD IS '建物CD 既存システム物理名: A2009C';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.ROOM_CD IS '部屋CD 既存システム物理名: A2010C';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.CORE_PRODUCT_ID IS '基幹商品ID 既存システム物理名: A2011S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.LAYER1_TYPE_ID IS '1層目タイプID 既存システム物理名: A2012S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.LAYER2_TYPE_ID IS '2層目タイプID 既存システム物理名: A2013S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.LAYER3_TYPE_ID IS '3層目タイプID 既存システム物理名: A2014S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.LAYER4_TYPE_ID IS '4層目タイプID 既存システム物理名: A2015S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.LAYER5_TYPE_ID IS '5層目タイプID 既存システム物理名: A2016S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.LAYER6_TYPE_ID IS '6層目タイプID 既存システム物理名: A2017S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.LAYER7_TYPE_ID IS '7層目タイプID 既存システム物理名: A2018S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.LAYER8_TYPE_ID IS '8層目タイプID 既存システム物理名: A2019S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.LAYER9_TYPE_ID IS '9層目タイプID 既存システム物理名: A2020S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.EXTERIOR_IMAGE_ID IS '外観画像ID 既存システム物理名: A2021S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.FLOOR_PLAN_IMAGE_ID IS '間取画像ID 既存システム物理名: A2022S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.INTERIOR_IMAGE_ID1 IS '内観画像ID1 既存システム物理名: A2023S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.INTERIOR_IMAGE_ID2 IS '内観画像ID2 既存システム物理名: A2024S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.INTERIOR_IMAGE_ID3 IS '内観画像ID3 既存システム物理名: A2025S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.INTERIOR_IMAGE_ID4 IS '内観画像ID4 既存システム物理名: A2026S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.INTERIOR_IMAGE_ID5 IS '内観画像ID5 既存システム物理名: A2027S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.INTERIOR_IMAGE_ID6 IS '内観画像ID6 既存システム物理名: A2028S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.INTERIOR_IMAGE_ID7 IS '内観画像ID7 既存システム物理名: A2029S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.INTERIOR_IMAGE_ID8 IS '内観画像ID8 既存システム物理名: A2030S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.INTERIOR_IMAGE_ID9 IS '内観画像ID9 既存システム物理名: A2031S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.INTERIOR_IMAGE_ID10 IS '内観画像ID10 既存システム物理名: A2032S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.INTERIOR_IMAGE_ID11 IS '内観画像ID11 既存システム物理名: A2033S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.INTERIOR_IMAGE_ID12 IS '内観画像ID12 既存システム物理名: A2034S';
COMMENT ON COLUMN CORE_PRODUCT_LINK_MASTER.CORE_PRODUCT_NAME IS '基幹商品名 既存システム物理名: A2035M';
