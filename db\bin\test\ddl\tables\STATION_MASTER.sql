-- TABLE: STATION_MASTER(駅マスタ)

CREATE TABLE STATION_MASTER(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATE_PROGRAM_ID                            varchar(10)                   
,    UPDATER                                      varchar(10)                   
,    LINE_CODE                                    varchar(4)        NOT NULL    
,    STATION_CODE                                 varchar(4)        NOT NULL    
,    LINE_NAME                                    varchar(20)                   
,    LINE_SHORT_NAME                              varchar(12)                   
,    STATION_NAME                                 varchar(22)                   
,    STATION_SHORT_NAME                           varchar(12)                   
,    TRANSFER_STATION_BLOCK_CODE                  varchar(1)                    
,    TRANSFER_STATION_CODE                        varchar(3)                    
,    START_YEAR_MONTH                             numeric(6,0)                  
,    END_YEAR_MONTH                               numeric(6,0)                  
,    BLOCK_CODE                                   varchar(1)                    
,    LINE_CODE_3_DIGIT                            varchar(3)                    
,    CONSTRAINT PK_STATION_MASTER PRIMARY KEY (LINE_CODE, STATION_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE STATION_MASTER IS '駅マスタ 既存システム物理名: EZF61P';
COMMENT ON COLUMN STATION_MASTER.CREATION_DATE IS '作成年月日 既存システム物理名: EZF01D';
COMMENT ON COLUMN STATION_MASTER.CREATION_TIME IS '作成時刻 既存システム物理名: EZF02H';
COMMENT ON COLUMN STATION_MASTER.UPDATE_DATE IS '更新年月日 既存システム物理名: EZF03D';
COMMENT ON COLUMN STATION_MASTER.UPDATE_TIME IS '更新時刻 既存システム物理名: EZF04H';
COMMENT ON COLUMN STATION_MASTER.UPDATE_PROGRAM_ID IS '更新プログラムID 既存システム物理名: EZF05N';
COMMENT ON COLUMN STATION_MASTER.UPDATER IS '更新者 既存システム物理名: EZF06C';
COMMENT ON COLUMN STATION_MASTER.LINE_CODE IS '沿線コード 既存システム物理名: EZFAPC';
COMMENT ON COLUMN STATION_MASTER.STATION_CODE IS '駅コード 既存システム物理名: EZFAQC';
COMMENT ON COLUMN STATION_MASTER.LINE_NAME IS '沿線名 既存システム物理名: EZF11M';
COMMENT ON COLUMN STATION_MASTER.LINE_SHORT_NAME IS '沿線略称名 既存システム物理名: EZF13M';
COMMENT ON COLUMN STATION_MASTER.STATION_NAME IS '駅名 既存システム物理名: EZF12M';
COMMENT ON COLUMN STATION_MASTER.STATION_SHORT_NAME IS '駅略称名 既存システム物理名: EZF14M';
COMMENT ON COLUMN STATION_MASTER.TRANSFER_STATION_BLOCK_CODE IS '連絡駅ブロックコード 既存システム物理名: EZF15C';
COMMENT ON COLUMN STATION_MASTER.TRANSFER_STATION_CODE IS '連絡駅コード 既存システム物理名: EZF16C';
COMMENT ON COLUMN STATION_MASTER.START_YEAR_MONTH IS '開始年月 既存システム物理名: EZF17D';
COMMENT ON COLUMN STATION_MASTER.END_YEAR_MONTH IS '終了年月 既存システム物理名: EZF18D';
COMMENT ON COLUMN STATION_MASTER.BLOCK_CODE IS 'ブロックコード 既存システム物理名: EZF19C';
COMMENT ON COLUMN STATION_MASTER.LINE_CODE_3_DIGIT IS '沿線コード(3桁) 既存システム物理名: EZF20C';
