package jp.ne.simplex.mock

import jp.ne.simplex.application.repository.aws.S3RepositoryInterface
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import java.io.ByteArrayInputStream
import java.nio.file.Path

class MockS3Repository(
    val getLatestFileFunc: (bucketName: String) -> Unit = { _ -> }
) :
    S3RepositoryInterface {
    override fun uploadFile(localFilePath: Path, s3Key: String, bucketName: String): String {
        return "dummy path"
    }

    override fun getLatestFile(bucketName: String): ResponseInputStream<GetObjectResponse> {
        getLatestFileFunc(bucketName)
        val emptyContent = ByteArrayInputStream(ByteArray(0))
        val dummyResponse = GetObjectResponse.builder().build()
        return ResponseInputStream(dummyResponse, emptyContent)
    }
}
