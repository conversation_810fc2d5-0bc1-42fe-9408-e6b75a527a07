package jp.ne.simplex.application.service

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.ParkingReservation
import jp.ne.simplex.application.repository.db.*
import jp.ne.simplex.application.repository.external.dkportal.DKPortalRepositoryInterface
import jp.ne.simplex.application.repository.external.eboard.EboardRepositoryInterface
import jp.ne.simplex.application.repository.mail.MailRepository
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.db.jooq.gen.tables.references.PARKING_RESERVATION
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ServerValidationException
import jp.ne.simplex.mock.*
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.stub.*
import org.jooq.DSLContext
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.assertEquals

class ParkingReservationServiceTest : AbstractTestContainerTest() {

    private val currentDateTime = LocalDateTime.of(2025, 7, 3, 13, 45, 30)
    override fun beforeEach() {
        MockLocalDateTime.setNow(currentDateTime)
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PARKING_RESERVATION)
    }

    private fun parkingReservationService(
        reservationRepository: ParkingReservationRepositoryInterface = MockParkingReservationRepository(),
        parkingDetailsService: ParkingDetailsService = parkingService(),
        parkingRepository: ParkingRepositoryInterface = MockParkingRepository(),
        parkingEnableRepository: ParkingEnableRepositoryInterface = MockParkingEnableRepository(),
        eBoardRepository: EboardRepositoryInterface = MockEboardRepository(),
        parkingContractService: ParkingContractService = parkingContractService(),
        parkingReservationMailService: ParkingReservationMailService = parkingReservationMailService(),
    ): ParkingReservationService {
        return ParkingReservationService(
            reservationRepository = reservationRepository,
            parkingDetailsService = parkingDetailsService,
            parkingRepository = parkingRepository,
            parkingEnableRepository = parkingEnableRepository,
            parkingContractService = parkingContractService,
            eBoardRepository = eBoardRepository,
            mailService = parkingReservationMailService
        )
    }

    private fun parkingContractService(
        parkingContractPossibilityRepository: ParkingContractPossibilityRepositoryInterface
        = MockParkingContractPossibilityRepository(),
        parkingDetailsService: ParkingDetailsService = parkingService(),
        parkingRepository: ParkingRepositoryInterface = MockParkingRepository(),
        dkPortalRepository: DKPortalRepositoryInterface = MockDKPortalRepository(),
    ): ParkingContractService {
        return ParkingContractService(
            context = dslContext,
            parkingContractPossibilityRepository = parkingContractPossibilityRepository,
            parkingDetailsService = parkingDetailsService,
            parkingRepository = parkingRepository,
            dkPortalRepository = dkPortalRepository,
        )
    }

    private fun parkingService(
        parkingRepository: ParkingRepositoryInterface = MockParkingRepository(),
        parkingDetailsRepository: ParkingDetailsRepositoryInterface = MockParkingDetailsRepository(),
        vacantParkingListRepository: VacantParkingListRepositoryInterface = MockVacantParkingListRepository()
    ): ParkingDetailsService {
        return ParkingDetailsService(
            parkingRepository = parkingRepository,
            parkingDetailsRepository = parkingDetailsRepository,
            vacantParkingListRepository = vacantParkingListRepository
        )
    }

    private fun parkingReservationMailService(
        emailAddressMasterRepository: EmailAddressRepositoryInterface = MockEmailAddressRepository(),
        officeBranchMappingRepository: OfficeBranchMappingRepositoryInterface = MockOfficeBranchMappingRepository(),
        buildingRepository: BuildingMasterRepositoryInterface = MockBuildingMasterRepository(),
        branchRepository: BranchRepositoryInterface = MockBranchRepository(),
        mailRepository: MailRepository = MockMailRepository(),
        parkingRepository: ParkingRepositoryInterface = MockParkingRepository(),
    ): ParkingReservationMailService {
        return ParkingReservationMailService(
            emailAddressRepository = emailAddressMasterRepository,
            officeBranchMappingRepository = officeBranchMappingRepository,
            buildingRepository = buildingRepository,
            branchRepository = branchRepository,
            mailRepository = mailRepository,
            receptAddress = "<EMAIL>",
            inputName = "いい物件ボード",
            isSendMail = true,
            parkingRepository = parkingRepository,
        )
    }

    @Nested
    @DisplayName("駐車場予約情報新規登録の際、複数予約の組み合わせ条件のバリデーションが期待通りに行えていることの検証")
    inner class Scenario1 {

        @Nested
        @DisplayName("駐車場が存在しないケース")
        inner class Scenario1x1 {

            @Test
            @DisplayName("駐車場が存在しない場合は予約不可")
            fun case1() {

                val registerApplication = stubRegisterParkingReservation(
                    reserveStartDatetime = LocalDate.now().atTime(LocalTime.MIN),
                    reservationType = ParkingReservation.Type.AUTO_APPLICATION,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )

                val err = assertThrows<ServerValidationException> {
                    parkingReservationService(
                        parkingRepository = MockParkingRepository(
                            isParkingExistFunc = { _ -> false }
                        )
                    ).updateReservation(stubJwtAuthInfo(), registerApplication)
                }

                assertEquals(
                    err.detail.message,
                    ErrorMessage.PARKING_DOES_NOT_EXIST.format().message
                )
            }
        }

        @Nested
        @DisplayName("予約がない駐車場に予約するケース")
        inner class Scenario1x2 {

            @Test
            @DisplayName("契約されていない場合、場所変更以外で予約可能")
            fun case1() {

                // 申込
                val registerApplication = stubRegisterParkingReservation(
                    reserveStartDatetime = LocalDate.now().atTime(LocalTime.MIN),
                    reservationType = ParkingReservation.Type.AUTO_APPLICATION,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )

                assertDoesNotThrow {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findActiveReservationsFunc = { _, _ -> emptyList() }
                        ),
                    ).updateReservation(stubJwtAuthInfo(), registerApplication)
                }
            }

            @Test
            @DisplayName("契約されていない場合、場所変更で予約不可")
            fun case2() {

                // 場所変更
                val registerReplace = stubRegisterParkingReservation(
                    reservationType = ParkingReservation.Type.REPLACE,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )

                val err = assertThrows<ServerValidationException> {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findActiveReservationsFunc = { _, _ -> emptyList() }
                        ),
                    ).updateReservation(stubJwtAuthInfo(), registerReplace)
                }
                assertEquals(
                    err.detail.message,
                    ErrorMessage.PARKING_RESERVATION_REPLACE_UNAVAILABLE.format().message
                )
            }

            @Test
            @DisplayName("契約されている場合、場所変更で予約可能")
            fun case3() {

                // 場所変更
                val registerReplace = stubRegisterParkingReservation(
                    reservationType = ParkingReservation.Type.REPLACE,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )

                assertDoesNotThrow {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findActiveReservationsFunc = { _, _ -> emptyList() }
                        ),
                        parkingDetailsService = parkingService(
                            parkingDetailsRepository = MockParkingDetailsRepository(
                                findParkingDetailByOrderCodeFunc = { _, _ ->
                                    listOf(
                                        stubParking(
                                            parkingLotList = listOf(
                                                stubParkingLot(
                                                    parkingStatusDivision = ParkingLot.StatusDivision.APPLIED
                                                )
                                            ),
                                        )
                                    )
                                }
                            )
                        ),
                    ).updateReservation(stubJwtAuthInfo(), registerReplace)
                }
            }

            @Test
            @DisplayName("契約されているかつ退去予定駐車場でない場合、場所変更以外で予約不可")
            fun case4() {

                // 仮申込
                val registerTentative = stubRegisterParkingReservation(
                    reserveStartDatetime = LocalDate.now().atTime(LocalTime.MIN),
                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                    parkingReservationStatus = ParkingReservation.Status.TENTATIVE,
                )

                val err = assertThrows<ServerValidationException> {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findActiveReservationsFunc = { _, _ -> emptyList() }
                        ),
                        parkingDetailsService = parkingService(
                            parkingDetailsRepository = MockParkingDetailsRepository(
                                findParkingDetailByOrderCodeFunc = { _, _ ->
                                    listOf(
                                        stubParking(
                                            parkingLotList = listOf(
                                                stubParkingLot(
                                                    parkingStatusDivision = ParkingLot.StatusDivision.APPLIED
                                                )
                                            ),
                                        )
                                    )
                                }
                            )
                        ),
                    ).updateReservation(stubJwtAuthInfo(), registerTentative)
                }
                assertEquals(err.detail.message, ErrorMessage.PARKING_IS_SIGNED.format().message)
            }

            @Test
            @DisplayName("契約されているかつ退去予定駐車場の場合、予約開始日が退去予定日より後でない場合は予約不可")
            fun case5() {
                val currentDate = LocalDate.now()

                // 仮申込
                val registerTentative = stubRegisterParkingReservation(
                    reserveStartDatetime = currentDate.atTime(LocalTime.MIN),
                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                    parkingReservationStatus = ParkingReservation.Status.TENTATIVE,
                )

                val err = assertThrows<ServerValidationException> {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findActiveReservationsFunc = { _, _ -> emptyList() }
                        ),
                        parkingDetailsService = parkingService(
                            parkingDetailsRepository = MockParkingDetailsRepository(
                                findParkingDetailByOrderCodeFunc = { _, _ ->
                                    listOf(
                                        stubParking(
                                            parkingLotList = listOf(
                                                stubParkingLot(
                                                    parkingStatusDivision = ParkingLot.StatusDivision.PLANNED_MOVE_OUT,
                                                    expectedMoveOutDate = currentDate.plusDays(1)
                                                        .yyyyMMdd()// 退去予定日が翌日
                                                )
                                            ),
                                        )
                                    )
                                }
                            )
                        ),
                    ).updateReservation(stubJwtAuthInfo(), registerTentative)
                }
                assertEquals(
                    err.detail.message,
                    ErrorMessage.PARKING_RESERVATION_INVALID_START_DATE_PLANNED_MOVE_OUT.format().message
                )
            }

            @Test
            @DisplayName("契約されているかつ退去予定駐車場の場合、予約開始日が退去予定日より後の場合は予約可能")
            fun case6() {
                val currentDate = LocalDate.now()

                // 仮申込
                val registerTentative = stubRegisterParkingReservation(
                    reserveStartDatetime = currentDate.atTime(LocalTime.MIN),
                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                    parkingReservationStatus = ParkingReservation.Status.TENTATIVE,
                )

                assertDoesNotThrow {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findActiveReservationsFunc = { _, _ -> emptyList() }
                        ),
                        parkingDetailsService = parkingService(
                            parkingDetailsRepository = MockParkingDetailsRepository(
                                findParkingDetailByOrderCodeFunc = { _, _ ->
                                    listOf(
                                        stubParking(
                                            parkingLotList = listOf(
                                                stubParkingLot(
                                                    parkingStatusDivision = ParkingLot.StatusDivision.PLANNED_MOVE_OUT,
                                                    expectedMoveOutDate = currentDate.minusDays(1)
                                                        .yyyyMMdd()// 退去予定日が前日
                                                )
                                            ),
                                        )
                                    )
                                }
                            )
                        ),
                    ).updateReservation(stubJwtAuthInfo(), registerTentative)
                }
            }
        }

        @Nested
        @DisplayName("予約がある駐車場に予約するケース")
        inner class Scenario1x3 {

            @Test
            @DisplayName("申込がある場合、仮申込or申込で予約不可")
            fun case1() {

                // 仮申込
                val registerTentative = stubRegisterParkingReservation(
                    reserveStartDatetime = LocalDate.now().atTime(LocalTime.MIN),
                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                    parkingReservationStatus = ParkingReservation.Status.TENTATIVE,
                )
                val err = assertThrows<ServerValidationException> {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findActiveReservationsFunc = { _, _ ->
                                listOf(
                                    stubParkingReservationInfo(
                                        reserveStartDatetime = LocalDate.now()
                                            .atTime(LocalTime.MIN),
                                        reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                    )
                                )
                            }
                        ),
                    ).updateReservation(stubJwtAuthInfo(), registerTentative)
                }
                assertEquals(
                    err.detail.message,
                    ErrorMessage.PARKING_RESERVATION_ALREADY_RESERVED.format().message
                )
            }

            @Test
            @DisplayName("申込or仮申込がある場合、予約期間が重複しなければ作業orウェルカムパークで予約可能")
            fun case2() {

                val now = LocalDate.now()

                // 作業で予約
                val registerWork = stubRegisterParkingReservation(
                    reserveStartDatetime = now.plusDays(10).atTime(LocalTime.MIN),
                    reserveEndDatetime = now.plusDays(14).atTime(LocalTime.MAX),
                    reservationType = ParkingReservation.Type.WORK,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )

                assertDoesNotThrow {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findActiveReservationsFunc = { _, _ ->
                                listOf(
                                    stubParkingReservationInfo(
                                        reserveStartDatetime = now.plusDays(15)
                                            .atTime(LocalTime.MIN),
                                        reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                    )
                                )
                            }
                        ),
                    ).updateReservation(stubJwtAuthInfo(), registerWork)
                }
            }

            @Test
            @DisplayName("申込or仮申込がある場合、予約期間が重複したら作業orウェルカムパークでも予約不可")
            fun case3() {

                val now = LocalDate.now()

                // 作業で予約
                val registerOneDay = stubRegisterParkingReservation(
                    reserveStartDatetime = now.plusDays(15).atTime(LocalTime.MIN),
                    reserveEndDatetime = now.plusDays(15).atTime(LocalTime.MAX),
                    reservationType = ParkingReservation.Type.WORK,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )

                val err = assertThrows<ServerValidationException> {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findActiveReservationsFunc = { _, _ ->
                                listOf(
                                    stubParkingReservationInfo(
                                        reserveStartDatetime = now.plusDays(15)
                                            .atTime(LocalTime.MIN),
                                        reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                    )
                                )
                            }
                        ),
                    ).updateReservation(stubJwtAuthInfo(), registerOneDay)
                }
                assertEquals(
                    err.detail.message,
                    ErrorMessage.PARKING_RESERVATION_DATE_CONFLICTED.format().message
                )
            }

            @Nested
            @DisplayName("作業orウェルカムパークの予約がある駐車場に予約するケース")
            inner class Scenario1x3x1 {
                @Test
                @DisplayName("既存の予約期間より前の日付から開始する設定で申込した場合（期間が重なる）は、予約でないこと")
                fun case1() {
                    val now = LocalDate.now()

                    val registerRequest = stubRegisterParkingReservation(
                        reserveStartDatetime = now.plusDays(-1).atTime(LocalTime.MIN),
                        reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    )

                    assertThrows<ServerValidationException> {
                        parkingReservationService(
                            MockParkingReservationRepository(
                                findActiveReservationsFunc = { _, _ ->
                                    listOf(
                                        stubParkingReservationInfo(
                                            reserveStartDatetime = now.atTime(LocalTime.MIN),
                                            reserveEndDatetime = now.plusDays(7)
                                                .atTime(LocalTime.MAX),
                                            reservationType = ParkingReservation.Type.WORK,
                                            parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                        )
                                    )
                                }
                            ),
                        ).updateReservation(stubJwtAuthInfo(), registerRequest)
                    }
                }

                @Test
                @DisplayName("既存の予約期間の開始日当日から開始する設定で申込した場合（期間が重なる）は、予約できないこと")
                fun case2() {
                    val now = LocalDate.now()

                    val registerRequest = stubRegisterParkingReservation(
                        reserveStartDatetime = now.atTime(LocalTime.MIN),
                        reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    )
                    assertThrows<ServerValidationException> {
                        parkingReservationService(
                            MockParkingReservationRepository(
                                findActiveReservationsFunc = { _, _ ->
                                    listOf(
                                        stubParkingReservationInfo(
                                            reserveStartDatetime = now.atTime(LocalTime.MIN),
                                            reserveEndDatetime = now.plusDays(7)
                                                .atTime(LocalTime.MAX),
                                            reservationType = ParkingReservation.Type.WORK,
                                            parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                        )
                                    )
                                }
                            ),
                        ).updateReservation(stubJwtAuthInfo(), registerRequest)
                    }
                }

                @Test
                @DisplayName("既存の予約期間内に開始する設定で申込した場合（期間が重なる）は、予約できないこと")
                fun case3() {
                    val now = LocalDate.now()

                    val registerRequest = stubRegisterParkingReservation(
                        reserveStartDatetime = now.plusDays(3).atTime(LocalTime.MIN),
                        reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    )
                    assertThrows<ServerValidationException> {
                        parkingReservationService(
                            MockParkingReservationRepository(
                                findActiveReservationsFunc = { _, _ ->
                                    listOf(
                                        stubParkingReservationInfo(
                                            reserveStartDatetime = now.atTime(LocalTime.MIN),
                                            reserveEndDatetime = now.plusDays(7)
                                                .atTime(LocalTime.MAX),
                                            reservationType = ParkingReservation.Type.WORK,
                                            parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                        )
                                    )
                                }
                            ),
                        ).updateReservation(stubJwtAuthInfo(), registerRequest)
                    }
                }

                @Test
                @DisplayName("既存の予約期間終了日当日から開始する設定で申込した場合（期間が重なる）は、予約できないこと")
                fun case4() {
                    val now = LocalDate.now()

                    val registerRequest = stubRegisterParkingReservation(
                        reserveStartDatetime = now.plusDays(7).atTime(LocalTime.MIN),
                        reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    )
                    assertThrows<ServerValidationException> {
                        parkingReservationService(
                            MockParkingReservationRepository(
                                findActiveReservationsFunc = { _, _ ->
                                    listOf(
                                        stubParkingReservationInfo(
                                            reserveStartDatetime = now.atTime(LocalTime.MIN),
                                            reserveEndDatetime = now.plusDays(7)
                                                .atTime(LocalTime.MAX),
                                            reservationType = ParkingReservation.Type.WORK,
                                            parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                        )
                                    )
                                }
                            ),
                        ).updateReservation(stubJwtAuthInfo(), registerRequest)
                    }
                }

                @Test
                @DisplayName("既存の予約期間より後の日付から開始する設定で申込した場合（期間が重ならない）は、予約できること")
                fun case5() {
                    val now = LocalDate.now()

                    val registerRequest = stubRegisterParkingReservation(
                        reserveStartDatetime = now.plusDays(8).atTime(LocalTime.MIN),
                        reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    )
                    assertDoesNotThrow {
                        parkingReservationService(
                            MockParkingReservationRepository(
                                findActiveReservationsFunc = { _, _ ->
                                    listOf(
                                        stubParkingReservationInfo(
                                            reserveStartDatetime = now.atTime(LocalTime.MIN),
                                            reserveEndDatetime = now.plusDays(7)
                                                .atTime(LocalTime.MAX),
                                            reservationType = ParkingReservation.Type.WORK,
                                            parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                        )
                                    )
                                }
                            ),
                        ).updateReservation(stubJwtAuthInfo(), registerRequest)
                    }
                }

                @Test
                @DisplayName("既存の予約期間より前の日付から開始し、既存の予約期間内で終了する設定で申込した場合（期間が重なる）は、予約できないこと")
                fun case6() {
                    val now = LocalDate.now()

                    val registerRequest = stubRegisterParkingReservation(
                        reserveStartDatetime = now.plusDays(-1).atTime(LocalTime.MIN),
                        reserveEndDatetime = now.plusDays(3).atTime(LocalTime.MAX),
                        reservationType = ParkingReservation.Type.WORK,
                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    )
                    assertThrows<ServerValidationException> {
                        parkingReservationService(
                            MockParkingReservationRepository(
                                findActiveReservationsFunc = { _, _ ->
                                    listOf(
                                        stubParkingReservationInfo(
                                            reserveStartDatetime = now.atTime(LocalTime.MIN),
                                            reserveEndDatetime = now.plusDays(7)
                                                .atTime(LocalTime.MAX),
                                            reservationType = ParkingReservation.Type.WORK,
                                            parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                        )
                                    )
                                }
                            ),
                        ).updateReservation(stubJwtAuthInfo(), registerRequest)
                    }
                }

                @Test
                @DisplayName("既存の予約期間より前の日付から開始/終了する設定で申込した場合（期間が重ならない）は、予約できること")
                fun case7() {
                    val now = LocalDate.now()

                    val registerRequest = stubRegisterParkingReservation(
                        reserveStartDatetime = now.plusDays(-2).atTime(LocalTime.MIN),
                        reserveEndDatetime = now.plusDays(-1).atTime(LocalTime.MAX),
                        reservationType = ParkingReservation.Type.WORK,
                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    )
                    assertDoesNotThrow {
                        parkingReservationService(
                            MockParkingReservationRepository(
                                findActiveReservationsFunc = { _, _ ->
                                    listOf(
                                        stubParkingReservationInfo(
                                            reserveStartDatetime = now.atTime(LocalTime.MIN),
                                            reserveEndDatetime = now.plusDays(7)
                                                .atTime(LocalTime.MAX),
                                            reservationType = ParkingReservation.Type.WORK,
                                            parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                        )
                                    )
                                }
                            ),
                        ).updateReservation(stubJwtAuthInfo(), registerRequest)
                    }
                }

                @Test
                @DisplayName("場所変更予約はできないこと")
                fun case8() {
                    val now = LocalDate.now()

                    // 場所変更
                    val registerReplace = stubRegisterParkingReservation(
                        reservationType = ParkingReservation.Type.REPLACE,
                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    )

                    val err = assertThrows<ServerValidationException> {
                        parkingReservationService(
                            MockParkingReservationRepository(
                                findActiveReservationsFunc = { _, _ ->
                                    listOf(
                                        stubParkingReservationInfo(
                                            reserveStartDatetime = now.plusDays(15)
                                                .atTime(LocalTime.MIN),
                                            reserveEndDatetime = now.plusDays(15)
                                                .atTime(LocalTime.MAX),
                                            reservationType = ParkingReservation.Type.WORK,
                                            parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                        )
                                    )
                                }
                            ),
                        ).updateReservation(stubJwtAuthInfo(), registerReplace)
                    }
                    assertEquals(
                        err.detail.message,
                        ErrorMessage.PARKING_RESERVATION_REPLACE_UNAVAILABLE.format().message
                    )
                }
            }

            @Test
            @DisplayName("場所変更がある場合、予約不可")
            fun case10() {

                // 仮申込
                val registerTentative = stubRegisterParkingReservation(
                    reserveStartDatetime = LocalDate.now().atTime(LocalTime.MIN),
                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                    parkingReservationStatus = ParkingReservation.Status.TENTATIVE,
                )

                val err = assertThrows<ServerValidationException> {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findActiveReservationsFunc = { _, _ ->
                                listOf(
                                    stubParkingReservationInfo(
                                        reservationType = ParkingReservation.Type.REPLACE,
                                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                    )
                                )
                            }
                        ),
                    ).updateReservation(stubJwtAuthInfo(), registerTentative)
                }
                assertEquals(
                    err.detail.message,
                    ErrorMessage.PARKING_RESERVATION_ALREADY_RESERVED.format().message
                )
            }
        }

        @Nested
        @DisplayName("駐車場が利用不可のケース")
        inner class Scenario1x4 {

            @Test
            @DisplayName("駐車場が利用不可の場合は予約不可")
            fun case1() {

                val registerApplication = stubRegisterParkingReservation(
                    reserveStartDatetime = LocalDate.now().atTime(LocalTime.MIN),
                    reservationType = ParkingReservation.Type.AUTO_APPLICATION,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )

                val err = assertThrows<ServerValidationException> {
                    parkingReservationService(
                        parkingEnableRepository = MockParkingEnableRepository(
                            isAvailableFunc = { _ -> false }
                        )
                    ).updateReservation(stubJwtAuthInfo(), registerApplication)
                }

                assertEquals(
                    err.detail.message,
                    ErrorMessage.PARKING_IS_NOT_AVAILABLE.format().message
                )
            }
        }
    }

    @Nested
    @DisplayName("駐車場予約情報更新の際、バリデーションが期待通りに行えていることの検証")
    inner class Scenario2 {

        @Nested
        @DisplayName("予約種別によるバリデーションの検証")
        inner class Scenario2x1 {

            @Test
            @DisplayName("場所変更->場所変更は変更可能")
            fun case1() {

                val updateTentative = stubUpdateParkingReservation(
                    receptionStaff = "シンプレクス",
                    reservationType = ParkingReservation.Type.REPLACE,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )

                assertDoesNotThrow {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findByIdFunc = { _ ->
                                stubParkingReservationInfo(
                                    reserveStartDatetime = LocalDate.now().atTime(LocalTime.MIN),
                                    reservationType = ParkingReservation.Type.REPLACE,
                                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                )
                            },
                        ),
                    ).updateReservation(stubJwtAuthInfo(), updateTentative)
                }
            }

            @Test
            @DisplayName("受付->作業は変更不可")
            fun case2() {

                val now = LocalDate.now()

                val updateWork = stubUpdateParkingReservation(
                    reserveStartDatetime = now.plusDays(5).atTime(LocalTime.MIN),
                    reservationType = ParkingReservation.Type.WORK,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )

                val err = assertThrows<ServerValidationException> {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findByIdFunc = { _ ->
                                stubParkingReservationInfo(
                                    reserveStartDatetime = now.atTime(LocalTime.MIN),
                                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                )
                            },
                        ),
                    ).updateReservation(stubJwtAuthInfo(), updateWork)
                }
                assertEquals(
                    err.detail.message,
                    ErrorMessage.PARKING_RESERVATION_TYPE_UNCHANGEABLE.format().message
                )
            }
        }

        @Nested
        @DisplayName("予約状態によるバリデーションの検証")
        inner class Scenario2x2 {

            @Test
            @DisplayName("受付or仮申込は変更可能")
            fun case1() {

                // 仮申込
                val updateTentative = stubUpdateParkingReservation(
                    reserveStartDatetime = LocalDate.now().atTime(LocalTime.MIN),
                    receptionStaff = "シンプレクス",
                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                    parkingReservationStatus = ParkingReservation.Status.TENTATIVE,
                )

                assertDoesNotThrow {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findByIdFunc = { _ ->
                                stubParkingReservationInfo(
                                    reserveStartDatetime = LocalDate.now().atTime(LocalTime.MIN),
                                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                                    parkingReservationStatus = ParkingReservation.Status.TENTATIVE,
                                )
                            },
                        ),
                    ).updateReservation(stubJwtAuthInfo(), updateTentative)
                }
            }

            @Test
            @DisplayName("完了orキャンセル状態は変更不可")
            fun case2() {

                val now = LocalDate.now()

                val updateWork = stubUpdateParkingReservation(
                    reserveStartDatetime = now.plusDays(6).atTime(LocalTime.MIN),
                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                    parkingReservationStatus = ParkingReservation.Status.CANCEL,
                )

                val err = assertThrows<ServerValidationException> {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findByIdFunc = { _ ->
                                stubParkingReservationInfo(
                                    reserveStartDatetime = now.plusDays(5).atTime(LocalTime.MIN),
                                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                                    parkingReservationStatus = ParkingReservation.Status.CANCEL, // キャンセル
                                )
                            },
                        ),
                    ).updateReservation(stubJwtAuthInfo(), updateWork)
                }

                assertEquals(
                    err.detail.message,
                    ErrorMessage.PARKING_RESERVATION_CHANGE_INVALID_STATUS.format().message
                )
            }
        }

        @Nested
        @DisplayName("複数予約の組み合わせによるバリデーションの検証")
        inner class Scenario2x3 {

            private val now = LocalDate.now()

            private val existValidReservations = listOf(
                // 10日後に開始・15日後に終了する(作業-受付)(変更対象)
                stubParkingReservationInfo(
                    reserveStartDatetime = now.plusDays(10).atTime(LocalTime.MIN),
                    reserveEndDatetime = now.plusDays(15).atTime(LocalTime.MAX),
                    reservationType = ParkingReservation.Type.WORK,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                ),
                // 30日後に開始する(申込-受付)
                stubParkingReservationInfo(
                    id = stubParkingReservationId("3d9b5d3c-4c7c-462f-9e97-0b4b5c2a6d41"),
                    reserveStartDatetime = now.plusDays(30).atTime(LocalTime.MIN),
                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                ),
                // 5日後に開始・終了する(1日利用-受付)
                stubParkingReservationInfo(
                    id = stubParkingReservationId("e2d1c2c4-1b4f-4a2d-bc5c-5a1e5c9c8dff"),
                    reserveStartDatetime = now.plusDays(5).atTime(LocalTime.of(8, 0, 0)),
                    reserveEndDatetime = now.plusDays(5).atTime(LocalTime.of(7, 59, 59)),
                    reservationType = ParkingReservation.Type.ONE_DAY,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )
            )

            @Test
            @DisplayName("変更後の予約期間が他の予約の予約期間と被っていなければ変更可能")
            fun case1() {

                // 作業を6日後開始・29日後に終了に変更
                val updateWork = stubUpdateParkingReservation(
                    reserveStartDatetime = now.plusDays(6).atTime(LocalTime.MIN),
                    reserveEndDatetime = now.plusDays(29).atTime(LocalTime.MAX),
                    reservationType = ParkingReservation.Type.WORK,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )

                assertDoesNotThrow {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findByIdFunc = { _ ->
                                stubParkingReservationInfo(
                                    reserveStartDatetime = now.plusDays(6).atTime(LocalTime.MIN),
                                    reservationType = ParkingReservation.Type.WORK,
                                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                )
                            },
                            findActiveReservationsFunc = { _, _ -> existValidReservations }
                        ),
                    ).updateReservation(stubJwtAuthInfo(), updateWork)
                }
            }

            @Test
            @DisplayName("変更後の予約期間が他の予約の予約期間と被っていたら変更不可")
            fun case2() {

                // 作業を6日後開始・30日後に終了に変更
                val updateWork = stubUpdateParkingReservation(
                    reserveStartDatetime = now.plusDays(6).atTime(LocalTime.MIN),
                    reserveEndDatetime = now.plusDays(30).atTime(LocalTime.MAX),
                    reservationType = ParkingReservation.Type.WORK,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )

                val err = assertThrows<ServerValidationException> {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findByIdFunc = { _ ->
                                stubParkingReservationInfo(
                                    reserveStartDatetime = now.plusDays(6).atTime(LocalTime.MIN),
                                    reservationType = ParkingReservation.Type.WORK,
                                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                )
                            },
                            findActiveReservationsFunc = { _, _ -> existValidReservations }
                        ),
                    ).updateReservation(stubJwtAuthInfo(), updateWork)
                }

                assertEquals(
                    err.detail.message,
                    ErrorMessage.PARKING_RESERVATION_DATE_CONFLICTED.format().message
                )
            }

            @Test
            @DisplayName("申込み予約後に場所変更予約をし、既存の申込み予約の時間変更をする場合は、変更可能")
            fun case3() {
                // 開始日を7日後から14日後に変更
                val updateRequest = stubUpdateParkingReservation(
                    reserveStartDatetime = now.plusDays(14).atTime(LocalTime.MIN),
                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                )

                assertDoesNotThrow {
                    parkingReservationService(
                        MockParkingReservationRepository(
                            findByIdFunc = { _ ->
                                stubParkingReservationInfo(
                                    reserveStartDatetime = now.plusDays(7).atTime(LocalTime.MIN),
                                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                )
                            },
                            findActiveReservationsFunc = { _, _ ->
                                listOf(
                                    // 場所変更の予約のみ
                                    stubParkingReservationInfo(
                                        id = ParkingReservation.Id.create(),
                                        reservationType = ParkingReservation.Type.REPLACE,
                                        parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                                    ),
                                )
                            }
                        ),
                    ).updateReservation(stubJwtAuthInfo(), updateRequest)
                }
            }
        }
    }

    @Nested
    @DisplayName("駐車場予約情報削除の際、予約状態によるバリデーションが期待通りに行えていることの検証")
    inner class Scenario3 {

        private val cancelReservation = stubCancelParkingReservation(
            stubParkingReservationId()
        )

        @Test
        @DisplayName("受付or仮申込状態の予約は取消可能")
        fun case1() {

            assertDoesNotThrow {
                parkingReservationService(
                    MockParkingReservationRepository(
                        findByIdFunc = { _ ->
                            stubParkingReservationInfo(
                                reserveStartDatetime = LocalDate.now().plusDays(5)
                                    .atTime(LocalTime.MIN),
                                reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                                parkingReservationStatus = ParkingReservation.Status.RESERVATION, //受付
                            )
                        },
                    ),
                ).updateReservation(stubJwtAuthInfo(), cancelReservation)
            }
        }

        @Test
        @DisplayName("完了orキャンセル状態の予約は取消不可")
        fun case2() {

            val err = assertThrows<ServerValidationException> {
                parkingReservationService(
                    MockParkingReservationRepository(
                        findByIdFunc = { _ ->
                            stubParkingReservationInfo(
                                reserveStartDatetime = LocalDate.now().plusDays(5)
                                    .atTime(LocalTime.MIN),
                                reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                                parkingReservationStatus = ParkingReservation.Status.FINISHED, //完了
                            )
                        },
                    ),
                ).updateReservation(stubJwtAuthInfo(), cancelReservation)
            }

            assertEquals(
                err.detail.message,
                ErrorMessage.PARKING_RESERVATION_CANCEL_INVALID_STATUS.format().message
            )
        }
    }

    @Nested
    @DisplayName("駐車場申込予約情報削除の際、予約状態によるバリデーションが期待通りに行えていることの検証")
    inner class Scenario4 {

        @Test
        @DisplayName("受付or仮申込状態の予約は取消可能")
        fun case1() {

            assertDoesNotThrow {
                parkingReservationService(
                    MockParkingReservationRepository(
                        findActiveApplicationFunc = { _ ->
                            stubParkingReservationInfo(
                                reserveStartDatetime = LocalDate.now().plusDays(5)
                                    .atTime(LocalTime.MIN),
                                reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                                parkingReservationStatus = ParkingReservation.Status.RESERVATION, //受付
                            )
                        },
                        findByIdFunc = { _ ->
                            stubParkingReservationInfo(
                                reserveStartDatetime = LocalDate.now().plusDays(5)
                                    .atTime(LocalTime.MIN),
                                reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                                parkingReservationStatus = ParkingReservation.Status.CANCEL, //取消
                            )
                        },
                    ),
                ).updateReservation(
                    stubJwtAuthInfo(),
                    stubCancelParkingApplicationReservation()
                )
            }
        }

        @Test
        @DisplayName("完了orキャンセル状態の予約は取消不可")
        fun case2() {

            val err = assertThrows<ServerValidationException> {
                parkingReservationService(
                    MockParkingReservationRepository(
                        findActiveApplicationFunc = { _ ->
                            stubParkingReservationInfo(
                                reserveStartDatetime = LocalDate.now().plusDays(5)
                                    .atTime(LocalTime.MIN),
                                reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                                parkingReservationStatus = ParkingReservation.Status.FINISHED, //完了
                            )
                        },
                    ),
                ).updateReservation(
                    stubJwtAuthInfo(),
                    stubCancelParkingApplicationReservation()
                )
            }

            assertEquals(
                err.detail.message,
                ErrorMessage.PARKING_RESERVATION_CANCEL_INVALID_STATUS.format().message
            )
        }

        @Test
        @DisplayName("予約種別が申込の予約は取消不可")
        fun case3() {

            val now = LocalDate.now()

            val cancelApplicationReservation = stubCancelParkingApplicationReservation(
                reservationType = ParkingReservation.Type.WORK
            )

            val err = assertThrows<ServerValidationException> {
                parkingReservationService(
                    MockParkingReservationRepository(
                        findActiveApplicationFunc = { _ ->
                            stubParkingReservationInfo(
                                reserveStartDatetime = now.plusDays(5).atTime(LocalTime.MIN),
                                reserveEndDatetime = now.plusDays(6).atTime(LocalTime.MAX),
                                reservationType = ParkingReservation.Type.WORK,
                                parkingReservationStatus = ParkingReservation.Status.RESERVATION, //受付
                            )
                        },
                    ),
                ).updateReservation(stubJwtAuthInfo(), cancelApplicationReservation)
            }

            assertEquals(
                err.detail.message,
                ErrorMessage.PARKING_RESERVATION_INVALID_TYPE.format().message
            )
        }
    }

    @Nested
    @DisplayName("駐車場予約(新規登録・更新・キャンセル)の際、後続処理がお互いに依存しないことの検証")
    inner class Scenario5 {

        private var eBoardNotifyCount = 0
        private var updateParkingContractPossibilityAndDKPortalNotifyCount = 0

        @AfterEach
        fun tearDown() {
            eBoardNotifyCount = 0
            updateParkingContractPossibilityAndDKPortalNotifyCount = 0
        }

        @Test
        @DisplayName("いい物件ボードへの連携が失敗しても、駐車場契約可能状態更新・DKポータルへの連携は行われること")
        fun case1() {
            parkingReservationService(
                reservationRepository = MockParkingReservationRepository(
                    findByIdFunc = { _ -> stubParkingReservationInfo() }
                ),
                eBoardRepository = MockEboardRepository(
                    reserveParkingFunc = { _, _ -> throw RuntimeException() } // いい物件への連携は失敗するようにする
                ),
                parkingContractService = MockParkingContractService(
                    context = dslContext,
                    parkingContractPossibilityRepository = MockParkingContractPossibilityRepository(),
                    parkingDetailsService = parkingService(),
                    parkingRepository = MockParkingRepository(),
                    dkPortalRepository = MockDKPortalRepository(),
                    updateParkingContractPossibilityAndInformDKPortalFunc = { _, _ -> updateParkingContractPossibilityAndDKPortalNotifyCount++ }
                )
            ).updateReservation(
                stubJwtAuthInfo(),
                stubRegisterParkingReservation(
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    reservationType = ParkingReservation.Type.AUTO_APPLICATION
                )
            )
            Thread.sleep(1000)
            assertEquals(0, eBoardNotifyCount)
            assertEquals(1, updateParkingContractPossibilityAndDKPortalNotifyCount)
        }

        @Test
        @DisplayName("駐車場契約可能状態更新・DKポータルへの連携失敗しても、いい物件ボードへの連携は行われること")
        fun case2() {
            parkingReservationService(
                reservationRepository = MockParkingReservationRepository(
                    findByIdFunc = { _ -> stubParkingReservationInfo() }
                ),
                eBoardRepository = MockEboardRepository(
                    reserveParkingFunc = { _, _ -> eBoardNotifyCount++ }
                ),
                parkingContractService = MockParkingContractService(
                    context = dslContext,
                    parkingContractPossibilityRepository = MockParkingContractPossibilityRepository(),
                    parkingDetailsService = parkingService(),
                    parkingRepository = MockParkingRepository(),
                    dkPortalRepository = MockDKPortalRepository(),
                    updateParkingContractPossibilityAndInformDKPortalFunc = { _, _ -> throw RuntimeException() } // 駐車場契約可能状態更新・DKポータルへの連携は失敗するようにする
                )
            ).updateReservation(
                stubJwtAuthInfo(),
                stubRegisterParkingReservation(
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    reservationType = ParkingReservation.Type.AUTO_APPLICATION
                )
            )
            Thread.sleep(1000)
            assertEquals(1, eBoardNotifyCount)
            assertEquals(0, updateParkingContractPossibilityAndDKPortalNotifyCount)
        }
    }

    class MockParkingContractService(
        context: DSLContext,
        parkingContractPossibilityRepository: ParkingContractPossibilityRepositoryInterface,
        parkingDetailsService: ParkingDetailsService,
        parkingRepository: ParkingRepositoryInterface,
        dkPortalRepository: DKPortalRepositoryInterface,
        val updateParkingContractPossibilityAndInformDKPortalFunc: (requestUser: AuthInfo.RequestUser, orderCode: Building.OrderCode) -> Unit = { _, _ -> }
    ) : ParkingContractService(
        context,
        parkingContractPossibilityRepository,
        parkingDetailsService,
        parkingRepository,
        dkPortalRepository
    ) {

        override fun updateParkingContractPossibilityAndInformDKPortal(
            requestUser: AuthInfo.RequestUser,
            orderCode: Building.OrderCode,
            fromBatch: Boolean,
        ) {
            updateParkingContractPossibilityAndInformDKPortalFunc(requestUser, orderCode)
        }
    }

    @Nested
    @DisplayName("予約完了消込バッチの検証")
    inner class Scenario6 {

        private lateinit var parkingReservationService: ParkingReservationService
        private val reservationId1 = ParkingReservation.Id.create()
        private val reservationId2 = ParkingReservation.Id.create()

        @BeforeEach
        fun setup() {
            parkingReservationService = parkingReservationService(
                reservationRepository = MockParkingReservationRepository(
                    findActiveReservationsForBatchFunc = { ->
                        listOf(
                            stubParkingReservationInfo(
                                id = reservationId1,
                                buildingCode = "100000001",
                                parkingLotCode = "010",
                                parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                            ),
                            stubParkingReservationInfo(
                                buildingCode = "100000001",
                                parkingLotCode = "020",
                                parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                            ),
                            stubParkingReservationInfo(
                                id = reservationId2,
                                buildingCode = "100000001",
                                parkingLotCode = "030",
                                parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                            )
                        )
                    },
                ),
                parkingDetailsService = parkingService(
                    parkingDetailsRepository = MockParkingDetailsRepository(
                        findParkingDetailByOrderCodeFunc = { _, _ ->
                            listOf(
                                stubParking(
                                    buildingCode = "100000001",
                                    parkingLotList = listOf(
                                        stubParkingLot(
                                            buildingCode = "100000001",
                                            parkingLotCode = "010",
                                            parkingStatusDivision = ParkingLot.StatusDivision.CONFIRMED
                                        ),
                                        stubParkingLot(
                                            buildingCode = "100000001",
                                            parkingLotCode = "020",
                                            parkingStatusDivision = ParkingLot.StatusDivision.VACANT
                                        ),
                                        stubParkingLot(
                                            buildingCode = "100000001",
                                            parkingLotCode = "030",
                                            parkingStatusDivision = ParkingLot.StatusDivision.OCCUPIED
                                        )
                                    )
                                ),
                            )
                        }
                    )
                )
            )
        }

        @Test
        @DisplayName("完了消し込み対象の予約が抽出されること")
        fun case01() {
            val targets = parkingReservationService.getActiveReservationsShouldFinish()
            // 予約は3件あるが、確定と入居中のもののみ抽出対象となる
            Assertions.assertEquals(2, targets.size)
            Assertions.assertTrue(targets.any { it.id == reservationId1 })
            Assertions.assertTrue(targets.any { it.id == reservationId2 })
            Assertions.assertTrue(targets.all { it.status == ParkingReservation.Status.FINISHED })
        }

        @Test
        @DisplayName("完了消し込み実施件数が対象と一致すること")
        fun case02() {
            val targets = parkingReservationService.getActiveReservationsShouldFinish()
            val count = parkingReservationService.finishReservations(targets)
            assertEquals(targets.size, count)
        }

    }
}
