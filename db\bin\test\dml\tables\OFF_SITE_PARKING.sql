truncate table OFF_SITE_PARKING;
insert into OFF_SITE_PARKING (CREATION_DATE, CREATION_TIME, UPDATE_DATE, UPDATE_TIME, UPDATE_PROGRAM_ID, UPDATER, LOGICAL_DELETE_SIGN, ORDER_CD, PARKING_CD, OFF_SITE_LOCATION_PREF_CD, OFF_SITE_LOCATION_CITY_CD, OFF_SITE_LOCATION_TOWN_CD, OFF_SITE_LOCATION_DETAIL, RESIDENCE_DISPLAY_TYPE, RESIDENCE_LOCATION_PREF_CD, RESIDENCE_LOCATION_CITY_CD, RESIDENCE_LOCATION_TOWN_CD, RESIDENCE_LOCATION_DETAIL, LATITUDE_WGS, LONGITUDE_WGS, DISTANCE, MAP_SCALE) values
 (0, 0, 0, 0, null, null, 0, '0005736', '002', null, null, null, null, null, null, null, null, null, 0, 0, 120, 0)
,(0, 0, 0, 0, null, null, 0, '0227847', '001', null, null, null, null, null, null, null, null, null, 0, 0, 2000, 0)
,(0, 0, 0, 0, null, null, 0, '0163096', '001', null, null, null, null, null, null, null, null, null, 0, 0, 1, 0)
,(0, 0, 0, 0, null, null, 0, '0196697', '001', null, null, null, null, null, null, null, null, null, 0, 0, 1234, 0)
,(0, 0, 0, 0, null, null, 0, '0243739', '001', null, null, null, null, null, null, null, null, null, 0, 0, 2000, 0)
,(0, 0, 0, 0, null, null, 0, '0005736', '004', null, null, null, null, null, null, null, null, null, 0, 0, 120, 0)
,(0, 0, 0, 0, null, null, 0, '0104357', '001', null, null, null, null, null, null, null, null, null, 0, 0, 2000, 0)
;
