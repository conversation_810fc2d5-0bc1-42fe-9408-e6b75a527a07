package jp.ne.simplex.mock

import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.repository.db.VacantParkingListRepositoryInterface

class MockVacantParkingListRepository(
    val deleteFunc: () -> Unit? = { -> },
    val saveFunc: (parkingLotId: ParkingLot.Id, parkingLotNumber: String?) -> Unit = { _, _ -> }
) : VacantParkingListRepositoryInterface {
    override fun delete() {
        deleteFunc()
    }

    override fun save(parkingLotId: ParkingLot.Id, parkingLotNumber: String?) {
        saveFunc(parkingLotId, parkingLotNumber)
    }
}
