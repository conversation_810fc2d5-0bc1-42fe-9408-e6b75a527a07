package jp.ne.simplex.application.model

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.types.shouldBeInstanceOf
import jp.ne.simplex.application.model.UpdatePropertyMaintenance.Companion.getUpdateAdFf
import jp.ne.simplex.application.model.UpdatePropertyMaintenance.Companion.getUpdatePublishStatus
import jp.ne.simplex.application.model.UpdatePropertyMaintenance.Companion.getUpdatePublishStatusWithUpState
import jp.ne.simplex.stub.stubProperty
import jp.ne.simplex.stub.stubPropertyId
import jp.ne.simplex.stub.stubUpdatePropertyMaintenance

class PropertyMaintenanceTest : FunSpec({
    // テストデータ
    val propertyId1 = stubPropertyId(
        buildingCode = "000000000",
        roomCode = "00000",
    )
    val propertyId2 = stubPropertyId(
        buildingCode = "111111111",
        roomCode = "11111",
    )
    val propertyId3 = stubPropertyId(
        buildingCode = "222222222",
        roomCode = "22222",
    )

    val property1 = stubProperty(
        buildingCode = propertyId1.buildingCode.value,
        roomCode = propertyId1.roomCode.value,
    )

    val property2 = stubProperty(
        buildingCode = propertyId2.buildingCode.value,
        roomCode = propertyId2.roomCode.value,
    )

    val property3 = stubProperty(
        buildingCode = propertyId3.buildingCode.value,
        roomCode = propertyId3.roomCode.value,
    )

    context("物件メンテナンス更新用オブジェクトから、各物件項目（公開指示/金額全般/AD・FF）更新用オブジェクトを取得できること") {

        // 物件1 ~ 3 の更新リクエスト
        val propertyMaintenanceUpdateReq = listOf(
            stubUpdatePropertyMaintenance(
                buildingCode = propertyId1.buildingCode.value,
                roomCode = propertyId1.roomCode.value,
            ), stubUpdatePropertyMaintenance(
                buildingCode = propertyId2.buildingCode.value,
                roomCode = propertyId2.roomCode.value,
            ), stubUpdatePropertyMaintenance(
                buildingCode = propertyId3.buildingCode.value,
                roomCode = propertyId3.roomCode.value,
            )
        )

        /** @see UpdatePropertyMaintenance.AdFf */
        test("AD・FF更新用オブジェクトを取得できること") {

            // 物件 1,2 のみDBに永続化されている場合
            val actual = propertyMaintenanceUpdateReq
                .getUpdateAdFf(listOf(property1, property3))

            actual.shouldBeInstanceOf<List<UpdatePropertyMaintenance.AdFf>>()
            actual.map { it.id }.contains(propertyId1).shouldBeTrue()
            actual.map { it.id }.contains(propertyId2).shouldBeFalse()
            actual.map { it.id }.contains(propertyId3).shouldBeTrue()
        }

        /** @see UpdatePropertyMaintenance.PublishStatus */
        test("公開指示更新用オブジェクトを取得できること") {
            // 物件 2 のみDBに永続化されている場合
            val actual = propertyMaintenanceUpdateReq
                .getUpdatePublishStatus(listOf(property2))

            actual.shouldBeInstanceOf<List<UpdatePropertyMaintenance.PublishStatus>>()
            actual.map { it.id }.contains(propertyId1).shouldBeFalse()
            actual.map { it.id }.contains(propertyId2).shouldBeTrue()
            actual.map { it.id }.contains(propertyId3).shouldBeFalse()
        }

        /** @see UpdatePropertyMaintenance.PublishStatusWithUpState */
        test("公開指示（掲載状態を含む）更新用オブジェクトを取得できること") {
            // 物件 1,2 のみDBに永続化されている場合
            // 物件 1 のみ掲載状態が設定されている場合
            val actual = propertyMaintenanceUpdateReq
                .getUpdatePublishStatusWithUpState(
                    listOf(property1, property2),
                    mapOf(property1.id to Property.UpState.RECRUITING)
                )

            actual.shouldBeInstanceOf<List<UpdatePropertyMaintenance.PublishStatusWithUpState>>()
            actual.map { it.id }.contains(propertyId1).shouldBeTrue()
            actual.map { it.id }.contains(propertyId2).shouldBeFalse()
            actual.map { it.id }.contains(propertyId3).shouldBeFalse()
        }
    }
})
