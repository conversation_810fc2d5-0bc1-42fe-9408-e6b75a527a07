/*
 * This file is generated by jOOQ.
 */
package jp.ne.simplex.db.jooq.gen.tables.records


import jp.ne.simplex.db.jooq.gen.tables.AgentTable
import jp.ne.simplex.db.jooq.gen.tables.pojos.AgentPojo

import org.jooq.Record1
import org.jooq.impl.UpdatableRecordImpl


/**
 * 仲介業者 既存システム物理名: ELA10P
 */
@Suppress("UNCHECKED_CAST")
open class AgentRecord private constructor() : UpdatableRecordImpl<AgentRecord>(AgentTable.AGENT) {

    open var sakuseiDt: Int?
        set(value): Unit = set(0, value)
        get(): Int? = get(0) as Int?

    open var sakuseiTm: Int?
        set(value): Unit = set(1, value)
        get(): Int? = get(1) as Int?

    open var koshinDt: Int?
        set(value): Unit = set(2, value)
        get(): Int? = get(2) as Int?

    open var koshinTm: Int?
        set(value): Unit = set(3, value)
        get(): Int? = get(3) as Int?

    open var koshinPgmId: String?
        set(value): Unit = set(4, value)
        get(): String? = get(4) as String?

    open var koshinsha: String?
        set(value): Unit = set(5, value)
        get(): String? = get(5) as String?

    open var chukaiGyoshaCd: String
        set(value): Unit = set(6, value)
        get(): String = get(6) as String

    open var togoTorihikisakiCd: String?
        set(value): Unit = set(7, value)
        get(): String? = get(7) as String?

    open var chukaiGyoshameiKanji: String?
        set(value): Unit = set(8, value)
        get(): String? = get(8) as String?

    open var chukaiGyoshameiKanji_2: String?
        set(value): Unit = set(9, value)
        get(): String? = get(9) as String?

    open var chukaiGyoshameiKana: String?
        set(value): Unit = set(10, value)
        get(): String? = get(10) as String?

    open var honshitenmeiKanji: String?
        set(value): Unit = set(11, value)
        get(): String? = get(11) as String?

    open var kensakuyoKana: String?
        set(value): Unit = set(12, value)
        get(): String? = get(12) as String?

    open var kojinHojinKbn: String?
        set(value): Unit = set(13, value)
        get(): String? = get(13) as String?

    open var hojinkakuKbn: String?
        set(value): Unit = set(14, value)
        get(): String? = get(14) as String?

    open var hojinkakuZengoKbn: String?
        set(value): Unit = set(15, value)
        get(): String? = get(15) as String?

    open var kanrenKaishaSign: Byte?
        set(value): Unit = set(16, value)
        get(): Byte? = get(16) as Byte?

    open var daihyoshameiKanji: String?
        set(value): Unit = set(17, value)
        get(): String? = get(17) as String?

    open var daihyoshameiKana: String?
        set(value): Unit = set(18, value)
        get(): String? = get(18) as String?

    open var chukaiGyoshaTantoBusho: String?
        set(value): Unit = set(19, value)
        get(): String? = get(19) as String?

    open var chukaiGyoshaTantoshamei: String?
        set(value): Unit = set(20, value)
        get(): String? = get(20) as String?

    open var yubinBango: String?
        set(value): Unit = set(21, value)
        get(): String? = get(21) as String?

    open var todofukenCd: String?
        set(value): Unit = set(22, value)
        get(): String? = get(22) as String?

    open var shikugunCd: String?
        set(value): Unit = set(23, value)
        get(): String? = get(23) as String?

    open var chosonazaTsushoCd: String?
        set(value): Unit = set(24, value)
        get(): String? = get(24) as String?

    open var jushoShosai: String?
        set(value): Unit = set(25, value)
        get(): String? = get(25) as String?

    open var biruMeisho: String?
        set(value): Unit = set(26, value)
        get(): String? = get(26) as String?

    open var denwaBango: String?
        set(value): Unit = set(27, value)
        get(): String? = get(27) as String?

    open var faxBango: String?
        set(value): Unit = set(28, value)
        get(): String? = get(28) as String?

    open var setsuritsuDt: Int?
        set(value): Unit = set(29, value)
        get(): Int? = get(29) as Int?

    open var takkenngyoMenkyoBango_1: String?
        set(value): Unit = set(30, value)
        get(): String? = get(30) as String?

    open var takkenngyoMenkyoBango_2: String?
        set(value): Unit = set(31, value)
        get(): String? = get(31) as String?

    open var takkenngyoMenkyoBango_3: String?
        set(value): Unit = set(32, value)
        get(): String? = get(32) as String?

    open var ginkoCd: String?
        set(value): Unit = set(33, value)
        get(): String? = get(33) as String?

    open var ginkoShitenCd: String?
        set(value): Unit = set(34, value)
        get(): String? = get(34) as String?

    open var kozaSbt: String?
        set(value): Unit = set(35, value)
        get(): String? = get(35) as String?

    open var kozaBango: String?
        set(value): Unit = set(36, value)
        get(): String? = get(36) as String?

    open var kozaMeigininmeiKana: String?
        set(value): Unit = set(37, value)
        get(): String? = get(37) as String?

    open var kozaMeigininmeiKanji: String?
        set(value): Unit = set(38, value)
        get(): String? = get(38) as String?

    open var shokaiTorokuShitenCd: String?
        set(value): Unit = set(39, value)
        get(): String? = get(39) as String?

    open var torihikiTeishiSign: Byte?
        set(value): Unit = set(40, value)
        get(): Byte? = get(40) as Byte?

    open var interfaceSign: Byte?
        set(value): Unit = set(41, value)
        get(): Byte? = get(41) as Byte?

    open var dataIkomotoKey_1: String?
        set(value): Unit = set(42, value)
        get(): String? = get(42) as String?

    open var dataIkomotoKey_2: String?
        set(value): Unit = set(43, value)
        get(): String? = get(43) as String?

    open var jichitaiCd: Int?
        set(value): Unit = set(44, value)
        get(): Int? = get(44) as Int?

    open var sakujoDt: Int?
        set(value): Unit = set(45, value)
        get(): Int? = get(45) as Int?

    open var tokuteiFudosanKbn: String?
        set(value): Unit = set(46, value)
        get(): String? = get(46) as String?

    open var syuyouChukaiGyoshaKbn: String?
        set(value): Unit = set(47, value)
        get(): String? = get(47) as String?

    open var tokuteiFcKbn: String?
        set(value): Unit = set(48, value)
        get(): String? = get(48) as String?

    open var yobiKbn_1: String?
        set(value): Unit = set(49, value)
        get(): String? = get(49) as String?

    open var yobiDt_1: Int?
        set(value): Unit = set(50, value)
        get(): Int? = get(50) as Int?

    open var yobiDt_2: Int?
        set(value): Unit = set(51, value)
        get(): Int? = get(51) as Int?

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    override fun key(): Record1<String?> = super.key() as Record1<String?>

    /**
     * Create a detached, initialised AgentRecord
     */
    constructor(sakuseiDt: Int? = null, sakuseiTm: Int? = null, koshinDt: Int? = null, koshinTm: Int? = null, koshinPgmId: String? = null, koshinsha: String? = null, chukaiGyoshaCd: String, togoTorihikisakiCd: String? = null, chukaiGyoshameiKanji: String? = null, chukaiGyoshameiKanji_2: String? = null, chukaiGyoshameiKana: String? = null, honshitenmeiKanji: String? = null, kensakuyoKana: String? = null, kojinHojinKbn: String? = null, hojinkakuKbn: String? = null, hojinkakuZengoKbn: String? = null, kanrenKaishaSign: Byte? = null, daihyoshameiKanji: String? = null, daihyoshameiKana: String? = null, chukaiGyoshaTantoBusho: String? = null, chukaiGyoshaTantoshamei: String? = null, yubinBango: String? = null, todofukenCd: String? = null, shikugunCd: String? = null, chosonazaTsushoCd: String? = null, jushoShosai: String? = null, biruMeisho: String? = null, denwaBango: String? = null, faxBango: String? = null, setsuritsuDt: Int? = null, takkenngyoMenkyoBango_1: String? = null, takkenngyoMenkyoBango_2: String? = null, takkenngyoMenkyoBango_3: String? = null, ginkoCd: String? = null, ginkoShitenCd: String? = null, kozaSbt: String? = null, kozaBango: String? = null, kozaMeigininmeiKana: String? = null, kozaMeigininmeiKanji: String? = null, shokaiTorokuShitenCd: String? = null, torihikiTeishiSign: Byte? = null, interfaceSign: Byte? = null, dataIkomotoKey_1: String? = null, dataIkomotoKey_2: String? = null, jichitaiCd: Int? = null, sakujoDt: Int? = null, tokuteiFudosanKbn: String? = null, syuyouChukaiGyoshaKbn: String? = null, tokuteiFcKbn: String? = null, yobiKbn_1: String? = null, yobiDt_1: Int? = null, yobiDt_2: Int? = null): this() {
        this.sakuseiDt = sakuseiDt
        this.sakuseiTm = sakuseiTm
        this.koshinDt = koshinDt
        this.koshinTm = koshinTm
        this.koshinPgmId = koshinPgmId
        this.koshinsha = koshinsha
        this.chukaiGyoshaCd = chukaiGyoshaCd
        this.togoTorihikisakiCd = togoTorihikisakiCd
        this.chukaiGyoshameiKanji = chukaiGyoshameiKanji
        this.chukaiGyoshameiKanji_2 = chukaiGyoshameiKanji_2
        this.chukaiGyoshameiKana = chukaiGyoshameiKana
        this.honshitenmeiKanji = honshitenmeiKanji
        this.kensakuyoKana = kensakuyoKana
        this.kojinHojinKbn = kojinHojinKbn
        this.hojinkakuKbn = hojinkakuKbn
        this.hojinkakuZengoKbn = hojinkakuZengoKbn
        this.kanrenKaishaSign = kanrenKaishaSign
        this.daihyoshameiKanji = daihyoshameiKanji
        this.daihyoshameiKana = daihyoshameiKana
        this.chukaiGyoshaTantoBusho = chukaiGyoshaTantoBusho
        this.chukaiGyoshaTantoshamei = chukaiGyoshaTantoshamei
        this.yubinBango = yubinBango
        this.todofukenCd = todofukenCd
        this.shikugunCd = shikugunCd
        this.chosonazaTsushoCd = chosonazaTsushoCd
        this.jushoShosai = jushoShosai
        this.biruMeisho = biruMeisho
        this.denwaBango = denwaBango
        this.faxBango = faxBango
        this.setsuritsuDt = setsuritsuDt
        this.takkenngyoMenkyoBango_1 = takkenngyoMenkyoBango_1
        this.takkenngyoMenkyoBango_2 = takkenngyoMenkyoBango_2
        this.takkenngyoMenkyoBango_3 = takkenngyoMenkyoBango_3
        this.ginkoCd = ginkoCd
        this.ginkoShitenCd = ginkoShitenCd
        this.kozaSbt = kozaSbt
        this.kozaBango = kozaBango
        this.kozaMeigininmeiKana = kozaMeigininmeiKana
        this.kozaMeigininmeiKanji = kozaMeigininmeiKanji
        this.shokaiTorokuShitenCd = shokaiTorokuShitenCd
        this.torihikiTeishiSign = torihikiTeishiSign
        this.interfaceSign = interfaceSign
        this.dataIkomotoKey_1 = dataIkomotoKey_1
        this.dataIkomotoKey_2 = dataIkomotoKey_2
        this.jichitaiCd = jichitaiCd
        this.sakujoDt = sakujoDt
        this.tokuteiFudosanKbn = tokuteiFudosanKbn
        this.syuyouChukaiGyoshaKbn = syuyouChukaiGyoshaKbn
        this.tokuteiFcKbn = tokuteiFcKbn
        this.yobiKbn_1 = yobiKbn_1
        this.yobiDt_1 = yobiDt_1
        this.yobiDt_2 = yobiDt_2
        resetChangedOnNotNull()
    }

    /**
     * Create a detached, initialised AgentRecord
     */
    constructor(value: AgentPojo?): this() {
        if (value != null) {
            this.sakuseiDt = value.sakuseiDt
            this.sakuseiTm = value.sakuseiTm
            this.koshinDt = value.koshinDt
            this.koshinTm = value.koshinTm
            this.koshinPgmId = value.koshinPgmId
            this.koshinsha = value.koshinsha
            this.chukaiGyoshaCd = value.chukaiGyoshaCd
            this.togoTorihikisakiCd = value.togoTorihikisakiCd
            this.chukaiGyoshameiKanji = value.chukaiGyoshameiKanji
            this.chukaiGyoshameiKanji_2 = value.chukaiGyoshameiKanji_2
            this.chukaiGyoshameiKana = value.chukaiGyoshameiKana
            this.honshitenmeiKanji = value.honshitenmeiKanji
            this.kensakuyoKana = value.kensakuyoKana
            this.kojinHojinKbn = value.kojinHojinKbn
            this.hojinkakuKbn = value.hojinkakuKbn
            this.hojinkakuZengoKbn = value.hojinkakuZengoKbn
            this.kanrenKaishaSign = value.kanrenKaishaSign
            this.daihyoshameiKanji = value.daihyoshameiKanji
            this.daihyoshameiKana = value.daihyoshameiKana
            this.chukaiGyoshaTantoBusho = value.chukaiGyoshaTantoBusho
            this.chukaiGyoshaTantoshamei = value.chukaiGyoshaTantoshamei
            this.yubinBango = value.yubinBango
            this.todofukenCd = value.todofukenCd
            this.shikugunCd = value.shikugunCd
            this.chosonazaTsushoCd = value.chosonazaTsushoCd
            this.jushoShosai = value.jushoShosai
            this.biruMeisho = value.biruMeisho
            this.denwaBango = value.denwaBango
            this.faxBango = value.faxBango
            this.setsuritsuDt = value.setsuritsuDt
            this.takkenngyoMenkyoBango_1 = value.takkenngyoMenkyoBango_1
            this.takkenngyoMenkyoBango_2 = value.takkenngyoMenkyoBango_2
            this.takkenngyoMenkyoBango_3 = value.takkenngyoMenkyoBango_3
            this.ginkoCd = value.ginkoCd
            this.ginkoShitenCd = value.ginkoShitenCd
            this.kozaSbt = value.kozaSbt
            this.kozaBango = value.kozaBango
            this.kozaMeigininmeiKana = value.kozaMeigininmeiKana
            this.kozaMeigininmeiKanji = value.kozaMeigininmeiKanji
            this.shokaiTorokuShitenCd = value.shokaiTorokuShitenCd
            this.torihikiTeishiSign = value.torihikiTeishiSign
            this.interfaceSign = value.interfaceSign
            this.dataIkomotoKey_1 = value.dataIkomotoKey_1
            this.dataIkomotoKey_2 = value.dataIkomotoKey_2
            this.jichitaiCd = value.jichitaiCd
            this.sakujoDt = value.sakujoDt
            this.tokuteiFudosanKbn = value.tokuteiFudosanKbn
            this.syuyouChukaiGyoshaKbn = value.syuyouChukaiGyoshaKbn
            this.tokuteiFcKbn = value.tokuteiFcKbn
            this.yobiKbn_1 = value.yobiKbn_1
            this.yobiDt_1 = value.yobiDt_1
            this.yobiDt_2 = value.yobiDt_2
            resetChangedOnNotNull()
        }
    }
}
