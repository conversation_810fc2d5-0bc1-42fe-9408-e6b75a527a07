-- TABLE: LAYOUT_IMAGE_REGISTRATION(配置図画像登録)

CREATE TABLE LAYOUT_IMAGE_REGISTRATION(
     CREATION_DATE                                numeric(8,0)                  
,    CREATION_TIME                                numeric(6,0)                  
,    CREATOR                                      varchar(6)                    
,    UPDATE_DATE                                  numeric(8,0)                  
,    UPDATE_TIME                                  numeric(6,0)                  
,    UPDATER                                      varchar(6)                    
,    BUILDING_CODE                                varchar(9)        NOT NULL    
,    CONSTRAINT PK_LAYOUT_IMAGE_REGISTRATION PRIMARY KEY (BUILDING_CODE)
     USING INDEX TABLESPACE :TS_IDX
) TABLESPACE :TS_TBL;

COMMENT ON TABLE LAYOUT_IMAGE_REGISTRATION IS '配置図画像登録 既存システム物理名: EMEPHP';
COMMENT ON COLUMN LAYOUT_IMAGE_REGISTRATION.CREATION_DATE IS '作成年月日 既存システム物理名: EMEP1D';
COMMENT ON COLUMN LAYOUT_IMAGE_REGISTRATION.CREATION_TIME IS '作成時刻 既存システム物理名: EMEP2T';
COMMENT ON COLUMN LAYOUT_IMAGE_REGISTRATION.CREATOR IS '作成者 既存システム物理名: EMEP3C';
COMMENT ON COLUMN LAYOUT_IMAGE_REGISTRATION.UPDATE_DATE IS '更新年月日 既存システム物理名: EMEP4D';
COMMENT ON COLUMN LAYOUT_IMAGE_REGISTRATION.UPDATE_TIME IS '更新時刻 既存システム物理名: EMEP5T';
COMMENT ON COLUMN LAYOUT_IMAGE_REGISTRATION.UPDATER IS '更新者 既存システム物理名: EMEP6C';
COMMENT ON COLUMN LAYOUT_IMAGE_REGISTRATION.BUILDING_CODE IS '建物コード 既存システム物理名: EMEP7C';
